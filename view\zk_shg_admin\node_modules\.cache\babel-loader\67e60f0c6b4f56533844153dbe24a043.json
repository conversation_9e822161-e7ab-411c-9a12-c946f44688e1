{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { brandCascader, productBrand, productBrandrev } from \"@/api/product\";\nexport default {\n  name: \"menusFrom\",\n  props: {\n    formValidate: {\n      type: Object,\n      default: null\n    },\n    fromName: {\n      type: Number,\n      default: 0\n    }\n  },\n  data: function data() {\n    return {\n      ruleValidate: {\n        brand_name: [{\n          required: true,\n          message: '请输入品牌名称',\n          trigger: 'blur'\n        }]\n      },\n      type: 1,\n      modals: false,\n      // authType:false,\n      FromData: [],\n      titleFrom: '',\n      grid: {\n        xl: 24,\n        lg: 24,\n        md: 12,\n        sm: 24,\n        xs: 24\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getAddFrom();\n  },\n  methods: {\n    // 获取新增表单\n    getAddFrom: function getAddFrom() {\n      var _this = this;\n\n      brandCascader().then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this.FromData = res.data;\n\n                case 1:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    handleReset: function handleReset() {\n      this.modals = false;\n      this.$parent.getData();\n    },\n    handleSubmit: function handleSubmit(name) {\n      var _this2 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this2.type == 2) {\n            productBrandrev(_this2.formValidate.id, _this2.formValidate).then(function (res) {\n              _this2.$Message.success(res.msg);\n\n              _this2.getAddFrom();\n\n              _this2.$parent.getData();\n\n              _this2.modals = false;\n            }).catch(function (err) {\n              _this2.$Message.error(err.msg);\n            });\n          } else {\n            productBrand(_this2.formValidate).then(function (res) {\n              _this2.$Message.success(res.msg);\n\n              _this2.getAddFrom();\n\n              if (_this2.fromName) {\n                _this2.$parent.getBrandList();\n              } else {\n                _this2.$parent.getData();\n              }\n\n              _this2.modals = false;\n            }).catch(function (err) {\n              _this2.$Message.error(err.msg);\n            });\n          }\n        } else {\n          _this2.$Message.error('请输入品牌名称');\n        }\n      });\n    },\n    cancle: function cancle() {\n      this.modals = false;\n    }\n  }\n};", null]}