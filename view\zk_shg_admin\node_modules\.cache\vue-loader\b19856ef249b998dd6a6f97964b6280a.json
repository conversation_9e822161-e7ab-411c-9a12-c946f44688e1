{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {mapState} from \"vuex\";\nimport uploadPictures from '@/components/uploadPictures';\nexport default {\n    name: \"pageTitle\",\n    components: {\n        uploadPictures\n    },\n    data(){\n        return {\n            value:'',\n            name:'',\n            isShow:true,\n            picList:['icondantu','iconpingpu','iconlashen'],\n            bgColor: false,\n            bgPic: false,\n            tabVal:0,\n            colorPicker: '#f5f5f5',\n            modalPic: false,\n            isChoice: '单选',\n            gridBtn: {\n                xl: 4,\n                lg: 8,\n                md: 8,\n                sm: 8,\n                xs: 8\n            },\n            gridPic: {\n                xl: 6,\n                lg: 8,\n                md: 12,\n                sm: 12,\n                xs: 12\n            },\n            bgPicUrl:''\n        }\n    },\n    created() {\n        let state = this.$store.state.admin.mobildConfig;\n        this.value = state.pageTitle\n        this.name = state.pageName\n        this.isShow = state.pageShow?true:false\n        this.bgColor = state.pageColor?true:false\n        this.bgPic = state.pagePic?true:false\n        this.colorPicker = state.pageColorPicker\n        this.tabVal = state.pageTabVal\n        this.bgPicUrl = state.pagePicUrl\n    },\n    methods:{\n        // 点击图文封面\n        modalPicTap (title) {\n            this.modalPic = true;\n        },\n        bindDelete () {\n            this.bgPicUrl = '';\n        },\n        getPic (pc) {\n            this.$nextTick(() => {\n                this.bgPicUrl = pc.att_dir;\n                this.modalPic = false;\n                this.$store.commit('admin/mobildConfig/UPPICURL',pc.att_dir)\n            })\n        },\n        colorPickerTap(colorPicker){\n            this.$store.commit('admin/mobildConfig/UPPICKER',colorPicker)\n        },\n        radioTap(val){\n            this.$store.commit('admin/mobildConfig/UPRADIO',val)\n        },\n        changVal(val){\n            this.$store.commit('admin/mobildConfig/UPTITLE',val.target.value)\n        },\n        changName(val){\n            this.$store.commit('admin/mobildConfig/UPNAME',val.target.value)\n        },\n        changeState(val){\n            this.$store.commit('admin/mobildConfig/UPSHOW',val)\n        },\n        bgColorTap(val){\n            this.$store.commit('admin/mobildConfig/UPCOLOR',val)\n        },\n        bgPicTap(val){\n            this.$store.commit('admin/mobildConfig/UPPIC',val)\n        }\n    }\n}\n", null]}