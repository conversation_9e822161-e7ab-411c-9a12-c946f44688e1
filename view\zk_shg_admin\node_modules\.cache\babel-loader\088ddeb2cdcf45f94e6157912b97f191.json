{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_1.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_1.vue", "mtime": 1735005448490}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex'; // import theme from \"@/mixins/theme\";\n\nexport default {\n  name: 'home_card_1',\n  cname: '卡片一',\n  configName: 'c_home_card_1',\n  icon: '#iconzujian-shangpinxuanxiangka',\n  type: 0,\n  // 0 基础组件 1 营销组件 2工具组件\n  defaultName: 'productCard1',\n  // 外面匹配名称\n  computed: _objectSpread({}, mapState('admin/mobildConfig', ['defaultArray'])),\n  watch: {},\n  // mixins: [theme],\n  data: function data() {\n    return {\n      // 默认初始化数据禁止修改\n      defaultConfig: {\n        cname: '卡片一',\n        name: 'productCard1',\n        timestamp: this.num,\n        isHide: false,\n        setUp: {\n          tabVal: 0\n        },\n        // 配置项\n        titleLeft: '展示设置',\n        titleTab: '选项卡设置',\n        titleRight: '选项卡样式',\n        titleCurrency: '通用样式',\n        titleCart: '购物车按钮',\n        styleConfig: {\n          title: '选择风格',\n          tabVal: 1,\n          tabList: [{\n            name: '样式一'\n          }, {\n            name: '样式二'\n          }, {\n            name: '样式三'\n          }, {\n            name: '样式四'\n          }, {\n            name: '样式五'\n          }]\n        },\n        slideConfig: {\n          title: '滑动置顶',\n          tabVal: 1,\n          tabList: [{\n            name: '启用'\n          }, {\n            name: '不启用'\n          }]\n        },\n        tabConfig: {\n          title: '点击下方选项卡可进行编辑；鼠标拖拽版块可调整顺序',\n          max: '',\n          tabCur: 0,\n          classList: [],\n          list: [{\n            chiild: [{\n              title: '标题',\n              val: '首发新品',\n              max: 4,\n              pla: '选填，不超过四个字'\n            }, {\n              title: '简介',\n              val: '最新出炉',\n              max: 4,\n              pla: '选填，不超过四个字'\n            }],\n            image: '',\n            tabVal: 1,\n            brandConfig: {\n              brandVal: []\n            },\n            selectConfig: {\n              activeValue: []\n            },\n            goodsLabel: {\n              activeValue: [],\n              list: []\n            },\n            goodsSort: 0,\n            numConfig: {\n              val: 6\n            },\n            goodsList: {\n              max: 20,\n              list: []\n            },\n            productList: {\n              list: []\n            }\n          }]\n        },\n        cartConfig: {\n          title: '是否显示',\n          tabVal: 0,\n          tabList: [{\n            name: '显示'\n          }, {\n            name: '隐藏'\n          }]\n        },\n        bntConfig: {\n          title: '按钮效果',\n          tabVal: 1,\n          tabList: [{\n            name: '进入商品详情页'\n          }, {\n            name: '商品加购'\n          }]\n        },\n        bntStyleConfig: {\n          typeFrom: 'bnt',\n          title: '按钮样式',\n          tabVal: 0\n        },\n        toneConfig: {\n          title: '色调',\n          tabVal: 0,\n          tabList: [{\n            name: '跟随主题风格'\n          }, {\n            name: '自定义'\n          }]\n        },\n        decorateColor: {\n          title: \"装饰元素\",\n          default: [{\n            item: \"#E93323\"\n          }, {\n            item: \"#FF7931\"\n          }],\n          color: [{\n            item: \"#E93323\"\n          }, {\n            item: \"#FF7931\"\n          }]\n        },\n        decorateColor2: {\n          title: \"装饰元素\",\n          default: [{\n            item: \"#E93323\"\n          }],\n          color: [{\n            item: \"#E93323\"\n          }]\n        },\n        textColor: {\n          title: \"选中文字\",\n          default: [{\n            item: \"#333333\"\n          }],\n          color: [{\n            item: \"#333333\"\n          }]\n        },\n        textColor2: {\n          title: \"选中文字\",\n          default: [{\n            item: \"#E93323\"\n          }],\n          color: [{\n            item: \"#E93323\"\n          }]\n        },\n        textColor3: {\n          title: \"选中文字\",\n          default: [{\n            item: \"#FFFFFF\"\n          }],\n          color: [{\n            item: \"#FFFFFF\"\n          }]\n        },\n        toneCartConfig: {\n          title: '色调',\n          tabVal: 0,\n          tabList: [{\n            name: '跟随主题风格'\n          }, {\n            name: '自定义'\n          }]\n        },\n        bntBgColor: {\n          title: '按钮颜色',\n          name: 'bntBgColor',\n          default: [{\n            item: '#E93323'\n          }, {\n            item: '#FF7931'\n          }],\n          color: [{\n            item: '#E93323'\n          }, {\n            item: '#FF7931'\n          }]\n        },\n        bottomBgColor: {\n          title: '底部背景',\n          default: [{\n            item: '#f5f5f5'\n          }],\n          color: [{\n            item: '#f5f5f5'\n          }]\n        },\n        topConfig: {\n          title: '上边距',\n          val: 0,\n          min: 0\n        },\n        bottomConfig: {\n          title: '下边距',\n          val: 0,\n          min: 0\n        },\n        prConfig: {\n          title: '左右边距',\n          val: 10,\n          min: 0\n        },\n        mbConfig: {\n          title: '页面间距',\n          val: 0,\n          min: 0\n        },\n        fillet: {\n          title: '背景圆角',\n          type: 0,\n          list: [{\n            val: \"全部\",\n            icon: \"iconcaozuo-zhengti\"\n          }, {\n            val: \"单个\",\n            icon: \"iconcaozuo-bianjiao\"\n          }],\n          valName: '圆角值',\n          val: 0,\n          min: 0,\n          valList: [{\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }]\n        }\n      },\n      // 样式\n      bottomBgColor: '',\n      mTop: 0,\n      topConfig: 0,\n      bottomConfig: 0,\n      prConfig: 0\n    };\n  },\n  mounted: function mounted() {},\n  methods: {\n    setConfig: function setConfig(data) {}\n  }\n};", null]}