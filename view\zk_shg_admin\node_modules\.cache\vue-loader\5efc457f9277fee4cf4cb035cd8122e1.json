{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import { mapState } from 'vuex'\n\t// import theme from \"@/mixins/theme\";\n\timport Setting from '@/setting';\n    export default {\n        name: 'home_bargain',\n        cname: '砍价',\n        icon:'#iconzujian-kanjia',\n        configName: 'c_home_bargain',\n        type:1,// 0 基础组件 1 营销组件 2工具组件\n        defaultName:'bargain', // 外面匹配名称\n        props: {\n            index: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n\t\t\tcolorStyle:{\n\t\t\t\ttype: null\n\t\t\t}\n        },\n        computed: {\n            ...mapState('admin/mobildConfig', ['defaultArray'])\n        },\n        watch: {\n            pageData: {\n                handler (nVal, oVal) {\n                    this.setConfig(nVal)\n                },\n                deep: true\n            },\n            num: {\n                handler (nVal, oVal) {\n                    let data = this.$store.state.admin.mobildConfig.defaultArray[nVal]\n                    this.setConfig(data)\n                },\n                deep: true\n            },\n            'defaultArray': {\n                handler (nVal, oVal) {\n                    let data = this.$store.state.admin.mobildConfig.defaultArray[this.num]\n                    this.setConfig(data);\n                },\n                deep: true\n            }\n        },\n\t\t// mixins: [theme],\n        data () {\n            return {\n                // 默认初始化数据禁止修改\n                defaultConfig: {\n\t\t\t\t\tcname: '砍价',\n                    name: 'bargain',\n                    timestamp: this.num,\n\t\t\t\t\tisHide:false,\n                    setUp: {\n                        tabVal: 0\n                    },\n\t\t\t\t\ttitleLeft:'头部设置',\n\t\t\t\t\ttitleGoodsList:'商品列表',\n\t\t\t\t\ttitleGoods:'商品设置',\n\t\t\t\t\ttitleRight:'头部样式',\n\t\t\t\t\ttitleGoodsStyle:'商品样式',\n\t\t\t\t\ttitleCurrency:'通用样式',\n\t\t\t\t\tstyleConfig:{\n\t\t\t\t\t\ttitle: '选择风格',\n\t\t\t\t\t\ttabVal: 1,\n\t\t\t\t\t\ttabList: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  name: '背景色'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t      name: '背景图片'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\timgBgConfig:{\n\t\t\t\t\t\tinfo: '建议：710px * 96px',\n\t\t\t\t\t\turl: Setting.apiBaseURL.replace(/adminapi/, '')+'statics/images/bargainBg.png',\n\t\t\t\t\t\ttype:'code',\n\t\t\t\t\t\tdelType:0,\n\t\t\t\t\t\tname:'背景图片'\n\t\t\t\t\t},\n\t\t\t\t\ttitleConfig:{\n\t\t\t\t\t\ttitle: '标题类型',\n\t\t\t\t\t\ttabVal: 0,\n\t\t\t\t\t\ttabList: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  name: '图片'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t      name: '文字'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\timgConfig:{\n\t\t\t\t\t\tinfo: '建议：140px * 32px',\n\t\t\t\t\t\turl: require('@/assets/images/bargain02.png'),\n\t\t\t\t\t\ttype:'code',\n\t\t\t\t\t\tdelType:0,\n\t\t\t\t\t\tname:'标题图片'\n\t\t\t\t\t},\n\t\t\t\t\timgColorConfig:{\n\t\t\t\t\t\tinfo: '建议：140px * 32px',\n\t\t\t\t\t\turl: require('@/assets/images/bargain01.png'),\n\t\t\t\t\t\ttype:'code',\n\t\t\t\t\t\tdelType:0,\n\t\t\t\t\t\tname:'标题图片'\n\t\t\t\t\t},\n\t\t\t\t\ttitleTxtConfig:{\n\t\t\t\t\t\ttitle: '标题文字',\n\t\t\t\t\t\tvalue: '疯狂砍价',\n\t\t\t\t\t\tplace: '请输入标题文字',\n\t\t\t\t\t\tmax: 6\n\t\t\t\t\t},\n\t\t\t\t\ttipTxtConfig:{\n\t\t\t\t\t\ttitle: '提示文字',\n\t\t\t\t\t\tvalue: '低至0元免费拿',\n\t\t\t\t\t\tplace: '请输入提示文字',\n\t\t\t\t\t\tmax: 15\n\t\t\t\t\t},\n\t\t\t\t\trightBntConfig:{\n\t\t\t\t\t\ttitle: '右侧按钮',\n\t\t\t\t\t\tvalue: '更多',\n\t\t\t\t\t\tplace: '请输入右侧按钮',\n\t\t\t\t\t\tmax: 6\n\t\t\t\t\t},\n\t\t\t\t\tgoodStyleConfig:{\n\t\t\t\t\t\ttitle: '选择风格',\n\t\t\t\t\t\ttabVal: 0,\n\t\t\t\t\t\ttabList: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  name: '单列展示'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t      name: '两列展示(纵向)'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  name: '三列展示'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  name: '左右滑动展示'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tnumberConfig:{\n\t\t\t\t\t\ttitle: '商品数量',\n\t\t\t\t\t\tval: 3,\n\t\t\t\t\t\tmin: 1\n\t\t\t\t\t},\n\t\t\t\t\tcheckboxInfo:{\n\t\t\t\t\t\ttitle: '展示信息',\n\t\t\t\t\t\tname: 'checkboxInfo',\n\t\t\t\t\t\ttype:[0,1,2,3],\n\t\t\t\t\t\tlist: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    id:0,\n\t\t\t\t\t\t\t    name:'商品名称'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    id:1,\n\t\t\t\t\t\t\t    name:'参与人数'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    id:2,\n\t\t\t\t\t\t\t    name:'商品价格'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    id:3,\n\t\t\t\t\t\t\t    name:'商品原价'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tbargainConfig:{\n\t\t\t\t\t\ttitle: '砍价按钮',\n\t\t\t\t\t\ttabVal: 0,\n\t\t\t\t\t\ttabList: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  name: '显示'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t      name: '隐藏'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\theaderBgColor:{\n\t\t\t\t\t\ttitle: '背景颜色',\n\t\t\t\t\t\tname: 'headerBgColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    item: '#fff'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    item: '#fff'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\ttitleText:{\n\t\t\t\t\t\ttitle: '标题文字',\n\t\t\t\t\t\ttabVal: 0,\n\t\t\t\t\t\ttabList: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    name: '加粗',\n\t\t\t\t\t\t\t    style: 'bold'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        name: '正常',\n\t\t\t\t\t\t        style: 'normal'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        name: '倾斜',\n\t\t\t\t\t\t        style: 'italic'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\ttitleColor:{\n\t\t\t\t\t\ttitle: '标题颜色',\n\t\t\t\t\t\tname: 'titleColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#282828'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#282828'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\ttitleNumber:{\n\t\t\t\t\t\ttitle: '标题字号',\n\t\t\t\t\t\tval: 16,\n\t\t\t\t\t\tmin: 0\n\t\t\t\t\t},\n\t\t\t\t\theaderBntColor:{\n\t\t\t\t\t\ttitle: '按钮颜色',\n\t\t\t\t\t\tname: 'headerBntColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\theaderBntColor2:{\n\t\t\t\t\t\ttitle: '按钮颜色',\n\t\t\t\t\t\tname: 'headerBntColor2',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#999'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#999'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tbntNumber:{\n\t\t\t\t\t\ttitle: '按钮字号',\n\t\t\t\t\t\tval: 12,\n\t\t\t\t\t\tmin: 0\n\t\t\t\t\t},\n\t\t\t\t\ttipsColor:{\n\t\t\t\t\t\ttitle: '提示文字',\n\t\t\t\t\t\tname: 'tipsColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\ttipsColor2:{\n\t\t\t\t\t\ttitle: '提示文字',\n\t\t\t\t\t\tname: 'tipsColor2',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#999'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#999'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tdividerColor:{\n\t\t\t\t\t\ttitle: '分割线',\n\t\t\t\t\t\tname: 'dividerColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#DDDDDD'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#DDDDDD'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tfilletImg:{\n\t\t\t\t\t\ttitle:'图片圆角',\n\t\t\t\t\t\ttype: 0,\n\t\t\t\t\t\tlist: [\n\t\t\t\t\t\t  {\n\t\t\t\t\t\t    val: \"全部\",\n\t\t\t\t\t\t    icon: \"iconcaozuo-zhengti\",\n\t\t\t\t\t\t  },\n\t\t\t\t\t\t  {\n\t\t\t\t\t\t    val: \"单个\",\n\t\t\t\t\t\t    icon: \"iconcaozuo-bianjiao\",\n\t\t\t\t\t\t  }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tvalName:'圆角值',\n\t\t\t\t\t\tval: 0,\n\t\t\t\t\t\tmin: 0,\n\t\t\t\t\t\tvalList:[\n\t\t\t\t\t\t\t{val:0},\n\t\t\t\t\t\t\t{val:0},\n\t\t\t\t\t\t\t{val:0},\n\t\t\t\t\t\t\t{val:0}\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tgoodsName:{\n\t\t\t\t\t\ttitle: '商品名称',\n\t\t\t\t\t\ttabVal: 1,\n\t\t\t\t\t\ttabList: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    name: '加粗',\n\t\t\t\t\t\t\t    style: 'bold'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        name: '正常',\n\t\t\t\t\t\t        style: 'normal'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tgoodsNameColor:{\n\t\t\t\t\t\ttitle: '商品名称',\n\t\t\t\t\t\tname: 'goodsNameColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#333333'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#333333'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tgoodsPriceColor:{\n\t\t\t\t\t\ttitle: '商品原价',\n\t\t\t\t\t\tname: 'goodsPriceColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#999999'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#999999'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\ttoneConfig:{\n\t\t\t\t\t\ttitle: '色调',\n\t\t\t\t\t\ttabVal: 0,\n\t\t\t\t\t\ttabList: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  name: '跟随主题风格'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t      name: '自定义'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tjoinNumColor:{\n\t\t\t\t\t\ttitle: '参与人数',\n\t\t\t\t\t\tname: 'joinNumColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#E93323'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#E93323'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tjoinNumColor2:{\n\t\t\t\t\t\ttitle: '参与人数',\n\t\t\t\t\t\tname: 'joinNumColor2',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tjoinBgColor:{\n\t\t\t\t\t\ttitle: '参与背景',\n\t\t\t\t\t\tname: 'progressColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#FF7931'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    item: '#E93323'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#FF7931'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#E93323'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tbargainPriceColor:{\n\t\t\t\t\t\ttitle: '砍价价格',\n\t\t\t\t\t\tname: 'bargainPriceColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#E93323'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#E93323'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tgoodsBntColor:{\n\t\t\t\t\t\ttitle: '按钮颜色',\n\t\t\t\t\t\tname: 'goodsBntColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#FF7931'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    item: '#E93323'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#FF7931'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#E93323'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tgoodsBntTxtColor:{\n\t\t\t\t\t\ttitle: '按钮文字',\n\t\t\t\t\t\tname: 'goodsBntTxtColor',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tmoduleColor:{\n\t\t\t\t\t\ttitle: '组件背景',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    item: '#fff'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#fff'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    item: '#fff'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tbottomBgColor:{\n\t\t\t\t\t\ttitle: '底部背景',\n\t\t\t\t\t\tdefault: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#f5f5f5'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tcolor: [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t        item: '#f5f5f5'\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\ttopConfig: {\n\t\t\t\t\t\ttitle: '上边距',\n\t\t\t\t\t\tval: 0,\n\t\t\t\t\t\tmin: 0\n\t\t\t\t\t},\n\t\t\t\t\tbottomConfig: {\n\t\t\t\t\t\ttitle: '下边距',\n\t\t\t\t\t\tval: 0,\n\t\t\t\t\t\tmin: 0\n\t\t\t\t\t},\n\t\t\t\t\tprConfig: {\n\t\t\t\t\t    title: '左右边距',\n\t\t\t\t\t    val: 10,\n\t\t\t\t\t    min: 0\n\t\t\t\t\t},\n\t\t\t\t\tmbConfig: {\n\t\t\t\t\t    title: '页面上间距',\n\t\t\t\t\t    val: 0,\n\t\t\t\t\t    min: 0\n\t\t\t\t\t},\n\t\t\t\t\tfillet:{\n\t\t\t\t\t\ttitle:'背景圆角',\n\t\t\t\t\t\ttype: 0,\n\t\t\t\t\t\tlist: [\n\t\t\t\t\t\t  {\n\t\t\t\t\t\t    val: \"全部\",\n\t\t\t\t\t\t    icon: \"iconcaozuo-zhengti\",\n\t\t\t\t\t\t  },\n\t\t\t\t\t\t  {\n\t\t\t\t\t\t    val: \"单个\",\n\t\t\t\t\t\t    icon: \"iconcaozuo-bianjiao\",\n\t\t\t\t\t\t  }\n\t\t\t\t\t\t],\n\t\t\t\t\t\tvalName:'圆角值',\n\t\t\t\t\t\tval: 8,\n\t\t\t\t\t\tmin: 0,\n\t\t\t\t\t\tvalList:[\n\t\t\t\t\t\t\t{val:0},\n\t\t\t\t\t\t\t{val:0},\n\t\t\t\t\t\t\t{val:0},\n\t\t\t\t\t\t\t{val:0}\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tpageData: {},\n\t\t\t\timgUrl: '',\n\t\t\t\timgBgUrl: '',\n\t\t\t\ttipsColor:'',\n\t\t\t\ttipsColor2:'',\n\t\t\t\tdividerColor:'',\n\t\t\t\trightBntTxt:'',\n\t\t\t\ttipTxt:'',\n\t\t\t\theaderBntColor:'',\n\t\t\t\theaderBntColor2:'',\n\t\t\t\tbntNumber:0,\n\t\t\t\tstyleConfig:0,\n\t\t\t\theaderBgColorLeft:'',\n\t\t\t\theaderBgColorRight:'',\n\t\t\t\timgColorUrl:'',\n\t\t\t\ttitleConfig:0,\n\t\t\t\ttitleTxtConfig:'',\n\t\t\t\tbgColor:'',\n\t\t\t\tbottomBgColor:'',\n\t\t\t\tmTop: 0,\n\t\t\t\ttopConfig:0,\n\t\t\t\tbottomConfig:0,\n\t\t\t\tprConfig:0,\n\t\t\t\ttitleText:'',\n\t\t\t\ttitleTabVal:0,\n\t\t\t\tcheckboxInfo:[],\n\t\t\t\timgRadius:0,\n\t\t\t\tbgRadius:0,\n\t\t\t\tbgRadius2:0,\n\t\t\t\tgoodsName:'',\n\t\t\t\tgoodsNameColor:'',\n\t\t\t\tgoodsPriceColor:'',\n\t\t\t\ttoneConfig:0,\n\t\t\t\tgoodsBntColorLeft:'',\n\t\t\t\tgoodsBntColorRight:'',\n\t\t\t\tgoodStyleConfig:0,\n\t\t\t\tgoodsBntTxtColor:'',\n\t\t\t\tbargainConfig:0,\n\t\t\t\tnumberConfig:1,\n\t\t\t\ttitleColor:'',\n\t\t\t\ttitleNumber:0,\n\t\t\t\tjoinNumColor:'',\n\t\t\t\tjoinNumColor2:'',\n\t\t\t\tbargainPriceColor:'',\n\t\t\t\tjoinBgColorLeft:'',\n\t\t\t\tjoinBgColorRight:'',\n\t\t\t\tthemeColor:'',\n\t\t\t\tthemeColor2:''\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.pageData = this.$store.state.admin.mobildConfig.defaultArray[this.num]\n                this.setConfig(this.pageData)\n            })\n        },\n        methods: {\n            setConfig (data) {\n                if(!data) return\n                if(data.mbConfig){\n\t\t\t\t\tthis.imgUrl = data.imgConfig.url;\n\t\t\t\t\tthis.imgBgUrl = data.imgBgConfig.url;\n\t\t\t\t\tthis.imgColorUrl = data.imgColorConfig.url;\n\t\t\t\t\tthis.tipsColor = data.tipsColor.color[0].item;\n\t\t\t\t\tthis.tipsColor2 = data.tipsColor2.color[0].item;\n\t\t\t\t\tthis.dividerColor = data.dividerColor.color[0].item;\n\t\t\t\t\tthis.rightBntTxt = data.rightBntConfig.value;\n\t\t\t\t\tthis.tipTxt = data.tipTxtConfig.value;\n\t\t\t\t\tthis.headerBntColor = data.headerBntColor.color[0].item;\n\t\t\t\t\tthis.headerBntColor2 = data.headerBntColor2.color[0].item;\n\t\t\t\t\tthis.bntNumber = data.bntNumber.val;\n\t\t\t\t\tthis.styleConfig = data.styleConfig.tabVal;\n\t\t\t\t\tthis.headerBgColorLeft = data.headerBgColor.color[0].item;\n\t\t\t\t\tthis.headerBgColorRight = data.headerBgColor.color[1].item;\n\t\t\t\t\tthis.titleConfig = data.titleConfig.tabVal;\n\t\t\t\t\tthis.titleTxtConfig = data.titleTxtConfig.value;\n\t\t\t\t\tlet bgColorLeft =  data.moduleColor.color[0].item;\n\t\t\t\t\tlet bgColorRight =  data.moduleColor.color[1].item;\n\t\t\t\t\tthis.bgColor = `linear-gradient(90deg,${bgColorLeft} 0%,${bgColorRight} 100%)`;\n\t\t\t\t\tthis.bottomBgColor = data.bottomBgColor.color[0].item;\n\t\t\t\t\tthis.mTop = data.mbConfig.val;\n\t\t\t\t\tthis.topConfig = data.topConfig.val;\n\t\t\t\t\tthis.bottomConfig = data.bottomConfig.val;\n\t\t\t\t\tthis.prConfig = data.prConfig.val;\n\t\t\t\t\tlet tabVal = data.titleText.tabVal;\n\t\t\t\t\tthis.titleTabVal = tabVal;\n\t\t\t\t\tthis.titleText = data.titleText.tabList[tabVal].style;\n\t\t\t\t\tthis.checkboxInfo = data.checkboxInfo.type;\n\t\t\t\t\tlet filletImg = data.filletImg.type;\n\t\t\t\t\tlet filletValImg = data.filletImg.val;\n\t\t\t\t\tlet valListImg = data.filletImg.valList;\n\t\t\t\t\tthis.imgRadius = filletImg? valListImg[0].val+ 'px ' +valListImg[1].val + 'px ' + valListImg[3].val + 'px ' + valListImg[2].val +'px' : filletValImg +'px';\n\t\t\t\t\tlet fillet = data.fillet.type;\n\t\t\t\t\tlet filletVal = data.fillet.val\n\t\t\t\t\tlet valList = data.fillet.valList;\n\t\t\t\t\tthis.bgRadius = fillet? valList[0].val+ 'px ' +valList[1].val + 'px 0 0': filletVal +'px ' +filletVal + 'px 0 0';\n\t\t\t\t\tthis.bgRadius2 = fillet? '0 0 '+valList[3].val+ 'px ' +valList[2].val + 'px': '0 0 '+filletVal +'px ' +filletVal + 'px';\n\t\t\t\t\tlet goodsTabVal = data.goodsName.tabVal;\n\t\t\t\t\tthis.goodsName = data.goodsName.tabList[goodsTabVal].style;\n\t\t\t\t\tthis.goodsNameColor = data.goodsNameColor.color[0].item;\n\t\t\t\t\tthis.goodsPriceColor = data.goodsPriceColor.color[0].item;\n\t\t\t\t\tthis.toneConfig = data.toneConfig.tabVal;\n\t\t\t\t\tthis.goodsBntColorLeft = data.goodsBntColor.color[0].item;\n\t\t\t\t\tthis.goodsBntColorRight = data.goodsBntColor.color[1].item;\n\t\t\t\t\tthis.goodStyleConfig = data.goodStyleConfig.tabVal;\n\t\t\t\t\tthis.goodsBntTxtColor = data.goodsBntTxtColor.color[0].item;\n\t\t\t\t\tthis.bargainConfig = data.bargainConfig.tabVal;\n\t\t\t\t\tthis.numberConfig = data.numberConfig.val;\n\t\t\t\t\tthis.titleColor = data.titleColor.color[0].item;\n\t\t\t\t\tthis.titleNumber = data.titleNumber.val;\n\t\t\t\t\tthis.joinNumColor = data.joinNumColor.color[0].item;\n\t\t\t\t\tthis.joinNumColor2 = data.joinNumColor2.color[0].item;\n\t\t\t\t\tthis.bargainPriceColor = data.bargainPriceColor.color[0].item;\n\t\t\t\t\tthis.joinBgColorLeft = data.joinBgColor.color[0].item;\n\t\t\t\t\tthis.joinBgColorRight = data.joinBgColor.color[1].item;\n\t\t\t\t\tthis.themeColor = `linear-gradient(90deg,${this.colorStyle.theme} 0%,${this.colorStyle.gradient} 100%)`;\n\t\t\t\t\tthis.themeColor2 = `linear-gradient(270deg,${this.colorStyle.theme} 0%,${this.colorStyle.gradient} 100%)`;\n                }\n            }\n        }\n    }\n", null]}