{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=style&index=0&id=040cd542&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1750985308971}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1725352506768}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.inventory-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n  .header-left {\r\n    .page-title {\r\n      margin: 0 0 8px 0;\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #303133;\r\n    }\r\n\r\n    .page-desc {\r\n      margin: 0;\r\n      color: #909399;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .header-right {\r\n    .el-button {\r\n      margin-left: 12px;\r\n    }\r\n  }\r\n}\r\n\r\n.filter-container {\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stats-container {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n\r\n  .stat-card {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 24px;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .stat-icon {\r\n      width: 60px;\r\n      height: 60px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 16px;\r\n      font-size: 24px;\r\n      color: white;\r\n\r\n      &.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n      &.normal { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n      &.warning { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n      &.danger { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n    }\r\n\r\n    .stat-content {\r\n      .stat-number {\r\n        font-size: 28px;\r\n        font-weight: 600;\r\n        color: #303133;\r\n        line-height: 1;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .stat-label {\r\n        color: #909399;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-container {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n}\r\n\r\n.product-info {\r\n  .product-name {\r\n    font-weight: 500;\r\n    color: #303133;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .product-spec {\r\n    font-size: 12px;\r\n    color: #909399;\r\n  }\r\n}\r\n\r\n.stock-normal { color: #67c23a; font-weight: 600; }\r\n.stock-low { color: #e6a23c; font-weight: 600; }\r\n.stock-out { color: #f56c6c; font-weight: 600; }\r\n\r\n.warn-stock {\r\n  color: #909399;\r\n}\r\n\r\n.price {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.product-display {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .product-name {\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .product-spec {\r\n    font-size: 12px;\r\n    color: #909399;\r\n  }\r\n}\r\n\r\n.current-stock {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.help-text {\r\n  margin-left: 12px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.increase { color: #67c23a; }\r\n.decrease { color: #f56c6c; }\r\n", null]}