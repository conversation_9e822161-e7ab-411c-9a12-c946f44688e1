{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\create.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\create.vue", "mtime": 1719540491000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport storeList from \"@/components/storeList\";\nimport goodsList from \"@/components/goodsList/index\";\nimport {\n  couponCategoryApi,\n  couponSaveApi,\n  couponDetailApi,\n} from \"@/api/marketing\";\nimport { brandList } from \"@/api/product\";\n// import { formatDate } from '@/utils/validate';\nimport Setting from \"@/setting\";\nexport default {\n  name: \"storeCouponCreate\",\n  components: {\n    goodsList,\n\tstoreList\n  },\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      couponType: [\n        { name: \"满减券\", title: \"满N元减N元\", id: 1 },\n        { name: \"折扣券\", title: \"满N元打N折\", id: 2 },\n      ],\n      disabled: false,\n\t  storesList:[],\n      formData: {\n        coupon_title: \"\",\n        coupon_price: 0,\n        type: 0,\n        use_min_price: 0,\n        coupon_time: 1,\n        start_use_time: 0,\n        end_use_time: 0,\n        start_time: 0,\n        end_time: 0,\n        receive_type: 1,\n        is_permanent: 1,\n        total_count: 1,\n        sort: 0,\n        status: 1,\n        product_id: \"\",\n        category_id: [],\n        brand_id: [],\n        coupon_type: 1,\n\t\tapplicable_type: 1,\n\t\tapplicable_store_id:[],\n    rule: '',\n    category: 1,\n      },\n      categoryList: [],\n      brandList: [],\n      productList: [],\n      isMinPrice: 0,\n      isCouponTime: 1,\n      isReceiveTime: 0,\n      modals: false,\n      datetime1: [],\n      datetime2: [],\n\t  storeModals: false,\n\t  currentTab: '1',\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\", \"menuCollapse\"]),\n  },\n  created() {\n    this.getBrandList();\n    this.getCategoryList();\n    if (this.$route.params.id) {\n      this.getCouponDetail();\n    }\n  },\n  methods: {\n\t//删除门店\n\tdelte(row){\n\t  this.storesList.forEach((item,index)=>{\n\t    if(row.id == item.id){\n\t      this.storesList.splice(index, 1)\n\t    }\n\t  })\n\t},\n\t\n\t//添加门店\n\taddStore(){\n\t  this.storeModals = true;\n\t},\n\t\n\t//关闭门店弹窗\n\tcancelStore(){\n\t  this.storeModals = false;\n\t},\n\tuniqueId(arr) {\n\t  const res = new Map();\n\t  return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1))\n\t},\n\tgetStoreId (data) {\n\t  this.storeModals = false;\n\t  let list = this.storesList.concat(data);\n\t  let uni = this.uniqueId(list);\n\t  this.storesList = uni;\n\t},\n    couponTypeTap(item) {\n      this.formData.coupon_price = 0;\n      this.formData.coupon_type = item.id;\n    },\n    // 品类\n    getCategoryList() {\n      couponCategoryApi(1).then(async (res) => {\n        res.data.forEach((val) => {\n          val.cate_name = `${val.html}${val.cate_name}`;\n        });\n        this.categoryList = res.data;\n      });\n    },\n    //品牌\n    getBrandList(){\n      brandList().then(res=>{\n        this.brandList = res.data;\n      })\n    },\n    // 优惠券\n    getCouponDetail() {\n      couponDetailApi(this.$route.params.id)\n        .then((res) => {\n          let data = res.data;\n          this.formData.coupon_title = data.coupon_title;\n          this.formData.coupon_type = data.coupon_type;\n          this.formData.type = data.type;\n          this.formData.category_id = Array.isArray(data.category_id) ? data.category_id : [];\n          this.formData.brand_id = Array.isArray(data.brand_id) ? data.brand_id : [];\n          this.formData.category = data.category;\n          this.formData.rule = data.rule;\n          this.formData.coupon_price = parseFloat(data.coupon_price);\n          this.formData.use_min_price = parseFloat(data.use_min_price);\n          this.formData.coupon_time = data.coupon_time;\n          this.formData.receive_type = data.receive_type;\n          this.formData.is_permanent = data.is_permanent;\n          this.formData.status = data.status;\n          this.formData.product_id = data.product_id;\n          this.formData.start_time = data.start_time;\n          this.formData.end_time = data.end_time;\n          this.formData.total_count = data.total_count;\n          this.formData.sort = data.sort;\n\t\t  this.formData.applicable_type = data.applicable_type;\n\t\t  this.storesList = data.stores || [];\n          if (\"productInfo\" in data) {\n            this.productList = data.productInfo;\n          }\n          if (!data.coupon_time) {\n            this.isCouponTime = 0;\n            this.datetime1 = [\n              data.start_use_time * 1000,\n              data.end_use_time * 1000,\n            ];\n            this.formData.start_use_time = this.makeDate(\n              data.start_use_time * 1000\n            );\n            this.formData.end_use_time = this.makeDate(\n              data.end_use_time * 1000\n            );\n          }\n          if (data.start_time) {\n            this.isReceiveTime = 1;\n            this.datetime2 = [data.start_time * 1000, data.end_time * 1000];\n            this.formData.start_time = this.makeDate(data.start_time * 1000);\n            this.formData.end_time = this.makeDate(data.end_time * 1000);\n          }\n          if (data.use_min_price !== \"0.00\") {\n            this.isMinPrice = 1;\n          }\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    makeDate(data) {\n      let date = new Date(data);\n      let YY = date.getFullYear() + \"-\";\n      let MM =\n        (date.getMonth() + 1 < 10\n          ? \"0\" + (date.getMonth() + 1)\n          : date.getMonth() + 1) + \"-\";\n      let DD = date.getDate() < 10 ? \"0\" + date.getDate() : date.getDate();\n      let hh =\n        (date.getHours() < 10 ? \"0\" + date.getHours() : date.getHours()) + \":\";\n      let mm =\n        (date.getMinutes() < 10 ? \"0\" + date.getMinutes() : date.getMinutes()) +\n        \":\";\n      let ss =\n        date.getSeconds() < 10 ? \"0\" + date.getSeconds() : date.getSeconds();\n      return YY + MM + DD + \" \" + hh + mm + ss;\n    },\n\t// 上一页：\n\tupTab() {\n\t  if(this.currentTab == '2'){\n\t    this.currentTab = (Number(this.currentTab) - 1).toString();\n\t  }\n\t},\n\t\n\tdownTab(){\n    let valid = this.validate();\n    if (typeof valid === 'boolean' && valid) {\n      this.currentTab = '2';\n    }\n\t},\n    // 创建\n    save() {\n      let valid = this.validate();\n      if (typeof valid !== 'boolean' || !valid) {\n        return;\n      }\n      this.disabled = false;\n\t  let storeId = []\n\t  this.storesList.forEach(item=>{\n\t    storeId.push(item.id)\n\t  })\n\t  if(this.formData.applicable_type==2 && !storeId.length){\n\t    return this.$Message.warning('请添加适用门店');\n\t  }\n\t  this.formData.applicable_store_id = storeId;\n      couponSaveApi(this.formData)\n        .then((res) => {\n          this.disabled = true;\n          this.$Message.success(res.msg);\n          setTimeout(() => {\n            this.$router.push({\n              path: this.roterPre + \"/marketing/store_coupon_issue/index\",\n            });\n          }, 1000);\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    // 使用有效期--时间段\n    dateChange(time) {\n      this.formData.start_use_time = time[0];\n      this.formData.end_use_time = time[1];\n    },\n    // 限时\n    timeChange(time) {\n      this.formData.start_time = time[0];\n      this.formData.end_time = time[1];\n    },\n    //对象数组去重；\n    unique(arr) {\n      const res = new Map();\n      return arr.filter(\n        (arr) => !res.has(arr.product_id) && res.set(arr.product_id, 1)\n      );\n    },\n    // 选择的商品\n    getProductId(productList) {\n      this.modals = false;\n      this.productList = this.unique(this.productList.concat(productList));\n      this.formData.product_id = \"\";\n      this.productList.forEach((value) => {\n        if (this.formData.product_id) {\n          this.formData.product_id += `,${value.product_id}`;\n        } else {\n          this.formData.product_id += `${value.product_id}`;\n        }\n      });\n    },\n    cancel() {\n      this.modals = false;\n    },\n    // 删除商品\n    remove(productId) {\n      for (let index = 0; index < this.productList.length; index++) {\n        if (this.productList[index].product_id == productId) {\n          this.productList.splice(index, 1);\n        }\n      }\n      this.formData.product_id = \"\";\n      this.productList.forEach((value) => {\n        if (this.formData.product_id) {\n          this.formData.product_id += `,${value.product_id}`;\n        } else {\n          this.formData.product_id += `${value.product_id}`;\n        }\n      });\n    },\n    // 优惠券种类改变\n    categoryChange(value) {\n      if (value == 2) {\n        this.formData.receive_type = 1;\n      }\n    },\n    // 表单验证\n    validate() {\n      if (!this.formData.coupon_title) {\n        return this.$Message.error(\"请输入优惠券名称\");\n      }\n      if (this.formData.type === 2) {\n        if (!this.formData.product_id) {\n          return this.$Message.error(\"请选择商品\");\n        }\n      }\n      if (this.formData.type === 1) {\n        if (!this.formData.category_id.length) {\n          return this.$Message.error(\"请选择品类\");\n        }\n      }\n      if (this.formData.type === 3) {\n        if (!this.formData.brand_id.length) {\n          return this.$Message.error(\"请选择品牌\");\n        }\n      }\n      if (this.formData.coupon_price <= 0) {\n        return this.$Message.error(\"优惠券面值不能小于0\");\n      }\n      if (!this.isMinPrice) {\n        this.formData.use_min_price = 0;\n      } else {\n        if (this.formData.use_min_price < 1) {\n          return this.$Message.error(\"优惠券最低消费不能小于0\");\n        }\n      }\n      if (this.isCouponTime) {\n        this.formData.start_use_time = 0;\n        this.formData.end_use_time = 0;\n        if (this.formData.coupon_time < 1) {\n          return this.$Message.error(\"使用有效期限不能小于1天\");\n        }\n      } else {\n        this.formData.coupon_time = 0;\n        if (!this.formData.start_use_time) {\n          return this.$Message.error(\"请选择使用有效期限\");\n        }\n      }\n      if (this.isReceiveTime) {\n        if (!this.formData.start_time) {\n          return this.$Message.error(\"请选择领取时间\");\n        }\n      } else {\n        this.formData.start_time = 0;\n        this.formData.end_time = 0;\n      }\n      if (this.formData.is_permanent) {\n        this.formData.total_count = 0;\n      } else {\n        if (this.formData.total_count < 1) {\n          return this.$Message.error(\"发布数量不能小于1\");\n        }\n      }\n      return true;\n    }\n  },\n};\n", null]}