{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_checkbox.vue?vue&type=template&id=0dd79b96&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_checkbox.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"checkboxs acea-row row-top\">\n    <div class=\"title-tips\" v-if=\"configData\">\n        <span>{{configData.title}}</span>\n    </div>\n    <div class=\"checkbox-box\">\n        <CheckboxGroup size=\"small\" v-model=\"configData.type\" @on-change=\"checkboxChange($event)\">\n            <Checkbox :label=\"item.id\" :disabled='(selectedData.length>=3 && userStyle && configData.userType && !selectedData.includes(item.id)?true:false)' v-for=\"(item,index) in configData.list\" :key=\"index\">\n                <span>{{item.name}}</span>\n            </Checkbox>\n        </CheckboxGroup>\n    </div>\n</div>\n", null]}