{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\commission\\handle\\commissionDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\commission\\handle\\commissionDetails.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { commissionDetailApi, extractlistApi } from '@/api/finance';\nimport { mapState } from 'vuex';\nexport default {\n    name: 'commissionDetails',\n    data () {\n        return {\n            modals: false,\n            spinShow: false,\n            detailsData: {},\n            Ids: 0,\n            loading: false,\n            formValidate: {\n                nickname: '',\n                start_time: '',\n                end_time: '',\n                page: 1, // 当前页\n                limit: 20 // 每页显示条数\n            },\n            total: 0,\n            columns: [\n                // {\n                //     title: '昵称',\n                //     key: 'nickname'\n                // },\n                {\n                    title: '佣金金额',\n                    key: 'number',\n                    minWidth:80\n                },\n                {\n                    title: '获得时间',\n                    key: '_add_time',\n                    minWidth:150\n                },\n                {\n                    title: '备注',\n                    key: 'mark',\n                    minWidth:330\n                }\n            ],\n            tabList: []\n\n        }\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 80;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'left';\n        }\n    },\n    mounted () {\n        if (this.Ids) {\n            this.getList();\n        }\n    },\n    methods: {\n        // 时间\n        onchangeTime (e) {\n            this.formValidate.start_time = e[0];\n            this.formValidate.end_time = e[1];\n        },\n        // 详情\n        getDetails (id) {\n            this.Ids = id;\n            this.spinShow = true;\n            commissionDetailApi(id).then(async res => {\n                if (res.status === 200) {\n                    let data = res.data\n                    this.detailsData = data.user_info;\n                    this.spinShow = false;\n                } else {\n                    this.spinShow = false;\n                    this.$Message.error(res.msg);\n                }\n            }).catch(res => {\n                this.spinShow = false;\n                this.$Message.error(res.msg);\n            })\n        },\n        // 列表\n        getList () {\n            this.loading = true;\n            extractlistApi(this.Ids, this.formValidate).then(async res => {\n                let data = res.data;\n                this.tabList = data.data;\n                this.total = data.count;\n                this.loading = false;\n            }).catch(res => {\n                this.loading = false;\n                this.$Message.error(res.msg);\n            })\n        },\n        pageChange (index) {\n            this.formValidate.page = index\n            this.getList();\n        },\n        // 搜索\n        userSearchs () {\n            this.formValidate.page = 1;\n            this.getList();\n        }\n    }\n}\n", null]}