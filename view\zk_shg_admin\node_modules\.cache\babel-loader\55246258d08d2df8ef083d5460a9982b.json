{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_img.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'c_button_img',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    }\n  },\n  data: function data() {\n    return {\n      defaults: {},\n      configData: {},\n      current: 0,\n      list: []\n    };\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n        this.getBnt(nVal);\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.defaults = _this.configObj;\n      _this.configData = _this.configObj[_this.configNme];\n\n      _this.getBnt(_this.defaults);\n    });\n  },\n  methods: {\n    tap: function tap(index) {\n      this.current = index;\n      this.configData.tabVal = index;\n    },\n    getBnt: function getBnt(nVal) {\n      var obj = [{\n        url: require('@/assets/images/cart2.png'),\n        width: 24,\n        height: 24\n      }, {\n        url: require('@/assets/images/cart3.png'),\n        width: 24,\n        height: 24\n      }];\n\n      if (nVal.bntStyleConfig.typeFrom == 'bnt') {\n        this.list = obj;\n      } else {\n        if (nVal.styleConfig.tabVal == 0 || nVal.styleConfig.tabVal == 4) {\n          this.list = [{\n            url: require('@/assets/images/cart1.png'),\n            width: 42,\n            height: 24\n          }, {\n            url: require('@/assets/images/cart2.png'),\n            width: 24,\n            height: 24\n          }, {\n            url: require('@/assets/images/cart3.png'),\n            width: 24,\n            height: 24\n          }];\n        } else {\n          this.current = this.current == 2 ? 1 : this.current;\n          this.list = obj;\n        }\n\n        nVal.bntStyleConfig.tabVal = this.current;\n      }\n    }\n  }\n};", null]}