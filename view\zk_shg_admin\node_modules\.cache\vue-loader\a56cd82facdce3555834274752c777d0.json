{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\lazyCascader\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\lazyCascader\\index.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    props: {\n        value: {\n            type: Array,\n            default: () => {\n                return []\n            }\n        },\n        separator: {\n            type: String,\n            default: '/'\n        },\n        placeholder: {\n            type: String,\n            default: '请选择'\n        },\n\n        width: {\n            type: String,\n            default: '400px'\n        },\n        filterable: <PERSON><PERSON><PERSON>,\n        clearable: <PERSON><PERSON><PERSON>,\n        disabled: <PERSON><PERSON><PERSON>,\n        props: {\n            type: Object,\n            default: () => {\n                return {}\n            }\n        },\n        suggestionsPopperClass: {\n            type: String,\n            default: 'suggestions-popper-class'\n        },\n        searchWidth: {\n            type: String\n        },\n        searchEmptyText: {\n            type: String,\n            default: '暂无数据'\n        }\n    },\n    data() {\n        return {\n            isSearchEmpty: false,\n            keyword: '',\n            options: [],\n            current: [],\n            labelObject: { label: [], value: [] },\n            labelArray: [],\n            currentProps: {\n                multiple: this.props.multiple,\n                checkStrictly: this.props.checkStrictly,\n                value: this.props.value,\n                label: this.props.label,\n                leaf: this.props.leaf,\n                lazy: true,\n                lazyLoad: this.lazyLoad\n            }\n        }\n    },\n    computed: {\n        placeholderVisible() {\n            if (this.current) {\n                return this.current.length == 0\n            } else {\n                return true\n            }\n        }\n    },\n    watch: {\n        current() {\n            this.getLabelArray()\n        },\n        value(v) {\n            this.current = v\n        },\n        keyword() {\n            this.isSearchEmpty = false\n        }\n    },\n    created() {\n        this.initOptions()\n    },\n    methods: {\n        // 搜索是否选中\n        isChecked(value) {\n            // 多选\n            if (this.props.multiple) {\n                const index = this.current.findIndex(item => {\n                    return item.join() == value.join()\n                })\n                if (index > -1) {\n                    return 'el-link el-link--primary'\n                } else {\n                    return ''\n                }\n            } else {\n                if (value.join() == this.current.join()) {\n                    return 'el-link el-link--primary'\n                } else {\n                    return ''\n                }\n            }\n        },\n        // 搜索\n        querySearch(query, callback) {\n            this.props.lazySearch(query, list => {\n                callback(list)\n                if (!list || !list.length) this.isSearchEmpty = true\n            })\n        },\n        // 选中搜索下拉搜索项\n        handleSelect(item) {\n            if (this.props.multiple) {\n                const index = this.current.findIndex(obj => {\n                    return obj.join() == item[this.props.value].join()\n                })\n                if (index == -1) {\n                    this.$refs.panel.clearCheckedNodes()\n                    this.current.push(item[this.props.value])\n                    this.$emit('change', this.current)\n                }\n            } else {\n                // 选中下拉选变更值\n                if (\n                    this.current == null ||\n                    item[this.props.value].join() !== this.current.join()\n                ) {\n                    this.$refs.panel.activePath = []\n                    this.current = item[this.props.value]\n                    this.$emit('change', this.current)\n                }\n            }\n            this.keyword = ''\n        },\n        // 初始化数据\n        async initOptions() {\n            this.props.lazyLoad(0, list => {\n                this.$set(this, 'options', list)\n                if (this.props.multiple) {\n                    this.current = [...this.value]\n                } else {\n                    this.current = this.value\n                }\n            })\n        },\n        async getLabelArray() {\n            if (this.props.multiple) {\n                const array = []\n                for (let i = 0; i < this.current.length; i++) {\n                    const obj = await this.getObject(this.current[i])\n                    array.push(obj)\n                }\n                this.labelArray = array\n                this.$emit('input', this.current)\n                if (!this.disabled) {\n                    this.$nextTick(() => {\n                        this.$refs.popover.updatePopper()\n                    })\n                }\n            } else {\n                this.labelObject = await this.getObject(this.current || [])\n                this.$emit('input', this.current)\n            }\n        },\n        /** 格式化id=>object */\n        async getObject(id) {\n            try {\n                let options = this.options\n                const nameArray = []\n                for (let i = 0; i < id.length; i++) {\n                    const index = options.findIndex(item => {\n                        return item[this.props.value] == id[i]\n                    })\n                    if (index < 0) {\n                        continue\n                    }\n                    nameArray.push(options[index][this.props.label])\n                    if (i < id.length - 1 && (!options[index].children.length)) {\n                        const list = new Promise(resolve => {\n                            this.props.lazyLoad(id[i], list => {\n                                resolve(list)\n                            })\n                        })\n                        this.$set(options[index], 'children', await list)\n                        options = options[index].children\n                    } else {\n                        options = options[index].children\n                    }\n                }\n                return { value: id, label: nameArray }\n            } catch (e) {\n                this.current = []\n                return { value: [], label: [] }\n            }\n        },\n        // 懒加载数据\n        async lazyLoad(node, resolve) {\n            let current = this.current\n            if (this.props.multiple) {\n                current = [...this.current]\n            }\n            if (node.root) {\n                resolve()\n            } else if (node.data[this.props.leaf]) {\n                resolve()\n            } else if (node.data.children && node.data.children.length) {\n                if (this.props.multiple) {\n                    this.current = current\n                }\n                resolve()\n            } else {\n                this.props.lazyLoad(node.value, list => {\n                    node.data.children = list\n                    if (this.props.multiple) {\n                        this.current = current\n                    }\n                    resolve(list)\n                })\n            }\n        },\n        // 删除多选值\n        /** 删除**/\n        handleClose(item) {\n            const index = this.current.findIndex(obj => {\n                return obj.join() == item.value.join()\n            })\n            if (index > -1) {\n                this.$refs.panel.clearCheckedNodes()\n                this.current.splice(index, 1)\n                this.$emit('change', this.current)\n            }\n        },\n        // 点击清空按钮\n        clearBtnClick() {\n            this.$refs.panel.clearCheckedNodes()\n            this.current = []\n            this.$emit('change', this.current)\n        },\n        change() {\n            this.$emit('change', this.current)\n        }\n    }\n}\n", null]}