{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState, mapMutations } from 'vuex'\nimport {\n  putWrite,\n  storeOrderApi,\n  handBatchDelivery,\n  otherBatchDelivery,\n  exportExpressList,\n} from '@/api/order'\nimport { staffListInfo } from '@/api/store'\nimport { getSupplierList } from '@/api/supplier'\nimport autoSend from '../handle/autoSend'\nimport queueList from '../handle/queueList'\nimport Setting from '@/setting'\nimport util from '@/libs/util'\nimport timeOptions from '@/utils/timeOptions'\n// import XLSX from 'xlsx';\n// const make_cols = refstr => Array(XLSX.utils.decode_range(refstr).e.c + 1).fill(0).map((x,i) => ({name:XLSX.utils.encode_col(i), key:i}));\nexport default {\n  name: 'table_from',\n  components: {\n    autoSend,\n    queueList,\n  },\n  props: ['formSelection', 'isAll', 'orderDataStatus'],\n  data() {\n    const codeNum = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('请填写核销码'))\n      }\n      // 模拟异步验证效果\n      if (!Number.isInteger(value)) {\n        callback(new Error('请填写12位数字'))\n      } else {\n        // const reg = /[0-9]{12}/;\n        const reg = /\\b\\d{12}\\b/\n        if (!reg.test(value)) {\n          callback(new Error('请填写12位数字'))\n        } else {\n          callback()\n        }\n      }\n    }\n    return {\n      currentTab: '-1',\n      grid: {\n        xl: 7,\n        lg: 12,\n        md: 24,\n        sm: 24,\n        xs: 24,\n      },\n      // 搜索条件\n      orderData: {\n        status: '',\n        data: '',\n        real_name: '',\n        field_key: 'all',\n        pay_type: '',\n        type:'', // 订单类型\n        store_id: '',\n        supplier_id: '',\n              },\n      modalTitleSs: '',\n      statusType: '',\n      time: '',\n      value2: [],\n      isDelIdList: [],\n      writeOffRules: {\n        code: [{ validator: codeNum, trigger: 'blur', required: true }],\n      },\n      writeOffFrom: {\n        code: '',\n        confirm: 0,\n      },\n      staffData: [], // 门店\n      supplierName: [], // 供应商\n      modals2: false,\n      timeVal: [],\n      options: timeOptions,\n      payList: [\n        { label: '全部', val: '' },\n        { label: '微信支付', val: '1' },\n        { label: '支付宝支付', val: '4' },\n        { label: '余额支付', val: '2' },\n        { label: '线下支付', val: '3' },\n      ],\n      manualModal: false,\n      uploadAction: `${Setting.apiBaseURL}/file/upload/1`,\n      uploadHeaders: {},\n      file: '',\n      autoModal: false,\n      isShow: false,\n      recordModal: false,\n      sendOutValue: '',\n      exportListOn: 0,\n      fileList: [],\n      // modal5: false,\n      // data5: [],\n      // cols5: []\n      // orderStatus: false,\n      // orderInfo:''\n    }\n  },\n  mounted() {\n    // this.getType_id = ''\n    // this.getStore_id = ''\n    // this.getSupplier_id = ''\n\n  },\n  computed: {\n    ...mapState('admin/layout', ['isMobile']),\n    ...mapState('admin/order', [\n      'orderChartType',\n      'isDels',\n      'delIdList',\n      'orderType',\n    ]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96\n    },\n    labelPosition() {\n      return this.isMobile ? 'top' : 'right'\n    },\n    today() {\n      const end = new Date()\n      const start = new Date()\n      var datetimeStart =\n        start.getFullYear() +\n        '/' +\n        (start.getMonth() + 1) +\n        '/' +\n        start.getDate()\n      var datetimeEnd =\n        end.getFullYear() + '/' + (end.getMonth() + 1) + '/' + end.getDate()\n      return [datetimeStart, datetimeEnd]\n    },\n  },\n  watch: {\n    $route() {\n      if (this.$route.fullPath === '/order/list?status=1') {\n        this.getPath()\n      }\n    },\n    orderDataStatus(value) {\n      this.selectChange2(value)\n    }\n  },\n  created() {\n    // this.timeVal = this.today;\n    // this.orderData.data = this.timeVal.join('-');\n\n    this.staffList()\n    this.getSupplierList()\n    if (this.$route.fullPath === '/order/list?status=1') {\n      this.getPath()\n    }\n    this.$parent.$emit('add')\n  },\n  methods: {\n    ...mapMutations('admin/order', [\n      'getOrderStatus',\n      'getOrderType',\n      'getOrderTime',\n      'getOrderNum',\n      'getfieldKey',\n      'getSupplier_id',\n      'getStore_id',\n      'getType_id',\n          ]),\n    getPath() {\n      this.orderData.status = this.$route.query.status.toString()\n      this.getOrderStatus(this.orderData.status)\n      this.$emit('getList', 1)\n      this.$emit('order-data', this.orderData)\n    },\n    clearTap(e){\n      this.getOrderNum(e.target.value)\n      this.$emit('order-data', this.orderData)\n    },\n    // 具体日期\n    onchangeTime(e) {\n      if (e[1].slice(-8) === '00:00:00') {\n        e[1] = e[1].slice(0, -8) + '23:59:59'\n        this.timeVal = e\n      } else {\n        this.timeVal = e\n      }\n      this.orderData.data = this.timeVal[0] ? this.timeVal.join('-') : ''\n      // this.$store.dispatch(\"admin/order/getOrderTabs\", {\n      //   data: this.orderData.data,\n      // });\n      this.getOrderTime(this.orderData.data)\n      this.$emit('getList', 1)\n      this.$emit('order-data', this.orderData)\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.$store.dispatch('admin/order/getOrderTabs', { data: tab })\n      this.orderData.data = tab\n      this.getOrderTime(this.orderData.data)\n      this.timeVal = []\n      this.$emit('getList')\n      this.$emit('order-data', this.orderData)\n    },\n\n    // 订单选择状态\n    selectChange2(tab) {\n      this.orderData.status = tab;\n      this.getOrderStatus(tab)\n      this.$emit('getList', 1)\n      this.$emit('order-data', this.orderData)\n    },\n\n    // 订单类型选择\n    typeChange(tab) {\n      this.getType_id(tab)\n      this.$emit('getList', 1)\n      this.$emit('order-data', this.orderData)\n    },\n\n        // 门店\n    storeChange(tab) {\n      this.getStore_id(tab)\n      this.$emit('getList', 1)\n      this.$emit('order-data', this.orderData)\n    },\n\n        // 供应商选择\n    supplierChange(tab) {\n      this.getSupplier_id(tab)\n      this.$emit('getList', 1)\n    },\n\n    userSearchs(type) {\n      this.getOrderType(type)\n      this.$emit('getList', 1)\n    },\n\n    // 时间状态\n    timeChange(time) {\n      this.getOrderTime(time)\n      this.$emit('getList')\n    },\n\n    // 门店列表\n    staffList() {\n      let data = {\n        page: 0,\n        limit: 0,\n      }\n      staffListInfo()\n        .then((res) => {\n          this.staffData = res.data\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg)\n        })\n    },\n\n    // 获取供应商内容\n    getSupplierList() {\n      getSupplierList()\n        .then(async (res) => {\n          this.supplierName = res.data\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg)\n        })\n    },\n\n    // 订单号搜索\n    orderSearch(num) {\n      this.getOrderNum(num)\n      this.getfieldKey(this.orderData.field_key)\n      this.$emit('getList', 1)\n    },\n\n    // 点击订单类型\n    onClickTab() {\n      this.$emit('onChangeType', this.currentTab)\n    },\n\n    // 批量删除\n    delAll() {\n      if (this.delIdList.length === 0) {\n        this.$Message.error('请先选择删除的订单！')\n      } else {\n        if (this.isDels) {\n          this.delIdList.filter((item) => {\n            this.isDelIdList.push(item.id)\n          })\n          let idss = {\n            ids: this.isDelIdList,\n            all: this.isAll,\n            where: this.orderData,\n          }\n          let delfromData = {\n            title: '删除订单',\n            url: `/order/dels`,\n            method: 'post',\n            ids: idss,\n          }\n          this.$modalSure(delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg)\n              this.tabList()\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg)\n            })\n        } else {\n          const title = '错误！'\n          const content =\n            '<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>'\n          this.$Modal.error({\n            title: title,\n            content: content,\n          })\n        }\n      }\n    },\n    handleSubmit() {\n      this.$emit('on-submit', this.data)\n    },\n\n    // 刷新\n    Refresh() {\n      this.$emit('getList')\n    },\n    //\n    handleReset() {\n      this.$refs.form.resetFields()\n      this.$emit('on-reset')\n    },\n    queuemModal() {\n      this.$refs.queue.modal = true\n    },\n  },\n}\n", null]}