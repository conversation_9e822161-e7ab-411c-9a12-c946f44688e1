{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\statistics.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\statistics.vue", "mtime": 1694049038000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport cardsData from '@/components/cards/cards';\nimport {\n  getcombinationStatistics,\n  getcombinationStatisticsPeople,\n  getcombinationStatisticsOrder,\n  orderPinkListApi,\n} from '@/api/marketing';\nimport Setting from \"@/setting\";\nimport {mapState} from \"vuex\";\n\nexport default {\n  name: 'index',\n  components: { cardsData },\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      modals: false,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      id: 0,\n      tbody: [],\n      total: 0,\n      tabs: [\n        {\n          type: '',\n          label: '活动参与人',\n        },\n        {\n          type: '',\n          label: '活动订单',\n        },\n      ],\n      currentTab: 0,\n      loading: false,\n      thead: [\n        {\n          title: '头像',\n          slot: 'avatar',\n        },\n        {\n          title: '发起用户',\n          key: 'nickname',\n        },\n        {\n          title: '开团时间',\n          key: '_add_time',\n        },\n        {\n          title: '拼团人数',\n          slot: 'people',\n        },\n        {\n          title: '结束时间',\n          key: '_stop_time',\n        },\n        {\n          title: '拼团状态',\n          slot: 'status',\n        },\n        {\n          title: '操作',\n          slot: 'action',\n        },\n      ],\n      thead2: [\n        {\n          title: '订单号',\n          key: 'order_id',\n        },\n        {\n          title: '用户',\n          key: 'real_name',\n        },\n        {\n          title: '订单状态',\n          key: 'status',\n        },\n        {\n          title: '订单支付金额',\n          key: 'pay_price',\n        },\n        {\n          title: '订单商品数',\n          key: 'total_num',\n        },\n        {\n          title: '下单时间',\n          key: 'add_time',\n        },\n        {\n          title: '支付时间',\n          key: 'pay_time',\n        },\n      ],\n      cardLists: [\n        {\n          col: 4,\n          count: 0,\n          name: '活动参与人数（人）',\n          className: 'iconcanyurenshu',\n          type: true\n        },\n        {\n          col: 4,\n          count: 0,\n          name: '推广人数（人）',\n          className: 'icontuiguangrenshu',\n          type: true\n        },\n        {\n          col: 4,\n          count: 0,\n          name: '发起拼团数',\n          className: 'iconfaqirenshu',\n          type: true\n        },\n        {\n          col: 4,\n          count: 0,\n          name: '成团数',\n          className: 'iconchengtuanshu',\n          type: true\n        },\n        {\n          col: 4,\n          count: 0,\n          name: '支付订单额（元）',\n          className: 'iconzhifudingdan',\n          type: true\n        },\n        {\n          col: 4,\n          count: 0,\n          name: '支付人数（人）',\n          className: 'iconzhifurenshu',\n          type: true\n        },\n      ],\n      pagination: {\n        page: 1,\n        limit: 15,\n        real_name: '',\n        status: '',\n      },\n      type: 0,\n      loading2: false,\n      tabList3: [],\n      columns2: [\n        {\n          title: 'ID',\n          key: 'id',\n          width: 80,\n        },\n        {\n          title: '用户名称',\n          slot: 'nickname',\n          minWidth: 100,\n        },\n        {\n          title: '用户头像',\n          slot: 'avatar',\n        },\n        {\n          title: '订单编号',\n          key: 'order_id',\n        },\n        {\n          title: '金额',\n          key: 'price',\n        },\n        {\n          title: '订单状态',\n          slot: 'action',\n        },\n      ],\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.id = this.$route.params.id;\n    this.getStatistics(this.id);\n    this.getList(this.id);\n  },\n  methods: {\n    // 统计\n    getStatistics(id) {\n      console.log(id);\n      getcombinationStatistics(id).then((res) => {\n        let arr = ['people_count', 'spread_count', 'start_count', 'success_count', 'pay_price', 'pay_count'];\n        this.cardLists.map((i, index) => {\n          i.count = res.data[arr[index]];\n        });\n      });\n    },\n    // 列表\n    getList(id) {\n      this.loading = true;\n      if (this.type == 0) {\n        getcombinationStatisticsPeople(this.id, this.pagination).then((res) => {\n          this.loading = false;\n          const { count, list } = res.data;\n          this.total = count;\n          this.tbody = list;\n        });\n      } else {\n        getcombinationStatisticsOrder(this.id, this.pagination).then((res) => {\n          this.loading = false;\n          const { count, list } = res.data;\n          this.total = count;\n          this.tbody = list;\n        });\n      }\n    },\n    // 标签切换\n    onClickTab() {\n      this.type = this.currentTab;\n      this.getList(this.id);\n    },\n    // 搜索\n    searchList() {\n      this.pagination.page = 1;\n      this.getList(this.id);\n    },\n    // 分页\n    pageChange(index) {\n      this.pagination.page = index;\n      this.getList(this.id);\n    },\n    // 查看详情\n    Info(row) {\n      this.modals = true;\n      this.rows = row;\n      orderPinkListApi(row.id)\n          .then(async (res) => {\n            let data = res.data;\n            this.tabList3 = data.list;\n            this.loading = false;\n          })\n          .catch((res) => {\n            this.loading = false;\n            this.$Message.error(res.msg);\n          });\n    },\n  },\n};\n", null]}