{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from 'vuex';\nimport { putWrite, storeOrderApi, handBatchDelivery, otherBatchDelivery, exportExpressList } from '@/api/order';\nimport { staffListInfo } from '@/api/store';\nimport { getSupplierList as _getSupplierList } from '@/api/supplier';\nimport autoSend from '../handle/autoSend';\nimport queueList from '../handle/queueList';\nimport Setting from '@/setting';\nimport util from '@/libs/util';\nimport timeOptions from '@/utils/timeOptions'; // import XLSX from 'xlsx';\n// const make_cols = refstr => Array(XLSX.utils.decode_range(refstr).e.c + 1).fill(0).map((x,i) => ({name:XLSX.utils.encode_col(i), key:i}));\n\nexport default {\n  name: 'table_from',\n  components: {\n    autoSend: autoSend,\n    queueList: queueList\n  },\n  props: ['formSelection', 'isAll', 'orderDataStatus'],\n  data: function data() {\n    var codeNum = function codeNum(rule, value, callback) {\n      if (!value) {\n        return callback(new Error('请填写核销码'));\n      } // 模拟异步验证效果\n\n\n      if (!Number.isInteger(value)) {\n        callback(new Error('请填写12位数字'));\n      } else {\n        // const reg = /[0-9]{12}/;\n        var reg = /\\b\\d{12}\\b/;\n\n        if (!reg.test(value)) {\n          callback(new Error('请填写12位数字'));\n        } else {\n          callback();\n        }\n      }\n    };\n\n    return {\n      currentTab: '-1',\n      grid: {\n        xl: 7,\n        lg: 12,\n        md: 24,\n        sm: 24,\n        xs: 24\n      },\n      // 搜索条件\n      orderData: {\n        status: '',\n        data: '',\n        real_name: '',\n        field_key: 'all',\n        pay_type: '',\n        type: '',\n        // 订单类型\n        store_id: '',\n        supplier_id: ''\n      },\n      modalTitleSs: '',\n      statusType: '',\n      time: '',\n      value2: [],\n      isDelIdList: [],\n      writeOffRules: {\n        code: [{\n          validator: codeNum,\n          trigger: 'blur',\n          required: true\n        }]\n      },\n      writeOffFrom: {\n        code: '',\n        confirm: 0\n      },\n      staffData: [],\n      // 门店\n      supplierName: [],\n      // 供应商\n      modals2: false,\n      timeVal: [],\n      options: timeOptions,\n      payList: [{\n        label: '全部',\n        val: ''\n      }, {\n        label: '微信支付',\n        val: '1'\n      }, {\n        label: '支付宝支付',\n        val: '4'\n      }, {\n        label: '余额支付',\n        val: '2'\n      }, {\n        label: '线下支付',\n        val: '3'\n      }],\n      manualModal: false,\n      uploadAction: \"\".concat(Setting.apiBaseURL, \"/file/upload/1\"),\n      uploadHeaders: {},\n      file: '',\n      autoModal: false,\n      isShow: false,\n      recordModal: false,\n      sendOutValue: '',\n      exportListOn: 0,\n      fileList: [] // modal5: false,\n      // data5: [],\n      // cols5: []\n      // orderStatus: false,\n      // orderInfo:''\n\n    };\n  },\n  mounted: function mounted() {// this.getType_id = ''\n    // this.getStore_id = ''\n    // this.getSupplier_id = ''\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {}, mapState('admin/order', ['orderChartType', 'isDels', 'delIdList', 'orderType']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    },\n    today: function today() {\n      var end = new Date();\n      var start = new Date();\n      var datetimeStart = start.getFullYear() + '/' + (start.getMonth() + 1) + '/' + start.getDate();\n      var datetimeEnd = end.getFullYear() + '/' + (end.getMonth() + 1) + '/' + end.getDate();\n      return [datetimeStart, datetimeEnd];\n    }\n  }),\n  watch: {\n    $route: function $route() {\n      if (this.$route.fullPath === '/order/list?status=1') {\n        this.getPath();\n      }\n    },\n    orderDataStatus: function orderDataStatus(value) {\n      this.selectChange2(value);\n    }\n  },\n  created: function created() {\n    // this.timeVal = this.today;\n    // this.orderData.data = this.timeVal.join('-');\n    this.staffList();\n    this.getSupplierList();\n\n    if (this.$route.fullPath === '/order/list?status=1') {\n      this.getPath();\n    }\n\n    this.$parent.$emit('add');\n  },\n  methods: _objectSpread({}, mapMutations('admin/order', ['getOrderStatus', 'getOrderType', 'getOrderTime', 'getOrderNum', 'getfieldKey', 'getSupplier_id', 'getStore_id', 'getType_id']), {\n    getPath: function getPath() {\n      this.orderData.status = this.$route.query.status.toString();\n      this.getOrderStatus(this.orderData.status);\n      this.$emit('getList', 1);\n      this.$emit('order-data', this.orderData);\n    },\n    clearTap: function clearTap(e) {\n      this.getOrderNum(e.target.value);\n      this.$emit('order-data', this.orderData);\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      if (e[1].slice(-8) === '00:00:00') {\n        e[1] = e[1].slice(0, -8) + '23:59:59';\n        this.timeVal = e;\n      } else {\n        this.timeVal = e;\n      }\n\n      this.orderData.data = this.timeVal[0] ? this.timeVal.join('-') : ''; // this.$store.dispatch(\"admin/order/getOrderTabs\", {\n      //   data: this.orderData.data,\n      // });\n\n      this.getOrderTime(this.orderData.data);\n      this.$emit('getList', 1);\n      this.$emit('order-data', this.orderData);\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.$store.dispatch('admin/order/getOrderTabs', {\n        data: tab\n      });\n      this.orderData.data = tab;\n      this.getOrderTime(this.orderData.data);\n      this.timeVal = [];\n      this.$emit('getList');\n      this.$emit('order-data', this.orderData);\n    },\n    // 订单选择状态\n    selectChange2: function selectChange2(tab) {\n      this.orderData.status = tab;\n      this.getOrderStatus(tab);\n      this.$emit('getList', 1);\n      this.$emit('order-data', this.orderData);\n    },\n    // 订单类型选择\n    typeChange: function typeChange(tab) {\n      this.getType_id(tab);\n      this.$emit('getList', 1);\n      this.$emit('order-data', this.orderData);\n    },\n    // 门店\n    storeChange: function storeChange(tab) {\n      this.getStore_id(tab);\n      this.$emit('getList', 1);\n      this.$emit('order-data', this.orderData);\n    },\n    // 供应商选择\n    supplierChange: function supplierChange(tab) {\n      this.getSupplier_id(tab);\n      this.$emit('getList', 1);\n    },\n    userSearchs: function userSearchs(type) {\n      this.getOrderType(type);\n      this.$emit('getList', 1);\n    },\n    // 时间状态\n    timeChange: function timeChange(time) {\n      this.getOrderTime(time);\n      this.$emit('getList');\n    },\n    // 门店列表\n    staffList: function staffList() {\n      var _this = this;\n\n      var data = {\n        page: 0,\n        limit: 0\n      };\n      staffListInfo().then(function (res) {\n        _this.staffData = res.data;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n      });\n    },\n    // 获取供应商内容\n    getSupplierList: function getSupplierList() {\n      var _this2 = this;\n\n      _getSupplierList().then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this2.supplierName = res.data;\n\n                case 1:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    // 订单号搜索\n    orderSearch: function orderSearch(num) {\n      this.getOrderNum(num);\n      this.getfieldKey(this.orderData.field_key);\n      this.$emit('getList', 1);\n    },\n    // 点击订单类型\n    onClickTab: function onClickTab() {\n      this.$emit('onChangeType', this.currentTab);\n    },\n    // 批量删除\n    delAll: function delAll() {\n      var _this3 = this;\n\n      if (this.delIdList.length === 0) {\n        this.$Message.error('请先选择删除的订单！');\n      } else {\n        if (this.isDels) {\n          this.delIdList.filter(function (item) {\n            _this3.isDelIdList.push(item.id);\n          });\n          var idss = {\n            ids: this.isDelIdList,\n            all: this.isAll,\n            where: this.orderData\n          };\n          var delfromData = {\n            title: '删除订单',\n            url: \"/order/dels\",\n            method: 'post',\n            ids: idss\n          };\n          this.$modalSure(delfromData).then(function (res) {\n            _this3.$Message.success(res.msg);\n\n            _this3.tabList();\n          }).catch(function (res) {\n            _this3.$Message.error(res.msg);\n          });\n        } else {\n          var title = '错误！';\n          var content = '<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>';\n          this.$Modal.error({\n            title: title,\n            content: content\n          });\n        }\n      }\n    },\n    handleSubmit: function handleSubmit() {\n      this.$emit('on-submit', this.data);\n    },\n    // 刷新\n    Refresh: function Refresh() {\n      this.$emit('getList');\n    },\n    //\n    handleReset: function handleReset() {\n      this.$refs.form.resetFields();\n      this.$emit('on-reset');\n    },\n    queuemModal: function queuemModal() {\n      this.$refs.queue.modal = true;\n    }\n  })\n};", null]}