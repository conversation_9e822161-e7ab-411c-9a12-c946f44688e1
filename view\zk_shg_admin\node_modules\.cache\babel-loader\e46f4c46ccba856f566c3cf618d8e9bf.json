{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_swipers_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_swipers_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport vuedraggable from 'vuedraggable';\nimport uploadPictures from '@/components/uploadPictures';\nimport linkaddress from '@/components/linkaddress';\nexport default {\n  name: 'c_swipers_list',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    },\n    index: {\n      type: null\n    }\n  },\n  components: {\n    draggable: vuedraggable,\n    linkaddress: linkaddress,\n    uploadPictures: uploadPictures\n  },\n  data: function data() {\n    return {\n      defaults: {},\n      configData: {},\n      menus: [],\n      list: [{\n        title: 'aa',\n        val: ''\n      }],\n      modalPic: false,\n      isChoice: '单选',\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      activeIndex: 0,\n      indexLast: 0,\n      lastObj: {}\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.defaults = _this.configObj;\n      _this.configData = _this.configObj[_this.configNme];\n    });\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n      },\n      deep: true\n    }\n  },\n  methods: {\n    linkUrl: function linkUrl(e) {\n      this.configData.list[this.activeIndex].info[this.indexLast].value = e;\n\n      if (this.defaults.name == \"pictureCube\") {\n        this.defaults.picStyle.picList[this.defaults.picStyle.tabVal].link = e;\n      }\n    },\n    getLink: function getLink(index, key, item) {\n      this.indexLast = item.length - 1;\n\n      if (key != item.length - 1) {\n        return;\n      }\n\n      this.activeIndex = index;\n      this.$refs.linkaddres.modals = true;\n    },\n    addBox: function addBox() {\n      if (this.configData.list.length == 0) {\n        this.lastObj.img = '';\n        this.lastObj.info[0].value = '';\n        this.configData.list.push(this.lastObj);\n      } else {\n        var obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]));\n        obj.img = '';\n        obj.info[0].value = '';\n        this.configData.list.push(obj);\n      }\n    },\n    // 点击图文封面\n    modalPicTap: function modalPicTap(title, index) {\n      this.activeIndex = index;\n      this.modalPic = true;\n    },\n    // 添加自定义弹窗\n    addCustomDialog: function addCustomDialog(editorId) {\n      window.UE.registerUI('test-dialog', function (editor, uiName) {\n        var dialog = new window.UE.ui.Dialog({\n          iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n          editor: editor,\n          name: uiName,\n          title: '上传图片',\n          cssRules: 'width:1200px;height:500px;padding:20px;'\n        });\n        this.dialog = dialog; // 参考上面的自定义按钮\n\n        var btn = new window.UE.ui.Button({\n          name: 'dialog-button',\n          title: '上传图片',\n          cssRules: \"background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;\",\n          onclick: function onclick() {\n            // 渲染dialog\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 37);\n    },\n    // 获取图片信息\n    getPic: function getPic(pc) {\n      var _this2 = this;\n\n      this.$nextTick(function () {\n        _this2.configData.list[_this2.activeIndex].img = pc.att_dir;\n        var data = _this2.defaults.menuConfig;\n\n        if (data && data.isCube) {\n          _this2.defaults.picStyle.picList.splice(_this2.defaults.picStyle.tabVal, 1, {\n            image: pc.att_dir,\n            link: data.list[0].info[0].value\n          });\n        }\n\n        _this2.modalPic = false;\n      });\n    },\n    onBlur: function onBlur() {\n      var data = this.defaults.menuConfig;\n      this.defaults.picStyle.picList[this.defaults.picStyle.tabVal].link = data.list[0].info[0].value;\n    },\n    // 删除\n    bindDelete: function bindDelete(item, index) {\n      if (this.configData.list.length == 1) {\n        this.lastObj = this.configData.list[0];\n      }\n\n      this.configData.list.splice(index, 1);\n    }\n  }\n};", null]}