{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\create.vue?vue&type=style&index=0&id=7977638a&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\create.vue", "mtime": 1719540491000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.tips{\n  display: inline-bolck;\n  font-size: 12px;\n  font-weight: 400;\n  color: #999999;\n  margin-top: 10px;\n}\n.imgPic{\n  .info{\n    width: 60%;\n    margin-left: 10px;\n  }\n  .pictrue{\n    height: 36px;\n    margin: 7px 3px 0 3px;\n    img{\n      height: 100%;\n      display: block;\n    }\n  }\n}\t\n.productType {\n  width: 120px;\n  height: 60px;\n  background: #FFFFFF;\n  border-radius: 3px;\n  border: 1px solid #E7E7E7;\n  float: left;\n  text-align: center;\n  padding-top: 8px;\n  position: relative;\n  cursor: pointer;\n  line-height: 23px;\n  margin-right: 12px;\n\n  &.on {\n    border-color: #1890FF;\n  }\n\n  .name {\n    font-size: 14px;\n    font-weight: 600;\n    color: rgba(0, 0, 0, 0.85);\n\n    &.on {\n      color: #1890FF;\n    }\n  }\n\n  .title {\n    font-size: 12px;\n    font-weight: 400;\n    color: #999999;\n  }\n\n  .jiao {\n    position: absolute;\n    bottom: 0;\n    right: 0;\n    width: 0;\n    height: 0;\n    border-bottom: 26px solid #1890FF;\n    border-left: 26px solid transparent;\n  }\n\n  .iconfont {\n    position: absolute;\n    bottom: -3px;\n    right: 1px;\n    color: #FFFFFF;\n    font-size: 12px;\n  }\n}\n\n.info {\n  color: #888;\n  font-size: 12px;\n}\n\n.ivu-input-wrapper {\n  width: 320px;\n}\n\n.ivu-input-number {\n  width: 160px;\n}\n\n.ivu-date-picker {\n  width: 320px;\n}\n\n.ivu-icon-ios-camera-outline {\n  width: 58px;\n  height: 58px;\n  border: 1px dotted rgba(0, 0, 0, 0.1);\n  border-radius: 4px;\n  background-color: rgba(0, 0, 0, 0.02);\n  line-height: 58px;\n  cursor: pointer;\n  vertical-align: middle;\n}\n\n.upload-list {\n  width: 58px;\n  height: 58px;\n  border: 1px dotted rgba(0, 0, 0, 0.1);\n  border-radius: 4px;\n  margin-right: 15px;\n  display: inline-block;\n  position: relative;\n  cursor: pointer;\n  vertical-align: middle;\n}\n\n.upload-list img {\n  display: block;\n  width: 100%;\n  height: 100%;\n}\n\n.ivu-icon-ios-close-circle {\n  position: absolute;\n  top: 0;\n  right: 0;\n  transform: translate(50%, -50%);\n}\n\n.form-submit {\n  /deep/.ivu-card {\n    border-radius: 0;\n  }\n\n  margin-bottom: 79px;\n\n  .fixed-card {\n    position: fixed;\n    right: 0;\n    bottom: 0;\n    left: 200px;\n    z-index: 99;\n    box-shadow: 0 -1px 2px rgb(240, 240, 240);\n\n    /deep/ .ivu-card-body {\n      padding: 15px 16px 14px;\n    }\n\n    .ivu-form-item {\n      margin-bottom: 0;\n    }\n\n    /deep/ .ivu-form-item-content {\n      margin-right: 124px;\n      text-align: center;\n    }\n\n    .ivu-btn {\n      height: 36px;\n      padding: 0 20px;\n    }\n  }\n}\n/deep/.vxe-tree-cell {\n   padding-left: 0!important;\n}\n", null]}