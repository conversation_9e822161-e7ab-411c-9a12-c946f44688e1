{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getWordsAll } from '@/api/diy';\nimport vuedraggable from 'vuedraggable';\nexport default {\n  name: 'c_hot_word',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    }\n  },\n  components: {\n    draggable: vuedraggable\n  },\n  data: function data() {\n    return {\n      hotWordList: [],\n      hotIndex: 1,\n      defaults: {},\n      configData: {},\n      wordList: []\n    };\n  },\n  created: function created() {\n    this.defaults = this.configObj;\n    this.configData = this.configObj[this.configNme];\n    this.wordsAll();\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        // this.hotWordList = nVal.hotList\n        this.configData = nVal[this.configNme];\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    wordsAll: function wordsAll() {\n      var _this = this;\n\n      getWordsAll().then(function (res) {\n        _this.wordList = res.data;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n      });\n    },\n    addHotTxt: function addHotTxt() {\n      // let obj = {}\n      // if(this.configData.list.length){\n      //     obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]))\n      // }else {\n      //     obj = {\n      //         val: ''\n      //     }\n      // }\n      var obj = {\n        val: ''\n      };\n      this.configData.list.push(obj); // this.$emit('input', this.hotWordList);\n    },\n    // 删除数组\n    bindDelete: function bindDelete(index) {\n      this.configData.list.splice(index, 1);\n    }\n  }\n};", null]}