{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_swipers_list.vue?vue&type=template&id=56ec5b72&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_swipers_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"hot_imgs\">\n        <div class=\"title\" v-if=\"configData.title\">\n            {{configData.title}}\n        </div>\n        <div class=\"list-box\">\n            <draggable\n                    class=\"dragArea list-group\"\n                    :list=\"configData.list\"\n                    group=\"peoples\"\n                    handle=\".move-icon\"\n            >\n                <div class=\"item\" v-for=\"(item,index) in configData.list\" :key=\"index\">\n\t\t\t\t\t<div class=\"delect-btn\" @click.stop=\"bindDelete(item,index)\" v-if=\"!configData.isCube\"><span class=\"iconfont-diy icondel_1\"></span></div>\n                    <div class=\"move-icon\">\n                        <span class=\"iconfont-diy icondrag\"></span>\n                    </div>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<div class=\"info\">\n\t\t\t\t\t\t\t<div class=\"info-item\">\n\t\t\t\t\t\t\t\t<span class=\"span\">{{item.imgTitle}}</span>\n\t\t\t\t\t\t\t\t<div class=\"img-box\" @click=\"modalPicTap('单选',index)\">\n\t\t\t\t\t\t\t\t    <img :src=\"item.img\" alt=\"\"  v-if=\"item.img\">\n\t\t\t\t\t\t\t\t    <div class=\"upload-box\" v-else><Icon type=\"ios-add\" size=\"50\" /></div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"info\">\n\t\t\t\t\t\t    <div class=\"info-item\" v-for=\"(infos,key) in item.info\" :key=\"key\">\n\t\t\t\t\t\t        <span class=\"span\">{{infos.title}}</span>\n\t\t\t\t\t\t        <div class=\"input-box\" @click=\"getLink(index,key,item.info)\">\n\t\t\t\t\t\t            <Input icon=\"ios-arrow-forward\" v-model=\"infos.value\" readonly :placeholder=\"infos.tips\" :maxlength=\"infos.max\" show-word-limit/>\n\t\t\t\t\t\t        </div>\n\t\t\t\t\t\t    </div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n                </div>\n            </draggable>\n            <div>\n                <Modal v-model=\"modalPic\" width=\"960px\" scrollable  footer-hide closable title='上传图片' :mask-closable=\"false\" :z-index=\"1\">\n                    <uploadPictures :isChoice=\"isChoice\" @getPic=\"getPic\" :gridBtn=\"gridBtn\" :gridPic=\"gridPic\" v-if=\"modalPic\"></uploadPictures>\n                </Modal>\n            </div>\n        </div>\n        <template v-if=\"configData.list\">\n            <div class=\"add-btn\" v-if=\"configData.list.length < configData.maxList\">\n                <Button class=\"btn\" type=\"primary\" ghost @click=\"addBox\">\n\t\t\t\t\t<span class=\"iconfont iconjiahao\"></span>添加\n\t\t\t\t</Button>\n            </div>\n        </template>\n        <linkaddress ref=\"linkaddres\" @linkUrl=\"linkUrl\"></linkaddress>\n    </div>\n", null]}