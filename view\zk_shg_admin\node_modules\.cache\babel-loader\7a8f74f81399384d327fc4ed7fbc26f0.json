{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport AreaBox from \"./AreaBox\";\nimport linkaddress from \"@/components/linkaddress\";\nexport default {\n  name: \"OperationFloor\",\n  components: {\n    AreaBox: AreaBox,\n    linkaddress: linkaddress\n  },\n  props: {\n    /**\n     * @description 图片数据对象\n     * @type {ImgData}\n     */\n    imgs: {\n      type: String,\n      // 图片类型\n      default: function _default() {\n        return \"\";\n      } // 默认值为空字符串\n\n    },\n\n    /**\n     * @description 是否为热门汤品\n     * @type {boolean}\n     */\n    isHotPot: {\n      type: Boolean,\n      // 布尔类型\n      default: function _default() {\n        return false;\n      } // 默认值为false\n\n    },\n\n    /**\n     * @description 图片区域数据对象\n     * @type {AreaData[]}\n     */\n    imgAreaData: {\n      type: Array,\n      // 数组类型\n      default: function _default() {\n        return [];\n      } // 默认值为空数组\n\n    },\n\n    /**\n     * @description 链接输入框样式对象\n     * @type {LinkInputStyle}\n     */\n    linkInputStyle: {\n      type: Object,\n      // 对象类型\n      default: function _default() {\n        return {\n          // 默认值为一个包含width属性的对象\n          width: \"300px\"\n        };\n      }\n    }\n  },\n  data: function data() {\n    return {\n      /**\n       * @description 对话框是否可见\n       * @type {boolean}\n       */\n      dialogVisible: false,\n\n      /**\n       * @description 开始的x坐标\n       * @type {number}\n       */\n      starX: 0,\n\n      /**\n       * @description 开始的y坐标\n       * @type {number}\n       */\n      starY: 0,\n\n      /**\n       * @description 区域宽度\n       * @type {number}\n       */\n      areaWidth: 0,\n\n      /**\n       * @description 区域高度\n       * @type {number}\n       */\n      areaHeight: 0,\n\n      /**\n       * @description 当前显示的图片索引\n       * @type {boolean}\n       */\n      caseShow: false,\n\n      /**\n       * @description 当前图片的宽度\n       * @type {null}\n       */\n      nowImgWidth: null,\n\n      /**\n       * @description 区域数据\n       * @type {Array}\n       */\n      areaData: [],\n\n      /**\n       * @description 当前显示的图片编号\n       * @type {number}\n       */\n      imgNum: 1,\n\n      /**\n       * @description 父元素宽度\n       * @type {number}\n       */\n      parentWidth: 0,\n\n      /**\n       * @description 父元素高度\n       * @type {number}\n       */\n      parentHeight: 0,\n\n      /**\n       * @description 默认宽度\n       * @type {number}\n       */\n      defaultWidth: 750,\n\n      /**\n       * @description 当前显示的图片索引\n       * @type {number}\n       */\n      itemIndex: 0\n    };\n  },\n  computed: {},\n  watch: {\n    imgAreaData: function imgAreaData(val) {\n      console.log(val, \"1111\");\n      this.areaData = _toConsumableArray(val);\n    }\n  },\n  mounted: function mounted() {\n    this.areaData = _toConsumableArray(this.imgAreaData);\n  },\n  methods: {\n    openModal: function openModal(type) {\n      var _this = this;\n\n      if (type) {\n        this.$nextTick(function () {\n          var parentDiv = document.querySelector(\"#img-box-container\"); //获取元素的宽高\n\n          _this.parentWidth = _this.defaultWidth; // this.parentWidth = parentDiv.clientWidth;\n\n          _this.parentHeight = parentDiv.clientHeight; // console.log(\"this.parentWidth\", this.parentWidth, this.parentHeight)\n        });\n      }\n    },\n    closeModal: function closeModal() {\n      var _this2 = this;\n\n      this.$Modal.confirm({\n        title: \"提示信息\",\n        content: \"<p>未保存内容，是否在离开前放弃保存？</p>\",\n        okText: \"确认\",\n        cancelText: \"取消\",\n        onOk: function onOk() {\n          _this2.$Modal.remove();\n\n          _this2.dialogVisible = false;\n        }\n      });\n    },\n    // 绘画热区开始\n    mouseDown: function mouseDown(e) {\n      var _this3 = this;\n\n      e.preventDefault();\n      this.caseShow = true; // 记录滑动的初始值\n\n      this.starX = e.layerX;\n      this.starY = e.layerY; // 鼠标滑动的过程\n\n      if (!document.onmousemove) {\n        var maxWidth = this.defaultWidth - e.layerX;\n\n        document.onmousemove = function (ev) {\n          if (ev.layerX - _this3.starX < maxWidth) {\n            _this3.areaWidth = ev.layerX - _this3.starX;\n          } else {\n            _this3.areaWidth = maxWidth;\n          }\n\n          _this3.areaHeight = ev.layerY - _this3.starY;\n        };\n      }\n    },\n    // 绘画热区结束\n    changeStop: function changeStop() {\n      document.onmousemove = null;\n      this.imgNum = this.areaData.length + 1;\n\n      if (this.caseShow && this.areaWidth > 10 && this.areaHeight > 10) {\n        var data = {\n          number: this.imgNum,\n          starX: this.starX,\n          starY: this.starY,\n          areaWidth: this.areaWidth,\n          areaHeight: this.areaHeight,\n          nowImgWidth: this.defaultWidth,\n          link: \"\"\n        };\n        this.areaData.push(data);\n      } // 初始化绘图\n\n\n      this.caseShow = false;\n      this.starX = 0;\n      this.starY = 0;\n      this.areaWidth = 0;\n      this.areaHeight = 0;\n    },\n    // 删除指定热区\n    delAreaBox: function delAreaBox(index) {\n      /* 删除某个热区 */\n      this.areaData.splice(index, 1);\n      this.$emit(\"delAreaData\", this.areaData);\n      /* 删除后 每个热区按顺序重新编号 */\n\n      if (this.areaData) {\n        var arr = this.areaData.filter(function (i) {\n          return i.number > index;\n        });\n        if (!arr) return;\n        arr.forEach(function (i) {\n          return i.number--;\n        });\n\n        if (this.areaData[this.areaData.length - 1]) {\n          this.imgNum = this.areaData[this.areaData.length - 1].number + 1;\n        } else {\n          this.imgNum = 1;\n        }\n      }\n    },\n    // 添加网址\n    addURL: function addURL(index, url) {\n      console.log(index, url);\n\n      var obj = _objectSpread({}, this.areaData[index], {\n        link: url\n      });\n\n      this.$set(this.areaData, index, obj);\n    },\n    // 保存热区信息\n    saveAreaData: function saveAreaData() {\n      if (this.areaData && !this.areaData.length || !this.checkData(this.areaData)) {\n        this.$Message.error(\"热区是否配置链接、是否至少添加一个热区?\");\n        return;\n      }\n\n      this.$emit(\"saveAreaData\", this.areaData);\n      this.dialogVisible = false;\n      this.$Message.success(\"编辑成功!\");\n    },\n\n    /**\n     * 检查列表中每个元素是否都有 link 属性\n     * @param {Array} list - 待检查的列表\n     * @returns {Boolean} - 是否所有元素都有 link 属性\n     */\n    checkData: function checkData(list) {\n      var isCheck = true;\n      list.some(function (val) {\n        if (!val.link) {\n          isCheck = false;\n        }\n      });\n      return isCheck;\n    },\n\n    /**\n     * @description 获取链接地址并打开添加链接的模态框\n     * @param {number} index - 当前项的索引值\n     */\n    getLink: function getLink(index) {\n      // 设置当前项的索引值\n      this.itemIndex = index; // 打开添加链接的模态框\n\n      this.$refs.linkaddres.modals = true;\n    },\n\n    /**\n     * @description 处理链接地址的输入事件\n     * @param {string} e - 链接地址\n     */\n    linkUrl: function linkUrl(e) {\n      // 将链接地址存储到对应的数据项中\n      this.areaData[this.itemIndex].link = e;\n    }\n  }\n};", null]}