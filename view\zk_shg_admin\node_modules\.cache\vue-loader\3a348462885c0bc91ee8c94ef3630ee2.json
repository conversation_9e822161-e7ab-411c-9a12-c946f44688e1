{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue?vue&type=template&id=d172c52a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"line-box\" v-if=\"configData\">\n        <div class=\"input-box\">\n            <draggable\n                    class=\"dragArea list-group\"\n                    :list=\"configData.list\"\n                    group=\"peoples\"\n                    handle=\".icon\"\n            >\n                <div class=\"input-item\" v-for=\"(item,index) in configData.list\" :key=\"index\">\n                    <div class=\"icon\">\n                        <span class=\"iconfont-diy iconxingzhuangjiehe\"></span>\n                    </div>\n                    <!-- <Input v-model=\"item.val\" maxlength=\"10\" placeholder=\"选填，不超过十个字\"  /> -->\n\t\t\t\t\t<Select v-model=\"item.val\">\n\t\t\t\t\t    <Option v-for=\"(val,index) in wordList\" :value=\"val.name\" :key=\"index\">{{ val.name }}</Option>\n\t\t\t\t\t</Select>\n                    <div class=\"delete\" @click.stop=\"bindDelete(index)\">\n\t\t\t\t\t\t<span class=\"iconfont icondel_2\"></span>\n                    </div>\n                </div>\n            </draggable>\n            <div class=\"add-btn\" @click=\"addHotTxt\" v-if=\"configData.list.length < 20\">\n                <Button class=\"btn\" type=\"primary\" ghost>\n\t\t\t\t\t<span class=\"iconfont iconjiahao\"></span>添加\n\t\t\t\t</Button>\n            </div>\n        </div>\n    </div>\n", null]}