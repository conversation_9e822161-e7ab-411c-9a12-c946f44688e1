{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\autoSend.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\autoSend.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getExpressData, orderExpressTemp, orderDeliveryList, orderSheetInfo, otherBatchDelivery } from \"@/api/order\";\nexport default {\n  name: \"orderSend\",\n  props: {\n    isAll: {\n      type: Number,\n      default: 1\n    },\n    ids: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    where: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      formItem: {\n        type: \"1\",\n        express_record_type: \"2\",\n        delivery_name: \"\",\n        delivery_id: \"\",\n        express_temp_id: \"\",\n        to_name: \"\",\n        to_tel: \"\",\n        to_addr: \"\",\n        sh_delivery: \"\",\n        fictitious_content: \"\"\n      },\n      modals: false,\n      express: [],\n      expressTemp: [],\n      deliveryList: [],\n      temp: {},\n      export_open: true\n    };\n  },\n  watch: {\n    \"formItem.express_temp_id\": function formItemExpress_temp_id(value) {}\n  },\n  methods: {\n    changeRadio: function changeRadio(o) {\n      this.$refs.formItem.resetFields();\n\n      switch (o) {\n        case \"1\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.delivery_id = \"\";\n          this.formItem.express_temp_id = \"\";\n          this.formItem.express_record_type = \"2\";\n          this.expressTemp = [];\n          break;\n\n        case \"2\":\n          this.formItem.sh_delivery = \"\";\n          this.formItem.express_record_type = \"1\";\n          break;\n\n        case \"3\":\n          this.formItem.fictitious_content = \"\";\n          this.formItem.express_record_type = \"1\";\n          break;\n      }\n    },\n    changeExpress: function changeExpress(j) {\n      switch (j) {\n        case \"2\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.express_temp_id = \"\";\n          this.expressTemp = [];\n          break;\n\n        case \"1\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.delivery_id = \"\";\n          break;\n\n        default:\n          break;\n      }\n    },\n    reset: function reset() {\n      this.formItem = {\n        type: \"1\",\n        express_record_type: \"2\",\n        delivery_name: \"\",\n        delivery_id: \"\",\n        express_temp_id: \"\",\n        expressTemp: [],\n        to_name: \"\",\n        to_tel: \"\",\n        to_addr: \"\",\n        sh_delivery: \"\",\n        fictitious_content: \"\"\n      };\n    },\n    // 物流公司列表\n    getList: function getList() {\n      var _this = this;\n\n      getExpressData(1).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this.express = res.data;\n\n                  _this.getSheetInfo();\n\n                case 2:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.loading = false;\n\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 提交\n    putSend: function putSend(name) {\n      var _this2 = this;\n\n      var data = Object.assign(this.formItem);\n      var arr = [];\n      this.ids.forEach(function (item) {\n        arr.push(item.id);\n      });\n\n      if (this.isAll == 1) {\n        data.all = 1;\n        data.where = this.where;\n      } else {\n        data.all = 0;\n        data.ids = arr;\n      }\n\n      if (this.formItem.type === \"1\") {\n        if (this.formItem.delivery_name === \"\") {\n          return this.$Message.error(\"快递公司不能为空\");\n        } else if (this.formItem.express_temp_id === \"\") {\n          return this.$Message.error(\"电子面单不能为空\");\n        } else if (this.formItem.to_name === \"\") {\n          return this.$Message.error(\"寄件人姓名不能为空\");\n        } else if (this.formItem.to_tel === \"\") {\n          return this.$Message.error(\"寄件人电话不能为空\");\n        } else if (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(this.formItem.to_tel)) {\n          return this.$Message.error(\"请输入正确的手机号码\");\n        } else if (this.formItem.to_addr === \"\") {\n          return this.$Message.error(\"寄件人地址不能为空\");\n        }\n      }\n\n      if (this.formItem.type === \"2\") {\n        if (this.formItem.express_temp_id) {\n          this.formItem.express_temp_id = \"\";\n        }\n\n        if (this.formItem.sh_delivery === \"\") {\n          return this.$Message.error(\"送货人不能为空\");\n        }\n      }\n\n      otherBatchDelivery(data).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  _this2.modals = false;\n\n                  _this2.$Message.success(res.msg);\n\n                  _this2.reset();\n\n                case 3:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.$Message.error(res.msg);\n\n        _this2.modals = false;\n      });\n    },\n    cancel: function cancel(name) {\n      this.modals = false;\n      this.reset();\n    },\n    // 电子面单列表\n    expressChange: function expressChange(value) {\n      var _this3 = this;\n\n      var expressItem = this.express.find(function (item) {\n        return item.value === value;\n      });\n\n      if (!expressItem) {\n        return;\n      }\n\n      this.formItem.delivery_code = expressItem.code;\n\n      if (this.formItem.type === \"1\") {\n        this.expressTemp = [];\n        this.formItem.express_temp_id = \"\";\n        orderExpressTemp({\n          com: this.formItem.delivery_code\n        }).then(function (res) {\n          _this3.expressTemp = res.data;\n\n          if (!res.data.length) {\n            _this3.$Message.error(\"请配置你所选快递公司的电子面单\");\n          }\n        }).catch(function (err) {\n          _this3.$Message.error(err.msg);\n        });\n      }\n    },\n    getDeliveryList: function getDeliveryList() {\n      var _this4 = this;\n\n      orderDeliveryList().then(function (res) {\n        _this4.deliveryList = res.data.list;\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    },\n    getSheetInfo: function getSheetInfo() {\n      var _this5 = this;\n\n      orderSheetInfo().then(function (res) {\n        var data = res.data;\n\n        for (var key in data) {\n          if (data.hasOwnProperty(key) && key !== \"express_temp_id\") {\n            _this5.formItem[key] = data[key];\n          }\n        }\n\n        _this5.export_open = data.export_open === undefined ? true : data.export_open;\n\n        if (!_this5.export_open) {\n          _this5.formItem.express_record_type = \"1\";\n        }\n\n        _this5.formItem.to_addr = data.to_add;\n      }).catch(function (err) {\n        _this5.$Message.error(err.msg);\n      });\n    },\n    shDeliveryChange: function shDeliveryChange(value) {\n      var deliveryItem = this.deliveryList.find(function (item) {\n        return item.id === value;\n      });\n      this.formItem.sh_delivery_name = deliveryItem.wx_name;\n      this.formItem.sh_delivery_id = deliveryItem.phone;\n      this.formItem.sh_delivery_uid = deliveryItem.uid;\n    },\n    expressTempChange: function expressTempChange(tempId) {\n      this.temp = this.expressTemp.find(function (item) {\n        return tempId === item.temp_id;\n      });\n    },\n    preview: function preview() {\n      this.$refs.viewer.$viewer.show();\n    }\n  }\n};", null]}