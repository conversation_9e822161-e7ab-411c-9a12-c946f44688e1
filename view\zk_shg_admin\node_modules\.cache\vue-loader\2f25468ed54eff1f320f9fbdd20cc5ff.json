{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_label.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_label.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\timport storeLabelList from \"@/components/storeLabelList\";\n    export default {\n        name: 'c_goods_label',\n\t\tcomponents:{\n\t\t   storeLabelList\n\t\t},\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            },\n            number: {\n                type: null\n            },\n        },\n        data () {\n            return {\n                defaults: {},\n                configData: {},\n                timeStamp: '',\n\t\t\t\tstoreLabelShow:false,\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj\n                this.configData = this.configObj[this.configNme]\n            })\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n                },\n                deep: true\n            },\n            number (nVal) {\n              this.timeStamp = nVal;\n            },\n        },\n        methods: {\n\t\t\topenStoreLabel(row) {\n\t\t\t  this.storeLabelShow = true;\n\t\t\t  this.$refs.storeLabel.storeLabel(JSON.parse(JSON.stringify(this.configData.list)));\n\t\t\t},\n\t\t\tcloseStoreLabel(label){\n\t\t\t  let index = this.configData.list.indexOf(this.configData.list.filter(d=>d.id == label.id)[0]);\n\t\t\t  this.configData.list.splice(index,1);\n\t\t\t  this.getLabelId(this.configData.list);\n\t\t\t},\n\t\t\tactiveStoreData(storeDataLabel){\n\t\t\t  this.storeLabelShow = false;\n\t\t\t  this.configData.list = storeDataLabel;\n\t\t\t  this.getLabelId(storeDataLabel);\n\t\t\t},\n\t\t\tgetLabelId(storeDataLabel){\n\t\t\t  let storeActiveIds = [];\n\t\t\t  storeDataLabel.forEach((item)=>{\n\t\t\t    storeActiveIds.push(item.id)\n\t\t\t  });\n\t\t\t  this.configData.activeValue = storeActiveIds;\n\t\t\t  this.$emit('getConfig', { name: 'goodsLabel'})\n\t\t\t},\n\t\t\t// 标签弹窗关闭\n\t\t\tstoreLabelClose() {\n\t\t\t  this.storeLabelShow = false;\n\t\t\t},\n\t\t}\n    }\n", null]}