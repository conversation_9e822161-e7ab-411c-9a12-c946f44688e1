{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from 'vuex';\nimport { levelListApi, setShowApi, createApi } from '@/api/user';\nimport taskList from './handle/task';\nimport editFrom from '@/components/from/from';\nexport default {\n  name: 'user_level',\n  components: {\n    editFrom: editFrom,\n    taskList: taskList\n  },\n  data: function data() {\n    return {\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns1: [{\n        title: '等级',\n        key: 'grade',\n        minWidth: 100\n      }, {\n        title: '等级图标',\n        slot: 'icons',\n        minWidth: 100\n      }, {\n        title: '等级名称',\n        key: 'name',\n        minWidth: 120\n      }, {\n        title: '享受折扣(%)',\n        key: 'discount',\n        minWidth: 100\n      }, {\n        title: '解锁经验值',\n        key: 'exp_num',\n        minWidth: 100\n      }, // {\n      //     title: '有效时间',\n      //     key: 'valid_date',\n      //     minWidth: 120\n      // },\n      // {\n      //     title: '是否永久',\n      //     slot: 'is_forevers',\n      //     minWidth: 130\n      // },\n      // {\n      //     title: '是否付费',\n      //     slot: 'is_pays',\n      //     minWidth: 120\n      // },\n      {\n        title: '是否显示',\n        slot: 'is_shows',\n        minWidth: 120\n      }, {\n        title: '等级说明',\n        key: 'explain',\n        minWidth: 120\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        width: 120\n      }],\n      levelFrom: {\n        is_show: '',\n        title: '',\n        page: 1,\n        limit: 10\n      },\n      levelLists: [],\n      total: 0,\n      FromData: null,\n      imgName: '',\n      visible: false,\n      levelId: 0,\n      modalTitleSs: '',\n      titleType: 'level',\n      modelTask: false,\n      num: 0\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  methods: _objectSpread({}, mapMutations('admin/userLevel', ['getlevelId']), {\n    // 操作\n    changeMenu: function changeMenu(row, name, num) {\n      this.levelId = row.id;\n\n      switch (name) {\n        case '1':\n          this.getlevelId(this.levelId);\n          this.$refs.tasks.modals = true;\n          this.$refs.tasks.getList();\n          break;\n\n        default:\n          this.del(row, '删除等级', num);\n      }\n    },\n    // 删除\n    del: function del(row, tit, num) {\n      var _this = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"user/user_level/delete/\".concat(row.id),\n        method: 'put',\n        ids: ''\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this.$Message.success(res.msg);\n\n        _this.levelLists.splice(num, 1);\n\n        if (!_this.levelLists.length) {\n          _this.levelFrom.page = _this.levelFrom.page == 1 ? 1 : _this.levelFrom.page - 1;\n        }\n\n        _this.getList();\n      }).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 删除成功\n    // submitModel () {\n    //     this.levelLists.splice(this.delfromData.num, 1)\n    // },\n    // 修改是否显示\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this2 = this;\n\n      var data = {\n        id: row.id,\n        is_show: row.is_show\n      };\n      setShowApi(data).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this2.$Message.success(res.msg);\n\n                case 1:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    // 等级列表\n    getList: function getList() {\n      var _this3 = this;\n\n      this.loading = true;\n      this.levelFrom.is_show = this.levelFrom.is_show || '';\n      levelListApi(this.levelFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  data = res.data;\n                  _this3.levelLists = data.list;\n                  _this3.total = res.data.count;\n                  _this3.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this3.loading = false;\n\n        _this3.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.levelFrom.page = index;\n      this.getList();\n    },\n    // 添加\n    add: function add() {\n      var _this4 = this;\n\n      this.levelId = 0;\n      this.$modalForm(createApi({\n        id: this.levelId\n      })).then(function () {\n        return _this4.getList();\n      });\n    },\n    // 编辑\n    edit: function edit(row) {\n      var _this5 = this;\n\n      this.levelId = row.id;\n      this.$modalForm(createApi({\n        id: this.levelId\n      })).then(function () {\n        return _this5.getList();\n      });\n      this.getlevelId(this.levelId);\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.levelFrom.page = 1;\n      this.getList();\n    },\n    // 修改成功\n    submitFail: function submitFail() {\n      this.getList();\n    }\n  })\n};", null]}