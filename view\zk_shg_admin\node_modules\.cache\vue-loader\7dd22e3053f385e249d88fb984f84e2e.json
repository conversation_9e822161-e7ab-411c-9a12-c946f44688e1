{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_search_box.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_search_box.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { mapMutations } from 'vuex'\n    export default {\n        name: 'c_search_box',\n        componentsName: 'search_box',\n        cname: '搜索框',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        data () {\n            return {\n                hotIndex: 1,\n                configObj: {}, // 配置对象\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ],// 当前页面组件\n\t\t\t\toneContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'styleConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContentType:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'styleTypeConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContentFix:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'fixConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContent:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_upload_img,\n\t\t\t\t\t  configNme: 'logoConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeContent:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'titleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'linkConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\trComContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleSearch'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'tipConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleHotWords'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_hot_word,\n\t\t\t\t\t  configNme: 'hotWords'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_number,\n\t\t\t\t\t  configNme: 'numConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'searchBoxColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'tipColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'hotWordsColor'\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttwoStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleTxt'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'txtFixConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle1:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'txtStyleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'txtColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'txtSize'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n                setUp: 0,\n                type: 0,\n\t\t\t\ttype2: 0\n            }\n        },\n        watch: {\n            num (nVal) {\n                // debugger;\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n                    this.setUp = nVal;\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n\t\t\t\t\t\tthis.getRComContent(arr);\n                    } else {\n\t\t\t\t\t\tthis.getRComStyle(arr)\n                    }\n                },\n                deep: true\n            },\n\t\t\t'configObj.styleConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tthis.getRComContent(arr);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.getRComStyle(arr)\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.styleTypeConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tthis.getRComContent(arr);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.getRComStyle(arr)\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n\t\t\tgetRComContent(arr){\n\t\t\t\tif(this.type == 1){\n\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.threeContent]\n\t\t\t\t}else if(this.type == 2){\n\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentFix]\n\t\t\t\t}else{\n\t\t\t\t\tif(this.type2 == 0){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentType,...this.threeContent,...this.rComContent]\n\t\t\t\t\t}else if(this.type2 == 1){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentType,...this.oneContentFix,...this.rComContent]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentType,...this.twoContent,...this.rComContent]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetRComStyle(arr){\n\t\t\t\tif(this.type == 0){\n\t\t\t\t\tif(this.type2 == 2){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.currencyStyle]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.twoStyle1,...this.currencyStyle]\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tthis.rCom = [...arr,...this.twoStyle,...this.twoStyle1,...this.currencyStyle]\n\t\t\t\t}\n\t\t\t}\n\t\t}\n    }\n", null]}