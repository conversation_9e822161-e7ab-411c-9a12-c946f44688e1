{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_bg_color.vue?vue&type=template&id=5e9e6f92&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_bg_color.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div>\n        <div class=\"c_row-item\" v-if=\"configData\">\n            <Col class=\"c_label\" >{{configData.title}}</Col>\n            <Col class=\"color-box\">\n                <div class=\"color-item\" v-for=\"(color,key) in configData.color\" :key=\"key\">\n                    <ColorPicker v-model=\"color.item\" @on-change=\"changeColor($event,color)\" alpha ></ColorPicker>\n\t\t\t\t\t<Input class=\"input\" v-model=\"color.item\"/>\n\t\t\t\t\t<span @click=\"resetBgA(color,index,key)\">重置</span>\n                </div>\n\t\t\t\t<div class=\"iconfont iconlianjie\" v-if=\"configData.color.length>1\"></div>\n            </Col>\n        </div>\n    </div>\n\n", null]}