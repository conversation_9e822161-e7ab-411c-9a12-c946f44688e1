{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormConfig\\c_home_time.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormConfig\\c_home_time.vue", "mtime": 1682663004000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport toolCom from '@/components/mobileConfigRight/index.js'\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapState, mapMutations, mapActions } from 'vuex'\nexport default {\n  name: 'c_home_time',\n  componentsName: 'home_time',\n  components: {\n    ...toolCom,\n    rightBtn\n  },\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  data () {\n    return {\n      configObj: {},\n      rCom: [\n        {\n          components: toolCom.c_input_item,\n          configNme: 'titleConfig'\n        },\n        {\n          components: toolCom.c_comb_data,\n          configNme: 'valConfig'\n        },\n        {\n          components: toolCom.c_input_item,\n          configNme: 'tipConfig'\n        },\n        {\n          components: toolCom.c_is_show,\n          configNme: 'titleShow'\n        },\n      ]\n    }\n  },\n  watch: {\n    num (nVal) {\n      let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n      this.configObj = value;\n    },\n    configObj: {\n      handler (nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n      },\n      deep: true\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n      this.configObj = value;\n    })\n  },\n  methods: {\n    // 获取组件参数\n    getConfig (data) {},\n  }\n}\n", null]}