{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue?vue&type=template&id=5889e2f3&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue", "mtime": 1709543824155}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt box\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Form',{ref:\"formValidate\",attrs:{\"inline\":\"\",\"model\":_vm.formValidate,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"创建时间：\"}},[_c('DatePicker',{staticClass:\"input-add\",attrs:{\"editable\":false,\"value\":_vm.timeVal,\"format\":\"yyyy/MM/dd\",\"type\":\"datetimerange\",\"placement\":\"bottom-start\",\"placeholder\":\"自定义时间\",\"options\":_vm.options},on:{\"on-change\":_vm.onchangeTime}})],1),_c('FormItem',[_c('Button',{staticStyle:{\"margin-left\":\"-75px\",\"margin-right\":\"14px\"},attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.search()}}},[_vm._v(\"查询\")])],1)],1)],1),_c('cards-data',{attrs:{\"cardLists\":_vm.cardLists}}),_c('Card',{staticClass:\"ive-mt tablebox\",attrs:{\"bordered\":false,\"dis-hover\":\"\",\"padding\":20}},[_c('div',{staticClass:\"table\"},[_c('Table',{ref:\"table\",attrs:{\"columns\":_vm.columns,\"data\":_vm.orderList,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"number\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.amount < 0)?_c('span',{staticClass:\"colorgreen\"},[_vm._v(_vm._s(row.amount))]):_vm._e(),(row.amount >= 0)?_c('span',{staticClass:\"colorred\"},[_vm._v(\"+ \"+_vm._s(row.amount))]):_vm._e()]}},{key:\"type\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.tradeType == 457)?_c('span',{staticClass:\"colorred\"},[_vm._v(\"\\n            备查款账户返余(充值) \\n          \")]):_vm._e(),(row.tradeType == 758)?_c('span',{staticClass:\"colorred\"},[_vm._v(\"\\n            余额支付(取消返回) \\n          \")]):_vm._e(),(row.tradeType == 1209)?_c('span',{staticClass:\"colorgreen\"},[_vm._v(\"\\n            实物或礼品卡余额支付(下单扣减)\\n          \")]):_vm._e()]}}])})],1),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"current\":_vm.formValidate.page,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.formValidate.limit},on:{\"on-change\":_vm.pageChange}})],1)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}