{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\group\\index.vue?vue&type=template&id=a7d71d86&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\group\\index.vue", "mtime": 1683594784000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Row',{attrs:{\"type\":\"flex\"}},[_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('Button',{directives:[{name:\"auth\",rawName:\"v-auth\",value:(['admin-user-group']),expression:\"['admin-user-group']\"}],attrs:{\"type\":\"primary\"},on:{\"click\":_vm.add}},[_vm._v(\"添加分组\")])],1)],1),_c('Table',{ref:\"table\",staticClass:\"mt25\",attrs:{\"columns\":_vm.columns1,\"data\":_vm.groupLists,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"icons\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('viewer',[_c('div',{staticClass:\"tabBox_img\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.icon),expression:\"row.icon\"}]})])])]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.edit(row.id)}}},[_vm._v(\"修改\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":function($event){return _vm.del(row,'删除分组',index)}}},[_vm._v(\"删除\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.groupFrom.limit},on:{\"on-change\":_vm.pageChange}})],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}