{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_select_item.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_select_item.vue", "mtime": 1682663004000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'c_select_item',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    }\n  },\n  data () {\n    return {\n      defaults: {},\n      configData: {},\n      batchWord: '',\n      visible:false,\n    }\n  },\n  created () {\n    this.defaults = this.configObj\n    this.configData = this.configObj[this.configNme]\n  },\n  watch: {\n    configObj: {\n      handler (nVal, oVal) {\n        this.configData = nVal[this.configNme]\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    cancel(num){\n      this.visible = false;\n      if(num==2){\n        let arr = this.batchWord.split('\\n');\n        let arrNew = [];\n        arr.forEach(item=>{\n          let obj = {};\n          obj['val'] = item;\n          arrNew.push(obj)\n        })\n        this.configData.list = this.configData.list.concat(arrNew);\n      }\n      this.batchWord = '';\n    },\n    addHotTxt () {\n      let obj = {\n        val: ''\n      };\n      this.configData.list.push(obj)\n    },\n    // 删除数组\n    bindDelete (index) {\n      this.configData.list.splice(index, 1)\n    },\n  }\n}\n", null]}