{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\orderDetall.vue?vue&type=template&id=4c40a268&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\orderDetall.vue", "mtime": 1694567788000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"order_detail\" v-if=\"orderDetail.userInfo\">\n    <div class=\"msg-box\">\n        <div class=\"box-title\">收货信息</div>\n        <div class=\"msg-wrapper\">\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>用户昵称：</span>{{orderDetail.userInfo.nickname}}\n                </div>\n                <div class=\"item\">\n                    <span>收货人：</span>{{orderDetail.orderInfo.real_name}}\n                </div>\n            </div>\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>联系电话：</span>{{orderDetail.orderInfo.user_phone}}\n                </div>\n                <div class=\"item\">\n                    <span>收货地址：</span>{{orderDetail.orderInfo.user_address}}\n                </div>\n            </div>\n        </div>\n    </div>\n    <div class=\"msg-box\" style=\"border: none;\">\n        <div class=\"box-title\">订单信息</div>\n        <div class=\"msg-wrapper\">\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>订单ID：</span>{{orderDetail.orderInfo.order_id}}\n                </div>\n                <div class=\"item\" style=\"color: red\">\n                    <span style=\"color: red\">订单状态：</span>{{orderDetail.orderInfo._status._title}}\n                </div>\n            </div>\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>商品总数：</span>{{orderDetail.orderInfo.total_num}}\n                </div>\n                <div class=\"item\">\n                    <span>商品总价：</span>￥{{orderDetail.orderInfo.total_price}}\n                </div>\n            </div>\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>交付邮费：</span>￥{{orderDetail.orderInfo.pay_postage}}\n                </div>\n                <div class=\"item\">\n                    <span>优惠券金额：</span>￥{{orderDetail.orderInfo.coupon_price}}\n                </div>\n            </div>\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>会员商品优惠：</span>￥{{orderDetail.orderInfo.vip_true_price||0.00}}\n                </div>\n                <div class=\"item\">\n                    <span>积分抵扣：</span>￥{{orderDetail.orderInfo.deduction_price||0.00}}\n                </div>\n            </div>\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>实际支付：</span>{{orderDetail.orderInfo.pay_price}}\n                </div>\n                <div class=\"item\">\n                    <span>首单优惠：</span>{{orderDetail.orderInfo.first_order_price}}\n                </div>\n            </div>\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>创建时间：</span>{{orderDetail.orderInfo.add_time}}\n                </div>\n                <div class=\"item\">\n                    <span>支付方式：</span>{{orderDetail.orderInfo._status._payType}}\n                </div>\n            </div>\n            <div class=\"msg-item\">\n                <div class=\"item\">\n                    <span>推广人：</span>{{orderDetail.userInfo.spread_name}}\n                </div>\n                <div class=\"item\">\n                    <span>商家备注：</span>{{orderDetail.orderInfo.mark}}\n                </div>\n            </div>\n        </div>\n    </div>\n    <div class=\"goods-box\">\n        <Table :columns=\"columns1\" :data=\"orderList\">\n            <template slot-scope=\"{ row, index }\" slot=\"id\">\n                {{row.productInfo.id}}\n            </template>\n            <template slot-scope=\"{ row, index }\" slot=\"name\">\n                <div class=\"product_info\">\n                    <img :src=\"row.productInfo.image\" alt=\"\">\n                    <p>{{row.productInfo.store_name}}</p>\n                </div>\n            </template>\n            <template slot-scope=\"{ row, index }\" slot=\"className\">\n                {{row.class_name}}\n            </template>\n            <template slot-scope=\"{ row, index }\" slot=\"price\">\n                {{row.productInfo.attrInfo.price}}\n            </template>\n            <template slot-scope=\"{ row, index }\" slot=\"total_num\">\n                {{orderDetail.orderInfo.total_num}}\n            </template>\n        </Table>\n    </div>\n    <Spin fix v-if=\"spinShow\"></Spin>\n</div>\n", null]}