{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\uploadPictures\\widgetImg.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\uploadPictures\\widgetImg.vue", "mtime": 1730707874008}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n\r\nimport uploadFrom from './index';\r\nexport default {\r\n  name: 'widgetImg',\r\n  components: { uploadFrom },\r\n  data() {\r\n    return {\r\n      isChoice: '单选',\r\n      isChoiceD: '多选',\r\n      gridPic: {\r\n        xl: 4,\r\n        lg: 4,\r\n        md: 8,\r\n        sm: 12,\r\n        xs: 12\r\n      },\r\n      gridBtn: {\r\n        xl: 4,\r\n        lg: 4,\r\n        md: 4,\r\n        sm: 8,\r\n        xs: 8\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    getPicD(pc) {\r\n      if (this.$route.query.fodder === 'dialog') {\r\n        // let str = ''\r\n        // for (let i = 0; i < pc.length; i++) {\r\n        //     nowEditor.editor.execCommand('insertimage', { src: pc[i].att_dir })\r\n        // }\r\n        // nowEditor.dialog.close(true)\r\n      } else {\r\n        const fodder = this.$route.query.fodder;\r\n        if (typeof form_create_helper !== 'undefined') {\r\n          const pcs = window.form_create_helper.get(this.$route.query.fodder) || [];\r\n          pc = pc.map(item => {\r\n            return item.att_dir;\r\n          });\r\n          const concatPc = pcs.concat(pc);\r\n          const pcList = Array.from(new Set(concatPc));\r\n          form_create_helper.set(fodder, pcList);\r\n          form_create_helper.close(fodder);\r\n        } else {\r\n          const pcs = window.parent.$fromSubmit[fodder].getValue() || [];\r\n          pc = pc.map(item => {\r\n            return item.att_dir;\r\n          });\r\n          const concatPc = pcs.concat(pc);\r\n          const pcList = Array.from(new Set(concatPc));\r\n          window.parent.$fromSubmit[fodder].setValue(pcList);\r\n          window.parent.$fromSubmit[fodder].closeUploadFrame();\r\n        }\r\n      }\r\n    },\r\n    getPic(pc) {\r\n      const fodder = this.$route.query.fodder;\r\n      if (typeof form_create_helper !== 'undefined') {\r\n        form_create_helper.set(fodder, pc.att_dir);\r\n        form_create_helper.close(fodder);\r\n      } else {\r\n        window.parent.$fromSubmit[fodder].setValue(pc);\r\n        window.parent.$fromSubmit[fodder].closeUploadFrame();\r\n      }\r\n    }\r\n    // getPic (pc) {\r\n    //     if (this.$route.query.fodder === 'dialog') {\r\n    //         /* eslint-disable */\r\n    //         nowEditor.dialog.close(true);\r\n    //         nowEditor.editor.setContent('<img src=\"'+pc.att_dir+'\">',true);\r\n    //     }\r\n    //     else {\r\n    //         form_create_helper.set(this.$route.query.fodder, pc.satt_dir)\r\n    //         form_create_helper.close(this.$route.query.fodder);\r\n    //     }\r\n    // }\r\n  }\r\n};\r\n", null]}