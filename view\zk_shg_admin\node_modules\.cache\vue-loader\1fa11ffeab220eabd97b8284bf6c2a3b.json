{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\setupUser\\index.vue?vue&type=template&id=85284970&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\setupUser\\index.vue", "mtime": 1689129842000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Tabs',{on:{\"on-click\":_vm.tabChange},model:{value:(_vm.tabVal),callback:function ($$v) {_vm.tabVal=$$v},expression:\"tabVal\"}},[_c('TabPane',{attrs:{\"label\":\"基础信息\",\"name\":\"basic\"}},[_c('Form',{attrs:{\"model\":_vm.basicsForm,\"label-width\":120}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"basics\"},[_vm._v(\"用户设置\")])]),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"用户默认头像：\"}},[(_vm.authorizedPicture)?_c('div',{staticClass:\"uploadPictrue\",on:{\"click\":function($event){return _vm.modalPicTap('单选')}}},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.authorizedPicture),expression:\"authorizedPicture\"}]})]):_c('div',{staticClass:\"uploadPictrue\",on:{\"click\":function($event){return _vm.modalPicTap('单选')}}},[_c('span',{staticClass:\"iconfont iconshangpinshuliang-jia\"})]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"建议尺寸：120*120px\")])])],1),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"14\"}},[_c('FormItem',{attrs:{\"label\":\"用户信息设置：\"}},[_c('Table',{ref:\"table\",staticClass:\"mt25 goods\",attrs:{\"data\":_vm.listOne,\"columns\":_vm.columns,\"highlight-row\":\"\",\"draggable\":true},on:{\"on-drag-drop\":_vm.onDragDrop},scopedSlots:_vm._u([{key:\"drag\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',{staticClass:\"iconfont icondrag\"})]}},{key:\"use\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('Checkbox',{attrs:{\"true-value\":1,\"false-value\":0},model:{value:(_vm.listOne[index].use),callback:function ($$v) {_vm.$set(_vm.listOne[index], \"use\", $$v)},expression:\"listOne[index].use\"}})]}},{key:\"required\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('Checkbox',{attrs:{\"disabled\":_vm.listOne[index].use == 0,\"true-value\":1,\"false-value\":0},model:{value:(_vm.listOne[index].required),callback:function ($$v) {_vm.$set(_vm.listOne[index], \"required\", $$v)},expression:\"listOne[index].required\"}})]}},{key:\"user_show\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('Checkbox',{attrs:{\"disabled\":_vm.listOne[index].use == 0,\"true-value\":1,\"false-value\":0},model:{value:(_vm.listOne[index].user_show),callback:function ($$v) {_vm.$set(_vm.listOne[index], \"user_show\", $$v)},expression:\"listOne[index].user_show\"}})]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(!_vm.listOne[index].param)?_c('a',{on:{\"click\":function($event){return _vm.delInfo(index)}}},[_vm._v(\"删除\")]):_vm._e()]}}])}),_c('div',{staticClass:\"upload-text goods\"},[_vm._v(\"\\n                    开启使用后，后台添加用户时可填写此信息；开启必填后，后台添加用户时此信息必须填写；开启用户端展示后，在商城用户个人信息中展示\\n                  \")]),_c('div',{staticClass:\"addInfo\",on:{\"click\":function($event){_vm.addModel = true}}},[_vm._v(\"新增信息\")]),_c('div',{staticClass:\"subBtn mt20\",on:{\"click\":function($event){return _vm.handleSubmit('basic')}}},[_vm._v(\"\\n                    保存\\n                  \")])],1)],1)],1)],1)],1),_c('TabPane',{attrs:{\"label\":\"登录注册\",\"name\":\"register\"}},[_c('Alert',{attrs:{\"type\":\"warning\",\"show-icon\":\"\"}},[_vm._v(_vm._s(_vm.loginForm.register_notice))]),_c('Form',{attrs:{\"model\":_vm.loginForm,\"label-width\":120}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"basics\"},[_vm._v(\"登录设置\")])]),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"强制手机号绑定：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.loginForm.store_user_mobile),callback:function ($$v) {_vm.$set(_vm.loginForm, \"store_user_mobile\", $$v)},expression:\"loginForm.store_user_mobile\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"商城登录时强制手机号登陆/绑定\")])],1)],1),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"用户协议：\"}},[_c('RadioGroup',{model:{value:(_vm.loginForm.store_user_agreement),callback:function ($$v) {_vm.$set(_vm.loginForm, \"store_user_agreement\", $$v)},expression:\"loginForm.store_user_agreement\"}},[_c('Radio',{attrs:{\"label\":\"0\"}},[_c('span',[_vm._v(\"自动同意\")])]),_c('Radio',{attrs:{\"label\":\"1\"}},[_c('span',[_vm._v(\"手动同意\")])])],1),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"商城登录时用户协议选定\")])],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"basics\"},[_vm._v(\"注册有礼\")])]),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"注册有礼启用：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.loginForm.newcomer_status),callback:function ($$v) {_vm.$set(_vm.loginForm, \"newcomer_status\", $$v)},expression:\"loginForm.newcomer_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"新用户注册后，会给用户赠送礼品\")])],1)],1),(_vm.loginForm.newcomer_status === 1)?_c('Col',{attrs:{\"span\":\"24\"}},[(_vm.loginForm.newcomer_status === 1)?_c('FormItem',{attrs:{\"label\":\"是否限时：\"}},[_c('RadioGroup',{model:{value:(_vm.loginForm.newcomer_limit_status),callback:function ($$v) {_vm.$set(_vm.loginForm, \"newcomer_limit_status\", $$v)},expression:\"loginForm.newcomer_limit_status\"}},[_c('Radio',{attrs:{\"label\":\"0\"}},[_c('span',[_vm._v(\"不限时\")])]),_c('Radio',{attrs:{\"label\":\"1\"}},[_c('span',[_vm._v(\"限时\")])])],1),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"新人注册活动的时间设置\")]),(_vm.loginForm.newcomer_limit_status == 1)?_c('div',{staticClass:\"mt10\"},[_c('Input',{staticClass:\"inputw\",attrs:{\"placeholder\":\"请输入限时天数\"},model:{value:(_vm.loginForm.newcomer_limit_time),callback:function ($$v) {_vm.$set(_vm.loginForm, \"newcomer_limit_time\", $$v)},expression:\"loginForm.newcomer_limit_time\"}}),(_vm.loginForm.newcomer_limit_status == 1)?_c('span',{staticClass:\"span-text\"},[_vm._v(\"\\n                      天\\n                    \")]):_vm._e()],1):_vm._e()],1):_vm._e()],1):_vm._e(),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[(_vm.loginForm.newcomer_status === 1)?_c('FormItem',{attrs:{\"label\":\"赠送积分：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.loginForm.register_integral_status),callback:function ($$v) {_vm.$set(_vm.loginForm, \"register_integral_status\", $$v)},expression:\"loginForm.register_integral_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"用户注册后即赠送一定数额的积分\")]),(_vm.loginForm.register_integral_status === 1)?_c('Input',{staticClass:\"inputw mt10\",attrs:{\"placeholder\":\"请输入赠送积分\"},model:{value:(_vm.loginForm.register_give_integral),callback:function ($$v) {_vm.$set(_vm.loginForm, \"register_give_integral\", $$v)},expression:\"loginForm.register_give_integral\"}}):_vm._e(),(_vm.loginForm.register_integral_status === 1)?_c('span',{staticClass:\"span-text\"},[_vm._v(\"\\n                    积分\\n                  \")]):_vm._e()],1):_vm._e()],1),(_vm.loginForm.newcomer_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[(_vm.loginForm.newcomer_status === 1)?_c('FormItem',{attrs:{\"label\":\"赠送余额：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.loginForm.register_money_status),callback:function ($$v) {_vm.$set(_vm.loginForm, \"register_money_status\", $$v)},expression:\"loginForm.register_money_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    用户注册后即赠送一定数额的储值余额\\n                  \")]),(_vm.loginForm.register_money_status === 1)?_c('Input',{staticClass:\"inputw mt10\",attrs:{\"placeholder\":\"请输入赠送余额\"},model:{value:(_vm.loginForm.register_give_money),callback:function ($$v) {_vm.$set(_vm.loginForm, \"register_give_money\", $$v)},expression:\"loginForm.register_give_money\"}}):_vm._e(),(_vm.loginForm.register_money_status === 1)?_c('span',{staticClass:\"span-text\"},[_vm._v(\"\\n                    元\\n                  \")]):_vm._e()],1):_vm._e()],1):_vm._e(),(_vm.loginForm.newcomer_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},_vm._l((_vm.promotionsData),function(item,indexw){return _c('div',{key:indexw,staticClass:\"item\"},[(_vm.loginForm.newcomer_status === 1)?_c('FormItem',{attrs:{\"label\":\"赠送优惠券：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.loginForm.register_coupon_status),callback:function ($$v) {_vm.$set(_vm.loginForm, \"register_coupon_status\", $$v)},expression:\"loginForm.register_coupon_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"用户注册后即赠送优惠券\")]),(\n                        _vm.loginForm.register_coupon_status === 1 &&\n                        item.giveCoupon.length > 0\n                      )?_c('Table',{ref:\"table\",refInFor:true,staticClass:\"table mt10\",attrs:{\"border\":\"\",\"columns\":_vm.columns1,\"data\":item.giveCoupon,\"width\":\"700\"},scopedSlots:_vm._u([{key:\"coupon_price\",fn:function(ref){\n                      var row = ref.row;\nreturn [(row.coupon_type == 1)?_c('span',[_vm._v(\"\\n                          \"+_vm._s(row.coupon_price)+\"元\\n                        \")]):_vm._e(),(row.coupon_type == 2)?_c('span',[_vm._v(\"\\n                          \"+_vm._s(parseFloat(row.coupon_price) / 10)+\"折（\"+_vm._s(row.coupon_price.toString().split('.')[0])+\"%）\\n                        \")]):_vm._e()]}},{key:\"coupon_type\",fn:function(ref){\n                      var row = ref.row;\nreturn [(row.coupon_type === 1)?_c('span',[_vm._v(\"满减券\")]):_c('span',[_vm._v(\"折扣券\")])]}},{key:\"status\",fn:function(ref){\n                      var row = ref.row;\n                      var index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.delCoupon(index, indexw)}}},[_vm._v(\"删除\")])]}}],null,true)}):_vm._e(),(_vm.loginForm.register_coupon_status === 1)?_c('div',{staticClass:\"add-coupon\",on:{\"click\":function($event){return _vm.addCoupon(indexw)}}},[_vm._v(\"\\n                      + 添加优惠券\\n                    \")]):_vm._e()],1):_vm._e()],1)}),0):_vm._e(),(_vm.loginForm.newcomer_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"首单优惠：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.loginForm.first_order_status),callback:function ($$v) {_vm.$set(_vm.loginForm, \"first_order_status\", $$v)},expression:\"loginForm.first_order_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    新用户下单时可享折扣，折扣仅对商品打折，运费无折扣\\n                  \")])],1)],1):_vm._e(),(\n                  _vm.loginForm.newcomer_status === 1 &&\n                  _vm.loginForm.first_order_status === 1\n                )?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"折扣力度：\"}},[_c('Input',{staticClass:\"inputw\",attrs:{\"placeholder\":\"请输入折扣力度\"},model:{value:(_vm.loginForm.first_order_discount),callback:function ($$v) {_vm.$set(_vm.loginForm, \"first_order_discount\", $$v)},expression:\"loginForm.first_order_discount\"}}),_c('span',{staticClass:\"span-text\"},[_vm._v(\"%\")]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    折扣力度为：0-100%，1折为10%\\n                  \")])],1)],1):_vm._e(),(\n                  _vm.loginForm.newcomer_status === 1 &&\n                  _vm.loginForm.first_order_status === 1\n                )?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"折扣上限：\"}},[_c('Input',{staticClass:\"inputw\",attrs:{\"placeholder\":\"请输入折扣上限\"},model:{value:(_vm.loginForm.first_order_discount_limit),callback:function ($$v) {_vm.$set(_vm.loginForm, \"first_order_discount_limit\", $$v)},expression:\"loginForm.first_order_discount_limit\"}}),_c('span',{staticClass:\"span-text\"},[_vm._v(\"元\")]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    首单优惠最高金额，单位：元\\n                  \")])],1)],1):_vm._e(),(_vm.loginForm.newcomer_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"新人专享价：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.loginForm.register_price_status),callback:function ($$v) {_vm.$set(_vm.loginForm, \"register_price_status\", $$v)},expression:\"loginForm.register_price_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    新用户可购买一件新人商品，购买后移动端不再显示新人专区\\n                  \")]),(_vm.loginForm.register_price_status === 1)?_c('vxe-table',{ref:\"xTree\",staticClass:\"goods mt10\",attrs:{\"border\":\"inner\",\"column-config\":{ resizable: true },\"row-id\":\"id\",\"tree-config\":{ children: 'attrValue', reserve: true },\"data\":_vm.tableData,\"header-cell-style\":{\n                      background: '#F7F7F7',\n                      height: '40px',\n                    }},on:{\"checkbox-all\":_vm.selectAll,\"checkbox-change\":_vm.selectAll}},[_c('vxe-column',{attrs:{\"type\":\"checkbox\",\"title\":\"多选\",\"width\":\"90\",\"tree-node\":\"\"}}),_c('vxe-column',{attrs:{\"field\":\"id\",\"title\":\"ID\",\"min-width\":\"80\"}}),_c('vxe-column',{attrs:{\"field\":\"info\",\"title\":\"商品信息\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\n                    var row = ref.row;\nreturn [_c('div',{staticClass:\"imgPic acea-row row-middle\"},[_c('viewer',[_c('div',{staticClass:\"pictrue\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.image),expression:\"row.image\"}]})])]),_c('div',{staticClass:\"info\"},[_c('Tooltip',{attrs:{\"max-width\":\"200\",\"placement\":\"bottom\",\"transfer\":\"\"}},[_c('span',{staticClass:\"line2\"},[_vm._v(_vm._s(row.store_name)+_vm._s(row.suk))]),_c('p',{attrs:{\"slot\":\"content\"},slot:\"content\"},[_vm._v(_vm._s(row.store_name)+_vm._s(row.suk))])])],1)],1)]}}],null,false,1988674042)}),_c('vxe-column',{attrs:{\"field\":\"price\",\"title\":\"售价\",\"min-width\":\"80\"}}),_c('vxe-column',{attrs:{\"field\":\"stock\",\"title\":\"库存\",\"min-width\":\"80\"}}),_c('vxe-column',{attrs:{\"field\":\"date\",\"title\":\"活动价\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\n                    var row = ref.row;\nreturn [_c('Input',{attrs:{\"border\":false,\"placeholder\":\"请输入活动价\"},on:{\"on-change\":function($event){return _vm.inputChange(row)}},model:{value:(row.ativity_price),callback:function ($$v) {_vm.$set(row, \"ativity_price\", $$v)},expression:\"row.ativity_price\"}})]}}],null,false,2807784357)}),_c('vxe-column',{attrs:{\"field\":\"date\",\"title\":\"操作\",\"min-width\":\"50\",\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\n                    var row = ref.row;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.del(row)}}},[_vm._v(\"删除\")])]}}],null,false,1383043590)})],1):_vm._e(),(_vm.loginForm.register_price_status === 1)?_c('div',{staticClass:\"add-goods\"},[_c('Button',{on:{\"click\":_vm.addGoods}},[_vm._v(\"添加商品\")]),_c('Button',{staticClass:\"goods-btn\",on:{\"click\":_vm.activityShowFn}},[_vm._v(\"\\n                      设置活动价\\n                    \")]),_c('Button',{on:{\"click\":_vm.delAll}},[_vm._v(\"批量删除\")])],1):_vm._e()],1)],1):_vm._e(),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[(_vm.loginForm.newcomer_status === 1)?_c('FormItem',{attrs:{\"label\":\"规则详情：\"}},[_c('WangEditor',{staticClass:\"goods\",attrs:{\"content\":_vm.loginForm.newcomer_agreement},on:{\"editorContent\":_vm.getEditorContent}})],1):_vm._e()],1),_c('Col',[_c('FormItem',[_c('div',{staticClass:\"subBtn\",staticStyle:{\"margin-top\":\"0px\"},on:{\"click\":function($event){return _vm.handleSubmit('register')}}},[_vm._v(\"\\n                    保存\\n                  \")])])],1)],1)],1)],1),_c('TabPane',{attrs:{\"label\":\"会员等级\",\"name\":\"level\"}},[_c('Form',{attrs:{\"model\":_vm.vipForm,\"label-width\":120}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"basics\"},[_vm._v(\"基础设置\")])]),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"会员等级启用：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.vipForm.member_func_status),callback:function ($$v) {_vm.$set(_vm.vipForm, \"member_func_status\", $$v)},expression:\"vipForm.member_func_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    开启会员等级后，可以获得经验值\\n                  \")])],1)],1),(_vm.vipForm.member_func_status === 1)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"下单获得经验：\"}},[_c('Input',{staticClass:\"inputw\",attrs:{\"placeholder\":\"请输入获得经验值\"},model:{value:(_vm.vipForm.order_give_exp),callback:function ($$v) {_vm.$set(_vm.vipForm, \"order_give_exp\", $$v)},expression:\"vipForm.order_give_exp\"}}),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    用户实际支付1元，可以获得多少经验值\\n                  \")])],1)],1):_vm._e(),(_vm.vipForm.member_func_status === 1)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"签到获得经验：\"}},[_c('Input',{staticClass:\"inputw\",attrs:{\"placeholder\":\"请输入签到获得经验值\"},model:{value:(_vm.vipForm.sign_give_exp),callback:function ($$v) {_vm.$set(_vm.vipForm, \"sign_give_exp\", $$v)},expression:\"vipForm.sign_give_exp\"}}),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"用户签到一次，赠送多少经验值\")])],1)],1):_vm._e(),(_vm.vipForm.member_func_status === 1)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"邀请新用户获得经验：\"}},[_c('Input',{staticClass:\"inputw\",attrs:{\"placeholder\":\"请输入获取新用户获得经验值\"},model:{value:(_vm.vipForm.invite_user_exp),callback:function ($$v) {_vm.$set(_vm.vipForm, \"invite_user_exp\", $$v)},expression:\"vipForm.invite_user_exp\"}}),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    邀请一个新用户注册，赠送多少经验值\\n                  \")])],1)],1):_vm._e(),_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"basics\"},[_vm._v(\"激活有礼\")])]),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"会员卡激活：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.vipForm.level_activate_status),callback:function ($$v) {_vm.$set(_vm.vipForm, \"level_activate_status\", $$v)},expression:\"vipForm.level_activate_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    开启后用户等级功能不能直接使用，需要用户填写信息，激活后才能使用用户等级\\n                  \")])],1)],1),(_vm.vipForm.level_activate_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"会员卡信息：\"}},[(_vm.listVip.length > 0)?_c('Table',{ref:\"table\",staticClass:\"mt10 mb10 goods\",attrs:{\"columns\":_vm.columns3,\"data\":_vm.listVip,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"required\",fn:function(ref){\n                    var row = ref.row;\n                    var index = ref.index;\nreturn [_c('Checkbox',{on:{\"on-change\":_vm.tapCheckbox},model:{value:(_vm.listVip[index].required),callback:function ($$v) {_vm.$set(_vm.listVip[index], \"required\", $$v)},expression:\"listVip[index].required\"}})]}},{key:\"action\",fn:function(ref){\n                    var row = ref.row;\n                    var index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.delVip(row, index)}}},[_vm._v(\"删除\")])]}}],null,false,1344792718)}):_vm._e(),_c('Button',{on:{\"click\":_vm.informationTap}},[_vm._v(\"\\n                    选择信息\\n                  \")])],1)],1):_vm._e(),(_vm.vipForm.level_activate_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"赠送积分：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.vipForm.level_integral_status),callback:function ($$v) {_vm.$set(_vm.vipForm, \"level_integral_status\", $$v)},expression:\"vipForm.level_integral_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    用户激活会员卡后即赠送一定数额的积分\\n                  \")]),(_vm.vipForm.level_integral_status === 1)?_c('Input',{staticClass:\"inputw mt10\",attrs:{\"placeholder\":\"请输入赠送的积分\"},model:{value:(_vm.vipForm.level_give_integral),callback:function ($$v) {_vm.$set(_vm.vipForm, \"level_give_integral\", $$v)},expression:\"vipForm.level_give_integral\"}}):_vm._e(),(_vm.vipForm.level_integral_status === 1)?_c('span',{staticClass:\"span-text\"},[_vm._v(\"\\n                    积分\\n                  \")]):_vm._e()],1)],1):_vm._e(),(_vm.vipForm.level_activate_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"赠送余额：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.vipForm.level_money_status),callback:function ($$v) {_vm.$set(_vm.vipForm, \"level_money_status\", $$v)},expression:\"vipForm.level_money_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    用户激活会员卡后即赠送一定数额的储值余额\\n                  \")]),(_vm.vipForm.level_money_status === 1)?_c('Input',{staticClass:\"inputw mt10\",attrs:{\"placeholder\":\"请输入赠送的余额\"},model:{value:(_vm.vipForm.level_give_money),callback:function ($$v) {_vm.$set(_vm.vipForm, \"level_give_money\", $$v)},expression:\"vipForm.level_give_money\"}}):_vm._e(),(_vm.vipForm.level_money_status === 1)?_c('span',{staticClass:\"span-text\"},[_vm._v(\"\\n                    元\\n                  \")]):_vm._e()],1)],1):_vm._e(),(_vm.vipForm.level_activate_status === 1)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"赠送优惠券：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.vipForm.level_coupon_status),callback:function ($$v) {_vm.$set(_vm.vipForm, \"level_coupon_status\", $$v)},expression:\"vipForm.level_coupon_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"upload-text\"},[_vm._v(\"\\n                    用户激活会员卡后即赠送优惠券\\n                  \")]),_vm._l((_vm.promotionsData),function(item,indexw){return _c('div',{key:indexw,staticClass:\"item\"},[(_vm.vipForm.level_coupon_status === 1)?_c('div',{staticClass:\"add-coupon\",on:{\"click\":function($event){return _vm.addCoupon(indexw)}}},[_vm._v(\"\\n                      + 添加优惠券\\n                    \")]):_vm._e(),(\n                        _vm.vipCopon.length > 0 && _vm.vipForm.level_coupon_status === 1\n                      )?_c('Table',{ref:\"table\",refInFor:true,staticClass:\"table\",attrs:{\"border\":\"\",\"columns\":_vm.columns1,\"data\":_vm.vipCopon,\"width\":\"700\"},scopedSlots:_vm._u([{key:\"coupon_price\",fn:function(ref){\n                      var row = ref.row;\nreturn [(row.coupon_type == 1)?_c('span',[_vm._v(\"\\n                          \"+_vm._s(row.coupon_price)+\"元\\n                        \")]):_vm._e(),(row.coupon_type == 2)?_c('span',[_vm._v(\"\\n                          \"+_vm._s(parseFloat(row.coupon_price) / 10)+\"折（\"+_vm._s(row.coupon_price.toString().split('.')[0])+\"%）\\n                        \")]):_vm._e()]}},{key:\"coupon_type\",fn:function(ref){\n                      var row = ref.row;\nreturn [(row.coupon_type === 1)?_c('span',[_vm._v(\"满减券\")]):_c('span',[_vm._v(\"折扣券\")])]}},{key:\"status\",fn:function(ref){\n                      var row = ref.row;\n                      var index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.delCoupon(index, indexw)}}},[_vm._v(\"删除\")])]}}],null,true)}):_vm._e()],1)})],2)],1):_vm._e(),_c('Col',[_c('FormItem',[_c('div',{staticClass:\"subBtn mt10\",on:{\"click\":function($event){return _vm.handleSubmit('level')}}},[_vm._v(\"\\n                    保存\\n                  \")])])],1)],1)],1)],1),_c('TabPane',{attrs:{\"label\":\"付费会员\",\"name\":\"svip\"}},[_c('Form',{attrs:{\"model\":_vm.basicsForm,\"label-width\":120}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"付费会员启用：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.member_card_status),callback:function ($$v) {_vm.member_card_status=$$v},expression:\"member_card_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1),(_vm.member_card_status == 1)?_c('FormItem',{attrs:{\"label\":\"付费会员价展示：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.svip_price_status),callback:function ($$v) {_vm.svip_price_status=$$v},expression:\"svip_price_status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1):_vm._e()],1),_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('FormItem',[_c('div',{staticClass:\"subBtn\",staticStyle:{\"margin-top\":\"0px\"},on:{\"click\":function($event){return _vm.handleSubmit('svip')}}},[_vm._v(\"\\n                    保存\\n                  \")])])],1)],1)],1)],1)],1)],1),_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"上传用户图片\",\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1),_c('Modal',{attrs:{\"title\":\"新增信息\",\"class-name\":\"vertical-center-modal\",\"scrollable\":\"\"},on:{\"on-cancel\":_vm.cancelSubmit},model:{value:(_vm.addModel),callback:function ($$v) {_vm.addModel=$$v},expression:\"addModel\"}},[_c('Form',{ref:\"formValidate\",attrs:{\"model\":_vm.formItem,\"rules\":_vm.ruleValidate,\"label-width\":90}},[_c('Row',[_c('Col',[_c('FormItem',{attrs:{\"label\":\"信息名称：\",\"prop\":\"info\"}},[_c('Input',{staticStyle:{\"width\":\"300px\"},attrs:{\"placeholder\":\"请输入信息名称\"},model:{value:(_vm.formItem.info),callback:function ($$v) {_vm.$set(_vm.formItem, \"info\", $$v)},expression:\"formItem.info\"}})],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"信息格式 ：\",\"prop\":\"format\"}},[_c('Select',{staticStyle:{\"width\":\"300px\"},model:{value:(_vm.formItem.format),callback:function ($$v) {_vm.$set(_vm.formItem, \"format\", $$v)},expression:\"formItem.format\"}},_vm._l((_vm.formatList),function(item){return _c('Option',{key:item.value,attrs:{\"value\":item.value}},[_vm._v(\"\\n                  \"+_vm._s(item.label)+\"\\n                \")])}),1)],1)],1),_c('Col',[(_vm.formItem.format === 'radio')?_c('FormItem',{attrs:{\"label\":\"单选项 ：\",\"prop\":\"singlearr\"}},[_c('div',{staticClass:\"arrbox\"},[_vm._l((_vm.formItem.singlearr),function(item,index){return _c('Tag',{key:index,attrs:{\"name\":item,\"closable\":true},on:{\"on-close\":_vm.handleClose}},[_vm._v(\"\\n                  \"+_vm._s(item)+\"\\n                \")])}),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.formItem.single),expression:\"formItem.single\"}],staticClass:\"arrbox_ip percentage9\",attrs:{\"placeholder\":\"请输入选项，回车确认\"},domProps:{\"value\":(_vm.formItem.single)},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.addlabel($event)},\"input\":function($event){if($event.target.composing){ return; }_vm.$set(_vm.formItem, \"single\", $event.target.value)}}})],2)]):_vm._e()],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"提示文案：\",\"prop\":\"tip\"}},[_c('Input',{staticStyle:{\"width\":\"300px\"},attrs:{\"placeholder\":\"请输入提示文案\"},model:{value:(_vm.formItem.tip),callback:function ($$v) {_vm.$set(_vm.formItem, \"tip\", $$v)},expression:\"formItem.tip\"}})],1)],1)],1)],1),_c('div',{staticClass:\"acea-row row-right\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{on:{\"click\":_vm.cancelSubmit}},[_vm._v(\"取消\")]),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.addSubmit}},[_vm._v(\"提交\")])],1)],1),_c('coupon-list',{ref:\"couponTemplates\",attrs:{\"discount\":true},on:{\"getCouponList\":_vm.getCouponList}}),_c('Modal',{staticClass:\"paymentFooter\",attrs:{\"title\":\"商品列表\",\"footerHide\":\"\",\"scrollable\":\"\",\"width\":\"900\"},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[(_vm.modals)?_c('goods-list',{ref:\"goodslist\",attrs:{\"ischeckbox\":true,\"isdiy\":true},on:{\"getProductId\":_vm.getProductId}}):_vm._e()],1),_c('information',{ref:\"information\",attrs:{\"listOne\":_vm.listOne},on:{\"getInfoList\":_vm.getInfoList}}),_c('Modal',{staticClass:\"paymentFooter\",attrs:{\"title\":\"设置\",\"width\":\"600\",\"closable\":false,\"mask-closable\":false,\"footer-hide\":\"\"},model:{value:(_vm.activityShow),callback:function ($$v) {_vm.activityShow=$$v},expression:\"activityShow\"}},[_c('Form',{ref:\"activityShow\",attrs:{\"model\":_vm.formActive,\"rules\":_vm.ruleActive,\"label-width\":100}},[_c('FormItem',{attrs:{\"label\":\"设置活动价：\",\"prop\":\"activeInput\"}},[_c('InputNumber',{staticClass:\"inputw\",attrs:{\"placeholder\":\"请输入活动价格\",\"min\":0},model:{value:(_vm.formActive.activeInput),callback:function ($$v) {_vm.$set(_vm.formActive, \"activeInput\", $$v)},expression:\"formActive.activeInput\"}})],1),_c('div',{staticClass:\"acea-row row-right\"},[_c('Button',{on:{\"click\":function($event){return _vm.cancel('activityShow')}}},[_vm._v(\"取消\")]),_c('Button',{staticClass:\"ml15 mr5\",attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.ok('activityShow')}}},[_vm._v(\"提交\")])],1)],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}