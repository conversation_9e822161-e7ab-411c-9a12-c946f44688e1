{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue", "mtime": 1750985338988}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\n// 注意：实际项目中需要引入echarts\r\n// import * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'ERPReport',\r\n  data() {\r\n    return {\r\n      // 日期选择\r\n      dateRange: [],\r\n\r\n      // 概览数据\r\n      overview: {\r\n        inventory_value: 1250000,\r\n        inventory_change: 12.5,\r\n        transfer_count: 156,\r\n        transfer_change: 8.3,\r\n        warning_count: 23,\r\n        efficiency: 92\r\n      },\r\n\r\n      // 图表控制\r\n      inventoryPeriod: 'month',\r\n      transferType: 'status',\r\n\r\n      // 报表数据\r\n      storeReport: [],\r\n      transferReport: [],\r\n      storeLoading: false,\r\n      transferLoading: false,\r\n\r\n      // 商品分析\r\n      hotProducts: [],\r\n      warningStats: {\r\n        severe: 5,\r\n        low: 18,\r\n        normal: 234,\r\n        abundant: 89\r\n      }\r\n    };\r\n  },\r\n\r\n  created() {\r\n    this.initData();\r\n  },\r\n\r\n  methods: {\r\n    // 初始化数据\r\n    async initData() {\r\n      // 设置默认日期范围（最近3个月）\r\n      const now = new Date();\r\n      const start = new Date(now.getFullYear(), now.getMonth() - 2, 1);\r\n      this.dateRange = [start, now];\r\n\r\n      await Promise.all([\r\n        this.loadStoreReport(),\r\n        this.loadTransferReport(),\r\n        this.loadProductAnalysis()\r\n      ]);\r\n\r\n      this.$nextTick(() => {\r\n        this.initCharts();\r\n      });\r\n    },\r\n\r\n    // 加载门店报表\r\n    async loadStoreReport() {\r\n      this.storeLoading = true;\r\n      // 模拟数据\r\n      setTimeout(() => {\r\n        this.storeReport = [\r\n          {\r\n            store_id: 1,\r\n            store_name: '上海总店',\r\n            total_products: 156,\r\n            total_stock: 2340,\r\n            total_value: 450000,\r\n            warning_count: 8,\r\n            out_stock_count: 2\r\n          },\r\n          {\r\n            store_id: 2,\r\n            store_name: '北京分店',\r\n            total_products: 134,\r\n            total_stock: 1890,\r\n            total_value: 380000,\r\n            warning_count: 12,\r\n            out_stock_count: 1\r\n          },\r\n          {\r\n            store_id: 3,\r\n            store_name: '深圳分店',\r\n            total_products: 142,\r\n            total_stock: 2100,\r\n            total_value: 420000,\r\n            warning_count: 3,\r\n            out_stock_count: 2\r\n          }\r\n        ];\r\n        this.storeLoading = false;\r\n      }, 1000);\r\n    },\r\n\r\n    // 加载调拨报表\r\n    async loadTransferReport() {\r\n      this.transferLoading = true;\r\n      // 模拟数据\r\n      setTimeout(() => {\r\n        this.transferReport = [\r\n          {\r\n            date: '2024-12-16',\r\n            total_orders: 12,\r\n            approved_orders: 10,\r\n            completed_orders: 8,\r\n            avg_process_time: 2.5,\r\n            total_quantity: 245\r\n          },\r\n          {\r\n            date: '2024-12-15',\r\n            total_orders: 8,\r\n            approved_orders: 8,\r\n            completed_orders: 7,\r\n            avg_process_time: 1.8,\r\n            total_quantity: 156\r\n          },\r\n          {\r\n            date: '2024-12-14',\r\n            total_orders: 15,\r\n            approved_orders: 13,\r\n            completed_orders: 12,\r\n            avg_process_time: 3.2,\r\n            total_quantity: 389\r\n          }\r\n        ];\r\n        this.transferLoading = false;\r\n      }, 1000);\r\n    },\r\n\r\n    // 加载商品分析\r\n    async loadProductAnalysis() {\r\n      // 模拟数据\r\n      this.hotProducts = [\r\n        { id: 1, name: '苹果iPhone 14', transfer_count: 45, trend: 'up' },\r\n        { id: 2, name: '华为Mate50', transfer_count: 38, trend: 'up' },\r\n        { id: 3, name: '小米13', transfer_count: 32, trend: 'down' },\r\n        { id: 4, name: 'OPPO Find X5', transfer_count: 28, trend: 'up' },\r\n        { id: 5, name: 'vivo X90', transfer_count: 25, trend: 'down' }\r\n      ];\r\n    },\r\n\r\n    // 初始化图表\r\n    initCharts() {\r\n      this.loadInventoryTrend();\r\n      this.loadTransferStats();\r\n    },\r\n\r\n    // 加载库存趋势图\r\n    loadInventoryTrend() {\r\n      // 模拟图表初始化\r\n      console.log('加载库存趋势图:', this.inventoryPeriod);\r\n      // 在实际项目中，这里会使用echarts初始化图表\r\n    },\r\n\r\n    // 加载调拨统计图\r\n    loadTransferStats() {\r\n      // 模拟图表初始化\r\n      console.log('加载调拨统计图:', this.transferType);\r\n      // 在实际项目中，这里会使用echarts初始化图表\r\n    },\r\n\r\n    // 工具方法\r\n    formatMoney(amount) {\r\n      return (amount / 10000).toFixed(1) + '万';\r\n    },\r\n\r\n    getStockHealth(row) {\r\n      const total = row.total_products;\r\n      const warning = row.warning_count;\r\n      const outStock = row.out_stock_count;\r\n      return Math.round(((total - warning - outStock) / total) * 100);\r\n    },\r\n\r\n    getProgressColor(row) {\r\n      const health = this.getStockHealth(row);\r\n      if (health >= 80) return '#67c23a';\r\n      if (health >= 60) return '#e6a23c';\r\n      return '#f56c6c';\r\n    },\r\n\r\n    getCompletionRate(row) {\r\n      return Math.round((row.completed_orders / row.total_orders) * 100);\r\n    },\r\n\r\n    getEfficiencyClass(row) {\r\n      const rate = this.getCompletionRate(row);\r\n      if (rate >= 80) return 'success-text';\r\n      if (rate >= 60) return 'warning-text';\r\n      return 'danger-text';\r\n    },\r\n\r\n    getEfficiencyTagType(row) {\r\n      const rate = this.getCompletionRate(row);\r\n      if (rate >= 80) return 'success';\r\n      if (rate >= 60) return 'warning';\r\n      return 'danger';\r\n    },\r\n\r\n    getEfficiencyText(row) {\r\n      const rate = this.getCompletionRate(row);\r\n      if (rate >= 80) return '优秀';\r\n      if (rate >= 60) return '良好';\r\n      return '待改进';\r\n    },\r\n\r\n    // 事件处理\r\n    handleDateChange() {\r\n      this.refreshData();\r\n    },\r\n\r\n    refreshData() {\r\n      this.loadStoreReport();\r\n      this.loadTransferReport();\r\n      this.loadProductAnalysis();\r\n      this.initCharts();\r\n    },\r\n\r\n    refreshProductAnalysis() {\r\n      this.loadProductAnalysis();\r\n    },\r\n\r\n    // 查看详情\r\n    viewStoreDetail(row) {\r\n      this.$message.info(`查看${row.store_name}详情功能开发中...`);\r\n    },\r\n\r\n    // 导出功能\r\n    exportReport() {\r\n      this.$message.info('导出报表功能开发中...');\r\n    },\r\n\r\n    exportStoreReport() {\r\n      this.$message.info('导出门店报表功能开发中...');\r\n    },\r\n\r\n    exportTransferReport() {\r\n      this.$message.info('导出调拨报表功能开发中...');\r\n    }\r\n  }\r\n};\r\n", null]}