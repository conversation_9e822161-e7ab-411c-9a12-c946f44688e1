{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\clientMomentInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\clientMomentInfo.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport cardsData from \"@/components/cards/cards\";\nimport { workMomentInfo,workGroupTemplateSendMsg,workMomentList } from \"@/api/work\";\n import { formatDate } from '@/utils/validate';\n import Setting from \"@/setting\";\nexport default {\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      cardLists: [],\n      optionData: {},\n      style: { height: \"400px\" },\n      spinShow: false,\n      tableColumn: [\n        {\n          title: \"员工\",\n          key: \"name\",\n          minWidth: 100,\n        },\n        {\n          title: \"发表状态\",\n          slot: \"status\",\n          minWidth: 100,\n        },\n        {\n          title: \"发送时间\",\n          slot: \"create_time\",\n          minWidth: 100,\n        },\n        {\n          title: \"已送达客户\",\n          slot: \"external_user_list\",\n          minWidth: 150,\n        },\n        // {\n        //   title: \"操作\",\n        //   slot: \"action\",\n        //   minWidth: 100,\n        // },\n      ],\n      tabIndex:0,\n      tableData: [],\n      userLoading: false,\n      timeVal: [],\n      tableForm: {\n        page: 1,\n        limit: 15,\n        status:\"\",\n        moment_id:\"\",\n        userid:\"\",\n      },\n      \n    };\n  },\n  filters: {\n      formatDate (time) {\n          if (time !== 0) {\n              let date = new Date(time * 1000);\n              return formatDate(date, 'yyyy-MM-dd hh:mm');\n          }\n      }\n  },\n  components: { cardsData },\n  mounted() {\n    this.getData();\n    // this.getMemberList();\n  },\n  methods: {\n    getList() {\n      this.userLoading = true;\n      workMomentList(this.tableForm)\n        .then((res) => {\n          this.tableData = res.data;\n          this.userLoading = false;\n        })\n        .catch((err) => {\n          this.userLoading = false;\n          this.$Message.error(err.msg);\n        });\n    },\n    getData() {\n      workMomentInfo(this.$route.params.id).then((res) => {\n        this.tableForm.moment_id = res.data.moment_id;\n        if(!!res.data.moment_id){\n          this.getList();\n        }\n        this.cardLists = [\n          {\n            col: 6,\n            count: res.data.info.externalUserCount,\n            name: \"已送达客户\",\n            type:1,\n            className: \"iconjinrixinzeng\",\n          },\n          {\n            col: 6,\n            count: res.data.info.userCount,\n            name: \"全部成员\",\n            type:1,\n            className: \"iconjinrituiqun\",\n          },\n          {\n            col: 6,\n            count: res.data.info.unSendUserCount,\n            name: \"未发送成员\",\n            type:1,\n            className: \"icondangqianqunchengyuan\",\n          },\n          {\n            col: 6,\n            count: res.data.info.sendUserCount,\n            name: \"已发送成员\",\n            type:1,\n            className: \"iconleijituiqun\",\n          },\n        ];\n        \n      });\n    },\n    \n    sendMsg(row,index){\n      workGroupTemplateSendMsg({\n\t\t\t\t\tuserid:row.msg_id,\n\t\t\t\t\ttime:row.create_time,\n\t\t\t\t\tid:\"\"\n\t\t\t\t}).then(res=>{\n\t\t\t\t\tthis.$Message.success(res.msg)\n\t\t\t\t}).catch(err=>{\n\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t})\n    },\n    search(){\n      this.tableForm.page = 1;\n      this.getList();\n      \n    },\n    pageChange(index) {\n      this.tableForm.page = index;\n      this.getList();\n    },\n  },\n};\n", null]}