{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue", "mtime": 1717062347000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport vuedraggable from 'vuedraggable';\nimport linkaddress from '@/components/linkaddress';\nexport default {\n  name: 'c_tab_list',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    },\n    index: {\n      type: null\n    }\n  },\n  components: {\n    linkaddress: linkaddress,\n    draggable: vuedraggable\n  },\n  data: function data() {\n    return {\n      defaults: {},\n      configData: {},\n      itemObj: {},\n      activeIndex: 0\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.defaults = _this.configObj;\n      _this.configData = _this.configObj[_this.configNme];\n    });\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n      },\n      deep: true\n    }\n  },\n  methods: {\n    linkUrl: function linkUrl(e) {\n      if (this.configData.list[this.activeIndex].dataType.tabVal) {\n        var obj = e.split('?')[1];\n        var obj2 = obj.split('&');\n        this.configData.list[this.activeIndex].classPage.name = obj2[1].split('=')[1];\n        this.configData.list[this.activeIndex].classPage.id = obj2[0].split('=')[1];\n      } else {\n        var _obj = e.split('?')[1];\n\n        var _obj2 = _obj.split('&');\n\n        this.configData.list[this.activeIndex].microPage.name = _obj2[1].split('=')[1];\n        this.configData.list[this.activeIndex].microPage.id = _obj2[0].split('=')[1];\n      }\n    },\n    getLink: function getLink(index) {\n      this.activeIndex = index;\n      var obj = {};\n\n      if (this.configData.list[this.activeIndex].dataType.tabVal) {\n        obj = {\n          id: 8,\n          pid: 2,\n          type: \"product_category\"\n        };\n      } else {\n        obj = {\n          id: 7,\n          pid: 1,\n          type: \"special\"\n        };\n      }\n\n      this.$refs.linkaddres.handleCheckChange('', obj);\n      this.$refs.linkaddres.modals = true;\n    },\n    addHotTxt: function addHotTxt() {\n      if (this.configData.list.length == 0) {\n        var storage = window.localStorage;\n        this.itemObj = JSON.parse(storage.getItem('itemObj'));\n        this.itemObj.dataType.tabVal = 0;\n        this.itemObj.microPage.name = '';\n        this.itemObj.classPage.name = '';\n        this.configData.list.push(this.itemObj);\n      } else {\n        var obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]));\n        obj.dataType.tabVal = 0;\n        obj.microPage.name = '';\n        obj.classPage.name = '';\n        this.configData.list.push(obj);\n      }\n    },\n    // 删除数组\n    bindDelete: function bindDelete(index) {\n      if (this.configData.list.length == 1) {\n        var itemObj = this.configData.list[0];\n        this.itemObj = itemObj;\n        var storage = window.localStorage;\n        storage.setItem('itemObj', JSON.stringify(itemObj));\n      }\n\n      this.configData.list.splice(index, 1);\n    }\n  }\n};", null]}