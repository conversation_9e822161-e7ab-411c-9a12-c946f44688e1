{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_fillet.vue?vue&type=style&index=0&id=54ca097e&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_fillet.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.fillets{\n\tpadding: 0 15px;\n}\n.txt_tab{\n\tmargin-top 20px\n}\n.c_row-item{\n\tmargin-bottom 20px\n\t&.on{\n\t\talign-items: flex-start\n\t\t.c_label{\n\t\t\tmargin-top: 8px;\n\t\t}\n\t}\n\t.c_label{\n\t\tfont-size: 12px\n\t\tspan{\n\t\t\tmargin-left: 34px;\n\t\t}\n\t}\n\t.right{\n\t\twidth: 204px;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between\n\t\tflex-wrap: wrap\n\t\talign-items center\n\t\t\n\t\t.input{\n\t\t\twidth: 95px;\n\t\t\t&.on{\n\t\t\t\tmargin-bottom: 14px\n\t\t\t}\n\t\t}\n\t}\n\t/deep/.ivu-input-prefix{\n\t\ttop:7px;\n\t}\n}\n.row-item{\n\tdisplay flex\n\tjustify-content space-between\n\talign-items center\n}\n.iconfont{\n\tfont-size 10px\n\tcolor: #666666;\n}\n        \n", null]}