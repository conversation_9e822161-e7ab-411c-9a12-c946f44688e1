{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\recharge\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\recharge\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport cardsData from \"@/components/cards/cards\";\nimport searchFrom from \"@/components/publicSearchFrom\";\nimport { mapState } from \"vuex\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport {\n  rechargelist<PERSON>pi,\n  userRecharge<PERSON><PERSON>,\n  refundEdit<PERSON><PERSON>,\n  exportUserR<PERSON>arge<PERSON><PERSON>,\n} from \"@/api/finance\";\nimport editFrom from \"@/components/from/from\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: \"recharge\",\n  components: { cardsData, searchFrom, editFrom },\n  data() {\n    return {\n      FromData: null,\n      formValidate: {\n        data: \"\",\n        paid: \"\",\n        nickname: \"\",\n        excel: 0,\n        page: 1,\n        limit: 20,\n      },\n      formValidate2: {\n        data: \"\",\n        paid: \"\",\n        nickname: \"\",\n      },\n      total: 0,\n      cardLists: [],\n      loading: false,\n      columns: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          sortable: true,\n          width: 80,\n        },\n        {\n          title: \"头像\",\n          key: \"avatar\",\n          minWidth: 80,\n          render: (h, params) => {\n            return h(\"viewer\", [\n              h(\n                \"div\",\n                {\n                  style: {\n                    width: \"36px\",\n                    height: \"36px\",\n                    borderRadius: \"4px\",\n                    cursor: \"pointer\",\n                  },\n                },\n                [\n                  h(\"img\", {\n                    attrs: {\n                      src: params.row.avatar\n                        ? params.row.avatar\n                        : require(\"../../../../assets/images/moren.jpg\"),\n                    },\n                    style: {\n                      width: \"100%\",\n                      height: \"100%\",\n                    },\n                  }),\n                ]\n              ),\n            ]);\n          },\n        },\n        {\n          title: \"用户昵称\",\n          slot: \"nickname\",\n          minWidth: 150,\n        },\n        {\n          title: \"订单号\",\n          key: \"order_id\",\n          minWidth: 160,\n        },\n        {\n          title: \"支付金额\",\n          key: \"price\",\n          // sortable: true,\n          minWidth: 110,\n        },\n        {\n          title: \"是否支付\",\n          slot: \"paid_type\",\n          minWidth: 110,\n        },\n        {\n          title: \"充值类型\",\n          key: \"_recharge_type\",\n          minWidth: 100,\n        },\n        {\n          title: \"支付时间\",\n          key: \"_pay_time\",\n          minWidth: 120,\n        },\n        {\n          title: \"操作\",\n          slot: \"right\",\n          // fixed: \"right\",\n          minWidth: 100,\n        },\n      ],\n      tabList: [],\n      options: timeOptions,\n      timeVal: [],\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  mounted() {\n    this.getList();\n    this.getUserRecharge();\n  },\n  methods: {\n    // 删除\n    del(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `finance/recharge/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.tabList.splice(num, 1);\n          if (!this.tabList.length) {\n            this.formValidate.page =\n                this.formValidate.page == 1 ? 1 : this.formValidate.page - 1;\n          }\n\t\t\t\t\tthis.getUserRecharge();\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 退款\n    refund(row) {\n      refundEditApi(row.id)\n        .then(async (res) => {\n          if (res.data.status === false) {\n            return this.$authLapse(res.data);\n          }\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 编辑提交成功\n    submitFail() {\n      this.getList();\n      this.getUserRecharge();\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.formValidate.page = 1;\n      this.getList();\n      this.getUserRecharge();\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.formValidate.page = 1;\n      this.getList();\n      this.getUserRecharge();\n    },\n    // 选择\n    selChange() {\n      this.formValidate.page = 1;\n      this.getList();\n      this.getUserRecharge();\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      rechargelistApi(this.formValidate)\n        .then(async (res) => {\n          let data = res.data;\n          this.tabList = data.list;\n          this.total = data.count;\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 搜索\n    orderSearch() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 小方块\n    getUserRecharge() {\n      userRechargeApi({\n        data: this.formValidate.data,\n        paid: this.formValidate.paid,\n        nickname: this.formValidate.nickname,\n      })\n        .then(async (res) => {\n          let data = res.data;\n          this.cardLists = data;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n      // userRechargeApi(this.formValidate2).then(async res => {\n      //     let data = res.data\n      //     this.cardLists = data;\n      // }).catch(res => {\n      //     this.$Message.error(res.msg);\n      // })\n    },\n    // 导出\n    // exports () {\n    //     let formValidate = this.formValidate;\n    //     let data = {\n    //         data: formValidate.data,\n    //         paid: formValidate.paid,\n    //         nickname: formValidate.nickname\n    //     };\n    //     exportUserRechargeApi(data).then(res => {\n    //         location.href = res.data[0];\n    //     }).catch(res => {\n    //         this.$Message.error(res.msg)\n    //     })\n    // },\n    // 数据导出；\n    async exports() {\n      let [th, filekey, data, fileName] = [[], [], [], \"\"];\n      //   let fileName = \"\";\n      let excelData = JSON.parse(JSON.stringify(this.formValidate));\n      excelData.page = 1;\n      for (let i = 0; i < excelData.page + 1; i++) {\n        let lebData = await this.getExcelData(excelData);\n        if (!fileName) fileName = lebData.filename;\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) th = lebData.header;\n        if (lebData.export.length) {\n          data = data.concat(lebData.export);\n          excelData.page++;\n        } else {\n          exportExcel(th, filekey, fileName, data);\n          return;\n        }\n      }\n    },\n    getExcelData(excelData) {\n      return new Promise((resolve, reject) => {\n        exportUserRechargeApi(excelData).then((res) => {\n          return resolve(res.data);\n        });\n      });\n    },\n  },\n};\n", null]}