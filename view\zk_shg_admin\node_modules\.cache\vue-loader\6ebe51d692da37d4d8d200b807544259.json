{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {\n  bargainList<PERSON>pi,\n  bargainSetStatus<PERSON><PERSON>,\n  stroe<PERSON><PERSON>gai<PERSON><PERSON><PERSON>,\n} from \"@/api/marketing\";\nimport { formatDate } from \"@/utils/validate\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"storeBargain\",\n  filters: {\n    formatDate(time) {\n      if (time !== 0) {\n        let date = new Date(time * 1000);\n        return formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    },\n    filterStatus(val) {\n      let nowTime = Date.parse(new Date()) / 1000;\n      if (\n        val.start_time < nowTime &&\n        val.stop_time > nowTime &&\n        val.status == 1\n      ) {\n        return \"活动进行中\";\n      } else if (val.stop_time < nowTime && val.status == 1) {\n        return \"活动已结束\";\n      } else {\n        return \"活动未开始\";\n      }\n    },\n  },\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      columns1: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          width: 80,\n        },\n        {\n          title: \"砍价图片\",\n          slot: \"image\",\n          minWidth: 90,\n        },\n        {\n          title: \"砍价名称\",\n          slot: \"title\",\n          minWidth: 150,\n        },\n        {\n          title: \"砍价价格\",\n          key: \"price\",\n          minWidth: 100,\n        },\n        {\n          title: \"最低价\",\n          key: \"min_price\",\n          minWidth: 100,\n        },\n        {\n          title: \"参与人数\",\n          key: \"count_people_all\",\n          minWidth: 100,\n        },\n        {\n          title: \"帮忙砍价人数\",\n          key: \"count_people_help\",\n          minWidth: 100,\n        },\n        {\n          title: \"砍价成功人数\",\n          key: \"count_people_success\",\n          minWidth: 100,\n        },\n        {\n          title: \"限量\",\n          key: \"quota_show\",\n          minWidth: 80,\n        },\n        {\n          title: \"限量剩余\",\n          key: \"quota\",\n          minWidth: 80,\n        },\n        {\n          title: \"活动状态\",\n          slot: \"start_name\",\n          minWidth: 100,\n        },\n        {\n          title: \"结束时间\",\n          slot: \"stop_time\",\n          minWidth: 150,\n        },\n        {\n          title: \"上架状态\",\n          slot: \"status\",\n          minWidth: 130,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n          width: 180,\n        },\n      ],\n      tableList: [],\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      tableFrom: {\n        start_status: \"\",\n        status: \"\",\n        store_name: \"\",\n        export: 0,\n        page: 1,\n        limit: 15,\n      },\n      tableFrom2: {\n        status: \"\",\n        store_name: \"\",\n        export: 1,\n      },\n      total: 0,\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 添加\n    add() {\n      this.$router.push({ path: `${this.roterPre}/marketing/store_bargain/create` });\n    },\n    // 导出\n    // exports () {\n    //     let formValidate = this.tableFrom;\n    //     let data = {\n    //         start_status: formValidate.start_status,\n    //         status: formValidate.status,\n    //         store_name: formValidate.store_name\n    //     };\n    //     stroeBargainApi(data).then(res => {\n    //         location.href = res.data[0];\n    //     }).catch(res => {\n    //         this.$Message.error(res.msg)\n    //     })\n    // },\n    // 数据导出；\n    async exports() {\n      let [th, filekey, data, fileName] = [[], [], [], \"\"];\n      //   let fileName = \"\";\n      let excelData = JSON.parse(JSON.stringify(this.tableFrom));\n      excelData.page = 1;\n      for (let i = 0; i < excelData.page + 1; i++) {\n        let lebData = await this.getExcelData(excelData);\n        if (!fileName) fileName = lebData.filename;\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) th = lebData.header;\n        if (lebData.export.length) {\n          data = data.concat(lebData.export);\n          excelData.page++;\n        } else {\n          exportExcel(th, filekey, fileName, data);\n          return;\n        }\n      }\n    },\n    getExcelData(excelData) {\n      return new Promise((resolve, reject) => {\n        stroeBargainApi(excelData).then((res) => {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 编辑\n    edit(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_bargain/create/\" + row.id + \"/0\",\n      });\n    },\n    // 一键复制\n    copy(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_bargain/create/\" + row.id + \"/1\",\n      });\n    },\n    // 删除\n    del(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `marketing/bargain/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.tableList.splice(num, 1);\n          if (!this.tableList.length) {\n            this.tableFrom.page =\n                this.tableFrom.page == 1 ? 1 : this.tableFrom.page - 1;\n          }\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    viewInfo(row) {\n      this.$router.push({\n        path: this.roterPre+'/marketing/store_bargain/statistics/' + row.id,\n      });\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      this.tableFrom.start_status = this.tableFrom.start_status || \"\";\n      this.tableFrom.status = this.tableFrom.status || \"\";\n      bargainListApi(this.tableFrom)\n        .then(async (res) => {\n          let data = res.data;\n          this.tableList = data.list;\n          this.total = res.data.count;\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 修改是否显示\n    onchangeIsShow(row) {\n      let data = {\n        id: row.id,\n        status: row.status,\n      };\n      if (!parseInt(row.status)) {\n        this.$Modal.confirm({\n          title: \"您确定要下架【\" + row.title + \"】商品吗？\",\n          content:\n            \"下架将会删除前台用户已砍的所有记录，所有用户将重新发起砍价,您确定要这样操作吗？\",\n          okText: \"我想好了，确定要下架\",\n          cancelText: \"取消下架操作\",\n          onOk: () => {\n            bargainSetStatusApi(data)\n              .then(async (res) => {\n                this.$Message.success(res.msg);\n              })\n              .catch((res) => {\n                this.$Message.error(res.msg);\n              });\n          },\n          onCancel: () => {\n            row.status = 1;\n          },\n        });\n      } else {\n        bargainSetStatusApi(data)\n          .then(async (res) => {\n            this.$Message.success(res.msg);\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n      }\n    },\n  },\n};\n", null]}