{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRecord.vue?vue&type=template&id=031be995&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRecord.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Modal',{staticClass:\"order_box\",attrs:{\"scrollable\":\"\",\"title\":\"订单记录\",\"width\":\"700\",\"footer-hide\":\"\"},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('Card',{attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Table',{attrs:{\"columns\":_vm.columns,\"border\":\"\",\"data\":_vm.recordData,\"loading\":_vm.loading,\"no-data-text\":\"暂无数据\",\"highlight-row\":\"\",\"no-filtered-data-text\":\"暂无筛选结果\"}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}