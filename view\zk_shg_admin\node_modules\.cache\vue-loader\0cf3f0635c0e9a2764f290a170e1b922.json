{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\record\\index.vue?vue&type=template&id=0293d71c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\record\\index.vue", "mtime": 1745374843360}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 用户-付费会员-会员记录 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n\t\t\t<div class=\"new_card_pd\">\n        <!-- 筛选条件 -->\n\t\t\t\t<Form\n        ref=\"formValidate\"\n        inline\n        :model=\"formValidate\"\n        :label-width=\"labelWidth\"\n        :label-position=\"labelPosition\"\n        class=\"tabform\"\n        @submit.native.prevent\n      >\n        <FormItem label=\"会员类型：\">\n          <Select\n            v-model=\"formValidate.member_type\"\n            clearable\n            @on-change=\"userSearchs\"\n            class=\"input-add\"\n          >\n            <Option\n              v-for=\"item in treeSelect\"\n              :value=\"item.value\"\n              :key=\"item.value\"\n              >{{ item.label }}</Option\n            >\n          </Select>\n        </FormItem>\n        <FormItem label=\"支付方式：\">\n          <Select\n            v-model=\"formValidate.pay_type\"\n            clearable\n            @on-change=\"paySearchs\"\n            class=\"input-add\"\n          >\n            <Option\n              v-for=\"item in payList\"\n              :value=\"item.val\"\n              :key=\"item.val\"\n              >{{ item.label }}</Option\n            >\n          </Select>\n        </FormItem>\n        <FormItem label=\"购买时间：\">\n          <DatePicker\n            :editable=\"false\"\n            @on-change=\"onchangeTime\"\n            :value=\"timeVal\"\n            format=\"yyyy/MM/dd\"\n            type=\"datetimerange\"\n            placement=\"bottom-start\"\n            placeholder=\"自定义时间\"\n            class=\"input-add\"\n            :options=\"options\"\n          ></DatePicker>\n        </FormItem>\n        <FormItem label=\"搜索：\">\n          <Input\n            placeholder=\"请输入用户名称搜索\"\n            element-id=\"name\"\n            v-model=\"formValidate.name\"\n            class=\"input-add mr14\"\n          />\n          <Button type=\"primary\" @click=\"selChange()\">查询</Button>\n        </FormItem>\n       \n      </Form>\n\t\t\t</div>\n\t\t</Card>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n      <!-- 会员记录表格 -->\n      <Table\n        :columns=\"thead\"\n        :data=\"tbody\"\n        ref=\"table\"\n        :loading=\"loading\"\n        highlight-row\n        no-userFrom-text=\"暂无数据\"\n        no-filtered-userFrom-text=\"暂无筛选结果\"\n      >\n      </Table>\n      <div class=\"acea-row row-right page\">\n        <Page\n          :total=\"total\"\n          :current=\"tablePage.page\"\n          :page-size=\"tablePage.limit\"\n          show-elevator\n          show-total\n          @on-change=\"pageChange\"\n        />\n      </div>\n    </Card>\n  </div>\n", null]}