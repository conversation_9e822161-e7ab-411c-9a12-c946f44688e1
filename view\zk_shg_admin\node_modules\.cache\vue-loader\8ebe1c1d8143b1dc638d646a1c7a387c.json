{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_search.vue?vue&type=template&id=87af8756&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_search.vue", "mtime": 1689129842000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"acea-row row-top\",staticStyle:{\"margin-bottom\":\"20px\"}},[_c('CheckboxGroup',{on:{\"on-change\":function($event){return _vm.checkboxChange($event)}},model:{value:(_vm.configData.type),callback:function ($$v) {_vm.$set(_vm.configData, \"type\", $$v)},expression:\"configData.type\"}},[_c('div',[_c('Checkbox',{attrs:{\"label\":1}},[_c('span',[_vm._v(\"商品分类\")])]),_c('Cascader',{attrs:{\"data\":_vm.configData.list,\"placeholder\":\"请选择商品分类\",\"change-on-select\":\"\",\"filterable\":\"\"},on:{\"on-change\":_vm.sliderChange},model:{value:(_vm.configData.activeValue),callback:function ($$v) {_vm.$set(_vm.configData, \"activeValue\", $$v)},expression:\"configData.activeValue\"}})],1),_c('div',[_c('Checkbox',{attrs:{\"label\":2}},[_c('span',[_vm._v(\"Twitter2\")])])],1)])],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}