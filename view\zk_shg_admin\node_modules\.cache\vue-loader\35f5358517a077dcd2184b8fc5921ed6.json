{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_imgs.vue?vue&type=template&id=d456676e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_imgs.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"hot_imgs\">\n    <div class=\"title\">\n        最多可添加4个版块，图片建议尺寸140 * 140px；鼠标拖拽左侧圆点可\n        调整版块顺序\n    </div>\n    <div class=\"list-box\">\n        <draggable\n                class=\"dragArea list-group\"\n                :list=\"defaults.menu\"\n                group=\"people\"\n                handle=\".move-icon\"\n        >\n        <div class=\"item\" v-for=\"(item,index) in defaults.menu\" :key=\"index\">\n            <div class=\"move-icon\">\n                <Icon type=\"ios-keypad-outline\" size=\"22\" />\n            </div>\n            <div class=\"img-box\" @click=\"modalPicTap('单选',index)\">\n                <img :src=\"item.img\" alt=\"\" v-if=\"item.img\">\n                <div class=\"upload-box\" v-else><Icon type=\"ios-camera-outline\" size=\"36\" /></div>\n                <div>\n                    <Modal v-model=\"modalPic\" width=\"960px\" scrollable  footer-hide closable title='上传图片' :mask-closable=\"false\" :z-index=\"1\">\n                        <uploadPictures :isChoice=\"isChoice\" @getPic=\"getPic\" :gridBtn=\"gridBtn\" :gridPic=\"gridPic\" v-if=\"modalPic\"></uploadPictures>\n                    </Modal>\n                </div>\n            </div>\n            <div class=\"info\">\n                <div class=\"info-item\" v-for=\"(infos,key) in item.info\" :key=\"key\">\n                    <span>{{infos.title}}</span>\n                    <div class=\"input-box\">\n                        <Input v-model=\"infos.value\" :placeholder=\"infos.tips\" :maxlength=\"infos.max\" />\n                    </div>\n                </div>\n            </div>\n        </div>\n        </draggable>\n    </div>\n    <div class=\"add-btn\" v-if=\"defaults.menu.length < 4\">\n        <Button style=\"width: 100%; height: 40px;\" @click=\"addBox\">添加板块</Button>\n    </div>\n\n</div>\n", null]}