{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue?vue&type=template&id=19f859da&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"upload_img\" :class=\"configData.type == 'code'?'on':''\" v-if=\"configData\">\n        <div class=\"header\">{{configData.header}}</div>\n        <div class=\"title\">{{configData.title}}</div>\n\t\t<div class=\"list\">\n\t\t\t<div class=\"item\">\n\t\t\t\t<div class=\"name\">{{configData.type == 'code'?configData.name:'图片'}}</div>\n\t\t\t\t<div class=\"picTxt\">\n\t\t\t\t\t<div class=\"box\" @click=\"modalPicTap('单选')\">\n\t\t\t\t\t\t<div class=\"pictrue acea-row row-center-wrapper\" v-if=\"configData.url\">\n\t\t\t\t\t\t\t<video v-if=\"configData.video\" :src=\"configData.url\"></video>\n\t\t\t\t\t\t\t<img :src=\"configData.url\" alt=\"\" v-else>\n\t\t\t\t\t\t\t<div v-if=\"configData.delType\" class=\"iconfont icondel_1\" @click.stop=\"bindDelete\"></div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t    <div class=\"upload-box\" v-else><Icon type=\"ios-add\" size=\"50\" /></div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"tip\">{{configData.info}}</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div v-if=\"configData.type != 'code'\" class=\"item\" @click=\"getLink\">\n\t\t\t\t<div class=\"name\">链接</div>\n\t\t\t\t<Input v-model=\"configData.link\" icon=\"ios-arrow-forward\" readonly placeholder=\"输入链接\" style=\"width: 290px\" />\n\t\t\t</div>\n\t\t</div>\n        <div>\n            <Modal v-model=\"modalPic\" width=\"960px\" scrollable  footer-hide closable :title=\"configData.header?configData.header:'上传图片'\" :mask-closable=\"false\" :z-index=\"1\">\n                <uploadPictures :isChoice=\"isChoice\" @getPic=\"getPic\" :gridBtn=\"gridBtn\" :gridPic=\"gridPic\" v-if=\"modalPic\"></uploadPictures>\n            </Modal>\n        </div>\n\t\t<linkaddress v-if=\"configData.type != 'code'\" ref=\"linkaddres\" @linkUrl=\"linkUrl\"></linkaddress>\n    </div>\n", null]}