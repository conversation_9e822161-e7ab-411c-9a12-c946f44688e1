{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue?vue&type=template&id=163c60bb&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"box\"},[_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"\\n            页面标题\\n        \")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"19\"}},[_c('Input',{attrs:{\"placeholder\":\"选填不超过30个字\",\"maxlength\":\"30\"},on:{\"on-change\":_vm.changVal},model:{value:(_vm.value),callback:function ($$v) {_vm.value=$$v},expression:\"value\"}})],1)],1),_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"\\n            页面状态\\n        \")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"19\"}},[_c('i-switch',{on:{\"on-change\":_vm.changeState},model:{value:(_vm.isShow),callback:function ($$v) {_vm.isShow=$$v},expression:\"isShow\"}})],1)],1),_c('div',{staticClass:\"c_row-item acea-row row-top\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"\\n            背景设置\\n        \")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"19\"}},[_c('div',{staticClass:\"acea-row row-between row-top color\"},[_c('Checkbox',{on:{\"on-change\":_vm.bgColorTap},model:{value:(_vm.bgColor),callback:function ($$v) {_vm.bgColor=$$v},expression:\"bgColor\"}},[_vm._v(\"背景色\")]),_c('ColorPicker',{on:{\"on-change\":function($event){return _vm.colorPickerTap(_vm.colorPicker)}},model:{value:(_vm.colorPicker),callback:function ($$v) {_vm.colorPicker=$$v},expression:\"colorPicker\"}})],1),_c('div',{staticClass:\"acea-row row-between row-top color\"},[_c('Checkbox',{on:{\"on-change\":_vm.bgPicTap},model:{value:(_vm.bgPic),callback:function ($$v) {_vm.bgPic=$$v},expression:\"bgPic\"}},[_vm._v(\"背景图\")]),_c('RadioGroup',{attrs:{\"type\":\"button\"},on:{\"on-change\":_vm.radioTap},model:{value:(_vm.tabVal),callback:function ($$v) {_vm.tabVal=$$v},expression:\"tabVal\"}},_vm._l((_vm.picList),function(item,index){return _c('Radio',{key:index,attrs:{\"label\":index}},[_c('span',{staticClass:\"iconfont-diy\",class:item})])}),1)],1),(_vm.bgPic)?_c('div',[_c('div',{staticClass:\"title\"},[_vm._v(\"建议尺寸：690 * 240px\")]),_c('div',{staticClass:\"boxs\",on:{\"click\":function($event){return _vm.modalPicTap('单选')}}},[(_vm.bgPicUrl)?_c('img',{attrs:{\"src\":_vm.bgPicUrl,\"alt\":\"\"}}):_c('div',{staticClass:\"upload-box\"},[_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"36\"}})],1),(_vm.bgPicUrl)?_c('div',{staticClass:\"replace\"},[_vm._v(\"更换图片\")]):_vm._e()])]):_vm._e()])],1),_c('div',[_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"上传背景图\",\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}