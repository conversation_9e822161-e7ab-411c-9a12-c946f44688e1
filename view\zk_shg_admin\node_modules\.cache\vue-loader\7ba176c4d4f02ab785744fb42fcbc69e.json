{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\group\\index.vue?vue&type=template&id=a7d71d86&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\group\\index.vue", "mtime": 1683594784000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 用户-用户分组 -->\n    <div>\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n            <!-- 相关操作 -->\n            <Row type=\"flex\">\n                <Col v-bind=\"grid\">\n                    <Button v-auth=\"['admin-user-group']\" type=\"primary\" @click=\"add\">添加分组</Button>\n                </Col>\n            </Row>\n            <!-- 用户分组表格 -->\n            <Table :columns=\"columns1\" :data=\"groupLists\" ref=\"table\" class=\"mt25\"\n                   :loading=\"loading\" highlight-row\n                   no-userFrom-text=\"暂无数据\"\n                   no-filtered-userFrom-text=\"暂无筛选结果\">\n                <template slot-scope=\"{ row, index }\" slot=\"icons\">\n                    <viewer>\n                        <div class=\"tabBox_img\">\n                            <img v-lazy=\"row.icon\">\n                        </div>\n                    </viewer>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"action\">\n                    <a @click=\"edit(row.id)\">修改</a>\n                    <Divider type=\"vertical\" />\n                    <a @click=\"del(row,'删除分组',index)\">删除</a>\n                </template>\n            </Table>\n            <div class=\"acea-row row-right page\">\n                <Page :total=\"total\" show-elevator show-total @on-change=\"pageChange\"\n                      :page-size=\"groupFrom.limit\"/>\n            </div>\n        </Card>\n    </div>\n", null]}