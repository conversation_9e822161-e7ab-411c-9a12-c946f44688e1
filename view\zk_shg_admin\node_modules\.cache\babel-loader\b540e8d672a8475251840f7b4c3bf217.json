{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\systemStore\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\systemStore\\index.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { storeApi, keyApi, storeAddApi, storeGetInfoApi } from '@/api/setting';\nimport { mapState } from 'vuex';\nimport city from '@/utils/city';\nimport uploadPictures from '@/components/uploadPictures';\nimport Setting from \"@/setting\";\nexport default {\n  name: 'systemStore',\n  components: {\n    uploadPictures: uploadPictures\n  },\n  props: {},\n  data: function data() {\n    var _this = this;\n\n    var validatePhone = function validatePhone(rule, value, callback) {\n      if (!value) {\n        return callback(new Error('请填写手机号'));\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n        callback(new Error('手机号格式不正确!'));\n      } else {\n        callback();\n      }\n    };\n\n    var validateUpload = function validateUpload(rule, value, callback) {\n      if (!_this.formItem.image) {\n        callback(new Error('请上传提货点logo'));\n      } else {\n        callback();\n      }\n    };\n\n    return {\n      roterPre: Setting.roterPre,\n      isTemplate: false,\n      spinShow: false,\n      modalMap: false,\n      addresData: [],\n      formItem: {\n        name: '',\n        introduction: '',\n        phone: '',\n        address: [],\n        address2: [],\n        detailed_address: '',\n        valid_time: [],\n        day_time: [],\n        latlng: '',\n        id: 0\n      },\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '请输入提货点名称',\n          trigger: 'blur'\n        }],\n        mail: [{\n          required: true,\n          message: 'Mailbox cannot be empty',\n          trigger: 'blur'\n        }, {\n          type: 'email',\n          message: 'Incorrect email format',\n          trigger: 'blur'\n        }],\n        address: [{\n          required: true,\n          message: '请选择提货点地址',\n          type: 'array',\n          trigger: 'change'\n        }],\n        valid_time: [{\n          required: true,\n          type: 'array',\n          message: '请选择核销时效',\n          trigger: 'change',\n          fields: {\n            0: {\n              type: 'date',\n              required: true,\n              message: '请选择年度范围'\n            },\n            1: {\n              type: 'date',\n              required: true,\n              message: '请选择年度范围'\n            }\n          }\n        }],\n        day_time: [{\n          required: true,\n          type: 'array',\n          message: '请选择提货点营业时间',\n          trigger: 'change'\n        }],\n        phone: [{\n          required: true,\n          validator: validatePhone,\n          trigger: 'blur'\n        }],\n        detailed_address: [{\n          required: true,\n          message: '请输入详细地址',\n          trigger: 'blur'\n        }],\n        image: [{\n          required: true,\n          validator: validateUpload,\n          trigger: 'change'\n        }],\n        latlng: [{\n          required: true,\n          message: '请选择经纬度',\n          trigger: 'blur'\n        }]\n      },\n      keyUrl: '',\n      grid: {\n        xl: 20,\n        lg: 20,\n        md: 20,\n        sm: 24,\n        xs: 24\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      modalPic: false,\n      isChoice: '单选'\n    };\n  },\n  created: function created() {\n    var that = this;\n    city.map(function (item) {\n      item.value = item.label;\n\n      if (item.children && item.children.length) {\n        item.children.map(function (j) {\n          j.value = j.label;\n\n          if (j.children && j.children.length) {\n            j.children.map(function (o) {\n              o.value = o.label;\n            });\n          }\n        });\n      }\n    });\n    setTimeout(function () {\n      that.addresData = city;\n    }, 10);\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  mounted: function mounted() {\n    window.addEventListener('message', function (event) {\n      // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息\n      var loc = event.data;\n\n      if (loc && loc.module === 'locationPicker') {\n        // 防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'\n        window.parent.selectAdderss(loc);\n      }\n    }, false);\n    window.selectAdderss = this.selectAdderss;\n  },\n  methods: {\n    cancel: function cancel() {\n      this.$refs['formItem'].resetFields();\n      this.clearFrom();\n    },\n    clearFrom: function clearFrom() {\n      this.formItem.introduction = '';\n      this.formItem.day_time = [];\n    },\n    // 选择经纬度\n    selectAdderss: function selectAdderss(data) {\n      this.formItem.latlng = data.latlng.lat + ',' + data.latlng.lng;\n      this.modalMap = false;\n    },\n    // key值\n    getKey: function getKey() {\n      var _this2 = this;\n\n      keyApi().then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var keys;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this2.modalMap = true;\n                  keys = res.data.key;\n                  _this2.keyUrl = \"https://apis.map.qq.com/tools/locpicker?type=1&key=\".concat(keys, \"&referer=myapp\");\n\n                case 3:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.$Modal.confirm({\n          title: '提示',\n          content: '<p>' + res.msg + '</p>',\n          onOk: function onOk() {\n            _this2.$router.push({\n              path: _this2.roterPre + '/setting/system_config'\n            });\n          },\n          onCancel: function onCancel() {}\n        }); // this.$Message.error(res.msg);\n\n      });\n    },\n    // 详情\n    getInfo: function getInfo(id) {\n      var that = this;\n      that.formItem.id = id;\n      that.spinShow = true;\n      storeGetInfoApi(id).then(function (res) {\n        var info = res.data.info || null;\n        that.formItem = info || that.formItem;\n        that.formItem.address = info.address2;\n        that.spinShow = false;\n      }).catch(function (res) {\n        that.spinShow = false;\n        that.$Message.error(res.msg);\n      });\n    },\n    // 选择图片\n    modalPicTap: function modalPicTap() {\n      this.modalPic = true;\n    },\n    // 选中图片\n    getPic: function getPic(pc) {\n      this.formItem.image = pc.att_dir;\n      this.modalPic = false;\n    },\n    // 选择地址\n    handleChange: function handleChange(value, selectedData) {\n      this.formItem.address = selectedData.map(function (o) {\n        return o.label;\n      }); //  this.formItem.address2 = selectedData.map(o => o.value);\n    },\n    // 核销时效\n    onchangeDate: function onchangeDate(e) {\n      this.formItem.valid_time = e;\n    },\n    // 营业时间\n    onchangeTime: function onchangeTime(e) {\n      this.formItem.day_time = e;\n    },\n    onSearch: function onSearch() {\n      this.getKey();\n    },\n    // 提交\n    handleSubmit: function handleSubmit(name) {\n      var _this3 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          storeAddApi(_this3.formItem).then(\n          /*#__PURE__*/\n          function () {\n            var _ref2 = _asyncToGenerator(\n            /*#__PURE__*/\n            _regeneratorRuntime.mark(function _callee2(res) {\n              return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n                while (1) {\n                  switch (_context2.prev = _context2.next) {\n                    case 0:\n                      _this3.$Message.success(res.msg);\n\n                      _this3.isTemplate = false;\n\n                      _this3.$parent.getList();\n\n                      _this3.$refs[name].resetFields();\n\n                      _this3.clearFrom();\n\n                    case 5:\n                    case \"end\":\n                      return _context2.stop();\n                  }\n                }\n              }, _callee2);\n            }));\n\n            return function (_x2) {\n              return _ref2.apply(this, arguments);\n            };\n          }()).catch(function (res) {\n            _this3.$Message.error(res.msg);\n          });\n        } else {\n          return false;\n        }\n      });\n    }\n  }\n};", null]}