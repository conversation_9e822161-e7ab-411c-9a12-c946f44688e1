{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_select_item.vue?vue&type=style&index=0&id=64304642&scoped=true&lang=less&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_select_item.vue", "mtime": 1682663004000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1725352507996}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.poptipOn{\n  display: none;\n}\n/deep/textarea.ivu-input{\n  resize: none;\n}\n.batchItem{\n  padding-left: 2px;\n  padding-right: 2px;\n  .title{\n    font-size: 13px;\n    color: #333;\n    font-weight: 400;\n    margin-top: 6px;\n  }\n  .tips{\n    font-size: 12px;\n    color: #999999;\n    font-weight: 400;\n    margin-top: 3px;\n    margin-bottom: 10px;\n  }\n  .batchBnt{\n    margin-top: 12px;\n  }\n}\n.select-word{\n  margin-bottom: 20px;\n  .c_row-item{\n    align-items: baseline !important;\n    text-align: right;\n    color: #666;\n    /deep/.ivu-input-suffix i{\n      color: #999;\n      font-size: 18px;\n    }\n    .button{\n       margin-top: 20px;\n      &.on{\n        margin-top: 0;\n      }\n      .bnt{\n        width: 143px;\n        height: 32px;\n        border: 1px solid #DCDFE6;\n        border-radius: 5px;\n        font-size: 13px;\n        cursor: pointer;\n        .iconfont{\n          font-size: 12px;\n          color: #1890FF;\n          margin-right: 5px;\n        }\n      }\n    }\n    .inputs{\n      margin-bottom: 10px;\n    }\n  }\n}\n", null]}