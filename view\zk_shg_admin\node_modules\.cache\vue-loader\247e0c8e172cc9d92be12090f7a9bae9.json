{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\userExtract\\index.vue?vue&type=template&id=e93a0d60&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\userExtract\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 财务-提现申请 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n      <div class=\"card_pd\">\n        <!-- 查询条件 -->\n        <Form\n        ref=\"formValidate\"\n        inline\n        :model=\"formValidate\"\n        :label-width=\"labelWidth\"\n        :label-position=\"labelPosition\"\n        @submit.native.prevent\n      >\n        <FormItem label=\"时间选择：\">\n          <DatePicker\n            :editable=\"false\"\n            @on-change=\"onchangeTime\"\n            :value=\"timeVal\"\n            format=\"yyyy/MM/dd HH:mm\"\n            type=\"datetimerange\"\n            placement=\"bottom-end\"\n            placeholder=\"自定义时间\"\n           class=\"input-width\"\n            :options=\"options\"\n          ></DatePicker>\n        </FormItem>\n\n        <FormItem label=\"提现状态：\">\n          <Select v-model=\"formValidate.status\" class=\"input-add\" @on-change=\"selChange\">\n            <Option\n              v-for=\"(itemn, indexn) in treeData.withdrawal\"\n              :value=\"itemn.value\"\n              :key=\"indexn\"\n              >{{ itemn.title }}</Option\n            >\n          </Select>\n        </FormItem>\n\n        <FormItem label=\"提现方式：\">\n          <Select v-model=\"formValidate.extract_type\" class=\"input-add\" @on-change=\"selChange\">\n            <Option\n              v-for=\"(itemn, indexn) in treeData.payment\"\n              :value=\"itemn.value\"\n              :key=\"indexn\"\n              >{{ itemn.title }}</Option\n            >\n          </Select>\n        </FormItem>\n\n        <FormItem label=\"搜索：\">\n          <div class=\"acea-row row-middle\">\n            <Input\n              placeholder=\"微信昵称/姓名/支付宝账号/银行卡号\"\n              element-id=\"name\"\n              v-model=\"formValidate.nireid\"\n               class=\"input-width\"\n            />\n          </div>\n        </FormItem>\n        <FormItem>\n          <Button type=\"primary\" @click=\"selChange\" class=\"btn-add\">查询</Button>\n          <Button @click=\"reset\">重置</Button>\n        </FormItem>\n      </Form>\n      </div>\n    </Card>\n    <cards-data :cardLists=\"cardLists\" v-if=\"extractStatistics\"></cards-data>\n    <Card :bordered=\"false\" dis-hover>\n      <!-- 表格 -->\n      <Table\n        ref=\"table\"\n        :columns=\"columns\"\n        :data=\"tabList\"\n        class=\"ivu-mt\"\n        :loading=\"loading\"\n        no-data-text=\"暂无数据\"\n        no-filtered-data-text=\"暂无筛选结果\"\n      >\n        <template slot-scope=\"{ row }\" slot=\"nickname\">\n          <div>\n            用户昵称: {{ row.nickname }} <br />\n            用户id:{{ row.uid }}\n          </div>\n        </template>\n        <template slot-scope=\"{ row }\" slot=\"extract_price\">\n          <div>{{ row.extract_price }}</div>\n        </template>\n        <template slot-scope=\"{ row }\" slot=\"extract_fee\">\n          <div>{{ row.extract_fee }}</div>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"add_time\">\n          <span> {{ row.add_time | formatDate }}</span>\n        </template>\n        <template slot-scope=\"{ row }\" slot=\"extract_type\">\n          <div class=\"type\" v-if=\"row.extract_type === 'bank'\">\n            <div class=\"item\">姓名:{{ row.real_name }}</div>\n            <div class=\"item\">银行卡号:{{ row.bank_code }}</div>\n            <div class=\"item\">银行开户地址:{{ row.bank_address }}</div>\n          </div>\n          <div class=\"type\" v-if=\"row.extract_type === 'weixin'\">\n            <div class=\"item\">昵称:{{ row.nickname }}</div>\n            <div class=\"item\">微信号:{{ row.wechat }}</div>\n          </div>\n          <div class=\"type\" v-if=\"row.extract_type === 'alipay'\">\n            <div class=\"item\">姓名:{{ row.real_name }}</div>\n            <div class=\"item\">支付宝号:{{ row.alipay_code }}</div>\n          </div>\n          <div class=\"type\" v-if=\"row.extract_type === 'balance'\">\n            <div class=\"item\">姓名:{{ row.real_name }}</div>\n            <div class=\"item\">提现方式：佣金转入余额</div>\n          </div>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"qrcode_url\">\n          <viewer\n            v-if=\"\n              row.extract_type === 'weixin' || row.extract_type === 'alipay'\n            \"\n          >\n            <div class=\"tabBox_img\">\n              <img v-lazy=\"row.qrcode_url\" />\n            </div>\n          </viewer>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"status\">\n          <div class=\"status\" v-if=\"row.status === 0\">\n            <div class=\"statusVal\">申请中</div>\n            <div>\n              <Button\n                type=\"error\"\n                icon=\"md-close\"\n                size=\"small\"\n                class=\"item\"\n                @click=\"invalid(row)\"\n                >无效</Button\n              >\n              <Button\n                type=\"info\"\n                icon=\"md-checkmark\"\n                size=\"small\"\n                class=\"item\"\n                @click=\"adopt(row, '审核通过', index)\"\n                >通过</Button\n              >\n            </div>\n          </div>\n          <div class=\"statusVal\" v-if=\"row.status === 1\">提现通过</div>\n          <div class=\"statusVal\" v-if=\"row.status === -1\">\n            提现未通过<br />未通过原因：{{ row.fail_msg }}\n          </div>\n        </template>\n        <template\n          slot-scope=\"{ row }\"\n          slot=\"createModalFrame\"\n          v-if=\"row.extract_type != 'balance'\"\n        >\n          <a href=\"javascript:void(0);\" @click=\"edit(row)\">编辑</a>\n        </template>\n      </Table>\n      <div class=\"acea-row row-right page\">\n        <Page\n          :total=\"total\"\n          :current=\"formValidate.page\"\n          show-elevator\n          show-total\n          @on-change=\"pageChange\"\n          :page-size=\"formValidate.limit\"\n        />\n      </div>\n    </Card>\n\n    <!-- 编辑表单-->\n    <edit-from\n      ref=\"edits\"\n      :FromData=\"FromData\"\n      @submitFail=\"submitFail\"\n    ></edit-from>\n    <!-- 拒绝通过-->\n    <Modal\n      v-model=\"modals\"\n      scrollable\n      closable\n      title=\"未通过原因\"\n      :mask-closable=\"false\"\n    >\n      <Input\n        v-model=\"fail_msg.message\"\n        type=\"textarea\"\n        :rows=\"4\"\n        placeholder=\"请输入未通过原因\"\n      />\n      <div slot=\"footer\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          long\n          :loading=\"modal_loading\"\n          @click=\"oks\"\n          >确定</Button\n        >\n      </div>\n    </Modal>\n  </div>\n", null]}