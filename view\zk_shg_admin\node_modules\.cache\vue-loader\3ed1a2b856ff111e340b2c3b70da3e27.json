{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=template&id=2f96c05e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1750985347698}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"transfer-container\" },\n    [\n      _c(\"div\", { staticClass: \"page-header\" }, [\n        _vm._m(0),\n        _c(\n          \"div\",\n          { staticClass: \"header-right\" },\n          [\n            _c(\n              \"el-button\",\n              {\n                attrs: { type: \"warning\", icon: \"el-icon-warning\" },\n                on: { click: _vm.showPendingOnly }\n              },\n              [_vm._v(\"待审核单据\")]\n            ),\n            _c(\n              \"el-button\",\n              {\n                attrs: { type: \"success\", icon: \"el-icon-download\" },\n                on: { click: _vm.exportTransfer }\n              },\n              [_vm._v(\"导出数据\")]\n            ),\n            _c(\n              \"el-button\",\n              {\n                attrs: { type: \"primary\", icon: \"el-icon-pie-chart\" },\n                on: { click: _vm.showStatistics }\n              },\n              [_vm._v(\"统计分析\")]\n            )\n          ],\n          1\n        )\n      ]),\n      _c(\n        \"div\",\n        { staticClass: \"filter-container\" },\n        [\n          _c(\n            \"el-form\",\n            { attrs: { model: _vm.filterForm, inline: \"\" } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"调拨单号：\" } },\n                [\n                  _c(\"el-input\", {\n                    staticStyle: { width: \"200px\" },\n                    attrs: { placeholder: \"输入调拨单号\" },\n                    model: {\n                      value: _vm.filterForm.order_no,\n                      callback: function($$v) {\n                        _vm.$set(_vm.filterForm, \"order_no\", $$v)\n                      },\n                      expression: \"filterForm.order_no\"\n                    }\n                  })\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"调出门店：\" } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      staticStyle: { width: \"200px\" },\n                      attrs: { placeholder: \"选择调出门店\", clearable: \"\" },\n                      model: {\n                        value: _vm.filterForm.from_store_id,\n                        callback: function($$v) {\n                          _vm.$set(_vm.filterForm, \"from_store_id\", $$v)\n                        },\n                        expression: \"filterForm.from_store_id\"\n                      }\n                    },\n                    [\n                      _c(\"el-option\", {\n                        attrs: { label: \"全部门店\", value: \"\" }\n                      }),\n                      _vm._l(_vm.storeList, function(store) {\n                        return _c(\"el-option\", {\n                          key: store.id,\n                          attrs: { label: store.name, value: store.id }\n                        })\n                      })\n                    ],\n                    2\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"调入门店：\" } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      staticStyle: { width: \"200px\" },\n                      attrs: { placeholder: \"选择调入门店\", clearable: \"\" },\n                      model: {\n                        value: _vm.filterForm.to_store_id,\n                        callback: function($$v) {\n                          _vm.$set(_vm.filterForm, \"to_store_id\", $$v)\n                        },\n                        expression: \"filterForm.to_store_id\"\n                      }\n                    },\n                    [\n                      _c(\"el-option\", {\n                        attrs: { label: \"全部门店\", value: \"\" }\n                      }),\n                      _vm._l(_vm.storeList, function(store) {\n                        return _c(\"el-option\", {\n                          key: store.id,\n                          attrs: { label: store.name, value: store.id }\n                        })\n                      })\n                    ],\n                    2\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"单据状态：\" } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      staticStyle: { width: \"150px\" },\n                      attrs: { placeholder: \"单据状态\", clearable: \"\" },\n                      model: {\n                        value: _vm.filterForm.status,\n                        callback: function($$v) {\n                          _vm.$set(_vm.filterForm, \"status\", $$v)\n                        },\n                        expression: \"filterForm.status\"\n                      }\n                    },\n                    [\n                      _c(\"el-option\", { attrs: { label: \"全部\", value: \"\" } }),\n                      _c(\"el-option\", { attrs: { label: \"待审核\", value: 0 } }),\n                      _c(\"el-option\", { attrs: { label: \"已通过\", value: 1 } }),\n                      _c(\"el-option\", { attrs: { label: \"已完成\", value: 2 } }),\n                      _c(\"el-option\", { attrs: { label: \"已拒绝\", value: 3 } }),\n                      _c(\"el-option\", { attrs: { label: \"已撤销\", value: 4 } })\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"申请日期：\" } },\n                [\n                  _c(\"el-date-picker\", {\n                    staticStyle: { width: \"240px\" },\n                    attrs: {\n                      type: \"daterange\",\n                      \"range-separator\": \"至\",\n                      \"start-placeholder\": \"开始日期\",\n                      \"end-placeholder\": \"结束日期\"\n                    },\n                    model: {\n                      value: _vm.filterForm.date_range,\n                      callback: function($$v) {\n                        _vm.$set(_vm.filterForm, \"date_range\", $$v)\n                      },\n                      expression: \"filterForm.date_range\"\n                    }\n                  })\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: { click: _vm.getTransferList }\n                    },\n                    [_vm._v(\"查询\")]\n                  ),\n                  _c(\"el-button\", { on: { click: _vm.resetFilter } }, [\n                    _vm._v(\"重置\")\n                  ])\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\"div\", { staticClass: \"stats-container\" }, [\n        _c(\"div\", { staticClass: \"stat-card\" }, [\n          _vm._m(1),\n          _c(\"div\", { staticClass: \"stat-content\" }, [\n            _c(\"div\", { staticClass: \"stat-number\" }, [\n              _vm._v(_vm._s(_vm.statistics.total_orders))\n            ]),\n            _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"总调拨单\")])\n          ])\n        ]),\n        _c(\"div\", { staticClass: \"stat-card\" }, [\n          _vm._m(2),\n          _c(\"div\", { staticClass: \"stat-content\" }, [\n            _c(\"div\", { staticClass: \"stat-number\" }, [\n              _vm._v(_vm._s(_vm.statistics.pending_orders))\n            ]),\n            _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"待审核\")])\n          ])\n        ]),\n        _c(\"div\", { staticClass: \"stat-card\" }, [\n          _vm._m(3),\n          _c(\"div\", { staticClass: \"stat-content\" }, [\n            _c(\"div\", { staticClass: \"stat-number\" }, [\n              _vm._v(_vm._s(_vm.statistics.approved_orders))\n            ]),\n            _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"已通过\")])\n          ])\n        ]),\n        _c(\"div\", { staticClass: \"stat-card\" }, [\n          _vm._m(4),\n          _c(\"div\", { staticClass: \"stat-content\" }, [\n            _c(\"div\", { staticClass: \"stat-number\" }, [\n              _vm._v(_vm._s(_vm.statistics.completed_orders))\n            ]),\n            _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"已完成\")])\n          ])\n        ])\n      ]),\n      _c(\n        \"div\",\n        { staticClass: \"table-container\" },\n        [\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.tableLoading,\n                  expression: \"tableLoading\"\n                }\n              ],\n              staticStyle: { width: \"100%\" },\n              attrs: { data: _vm.transferList, stripe: \"\", border: \"\" },\n              on: { \"selection-change\": _vm.handleSelectionChange }\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"selection\", width: \"55\", align: \"center\" }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"order_no\",\n                  label: \"调拨单号\",\n                  width: \"160\",\n                  align: \"center\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"el-link\",\n                          {\n                            attrs: { type: \"primary\" },\n                            on: {\n                              click: function($event) {\n                                return _vm.viewDetail(scope.row)\n                              }\n                            }\n                          },\n                          [_vm._v(_vm._s(scope.row.order_no))]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"商品信息\", \"min-width\": \"250\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"product-info\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: {\n                                width: \"40px\",\n                                height: \"40px\",\n                                \"border-radius\": \"4px\",\n                                \"margin-right\": \"12px\"\n                              },\n                              attrs: {\n                                src: scope.row.product_image,\n                                fit: \"cover\"\n                              }\n                            }),\n                            _c(\"div\", [\n                              _c(\"div\", { staticClass: \"product-name\" }, [\n                                _vm._v(_vm._s(scope.row.product_name))\n                              ]),\n                              scope.row.sku\n                                ? _c(\"div\", { staticClass: \"product-spec\" }, [\n                                    _vm._v(_vm._s(scope.row.sku))\n                                  ])\n                                : _vm._e(),\n                              _c(\"div\", { staticClass: \"transfer-qty\" }, [\n                                _vm._v(\n                                  \"调拨数量：\" + _vm._s(scope.row.transfer_qty)\n                                )\n                              ])\n                            ])\n                          ],\n                          1\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"调拨门店\", width: \"200\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\"div\", { staticClass: \"transfer-stores\" }, [\n                          _c(\"div\", { staticClass: \"from-store\" }, [\n                            _vm._v(_vm._s(scope.row.from_store_name))\n                          ]),\n                          _c(\"i\", {\n                            staticClass: \"el-icon-right transfer-arrow\"\n                          }),\n                          _c(\"div\", { staticClass: \"to-store\" }, [\n                            _vm._v(_vm._s(scope.row.to_store_name))\n                          ])\n                        ])\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"单据状态\", width: \"100\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"el-tag\",\n                          {\n                            attrs: {\n                              type: _vm.getStatusTagType(scope.row.status),\n                              size: \"small\"\n                            }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(_vm.getStatusText(scope.row.status)) +\n                                \"\\n          \"\n                            )\n                          ]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"applicant\",\n                  label: \"申请人\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"apply_time\",\n                  label: \"申请时间\",\n                  width: \"160\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"approve_time\",\n                  label: \"审核时间\",\n                  width: \"160\",\n                  align: \"center\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        scope.row.approve_time\n                          ? _c(\"span\", [_vm._v(_vm._s(scope.row.approve_time))])\n                          : _c(\"span\", { staticClass: \"text-muted\" }, [\n                              _vm._v(\"-\")\n                            ])\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  width: \"250\",\n                  align: \"center\",\n                  fixed: \"right\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function($event) {\n                                return _vm.viewDetail(scope.row)\n                              }\n                            }\n                          },\n                          [_vm._v(\"查看详情\")]\n                        ),\n                        scope.row.status === 0\n                          ? [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.approveTransfer(\n                                        scope.row,\n                                        true\n                                      )\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"通过\")]\n                              ),\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.approveTransfer(\n                                        scope.row,\n                                        false\n                                      )\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"拒绝\")]\n                              )\n                            ]\n                          : _vm._e(),\n                        scope.row.status === 1\n                          ? [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.executeTransfer(scope.row)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"执行调拨\")]\n                              ),\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.forceExecute(scope.row)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"强制执行\")]\n                              )\n                            ]\n                          : _vm._e(),\n                        scope.row.status === 3 || scope.row.status === 4\n                          ? [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.deleteTransfer(scope.row)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"删除\")]\n                              )\n                            ]\n                          : _vm._e()\n                      ]\n                    }\n                  }\n                ])\n              })\n            ],\n            1\n          ),\n          _vm.selectedRows.length > 0\n            ? _c(\n                \"div\",\n                { staticClass: \"batch-actions\" },\n                [\n                  _c(\"span\", { staticClass: \"selected-count\" }, [\n                    _vm._v(\"已选择 \" + _vm._s(_vm.selectedRows.length) + \" 项\")\n                  ]),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: {\n                        click: function($event) {\n                          return _vm.batchApprove(true)\n                        }\n                      }\n                    },\n                    [_vm._v(\"批量通过\")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: {\n                        click: function($event) {\n                          return _vm.batchApprove(false)\n                        }\n                      }\n                    },\n                    [_vm._v(\"批量拒绝\")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: { click: _vm.batchExecute }\n                    },\n                    [_vm._v(\"批量执行\")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: { click: _vm.clearSelection }\n                    },\n                    [_vm._v(\"取消选择\")]\n                  )\n                ],\n                1\n              )\n            : _vm._e(),\n          _c(\n            \"div\",\n            { staticClass: \"pagination-container\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"current-page\": _vm.currentPage,\n                  \"page-sizes\": [20, 50, 100, 200],\n                  \"page-size\": _vm.pageSize,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.total\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.handleCurrentChange\n                }\n              })\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"调拨详情\",\n            visible: _vm.detailDialogVisible,\n            width: \"700px\"\n          },\n          on: {\n            \"update:visible\": function($event) {\n              _vm.detailDialogVisible = $event\n            }\n          }\n        },\n        [\n          _vm.currentDetail\n            ? _c(\"div\", { staticClass: \"detail-content\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"detail-section\" },\n                  [\n                    _c(\"h4\", [_vm._v(\"基本信息\")]),\n                    _c(\n                      \"el-row\",\n                      { attrs: { gutter: 20 } },\n                      [\n                        _c(\"el-col\", { attrs: { span: 12 } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"调拨单号：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.order_no))\n                            ])\n                          ])\n                        ]),\n                        _c(\"el-col\", { attrs: { span: 12 } }, [\n                          _c(\n                            \"div\",\n                            { staticClass: \"detail-item\" },\n                            [\n                              _c(\"span\", { staticClass: \"label\" }, [\n                                _vm._v(\"单据状态：\")\n                              ]),\n                              _c(\n                                \"el-tag\",\n                                {\n                                  attrs: {\n                                    type: _vm.getStatusTagType(\n                                      _vm.currentDetail.status\n                                    )\n                                  }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                \" +\n                                      _vm._s(\n                                        _vm.getStatusText(\n                                          _vm.currentDetail.status\n                                        )\n                                      ) +\n                                      \"\\n              \"\n                                  )\n                                ]\n                              )\n                            ],\n                            1\n                          )\n                        ]),\n                        _c(\"el-col\", { attrs: { span: 12 } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"申请人：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.applicant))\n                            ])\n                          ])\n                        ]),\n                        _c(\"el-col\", { attrs: { span: 12 } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"申请时间：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.apply_time))\n                            ])\n                          ])\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\"div\", { staticClass: \"detail-section\" }, [\n                  _c(\"h4\", [_vm._v(\"商品信息\")]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"product-detail\" },\n                    [\n                      _c(\"el-image\", {\n                        staticStyle: {\n                          width: \"80px\",\n                          height: \"80px\",\n                          \"border-radius\": \"8px\",\n                          \"margin-right\": \"16px\"\n                        },\n                        attrs: {\n                          src: _vm.currentDetail.product_image,\n                          fit: \"cover\"\n                        }\n                      }),\n                      _c(\"div\", [\n                        _c(\"div\", { staticClass: \"product-name\" }, [\n                          _vm._v(_vm._s(_vm.currentDetail.product_name))\n                        ]),\n                        _vm.currentDetail.sku\n                          ? _c(\"div\", { staticClass: \"product-spec\" }, [\n                              _vm._v(\"规格：\" + _vm._s(_vm.currentDetail.sku))\n                            ])\n                          : _vm._e(),\n                        _c(\"div\", { staticClass: \"transfer-qty\" }, [\n                          _vm._v(\n                            \"调拨数量：\" +\n                              _vm._s(_vm.currentDetail.transfer_qty)\n                          )\n                        ])\n                      ])\n                    ],\n                    1\n                  )\n                ]),\n                _c(\n                  \"div\",\n                  { staticClass: \"detail-section\" },\n                  [\n                    _c(\"h4\", [_vm._v(\"调拨信息\")]),\n                    _c(\n                      \"el-row\",\n                      { attrs: { gutter: 20 } },\n                      [\n                        _c(\"el-col\", { attrs: { span: 12 } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"调出门店：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.from_store_name))\n                            ])\n                          ])\n                        ]),\n                        _c(\"el-col\", { attrs: { span: 12 } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"调入门店：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.to_store_name))\n                            ])\n                          ])\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _vm.currentDetail.remark\n                  ? _c(\"div\", { staticClass: \"detail-section\" }, [\n                      _c(\"h4\", [_vm._v(\"申请原因\")]),\n                      _c(\"div\", { staticClass: \"remark-content\" }, [\n                        _vm._v(_vm._s(_vm.currentDetail.remark))\n                      ])\n                    ])\n                  : _vm._e(),\n                _vm.currentDetail.approve_remark\n                  ? _c(\"div\", { staticClass: \"detail-section\" }, [\n                      _c(\"h4\", [_vm._v(\"审核备注\")]),\n                      _c(\"div\", { staticClass: \"remark-content\" }, [\n                        _vm._v(_vm._s(_vm.currentDetail.approve_remark))\n                      ])\n                    ])\n                  : _vm._e()\n              ])\n            : _vm._e()\n        ]\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: _vm.approveType ? \"审核通过\" : \"审核拒绝\",\n            visible: _vm.approveDialogVisible,\n            width: \"500px\"\n          },\n          on: {\n            \"update:visible\": function($event) {\n              _vm.approveDialogVisible = $event\n            }\n          }\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"approveForm\",\n              attrs: {\n                model: _vm.approveForm,\n                rules: _vm.approveRules,\n                \"label-width\": \"100px\"\n              }\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"审核备注：\", prop: \"remark\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: {\n                      type: \"textarea\",\n                      placeholder: _vm.approveType\n                        ? \"请填写通过原因（可选）\"\n                        : \"请填写拒绝原因\",\n                      rows: 4\n                    },\n                    model: {\n                      value: _vm.approveForm.remark,\n                      callback: function($$v) {\n                        _vm.$set(_vm.approveForm, \"remark\", $$v)\n                      },\n                      expression: \"approveForm.remark\"\n                    }\n                  })\n                ],\n                1\n              )\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\"\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function($event) {\n                      _vm.approveDialogVisible = false\n                    }\n                  }\n                },\n                [_vm._v(\"取消\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: _vm.approveType ? \"success\" : \"danger\",\n                    loading: _vm.approveLoading\n                  },\n                  on: { click: _vm.submitApprove }\n                },\n                [\n                  _vm._v(\n                    \"\\n        \" +\n                      _vm._s(_vm.approveType ? \"确认通过\" : \"确认拒绝\") +\n                      \"\\n      \"\n                  )\n                ]\n              )\n            ],\n            1\n          )\n        ],\n        1\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"header-left\" }, [\n      _c(\"h2\", { staticClass: \"page-title\" }, [_vm._v(\"调拨管理\")]),\n      _c(\"p\", { staticClass: \"page-desc\" }, [\n        _vm._v(\"管理门店间商品调拨申请，支持审核、执行和统计分析\")\n      ])\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"stat-icon total\" }, [\n      _c(\"i\", { staticClass: \"el-icon-document\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"stat-icon pending\" }, [\n      _c(\"i\", { staticClass: \"el-icon-time\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"stat-icon approved\" }, [\n      _c(\"i\", { staticClass: \"el-icon-check\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"stat-icon completed\" }, [\n      _c(\"i\", { staticClass: \"el-icon-circle-check\" })\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}