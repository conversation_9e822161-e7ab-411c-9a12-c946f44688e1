{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=template&id=2f96c05e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751015931917}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"transfer-container\" },\n    [\n      _c(\"Card\", { attrs: { \"dis-hover\": \"\" } }, [\n        _c(\"div\", { staticClass: \"page-header\" }, [\n          _c(\"div\", { staticClass: \"header-left\" }, [\n            _c(\"h2\", { staticClass: \"page-title\" }, [_vm._v(\"调拨管理\")]),\n            _c(\"p\", { staticClass: \"page-desc\" }, [\n              _vm._v(\"管理门店间商品调拨申请，支持审核、执行和统计分析\")\n            ])\n          ]),\n          _c(\n            \"div\",\n            { staticClass: \"header-right\" },\n            [\n              _c(\n                \"Button\",\n                {\n                  attrs: { type: \"warning\", icon: \"ios-warning\" },\n                  on: { click: _vm.showPendingOnly }\n                },\n                [_vm._v(\"待审核单据\")]\n              ),\n              _c(\n                \"Button\",\n                {\n                  staticStyle: { \"margin-left\": \"8px\" },\n                  attrs: { type: \"success\", icon: \"ios-download\" },\n                  on: { click: _vm.exportTransfer }\n                },\n                [_vm._v(\"导出数据\")]\n              ),\n              _c(\n                \"Button\",\n                {\n                  staticStyle: { \"margin-left\": \"8px\" },\n                  attrs: { type: \"primary\", icon: \"ios-stats\" },\n                  on: { click: _vm.showStatistics }\n                },\n                [_vm._v(\"统计分析\")]\n              ),\n              _c(\n                \"Button\",\n                {\n                  staticStyle: { \"margin-left\": \"8px\" },\n                  attrs: { type: \"primary\", icon: \"ios-add\" },\n                  on: { click: _vm.showAddDialog }\n                },\n                [_vm._v(\"添加调拨\")]\n              )\n            ],\n            1\n          )\n        ])\n      ]),\n      _c(\n        \"Card\",\n        { staticStyle: { \"margin-top\": \"16px\" }, attrs: { \"dis-hover\": \"\" } },\n        [\n          _c(\n            \"Form\",\n            { attrs: { model: _vm.filterForm, inline: \"\" } },\n            [\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"调拨单号：\" } },\n                [\n                  _c(\"Input\", {\n                    staticStyle: { width: \"200px\" },\n                    attrs: { placeholder: \"输入调拨单号\" },\n                    model: {\n                      value: _vm.filterForm.order_no,\n                      callback: function($$v) {\n                        _vm.$set(_vm.filterForm, \"order_no\", $$v)\n                      },\n                      expression: \"filterForm.order_no\"\n                    }\n                  })\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"调出门店：\" } },\n                [\n                  _c(\n                    \"Select\",\n                    {\n                      staticStyle: { width: \"200px\" },\n                      attrs: { placeholder: \"选择调出门店\", clearable: \"\" },\n                      model: {\n                        value: _vm.filterForm.from_store_id,\n                        callback: function($$v) {\n                          _vm.$set(_vm.filterForm, \"from_store_id\", $$v)\n                        },\n                        expression: \"filterForm.from_store_id\"\n                      }\n                    },\n                    [\n                      _c(\"Option\", { attrs: { label: \"全部门店\", value: \"\" } }),\n                      _vm._l(_vm.storeList, function(store) {\n                        return _c(\"Option\", {\n                          key: store.value || store.id,\n                          attrs: {\n                            label: store.label || store.name,\n                            value: store.value || store.id\n                          }\n                        })\n                      })\n                    ],\n                    2\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"调入门店：\" } },\n                [\n                  _c(\n                    \"Select\",\n                    {\n                      staticStyle: { width: \"200px\" },\n                      attrs: { placeholder: \"选择调入门店\", clearable: \"\" },\n                      model: {\n                        value: _vm.filterForm.to_store_id,\n                        callback: function($$v) {\n                          _vm.$set(_vm.filterForm, \"to_store_id\", $$v)\n                        },\n                        expression: \"filterForm.to_store_id\"\n                      }\n                    },\n                    [\n                      _c(\"Option\", { attrs: { label: \"全部门店\", value: \"\" } }),\n                      _vm._l(_vm.storeList, function(store) {\n                        return _c(\"Option\", {\n                          key: store.value || store.id,\n                          attrs: {\n                            label: store.label || store.name,\n                            value: store.value || store.id\n                          }\n                        })\n                      })\n                    ],\n                    2\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"单据状态：\" } },\n                [\n                  _c(\n                    \"Select\",\n                    {\n                      staticStyle: { width: \"150px\" },\n                      attrs: { placeholder: \"单据状态\", clearable: \"\" },\n                      model: {\n                        value: _vm.filterForm.status,\n                        callback: function($$v) {\n                          _vm.$set(_vm.filterForm, \"status\", $$v)\n                        },\n                        expression: \"filterForm.status\"\n                      }\n                    },\n                    [\n                      _c(\"Option\", { attrs: { label: \"全部\", value: \"\" } }),\n                      _c(\"Option\", { attrs: { label: \"待审核\", value: \"0\" } }),\n                      _c(\"Option\", { attrs: { label: \"已通过\", value: \"1\" } }),\n                      _c(\"Option\", { attrs: { label: \"已完成\", value: \"2\" } }),\n                      _c(\"Option\", { attrs: { label: \"已拒绝\", value: \"3\" } }),\n                      _c(\"Option\", { attrs: { label: \"已撤销\", value: \"4\" } })\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"申请日期：\" } },\n                [\n                  _c(\"DatePicker\", {\n                    staticStyle: { width: \"240px\" },\n                    attrs: { type: \"daterange\", placeholder: \"选择日期范围\" },\n                    model: {\n                      value: _vm.filterForm.date_range,\n                      callback: function($$v) {\n                        _vm.$set(_vm.filterForm, \"date_range\", $$v)\n                      },\n                      expression: \"filterForm.date_range\"\n                    }\n                  })\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                [\n                  _c(\n                    \"Button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: { click: _vm.getTransferList }\n                    },\n                    [_vm._v(\"查询\")]\n                  ),\n                  _c(\n                    \"Button\",\n                    {\n                      staticStyle: { \"margin-left\": \"8px\" },\n                      on: { click: _vm.resetFilter }\n                    },\n                    [_vm._v(\"重置\")]\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"Row\",\n        { staticStyle: { \"margin-top\": \"16px\" }, attrs: { gutter: 16 } },\n        [\n          _c(\n            \"Col\",\n            { attrs: { span: \"6\" } },\n            [\n              _c(\"Card\", [\n                _c(\"div\", { staticClass: \"stat-card\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon total\" },\n                    [\n                      _c(\"Icon\", {\n                        attrs: { type: \"ios-document\", size: \"24\" }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-content\" }, [\n                    _c(\"div\", { staticClass: \"stat-number\" }, [\n                      _vm._v(_vm._s(_vm.statistics.total_orders))\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-label\" }, [\n                      _vm._v(\"总调拨单\")\n                    ])\n                  ])\n                ])\n              ])\n            ],\n            1\n          ),\n          _c(\n            \"Col\",\n            { attrs: { span: \"6\" } },\n            [\n              _c(\"Card\", [\n                _c(\"div\", { staticClass: \"stat-card\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon pending\" },\n                    [_c(\"Icon\", { attrs: { type: \"ios-time\", size: \"24\" } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-content\" }, [\n                    _c(\"div\", { staticClass: \"stat-number\" }, [\n                      _vm._v(_vm._s(_vm.statistics.pending_orders))\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"待审核\")])\n                  ])\n                ])\n              ])\n            ],\n            1\n          ),\n          _c(\n            \"Col\",\n            { attrs: { span: \"6\" } },\n            [\n              _c(\"Card\", [\n                _c(\"div\", { staticClass: \"stat-card\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon approved\" },\n                    [\n                      _c(\"Icon\", {\n                        attrs: { type: \"ios-checkmark\", size: \"24\" }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-content\" }, [\n                    _c(\"div\", { staticClass: \"stat-number\" }, [\n                      _vm._v(_vm._s(_vm.statistics.approved_orders))\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"已通过\")])\n                  ])\n                ])\n              ])\n            ],\n            1\n          ),\n          _c(\n            \"Col\",\n            { attrs: { span: \"6\" } },\n            [\n              _c(\"Card\", [\n                _c(\"div\", { staticClass: \"stat-card\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon completed\" },\n                    [\n                      _c(\"Icon\", {\n                        attrs: { type: \"ios-checkmark-circle\", size: \"24\" }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-content\" }, [\n                    _c(\"div\", { staticClass: \"stat-number\" }, [\n                      _vm._v(_vm._s(_vm.statistics.completed_orders))\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"已完成\")])\n                  ])\n                ])\n              ])\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"Card\",\n        { staticStyle: { \"margin-top\": \"16px\" }, attrs: { \"dis-hover\": \"\" } },\n        [\n          _c(\n            \"Table\",\n            {\n              attrs: {\n                data: _vm.transferList,\n                loading: _vm.tableLoading,\n                stripe: \"\",\n                border: \"\"\n              },\n              on: { \"on-selection-change\": _vm.handleSelectionChange }\n            },\n            [\n              _c(\"Column\", {\n                attrs: { type: \"selection\", width: \"60\", align: \"center\" }\n              }),\n              _c(\"Column\", {\n                attrs: {\n                  prop: \"order_no\",\n                  label: \"调拨单号\",\n                  width: \"160\",\n                  align: \"center\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(ref) {\n                      var row = ref.row\n                      return [\n                        _c(\n                          \"a\",\n                          {\n                            staticStyle: { color: \"#2d8cf0\" },\n                            attrs: { href: \"javascript:;\" },\n                            on: {\n                              click: function($event) {\n                                return _vm.viewDetail(row)\n                              }\n                            }\n                          },\n                          [_vm._v(_vm._s(row.order_no))]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"Column\", {\n                attrs: { label: \"商品信息\", \"min-width\": \"250\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(ref) {\n                      var row = ref.row\n                      return [\n                        _c(\"div\", { staticClass: \"product-info\" }, [\n                          _c(\"img\", {\n                            staticStyle: {\n                              width: \"40px\",\n                              height: \"40px\",\n                              \"border-radius\": \"4px\",\n                              \"margin-right\": \"12px\",\n                              \"object-fit\": \"cover\"\n                            },\n                            attrs: {\n                              src:\n                                row.product_image ||\n                                \"/static/images/default-product.png\",\n                              alt: \"商品图片\"\n                            }\n                          }),\n                          _c(\"div\", [\n                            _c(\"div\", { staticClass: \"product-name\" }, [\n                              _vm._v(_vm._s(row.product_name || \"未知商品\"))\n                            ]),\n                            row.sku\n                              ? _c(\"div\", { staticClass: \"product-spec\" }, [\n                                  _vm._v(_vm._s(row.sku))\n                                ])\n                              : _vm._e(),\n                            _c(\"div\", { staticClass: \"transfer-qty\" }, [\n                              _vm._v(\n                                \"调拨数量：\" + _vm._s(row.transfer_qty || 0)\n                              )\n                            ])\n                          ])\n                        ])\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"Column\", {\n                attrs: { label: \"调拨门店\", width: \"200\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(ref) {\n                      var row = ref.row\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"transfer-stores\" },\n                          [\n                            _c(\"div\", { staticClass: \"from-store\" }, [\n                              _vm._v(_vm._s(row.from_store_name || \"未知门店\"))\n                            ]),\n                            _c(\"Icon\", {\n                              staticStyle: { margin: \"0 8px\" },\n                              attrs: { type: \"ios-arrow-forward\" }\n                            }),\n                            _c(\"div\", { staticClass: \"to-store\" }, [\n                              _vm._v(_vm._s(row.to_store_name || \"未知门店\"))\n                            ])\n                          ],\n                          1\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"Column\", {\n                attrs: { label: \"单据状态\", width: \"100\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(ref) {\n                      var row = ref.row\n                      return [\n                        _c(\n                          \"Tag\",\n                          {\n                            attrs: { color: _vm.getStatusTagColor(row.status) }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(_vm.getStatusText(row.status)) +\n                                \"\\n          \"\n                            )\n                          ]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"Column\", {\n                attrs: {\n                  prop: \"applicant\",\n                  label: \"申请人\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"Column\", {\n                attrs: {\n                  prop: \"apply_time_text\",\n                  label: \"申请时间\",\n                  width: \"160\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"Column\", {\n                attrs: { label: \"审核时间\", width: \"160\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(ref) {\n                      var row = ref.row\n                      return [\n                        row.approve_time_text\n                          ? _c(\"span\", [_vm._v(_vm._s(row.approve_time_text))])\n                          : _c(\"span\", { staticStyle: { color: \"#c5c8ce\" } }, [\n                              _vm._v(\"-\")\n                            ])\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"Column\", {\n                attrs: {\n                  label: \"操作\",\n                  width: \"250\",\n                  align: \"center\",\n                  fixed: \"right\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(ref) {\n                      var row = ref.row\n                      return [\n                        _c(\n                          \"Button\",\n                          {\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function($event) {\n                                return _vm.viewDetail(row)\n                              }\n                            }\n                          },\n                          [_vm._v(\"查看详情\")]\n                        ),\n                        row.status === 0\n                          ? [\n                              _c(\n                                \"Button\",\n                                {\n                                  staticStyle: { color: \"#19be6b\" },\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.approveTransfer(row, true)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"通过\")]\n                              ),\n                              _c(\n                                \"Button\",\n                                {\n                                  staticStyle: { color: \"#ed4014\" },\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.approveTransfer(row, false)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"拒绝\")]\n                              )\n                            ]\n                          : _vm._e(),\n                        row.status === 1\n                          ? [\n                              _c(\n                                \"Button\",\n                                {\n                                  staticStyle: { color: \"#2d8cf0\" },\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.executeTransfer(row)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"执行调拨\")]\n                              ),\n                              _c(\n                                \"Button\",\n                                {\n                                  staticStyle: { color: \"#ff9900\" },\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.forceExecute(row)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"强制执行\")]\n                              )\n                            ]\n                          : _vm._e(),\n                        row.status === 3 || row.status === 4\n                          ? [\n                              _c(\n                                \"Button\",\n                                {\n                                  staticStyle: { color: \"#ed4014\" },\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.deleteTransfer(row)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"删除\")]\n                              )\n                            ]\n                          : _vm._e()\n                      ]\n                    }\n                  }\n                ])\n              })\n            ],\n            1\n          ),\n          _vm.selectedRows.length > 0\n            ? _c(\n                \"div\",\n                {\n                  staticClass: \"batch-actions\",\n                  staticStyle: { \"margin-top\": \"16px\" }\n                },\n                [\n                  _c(\n                    \"span\",\n                    {\n                      staticClass: \"selected-count\",\n                      staticStyle: { \"margin-right\": \"16px\", color: \"#515a6e\" }\n                    },\n                    [\n                      _vm._v(\n                        \"已选择 \" + _vm._s(_vm.selectedRows.length) + \" 项\"\n                      )\n                    ]\n                  ),\n                  _c(\n                    \"Button\",\n                    {\n                      attrs: { size: \"small\", type: \"success\" },\n                      on: {\n                        click: function($event) {\n                          return _vm.batchApprove(true)\n                        }\n                      }\n                    },\n                    [_vm._v(\"批量通过\")]\n                  ),\n                  _c(\n                    \"Button\",\n                    {\n                      staticStyle: { \"margin-left\": \"8px\" },\n                      attrs: { size: \"small\", type: \"error\" },\n                      on: {\n                        click: function($event) {\n                          return _vm.batchApprove(false)\n                        }\n                      }\n                    },\n                    [_vm._v(\"批量拒绝\")]\n                  ),\n                  _c(\n                    \"Button\",\n                    {\n                      staticStyle: { \"margin-left\": \"8px\" },\n                      attrs: { size: \"small\", type: \"primary\" },\n                      on: { click: _vm.batchExecute }\n                    },\n                    [_vm._v(\"批量执行\")]\n                  ),\n                  _c(\n                    \"Button\",\n                    {\n                      staticStyle: { \"margin-left\": \"8px\" },\n                      attrs: { size: \"small\" },\n                      on: { click: _vm.clearSelection }\n                    },\n                    [_vm._v(\"取消选择\")]\n                  )\n                ],\n                1\n              )\n            : _vm._e(),\n          _c(\n            \"div\",\n            {\n              staticClass: \"pagination-container\",\n              staticStyle: { \"margin-top\": \"16px\", \"text-align\": \"right\" }\n            },\n            [\n              _c(\"Page\", {\n                attrs: {\n                  current: _vm.currentPage,\n                  \"page-size\": _vm.pageSize,\n                  total: _vm.total,\n                  \"show-sizer\": \"\",\n                  \"show-elevator\": \"\",\n                  \"show-total\": \"\"\n                },\n                on: {\n                  \"on-change\": _vm.handleCurrentChange,\n                  \"on-page-size-change\": _vm.handleSizeChange\n                }\n              })\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"Modal\",\n        {\n          attrs: { title: \"调拨详情\", width: \"700\" },\n          model: {\n            value: _vm.detailDialogVisible,\n            callback: function($$v) {\n              _vm.detailDialogVisible = $$v\n            },\n            expression: \"detailDialogVisible\"\n          }\n        },\n        [\n          _vm.currentDetail\n            ? _c(\"div\", { staticClass: \"detail-content\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"detail-section\" },\n                  [\n                    _c(\"h4\", [_vm._v(\"基本信息\")]),\n                    _c(\n                      \"Row\",\n                      { attrs: { gutter: 20 } },\n                      [\n                        _c(\"Col\", { attrs: { span: \"12\" } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"调拨单号：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.order_no))\n                            ])\n                          ])\n                        ]),\n                        _c(\"Col\", { attrs: { span: \"12\" } }, [\n                          _c(\n                            \"div\",\n                            { staticClass: \"detail-item\" },\n                            [\n                              _c(\"span\", { staticClass: \"label\" }, [\n                                _vm._v(\"单据状态：\")\n                              ]),\n                              _c(\n                                \"Tag\",\n                                {\n                                  attrs: {\n                                    color: _vm.getStatusTagColor(\n                                      _vm.currentDetail.status\n                                    )\n                                  }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                \" +\n                                      _vm._s(\n                                        _vm.getStatusText(\n                                          _vm.currentDetail.status\n                                        )\n                                      ) +\n                                      \"\\n              \"\n                                  )\n                                ]\n                              )\n                            ],\n                            1\n                          )\n                        ]),\n                        _c(\"Col\", { attrs: { span: \"12\" } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"申请人：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.applicant))\n                            ])\n                          ])\n                        ]),\n                        _c(\"Col\", { attrs: { span: \"12\" } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"申请时间：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.apply_time_text))\n                            ])\n                          ])\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\"div\", { staticClass: \"detail-section\" }, [\n                  _c(\"h4\", [_vm._v(\"商品信息\")]),\n                  _c(\"div\", { staticClass: \"product-detail\" }, [\n                    _c(\"img\", {\n                      staticStyle: {\n                        width: \"80px\",\n                        height: \"80px\",\n                        \"border-radius\": \"8px\",\n                        \"margin-right\": \"16px\",\n                        \"object-fit\": \"cover\"\n                      },\n                      attrs: {\n                        src:\n                          _vm.currentDetail.product_image ||\n                          \"/static/images/default-product.png\",\n                        alt: \"商品图片\"\n                      }\n                    }),\n                    _c(\"div\", [\n                      _c(\"div\", { staticClass: \"product-name\" }, [\n                        _vm._v(_vm._s(_vm.currentDetail.product_name))\n                      ]),\n                      _vm.currentDetail.sku\n                        ? _c(\"div\", { staticClass: \"product-spec\" }, [\n                            _vm._v(\"规格：\" + _vm._s(_vm.currentDetail.sku))\n                          ])\n                        : _vm._e(),\n                      _c(\"div\", { staticClass: \"transfer-qty\" }, [\n                        _vm._v(\n                          \"调拨数量：\" + _vm._s(_vm.currentDetail.transfer_qty)\n                        )\n                      ])\n                    ])\n                  ])\n                ]),\n                _c(\n                  \"div\",\n                  { staticClass: \"detail-section\" },\n                  [\n                    _c(\"h4\", [_vm._v(\"调拨信息\")]),\n                    _c(\n                      \"Row\",\n                      { attrs: { gutter: 20 } },\n                      [\n                        _c(\"Col\", { attrs: { span: \"12\" } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"调出门店：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.from_store_name))\n                            ])\n                          ])\n                        ]),\n                        _c(\"Col\", { attrs: { span: \"12\" } }, [\n                          _c(\"div\", { staticClass: \"detail-item\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"调入门店：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"value\" }, [\n                              _vm._v(_vm._s(_vm.currentDetail.to_store_name))\n                            ])\n                          ])\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _vm.currentDetail.remark\n                  ? _c(\"div\", { staticClass: \"detail-section\" }, [\n                      _c(\"h4\", [_vm._v(\"申请原因\")]),\n                      _c(\"div\", { staticClass: \"remark-content\" }, [\n                        _vm._v(_vm._s(_vm.currentDetail.remark))\n                      ])\n                    ])\n                  : _vm._e(),\n                _vm.currentDetail.approve_remark\n                  ? _c(\"div\", { staticClass: \"detail-section\" }, [\n                      _c(\"h4\", [_vm._v(\"审核备注\")]),\n                      _c(\"div\", { staticClass: \"remark-content\" }, [\n                        _vm._v(_vm._s(_vm.currentDetail.approve_remark))\n                      ])\n                    ])\n                  : _vm._e()\n              ])\n            : _vm._e()\n        ]\n      ),\n      _c(\n        \"Modal\",\n        {\n          attrs: {\n            title: _vm.approveType ? \"审核通过\" : \"审核拒绝\",\n            width: \"500\"\n          },\n          model: {\n            value: _vm.approveDialogVisible,\n            callback: function($$v) {\n              _vm.approveDialogVisible = $$v\n            },\n            expression: \"approveDialogVisible\"\n          }\n        },\n        [\n          _c(\n            \"Form\",\n            {\n              ref: \"approveForm\",\n              attrs: {\n                model: _vm.approveForm,\n                rules: _vm.approveRules,\n                \"label-width\": 100\n              }\n            },\n            [\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"审核备注：\", prop: \"remark\" } },\n                [\n                  _c(\"Input\", {\n                    attrs: {\n                      type: \"textarea\",\n                      placeholder: _vm.approveType\n                        ? \"请填写通过原因（可选）\"\n                        : \"请填写拒绝原因\",\n                      rows: 4\n                    },\n                    model: {\n                      value: _vm.approveForm.remark,\n                      callback: function($$v) {\n                        _vm.$set(_vm.approveForm, \"remark\", $$v)\n                      },\n                      expression: \"approveForm.remark\"\n                    }\n                  })\n                ],\n                1\n              )\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\"\n            },\n            [\n              _c(\n                \"Button\",\n                {\n                  on: {\n                    click: function($event) {\n                      _vm.approveDialogVisible = false\n                    }\n                  }\n                },\n                [_vm._v(\"取消\")]\n              ),\n              _c(\n                \"Button\",\n                {\n                  staticStyle: { \"margin-left\": \"8px\" },\n                  attrs: {\n                    type: _vm.approveType ? \"success\" : \"error\",\n                    loading: _vm.approveLoading\n                  },\n                  on: { click: _vm.submitApprove }\n                },\n                [\n                  _vm._v(\n                    \"\\n        \" +\n                      _vm._s(_vm.approveType ? \"确认通过\" : \"确认拒绝\") +\n                      \"\\n      \"\n                  )\n                ]\n              )\n            ],\n            1\n          )\n        ],\n        1\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}