{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\groupTemplateInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\groupTemplateInfo.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport cardsData from \"@/components/cards/cards\";\nimport {\n  workGroupChat,\n  groupChatList,\n  workGroupTemplateChatInfo,\n  groupChatOwnerList,\n  workGroupTemplateSendMsg,\n  groupOwnerChatList,\n} from \"@/api/work\";\nimport Setting from \"@/setting\";\nexport default {\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      formItem: {},\n      modals: false,\n      labelList: [],\n      cardLists: [],\n      tabList3: [],\n      optionData: {},\n      style: { height: \"400px\" },\n      spinShow: false,\n      tableColumn2: [\n        {\n          title: \"群聊名称\",\n          key: \"name\",\n          minWidth: 100,\n        },\n        {\n          title: \"群主\",\n          slot: \"ownerInfo\",\n          minWidth: 100,\n        },\n        {\n          title: \"群聊成员数量\",\n          key: \"member_num\",\n          minWidth: 100,\n        },\n        {\n          title: \"消息送达状态\",\n          slot: \"status\",\n          minWidth: 100,\n        },\n        {\n          title: \"群聊创建时间\",\n          key: \"group_create_time\",\n          minWidth: 100,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          // fixed: \"right\",\n          minWidth: 170,\n        },\n      ],\n      tableColumn1: [\n        {\n          title: \"群主\",\n          slot: \"ownerInfo\",\n          minWidth: 100,\n        },\n        {\n          title: \"群发发送状态\",\n          slot: \"status\",\n          minWidth: 100,\n        },\n        {\n          title: \"本次群发群聊总数\",\n          key: \"member_num\",\n          minWidth: 100,\n        },\n        {\n          title: \"已发送群聊数\",\n          key: \"status\",\n          minWidth: 100,\n        },\n        {\n          title: \"确认发送时间\",\n          key: \"create_time\",\n          minWidth: 100,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          // fixed: \"right\",\n          minWidth: 170,\n        },\n      ],\n      columns2: [\n        {\n          title: \"群聊名称\",\n          key: \"name\",\n          minWidth: 150,\n        },\n        {\n          title: \"群人数\",\n          key: \"member_num\",\n        },\n        {\n          title: \"送达状态\",\n          slot: \"status\",\n        },\n      ],\n      selectGroup: {},\n      tabIndex: 0,\n      ownerData: [],\n      tableData: [],\n      ownerForm: {\n        page: 1,\n        limit: 15,\n      },\n      userLoading: false,\n      timeVal: [],\n      tableForm: {\n        id: \"\",\n        page: 1,\n        limit: 15,\n        status: \"\",\n        name:\"\",\n        owner:null\n      },\n      tableForm2: {\n        page: 1,\n        limit: 15,\n        status: \"\",\n      },\n    };\n  },\n  components: { cardsData },\n  mounted() {\n    this.tableForm.id = this.$route.params.id;\n    this.getData();\n    this.getWorkGroupChat();\n    this.getgroupChatList();\n  },\n  methods: {\n    getData() {\n      workGroupTemplateChatInfo(this.$route.params.id).then((res) => {\n        this.cardLists = [\n          {\n            col: 6,\n            count: res.data.user_count,\n            name: \"已发群主\",\n            type: 1,\n            className: \"iconjinrixinzeng\",\n          },\n          {\n            col: 6,\n            count: res.data.unuser_count,\n            name: \"未发送群主\",\n            type: 1,\n            className: \"iconjinrituiqun\",\n          },\n          {\n            col: 6,\n            count: res.data.external_user_count,\n            name: \"已送达群聊\",\n            type: 1,\n            className: \"icondangqianqunchengyuan\",\n          },\n          {\n            col: 6,\n            count: res.data.external_unuser_count,\n            name: \"未送达群聊\",\n            type: 1,\n            className: \"iconleijituiqun\",\n          },\n        ];\n      });\n    },\n    onChangeType() {\n      if (this.tabIndex == 0) {\n        this.getgroupChatList();\n      } else {\n        this.getgroupChatOwnerList();\n      }\n    },\n    search() {\n      if (this.tabIndex == 0) {\n        this.getgroupChatList();\n      } else {\n        this.getgroupChatOwnerList();\n      }\n    },\n    search1() {\n      this.tableForm2.page = 1;\n      this.detailsInfo(this.selectGroup);\n    },\n    pageChange(index) {\n      this.tableForm.page = index;\n      if (this.tabIndex == 0) {\n        this.getgroupChatList();\n      } else {\n        this.getgroupChatOwnerList();\n      }\n    },\n    //获取群主\n    getWorkGroupChat() {\n      workGroupChat({})\n        .then((res) => {\n          this.ownerData = res.data.list;\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    // 客户群接收详情\n    getgroupChatList() {\n      groupChatList(this.tableForm.id, this.tableForm).then((res) => {\n        this.tableData = res.data;\n      });\n    },\n    // 群主发送详情\n    getgroupChatOwnerList() {\n      groupChatOwnerList(this.tableForm.id, this.tableForm).then((res) => {\n        this.tableData = res.data;\n      });\n    },\n    sendMessage(row, index) {\n      workGroupTemplateSendMsg({\n        userid: row.owner,\n        time: row.create_time,\n        id: \"\",\n      })\n        .then((res) => {\n          this.$Message.success(res.msg);\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    // 群发详情\n    detailsInfo(row) {\n      this.modals = true;\n      this.selectGroup = row;\n      groupOwnerChatList({\n        chat_id : row.chat_ids,\n        status: this.tableForm2.status,\n        page: this.tableForm2.page,\n        limit: this.tableForm2.limit,\n      })\n        .then((res) => {\n          this.tabList3 = res.data;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    groupCount(row, index) {\n      this.$router.push(this.roterPre + \"/work/client/statistical/\" + row.id);\n    },\n    pageChange1(index) {\n      this.tableForm2.page = index;\n      this.detailsInfo(this.selectGroup);\n    },\n  },\n};\n", null]}