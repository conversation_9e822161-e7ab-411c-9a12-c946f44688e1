{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\addMoment.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\addMoment.vue", "mtime": 1710488036000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from \"vuex\";\nimport { workLabel, workMomentSave, workClientCount } from \"@/api/work\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport department from \"@/components/department/index.vue\";\nimport userLabel from \"@/components/labelList\";\nimport Setting from \"@/setting\";\nimport util from \"@/libs/util\";\nimport { uploadByPieces } from \"@/utils/upload\";\nexport default {\n  data: function data() {\n    var _ref;\n\n    return _ref = {\n      roterPre: Setting.roterPre,\n      formItem: {\n        name: \"\",\n        type: \"0\",\n        client_tag_list: [],\n        client_type: \"0\",\n        user_ids: [],\n        welcome_words: {\n          text: {\n            content: \"\"\n          },\n          attachments: []\n        },\n        // images:[],\n        send_type: \"0\",\n        send_time: \"\" //定时发送\n\n      },\n      descType: \"0\",\n      modalPic: false,\n      picTit: \"\",\n      tableIndex: 0,\n      isChoice: \"\",\n      //   labelList: [],\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '名称不能为空',\n          trigger: 'blur'\n        }],\n        send_time: [{\n          required: true,\n          message: '发送时间不能为空',\n          trigger: 'change'\n        }]\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      linkObj: {\n        msgtype: \"link\",\n        link: {\n          media_id: \"\",\n          title: \"\",\n          url: \"\"\n        }\n      },\n      videoObj: {\n        msgtype: \"video\",\n        video: {\n          media_id: \"\",\n          url: \"\"\n        }\n      },\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: [],\n      fileUrl2: Setting.apiBaseURL + \"/file/video_upload\",\n      upload_type: \"\",\n      //视频上传类型 1 本地上传 2 3 4 OSS上传\n      uploadData: {},\n      // 上传参数\n      header: {},\n      progress: 0\n    }, _defineProperty(_ref, \"upload_type\", true), _defineProperty(_ref, \"clientCount\", 0), _defineProperty(_ref, \"labelShow\", false), _defineProperty(_ref, \"dataLabel\", []), _ref;\n  },\n  components: {\n    uploadPictures: uploadPictures,\n    department: department,\n    userLabel: userLabel\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  watch: {\n    \"formItem.client_tag_list\": function formItemClient_tag_list(val, oldVal) {\n      if (val !== oldVal) {\n        this.getClientCount();\n      }\n    },\n    \"formItem.user_ids\": function formItemUser_ids(val, oldVal) {\n      if (val !== oldVal) {\n        this.getClientCount();\n      }\n    },\n    dataLabel: function dataLabel(val) {\n      this.formItem.client_tag_list = val.map(function (item) {\n        return item.tag_id;\n      });\n    }\n  },\n  mounted: function mounted() {\n    this.setCopyrightShow({\n      value: false\n    }); // this.getWorkLabel();\n\n    this.getToken();\n    this.getClientCount();\n  },\n  destroyed: function destroyed() {\n    this.setCopyrightShow({\n      value: true\n    });\n  },\n  methods: _objectSpread({}, mapMutations(\"admin/layout\", [\"setCopyrightShow\"]), {\n    delVideo: function delVideo() {\n      this.videoObj.video.url = '';\n      this.upload_type = true;\n    },\n    //获取客户标签\n    getWorkLabel: function getWorkLabel() {\n      var _this = this;\n\n      workLabel().then(function (res) {\n        _this.labelList = res.data.map(function (org) {\n          return _this.mapTree(org);\n        });\n      });\n    },\n    mapTree: function mapTree(org) {\n      var _this2 = this;\n\n      var haveChildren = Array.isArray(org.children) && org.children.length > 0;\n      return {\n        //分别将我们查询出来的值做出改变他的key\n        title: org.label,\n        expand: true,\n        value: org.value,\n        selected: false,\n        checked: false,\n        children: haveChildren ? org.children.map(function (i) {\n          return _this2.mapTree(i);\n        }) : []\n      };\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, picTit, index) {\n      this.modalPic = true;\n      this.isChoice = tit === \"dan\" ? \"单选\" : \"多选\";\n      this.picTit = picTit;\n      this.tableIndex = index;\n    },\n    snedChangeTime: function snedChangeTime(val) {\n      this.formItem.send_time = val;\n    },\n    handleRemove: function handleRemove(i) {\n      this.formItem.welcome_words.attachments.splice(i, 1);\n    },\n    // 获取多张图信息\n    getPicD: function getPicD(pc) {\n      var _this3 = this;\n\n      var images = [];\n      images = pc.map(function (item) {\n        return item.att_dir;\n      });\n      images.forEach(function (item) {\n        var imageObj = {\n          msgtype: \"image\",\n          image: {\n            media_id: \"\",\n            pic_url: \"\"\n          }\n        };\n        imageObj.image.pic_url = item;\n\n        _this3.formItem.welcome_words.attachments.push(imageObj);\n      });\n      this.modalPic = false;\n    },\n    addUser: function addUser() {\n      this.userList = this.formItem.user_ids;\n      this.$refs.department.memberStatus = true;\n    },\n    changeMastart: function changeMastart(arr, type) {\n      this.formItem.user_ids = arr.map(function (item) {\n        return {\n          userid: item.userid,\n          name: item.name,\n          id: item.id\n        };\n      });\n    },\n    //tag标签删除成员\n    handleDel: function handleDel(id) {\n      var index = this.formItem.user_ids.findIndex(function (val) {\n        return val.id === id;\n      });\n      this.formItem.user_ids.splice(index, 1);\n      this.getClientCount();\n    },\n    linkBlur: function linkBlur() {\n      if (!this.linkObj.link.url) return this.$Message.warning(\"请输入链接\");\n    },\n    titleBlur: function titleBlur() {\n      if (!this.linkObj.link.title) return this.$Message.warning(\"请输入链接标题\");\n    },\n    submit: function submit() {\n      var _this4 = this;\n\n      if (!this.formItem.welcome_words.text.content.length) return this.$Message.error(\"请填写消息内容1\");\n\n      if (this.descType == 2) {\n        this.formItem.welcome_words.attachments.push(this.linkObj);\n      } else if (this.descType == 1) {\n        this.formItem.welcome_words.attachments.push(this.videoObj);\n      }\n\n      var formData = this.deepClone(this.formItem);\n      formData.user_ids = formData.user_ids.map(function (item) {\n        return item.userid;\n      });\n\n      if (formData.client_type === '0') {\n        formData.client_tag_list = [];\n      }\n\n      this.$refs.formItem.validate(function (valid) {\n        if (valid) {\n          workMomentSave(formData).then(function (res) {\n            _this4.$Message.success(res.msg);\n\n            _this4.$router.push(_this4.roterPre + \"/work/client/moment\");\n          }).catch(function (err) {\n            _this4.$Message.error(err.msg);\n          });\n        }\n      });\n    },\n    //深克隆\n    deepClone: function deepClone(obj) {\n      var newObj = Array.isArray(obj) ? [] : {};\n\n      if (obj && _typeof(obj) === \"object\") {\n        for (var key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            newObj[key] = obj && _typeof(obj[key]) === \"object\" ? this.deepClone(obj[key]) : obj[key];\n          }\n        }\n      }\n\n      return newObj;\n    },\n    videoSaveToUrl: function videoSaveToUrl(file) {\n      var _this5 = this;\n\n      var imgTypeArr = [\"video/mp4\"];\n      var imgType = imgTypeArr.indexOf(file.type) !== -1;\n\n      if (!imgType) {\n        return this.$Message.warning({\n          content: '文件  ' + file.name + '  格式不正确, 请选择格式正确的视频',\n          duration: 5\n        });\n      }\n\n      uploadByPieces({\n        randoms: \"\",\n        // 随机数，这里作为给后端处理分片的标识 根据项目看情况 是否要加\n        file: file,\n        // 视频实体\n        pieceSize: 3,\n        // 分片大小\n        success: function success(data) {\n          _this5.upload_type = false;\n          _this5.videoObj.video.url = data.file_path;\n\n          _this5.$Spin.hide();\n        },\n        error: function error(e) {\n          _this5.$Message.error(e.msg);\n\n          _this5.$Spin.hide();\n        },\n        uploading: function uploading(chunk, allChunk) {\n          _this5.$Spin.show();\n        }\n      });\n      return false;\n    },\n    // 上传头部token\n    getToken: function getToken() {\n      this.header[\"Authori-zation\"] = \"Bearer \" + util.cookies.get(\"token\");\n    },\n    getClientCount: function getClientCount() {\n      var _this6 = this;\n\n      workClientCount({\n        is_all: this.formItem.client_type == 1 ? 0 : 1,\n        label: this.formItem.client_tag_list,\n        userid: this.formItem.user_ids.map(function (item) {\n          return item.userid;\n        })\n      }).then(function (res) {\n        _this6.clientCount = res.data.sum_count;\n      });\n    },\n    openLabel: function openLabel() {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    activeData: function activeData(dataLabel) {\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    // 标签弹窗关闭\n    labelClose: function labelClose() {\n      this.labelShow = false;\n    },\n    closeLabel: function closeLabel(label) {\n      var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.dataLabel.splice(index, 1);\n    }\n  })\n};", null]}