{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_title.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_title.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nexport default {\n  name: 'c_home_bargain',\n  componentsName: 'home_bargain',\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      rComContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleLeft'\n      }, {\n        components: toolCom.c_input_item,\n        configNme: 'titleConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'buttonConfig'\n      }],\n      oneContent: [{\n        components: toolCom.c_input_item,\n        configNme: 'titleConfigRight'\n      }, {\n        components: toolCom.c_input_item,\n        configNme: 'linkConfig'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'themeColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'fontSize'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'textStyle'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'buttonColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'buttonText'\n      }],\n      twoStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'themeColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'fontSize'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'textPosition'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'textStyle'\n      }],\n      currencyStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'titleColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'mbConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      setUp: 0,\n      type: 0\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          if (this.type == 0) {\n            var rCom = arr.concat(this.rComContent);\n            this.rCom = rCom.concat(this.oneContent);\n          } else {\n            this.rCom = arr.concat(this.rComContent);\n          }\n        } else {\n          if (this.type == 0) {\n            var _rCom = arr.concat(this.oneStyle);\n\n            this.rCom = _rCom.concat(this.currencyStyle);\n          } else {\n            var _rCom2 = arr.concat(this.twoStyle);\n\n            this.rCom = _rCom2.concat(this.currencyStyle);\n          }\n        }\n      },\n      deep: true\n    },\n    'configObj.buttonConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          if (nVal == 0) {\n            var rCom = arr.concat(this.oneStyle);\n            this.rCom = rCom.concat(this.currencyStyle);\n          } else {\n            var _rCom3 = arr.concat(this.twoStyle);\n\n            this.rCom = _rCom3.concat(this.currencyStyle);\n          }\n        } else {\n          if (nVal == 0) {\n            var _rCom4 = arr.concat(this.rComContent);\n\n            this.rCom = _rCom4.concat(this.oneContent);\n          } else {\n            this.rCom = arr.concat(this.rComContent);\n          }\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    // 获取组件参数\n    getConfig: function getConfig(data) {}\n  }\n};", null]}