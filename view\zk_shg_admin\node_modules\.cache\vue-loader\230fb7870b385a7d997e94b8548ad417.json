{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_checkbox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_checkbox.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    export default {\n        name: 'c_checkbox',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            }\n        },\n        data () {\n            return {\n                formData: {\n                    type: 0\n                },\n                defaults: {},\n                configData: {},\n\t\t\t\tselectedData:[],\n\t\t\t\tuserStyle:0\n            }\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n\t\t\t\t\tthis.userStyle = nVal.styleConfig.tabVal\n\t\t\t\t\tthis.selectedData = nVal.checkboxInfo.type\n                },\n                deep: true\n            },\n\t\t\t'configObj.styleConfig.tabVal':{\n\t\t\t\thandler(nVal, oVal){\n\t\t\t\t\tif(this.configData.userType){\n\t\t\t\t\t\tthis.configData.type = [3,1,2]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj;\n                this.configData = this.configObj[this.configNme];\n\t\t\t\tthis.userStyle = this.defaults.styleConfig.tabVal\n\t\t\t\tthis.selectedData = this.defaults.checkboxInfo.type\n            })\n        },\n        methods: {\n            checkboxChange (e) {\n                // this.$emit('getConfig', e);\n            }\n        }\n    }\n", null]}