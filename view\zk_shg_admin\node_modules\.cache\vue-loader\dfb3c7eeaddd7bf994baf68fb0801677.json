{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue?vue&type=template&id=5906bf54&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\",\"padding\":0}},[_c('div',{staticClass:\"new_card_pd\"},[_c('Form',{ref:\"levelFrom\",attrs:{\"model\":_vm.levelFrom,\"inline\":\"\",\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"状态：\",\"label-for\":\"status1\"}},[_c('Select',{staticStyle:{\"width\":\"250px\"},attrs:{\"placeholder\":\"请选择\",\"clearable\":\"\",\"element-id\":\"status1\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.levelFrom.is_show),callback:function ($$v) {_vm.$set(_vm.levelFrom, \"is_show\", $$v)},expression:\"levelFrom.is_show\"}},[_c('Option',{attrs:{\"value\":\"1\"}},[_vm._v(\"显示\")]),_c('Option',{attrs:{\"value\":\"0\"}},[_vm._v(\"不显示\")])],1)],1),_c('FormItem',{attrs:{\"label\":\"等级名称：\",\"label-for\":\"title\"}},[_c('Input',{staticStyle:{\"width\":\"250px\",\"margin-right\":\"14px\"},attrs:{\"placeholder\":\"请输入等级名称\"},model:{value:(_vm.levelFrom.title),callback:function ($$v) {_vm.$set(_vm.levelFrom, \"title\", $$v)},expression:\"levelFrom.title\"}}),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.userSearchs}},[_vm._v(\"查询\")])],1)],1)],1)]),_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Button',{directives:[{name:\"auth\",rawName:\"v-auth\",value:(['admin-user-level_add']),expression:\"['admin-user-level_add']\"}],attrs:{\"type\":\"primary\"},on:{\"click\":_vm.add}},[_vm._v(\"添加会员等级\")]),_c('Table',{ref:\"table\",staticClass:\"mt25\",attrs:{\"columns\":_vm.columns1,\"data\":_vm.levelLists,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"icons\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('viewer',[_c('div',{staticClass:\"tabBox_img\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.icon),expression:\"row.icon\"}]})])])]}},{key:\"is_forevers\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('i-switch',{attrs:{\"value\":row.is_forever,\"true-value\":1,\"false-value\":0,\"disabled\":true,\"size\":\"large\"},model:{value:(row.is_forever),callback:function ($$v) {_vm.$set(row, \"is_forever\", $$v)},expression:\"row.is_forever\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"永久\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"非永久\")])])]}},{key:\"is_pays\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('i-switch',{attrs:{\"value\":row.is_pay,\"true-value\":1,\"false-value\":0,\"disabled\":true,\"size\":\"large\"},model:{value:(row.is_pay),callback:function ($$v) {_vm.$set(row, \"is_pay\", $$v)},expression:\"row.is_pay\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"付费\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"免费\")])])]}},{key:\"is_shows\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('i-switch',{attrs:{\"value\":row.is_show,\"true-value\":1,\"false-value\":0,\"size\":\"large\"},on:{\"on-change\":function($event){return _vm.onchangeIsShow(row)}},model:{value:(row.is_show),callback:function ($$v) {_vm.$set(row, \"is_show\", $$v)},expression:\"row.is_show\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"显示\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"隐藏\")])])]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.edit(row)}}},[_vm._v(\"编辑\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":function($event){return _vm.changeMenu(row,2,index)}}},[_vm._v(\"删除\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"current\":_vm.levelFrom.page,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.levelFrom.limit},on:{\"on-change\":_vm.pageChange}})],1)],1),_c('task-list',{ref:\"tasks\"})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}