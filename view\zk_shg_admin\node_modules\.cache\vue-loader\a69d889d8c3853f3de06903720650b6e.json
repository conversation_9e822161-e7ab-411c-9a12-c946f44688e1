{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\attrList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\attrList.vue", "mtime": 1642123010000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    export default {\n        name: \"attrList\",\n        props:{\n\t\t\tattrs: {\n\t\t\t  type: Array,\n\t\t\t  default: function () {\n\t\t\t    return [];\n\t\t\t  }\n\t\t\t}\n        },\n        data(){\n            return {\n            }\n        },\n        mounted() {\n        },\n        methods:{\n\t\t\tselectAttr(label,index){\n\t\t\t\tlabel.select = !label.select;\n\t\t\t\tthis.$emit('activeData',JSON.parse(JSON.stringify(this.attrs)))\n\t\t\t},\n\t\t\tcancel(){\n\t\t\t\tthis.$emit('close')\n\t\t\t},\n\t\t\treset(){\n\t\t\t\tlet data = this.attrs;\n\t\t\t\tdata.map(el=>{\n\t\t\t\t\tel.details.map(label=>{\n\t\t\t\t\t\tlabel.select = false\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\tthis.attrs = data;\n\t\t\t},\n\t\t\tsubBtn(){\n\t\t\t\tthis.$emit('subAttrs')\n\t\t\t}\n        }\n    }\n", null]}