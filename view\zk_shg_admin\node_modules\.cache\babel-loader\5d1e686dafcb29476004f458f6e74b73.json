{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\orderlistDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\orderlistDetails.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport cardsData from '../../../components/cards/cards';\nimport tableForm from './components/tableFrom';\nimport tableList from './components/tableList';\nexport default {\n  name: 'orderlistDetails',\n  components: {\n    tableForm: tableForm,\n    tableList: tableList,\n    cardsData: cardsData\n  },\n  data: function data() {\n    return {\n      currentTab: '',\n      cardLists: [],\n      selection: [],\n      orderData: {\n        status: '',\n        data: '',\n        real_name: '',\n        field_key: 'all',\n        pay_type: ''\n      },\n      // display: 'none',\n      autoDisabled: true,\n      isAll: -1\n    };\n  },\n  methods: {\n    changeGetTabs: function changeGetTabs() {\n      this.$parent.getTabs();\n    },\n    // tab xuanxiang dezhi\n    getChangeTabs: function getChangeTabs(tab) {\n      this.$refs.table.getList();\n    },\n    // 列表数据\n    getData: function getData(res) {\n      if (this.$refs.table) {\n        this.$refs.table.checkBox = false;\n        this.$refs.table.getList(res);\n      }\n    },\n    // 模块数据\n    getCards: function getCards(list) {\n      this.cardLists = list;\n    },\n    handleResize: function handleResize() {\n      this.$refs.ellipsis.forEach(function (item) {\n        return item.init();\n      });\n    },\n    orderSelect: function orderSelect(selection) {\n      this.selection = selection;\n    },\n    onOrderData: function onOrderData(e) {\n      this.orderData = e;\n    },\n    orderDatas: function orderDatas(e) {\n      this.orderData = e;\n    },\n    onAutoDisabled: function onAutoDisabled(e) {\n      this.autoDisabled = e ? true : false;\n    },\n    onAll: function onAll(e) {\n      this.isAll = e;\n    }\n  },\n  mounted: function mounted() {}\n};", null]}