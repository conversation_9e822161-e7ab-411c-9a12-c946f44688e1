{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751016248065}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getSalesStatistics, getStoreOptions, exportSalesStatistics } from '@/api/erp';\nimport exportExcel from '@/utils/newToExcel.js';\n\nexport default {\n  name: 'SalesStatistics',\n  data() {\n    return {\n      // 筛选表单\n      filterForm: {\n        store_id: '',\n        keyword: '',\n        start_date: '',\n        end_date: ''\n      },\n      // 日期范围\n      dateRange: [],\n      // 列表数据\n      storeList: [],\n      columns: [],\n      tableData: [],\n      // 分页\n      currentPage: 1,\n      pageSize: 20,\n      total: 0,\n      // 状态\n      tableLoading: false\n    };\n  },\n  mounted() {\n    this.initData();\n  },\n  methods: {\n    // 初始化数据\n    async initData() {\n      await this.fetchStoreOptions();\n      await this.getSalesStatistics();\n    },\n    // 获取门店选项\n    async fetchStoreOptions() {\n      try {\n        const { data } = await getStoreOptions();\n        this.storeList = data || [];\n      } catch (error) {\n        console.error('获取门店列表失败:', error);\n      }\n    },\n    // 获取销售统计数据\n    async getSalesStatistics() {\n      this.tableLoading = true;\n      try {\n        const params = {\n          ...this.filterForm,\n          page: this.currentPage,\n          limit: this.pageSize\n        };\n        const { data } = await getSalesStatistics(params);\n        this.tableData = data.data;\n        this.columns = data.columns;\n        this.total = data.total;\n      } catch (error) {\n        console.error('获取销售统计失败:', error);\n        this.$Message.error('获取销售统计失败');\n      } finally {\n        this.tableLoading = false;\n      }\n    },\n    // 处理日期变化\n    handleDateChange(value) {\n      if (value && value.length === 2) {\n        this.filterForm.start_date = value[0];\n        this.filterForm.end_date = value[1];\n      } else {\n        this.filterForm.start_date = '';\n        this.filterForm.end_date = '';\n      }\n    },\n    // 重置筛选\n    resetFilter() {\n      this.filterForm = {\n        store_id: '',\n        keyword: '',\n        start_date: '',\n        end_date: ''\n      };\n      this.dateRange = [];\n      this.currentPage = 1;\n      this.getSalesStatistics();\n    },\n    // 分页变化\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      this.getSalesStatistics();\n    },\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n      this.getSalesStatistics();\n    },\n    // 导出销售数据\n    async exportSales() {\n      this.exportListOn = this.exportList.findIndex(\n        (item) => item.name === value\n      );\n      let [th, filekey, data, fileName] = [[], [], [], ''];\n      const excelData = {\n        ...this.where,\n        page: 1,\n        export_type: value,\n        ids: this.checkUidList.join()\n      };\n      for (let i = 0; i < excelData.page; i++) {\n        const lebData = await this.downOrderData(excelData);\n        if (!lebData.export.length) {\n          break;\n        }\n        if (!fileName) {\n          fileName = lebData.filename;\n        }\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) {\n          th = lebData.header;\n        }\n        data = data.concat(lebData.export);\n        excelData.page++;\n      }\n      const sheetData = [];\n      for (let j = 0; j < data.length; j++) {\n        const goodsList = data[j].goods_name.split('\\n');\n        for (let k = 0; k < goodsList.length; k++) {\n          const row = { ...data[j] };\n          row.goods_name = goodsList[k];\n          if (k) {\n            for (const key in row) {\n              if (Object.hasOwnProperty.call(row, key)) {\n                if (key !== 'goods_name') {\n                  row[key] = null;\n                }\n              }\n            }\n          }\n          sheetData.push(row);\n        }\n      }\n      exportExcel(th, filekey, fileName, sheetData);\n    }\n  }\n};\n", null]}