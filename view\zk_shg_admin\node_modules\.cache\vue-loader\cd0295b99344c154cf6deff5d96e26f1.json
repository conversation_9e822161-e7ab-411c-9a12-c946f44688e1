{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751009583468}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { getSalesStatistics, getStoreOptions, exportSalesStatistics } from '@/api/erp';\r\nimport FileSaver from 'file-saver';\r\n\r\nexport default {\r\n  name: 'SalesStatistics',\r\n  data() {\r\n    return {\r\n      // 筛选表单\r\n      filterForm: {\r\n        store_id: '',\r\n        keyword: '',\r\n        start_date: '',\r\n        end_date: ''\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 列表数据\r\n      storeList: [],\r\n      columns: [],\r\n      tableData: [],\r\n      // 分页\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      total: 0,\r\n      // 状态\r\n      tableLoading: false\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initData() {\r\n      await this.fetchStoreOptions();\r\n      await this.getSalesStatistics();\r\n    },\r\n    // 获取门店选项\r\n    async fetchStoreOptions() {\r\n      try {\r\n        const { data } = await getStoreOptions();\r\n        this.storeList = data || [];\r\n      } catch (error) {\r\n        console.error('获取门店列表失败:', error);\r\n      }\r\n    },\r\n    // 获取销售统计数据\r\n    async getSalesStatistics() {\r\n      this.tableLoading = true;\r\n      try {\r\n        const params = {\r\n          ...this.filterForm,\r\n          page: this.currentPage,\r\n          limit: this.pageSize\r\n        };\r\n        const { data } = await getSalesStatistics(params);\r\n        this.tableData = data.data;\r\n        this.columns = data.columns;\r\n        this.total = data.total;\r\n      } catch (error) {\r\n        console.error('获取销售统计失败:', error);\r\n        this.$Message.error('获取销售统计失败');\r\n      } finally {\r\n        this.tableLoading = false;\r\n      }\r\n    },\r\n    // 处理日期变化\r\n    handleDateChange(value) {\r\n      if (value && value.length === 2) {\r\n        this.filterForm.start_date = value[0];\r\n        this.filterForm.end_date = value[1];\r\n      } else {\r\n        this.filterForm.start_date = '';\r\n        this.filterForm.end_date = '';\r\n      }\r\n    },\r\n    // 重置筛选\r\n    resetFilter() {\r\n      this.filterForm = {\r\n        store_id: '',\r\n        keyword: '',\r\n        start_date: '',\r\n        end_date: ''\r\n      };\r\n      this.dateRange = [];\r\n      this.currentPage = 1;\r\n      this.getSalesStatistics();\r\n    },\r\n    // 分页变化\r\n    handleCurrentChange(page) {\r\n      this.currentPage = page;\r\n      this.getSalesStatistics();\r\n    },\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.getSalesStatistics();\r\n    },\r\n    // 导出销售数据\r\n    async exportSales() {\r\n      try {\r\n        const res = await exportSalesStatistics(this.filterForm);\r\n        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\r\n        const fileName = `商品销售统计_${new Date().toLocaleDateString()}.xlsx`;\r\n        FileSaver.saveAs(blob, fileName);\r\n      } catch (error) {\r\n        console.error('导出失败:', error);\r\n        this.$Message.error('导出失败');\r\n      }\r\n    }\r\n  }\r\n};\r\n", null]}