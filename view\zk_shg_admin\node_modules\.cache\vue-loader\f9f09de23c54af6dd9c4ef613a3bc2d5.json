{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nimport { memberAgreement, memberAgreementSave } from \"@/api/user\";\nimport { mapState,mapMutations } from \"vuex\";\nexport default {\n    components: { WangEditor },\n    data() {\n        return {\n            id: 0,\n            agreement: {\n                title: \"\",\n                content: \"\",\n                status: 1\n            },\n            content:'',\n            spinShow:false\n        };\n    },\n    created() {\n        this.memberAgreement();\n    },\n\tcomputed: {\n\t  ...mapState(\"admin/layout\", [\"isMobile\",\"menuCollapse\"]),\n\t  labelWidth() {\n\t    return this.isMobile ? undefined : 120;\n\t  },\n\t  labelPosition() {\n\t    return this.isMobile ? \"top\" : \"right\";\n\t  },\n\t  labelBottom() {\n\t    return this.isMobile ? undefined : 15;\n\t  }\n\t},\n    methods: {\n\t\tgetEditorContent(data){\n\t\t    this.agreement.content = data\n\t\t},\n        memberAgreement() {\n            this.spinShow = true;\n            memberAgreement()\n                .then(res => {\n                    this.spinShow = false;\n                    const { title, content, status, id } = res.data;\n                    this.agreement.title = title;\n                    this.agreement.content = content;\n                    this.content = content;\n                    this.agreement.status = status;\n                    this.id = id;\n                })\n                .catch(err => {\n                    this.$Message.error(err);\n                    this.spinShow = false;\n                });\n        },\n        // 保存\n        memberAgreementSave() {\n            this.$Spin.show();\n            memberAgreementSave(this.id, this.agreement)\n                .then(res => {\n                    this.$Spin.hide();\n                    this.$Message.success(\"保存成功\");\n                    this.memberAgreement();\n                })\n                .catch(err => {\n                    this.$Spin.hide();\n                    this.$Message.error(err.msg);\n                });\n        }\n    }\n};\n", null]}