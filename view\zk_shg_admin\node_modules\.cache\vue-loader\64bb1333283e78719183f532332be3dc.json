{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\type\\index.vue?vue&type=template&id=73c590be&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\type\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 用户-付费会员-会员类型 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n      <Button type=\"primary\" @click=\"addType\">添加类型</Button>\n      <!-- 会员类型表格 -->\n      <Table\n        class=\"mt25\"\n        :columns=\"thead\"\n        :data=\"tbody\"\n        :loading=\"loading\"\n        highlight-row\n        no-userFrom-text=\"暂无数据\"\n        no-filtered-userFrom-text=\"暂无筛选结果\"\n      >\n        <template slot-scope=\"{ row }\" slot=\"is_del\">\n          <i-switch\n            v-model=\"row.is_del\"\n            :value=\"row.is_del\"\n            :true-value=\"0\"\n            :false-value=\"1\"\n            @on-change=\"onchangeIsShow(row)\"\n            size=\"large\"\n          >\n            <span slot=\"open\">启用</span>\n            <span slot=\"close\">禁用</span>\n          </i-switch>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"action\">\n          <a href=\"javascript:\" @click=\"editType(row)\">编辑</a>\n          <Divider\n            type=\"vertical\"\n            v-if=\"row.type !== 'free' && row.type !== 'ever'\"\n          />\n          <a\n            v-if=\"row.type !== 'free' && row.type !== 'ever'\"\n            href=\"javascript:\"\n            @click=\"del(row, '删除类型', index)\"\n            >删除</a\n          >\n        </template>\n      </Table>\n    </Card>\n    <Modal\n      v-model=\"modal\"\n      :title=\"`${rowModelType}${rowEdit && rowEdit.title}会员`\"\n      footer-hide\n      @on-cancel=\"cancel\"\n    >\n      <form-create\n        v-model=\"fapi\"\n        :rule=\"rule\"\n        @on-submit=\"onSubmit\"\n      ></form-create>\n    </Modal>\n  </div>\n", null]}