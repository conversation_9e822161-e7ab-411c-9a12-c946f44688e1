{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_imgs.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_imgs.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport vuedraggable from 'vuedraggable'\nimport { mapState, mapActions } from 'vuex';\nimport UeditorWrap from 'vue-ueditor-wrap';\nimport uploadPictures from '@/components/uploadPictures';\nimport { wechatNewsAddApi, wechatNewsInfotApi } from '@/api/app';\nexport default {\n    name: 'c_hot_imgs',\n    props: {\n        configObj: {\n            type: Object\n        }\n    },\n    components: {\n        draggable: vuedraggable,\n        UeditorWrap,\n        uploadPictures\n    },\n    data () {\n        return {\n            defaults: {},\n            menus: [],\n            list: [\n                {\n                    title: 'aa',\n                    val: ''\n                }\n            ],\n            modalPic: false,\n            isChoice: '单选',\n            gridBtn: {\n                xl: 4,\n                lg: 8,\n                md: 8,\n                sm: 8,\n                xs: 8\n            },\n            gridPic: {\n                xl: 6,\n                lg: 8,\n                md: 12,\n                sm: 12,\n                xs: 12\n            },\n            activeIndex: 0\n        }\n    },\n    created () {\n        this.defaults = this.configObj\n    },\n    watch: {\n        configObj: {\n            handler (nVal, oVal) {\n                this.defaults = nVal\n            },\n            immediate: true,\n            deep: true\n        }\n    },\n    methods: {\n        addBox () {\n            let obj = {\n                img: 'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1594458238721&di=d9978a807dcbf5d8a01400875bc51162&imgtype=0&src=http%3A%2F%2Fattachments.gfan.com%2Fforum%2F201604%2F23%2F002205xqdkj84gnw4oi85v.jpg',\n                info: [\n                    {\n                        title: '标题',\n                        value: '',\n                        tips: '选填，不超过4个字',\n                        max: 4\n                    },\n                    {\n                        title: '简介',\n                        value: '',\n                        tips: '选填，不超过20个字',\n                        max: 20\n                    }\n                ],\n                link: {\n                    title: '链接',\n                    optiops: [\n                        {\n                            type: 0,\n                            value: '',\n                            label: '一级>二级分类'\n                        },\n                        {\n                            type: 1,\n                            value: '',\n                            label: '自定义链接'\n                        }\n                    ]\n                }\n            }\n            this.defaults.menu.push(obj)\n        },\n        // 点击图文封面\n        modalPicTap (title, index) {\n            this.activeIndex = index\n            this.modalPic = true;\n        },\n        // 添加自定义弹窗\n        addCustomDialog (editorId) {\n            window.UE.registerUI('test-dialog', function (editor, uiName) {\n                let dialog = new window.UE.ui.Dialog({\n                    iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n                    editor: editor,\n                    name: uiName,\n                    title: '上传图片',\n                    cssRules: 'width:1200px;height:500px;padding:20px;'\n                });\n                this.dialog = dialog;\n                // 参考上面的自定义按钮\n                var btn = new window.UE.ui.Button({\n                    name: 'dialog-button',\n                    title: '上传图片',\n                    cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,\n                    onclick: function () {\n                        // 渲染dialog\n                        dialog.render();\n                        dialog.open();\n                    }\n                });\n\n                return btn;\n            }, 37);\n        },\n        // 获取图片信息\n        getPic (pc) {\n            this.defaults.menu[this.activeIndex].img = pc.att_dir;\n            this.modalPic = false;\n        }\n    }\n}\n", null]}