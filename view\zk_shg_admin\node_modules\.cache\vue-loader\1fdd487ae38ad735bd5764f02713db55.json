{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\information\\index.vue?vue&type=template&id=6c4a1af8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\information\\index.vue", "mtime": 1662022394000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"information\">\n  <Modal\n    v-model=\"isShow\"\n    title=\"选择信息\"\n    footerHide\n    class=\"paymentFooter\"\n    scrollable\n    width=\"900\"\n    @on-cancel=\"cancel\"\n  >\n    <Form ref=\"formValidate\" :model=\"formValidate\" :label-width=\"100\">\n      <FormItem label=\"信息搜索：\">\n        <Input\n          v-model=\"formValidate.info\"\n          placeholder=\"请输入信息\"\n          class=\"inputw\"\n        />\n        <Button type=\"primary\" @click=\"handleSubmit\">\n          查询\n        </Button>\n      </FormItem>\n    </Form>\n    <Table\n      ref=\"table\"\n      no-data-text=\"暂无数据\"\n      no-filtered-data-text=\"暂无筛选结果\"\n      :columns=\"columns1\"\n      :data=\"listOneNew\"\n      :loading=\"loading\"\n      class=\"mr-20\"\n      @on-selection-change=\"selectionTap\"\n    ></Table>\n    <div class=\"footer mt20\">\n      <Button class=\"btn\" @click=\"cancel\">取消</Button>\n      <Button type=\"primary\" class=\"btn\" @click=\"ok\">确认</Button>\n    </div>\n  </Modal>\n</div>\n", null]}