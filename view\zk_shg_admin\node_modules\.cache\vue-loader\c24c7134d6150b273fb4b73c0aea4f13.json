{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\create.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\create.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nimport goodsList from '@/components/goodsList/index';\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nimport uploadPictures from '@/components/uploadPictures';\nimport freightTemplate from \"@/components/freightTemplate\";\nimport { combinationInfoApi, combinationCreatApi, productAttrsApi } from '@/api/marketing';\nimport { productGetTemplateApi, productAllUnit, productUnitCreate } from '@/api/product';\nimport Setting from \"@/setting\";\nexport default {\n    name: 'storeCombinationCreate',\n    components: { goodsList, uploadPictures, WangEditor, freightTemplate },\n    data() {\n        return {\n          roterPre: Setting.roterPre,\n            template: false,\n            submitOpen: false,\n            spinShow: false,\n            isChoice: '',\n            current: 0,\n            modalPic: false,\n            grid: {\n                xl: 12,\n                lg: 20,\n                md: 24,\n                sm: 24,\n                xs: 24\n            },\n            grid2: {\n                xl: 8,\n                lg: 8,\n                md: 12,\n                sm: 24,\n                xs: 24\n            },\n            gridPic: {\n                xl: 6,\n                lg: 8,\n                md: 12,\n                sm: 12,\n                xs: 12\n            },\n            gridBtn: {\n                xl: 4,\n                lg: 8,\n                md: 8,\n                sm: 8,\n                xs: 8\n            },\n            modals: false,\n            modal_loading: false,\n            images: [],\n            templateList: [],\n            columns: [],\n            specsData: [],\n            picTit: '',\n            tableIndex: 0,\n            unitNameList: [],\n            formValidate: {\n                is_support_refund: 0,\n                product_type: 0,\n                freight: 1, //运费设置\n                delivery_type: [],\n                images: [],\n                info: '',\n                title: '',\n                image: '',\n                unit_name: '',\n                price: 0,\n                effective_time: 24,\n                stock: 1,\n                sales: 0,\n                sort: 0,\n                postage: 0,\n                is_postage: 0,\n                is_host: 0,\n                is_show: 0,\n                section_time: [],\n                description: '',\n                id: 0,\n                product_id: 0,\n                people: 2,\n                once_num: 1,\n                num: 1,\n                temp_id: '',\n                attrs: [],\n                items: [],\n                virtual: 100,\n                peopleNum: 0\n            },\n            description: '',\n            ruleValidate: {\n                image: [\n                    { required: true, message: '请选择主图', trigger: 'change' }\n                ],\n                images: [\n                    { required: true, type: 'array', message: '请选择主图', trigger: 'change' },\n                    { type: 'array', min: 1, message: 'Choose two hobbies at best', trigger: 'change' }\n                ],\n                title: [\n                    { required: true, message: '请输入拼团名称', trigger: 'blur' }\n                ],\n                info: [\n                    { required: true, message: '请输入拼团简介', trigger: 'blur' }\n                ],\n                section_time: [\n                    { required: true, type: 'array', message: '请选择活动时间', trigger: 'change' }\n                ],\n                unit_name: [\n                    {\n                        required: true,\n                        message: \"请输入单位\",\n                        trigger: \"change\",\n                    }\n                ],\n                price: [\n                    { required: true, type: 'number', message: '请输入拼团价', trigger: 'blur' }\n                ],\n                cost: [\n                    { required: true, type: 'number', message: '请输入成本价', trigger: 'blur' }\n                ],\n                stock: [\n                    { required: true, type: 'number', message: '请输入库存', trigger: 'blur' }\n                ],\n                give_integral: [\n                    { required: true, type: 'number', message: '请输入赠送积分', trigger: 'blur' }\n                ],\n                effective_time: [\n                    { required: true, type: 'number', message: '请输入拼团时效', trigger: 'blur' }\n                ],\n                people: [\n                    { required: true, type: 'number', message: '请输入拼团人数', trigger: 'blur' }\n                ],\n                num: [\n                    { required: true, type: 'number', message: '请输入购买数量限制', trigger: 'blur' }\n                ],\n                once_num: [\n                    { required: true, type: 'number', message: '请输入单次购买数量限制', trigger: 'blur' }\n                ],\n                virtual: [\n                    { required: true, type: 'number', message: '请输入虚拟成团比例', trigger: 'blur' }\n                ]\n            },\n            copy: 0\n        }\n    },\n    watch: {\n        'formValidate.peopleNum': (n) => {\n        }\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile',\n            'menuCollapse'\n        ]),\n        labelWidth() {\n            return this.isMobile ? undefined : 155;\n        },\n        labelPosition() {\n            return this.isMobile ? 'top' : 'right';\n        }\n    },\n    mounted() {\n        if (this.$route.params.id) {\n            this.copy = this.$route.params.copy;\n            this.current = 1;\n            this.getInfo();\n        }\n        this.getAllUnit();\n    },\n    methods: {\n        changeTemplate(msg) {\n            this.template = msg;\n        },\n        // 添加运费模板\n        addTemp() {\n            this.$refs.templates.isTemplate = true;\n        },\n        addUnit() {\n            this.$modalForm(productUnitCreate()).then(() => this.getAllUnit());\n        },\n        getAllUnit() {\n            productAllUnit().then(res => {\n                this.unitNameList = res.data;\n            }).catch(err => {\n                this.$Message.error(err.msg);\n            })\n        },\n        getEditorContent(data) {\n            this.description = data;\n        },\n        peopleChange(n) {\n            if (n < 2) {\n                this.formValidate.people = 2;\n                this.$set(this.formValidate, 'people', 2);\n            }\n        },\n        peopleNumchange(n) {\n            if (n != 0) {\n                this.formValidate.virtual = Math.floor((this.formValidate.people - n) / this.formValidate.people * 100);\n            }\n        },\n        // 拼团规格；\n        productAttrs(row) {\n            let that = this;\n            productAttrsApi(row.id, 3).then(res => {\n                let data = res.data.info;\n                let selection = {\n                    type: 'selection',\n                    width: 60,\n                    align: 'center'\n                };\n                that.specsData = data.attrs;\n                that.specsData.forEach(function (item, index) {\n                    that.$set(that.specsData[index], 'id', index);\n                });\n                that.formValidate.items = data.items;\n                that.columns = data.header;\n                that.columns.unshift(selection);\n                that.inputChange(data);\n            }).catch(res => {\n                that.$Message.error(res.msg);\n            })\n        },\n        inputChange(data) {\n            let that = this;\n            let $index = [];\n            data.header.forEach(function (item, index) {\n                if (item.type === 1) {\n                    $index.push({ index: index, key: item.key, title: item.title });\n                }\n            });\n            $index.forEach(function (item, index) {\n                let title = item.title;\n                let key = item.key;\n                let row = {\n                    title: title,\n                    key: key,\n                    align: 'center',\n                    minWidth: 100,\n                    render: (h, params) => {\n                        return h('div', [\n                            h('InputNumber', {\n                                props: {\n                                    min: 0,\n                                    value: key === 'price' ? params.row.price : params.row.quota\n                                },\n                                on: {\n                                    'on-change': e => {\n                                        key === 'price' ? params.row.price = e : params.row.quota = e;\n                                        that.specsData[params.index] = params.row;\n                                        if (!!that.formValidate.attrs && that.formValidate.attrs.length) {\n                                            that.formValidate.attrs.forEach((v, index) => {\n                                                if (v.id === params.row.id) {\n                                                    that.formValidate.attrs.splice(index, 1, params.row);\n                                                }\n                                            });\n                                        }\n                                    }\n                                }\n                            })\n                        ])\n                    }\n                };\n                that.columns.splice(item.index, 1, row);\n            });\n        },\n        // 多选\n        changeCheckbox(selection) {\n            this.formValidate.attrs = selection;\n        },\n        // 获取运费模板；\n        productGetTemplate(id) {\n            productGetTemplateApi({id:id}).then(res => {\n                this.templateList = res.data;\n            })\n        },\n        // 表单验证\n        validate(prop, status, error) {\n            if (status === false) {\n                this.$Message.error(error);\n            }\n        },\n        // 商品id\n        getProductId(row) {\n            this.modal_loading = false;\n            this.modals = false;\n            setTimeout(() => {\n                this.formValidate = {\n                    is_support_refund: row.is_support_refund,\n                    product_type: row.product_type,\n                    images: row.slider_image,\n                    info: row.store_info,\n                    title: row.store_name,\n                    image: row.image,\n                    unit_name: row.unit_name,\n                    price: 0, // 不取商品中的原价\n                    effective_time: 24,\n                    stock: row.stock,\n                    sales: row.sales,\n                    sort: row.sort,\n                    postage: row.postage,\n                    is_postage: row.is_postage,\n                    is_host: row.is_hot,\n                    is_show: 0,\n                    section_time: [],\n                    description: row.description, // 不取商品中的\n                    id: 0,\n                    people: 2,\n                    num: 1,\n                    once_num: 1,\n                    product_id: row.id,\n                    temp_id: row.temp_id,\n                    virtual: 100,\n                    peopleNum: 0,\n                    freight: 1, //运费设置\n                    delivery_type: []\n                };\n                this.productGetTemplate(row.id);\n\t\t\t\tthis.productAttrs(row);\n            }, 500);\n        },\n        cancel() {\n            this.modals = false;\n        },\n        // 具体日期\n        onchangeTime(e) {\n            this.formValidate.section_time = e;\n        },\n        // 详情\n        getInfo() {\n            this.spinShow = true;\n            combinationInfoApi(this.$route.params.id).then(async res => {\n                let that = this;\n                let info = res.data.info;\n                let selection = {\n                    type: 'selection',\n                    width: 60,\n                    align: 'center'\n                };\n                this.formValidate = info;\n                this.description = info.description\n\n                if (parseInt(this.formValidate.virtual) !== 100) {\n                    this.formValidate.peopleNum = Math.floor(this.formValidate.people - this.formValidate.virtual / 100 * this.formValidate.people);\n                } else {\n                    this.formValidate.peopleNum = 0;\n                }\n                this.$set(this.formValidate, 'items', info.attrs.items);\n                this.columns = info.attrs.header;\n                this.columns.unshift(selection);\n                this.specsData = info.attrs.value;\n                that.specsData.forEach(function (item, index) {\n                    that.$set(that.specsData[index], 'id', index);\n                });\n                let data = info.attrs;\n                let attr = [];\n                for (let index in info.attrs.value) {\n                    if (info.attrs.value[index]._checked) {\n                        attr.push(info.attrs.value[index]);\n                    }\n                }\n                that.formValidate.attrs = attr;\n                that.inputChange(data);\n                this.spinShow = false;\n            }).catch(res => {\n                this.spinShow = false;\n                this.$Message.error(res.msg);\n            })\n        },\n        // 下一步\n        next(name) {\n            let that = this;\n            if (this.current === 2) {\n                this.formValidate.description = this.description\n                this.$refs[name].validate((valid) => {\n                    if (valid) {\n                        if (this.copy == 1) this.formValidate.copy = 1;\n                        this.formValidate.id = Number(this.$route.params.id) || 0;\n                        this.submitOpen = true;\n                        if (!this.formValidate.product_type) {\n                            this.formValidate.is_support_refund = 1;\n                        }\n                        combinationCreatApi(this.formValidate).then(async res => {\n                            this.submitOpen = false;\n                            this.$Message.success(res.msg);\n                            setTimeout(() => {\n                                this.$router.push({ path: `${this.roterPre}/marketing/store_combination/index` });\n                            }, 500);\n                        }).catch(res => {\n                            this.submitOpen = false;\n                            this.$Message.error(res.msg);\n                        })\n                    } else {\n                        return false\n                    }\n                })\n            } else if (this.current === 1) {\n                this.$refs[name].validate((valid) => {\n                    if (valid) {\n                        if (that.formValidate.people < 2) {\n                            return that.$Message.error('拼团人数必须大于2')\n                        }\n                        if (that.formValidate.num < 0) {\n                            return that.$Message.error('购买数量限制必须大于0')\n                        }\n                        if (that.formValidate.once_num < 0) {\n                            return that.$Message.error('单次购买数量限制必须大于0')\n                        }\n                        if (!that.formValidate.attrs) {\n                            return that.$Message.error('请选择属性规格');\n                        } else {\n                            for (let index in that.formValidate.attrs) {\n                                if (that.formValidate.attrs[index].quota <= 0) {\n                                    return that.$Message.error('拼团限量必须大于0');\n                                }\n                            }\n                        }\n                        if(this.formValidate.product_type == 0 && !this.formValidate.delivery_type.length){\n                            return this.$Message.warning(\"请选择配送方式\");\n                        }\n                        if (this.formValidate.product_type == 0 && this.formValidate.freight == 2 && this.formValidate.postage <= 0) {\n                            return this.$Message.warning(\"物流设置-固定邮费不能为0\");\n                        }\n                        if (this.formValidate.product_type == 0 && this.formValidate.freight == 3 && !this.formValidate.temp_id) {\n                            return this.$Message.warning(\"物流设置-运费模板不能为空\");\n                        }\n                        this.current += 1;\n                    } else {\n                        return this.$Message.warning('请完善商品信息');\n                    }\n                })\n            } else {\n                if (this.formValidate.image) {\n                    this.current += 1;\n                } else {\n                    this.$Message.warning('请选择商品');\n                }\n            }\n        },\n        // 上一步\n        step() {\n            this.current--;\n        },\n        // 内容\n        getContent(val) {\n            this.formValidate.description = val;\n        },\n        // 点击商品图\n        modalPicTap(tit, picTit, index) {\n            this.modalPic = true;\n            this.isChoice = tit === 'dan' ? '单选' : '多选';\n            this.picTit = picTit;\n            this.tableIndex = index;\n        },\n        // 获取单张图片信息\n        getPic(pc) {\n            switch (this.picTit) {\n                case 'danFrom':\n                    this.formValidate.image = pc.att_dir;\n                    break;\n                default:\n                    if (!!this.formValidate.attrs && this.formValidate.attrs.length) {\n                        this.$set(this.specsData[this.tableIndex], '_checked', true);\n                    }\n                    this.specsData[this.tableIndex].pic = pc.att_dir;\n            }\n            this.modalPic = false;\n        },\n        // 获取多张图信息\n        getPicD(pc) {\n            this.images = pc;\n            this.images.map((item) => {\n                this.formValidate.images.push(item.att_dir)\n                this.formValidate.images = this.formValidate.images.splice(0, 10);\n            });\n            this.modalPic = false;\n        },\n        handleRemove(i) {\n            this.images.splice(i, 1);\n            this.formValidate.images.splice(i, 1);\n        },\n        // 选择商品\n        changeGoods() {\n            this.modals = true;\n        },\n        // 移动\n        handleDragStart(e, item) {\n            this.dragging = item;\n        },\n        handleDragEnd(e, item) {\n            this.dragging = null\n        },\n        // 首先把div变成可以放置的元素，即重写dragenter/dragover\n        handleDragOver(e) {\n            e.dataTransfer.dropEffect = 'move';\n        },\n        handleDragEnter(e, item) {\n            e.dataTransfer.effectAllowed = 'move'\n            if (item === this.dragging) {\n                return\n            }\n            const newItems = [...this.formValidate.images]\n            const src = newItems.indexOf(this.dragging)\n            const dst = newItems.indexOf(item)\n            newItems.splice(dst, 0, ...newItems.splice(src, 1))\n            this.formValidate.images = newItems;\n        },\n        // 添加自定义弹窗\n        addCustomDialog(editorId) {\n            window.UE.registerUI('test-dialog', function (editor, uiName) {\n                // 创建 dialog\n                let dialog = new window.UE.ui.Dialog({\n                    // 指定弹出层中页面的路径，这里只能支持页面，路径参考常见问题 2\n                    iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n                    // 需要指定当前的编辑器实例\n                    editor: editor,\n                    // 指定 dialog 的名字\n                    name: uiName,\n                    // dialog 的标题\n                    title: '上传图片',\n                    // 指定 dialog 的外围样式\n                    cssRules: 'width:1200px;height:500px;padding:20px;'\n                });\n                this.dialog = dialog;\n                // 参考上面的自定义按钮\n                var btn = new window.UE.ui.Button({\n                    name: 'dialog-button',\n                    title: '上传图片',\n                    cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,\n                    onclick: function () {\n                        // 渲染dialog\n                        dialog.render();\n                        dialog.open();\n                    }\n                });\n                return btn;\n            }, 37);\n        }\n    }\n}\n", null]}