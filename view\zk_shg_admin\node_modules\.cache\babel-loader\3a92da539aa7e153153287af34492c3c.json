{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\syncSetting\\buildData.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\syncSetting\\buildData.js", "mtime": 1658973957000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport { getNewFormBuildRuleApi } from '@/api/setting';\nexport default {\n  mounted: function mounted() {\n    this.getNewFormBuildRule();\n  },\n  methods: {\n    getNewFormBuildRule: function getNewFormBuildRule() {\n      var _this = this;\n\n      getNewFormBuildRuleApi(this.type ? this.type : this.typeMole).then(function (res) {\n        // 定义数据\n        _this.rules = res.data.rules;\n        _this.url = res.data.url;\n        var validate = res.data.validate;\n        Object.keys(validate).map(function (key) {\n          if (_typeof(validate[key]) === \"object\") {\n            validate[key].map(function (item) {\n              if (item.pattern !== undefined) {\n                item.pattern = eval(item.pattern);\n              }\n            });\n          }\n        });\n        _this.ruleValidate = validate;\n      });\n    },\n    setRulesValue: function setRulesValue(rules, data) {\n      var _this2 = this;\n\n      rules.map(function (item) {\n        if (item.field !== undefined) {\n          item.value = data[item.field] || '';\n        }\n\n        if (_typeof(item.options) === 'object') {\n          item.options.map(function (option) {\n            if (option.componentsModel !== undefined) {\n              option.componentsModel = _this2.setRulesValue(option.componentsModel, data);\n            }\n          });\n        }\n\n        if (_typeof(item.control) === 'object') {\n          item.control.map(function (value) {\n            if (value.componentsModel !== undefined) {\n              value.componentsModel = _this2.setRulesValue(value.componentsModel, data);\n            }\n          });\n        }\n\n        if (_typeof(item.componentsModel) === \"object\") {\n          item.componentsModel = _this2.setRulesValue(item.componentsModel, data);\n        }\n      });\n      return rules;\n    }\n  }\n};", null]}