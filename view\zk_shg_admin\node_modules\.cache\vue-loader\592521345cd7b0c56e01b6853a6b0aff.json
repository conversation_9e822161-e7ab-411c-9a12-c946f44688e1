{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\featuredGood\\index.vue?vue&type=template&id=63f0257e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\featuredGood\\index.vue", "mtime": 1712136648869}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\",\"padding\":0}},[_c('div',{staticClass:\"card_pd\"},[_c('Form',{ref:\"formValidate\",attrs:{\"inline\":\"\",\"model\":_vm.formValidate,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"商品分类：\",\"prop\":\"categoryId\"}},[_c('el-cascader',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择商品分类\",\"size\":\"mini\",\"options\":_vm.data1,\"props\":_vm.props,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.selChange},model:{value:(_vm.formValidate.categoryId),callback:function ($$v) {_vm.$set(_vm.formValidate, \"categoryId\", $$v)},expression:\"formValidate.categoryId\"}})],1),_c('FormItem',{attrs:{\"label\":\"商品条件：\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"multiple\":\"\"},model:{value:(_vm.formValidate.filters),callback:function ($$v) {_vm.$set(_vm.formValidate, \"filters\", $$v)},expression:\"formValidate.filters\"}},_vm._l((_vm.filter),function(itemn,indexn){return _c('Option',{key:indexn,attrs:{\"value\":itemn.key}},[_vm._v(_vm._s(itemn.desc))])}),1)],1),_c('FormItem',{attrs:{\"label\":\"价格区间：\"}},[_c('InputNumber',{model:{value:(_vm.formValidate.priceStart),callback:function ($$v) {_vm.$set(_vm.formValidate, \"priceStart\", $$v)},expression:\"formValidate.priceStart\"}}),_vm._v(\"\\n        --\\n        \"),_c('InputNumber',{model:{value:(_vm.formValidate.priceEnd),callback:function ($$v) {_vm.$set(_vm.formValidate, \"priceEnd\", $$v)},expression:\"formValidate.priceEnd\"}})],1),_c('FormItem',{attrs:{\"label\":\"搜索：\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('Input',{staticClass:\"input-width\",attrs:{\"placeholder\":\"商品关键字\",\"element-id\":\"keyword\"},model:{value:(_vm.formValidate.keyword),callback:function ($$v) {_vm.$set(_vm.formValidate, \"keyword\", $$v)},expression:\"formValidate.keyword\"}})],1)]),_c('FormItem',[_c('Button',{staticClass:\"btn-add\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.selChange}},[_vm._v(\"查询\")]),_c('Button',{on:{\"click\":_vm.reset}},[_vm._v(\"重置\")])],1)],1)],1)]),_c('Card',{attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Table',{ref:\"table\",staticClass:\"ivu-mt\",attrs:{\"columns\":_vm.columns,\"data\":_vm.tabList,\"loading\":_vm.loading,\"no-data-text\":\"暂无数据\",\"no-filtered-data-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"name\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',[_vm._v(\"\\n          \"+_vm._s(row.title)+\"\\n        \")])]}},{key:\"serviceList\",fn:function(ref){\nvar row = ref.row;\nreturn _vm._l((row.serviceList),function(item,index){return _c('div',{key:index},[_vm._v(\"\\n          \"+_vm._s(item.name)),_c('br')])})}},{key:\"price\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',[_c('div',{staticStyle:{\"color\":\"red\",\"font-weight\":\"bold\"}},[_vm._v(\"价格区间：¥\"+_vm._s(row.minPrice / 100)+\" ~ \"+_vm._s(row.maxPrice / 100))]),_vm._v(\"\\n            SKU数量：\"+_vm._s(row.skuCnt)+\"\\n        \")])]}},{key:\"image\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('viewer',[_c('div',{staticClass:\"tabBox_img\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.imgUrl),expression:\"row.imgUrl\"}]})])])]}},{key:\"createModalFrame\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.dao(row.itemId)}}},[_vm._v(\"导入商品库\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"current\":_vm.formValidate.pageNum,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.formValidate.pageSize},on:{\"on-change\":_vm.pageChange}})],1)],1),_c('Modal',{attrs:{\"title\":\"选择分类\",\"width\":\"400\",\"class-name\":\"batch-modal\"},model:{value:(_vm.batchModal),callback:function ($$v) {_vm.batchModal=$$v},expression:\"batchModal\"}},[_c('el-cascader',{class:{ single: !_vm.cate_id.length },attrs:{\"options\":_vm.data2,\"props\":{ emitPath: false, multiple: true, checkStrictly: true },\"size\":\"small\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.cate_id),callback:function ($$v) {_vm.cate_id=$$v},expression:\"cate_id\"}}),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.saveBatch}},[_vm._v(\"确认\")])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}