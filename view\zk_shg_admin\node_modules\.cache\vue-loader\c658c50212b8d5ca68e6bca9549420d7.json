{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue?vue&type=template&id=19f859da&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"upload_img\",class:_vm.configData.type == 'code'?'on':''},[_c('div',{staticClass:\"header\"},[_vm._v(_vm._s(_vm.configData.header))]),_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.configData.title))]),_c('div',{staticClass:\"list\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"name\"},[_vm._v(_vm._s(_vm.configData.type == 'code'?_vm.configData.name:'图片'))]),_c('div',{staticClass:\"picTxt\"},[_c('div',{staticClass:\"box\",on:{\"click\":function($event){return _vm.modalPicTap('单选')}}},[(_vm.configData.url)?_c('div',{staticClass:\"pictrue acea-row row-center-wrapper\"},[(_vm.configData.video)?_c('video',{attrs:{\"src\":_vm.configData.url}}):_c('img',{attrs:{\"src\":_vm.configData.url,\"alt\":\"\"}}),(_vm.configData.delType)?_c('div',{staticClass:\"iconfont icondel_1\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete($event)}}}):_vm._e()]):_c('div',{staticClass:\"upload-box\"},[_c('Icon',{attrs:{\"type\":\"ios-add\",\"size\":\"50\"}})],1)]),_c('div',{staticClass:\"tip\"},[_vm._v(_vm._s(_vm.configData.info))])])]),(_vm.configData.type != 'code')?_c('div',{staticClass:\"item\",on:{\"click\":_vm.getLink}},[_c('div',{staticClass:\"name\"},[_vm._v(\"链接\")]),_c('Input',{staticStyle:{\"width\":\"290px\"},attrs:{\"icon\":\"ios-arrow-forward\",\"readonly\":\"\",\"placeholder\":\"输入链接\"},model:{value:(_vm.configData.link),callback:function ($$v) {_vm.$set(_vm.configData, \"link\", $$v)},expression:\"configData.link\"}})],1):_vm._e()]),_c('div',[_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":_vm.configData.header?_vm.configData.header:'上传图片',\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1)],1),(_vm.configData.type != 'code')?_c('linkaddress',{ref:\"linkaddres\",on:{\"linkUrl\":_vm.linkUrl}}):_vm._e()],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}