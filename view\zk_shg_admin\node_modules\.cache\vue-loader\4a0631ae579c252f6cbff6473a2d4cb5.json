{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport linkaddress from \"@/components/linkaddress\";\nexport default {\n  name: \"AreaBox\",\n  components: { linkaddress },\n  props: {\n    areaInit: {\n      type: Object,\n      default: () => {},\n    },\n    areaDataIndex: {\n      type: Number,\n      default: null,\n    },\n    link: {\n      type: String,\n      default: \"\",\n    },\n    title: {\n      type: String,\n      default: \"\",\n    },\n    type: {\n      type: Number,\n      default: -1,\n    },\n    parentWidth: {\n      type: Number,\n      default: 0,\n    },\n    parentHeight: {\n      type: Number,\n      default: 0,\n    },\n  },\n  data() {\n    return {\n      areaTitle: \"\",\n      url: \"\",\n      editBoxShow: false,\n      promptText: \"双击设置热区\",\n      // box操作初始点\n      move: {\n        // 拖动\n        startX: 0,\n        starY: 0,\n        // 形变\n        start1X: 0,\n        start1Y: 0,\n      },\n    };\n  },\n  computed: {\n    isSet() {\n      return !!this.link;\n    },\n  },\n  watch: {\n    title(val) {\n      this.areaTitle = val;\n    },\n    link(val) {\n      this.url = val;\n    },\n  },\n  mounted() {\n    this.url = this.link;\n  },\n  methods: {\n    // 删除\n    del() {\n      this.$emit(\"delAreaBox\", this.areaDataIndex);\n    },\n    // 添加网址\n    addURL() {\n      if (!this.url) {\n        this.$Message.error(\"请输入链接\");\n      } else {\n        this.$emit(\"addURL\", this.areaDataIndex, this.url);\n        this.editBoxShow = false;\n      }\n    },\n    // 开始拖动限制范围\n    mouseDownLint(e) {\n      console.log(e);\n      e.preventDefault();\n      this.starX = e.clientX;\n      this.starY = e.clientY;\n      const childrenDiv = e.target || e;\n      //获取子元素的宽高\n      let childrenWidth = childrenDiv.getBoundingClientRect().width;\n      let childrenHight = childrenDiv.getBoundingClientRect().height;\n      // console.log(childrenWidth, childrenHight)\n      if (!document.onmousemove) {\n        const initX = this.areaInit.starX;\n        const initY = this.areaInit.starY;\n        document.onmousemove = (ev) => {\n          // 移动位置\n          let nLeft = initX + ev.clientX - this.starX;\n          let nTop = initY + ev.clientY - this.starY;\n          nLeft = nLeft <= 0 ? 0 : nLeft; //判断左边是否越界\n          nTop = nTop <= 0 ? 0 : nTop; //判断上边是否越界\n          let nRight = nLeft + childrenWidth;\n          let nBottom = nTop + childrenHight;\n          // 判断右边是否越界\n          if (nRight >= this.parentWidth) {\n            nLeft = this.parentWidth - childrenWidth;\n          }\n          // 判断下边是否越界\n          if (nBottom >= this.parentHeight) {\n            nTop = this.parentHeight - childrenHight;\n          }\n          this.areaInit.starX = nLeft;\n          this.areaInit.starY = nTop;\n        };\n      }\n    },\n    // 开始拖动不限制范围\n    mouseDown(e) {\n      e.preventDefault();\n      this.starX = e.clientX;\n      this.starY = e.clientY;\n      if (!document.onmousemove) {\n        const initX = this.areaInit.starX;\n        const initY = this.areaInit.starY;\n        document.onmousemove = (ev) => {\n          this.areaInit.starX = initX + ev.clientX - this.starX;\n          this.areaInit.starY = initY + ev.clientY - this.starY;\n        };\n      }\n    },\n    // 结束拖动/变形\n    mouseUp() {\n      document.onmousemove = null;\n    },\n    // 形变开始\n    shapeDown(e) {\n      e.preventDefault();\n\n      this.star1X = e.clientX;\n      this.star1Y = e.clientY;\n      // 获取左部和底部的偏移量\n\n      if (!document.onmousemove) {\n        const initX = this.areaInit.areaWidth;\n        const initY = this.areaInit.areaHeight;\n        document.onmousemove = (ev) => {\n          this.areaInit.areaWidth = initX + ev.clientX - this.star1X;\n          this.areaInit.areaHeight = initY + ev.clientY - this.star1Y;\n        };\n      }\n    },\n    getLink() {\n      this.$refs.linkaddres.modals = true;\n    },\n    linkUrl(e) {\n      this.url = e;\n    },\n  },\n};\n", null]}