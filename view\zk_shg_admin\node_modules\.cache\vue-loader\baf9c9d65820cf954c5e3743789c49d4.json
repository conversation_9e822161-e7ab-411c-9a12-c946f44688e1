{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_video.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_video.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    export default {\n        name: 'c_video',\n        componentsName: 'home_video',\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_set_up,\n\t\t\t\t\t    configNme: 'setUp'\n\t\t\t\t\t}\n                ]\n            }\n        },\n        watch: {\n            num (nVal) {\n                // debugger;\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n\t\t\t'configObj.setUp.tabVal': {\n\t\t\t    handler (nVal, oVal) {\n\t\t\t        var arr = [this.rCom[0]]\n\t\t\t        if (nVal == 0) {\n\t\t\t            let tempArr = [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  components: toolCom.c_upload_img,\n\t\t\t\t\t\t\t  configNme: 'videoConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  components: toolCom.c_upload_img,\n\t\t\t\t\t\t\t  configNme: 'imgConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t\t\t    configNme: 'scaleConfig'\n\t\t\t\t\t\t\t}\n\t\t\t            ]\n\t\t\t            this.rCom = arr.concat(tempArr)\n\t\t\t        } else {\n\t\t\t            let tempArr = [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t\t\t},\n\t\t\t                {\n\t\t\t                    components: toolCom.c_slider,\n\t\t\t                    configNme: 'topConfig'\n\t\t\t                },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t\t\t}\n\t\t\t            ]\n\t\t\t            this.rCom = arr.concat(tempArr)\n\t\t\t        }\n\t\t\t    },\n\t\t\t    deep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n            // 获取组件参数\n            getConfig (data) {},\n        }\n    }\n", null]}