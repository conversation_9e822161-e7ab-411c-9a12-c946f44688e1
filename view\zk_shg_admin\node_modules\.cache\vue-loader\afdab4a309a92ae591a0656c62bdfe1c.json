{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_coupon.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_coupon.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { mapMutations } from 'vuex'\n    export default {\n        name: 'c_home_coupon',\n        componentsName: 'home_coupon',\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_set_up,\n\t\t\t\t\t    configNme: 'setUp'\n\t\t\t\t\t}\n                ],\n\t\t\t\toneStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_title,\n\t\t\t\t\t    configNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'couponMoneyColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tbntBgStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bntBgColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcouponBgStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'couponBgColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyTitleStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'spacingConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tmoduleColorStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tmoduleColorStyle2:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor2'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsetUp:0,\n\t\t\t\ttype:0,\n\t\t\t\ttype2:0\n            }\n        },\n        watch: {\n            num (nVal) {\n                this.configObj = this.$store.state.admin.mobildConfig.defaultArray[nVal]\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n\t\t\t'configObj.setUp.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.setUp = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\tlet tempArr = [\n\t\t\t\t\t\t    {\n\t\t\t\t\t\t    \tcomponents: toolCom.c_title,\n\t\t\t\t\t\t    \tconfigNme: 'titleLeft'\n\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_button_style,\n\t\t\t\t\t\t\t\tconfigNme: 'styleConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleData'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'numberConfig'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t];\n\t\t\t\t\t\tthis.rCom = arr.concat(tempArr)\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type2);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.styleConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tthis.getRComStyle(arr,nVal,this.type2);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.toneConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,nVal);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n\t\t\tgetRComStyle(arr,type,type2){\n\t\t\t\tif(type == 0 || type == 3){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyTitleStyle,...this.moduleColorStyle2,...this.currencyStyle]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.bntBgStyle,...this.couponBgStyle,...this.currencyTitleStyle,...this.moduleColorStyle2,...this.currencyStyle]\n\t\t\t\t\t}\n\t\t\t\t}else if(type == 1){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyTitleStyle,...this.moduleColorStyle,...this.currencyStyle]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.bntBgStyle,...this.currencyTitleStyle,...this.moduleColorStyle,...this.currencyStyle]\n\t\t\t\t\t}\n\t\t\t\t}else if(type == 2){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyTitleStyle,...this.moduleColorStyle2,...this.currencyStyle]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.currencyTitleStyle,...this.moduleColorStyle2,...this.currencyStyle]\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyTitleStyle,...this.moduleColorStyle2,...this.currencyStyle]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.bntBgStyle,...this.currencyTitleStyle,...this.moduleColorStyle2,...this.currencyStyle]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n            // 获取组件参数\n            getConfig (data) {},\n            handleSubmit (name) {\n                let obj = {}\n                obj.activeIndex = this.activeIndex\n                obj.data = this.configObj\n                this.add(obj);\n            },\n            ...mapMutations({\n                add: 'admin/mobildConfig/UPDATEARR'\n            })\n        }\n    }\n", null]}