{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCoupon\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCoupon\\index.vue", "mtime": 1650783676000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport { couponListApi, couponCreateApi, couponEditeApi, couponSendApi } from '@/api/marketing';\nimport editFrom from '@/components/from/from';\nimport { formatDate as _formatDate } from '@/utils/validate';\nexport default {\n  name: 'storeCoupon',\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  components: {\n    editFrom: editFrom\n  },\n  data: function data() {\n    return {\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns1: [{\n        title: 'ID',\n        key: 'id',\n        width: 80\n      }, {\n        title: '优惠券名称',\n        key: 'title',\n        minWidth: 150\n      }, {\n        title: '优惠券类型',\n        key: 'type',\n        minWidth: 80\n      }, {\n        title: '面值',\n        slot: 'coupon_price',\n        minWidth: 100\n      }, {\n        title: '最低消费额',\n        key: 'use_min_price',\n        minWidth: 100\n      }, {\n        title: '有效期限(天)',\n        key: 'coupon_time',\n        minWidth: 120\n      }, {\n        title: '排序',\n        key: 'sort',\n        minWidth: 80\n      }, {\n        title: '是否有效',\n        slot: 'status',\n        minWidth: 90\n      }, {\n        title: '添加时间',\n        slot: 'add_time',\n        minWidth: 150\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        minWidth: 170\n      }],\n      tableFrom: {\n        status: '',\n        title: '',\n        page: 1,\n        limit: 15\n      },\n      tableList: [],\n      total: 0,\n      FromData: null\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'left';\n    }\n  }),\n  methods: {\n    // 失效\n    couponInvalid: function couponInvalid(row, tit, num) {\n      var _this = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"marketing/coupon/status/\".concat(row.id),\n        method: 'PUT',\n        ids: ''\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this.$Message.success(res.msg);\n\n        _this.getList();\n      }).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 发布\n    couponSend: function couponSend(row) {\n      var _this2 = this;\n\n      this.$modalForm(couponSendApi(row.id)).then(function () {\n        return _this2.getList();\n      });\n    },\n    // 删除\n    couponDel: function couponDel(row, tit, num) {\n      var _this3 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"marketing/coupon/del/\".concat(row.id),\n        method: 'DELETE',\n        ids: ''\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this3.$Message.success(res.msg);\n\n        _this3.tableList.splice(num, 1);\n      }).catch(function (res) {\n        _this3.$Message.error(res.msg);\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this4 = this;\n\n      this.loading = true;\n      this.tableFrom.status = this.tableFrom.status || '';\n      couponListApi(this.tableFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this4.tableList = data.list;\n                  _this4.total = res.data.count;\n                  _this4.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this4.loading = false;\n\n        _this4.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    changeType: function changeType(data) {\n      this.type = data;\n    },\n    // 添加\n    add: function add() {\n      // this.$modalForm(couponCreateApi()).then(() => this.getList());\n      this.addType(0);\n    },\n    addType: function addType(type) {\n      var _this5 = this;\n\n      couponCreateApi(type).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context2.next = 2;\n                    break;\n                  }\n\n                  return _context2.abrupt(\"return\", _this5.$authLapse(res.data));\n\n                case 2:\n                  _this5.FromData = res.data;\n                  _this5.$refs.edits.modals = true;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this5.$Message.error(res.msg);\n      });\n    },\n    // 编辑\n    edit: function edit(row) {\n      var _this6 = this;\n\n      this.$modalForm(couponEditeApi(row.id)).then(function () {\n        return _this6.getList();\n      });\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 修改成功\n    submitFail: function submitFail() {\n      this.getList();\n    }\n  }\n};", null]}