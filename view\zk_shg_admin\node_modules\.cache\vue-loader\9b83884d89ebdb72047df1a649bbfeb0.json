{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\refund\\index.vue?vue&type=template&id=bad6c156&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\refund\\index.vue", "mtime": 1717468541000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 订单-售后订单 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n      <div class=\"new_card_pd\">\n        <!-- 筛选条件 -->\n        <Form\n          ref=\"pagination\"\n          inline\n          :model=\"pagination\"\n          :label-width=\"labelWidth\"\n          :label-position=\"labelPosition\"\n          @submit.native.prevent\n        >\n          <FormItem label=\"订单状态：\">\n            <Select v-model=\"pagination.refund_type\" class=\"input-add\" @on-change=\"orderSearch\">\n              <Option\n                v-for=\"(item, index) in num\"\n                :value=\"index\"\n                :key=\"index\"\n                >{{ item.name }}</Option\n              >\n            </Select>\n          </FormItem>\n          <FormItem label=\"退款时间：\">\n            <DatePicker\n              :editable=\"false\"\n              @on-change=\"onchangeTime\"\n              :value=\"timeVal\"\n              format=\"yyyy/MM/dd\"\n              type=\"daterange\"\n              placement=\"bottom-start\"\n              placeholder=\"自定义时间\"\n              class=\"input-add\"\n              :options=\"options\"\n            ></DatePicker>\n          </FormItem>\n          <FormItem label=\"订单搜索：\" label-for=\"title\">\n            <Input\n              v-model=\"pagination.order_id\"\n              placeholder=\"请输入订单号\"\n              class=\"input-add mr14\"\n            />\n            <Button type=\"primary\" @click=\"orderSearch()\">查询</Button>\n          </FormItem>\n        </Form>\n      </div>\n    </Card>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n      <!-- 售后订单表格 -->\n      <Table\n        :columns=\"thead\"\n        :data=\"tbody\"\n        ref=\"table\"\n        :loading=\"loading\"\n        highlight-row\n        no-userFrom-text=\"暂无数据\"\n        no-filtered-userFrom-text=\"暂无筛选结果\"\n      >\n        <template slot-scope=\"{ row }\" slot=\"order_id\">\n          <span v-text=\"row.order_id\" style=\"display: block\"></span>\n          <span v-show=\"row.is_del === 1 && row.delete_time == null\" class=\"span-del\"\n            >用户已删除</span\n          >\n        </template>\n\t\t\t\t<template slot-scope=\"{ row }\" slot=\"nickname\">\n\t\t\t\t  <div>{{ row.nickname }}<span style=\"color: #ed4014;\" v-if=\"row.delete_time != null\"> (已注销)</span></div>\n\t\t\t\t</template>\n        <template slot-scope=\"{ row }\" slot=\"user\">\n          <div>用户名：{{ row.nickname }}</div>\n          <div>用户ID：{{ row.uid }}</div>\n        </template>\n        <template slot-scope=\"{ row }\" slot=\"apply_type\">\n          <Tag color=\"blue\" size=\"medium\" v-if=\"row.apply_type == 1\">仅退款</Tag>\n          <Tag color=\"blue\" size=\"medium\" v-if=\"row.apply_type == 2\">退货退款(快递退回)</Tag>\n          <Tag color=\"blue\" size=\"medium\" v-if=\"row.apply_type == 3\">退货退款(到店退货)</Tag>\n          <Tag color=\"blue\" size=\"medium\" v-if=\"row.apply_type == 4\">商家主动退款</Tag>\n        </template>\n        <template slot-scope=\"{ row }\" slot=\"refund_type\">\n          <Tag color=\"blue\" size=\"medium\" v-if=\"[0, 1, 2].includes(row.refund_type)\">待处理</Tag>\n          <Tag color=\"red\" size=\"medium\" v-if=\"row.refund_type == 3\">拒绝退款</Tag>\n          <Tag color=\"blue\" size=\"medium\" v-if=\"row.refund_type == 4\">商品待退货</Tag>\n          <Tag color=\"blue\" size=\"medium\" v-if=\"row.refund_type == 5\">退货待收货</Tag>\n          <Tag color=\"green\" size=\"medium\" v-if=\"row.refund_type == 6\">已退款</Tag>\n        </template>\n        <template slot-scope=\"{ row }\" slot=\"info\">\n          <!-- <div class=\"tabBox\" v-for=\"(val, i) in row._info\" :key=\"i\">\n            <div class=\"tabBox_img\" v-viewer>\n              <img\n                v-lazy=\"val.cart_info.productInfo.attrInfo? val.cart_info.productInfo.attrInfo.image : val.cart_info.productInfo.image\"/>\n            </div>\n            <span class=\"tabBox_tit line2\">\n              {{ val.cart_info.productInfo.store_name + \" | \"}}\n              {{val.cart_info.productInfo.attrInfo? val.cart_info.productInfo.attrInfo.suk : \"\"}}\n            </span>\n            <span class=\"tabBox_pice\">{{ \"￥\" + val.cart_info.truePrice + \" x \" + val.cart_info.cart_num}}</span>\n          </div> -->\n          <Tooltip theme=\"dark\" max-width=\"300\" :delay=\"600\">\n          <div class=\"tabBox\" v-for=\"(val, i) in row._info\" :key=\"i\">\n            <div class=\"tabBox_img\" v-viewer>\n              <img\n                v-lazy=\"\n                  val.cart_info.productInfo.attrInfo\n                    ? val.cart_info.productInfo.attrInfo.image\n                    : val.cart_info.productInfo.image\n                \"\n              />\n            </div>\n            <span class=\"tabBox_tit line1\">\n              <span class=\"font-color-red\" v-if=\"val.cart_info.is_gift\">赠品</span>\n              {{ val.cart_info.productInfo.store_name + \" | \"}}\n              {{val.cart_info.productInfo.attrInfo ? val.cart_info.productInfo.attrInfo.suk: \"\"}}\n            </span>\n          </div>\n          <div slot=\"content\">\n            <div v-for=\"(val, i) in row._info\" :key=\"i\">\n              <p class=\"font-color-red\" v-if=\"val.cart_info.is_gift\">赠品</p>\n              <p>{{ val.cart_info.productInfo.store_name }}</p>\n              <p> {{val.cart_info.productInfo.attrInfo ? val.cart_info.productInfo.attrInfo.suk: \"\"}}</p>\n              <p class=\"tabBox_pice\">{{ \"￥\" + val.cart_info.truePrice + \" x \" + val.cart_info.cart_num }}</p>\n            </div>\n          </div>\n        </Tooltip>\n        </template>\n        <template slot-scope=\"{ row }\" slot=\"statusName\">\n          <Tooltip theme=\"dark\" max-width=\"300\" :delay=\"600\">\n            <div v-html=\"row.refund_reason\" class=\"pt5\"></div>\n            <div slot=\"content\">\n              <div  class=\"pt5\">退款原因：{{row.refund_explain}}</div>\n              <div v-if=\"row.refund_goods_explain\" class=\"pt5\">退货原因：{{row.refund_goods_explain}}</div>\n            </div>\n          </Tooltip>\n          <div class=\"pictrue-box\" v-if=\"row.refund_img\">\n            <div\n              v-viewer\n              v-for=\"(item, index) in row.refund_img || []\"\n              :key=\"index\"\n            >\n              <img class=\"pictrue mr10\" v-lazy=\"item\" :src=\"item\" />\n            </div>\n          </div>\n        </template>\n        <!-- <template slot-scope=\"{ row }\" slot=\"statusGoodName\">\n          <div v-html=\"row.refund_goods_explain\" class=\"pt5\"></div>\n          <div class=\"pictrue-box\" v-if=\"row.refund_goods_img\">\n            <div\n              v-viewer\n              v-for=\"(item, index) in row.refund_goods_img || []\"\n              :key=\"index\"\n            >\n              <img class=\"pictrue mr10\" v-lazy=\"item\" :src=\"item\" />\n            </div>\n          </div>\n        </template> -->\n        <template slot-scope=\"{ row }\" slot=\"action\">\n          <!-- <a\n            @click=\"changeMenu(row, '5')\"\n\t\t\t:disabled=\"openErp\"\n            v-show=\"\n              [1, 2, 5].includes(row.refund_type) &&\n              (parseFloat(row.pay_price) > parseFloat(row.refunded_price) ||\n                row.pay_price == 0)\n            \"\n            >{{ row.refund_type == 2 ? \"同意退货\" : \"立即退款\" }}</a\n          >\n          <Divider\n            type=\"vertical\"\n            v-show=\"\n              [1, 2, 5].includes(row.refund_type) &&\n              (parseFloat(row.pay_price) > parseFloat(row.refunded_price) ||\n                row.pay_price == 0)\n            \"\n          /> -->\n          <a\n              @click=\"changeMenu(row, '5')\"\n              :disabled=\"openErp\"\n              v-show=\"\n              (row.apply_type == 1 || row.refund_type == 5 || (row.refund_type == 4 && row.apply_type == 3)) &&\n              ![3, 6].includes(row.refund_type) &&\n              (parseFloat(row.pay_price) > parseFloat(row.refunded_price) || row.pay_price == 0)\n            \"\n          >立即退款</a\n          >\n          <Divider\n              type=\"vertical\"\n              v-show=\"\n              (row.apply_type == 1 || row.refund_type == 5 || (row.refund_type == 4 && row.apply_type == 3)) &&\n              ![3, 6].includes(row.refund_type) &&\n              (parseFloat(row.pay_price) > parseFloat(row.refunded_price) || row.pay_price == 0)\n            \"\n          />\n          <a\n              @click=\"changeMenu(row, '55')\"\n              :disabled=\"openErp\"\n              v-show=\"\n              [2, 3].includes(row.apply_type) && [0, 1, 2].includes(row.refund_type)\"\n          >同意退货</a\n          >\n          <Divider\n              type=\"vertical\"\n              v-show=\"[2, 3].includes(row.apply_type) && [0, 1, 2].includes(row.refund_type)\"\n          />\n          <a @click=\"changeMenu(row, '2')\">订单详情</a>\n          \n        </template>\n      </Table>\n      <div class=\"acea-row row-right page\">\n        <Page\n          :total=\"total\"\n          :current=\"pagination.page\"\n          show-elevator\n          show-total\n          @on-change=\"pageChange\"\n          :page-size=\"pagination.limit\"\n        />\n      </div>\n    </Card>\n    <!-- 编辑 退款 退积分 不退款-->\n    <edit-from\n      ref=\"edits\"\n      :FromData=\"FromData\"\n      @submitFail=\"submitFail\"\n    ></edit-from>\n    <!-- 详情 -->\n    <details-from\n      ref=\"detailss\"\n      :orderDatalist=\"orderDatalist\"\n      :orderId=\"orderId\"\n      :rowActive=\"rowActive\"\n\t  :openErp=\"openErp\"\n    ></details-from>\n    <!-- 备注 -->\n    <order-remark\n      ref=\"remarks\"\n      remarkType=\"refund\"\n      :orderId=\"orderId\"\n      @submitFail=\"submitFail\"\n    ></order-remark>\n    <!-- 记录 -->\n    <order-record ref=\"record\"></order-record>\n  </div>\n", null]}