{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_goods_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_goods_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import { getCategory, getProduct } from '@/api/diy'\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    export default {\n        name: 'c_home_goods_list',\n        componentsName: 'home_goods_list',\n        cname: '��Ʒ�б�',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ],\n\t\t\t\toneContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'styleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleGoods'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_select,\n\t\t\t\t\t\tconfigNme: 'typeConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContent1:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_goods,\n\t\t\t\t\t    configNme: 'goodsList'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContent2:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_brand,\n\t\t\t\t\t    configNme: 'brandList'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'numberConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'goodsSort'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContent3:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_classify,\n\t\t\t\t\t    configNme: 'classList'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'numberConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'goodsSort'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContent4:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_goods_label,\n\t\t\t\t\t\tconfigNme: 'goodsLabel'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'numberConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'goodsSort'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleContents'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_checkbox,\n\t\t\t\t\t    configNme: 'checkboxInfo'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCart'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'cartConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeContent1:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_button_img,\n\t\t\t\t\t    configNme: 'bntStyleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'bntConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'filletImg'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'goodsName'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle1:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'goodsNameColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'goodsPriceColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle2:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'soldNumColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle3:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'scoreColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCart'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneCartConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle1:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bntBgColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyTitleStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tmoduleColorStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n                setUp: 0,\n                type: 0,\n\t\t\t\ttype2: 0,\n\t\t\t\ttype3: 0,\n\t\t\t\ttype4: 0,\n\t\t\t\ttype5: 0,\n                lockStatus: false\n            }\n        },\n        watch: {\n            num (nVal) {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n                if(!value.selectConfig.list || !value.selectConfig.list[0].value){\n                    this.getCategory();\n                }\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n                    this.setUp = nVal;\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n                        this.getRComContent(arr,this.type,this.type2,this.type3);\n                    } else {\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type3,this.type4,this.type5);\n                    }\n                },\n                deep: true\n            },\n            'configObj.styleConfig.tabVal': {\n                handler (nVal, oVal) {\n                    this.type = nVal;\n                    var arr = [this.rCom[0]];\n                    if(this.setUp === 0){\n\t\t\t\t\t\tthis.getRComContent(arr,nVal,this.type2,this.type3);\n                    }else{\n\t\t\t\t\t\tthis.getRComStyle(arr,nVal,this.type3,this.type4,this.type5);\n\t\t\t\t\t}\n                },\n                deep: true\n            },\n\t\t\t'configObj.typeConfig.activeValue': {\n\t\t\t    handler (nVal, oVal) {\n\t\t\t        this.type2 = nVal;\n\t\t\t        var arr = [this.rCom[0]];\n\t\t\t        if(this.setUp === 0){\n\t\t\t\t\t\tthis.getRComContent(arr,this.type,nVal,this.type3);\n\t\t\t        }\n\t\t\t    },\n\t\t\t    deep: true\n\t\t\t},\n\t\t\t'configObj.cartConfig.tabVal': {\n\t\t\t    handler (nVal, oVal) {\n\t\t\t        this.type3 = nVal;\n\t\t\t        var arr = [this.rCom[0]];\n\t\t\t        if(this.setUp === 0){\n\t\t\t\t\t\tthis.getRComContent(arr,this.type,this.type2,nVal);\n\t\t\t        }else{\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,nVal,this.type4,this.type5);\n\t\t\t\t\t}\n\t\t\t    },\n\t\t\t    deep: true\n\t\t\t},\n\t\t\t'configObj.toneConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type4 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type3,nVal,this.type5);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.toneCartConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type5 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type3,this.type4,nVal);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n                this.getCategory();\n            })\n        },\n        methods: {\n\t\t\tgetRComContent(arr,type,type2,type3){\n\t\t\t\tif(type == 3){\n\t\t\t\t\tif(type2 == 1){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent1,...this.twoContent]\n\t\t\t\t\t}else if(type2 == 2){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent2,...this.twoContent]\n\t\t\t\t\t}else if(type2 == 3){\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent3,...this.twoContent]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent4,...this.twoContent]\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tif(type2 == 1){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent1,...this.twoContent,...this.threeContent,...this.threeContent1]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent1,...this.twoContent,...this.threeContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}else if(type2 == 2){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent2,...this.twoContent,...this.threeContent,...this.threeContent1]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent2,...this.twoContent,...this.threeContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}else if(type2 == 3){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent3,...this.twoContent,...this.threeContent,...this.threeContent1]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent3,...this.twoContent,...this.threeContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent4,...this.twoContent,...this.threeContent,...this.threeContent1]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContent4,...this.twoContent,...this.threeContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetRComStyle(arr,type,type3,type4,type5){\n\t\t\t\tlet obj4 = [],currencyStyle = []\n\t\t\t\tif(type4){\n\t\t\t\t\tif(type == 1 || type == 4){\n\t\t\t\t\t\tobj4 = [...this.oneStyle1,...this.oneStyle2]\n\t\t\t\t\t\tcurrencyStyle = [...this.currencyTitleStyle,...this.currencyStyle]\n\t\t\t\t\t}else if(type == 0){\n\t\t\t\t\t\tobj4 = [...this.oneStyle1,...this.oneStyle2,...this.oneStyle3]\n\t\t\t\t\t\tcurrencyStyle = [...this.currencyTitleStyle,...this.currencyStyle]\n\t\t\t\t\t}else if(type == 2 || type == 3){\n\t\t\t\t\t\tobj4 = [...this.oneStyle1]\n\t\t\t\t\t\tcurrencyStyle = [...this.currencyTitleStyle,...this.moduleColorStyle,...this.currencyStyle]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tobj4 = [...this.oneStyle1,...this.oneStyle2]\n\t\t\t\t\t\tcurrencyStyle = [...this.currencyTitleStyle,...this.moduleColorStyle,...this.currencyStyle]\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tif(type == 0 || type == 1 || type == 4){\n\t\t\t\t\t\tcurrencyStyle = [...this.currencyTitleStyle,...this.currencyStyle]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tcurrencyStyle = [...this.currencyTitleStyle,...this.moduleColorStyle,...this.currencyStyle]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet obj5 = []\n\t\t\t\tif(type != 3){\n\t\t\t\t\tif(type5){\n\t\t\t\t\t\tobj5 = [...this.twoStyle,...this.twoStyle1]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tobj5 = [...this.twoStyle]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(type3 == 0){\n\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...obj4,...obj5,...currencyStyle]\n\t\t\t\t}else{\n\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...obj4,...currencyStyle]\n\t\t\t\t}\n\t\t\t},\n            getConfig (data,name) {\n                if (name != 'radio' && !data.name && data == 1) {\n                    this.configObj.goodsList.list = []\n                    return\n                }\n                if (name != 'radio' && !data.name && data == 0 && !this.configObj.classList.classVal.length) {\n\t\t\t\t\tthis.configObj.goodsList.list = []\n                    return\n                }\n                // if( data.name=='radio'){\n                //     return;\n                // }\n\t\t\t\tlet type = this.configObj.typeConfig.activeValue;\n\t\t\t\tlet dataObj = {\n\t\t\t\t\tpage: 1,\n\t\t\t\t\tlimit: this.configObj.numberConfig.val,\n\t\t\t\t\tpriceOrder: this.configObj.goodsSort.tabVal == 2 ? 'desc' : '',\n\t\t\t\t\tsalesOrder: this.configObj.goodsSort.tabVal == 1 ? 'desc' : '',\n\t\t\t\t}\n\t\t\t\tif(type == 1){\n\t\t\t\t\tthis.configObj.productList.list = []\n\t\t\t\t\treturn\n\t\t\t\t}else if(type == 2){\n\t\t\t\t\tdataObj.brand_id = this.configObj.brandList.brandVal;\n\t\t\t\t}else if(type == 3){\n\t\t\t\t\tdataObj.id = this.configObj.classList.classVal;\n\t\t\t\t}else {\n\t\t\t\t\tdataObj.store_label_id = this.configObj.goodsLabel.activeValue;\n\t\t\t\t}\n                getProduct(dataObj).then(res => {\n                    this.configObj.productList.list = res.data;\n                })\n            },\n            getCategory () {\n                getCategory().then(res => {\n                    this.$set(this.configObj.selectConfig,'list',res.data)\n                })\n            }\n        }\n    }\n", null]}