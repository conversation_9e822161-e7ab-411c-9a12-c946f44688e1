{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\index.vue", "mtime": 1685091240000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {getGroupChatList} from \"@/api/work\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"\",\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      formInline:{},\n      options: timeOptions,\n      columns1: [\n        {\n          title: \"二维码\",\n          slot: \"qr_code\",\n          minWidth: 80,\n          align: 'center'\n        },\n        {\n          title: \"二维码名称\",\n          key: \"name\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"群名称\",\n          key: \"group_name\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"群聊\",\n          slot: \"chat_list\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"自动创建\",\n          slot: \"auth_group_chat\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"标签\",\n          slot: \"label_list\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"创建时间\",\n          key: \"create_time\",\n          minWidth: 130,\n          align: 'center'\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          // fixed: \"right\",\n          minWidth: 130,\n          align: 'center'\n        },\n      ],\n      tableData: [],\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      timeVal:[],\n      tableFrom: {\n        name:\"\",\n        create_time:\"\",\n        page: 1,\n        limit: 15,\n      },\n      total: 0,\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    },\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    getList(){\n      this.loading = true;\n      getGroupChatList(this.tableFrom).then(res=>{\n        this.tableData = res.data;\n        this.loading = false;\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n        this.loading = false;\n      })\n    },\n    search(){\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    onchangeTime(e){\n      this.timeVal = e;\n      this.tableFrom.create_time = this.timeVal.join(\"-\");\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    downItem(row,index){\n      let link = document.createElement('a')\n      let url =  row.qr_code;  //codeIMG  要下载的路径\n      // 这里是将url转成blob地址，\n      fetch(url).then(res => res.blob()).then(blob => { \n        // 将链接地址字符内容转变成blob地址\n        link.href = URL.createObjectURL(blob)\n        link.download = row.name  \n         document.body.appendChild(link)\n         link.click()  \n     })\n    },\n    editData(row,index){\n      this.$router.push({ path: this.roterPre + \"/work/addAuthGroup/\" + row.id });\n    },\n    delItem(row,index){\n      let delfromData = {\n        title: '删除该自动拉群',\n        num:index,\n        url: `work/group_chat_auth/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.tableData.list.splice(index, 1);\n          if (!this.tableData.list.length) {\n            this.tableFrom.page =\n                this.tableFrom.page == 1 ? 1 : this.tableFrom.page - 1;\n          }\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index){\n      this.tableFrom.page = index;\n      this.getList();\n    }\n  },\n};\n", null]}