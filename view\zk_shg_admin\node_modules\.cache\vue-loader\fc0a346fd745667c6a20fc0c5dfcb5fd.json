{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\autoSend.vue?vue&type=template&id=ac4c6a60&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\autoSend.vue", "mtime": 1676252006000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<Modal\n  v-model=\"modals\"\n  scrollable\n  title=\"订单发送货\"\n  class=\"order_box\"\n  :closable=\"false\"\n  class-name=\"vertical-center-modal\"\n>\n  <Form\n    ref=\"formItem\"\n    :model=\"formItem\"\n    :label-width=\"100\"\n    @submit.native.prevent\n  >\n    <FormItem label=\"选择类型：\">\n      <RadioGroup v-model=\"formItem.type\" @on-change=\"changeRadio\">\n        <Radio label=\"1\">打印电子面单</Radio>\n        <Radio label=\"2\">送货</Radio>\n        <Radio label=\"3\">虚拟</Radio>\n      </RadioGroup>\n    </FormItem>\n    <div v-show=\"formItem.type === '1'\">\n      <FormItem label=\"快递公司：\" required>\n        <Select\n          v-model=\"formItem.delivery_name\"\n          filterable\n          placeholder=\"请选择快递公司\"\n          class=\"input-add\"\n          @on-change=\"expressChange\"\n        >\n          <Option\n            v-for=\"(item, i) in express\"\n            :value=\"item.value\"\n            :key=\"item.value\"\n            >{{ item.value }}</Option\n          >\n        </Select>\n      </FormItem>\n      <template v-if=\"formItem.type === '1'\">\n        <FormItem label=\"电子面单：\" required class=\"express_temp_id\">\n          <Select\n            v-model=\"formItem.express_temp_id\"\n            placeholder=\"请选择电子面单\"\n           class=\"input-add\"\n            @on-change=\"expressTempChange\"\n          >\n            <Option\n              v-for=\"(item, i) in expressTemp\"\n              :value=\"item.temp_id\"\n              :key=\"i\"\n              >{{ item.title }}</Option\n            >\n          </Select>\n          <Button v-if=\"formItem.express_temp_id\" type=\"text\" @click=\"preview\"\n            >预览</Button\n          >\n        </FormItem>\n        <FormItem label=\"寄件人姓名：\" required>\n          <Input\n            v-model=\"formItem.to_name\"\n            placeholder=\"请输入寄件人姓名\"\n            class=\"input-add\"\n          ></Input>\n        </FormItem>\n        <FormItem label=\"寄件人电话：\" required>\n          <Input\n            v-model=\"formItem.to_tel\"\n            placeholder=\"请输入寄件人电话\"\n            class=\"input-add\"\n          ></Input>\n        </FormItem>\n        <FormItem label=\"寄件人地址：\" required>\n          <Input\n            v-model=\"formItem.to_addr\"\n            placeholder=\"请输入寄件人地址\"\n            class=\"input-add\"\n          ></Input>\n        </FormItem>\n      </template>\n    </div>\n    <div v-show=\"formItem.type === '2'\">\n      <FormItem label=\"送货人：\" required>\n        <Select\n          v-model=\"formItem.sh_delivery\"\n          placeholder=\"请选择送货人\"\n          class=\"input-add\"\n          @on-change=\"shDeliveryChange\"\n        >\n          <Option v-for=\"(item, i) in deliveryList\" :value=\"item.id\" :key=\"i\"\n            >{{ item.wx_name }}（{{ item.phone }}）</Option\n          >\n        </Select>\n      </FormItem>\n    </div>\n    <div v-show=\"formItem.type === '3'\">\n      <FormItem label=\"备注：\">\n        <Input\n          v-model=\"formItem.fictitious_content\"\n          type=\"textarea\"\n          :autosize=\"{ minRows: 2, maxRows: 5 }\"\n          placeholder=\"备注\"\n         class=\"input-add\"\n        ></Input>\n      </FormItem>\n    </div>\n  </Form>\n  <div slot=\"footer\">\n    <Button @click=\"cancel\">取消</Button>\n    <Button type=\"primary\" @click=\"putSend\">提交</Button>\n  </div>\n  <div ref=\"viewer\" v-viewer v-show=\"temp\">\n    <img :src=\"temp.pic\" class=\"display-add \"/>\n  </div>\n</Modal>\n", null]}