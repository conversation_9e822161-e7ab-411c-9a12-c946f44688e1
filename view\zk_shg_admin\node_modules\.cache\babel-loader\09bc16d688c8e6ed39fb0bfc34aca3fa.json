{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\userExtract\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\userExtract\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport cardsData from \"@/components/cards/cards\";\nimport searchFrom from \"@/components/publicSearchFrom\";\nimport { mapState } from \"vuex\";\nimport { cashListApi, cashEditApi, refuseApi } from \"@/api/finance\";\nimport { formatDate as _formatDate } from \"@/utils/validate\";\nimport editFrom from \"@/components/from/from\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: \"cashApply\",\n  components: {\n    cardsData: cardsData,\n    searchFrom: searchFrom,\n    editFrom: editFrom\n  },\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    }\n  },\n  data: function data() {\n    return {\n      modal_loading: false,\n      options: timeOptions,\n      fail_msg: {\n        message: \"输入信息不完整或有误!\"\n      },\n      modals: false,\n      total: 0,\n      cardLists: [],\n      loading: false,\n      columns: [{\n        title: \"ID\",\n        key: \"id\",\n        width: 80\n      }, {\n        title: \"用户信息\",\n        slot: \"nickname\",\n        minWidth: 180\n      }, {\n        title: \"提现到账金额\",\n        slot: \"extract_price\",\n        minWidth: 90\n      }, {\n        title: \"手续费\",\n        slot: \"extract_fee\",\n        minWidth: 90\n      }, {\n        title: \"提现方式\",\n        slot: \"extract_type\",\n        minWidth: 150\n      }, {\n        title: \"收款码\",\n        slot: \"qrcode_url\",\n        minWidth: 150\n      }, {\n        title: \"添加时间\",\n        slot: \"add_time\",\n        minWidth: 100\n      }, {\n        title: \"备注\",\n        key: \"mark\",\n        minWidth: 100\n      }, {\n        title: \"审核状态\",\n        slot: \"status\",\n        minWidth: 180\n      }, {\n        title: \"操作\",\n        slot: \"createModalFrame\",\n        fixed: \"right\",\n        width: 100\n      }],\n      tabList: [],\n      fromList: {\n        title: \"选择时间\",\n        custom: true,\n        fromTxt: [{\n          text: \"全部\",\n          val: \"\"\n        }, {\n          text: \"昨天\",\n          val: \"yesterday\"\n        }, {\n          text: \"今天\",\n          val: \"today\"\n        }, {\n          text: \"本周\",\n          val: \"week\"\n        }, {\n          text: \"本月\",\n          val: \"month\"\n        }, {\n          text: \"本季度\",\n          val: \"quarter\"\n        }, {\n          text: \"本年\",\n          val: \"year\"\n        }]\n      },\n      treeData: {\n        withdrawal: [{\n          title: \"全部\",\n          value: \"\"\n        }, {\n          title: \"未通过\",\n          value: -1\n        }, {\n          title: \"申请中\",\n          value: 0\n        }, {\n          title: \"已通过\",\n          value: 1\n        }],\n        payment: [{\n          title: \"全部\",\n          value: \"\"\n        }, {\n          title: \"支付宝\",\n          value: \"alipay\"\n        }, {\n          title: \"银行卡\",\n          value: \"bank\"\n        }, {\n          title: \"微信\",\n          value: \"wx\"\n        } // {\n        // \ttitle: '提现到余额',\n        // \tvalue: 'balance'\n        // }\n        ]\n      },\n      formValidate: {\n        status: \"\",\n        extract_type: \"\",\n        nireid: \"\",\n        data: \"\",\n        page: 1,\n        limit: 20\n      },\n      extractStatistics: {},\n      timeVal: [],\n      FromData: null,\n      extractId: 0\n    };\n  },\n  watch: {\n    $route: function $route() {\n      if (this.$route.fullPath === \"/finance/user_extract/index?status=0\") {\n        this.getPath();\n      }\n    }\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  // created() {\n  //     if(this.$route.query.key == 0){\n  //         this.formValidate.status = parseInt(this.$route.query.key)\n  //     }\n  // },\n  mounted: function mounted() {\n    if (this.$route.fullPath === \"/finance/user_extract/index?status=0\") {\n      this.getPath();\n    } else {\n      this.getList();\n    } // this.getList();\n\n  },\n  methods: {\n    getPath: function getPath() {\n      this.formValidate.page = 1;\n      this.formValidate.status = parseInt(this.$route.query.status);\n      this.getList();\n    },\n    // 无效\n    invalid: function invalid(row) {\n      this.extractId = row.id;\n      this.modals = true;\n    },\n    // 确定\n    oks: function oks() {\n      var _this = this;\n\n      this.modal_loading = true;\n      refuseApi(this.extractId, this.fail_msg).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this.$Message.success(res.msg);\n\n                  _this.modal_loading = false;\n                  _this.modals = false;\n\n                  _this.getList();\n\n                case 4:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 通过\n    adopt: function adopt(row, tit, num) {\n      var _this2 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"finance/extract/adopt/\".concat(row.id),\n        method: \"put\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this2.$Message.success(res.msg);\n\n        _this2.getList();\n      }).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.formValidate.page = 1;\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 选择\n    selChange: function selChange() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    reset: function reset() {\n      this.formValidate = {\n        status: \"\",\n        extract_type: \"\",\n        nireid: \"\",\n        data: \"\",\n        page: 1,\n        limit: 20\n      };\n      this.timeVal = []; // this.$refs.formValidate.resetFields()\n\n      this.getList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this3 = this;\n\n      this.loading = true;\n      cashListApi(this.formValidate).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  data = res.data;\n                  _this3.tabList = data.list.list;\n                  _this3.total = data.list.count;\n                  _this3.extractStatistics = data.extract_statistics;\n                  _this3.cardLists = [{\n                    col: 6,\n                    count: _this3.extractStatistics.price,\n                    name: \"待提现金额\",\n                    className: \"md-basket\"\n                  }, {\n                    col: 6,\n                    count: _this3.extractStatistics.brokerage_count,\n                    name: \"佣金总金额\",\n                    className: \"md-pricetags\"\n                  }, {\n                    col: 6,\n                    count: _this3.extractStatistics.priced,\n                    name: \"已提现金额\",\n                    className: \"md-cash\"\n                  }, {\n                    col: 6,\n                    count: _this3.extractStatistics.brokerage_not,\n                    name: \"未提现金额\",\n                    className: \"ios-cash\"\n                  }];\n                  _this3.loading = false;\n\n                case 6:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this3.loading = false;\n\n        _this3.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 编辑\n    edit: function edit(row) {\n      var _this4 = this;\n\n      cashEditApi(row.id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref3 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee3(res) {\n          return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n            while (1) {\n              switch (_context3.prev = _context3.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context3.next = 2;\n                    break;\n                  }\n\n                  return _context3.abrupt(\"return\", _this4.$authLapse(res.data));\n\n                case 2:\n                  _this4.FromData = res.data;\n                  _this4.$refs.edits.modals = true;\n\n                case 4:\n                case \"end\":\n                  return _context3.stop();\n              }\n            }\n          }, _callee3);\n        }));\n\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this4.$Message.error(res.msg);\n      });\n    },\n    // 编辑提交成功\n    submitFail: function submitFail() {\n      this.getList();\n    }\n  }\n};", null]}