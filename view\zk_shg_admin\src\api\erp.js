// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import request from '@/plugins/request';

/**
 * @description erp设置
 * @param {Object} param data {Object} 传值参数
 */
export function erpConfig() {
  return request({
    url: 'erp/config',
    method: 'get'
  });
}

/**
 * @description erp门店列表
 * @param {Object} param data {Object} 传值参数
 */
export function erpShop(data) {
  return request({
    url: 'store/erp/shop',
    method: 'get',
    params: data
  });
}

/**
 * @description 导入erp
 * @param {Object} param data {Object} 传值参数
 */
export function erpProduct(data) {
  return request({
    url: 'product/import_erp_product',
    method: 'post',
    data
  });
}

// =========================== 库存管理相关接口 ===========================

/**
 * @description 获取库存列表
 * @param {Object} data 查询参数
 */
export function getInventoryList(data) {
  return request({
    url: 'erp/inventory/list',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取库存统计
 * @param {Object} data 查询参数
 */
export function getInventoryStatistics(data) {
  return request({
    url: 'erp/inventory/statistics',
    method: 'get',
    params: data
  });
}

/**
 * @description 调整库存
 * @param {Object} data 调整参数
 */
export function adjustInventory(data) {
  return request({
    url: 'erp/inventory/adjust',
    method: 'post',
    data
  });
}

/**
 * @description 批量调整库存
 * @param {Object} data 批量调整参数
 */
export function batchAdjustInventory(data) {
  return request({
    url: 'erp/inventory/batch_adjust',
    method: 'post',
    data
  });
}

/**
 * @description 设置库存预警
 * @param {Object} data 预警参数
 */
export function setInventoryWarning(data) {
  return request({
    url: 'erp/inventory/warning',
    method: 'post',
    data
  });
}

/**
 * @description 获取库存变动记录
 * @param {Object} data 查询参数
 */
export function getInventoryRecord(data) {
  return request({
    url: 'erp/inventory/record',
    method: 'get',
    params: data
  });
}

/**
 * @description 导出库存数据
 * @param {Object} data 导出参数
 */
export function exportInventory(data) {
  return request({
    url: 'erp/inventory/export',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

/**
 * @description 获取预警商品列表
 * @param {Object} data 查询参数
 */
export function getWarningList(data) {
  return request({
    url: 'erp/inventory/warning',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取门店选项
 */
export function getStoreOptions() {
  return request({
    url: 'erp/inventory/store_options',
    method: 'get'
  });
}

/**
 * @description 获取分类选项
 */
export function getCategoryOptions() {
  return request({
    url: 'erp/inventory/product_options',
    method: 'get'
  });
}

/**
 * @description 获取商品销售统计
 * @param {Object} data 查询参数
 */
export function getSalesStatistics(data) {
  return request({
    url: 'erp/inventory/sales_statistics',
    method: 'get',
    params: data
  });
}

/**
 * @description 导出商品销售统计
 * @param {Object} data 查询参数
 */
export function exportSalesStatistics(data) {
  return request({
    url: 'erp/inventory/export_statistics',
    method: 'get',
    params: data
  });
}

// =========================== 调拨管理相关接口 ===========================

/**
 * @description 获取调拨单列表
 * @param {Object} data 查询参数
 */
export function getTransferList(data) {
  return request({
    url: 'admin/erp/transfer/list',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取调拨单详情
 * @param {Number} id 调拨单ID
 */
export function getTransferDetail(id) {
  return request({
    url: `admin/erp/transfer/detail/${id}`,
    method: 'get'
  });
}

/**
 * @description 审核调拨单
 * @param {Object} data 审核参数
 */
export function approveTransfer(data) {
  return request({
    url: 'admin/erp/transfer/approve',
    method: 'post',
    data
  });
}

/**
 * @description 批量审核调拨单
 * @param {Object} data 批量审核参数
 */
export function batchApproveTransfer(data) {
  return request({
    url: 'admin/erp/transfer/batch_approve',
    method: 'post',
    data
  });
}

/**
 * @description 执行调拨
 * @param {Object} data 执行参数
 */
export function executeTransfer(data) {
  return request({
    url: 'admin/erp/transfer/execute',
    method: 'post',
    data
  });
}

/**
 * @description 强制执行调拨
 * @param {Object} data 强制执行参数
 */
export function forceExecuteTransfer(data) {
  return request({
    url: 'admin/erp/transfer/force_execute',
    method: 'post',
    data
  });
}

/**
 * @description 批量执行调拨
 * @param {Object} data 批量执行参数
 */
export function batchExecuteTransfer(data) {
  return request({
    url: 'admin/erp/transfer/batch_execute',
    method: 'post',
    data
  });
}

/**
 * @description 删除调拨单
 * @param {Number} id 调拨单ID
 */
export function deleteTransfer(id) {
  return request({
    url: `admin/erp/transfer/delete/${id}`,
    method: 'delete'
  });
}

/**
 * @description 获取调拨统计
 * @param {Object} data 查询参数
 */
export function getTransferStatistics(data) {
  return request({
    url: 'admin/erp/transfer/statistics',
    method: 'get',
    params: data
  });
}

/**
 * @description 导出调拨数据
 * @param {Object} data 导出参数
 */
export function exportTransfer(data) {
  return request({
    url: 'admin/erp/transfer/export',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

// =========================== 报表分析相关接口 ===========================

/**
 * @description 获取库存报表
 * @param {Object} data 查询参数
 */
export function getInventoryReport(data) {
  return request({
    url: 'admin/erp/report/inventory',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取调拨报表
 * @param {Object} data 查询参数
 */
export function getTransferReport(data) {
  return request({
    url: 'admin/erp/report/transfer',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取预警报表
 * @param {Object} data 查询参数
 */
export function getWarningReport(data) {
  return request({
    url: 'admin/erp/report/warning',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取数据分析
 * @param {Object} data 查询参数
 */
export function getDataAnalysis(data) {
  return request({
    url: 'admin/erp/report/analysis',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取门店库存报表
 * @param {Object} data 查询参数
 */
export function getStoreInventoryReport(data) {
  return request({
    url: 'admin/erp/report/store_inventory',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取调拨效率报表
 * @param {Object} data 查询参数
 */
export function getTransferEfficiencyReport(data) {
  return request({
    url: 'admin/erp/report/transfer_efficiency',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取热门商品分析
 * @param {Object} data 查询参数
 */
export function getHotProductAnalysis(data) {
  return request({
    url: 'admin/erp/report/hot_products',
    method: 'get',
    params: data
  });
}

/**
 * @description 获取库存趋势数据
 * @param {Object} data 查询参数
 */
export function getInventoryTrend(data) {
  return request({
    url: 'admin/erp/report/inventory_trend',
    method: 'get',
    params: data
  });
}

/**
 * @description 导出综合报表
 * @param {Object} data 导出参数
 */
export function exportReport(data) {
  return request({
    url: 'admin/erp/report/export',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

// =========================== 基础数据接口 ===========================

/**
 * @description 获取门店列表
 */
export function getStoreList() {
  return request({
    url: 'erp/inventory/store_options',
    method: 'get'
  });
}

/**
 * @description 获取商品分类列表
 */
export function getCategoryList() {
  return request({
    url: 'erp/inventory/product_options',
    method: 'get'
  });
}

/**
 * @description 获取商品列表
 * @param {Object} data 查询参数
 */
export function getProductList(data) {
  return request({
    url: 'admin/erp/product/list',
    method: 'get',
    params: data
  });
}

// 导出所有库存管理相关的API
export const inventoryApi = {
  getInventoryList,
  getInventoryStatistics,
  getStatistics: getInventoryStatistics, // 添加别名
  adjustInventory,
  adjustStock: adjustInventory, // 添加别名
  batchAdjustInventory,
  setInventoryWarning,
  setWarning: setInventoryWarning, // 添加别名
  getInventoryRecord,
  getStockRecord: getInventoryRecord, // 添加别名
  exportInventory,
  getWarningList,
  getStoreList,
  getCategoryList,
  getProductList,
  // 新增的销售统计相关API
  getStoreOptions,
  getCategoryOptions,
  getSalesStatistics,
  exportSalesStatistics
};

// 导出所有调拨管理相关的API
export const transferApi = {
  getTransferList,
  getTransferDetail,
  approveTransfer,
  batchApproveTransfer,
  executeTransfer,
  forceExecuteTransfer,
  batchExecuteTransfer,
  deleteTransfer,
  getTransferStatistics,
  exportTransfer,
  getStoreList
};

// 导出所有报表分析相关的API
export const reportApi = {
  getInventoryReport,
  getTransferReport,
  getWarningReport,
  getDataAnalysis,
  getStoreInventoryReport,
  getTransferEfficiencyReport,
  getHotProductAnalysis,
  getInventoryTrend,
  exportReport
};
