{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue", "mtime": 1728874543955}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { formatDate } from '@/utils/validate';\nimport userLabel from \"../../../components/userLabel\";\nimport labelList from \"@/components/labelList\";\nimport { mapState } from \"vuex\";\nimport expandRow from \"./tableExpand.vue\";\nimport {\n  userList,\n  getUserData,\n  isShowApi,\n  editOtherApi,\n  giveLevelApi,\n  userSetGroup,\n  userGroupApi,\n  levelListApi,\n  userSetLabelApi,\n  userLabelApi,\n  userSynchro,\n  getUserSaveForm,\n  giveLevelTimeApi,\n  extendInfo,\n  batchProcess\n} from \"@/api/user\";\nimport { agentSpreadApi } from \"@/api/agent\";\nimport editFrom from \"../../../components/from/from\";\nimport sendFrom from \"@/components/sendCoupons/index\";\nimport userDetails from \"./handle/userDetails\";\nimport newsCategory from \"@/components/newsCategory/index\";\nimport city from \"@/utils/city\";\nimport customerInfo from \"@/components/customerInfo\";\nexport default {\n  name: \"user_list\",\n  filters: {\n    formatDate (time) {\n      if (time !== 0) {\n        let date = new Date(time * 1000);\n        return formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  components: {\n    expandRow,\n    editFrom,\n    sendFrom,\n    userDetails,\n    newsCategory,\n    customerInfo,\n    userLabel,\n    labelList,\n  },\n  data() {\n    return {\n      dataLabel: [],\n      labelListShow: false,\n      labelShow: false,\n      customerShow: false,\n      promoterShow: false,\n      labelActive: {\n        uid: 0,\n      },\n      formInline: {\n        uid: 0,\n        spread_uid: 0,\n        image: \"\",\n      },\n      options: {\n        shortcuts: [\n          {\n            text: \"今天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                  new Date(\n                      new Date().getFullYear(),\n                      new Date().getMonth(),\n                      new Date().getDate()\n                  )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"昨天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                  start.setTime(\n                      new Date(\n                          new Date().getFullYear(),\n                          new Date().getMonth(),\n                          new Date().getDate() - 1\n                      )\n                  )\n              );\n              end.setTime(\n                  end.setTime(\n                      new Date(\n                          new Date().getFullYear(),\n                          new Date().getMonth(),\n                          new Date().getDate() - 1\n                      )\n                  )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"最近7天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                  start.setTime(\n                      new Date(\n                          new Date().getFullYear(),\n                          new Date().getMonth(),\n                          new Date().getDate() - 6\n                      )\n                  )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"最近30天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                  start.setTime(\n                      new Date(\n                          new Date().getFullYear(),\n                          new Date().getMonth(),\n                          new Date().getDate() - 29\n                      )\n                  )\n              );\n              return [start, end];\n            },\n          },\n\t\t  {\n\t\t    text: \"上月\",\n\t\t    value() {\n\t\t      const end = new Date();\n\t\t      const start = new Date();\n\t\t  \tconst day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n\t\t      start.setTime(\n\t\t        start.setTime(\n\t\t          new Date(new Date().getFullYear(), new Date().getMonth()-1, 1)\n\t\t        )\n\t\t      );\n\t\t  \tend.setTime(\n\t\t  \t  end.setTime(\n\t\t  \t    new Date(new Date().getFullYear(), new Date().getMonth()-1, day)\n\t\t  \t  )\n\t\t  \t);\n\t\t      return [start, end];\n\t\t    },\n\t\t  },\n          {\n            text: \"本月\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                  start.setTime(\n                      new Date(new Date().getFullYear(), new Date().getMonth(), 1)\n                  )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"本年\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                  start.setTime(new Date(new Date().getFullYear(), 0, 1))\n              );\n              return [start, end];\n            },\n          },\n        ],\n      },\n      collapse: false,\n      headeType: \"-1\",\n      headeNum: [\n        { type: \"-1\", name: \"全部\" },\n        { type: \"wechat\", name: \"微信公众号\" },\n        { type: \"routine\", name: \"微信小程序\" },\n        { type: \"h5\", name: \"H5\" },\n        { type: \"pc\", name: \"PC\" },\n        { type: \"app\", name: \"APP\" },\n      ],\n      address: [],\n      addresData: city,\n      isShowSend: true,\n      modal13: false,\n      maxCols: 4,\n      scrollerHeight: \"600\",\n      contentTop: \"130\",\n      contentWidth: \"98%\",\n      // grid: {\n      //   xl: 8,\n      //   lg: 8,\n      //   md: 12,\n      //   sm: 24,\n      //   xs: 24,\n      // },\n      grid2: {\n        xl: 18,\n        lg: 16,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      loading: false,\n      total: 0,\n      userFrom: {\n        label_id: \"\",\n        user_type: \"\",\n        status: \"\",\n        sex: \"\",\n        is_promoter: \"\",\n        country: \"\",\n        isMember: \"\",\n        pay_count: \"\",\n        user_time_type: \"\",\n        user_time: \"\",\n        nickname: \"\",\n        province: \"\",\n        city: \"\",\n        page: 1,\n        limit: 15,\n        level: \"\",\n        group_id: \"\",\n        field_key: \"\",\n      },\n      field_key: \"\",\n      level: \"\",\n      group_id: \"\",\n      label_id: \"\",\n      user_time_type: \"\",\n      pay_count: \"\",\n      userLists: [],\n      FromData: null,\n      selectionList: [],\n      user_ids: \"\",\n      selectedData: [],\n      timeVal: [],\n      array_ids: [],\n      groupList: [],\n      levelList: [],\n      labelFrom: {\n        page: 1,\n        limit: \"\",\n      },\n      labelLists: [],\n      display: \"none\",\n      checkBox: false,\n      selectionCopy: [],\n      isCheckBox: false,\n      isAll: 0,\n      userId: 0,\n      checkUidList:[],\n      batchModal: false,\n      menuActive: 1,\n      batchLabel: [],\n      batchData: {\n        group_id: 0,\n        label_id: [],\n        level_id: 0,\n        money_status: 0,\n        money: 0,\n        integration_status: 0,\n        integration: 0,\n        days_status: 1,\n        day: 0,\n        spread_uid: '',\n      },\n      spread_name: ''\n    };\n  },\n  watch: {\n    selectionList(value) {\n      let arr = value.map((item) => item.uid);\n      this.array_ids = arr;\n      this.user_ids = arr.join();\n    },\n    userLists: {\n      deep: true,\n      handler(value) {\n        value.forEach((item) => {\n          this.selectionList.forEach((itm) => {\n            if (itm.uid === item.uid) {\n              item.checkBox = true;\n            }\n          });\n        });\n        const arr = this.userLists.filter((item) => item.checkBox);\n        if (this.userLists.length) {\n          this.checkBox = this.userLists.length === arr.length;\n        } else {\n          this.checkBox = false;\n        }\n      },\n    },\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 100;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getList();\n  },\n  mounted() {\n    this.userGroup();\n    this.levelLists();\n    this.groupLists();\n  },\n  methods: {\n    checkboxItem(e){\n      let uid = parseInt(e.rowid);\n      let index = this.checkUidList.indexOf(uid);\n      if(index !== -1){\n        this.checkUidList = this.checkUidList.filter((item)=> item !== uid);\n      }else{\n        this.checkUidList.push(uid);\n      }\n    },\n    checkboxAll(){\n      // 获取选中当前值\n      let obj2 = this.$refs.xTable.getCheckboxRecords(true);\n      // 获取之前选中值\n      let obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\t  if(this.isAll == 0 && this.checkUidList.length <= obj.length && !this.isCheckBox){\n\t  \tobj = [];\n\t  }\n      obj = obj.concat(obj2);\n      let uids = [];\n      obj.forEach((item)=>{\n        uids.push(parseInt(item.uid))\n      })\n      this.checkUidList = uids;\n      if(!obj2.length){\n        this.isCheckBox = false;\n      }\n    },\n    allPages(e){\n      this.isAll = e;\n      if(e==0){\n        this.$refs.xTable.toggleAllCheckboxRow();\n        // this.checkboxAll();\n      }else{\n        if(!this.isCheckBox){\n          this.$refs.xTable.setAllCheckboxRow(true);\n          this.isCheckBox = true;\n          this.isAll = 1;\n        }else{\n          this.$refs.xTable.setAllCheckboxRow(false);\n          this.isCheckBox = false;\n          this.isAll = 0;\n        }\n        this.checkUidList = []\n      }\n    },\n    closeLabel(label) {\n      let index = this.dataLabel.indexOf(\n          this.dataLabel.filter((d) => d.id == label.id)[0]\n      );\n      this.dataLabel.splice(index, 1);\n    },\n    activeData(dataLabel) {\n      this.labelListShow = false;\n      if (this.batchModal && this.menuActive === 2) {\n        this.batchLabel = dataLabel;\n        this.batchData.label_id = dataLabel.map(item => item.id);\n      } else {\n        this.dataLabel = dataLabel;\n      }\n    },\n    openLabelList(row) {\n      this.labelListShow = true;\n      let data = JSON.parse(JSON.stringify(this.dataLabel));\n      if (this.batchModal && this.menuActive === 2) {\n        data = JSON.parse(JSON.stringify(this.batchLabel));\n      }\n      this.$refs.labelList.userLabel(data);\n    },\n    // 标签弹窗关闭\n    labelListClose() {\n      this.labelListShow = false;\n    },\n    // 标签弹窗关闭\n    labelClose(e) {\n      if (!e) {\n        this.getList();\n      }\n      this.labelShow = false;\n      this.labelActive.uid = 0;\n    },\n    // 提交\n    putSend(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if (!this.formInline.spread_uid) {\n            return this.$Message.error(\"请上传用户\");\n          }\n          agentSpreadApi(this.formInline)\n              .then((res) => {\n                this.promoterShow = false;\n                this.$Message.success(res.msg);\n                this.getList();\n                this.$refs[name].resetFields();\n              })\n              .catch((res) => {\n                this.$Message.error(res.msg);\n              });\n        }\n      });\n    },\n    save() {\n      this.$modalForm(getUserSaveForm()).then(() => this.getList());\n      // getUserSaveForm().then(async (res) => {\n      // \tif(res.data.status === false){\n      // \t\treturn this.$authLapse(res.data);\n      // \t}\n      // \tthis.FromData = res.data;\n      // \tthis.$refs.edits.modals = true;\n      // }).catch(err=>{\n      // \tthis.$Message.error(err.msg);\n      // })\n    },\n    synchro() {\n      userSynchro()\n          .then((res) => {\n            this.$Message.success(res.msg);\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n    },\n    // 分组列表\n    groupLists() {\n      this.loading = true;\n      userLabelApi(this.labelFrom)\n          .then(async (res) => {\n            let data = res.data;\n            this.labelLists = data.list;\n          })\n          .catch((res) => {\n            this.loading = false;\n            this.$Message.error(res.msg);\n          });\n    },\n    onClickTab(type) {\n      this.isAll = 0;\n      this.isCheckBox = false;\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = [];\n      this.userFrom.page = 1;\n      this.userFrom.user_type = type==-1?'':type;\n      this.getList();\n    },\n    userGroup() {\n      let data = {\n        page: 1,\n        limit: \"\",\n      };\n      userGroupApi(data).then((res) => {\n        this.groupList = res.data.list;\n      });\n    },\n    levelLists() {\n      let data = {\n        page: 1,\n        limit: \"\",\n        title: \"\",\n        is_show: 1,\n      };\n      levelListApi(data).then((res) => {\n        this.levelList = res.data.list;\n      });\n    },\n    // 批量设置分组；\n    setGroup() {\n      if (this.selectionList.length === 0) {\n        this.$Message.warning(\"请选择要设置分组的用户\");\n      } else {\n        let uids = {\n          all: this.isAll,\n          uids: this.array_ids\n        };\n        if (this.isAll == 1) {\n          uids.where = this.userFrom;\n          uids.where = {\n            city: this.userFrom.city,\n            country: this.userFrom.country,\n            field_key: this.userFrom.field_key,\n            group_id: this.userFrom.group_id,\n            isMember: this.userFrom.isMember,\n            is_promoter: this.userFrom.is_promoter,\n            label_id: this.userFrom.label_id,\n            level: this.userFrom.level,\n            nickname: this.userFrom.nickname,\n            pay_count: this.userFrom.pay_count,\n            province: this.userFrom.province,\n            sex: this.userFrom.sex,\n            status: this.userFrom.status,\n            user_time: this.userFrom.user_time,\n            user_time_type: this.userFrom.user_time_type,\n            user_type: this.userFrom.user_type,\n          };\n        }\n        this.$modalForm(userSetGroup(uids)).then(() => this.getList());\n      }\n    },\n    // 批量设置标签；\n    setLabel() {\n      if (this.selectionList.length === 0) {\n        this.$Message.warning(\"请选择要设置标签的用户\");\n      } else {\n        let uids = {\n          all: this.isAll,\n          uids: this.array_ids\n        };\n        if (this.isAll == 1) {\n          uids.where = {\n            city: this.userFrom.city,\n            country: this.userFrom.country,\n            field_key: this.userFrom.field_key,\n            group_id: this.userFrom.group_id,\n            isMember: this.userFrom.isMember,\n            is_promoter: this.userFrom.is_promoter,\n            label_id: this.userFrom.label_id,\n            level: this.userFrom.level,\n            nickname: this.userFrom.nickname,\n            pay_count: this.userFrom.pay_count,\n            province: this.userFrom.province,\n            sex: this.userFrom.sex,\n            status: this.userFrom.status,\n            user_time: this.userFrom.user_time,\n            user_time_type: this.userFrom.user_time_type,\n            user_type: this.userFrom.user_type,\n          };\n        }\n        this.labelShow = true;\n        this.labelActive.uid = uids;\n        // this.$modalForm(userSetLabelApi(uids)).then(() => this.getList());\n      }\n    },\n    // 是否为付费会员；\n    changeMember() {\n      this.userFrom.page = 1;\n      this.getList();\n    },\n    // 选择国家\n    changeCountry() {\n      if (this.userFrom.country === \"abroad\" || !this.userFrom.country) {\n        this.selectedData = [];\n        this.userFrom.province = \"\";\n        this.userFrom.city = \"\";\n        this.address = [];\n      }\n\t  this.userSearchs();\n    },\n    // 选择地址\n    handleChange(value, selectedData) {\n      this.selectedData = selectedData.map((o) => o.label);\n      this.userFrom.province = this.selectedData[0];\n      this.userFrom.city = this.selectedData[1];\n\t  this.userSearchs();\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.userFrom.user_time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n\t  this.userSearchs();\n    },\n    // 操作\n    changeMenu(row, name, index) {\n      this.userId = row.uid;\n      let uid = [];\n      uid.push(row.uid);\n      let uids = { uids: uid };\n      switch (name) {\n        case \"1\":\n          this.$refs.userDetails.modals = true;\n          this.$refs.userDetails.activeName = \"info\";\n          this.$refs.userDetails.getDetails(row.uid);\n          break;\n        case \"2\":\n          this.getOtherFrom(row.uid);\n          break;\n        case \"3\":\n          // this.giveLevel(row.uid);\n          this.giveLevelTime(row.uid);\n          break;\n        case \"4\":\n          this.del(\n              row,\n              \"清除 【 \" + row.nickname + \" 】的会员等级\",\n              index,\n              \"user\"\n          );\n          break;\n        case \"5\":\n          this.$modalForm(userSetGroup(uids)).then(() =>\n              this.$refs.sends.getList()\n          );\n          break;\n        case \"6\":\n          this.openLabel(row);\n          // this.$modalForm(userSetLabelApi(uids)).then(() => this.$refs.sends.getList());\n          break;\n        case \"7\":\n          this.editS(row);\n          break;\n        default:\n          this.del(\n              row,\n              \"解除【 \" + row.nickname + \" 】的上级推广人\",\n              index,\n              \"tuiguang\"\n          );\n          break;\n          // this.del(row, '清除 【 ' + row.nickname + ' 】的会员等级', index)\n      }\n    },\n    openLabel(row) {\n      this.labelShow = true;\n      this.labelActive.uid = row.uid;\n    },\n    editS(row) {\n      this.promoterShow = true;\n      this.formInline.uid = row.uid;\n    },\n    customer() {\n      this.customerShow = true;\n    },\n    imageObject(e) {\n      this.customerShow = false;\n      if (this.batchModal && this.menuActive === 6) {\n        this.batchData.spread_uid = e.uid;\n        this.spread_name = e.name;\n      } else {\n        this.formInline.spread_uid = e.uid;\n        this.formInline.image = e.image;\n      }\n    },\n    cancel(name) {\n      this.promoterShow = false;\n      this.$refs[name].resetFields();\n    },\n    // 赠送会员等级\n    giveLevel(id) {\n      giveLevelApi(id)\n          .then(async (res) => {\n            if (res.data.status === false) {\n              return this.$authLapse(res.data);\n            }\n            this.FromData = res.data;\n            this.$refs.edits.modals = true;\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    // 赠送会员等级\n    giveLevelTime(id) {\n      giveLevelTimeApi(id)\n          .then(async (res) => {\n            if (res.data.status === false) {\n              return this.$authLapse(res.data);\n            }\n            this.FromData = res.data;\n            this.$refs.edits.modals = true;\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    // 删除\n    del(row, tit, num, name) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url:\n            name === \"user\"\n                ? `user/del_level/${row.uid}`\n                : `agent/stair/delete_spread/${row.uid}`,\n        method: name === \"user\" ? \"DELETE\" : \"PUT\",\n        // url: `user/del_level/${row.uid}`,\n        // method: 'DELETE',\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n          .then((res) => {\n            this.$Message.success(res.msg);\n            this.getList();\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    // 清除会员删除成功\n    submitModel() {\n      this.getList();\n    },\n    // 会员列表\n    getList() {\n      this.loading = true;\n      let activeIds = [];\n      this.dataLabel.forEach((item) => {\n        activeIds.push(item.id);\n      });\n      this.userFrom.label_id = activeIds.join(\",\") || \"\";\n      this.userFrom.user_type = this.userFrom.user_type || \"\";\n      this.userFrom.status = this.userFrom.status || \"\";\n      this.userFrom.sex = this.userFrom.sex || \"\";\n      this.userFrom.is_promoter = this.userFrom.is_promoter || \"\";\n      this.userFrom.country = this.userFrom.country || \"\";\n      this.userFrom.user_time_type = this.userFrom.user_time_type || \"\";\n      this.userFrom.pay_count = this.userFrom.pay_count || \"\";\n      // this.userFrom.label_id = this.userFrom.label_id || \"\";\n      this.userFrom.field_key = this.field_key === \"all\" ? \"\" : this.field_key;\n      this.userFrom.level =\n          this.userFrom.level === \"all\" ? \"\" : this.userFrom.level;\n      this.userFrom.group_id =\n          this.userFrom.group_id === \"all\" ? \"\" : this.userFrom.group_id;\n      userList(this.userFrom)\n          .then(async (res) => {\n            let data = res.data;\n            data.list.forEach((item) => {\n              item.checkBox = false;\n            });\n            this.userLists = data.list;\n            this.total = data.count;\n            this.loading = false;\n            this.$nextTick(function(){\n              if (this.isAll == 1) {\n                this.selectionList = this.userLists;\n                if(this.isCheckBox){\n                  this.$refs.xTable.setAllCheckboxRow(true);\n                }else{\n                  this.$refs.xTable.setAllCheckboxRow(false);\n                }\n              }else{\n\t\t\t\tlet obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\t\t\t\tif(!this.checkUidList.length || this.checkUidList.length <= obj.length){\n\t\t\t\t  this.$refs.xTable.setAllCheckboxRow(false);\n\t\t\t\t}\n              }\n            })\n\n          })\n          .catch((res) => {\n            this.loading = false;\n            this.$Message.error(res.msg);\n          });\n    },\n    pageChange({ currentPage, pageSize }) {\n      this.userFrom.page = currentPage;\n      this.userFrom.limit = pageSize;\n      this.getList();\n    },\n    // pageChange(index) {\n    //   this.userFrom.page = index;\n    //   this.getList();\n    // },\n    // 搜索\n    userSearchs() {\n      if (this.userFrom.user_time_type && !this.timeVal.length) {\n        return this.$Message.error(\"请选择访问时间\");\n      }\n      if (this.timeVal.length && !this.userFrom.user_time_type) {\n        return this.$Message.error(\"请选择访问情况\");\n      }\n      this.isAll = 0;\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = [];\n      this.userFrom.page = 1;\n      this.selectionList = [];\n      this.getList();\n    },\n    // 重置\n    reset(name) {\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = []\n      this.headeType = \"-1\";\n      this.userFrom = {\n        user_type: \"\",\n        status: \"\",\n        sex: \"\",\n        is_promoter: \"\",\n        country: \"\",\n        pay_count: \"\",\n        user_time_type: \"\",\n        user_time: \"\",\n        nickname: \"\",\n        field_key: \"\",\n        level: \"\",\n        group_id: \"\",\n        label_id: \"\",\n        page: 1, // 当前页\n        limit: 20, // 每页显示条数\n      };\n      this.field_key = \"\";\n      this.level = \"\";\n      this.group_id = \"\";\n      this.label_id = \"\";\n      this.user_time_type = \"\";\n      this.pay_count = \"\";\n      this.timeVal = [];\n      this.selectionList = [];\n      this.dataLabel = [];\n      this.getList();\n    },\n    // 获取编辑表单数据\n    getUserFrom(id) {\n      this.$modalForm(getUserData(id)).then(() => this.getList());\n      // getUserData(id)\n      //   .then(async (res) => {\n      //     if (res.data.status === false) {\n      //       return this.$authLapse(res.data);\n      //     }\n      //     this.FromData = res.data;\n      //     this.$refs.edits.modals = true;\n      //   })\n      //   .catch((res) => {\n      //     this.$Message.error(res.msg);\n      //   });\n    },\n    // 获取积分余额表单\n    getOtherFrom(id) {\n      editOtherApi(id)\n          .then(async (res) => {\n            if (res.data.status === false) {\n              return this.$authLapse(res.data);\n            }\n            res.data.rules[1].props.max = 999999;\n            this.FromData = res.data;\n            this.$refs.edits.modals = true;\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    // 修改状态\n    onchangeIsShow(row) {\n      let data = {\n        id: row.uid,\n        status: row.status,\n      };\n      isShowApi(data)\n          .then(async (res) => {\n            this.$Message.success(res.msg);\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    // 点击发送优惠券\n    onSend() {\n      if (this.checkUidList.length === 0 && this.isAll==0) {\n         return this.$Message.warning(\"请选择要发送优惠券的用户\");\n      }\n      this.$refs.sends.modals = true;\n      this.$refs.sends.getList();\n    },\n    // 发送图文消息\n    onSendPic() {\n      if (this.checkUidList.length === 0 && this.isAll==0) {\n        this.$Message.warning(\"请选择要发送图文消息的用户\");\n      } else {\n        this.modal13 = true;\n      }\n    },\n    // 编辑\n    edit(row) {\n      this.getUserFrom(row.uid);\n      // this.$modalForm(getUserSaveForm(row.uid)).then(() => this.getList());\n    },\n    //信息补充\n    extendInfo(row) {\n      // this.$modalForm(extendInfo(row.uid)).then(() => this.getList());\n      extendInfo(row.uid).then(async (res) => {\n        if(res.data.status === false){\n          return this.$authLapse(res.data);\n        }\n        this.FromData = res.data;\n        this.$refs.edits.modals = true;\n        // this.getList()\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n      })\n    },\n    // 修改成功\n    submitFail(p) {\n      // this.getList();\n      if (this.$refs.userDetails.modals) {\n        this.$refs.userDetails.getDetails(this.userId);\n      }\n    },\n    // 排序\n    // sortChanged(e) {\n    //   this.userFrom[e.key] = e.order;\n    //   this.getList();\n    // },\n    // onSelectCancel(selection, row) {},\n    menuSelect(name) {\n      this.menuActive = name;\n    },\n    setBatch() {\n      this.batchModal = true;\n    },\n    tagClose(id) {\n      let index = this.batchLabel.findIndex(item => item.id === id);\n      this.batchLabel.splice(index, 1);\n    },\n    cancelBatch() {\n      this.batchModal = false;\n    },\n    // 保存批量操作\n    saveBatch() {\n      batchProcess({\n        type: this.menuActive,\n        uids: this.checkUidList,\n        all: this.isAll,\n        where: this.userFrom,\n        data: this.batchData\n      }).then(res => {\n        this.$Message.success(res.msg);\n        this.batchModal = false;\n      }).catch(res => {\n        this.$Message.error(res.msg);\n      });\n    },\n    batchVisibleChange() {\n      this.batchData = {\n        group_id: 0,\n        label_id: [],\n        level_id: 0,\n        money_status: 0,\n        money: 0,\n        integration_status: 0,\n        integration: 0,\n        days_status: 1,\n        day: 0,\n        spread_uid: '',\n      };\n      this.batchLabel = [];\n      this.spread_name = '';\n      this.menuActive = 1;\n    }\n  },\n};\n", null]}