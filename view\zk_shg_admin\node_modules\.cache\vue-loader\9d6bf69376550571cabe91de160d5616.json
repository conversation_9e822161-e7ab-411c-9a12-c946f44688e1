{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=template&id=5aa3ab28&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751013157007}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"transfer-container\"},[_c('div',{staticClass:\"page-header\"},[_vm._m(0),_c('div',{staticClass:\"header-right\"},[_c('el-button',{attrs:{\"type\":\"warning\",\"icon\":\"el-icon-warning\"},on:{\"click\":_vm.showPendingOnly}},[_vm._v(\"待审核单据\")]),_c('el-button',{attrs:{\"type\":\"success\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.exportTransfer}},[_vm._v(\"导出数据\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-pie-chart\"},on:{\"click\":_vm.showStatistics}},[_vm._v(\"统计分析\")])],1)]),_c('div',{staticClass:\"filter-container\"},[_c('el-form',{attrs:{\"model\":_vm.filterForm,\"inline\":\"\"}},[_c('el-form-item',{attrs:{\"label\":\"调拨单号：\"}},[_c('el-input',{staticStyle:{\"width\":\"200px\"},attrs:{\"placeholder\":\"输入调拨单号\"},model:{value:(_vm.filterForm.order_no),callback:function ($$v) {_vm.$set(_vm.filterForm, \"order_no\", $$v)},expression:\"filterForm.order_no\"}})],1),_c('el-form-item',{attrs:{\"label\":\"调出门店：\"}},[_c('el-select',{staticStyle:{\"width\":\"200px\"},attrs:{\"placeholder\":\"选择调出门店\",\"clearable\":\"\"},model:{value:(_vm.filterForm.from_store_id),callback:function ($$v) {_vm.$set(_vm.filterForm, \"from_store_id\", $$v)},expression:\"filterForm.from_store_id\"}},[_c('el-option',{attrs:{\"label\":\"全部门店\",\"value\":\"\"}}),_vm._l((_vm.storeList),function(store){return _c('el-option',{key:store.id,attrs:{\"label\":store.name,\"value\":store.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"调入门店：\"}},[_c('el-select',{staticStyle:{\"width\":\"200px\"},attrs:{\"placeholder\":\"选择调入门店\",\"clearable\":\"\"},model:{value:(_vm.filterForm.to_store_id),callback:function ($$v) {_vm.$set(_vm.filterForm, \"to_store_id\", $$v)},expression:\"filterForm.to_store_id\"}},[_c('el-option',{attrs:{\"label\":\"全部门店\",\"value\":\"\"}}),_vm._l((_vm.storeList),function(store){return _c('el-option',{key:store.id,attrs:{\"label\":store.name,\"value\":store.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"单据状态：\"}},[_c('el-select',{staticStyle:{\"width\":\"150px\"},attrs:{\"placeholder\":\"单据状态\",\"clearable\":\"\"},model:{value:(_vm.filterForm.status),callback:function ($$v) {_vm.$set(_vm.filterForm, \"status\", $$v)},expression:\"filterForm.status\"}},[_c('el-option',{attrs:{\"label\":\"全部\",\"value\":\"\"}}),_c('el-option',{attrs:{\"label\":\"待审核\",\"value\":0}}),_c('el-option',{attrs:{\"label\":\"已通过\",\"value\":1}}),_c('el-option',{attrs:{\"label\":\"已完成\",\"value\":2}}),_c('el-option',{attrs:{\"label\":\"已拒绝\",\"value\":3}}),_c('el-option',{attrs:{\"label\":\"已撤销\",\"value\":4}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"申请日期：\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"240px\"},attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\"},model:{value:(_vm.filterForm.date_range),callback:function ($$v) {_vm.$set(_vm.filterForm, \"date_range\", $$v)},expression:\"filterForm.date_range\"}})],1),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.getTransferList}},[_vm._v(\"查询\")]),_c('el-button',{on:{\"click\":_vm.resetFilter}},[_vm._v(\"重置\")])],1)],1)],1),_c('div',{staticClass:\"stats-container\"},[_c('div',{staticClass:\"stat-card\"},[_vm._m(1),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.statistics.total_orders))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"总调拨单\")])])]),_c('div',{staticClass:\"stat-card\"},[_vm._m(2),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.statistics.pending_orders))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"待审核\")])])]),_c('div',{staticClass:\"stat-card\"},[_vm._m(3),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.statistics.approved_orders))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"已通过\")])])]),_c('div',{staticClass:\"stat-card\"},[_vm._m(4),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.statistics.completed_orders))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"已完成\")])])])]),_c('div',{staticClass:\"table-container\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.tableLoading),expression:\"tableLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.transferList,\"stripe\":\"\",\"border\":\"\"},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"order_no\",\"label\":\"调拨单号\",\"width\":\"160\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-link',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.viewDetail(scope.row)}}},[_vm._v(_vm._s(scope.row.order_no))])]}}])}),_c('el-table-column',{attrs:{\"label\":\"商品信息\",\"min-width\":\"250\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"product-info\"},[_c('el-image',{staticStyle:{\"width\":\"40px\",\"height\":\"40px\",\"border-radius\":\"4px\",\"margin-right\":\"12px\"},attrs:{\"src\":scope.row.product_image,\"fit\":\"cover\"}}),_c('div',[_c('div',{staticClass:\"product-name\"},[_vm._v(_vm._s(scope.row.product_name))]),(scope.row.sku)?_c('div',{staticClass:\"product-spec\"},[_vm._v(_vm._s(scope.row.sku))]):_vm._e(),_c('div',{staticClass:\"transfer-qty\"},[_vm._v(\"调拨数量：\"+_vm._s(scope.row.transfer_qty))])])],1)]}}])}),_c('el-table-column',{attrs:{\"label\":\"调拨门店\",\"width\":\"200\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"transfer-stores\"},[_c('div',{staticClass:\"from-store\"},[_vm._v(_vm._s(scope.row.from_store_name))]),_c('i',{staticClass:\"el-icon-right transfer-arrow\"}),_c('div',{staticClass:\"to-store\"},[_vm._v(_vm._s(scope.row.to_store_name))])])]}}])}),_c('el-table-column',{attrs:{\"label\":\"单据状态\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getStatusTagType(scope.row.status),\"size\":\"small\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.getStatusText(scope.row.status))+\"\\n          \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"applicant\",\"label\":\"申请人\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"apply_time\",\"label\":\"申请时间\",\"width\":\"160\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"approve_time\",\"label\":\"审核时间\",\"width\":\"160\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.approve_time)?_c('span',[_vm._v(_vm._s(scope.row.approve_time))]):_c('span',{staticClass:\"text-muted\"},[_vm._v(\"-\")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"250\",\"align\":\"center\",\"fixed\":\"right\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDetail(scope.row)}}},[_vm._v(\"查看详情\")]),(scope.row.status === 0)?[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.approveTransfer(scope.row, true)}}},[_vm._v(\"通过\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.approveTransfer(scope.row, false)}}},[_vm._v(\"拒绝\")])]:_vm._e(),(scope.row.status === 1)?[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.executeTransfer(scope.row)}}},[_vm._v(\"执行调拨\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.forceExecute(scope.row)}}},[_vm._v(\"强制执行\")])]:_vm._e(),(scope.row.status === 3 || scope.row.status === 4)?[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.deleteTransfer(scope.row)}}},[_vm._v(\"删除\")])]:_vm._e()]}}])})],1),(_vm.selectedRows.length > 0)?_c('div',{staticClass:\"batch-actions\"},[_c('span',{staticClass:\"selected-count\"},[_vm._v(\"已选择 \"+_vm._s(_vm.selectedRows.length)+\" 项\")]),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.batchApprove(true)}}},[_vm._v(\"批量通过\")]),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.batchApprove(false)}}},[_vm._v(\"批量拒绝\")]),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":_vm.batchExecute}},[_vm._v(\"批量执行\")]),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":_vm.clearSelection}},[_vm._v(\"取消选择\")])],1):_vm._e(),_c('div',{staticClass:\"pagination-container\"},[_c('el-pagination',{attrs:{\"current-page\":_vm.currentPage,\"page-sizes\":[20, 50, 100, 200],\"page-size\":_vm.pageSize,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":\"调拨详情\",\"visible\":_vm.detailDialogVisible,\"width\":\"700px\"},on:{\"update:visible\":function($event){_vm.detailDialogVisible=$event}}},[(_vm.currentDetail)?_c('div',{staticClass:\"detail-content\"},[_c('div',{staticClass:\"detail-section\"},[_c('h4',[_vm._v(\"基本信息\")]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"调拨单号：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.currentDetail.order_no))])])]),_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"单据状态：\")]),_c('el-tag',{attrs:{\"type\":_vm.getStatusTagType(_vm.currentDetail.status)}},[_vm._v(\"\\n                \"+_vm._s(_vm.getStatusText(_vm.currentDetail.status))+\"\\n              \")])],1)]),_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"申请人：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.currentDetail.applicant))])])]),_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"申请时间：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.currentDetail.apply_time))])])])],1)],1),_c('div',{staticClass:\"detail-section\"},[_c('h4',[_vm._v(\"商品信息\")]),_c('div',{staticClass:\"product-detail\"},[_c('el-image',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"border-radius\":\"8px\",\"margin-right\":\"16px\"},attrs:{\"src\":_vm.currentDetail.product_image,\"fit\":\"cover\"}}),_c('div',[_c('div',{staticClass:\"product-name\"},[_vm._v(_vm._s(_vm.currentDetail.product_name))]),(_vm.currentDetail.sku)?_c('div',{staticClass:\"product-spec\"},[_vm._v(\"规格：\"+_vm._s(_vm.currentDetail.sku))]):_vm._e(),_c('div',{staticClass:\"transfer-qty\"},[_vm._v(\"调拨数量：\"+_vm._s(_vm.currentDetail.transfer_qty))])])],1)]),_c('div',{staticClass:\"detail-section\"},[_c('h4',[_vm._v(\"调拨信息\")]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"调出门店：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.currentDetail.from_store_name))])])]),_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"调入门店：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.currentDetail.to_store_name))])])])],1)],1),(_vm.currentDetail.remark)?_c('div',{staticClass:\"detail-section\"},[_c('h4',[_vm._v(\"申请原因\")]),_c('div',{staticClass:\"remark-content\"},[_vm._v(_vm._s(_vm.currentDetail.remark))])]):_vm._e(),(_vm.currentDetail.approve_remark)?_c('div',{staticClass:\"detail-section\"},[_c('h4',[_vm._v(\"审核备注\")]),_c('div',{staticClass:\"remark-content\"},[_vm._v(_vm._s(_vm.currentDetail.approve_remark))])]):_vm._e()]):_vm._e()]),_c('el-dialog',{attrs:{\"title\":_vm.approveType ? '审核通过' : '审核拒绝',\"visible\":_vm.approveDialogVisible,\"width\":\"500px\"},on:{\"update:visible\":function($event){_vm.approveDialogVisible=$event}}},[_c('el-form',{ref:\"approveForm\",attrs:{\"model\":_vm.approveForm,\"rules\":_vm.approveRules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"审核备注：\",\"prop\":\"remark\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"placeholder\":_vm.approveType ? '请填写通过原因（可选）' : '请填写拒绝原因',\"rows\":4},model:{value:(_vm.approveForm.remark),callback:function ($$v) {_vm.$set(_vm.approveForm, \"remark\", $$v)},expression:\"approveForm.remark\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.approveDialogVisible = false}}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":_vm.approveType ? 'success' : 'danger',\"loading\":_vm.approveLoading},on:{\"click\":_vm.submitApprove}},[_vm._v(\"\\n        \"+_vm._s(_vm.approveType ? '确认通过' : '确认拒绝')+\"\\n      \")])],1)],1)],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_vm._v(\"调拨管理\")]),_c('p',{staticClass:\"page-desc\"},[_vm._v(\"管理门店间商品调拨申请，支持审核、执行和统计分析\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"stat-icon total\"},[_c('i',{staticClass:\"el-icon-document\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"stat-icon pending\"},[_c('i',{staticClass:\"el-icon-time\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"stat-icon approved\"},[_c('i',{staticClass:\"el-icon-check\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"stat-icon completed\"},[_c('i',{staticClass:\"el-icon-circle-check\"})])}]\n\nexport { render, staticRenderFns }"]}