{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableList.vue", "mtime": 1733823002405}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Distribution from './distribution.vue'\nimport expandRow from './tableExpand.vue'\nimport {\n  orderList,\n  getOrdeDatas,\n  getDataInfo,\n  getRefundFrom,\n  getnoRefund,\n  refundIntegral,\n  getDistribution,\n  writeUpdate,\n  storeOrderApi,\n  handBatchDelivery,\n  putWrite,\n  exportExpressList,\n   remindOrder,\n   putOpenRefund\n} from '@/api/order'\nimport { erpConfig } from '@/api/erp'\nimport { mapState, mapMutations } from 'vuex'\nimport editFrom from '../../../../components/from/from'\nimport detailsFrom from '../handle/orderDetails'\nimport orderRemark from '../handle/orderRemark'\nimport orderRecord from '../handle/orderRecord'\nimport orderSend from '../handle/orderSend'\nimport userDetails from '@/pages/user/list/handle/userDetails'\nimport autoSend from '../handle/autoSend'\nimport queueList from '../handle/queueList'\nimport Setting from '@/setting'\nimport util from '@/libs/util'\nimport exportExcel from '@/utils/newToExcel.js'\nimport Template from '../../../setting/devise/template.vue'\nimport printJS from 'print-js';\n\nexport default {\n  name: 'table_list',\n  components: {\n    expandRow,\n    editFrom,\n    detailsFrom,\n    orderRemark,\n    orderRecord,\n    orderSend,\n    userDetails,\n    Distribution,\n    autoSend,\n    queueList,\n    Template,\n  },\n  props: ['where', 'currentTab'],\n  data() {\n    const codeNum = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('请填写核销码'))\n      }\n      // 模拟异步验证效果\n      if (!Number.isInteger(value)) {\n        callback(new Error('请填写12位数字'))\n      } else {\n        // const reg = /[0-9]{12}/;\n        const reg = /\\b\\d{12}\\b/\n        if (!reg.test(value)) {\n          callback(new Error('请填写12位数字'))\n        } else {\n          callback()\n        }\n      }\n    }\n    return {\n      roterPre: Setting.roterPre,\n      openErp: false,\n      // currentTab: '-1',\n            distshow: false, //分配的弹窗\n      delfromData: {},\n      modal: false,\n      orderList: [],\n      pay_type: '',\n      orderCards: [],\n      loading: false,\n      orderId: 0,\n      page: {\n        total: 0, // 总条数\n        pageNum: 1, // 当前页\n        pageSize: 10, // 每页显示条数\n      },\n      data: [],\n      FromData: null,\n      orderDatalist: null,\n      modalTitleSs: '',\n      isDelIdList: [],\n      checkBox: false,\n      formSelection: [],\n      display: 'none',\n      autoDisabled: false,\n      status: 0, //发货状态判断\n      isAll: 0,\n      rowActive: {},\n      tablists: {},\n      selectArr: [],\n      exportList: [\n        {\n          name: '1',\n          label: '导出发货单',\n        },\n        {\n          name: '0',\n          label: '导出订单',\n        },\n      ],\n      exportListOn: 0,\n      manualModal: false,\n      uploadAction: `${Setting.apiBaseURL}/file/upload/1`,\n      uploadHeaders: {},\n      autoModal: false,\n      isShow: false,\n      recordModal: false,\n      sendOutValue: '',\n      fileList: [],\n      file: '',\n      modals2: false,\n      writeOffRules: {\n        code: [{ validator: codeNum, trigger: 'blur', required: true }],\n      },\n      writeOffFrom: {\n        code: '',\n        confirm: 0,\n      },\n      orderConNum: 0,\n      orderConId: 0,\n      checkUidList: [],\n      refundModal: false,\n      refundColumns: [\n        {\n          type: 'selection',\n          width: 60,\n          align: 'center'\n        },\n        {\n          title: '商品信息',\n          width: 210,\n          slot: 'product'\n        },\n        {\n          title: '规格',\n          render: (h, params) => {\n            return h('div', params.row.productInfo.attrInfo.suk);\n          }\n        },\n        {\n          title: '售价',\n          render: (h, params) => {\n            return h('div', params.row.productInfo.attrInfo.price);\n          }\n        },\n        {\n          title: '优惠价',\n          key: 'refundPrice'\n        },\n        {\n          title: '总数',\n          key: 'cart_num'\n        },\n        {\n          title: '退款数量',\n          slot: 'action',\n          width: 160,\n        }\n      ],\n      refundProduct: [],\n      refundSelection: [],\n      refundMoney: 0,\n      is_split_order: 0,\n      orderDataStatus: '',\n    }\n  },\n  computed: {\n    ...mapState('admin/layout', ['isMobile']),\n    ...mapState('admin/order', [\n      'orderPayType',\n      'orderStatus',\n      'orderTime',\n      'orderNum',\n      'fieldKey',\n      'orderType',\n      'orderChartType',\n      'supplier_id',\n      'store_id',\n      'type_id',\n    ]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96\n    },\n    labelPosition() {\n      return this.isMobile ? 'top' : 'right'\n    },\n    refundProductNum() {\n      return this.refundProduct.reduce((total, { refundNum }) => (total + refundNum), 0);\n    },\n  },\n  mounted() {},\n  created() {\n    this.getList()\n    this.getToken()\n    this.getErpConfig()\n  },\n  watch: {\n    currentTab() {\n      this.onClickTab()\n    },\n    orderType: function () {\n      this.page.pageNum = 1\n      this.getList()\n    },\n    formSelection(value) {\n      // this.$emit('order-select', value)\n      // if (value.length) {\n      //   this.$emit('auto-disabled', 0)\n      // } else {\n      //   this.$emit('auto-disabled', 1)\n      // }\n      // let isDel = value.some((item) => {\n      //   return item.is_del === 1\n      // })\n      // this.getIsDel(isDel)\n      // this.getisDelIdListl(value)\n    },\n    orderList: {\n      deep: true,\n      handler(value) {\n        value.forEach((item) => {\n          this.formSelection.forEach((itm) => {\n            if (itm.id === item.id) {\n              item.checkBox = true\n            }\n          })\n        })\n        const arr = this.orderList.filter((item) => item.checkBox)\n        if (this.orderList.length) {\n          this.checkBox = this.orderList.length === arr.length\n        } else {\n          this.checkBox = false\n        }\n      },\n    },\n    refundSelection: {\n      handler(value) {\n        this.refundMoney = value.reduce((total, { refundPrice, refundNum }) => {\n          return this.$computes.Add(total, this.$computes.Mul(refundPrice, refundNum));\n        }, 0);\n      },\n      deep: true\n    },\n    is_split_order(value) {\n      this.$nextTick(() => {\n        this.$refs.refundTable.selectAll(!!value);\n      });\n    },\n    refundMoney(value) {\n      this.$nextTick(() => {\n        if (typeof value != 'number') {\n          return;\n        }\n        if (parseFloat(value) == parseInt(value)) {\n          return;\n        }\n        if (value.toString().length - (value.toString().indexOf('.') + 1) > 2) {\n          this.refundMoney = Number(value.toFixed(2));\n        }\n      });\n    },\n  },\n  methods: {\n    ...mapMutations('admin/order', [\n      'getIsDel',\n      'getisDelIdListl',\n      'onChangeTabs',\n      'getStore_id',\n      'getSupplier_id',\n          ]),\n    visibleChange(visible) {\n      this.is_split_order = 0;\n      if (!visible) {\n        this.refundSelection = [];\n      }\n    },\n    cancelRefundModal() {\n      this.refundModal = false;\n    },\n    putOpenRefund() {\n      let data = {\n        id: this.orderId,\n        refund_price: this.refundMoney,\n        type: 1,\n        is_split_order: this.is_split_order\n      };\n      if (this.is_split_order) {\n        if (!this.refundSelection.length) {\n          return this.$Message.error('请选择需要退款的商品');\n        }\n        data.cart_ids = this.refundSelection.map(({ id, refundNum }) => ({\n          cart_id: id,\n          cart_num: refundNum\n        }));\n      }\n      putOpenRefund(data).then(res => {\n        this.$Message.success(res.msg);\n        this.refundModal = false;\n        this.getData(this.orderDatalist.orderInfo.id);\n      }).catch(err => {\n        this.$Message.error(err.msg);\n      });\n    },\n    refundSelectionChange(selection) {\n      this.refundSelection = selection;\n    },\n    refundNumChange({ id, refundNum }) {\n      let result = this.refundSelection.find(item => item.id === id);\n      if (result) {\n        result.refundNum = refundNum;\n      }\n    },\n    checkboxItem(e){\n      let id = parseInt(e.rowid);\n      let index = this.checkUidList.indexOf(id);\n      if(index !== -1){\n        this.checkUidList = this.checkUidList.filter((item)=> item !== id);\n      }else{\n        this.checkUidList.push(id);\n      }\n    },\n    checkboxAll(){\n      // 获取选中当前值\n      let obj2 = this.$refs.xTable.getCheckboxRecords(true);\n      // 获取之前选中值\n      let obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\t  if(this.isAll == 0 && this.checkUidList.length <= obj.length && !this.isCheckBox){\n\t  \t obj = [];\n\t  }\n      obj = obj.concat(obj2);\n      let ids = [];\n      obj.forEach((item)=>{\n        ids.push(parseInt(item.id))\n      })\n      this.checkUidList = ids;\n      if(!obj2.length){\n        this.isCheckBox = false;\n      }\n    },\n    allPages(e){\n      this.isAll = e;\n      if(e==0){\n        this.$refs.xTable.toggleAllCheckboxRow();\n        // this.checkboxAll();\n      }else{\n        if(!this.isCheckBox){\n          this.$refs.xTable.setAllCheckboxRow(true);\n          this.isCheckBox = true;\n          this.isAll = 1;\n        }else{\n          this.$refs.xTable.setAllCheckboxRow(false);\n          this.isCheckBox = false;\n          this.isAll = 0;\n        }\n        this.checkUidList = []\n      }\n    },\n    //erp配置\n    getErpConfig() {\n      erpConfig()\n        .then((res) => {\n          this.openErp = res.data.open_erp\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg)\n        })\n    },\n    printOreder() {\n      if (this.checkUidList.length > 10 || (this.isAll==1 && this.page.total>10)) {\n        return this.$Message.error('最多批量打印10个订单')\n      }\n      let ids = []\n      if (this.isAll==1 && this.page.total<=10){\n        this.orderList.forEach(item=>{\n          ids.push(parseInt(item.id))\n        })\n      }\n      let pathInfo = this.$router.resolve({\n        path: this.roterPre + '/supplier/order/distribution',\n        query: {\n          id: this.isAll==1?ids.join(','):this.checkUidList.join(','),\n          status: 2,\n        },\n      })\n      window.open(pathInfo.href, '_blank')\n    },\n\n    delAll() {\n      if (this.checkUidList.length === 0 && this.isAll==0) {\n        return this.$Message.error('请先选择删除的订单！')\n      }\n      let idss = {\n        all: this.isAll,\n        ids: this.checkUidList,\n      }\n      let delfromData = {\n        title: '删除订单',\n        url: `/order/dels`,\n        method: 'post',\n        ids: idss,\n      }\n      this.$modalSure(delfromData)\n          .then((res) => {\n            this.$Message.success(res.msg)\n            this.checkUidList = []\n            this.getList()\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg)\n          })\n    },\n\n    onAuto() {\n      this.$refs.sends.modals = true;\n      this.$refs.sends.getList();\n      this.$refs.sends.getDeliveryList();\n    },\n    // 提醒发货\n       btnClick(row) {\n      let data = {\n        supplier_id: row.supplier_id,\n        id: row.id,\n      }\n      remindOrder(data)\n        .then(async (res) => {\n          this.$Message.success(res.msg)\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg)\n        })\n    },\n\n    queuemModal() {\n      this.$refs.queue.modal = true\n    },\n\n    // 下载物流公司对照表\n    async getExpressList() {\n      let [th, filekey, data, fileName] = [[], [], [], '']\n      let lebData = await this.getExcelData()\n      if (!fileName) fileName = lebData.filename\n      if (!filekey.length) {\n        filekey = lebData.filekey\n      }\n      if (!th.length) th = lebData.header\n      data = lebData.export\n      exportExcel(th, filekey, fileName, data)\n    },\n\n    getExcelData() {\n      return new Promise((resolve, reject) => {\n        exportExpressList().then((res) => {\n          return resolve(res.data)\n        })\n      })\n    },\n\n    // 订单核销\n    writeOff() {\n      this.modals2 = true\n    },\n\n    // 验证\n    search(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.writeOffFrom.confirm = 0\n          putWrite(this.writeOffFrom)\n            .then(async (res) => {\n              if (res.status === 200) {\n                // this.orderInfo = res.data;\n                this.$Message.success(res.msg)\n              } else {\n                this.$Message.error(res.msg)\n              }\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg)\n            })\n        } else {\n          this.$Message.error('请填写正确的核销码')\n        }\n      })\n    },\n\n    // 订单核销\n    ok() {\n      if (!this.writeOffFrom.code) {\n        this.$Message.warning('请先验证订单！')\n      } else {\n        this.writeOffFrom.confirm = 1\n        putWrite(this.writeOffFrom)\n          .then(async (res) => {\n            if (res.status === 200) {\n              this.$Message.success(res.msg)\n              this.modals2 = false\n              this.$refs[name].resetFields()\n              this.$emit('getList')\n            } else {\n              this.$Message.error(res.msg)\n            }\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg)\n          })\n      }\n    },\n\n    del(name) {\n      // this.orderInfo = ''\n      this.modals2 = false\n      this.writeOffFrom.confirm = 0\n      this.$refs[name].resetFields()\n    },\n\n    // 上传头部token\n    getToken() {\n      this.uploadHeaders['Authori-zation'] =\n        'Bearer ' + util.cookies.get('token')\n    },\n\n    // 上传成功\n    uploadSuccess(res, file, fileList) {\n      if (res.status === 200) {\n        this.$Message.success(res.msg)\n        this.file = res.data.src\n        this.fileList = fileList\n      } else {\n        this.$Message.error(res.msg)\n      }\n    },\n\n    //移除文件\n    removeFile(file, fileList) {\n      this.file = ''\n      this.fileList = fileList\n    },\n\n    // 手动批量发货-确定\n    manualModalOk() {\n      this.$refs.upload.clearFiles()\n      handBatchDelivery({ file: this.file })\n        .then((res) => {\n          this.$Message.success(res.msg)\n          this.fileList = []\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg)\n          this.fileList = []\n        })\n    },\n\n    // 手动批量发货-取消\n    manualModalCancel() {\n      this.fileList = []\n      this.$refs.upload.clearFiles()\n    },\n\n    getTabs() {\n      this.spinShow = true\n      this.$store\n        .dispatch('admin/order/getOrderTabs', {\n          status: this.orderStatus,\n          pay_type: this.orderPayType,\n          data: this.orderTime,\n          real_name: this.orderNum,\n          field_key: this.fieldKey,\n          type: this.type_id,\n          plat_type: this.currentTab,\n          store_id: this.store_id,\n          supplier_id: this.supplier_id,\n        })\n        .then((res) => {\n          this.tablists = res.data\n          // this.onChangeChart(this.tablists)\n          this.spinShow = false\n        })\n        .catch((res) => {\n          this.spinShow = false\n          this.$Message.error(res.msg)\n        })\n    },\n    onClickTab() {\n      this.onChangeTabs(this.currentTab)\n            this.isAll = 0;\n      this.isCheckBox = false;\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = [];\n      if (this.currentTab == 1) {\n              this.getSupplier_id('')\n      }\n      if (this.currentTab == 2) {\n        this.getStore_id('')\n      }\n      this.getList()\n      this.$store.dispatch('admin/order/getOrderTabs', {\n      type: this.currentTab,\n      })\n    },\n    closeDetail() {\n      this.$refs.detailss.modals = false\n    },\n    distribution(row) {\n      this.$refs.distshow.modals = true\n      this.$refs.distshow.formValidate.keywords = ''\n      this.$refs.distshow.getList(row.id)\n    },\n    showUserInfo(row) {\n      this.$refs.userDetails.modals = true\n      this.$refs.userDetails.activeName = 'info'\n      this.$refs.userDetails.getDetails(row.uid)\n    },\n\t//修改增加打印方法\n\tprintImg(url) {\n\t  printJS({\n\t    printable: url,\n\t    type: 'image',\n\t    documentTitle: '快递信息',\n\t    style: `img{\n\t      width: 100%;\n\t      height: 476px;\n\t    }`,\n\t  });\n\t},\n    // 操作\n    changeMenu(row, name, num) {\n      this.orderId = row.id\n      this.orderConId = row.pid > 0 ? row.pid : row.id\n      this.orderConNum = num\n      switch (name) {\n        case '1':\n          this.delfromData = {\n            title: '修改立即支付',\n            url: `/order/pay_offline/${row.id}`,\n            method: 'post',\n            ids: '',\n          }\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg)\n              this.$emit('changeGetTabs')\n              this.getData(row.id, 1)\n              this.getList()\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg)\n            })\n          // this.modalTitleSs = '修改立即支付';\n          break\n        case '2':\n          this.rowActive = row\n          this.getData(row.id)\n          break\n        case '3':\n          this.$refs.record.modals = true\n          this.$refs.record.getList(row.id)\n          break\n        case '4':\n          this.$refs.remarks.formValidate.remark = row.remark\n          this.$refs.remarks.modals = true\n          break\n        case '5':\n          this.getOnlyrefundData(row.id, row.refund_type)\n          break\n        case '55':\n          this.getrefundData(row.id, row.refund_type)\n          break\n        case '6':\n          this.getRefundIntegral(row.id)\n          break\n        case '7':\n          this.getNorefundData(row.id)\n          break\n        case '8':\n          this.delfromData = {\n            title: '修改确认收货',\n            url: `/order/take/${row.id}`,\n            method: 'put',\n            ids: '',\n          }\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg)\n              this.$emit('changeGetTabs')\n              this.getList()\n              if (num) {\n                this.$refs.detailss.getSplitOrder(row.pid)\n              } else {\n                this.getData(row.id, 1)\n              }\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg)\n            })\n          // this.modalTitleSs = '修改确认收货';\n          break\n        case '10':\n          this.delfromData = {\n            title: '立即打印订单',\n            info: '您确认打印此订单吗?',\n            url: `/order/print/${row.id}`,\n            method: 'get',\n            ids: '',\n          }\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg)\n              this.$emit('changeGetTabs')\n              this.getList()\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg)\n            })\n          break\n        case '11':\n          this.delfromData = {\n            title: '立即打印电子面单',\n            info: '您确认打印此电子面单吗?',\n            url: `/order/order_dump/${row.id}`,\n            method: 'get',\n            ids: '',\n          }\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg)\n              this.getList()\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg)\n            })\n          break\n        case '12':\n          let pathInfo = this.$router.resolve({\n            path: this.roterPre + '/supplier/order/distribution',\n            query: {\n              id: row.id,\n              status: 2,\n            },\n          })\n          window.open(pathInfo.href, '_blank')\n          break\n\t\tcase '13':\n\t\t  this.printImg(row.kuaidi_label);\n\t\t  break;  \n        default:\n          this.delfromData = {\n            title: '删除订单',\n            url: `/order/del/${row.id}`,\n            method: 'DELETE',\n            ids: '',\n          }\n          // this.modalTitleSs = '删除订单';\n          this.delOrder(row, this.delfromData)\n      }\n    },\n\n    // 立即支付 /确认收货//删除单条订单\n    submitModel() {\n      this.getList()\n    },\n    pageChange(index) {\n      this.page.pageNum = index\n      this.getList()\n    },\n    limitChange(limit) {\n      this.page.pageSize = limit\n      this.getList()\n    },\n\n    // 订单列表\n    getList(res) {\n      if(res==1){\n        this.isAll = 0;\n        this.$refs.xTable.setAllCheckboxRow(false);\n        this.checkUidList = [];\n      }\n      this.page.pageNum = res === 1 ? 1 : this.page.pageNum\n      this.loading = true\n      orderList({\n        page: this.page.pageNum,\n        limit: this.page.pageSize,\n        status: this.orderStatus,\n        pay_type: this.orderPayType,\n        data: this.orderTime,\n        real_name: this.orderNum,\n        field_key: this.fieldKey,\n        type: this.type_id,\n        plat_type: this.currentTab,\n                store_id: this.store_id,\n        supplier_id: this.supplier_id,\n        user_reserve_type:1,\n      })\n        .then(async (res) => {\n          let data = res.data\n          data.data.forEach((item) => {\n            item.checkBox = this.isAll == 1;\n            if (item.id == this.orderId) {\n              this.rowActive = item\n            }\n          })\n          // this.orderList = data.data;\n          this.$set(this, 'orderList', data.data)\n          this.orderCards = data.stat\n          this.page.total = data.count\n          this.$emit('on-changeCards', data.stat)\n          this.loading = false\n          this.getTabs()\n          this.$nextTick(function(){\n            if (this.isAll == 1) {\n              if(this.isCheckBox){\n                this.$refs.xTable.setAllCheckboxRow(true);\n              }else{\n                this.$refs.xTable.setAllCheckboxRow(false);\n              }\n            }else{\n\t\t\t  let obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\t\t\t  if(!this.checkUidList.length || this.checkUidList.length <= obj.length){\n\t\t\t    this.$refs.xTable.setAllCheckboxRow(false);\n\t\t\t  }\n            }\n          })\n        })\n        .catch((res) => {\n          this.loading = false\n          this.$Message.error(res.msg)\n        })\n    },\n\n    // 编辑\n    edit(row) { \n      this.getOrderData(row.id)\n    },\n    splitOrderDetail(row) {\n      this.$router.push({\n        path: this.roterPre + 'split_list',\n        query: {\n          id: row.id,\n          orderChartType: this.orderStatus,\n        },\n      })\n    },\n\n    // 删除单条订单\n    delOrder(row, data) {\n      if (row.is_del === 1) {\n        this.$modalSure(data)\n          .then((res) => {\n            this.$Message.success(res.msg)\n            this.getList()\n            this.$refs.detailss.modals = false\n            this.$emit('changeGetTabs')\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg)\n          })\n      } else {\n        const title = '错误！'\n        const content =\n          '<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>'\n        this.$Modal.error({\n          title: title,\n          content: content,\n        })\n      }\n    },\n\n    // 获取编辑表单数据\n    getOrderData(id) {\n      getOrdeDatas(id)\n        .then(async (res) => {\n          if (res.data.status === false) {\n            return this.$authLapse(res.data)\n          }\n          this.$authLapse(res.data)\n          this.FromData = res.data\n          this.$refs.edits.modals = true\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg)\n        })\n    },\n\n    // 获取详情表单数据\n    getData(id, type) {\n      // this.$refs.detailss.modals = true;\n      getDataInfo(id)\n        .then(async (res) => {\n          if (!type) {\n            this.$refs.detailss.modals = true\n          }\n          // this.$refs.detailss.activeName = 'detail'\n          this.orderDatalist = res.data\n          if (this.orderDatalist.orderInfo.refund_reason_wap_img) {\n            try {\n              this.orderDatalist.orderInfo.refund_reason_wap_img = JSON.parse(\n                this.orderDatalist.orderInfo.refund_reason_wap_img\n              )\n            } catch (e) {\n              this.orderDatalist.orderInfo.refund_reason_wap_img = []\n            }\n          }\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg)\n        })\n    },\n\n    // 修改成功\n    submitFail(type) {\n      this.status = 0\n      this.getList()\n      if (this.orderConNum != 1) {\n        this.getData(this.orderId, 1)\n      } else {\n        this.$refs.detailss.getSplitOrder(this.orderConId)\n      }\n      if (type) {\n        this.$emit('changeGetTabs')\n      }\n    },\n\n    // 仅退款\n    getOnlyrefundData(id, refund_type) {\n      // this.$modalForm(getRefundFrom(id)).then(() => {\n      //   this.getList()\n      //   this.$emit('changeGetTabs')\n      //   this.$refs.detailss.modals = false\n      // })\n      let _info = this.rowActive._info;\n      let cart_info = Object.keys(_info).map((key) => {\n        return _info[key].cart_info;\n      });\n      cart_info.forEach((value) => {\n        value.refundPrice = this.$computes.Div(value.refund_price, value.cart_num);\n        value.refundNum = value.cart_num - value.refund_num;\n        value._disabled = !value.refundNum;\n      });\n      this.refundProduct = cart_info;\n      if (this.refundProductNum === 1) {\n        this.refundSelection = cart_info;\n      }\n      this.refundModal = true;\n    },\n\n    // 退货退款\n    getrefundData(id, refund_type) {\n      this.delfromData = {\n        title: '是否立即退货退款',\n        url: `/refund/agree/${id}`,\n        method: 'get',\n      }\n      this.$modalSure(this.delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg)\n          this.getList()\n          this.$emit('changeGetTabs')\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg)\n        })\n    },\n\n    // 获取退积分表单数据\n    getRefundIntegral(id) {\n      refundIntegral(id)\n        .then(async (res) => {\n          this.FromData = res.data\n          this.$refs.edits.modals = true\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg)\n        })\n    },\n\n    // 不退款表单数据\n    getNorefundData(id) {\n      this.$modalForm(getnoRefund(id)).then(() => {\n        this.getList()\n        this.$emit('changeGetTabs')\n      })\n    },\n\n    // 发送货\n    sendOrder(row, num) {\n      this.orderConId = row.pid\n      this.orderConNum = num\n      this.$store.commit('admin/order/setSplitOrder', row.total_num)\n      this.$refs.send.modals = true\n      this.orderId = row.id\n      this.status = row._status\n      this.pay_type = row.pay_type\n      this.$refs.send.getList()\n      this.$refs.send.getDeliveryList()\n      this.$nextTick((e) => {\n        this.$refs.send.getCartInfo(row._status, row.id)\n      })\n    },\n\n    // 配送信息表单数据\n    delivery(row, num) {\n      getDistribution(row.id)\n        .then(async (res) => {\n          this.orderConNum = num\n          this.orderConId = row.pid\n          this.FromData = res.data\n          this.$refs.edits.modals = true\n          if (num != 1) {\n            this.getData(this.orderId, 1)\n          }\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg)\n        })\n    },\n\n    // 订单导出\n    change(status) {},\n    async exports(value) {\n      this.exportListOn = this.exportList.findIndex(\n          (item) => item.name === value\n      )\n      let [th, filekey, data, fileName] = [[], [], [], '']\n      let excelData = {\n        ...this.where,\n        page: 1,\n        export_type: value,\n        ids: this.checkUidList.join()\n      }\n      for (let i = 0; i < excelData.page; i++) {\n        let lebData = await this.downOrderData(excelData)\n        if (!lebData.export.length) {\n          break;\n        }\n        if (!fileName) {\n          fileName = lebData.filename\n        }\n        if (!filekey.length) {\n          filekey = lebData.filekey\n        }\n        if (!th.length) {\n          th = lebData.header\n        }\n        data = data.concat(lebData.export)\n        excelData.page++\n      }\n      let sheetData = []\n      for (let j = 0; j < data.length; j++) {\n        let goodsList = data[j].goods_name.split('\\n')\n        for (let k = 0; k < goodsList.length; k++) {\n          let row = {...data[j]}\n          row.goods_name = goodsList[k]\n          if (k) {\n            for (const key in row) {\n              if (Object.hasOwnProperty.call(row, key)) {\n                if (key !== 'goods_name') {\n                  row[key] = null\n                }\n              }\n            }\n          }\n          sheetData.push(row)\n        }\n      }\n      exportExcel(th, filekey, fileName, sheetData)\n    },\n\n    downOrderData(excelData) {\n      return new Promise((resolve, reject) => {\n        storeOrderApi(excelData).then((res) => {\n          return resolve(res.data)\n        })\n      })\n    },\n\n    // 核销订单\n    bindWrite(row) {\n      let self = this\n      this.$Modal.confirm({\n        title: '提示',\n        content: '确定要核销该订单吗？',\n        cancelText: '取消',\n        closable: true,\n        maskClosable: true,\n        onOk: function () {\n          writeUpdate(row.order_id).then((res) => {\n            self.$Message.success(res.msg)\n            self.getList()\n          })\n        },\n        onCancel: () => {},\n      })\n    },\n    selectChange2() {\n      this.$emit('selectChange2', this.orderDataStatus)\n    },\n  },\n}\n", null]}