{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue?vue&type=template&id=65e1da4a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue", "mtime": 1690191972000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Modal',{attrs:{\"title\":\"优惠券列表\",\"width\":\"60%\"},on:{\"on-ok\":_vm.ok,\"on-cancel\":_vm.cancel},model:{value:(_vm.isTemplate),callback:function ($$v) {_vm.isTemplate=$$v},expression:\"isTemplate\"}},[_c('Table',{ref:\"table\",staticClass:\"mt25\",attrs:{\"columns\":_vm.columns,\"data\":_vm.couponList,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},on:{\"on-select\":_vm.handleSelectRow,\"on-select-cancel\":_vm.handleCancelRow,\"on-select-all\":_vm.handleSelectAll,\"on-select-all-cancel\":_vm.handleSelectAll,\"on-selection-change\":_vm.changeCheckbox},scopedSlots:_vm._u([{key:\"coupon_price\",fn:function(ref){\nvar row = ref.row;\nreturn [(row.coupon_type==1)?_c('span',[_vm._v(_vm._s(row.coupon_price)+\"元\")]):_vm._e(),(row.coupon_type==2)?_c('span',[_vm._v(_vm._s(parseFloat(row.coupon_price)/10)+\"折（\"+_vm._s(row.coupon_price.toString().split(\".\")[0])+\"%）\")]):_vm._e()]}},{key:\"count\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.is_permanent)?_c('span',[_vm._v(\"不限量\")]):_c('div',[_c('span',{staticClass:\"fa\"},[_vm._v(\"发布：\"+_vm._s(row.total_count))]),_c('span',{staticClass:\"sheng\"},[_vm._v(\"剩余：\"+_vm._s(row.remain_count))])])]}},{key:\"start_time\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.start_time)?_c('div',[_vm._v(\"\\n                        \"+_vm._s(_vm._f(\"formatDate\")(row.start_time))+\" - \"+_vm._s(_vm._f(\"formatDate\")(row.end_time))+\"\\n                    \")]):_c('span',[_vm._v(\"不限时\")])]}},{key:\"type\",fn:function(ref){\nvar row = ref.row;\nreturn [(row.type === 1)?_c('span',[_vm._v(\"品类券\")]):(row.type === 2)?_c('span',[_vm._v(\"商品券\")]):(row.type === 3)?_c('span',[_vm._v(\"会员券\")]):_c('span',[_vm._v(\"通用券\")])]}},{key:\"status\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('Tag',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.status===1),expression:\"row.status===1\"}],attrs:{\"color\":\"blue\"}},[_vm._v(\"正常\")]),_c('Tag',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.status===0),expression:\"row.status===0\"}],attrs:{\"color\":\"gold\"}},[_vm._v(\"未开启\")]),_c('Tag',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.status=== -1),expression:\"row.status=== -1\"}],attrs:{\"color\":\"red\"}},[_vm._v(\"已失效\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.tableFrom.limit},on:{\"on-change\":_vm.receivePageChange}})],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}