{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport vuedraggable from 'vuedraggable'\nimport linkaddress from '@/components/linkaddress';\n\nexport default {\n    name: 'c_product',\n    props: {\n        configObj: {\n            type: Object\n        },\n        configNme: {\n            type: String\n        },\n        index: {\n            type: null\n        }\n    },\n    components: {\n        linkaddress,\n        draggable: vuedraggable\n    },\n    data () {\n        return {\n            defaults: {},\n            configData: {},\n            itemObj: {},\n            activeIndex: 0\n        }\n    },\n    mounted () {\n        this.$nextTick(() => {\n            this.defaults = this.configObj\n            this.configData = this.configObj[this.configNme]\n        })\n    },\n    watch: {\n        configObj: {\n            handler (nVal, oVal) {\n                this.defaults = nVal\n                this.configData = nVal[this.configNme]\n            },\n            deep: true\n        }\n    },\n    methods: {\n        linkUrl(e){\n            this.configData.list[this.activeIndex].chiild[1].val = e\n        },\n        getLink (index,key,item){\n            if(!key || item.link){\n                return\n            }\n            this.activeIndex = index\n            this.$refs.linkaddres.modals = true\n        },\n        addHotTxt () {\n            if (this.configData.list.length == 0) {\n                let storage = window.localStorage;\n                this.itemObj = JSON.parse(storage.getItem('itemObj'));\n                if(this.itemObj.link){\n                    this.itemObj.link.activeVal = 0;\n                }\n                this.itemObj.chiild[0].val='';\n                this.itemObj.chiild[1].val='';\n                this.configData.list.push(this.itemObj)\n            } else {\n                let obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]));\n                if(obj.chiild[0].empty){\n                    obj.chiild[0].val='';\n                    obj.chiild[1].val='';\n                }\n                this.configData.list.push(obj)\n            }\n        },\n        // 删除数组\n        bindDelete (index) {\n            if (this.configData.list.length == 1) {\n                let itemObj = this.configData.list[0];\n                this.itemObj = itemObj;\n                let storage = window.localStorage;\n                storage.setItem('itemObj', JSON.stringify(itemObj));\n            }\n            this.configData.list.splice(index, 1)\n            this.configData.tabCur = 0;\n            this.$emit('getConfig', { name: 'delete', indexs: 0})\n        },\n        sliderChange (index) {\n            this.configData.tabCur = index;\n            this.$emit('getConfig', { name: 'product', indexs: index })\n        },\n        activeBtn (index) {\n            this.configData.tabCur = index;\n            this.$emit('getConfig', { name: 'product', indexs: index })\n        }\n    }\n}\n", null]}