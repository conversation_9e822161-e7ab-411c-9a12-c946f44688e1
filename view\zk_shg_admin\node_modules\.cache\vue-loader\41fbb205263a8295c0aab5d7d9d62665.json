{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\icon\\icon.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\icon\\icon.vue", "mtime": 1725352513070}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n\nconst prefixCls = 'ivu-icon';\n\nexport default {\n    name: 'Icon',\n    props: {\n        type: {\n            type: String,\n            default: ''\n        },\n        size: [Number, String],\n        color: String,\n        custom: {\n            type: String,\n            default: ''\n        }\n    },\n    computed: {\n        classes () {\n            return [\n                `${prefixCls}`,\n                {\n                    [`${prefixCls}-${this.type}`]: this.type !== '',\n                    [`${this.custom}`]: this.custom !== '',\n                }\n            ];\n        },\n        styles () {\n            let style = {};\n\n            if (this.size) {\n                style['font-size'] = `${this.size}px`;\n            }\n\n            if (this.color) {\n                style.color = this.color;\n            }\n\n            return style;\n        }\n    },\n    methods: {\n        handleClick (event) {\n            this.$emit('click', event);\n        }\n    }\n};\n", null]}