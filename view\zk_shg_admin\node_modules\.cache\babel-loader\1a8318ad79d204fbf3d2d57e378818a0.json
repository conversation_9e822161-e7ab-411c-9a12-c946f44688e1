{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\offline\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\offline\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { orderScanList, orderOfflineScan } from \"@/api/order\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  data: function data() {\n    return {\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      thead: [{\n        title: \"订单号\",\n        key: \"order_id\"\n      }, {\n        title: \"用户信息\",\n        key: \"nickname\"\n      }, {\n        title: \"实际支付\",\n        key: \"pay_price\"\n      }, {\n        title: \"优惠价格\",\n        key: \"true_price\"\n      }, {\n        title: \"支付时间\",\n        key: \"pay_time\"\n      }],\n      tbody: [],\n      loading: false,\n      total: 0,\n      animal: 1,\n      pagination: {\n        page: 1,\n        limit: 30,\n        order_id: \"\",\n        add_time: \"\"\n      },\n      options: timeOptions,\n      timeVal: [],\n      modal: false,\n      qrcode: null,\n      name: \"\",\n      spin: false\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getOrderList();\n  },\n  methods: {\n    onchangeCode: function onchangeCode(e) {\n      this.animal = e;\n      this.qrcodeShow();\n    },\n    // 具体日期搜索()；\n    onchangeTime: function onchangeTime(e) {\n      this.pagination.page = 1;\n      this.timeVal = e;\n      this.pagination.add_time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.getOrderList();\n    },\n    // 订单列表\n    getOrderList: function getOrderList() {\n      var _this = this;\n\n      this.loading = true;\n      orderScanList(this.pagination).then(function (res) {\n        _this.loading = false;\n        var _res$data = res.data,\n            count = _res$data.count,\n            list = _res$data.list;\n        _this.total = count;\n        _this.tbody = list;\n      }).catch(function (err) {\n        _this.loading = false;\n\n        _this.$Message.error(err.msg);\n      });\n    },\n    // 分页\n    pageChange: function pageChange(index) {\n      this.pagination.page = index;\n      this.getOrderList();\n    },\n    search: function search() {\n      this.pagination.page = 1;\n      this.getOrderList();\n    },\n    // 查看二维码\n    qrcodeShow: function qrcodeShow() {\n      var _this2 = this;\n\n      this.spin = true;\n      orderOfflineScan({\n        type: this.animal\n      }).then(function (res) {\n        _this2.spin = false;\n        _this2.qrcode = res.data;\n        _this2.modal = true;\n      }).catch(function (err) {\n        _this2.spin = false;\n\n        _this2.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}