{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\queueList.vue?vue&type=template&id=93c4597c&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\queueList.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <Modal v-model=\"modal\" title=\"任务列表\" width=\"1000\" footer-hide>\n        <!-- <div> -->\n        <!-- <div class=\"i-layout-page-header\">\n            <PageHeader class=\"product_tabs\" title=\"任务列表\" hidden-breadcrumb></PageHeader>\n        </div> -->\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n            <Form ref=\"formValidate\" :model=\"formValidate\" :label-width=\"labelWidth\" :label-position=\"labelPosition\" class=\"tabform\" @submit.native.prevent>\n                <Row :gutter=\"24\" type=\"flex\">\n                    <Col span=\"10\">\n                        <FormItem label=\"操作时间：\">\n                            <DatePicker\n                                :editable=\"false\"\n                                @on-change=\"onchangeTime\"\n                                :value=\"timeVal\"\n                                format=\"yyyy/MM/dd\"\n                                type=\"datetimerange\"\n                                placement=\"bottom-start\"\n                                placeholder=\"自定义时间\"\n                                style=\"width: 90%\"\n                                :options=\"options\"\n                            ></DatePicker>\n                        </FormItem>\n                    </Col>\n                    <Col span=\"7\">\n                        <FormItem label=\"类型：\">\n                            <Select v-model=\"formValidate.type\" clearable @on-change=\"typeSearchs\">\n                                <Option v-for=\"item in typeList\" :value=\"item.value\" :key=\"item.value\">{{ item.label }}</Option>\n                            </Select>\n                        </FormItem>\n                    </Col>\n                    <Col span=\"7\">\n                        <FormItem label=\"状态：\">\n                            <Select v-model=\"formValidate.status\" clearable @on-change=\"statusSearchs\">\n                                <Option v-for=\"item in statusList\" :value=\"item.value\" :key=\"item.value\">{{ item.label }}</Option>\n                            </Select>\n                        </FormItem>\n                    </Col>\n                </Row>\n            </Form>\n            <Table class=\"mt25\" height=\"530\" :columns=\"columns1\" :data=\"data1\" :loading=\"loading\">\n                <template slot-scope=\"{ row, index }\" slot=\"action\">\n                    <template v-if=\"row.is_show_log\">\n                        <a @click=\"deliveryLook(row)\">查看</a>\n                        <Divider type=\"vertical\" />\n                    </template>\n                    <template>\n                        <Dropdown @on-click=\"changeMenu(row, $event)\">\n                            <a>更多<Icon type=\"ios-arrow-down\"></Icon></a>\n                            <DropdownMenu slot=\"list\">\n                                <DropdownItem name=\"1\">下载</DropdownItem>\n                                <DropdownItem name=\"2\">重新执行</DropdownItem>\n                                <DropdownItem v-if=\"row.is_stop_button\" name=\"3\">停止任务</DropdownItem>\n                                <DropdownItem v-if=\"row.is_error_button\" name=\"4\">清除异常任务</DropdownItem>\n                            </DropdownMenu>\n                        </Dropdown>\n                    </template>\n                </template>\n            </Table>\n            <div class=\"acea-row row-right page\">\n                <Page :total=\"page1.total\" :current=\"page1.pageNum\" show-elevator show-total @on-change=\"pageChange\" @on-page-size-change=\"limitChange\" :page-size=\"page1.pageSize\" show-sizer />\n            </div>\n        </Card>\n        <Modal v-model=\"modal1\" width=\"900\" footer-hide>\n            <Table height=\"500\" class=\"mt25\" :columns=\"columns4\" :data=\"data2\" :loading=\"loading2\"></Table>\n            <div class=\"acea-row row-right page\">\n                <Page :total=\"page2.total\" :current=\"page2.pageNum\" show-elevator show-total @on-change=\"pageChange2\" @on-page-size-change=\"limitChange2\" :page-size=\"page2.pageSize\" show-sizer />\n            </div>\n        </Modal>\n        <!-- </div> -->\n</Modal>\n", null]}