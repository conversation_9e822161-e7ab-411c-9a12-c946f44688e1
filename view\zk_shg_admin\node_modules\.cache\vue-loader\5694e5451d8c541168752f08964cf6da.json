{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\index.vue", "mtime": 1685091240000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\timport { mapState } from \"vuex\";\n\timport timeOptions from \"@/utils//timeOptions\";\n\timport {getGroupTemplateChatList,workGroupTemplateSendMsg} from \"@/api/work\";\n\timport { log } from 'util';\n\t// import { apiGoods } from \"@/mock/mock\";\nimport Setting from \"@/setting\";\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n        roterPre: Setting.roterPre,\n\t\t\t\toptions: timeOptions,\n\t\t\t\ttimeVal: [],\n\t\t\t\ttableFrom: {\n\t\t\t\t\tname:\"\",\n\t\t\t\t\tcreate_time:\"\",\n\t\t\t\t\tclient_type:\"\",\n\t\t\t\t\tpage: 1,\n\t\t\t\t\ttype: '1',\n\t\t\t\t\tlimit: 15,\n\t\t\t\t},\n\t\t\t\ttotal: 0,\n\t\t\t\ttableList: [],\n\t\t\t\tloading: false,\n\t\t\t\ttableData: [],\n\t\t\t\tcolumns1: [\n                    {\n                        title: '群发名称',\n                        key: 'name',\n                        minWidth: 150\n                    },\n                    {\n                        title: '已发送群主',\n                        key: 'user_count',\n                        minWidth: 120\n                    },\n                    {\n                        title: '送达群聊',\n                        key: 'external_user_count',\n                        minWidth: 120\n                    },\n                    {\n                        title: '未发送群主',\n                        key: 'unuser_count',\n                        minWidth: 120\n                    },\n                    {\n                        title: '未送达群聊',\n                        key: 'external_unuser_count',\n                        minWidth: 120\n\t\t\t\t\t},\n\t\t\t\t\t{\n                        title: '是否发送',\n                        slot: 'send_type',\n                        minWidth: 120\n                    },\n\t\t\t\t\t{\n                        title: '群发类型',\n                        slot: 'template_type',\n                        minWidth: 120\n                    },\n                    {\n                        title: '发送时间',\n                        key: 'update_time',\n                        minWidth: 150\n                    },\n\t\t\t\t\t{\n                        title: '创建时间',\n                        key: 'create_time',\n                        minWidth: 150\n                    },\n                    {\n                        title: '操作',\n                        slot: 'action',\n                        fixed: 'right',\n                        minWidth: 170\n                    }\n                ],\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t  ...mapState(\"admin/layout\", [\"isMobile\"]),\n\t\t  labelWidth() {\n\t\t    return this.isMobile ? undefined : 96;\n\t\t  },\n\t\t  labelPosition() {\n\t\t    return this.isMobile ? \"top\" : \"right\";\n\t\t  }\n\t\t},\n\t\tcreated() {\n\t\t\tthis.getList();\n\t\t},\n\t\tmethods: {\n\t\t\tgetList(){\n\t\t\t\tthis.loading = true;\n\t\t\t\tgetGroupTemplateChatList(this.tableFrom).then(res=>{\n\t\t\t\t\tthis.tableData = res.data;\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t}).catch(err=>{\n\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t})\n\t\t\t},\n\t\t\tsearch(){\n\t\t\t\tthis.tableFrom.page = 1;\n\t\t\t\tthis.getList();\n\t\t\t},\n\t\t\t// 具体日期\n            onchangeTime (e) {\n                this.timeVal = e\n\t\t\t\tthis.tableFrom.time = this.timeVal.join(\"-\");\n                this.tableFrom.page = 1;\n\t\t\t\tthis.getList();\n            },\n\t\t\tpageChange(index){\n\t\t\t\tthis.tableFrom.page = index;\n\t\t\t\tthis.getList();\n\t\t\t},\n\t\t\t// 删除\n\t\t\tdelItem(row,index){\n\t\t\t\tlet delfromData = {\n\t\t\t\t\ttitle: '删除该客户群发',\n\t\t\t\t\tnum:index,\n\t\t\t\t\turl: `work/group_template_chat/${row.id}`,\n\t\t\t\t\tmethod: \"DELETE\",\n\t\t\t\t\tids: \"\",\n\t\t\t\t};\n\t\t\t\tthis.$modalSure(delfromData)\n\t\t\t\t\t.then((res) => {\n\t\t\t\t\tthis.$Message.success(res.msg);\n          this.tableData.list.splice(index, 1);\n          if (!this.tableData.list.length) {\n             this.tableFrom.page =\n             this.tableFrom.page == 1 ? 1 : this.tableFrom.page - 1;\n          }\n          this.getList();\n\t\t\t\t\t})\n\t\t\t\t\t.catch((res) => {\n\t\t\t\t\tthis.$Message.error(res.msg);\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 详情\n\t\t\tdetailsItem(row,index) {\n\t\t\t\tthis.$router.push(this.roterPre + \"/work/group/template_info/\"+ row.id)\n\t\t\t},\n\t\t\t// 提醒发送\n\t\t\tsendMessage(row,index) {\n\t\t\t\tworkGroupTemplateSendMsg({\n\t\t\t\t\tuserid:\"\",\n\t\t\t\t\ttime:row.update_time,\n\t\t\t\t\tid:row.id\n\t\t\t\t}).then(res=>{\n\t\t\t\t\tthis.$Message.success(res.msg)\n\t\t\t\t}).catch(err=>{\n\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n", null]}