{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport Setting from \"@/setting\";\nimport util from \"@/libs/util\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport { importCard, exportProductCard } from \"@/api/product\";\nexport default {\n  name: \"addCarMy\",\n  props: {\n    virtualList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  data: function data() {\n    return {\n      cartMyType: 1,\n      fixedCar: {\n        disk_info: '',\n        stock: 0\n      },\n      cardUrl: Setting.apiBaseURL + \"/file/upload/1\",\n      header: {} //请求头部信息\n\n    };\n  },\n  created: function created() {\n    this.getToken();\n  },\n  mounted: function mounted() {},\n  methods: {\n    // 下载卡密\n    getCarMyList: function () {\n      var _getCarMyList = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        var th, filekey, data, fileName, lebData;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                th = [], filekey = [], data = [], fileName = \"\";\n                _context.next = 3;\n                return this.getExcelData();\n\n              case 3:\n                lebData = _context.sent;\n                if (!fileName) fileName = lebData.filename;\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) th = lebData.header;\n                data = lebData.export;\n                exportExcel(th, filekey, fileName, data);\n\n              case 9:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function getCarMyList() {\n        return _getCarMyList.apply(this, arguments);\n      }\n\n      return getCarMyList;\n    }(),\n    getExcelData: function getExcelData() {\n      return new Promise(function (resolve, reject) {\n        exportProductCard().then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    },\n    removeVirtual: function removeVirtual(index) {\n      this.virtualList.splice(index, 1);\n    },\n    upFile: function upFile(res) {\n      var _this = this;\n\n      importCard({\n        file: res.data.src\n      }).then(function (res) {\n        _this.$emit('changeVirtual', JSON.parse(JSON.stringify(res.data))); //this.$refs.upload.clearFiles();\t\n\n      }).catch(function (err) {\n        return _this.$Message.error(err.msg);\n      });\n    },\n    handleFormatError: function handleFormatError(file) {\n      return this.$Message.error('必须上传xlsx格式文件');\n    },\n    // 上传头部token\n    getToken: function getToken() {\n      this.header[\"Authori-zation\"] = \"Bearer \" + util.cookies.get(\"token\");\n    },\n    cancel: function cancel() {\n      this.$emit('closeCarMy');\n    },\n    handleAdd: function handleAdd() {\n      this.virtualList.push({\n        key: \"\",\n        value: \"\"\n      });\n    },\n    beforeUpload: function beforeUpload() {\n      var _this2 = this;\n\n      var promise = new Promise(function (resolve) {\n        _this2.$nextTick(function () {\n          resolve(true);\n        });\n      });\n      return promise;\n    },\n    subBtn: function subBtn() {\n      if (this.cartMyType == 1) {\n        this.fixedCar.cartMyType = 1;\n\n        if (this.fixedCar.disk_info == '') {\n          return this.$Message.error(\"请填写卡密信息\");\n        }\n\n        if (!this.fixedCar.stock) {\n          return this.$Message.error(\"请填写库存数量\");\n        }\n\n        this.$emit('fixdBtn', JSON.parse(JSON.stringify(this.fixedCar)));\n      } else {\n        var data = {\n          cartMyType: 2,\n          virtualList: this.virtualList\n        };\n\n        for (var i = 0; i < this.virtualList.length; i++) {\n          var element = this.virtualList[i];\n\n          if (!element.value) {\n            return this.$Message.error(\"请输入所有卡密\");\n          }\n        }\n\n        this.$emit('fixdBtn', JSON.parse(JSON.stringify(data)));\n      }\n    }\n  }\n};", null]}