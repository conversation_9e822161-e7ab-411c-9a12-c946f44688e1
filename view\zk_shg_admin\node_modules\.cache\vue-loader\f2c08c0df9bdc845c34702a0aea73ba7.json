{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue?vue&type=style&index=0&id=4c7f6892&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    /deep/.ivu-input {\n\t\tfont-size: 12px!important\n\t}\n\t/deep/.ivu-input-word-count{\n\t\tcolor: #BBBBBB;\n\t}\n\t/deep/.ivu-input-icon{\n\t\tcolor: #BBBBBB;\n\t}\n    .c_product {\n\t\tmargin-bottom: 20px\n\t\tpadding: 0 15px 20px 15px\n\t\t.list-box{\n\t\t\t.item{\n\t\t\t\tposition relative\n\t\t\t\tdisplay flex\n\t\t\t\tpadding 18px 20px 18px 0\n\t\t\t\tbackground-color: #F9F9F9\n\t\t\t\tborder-radius: 3px;\n\t\t\t\t\n\t\t\t\t.delete{\n\t\t\t\t\tposition absolute\n\t\t\t\t\tright 0\n\t\t\t\t\ttop 0\n\t\t\t\t\tright: -13px;\n\t\t\t\t\ttop: -14px;\n\t\t\t\t\tcolor #ccc;\n\t\t\t\t\tcursor pointer\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.move-icon{\n\t\t\t\tdisplay flex\n\t\t\t\talign-items center\n\t\t\t\tjustify-content center\n\t\t\t\twidth 40px\n\t\t\t\tcursor move\n\t\t\t}\n\t\t\t\n\t\t\t.content{\n\t\t\t\tflex 1\n\t\t\t\t\n\t\t\t\t.con-item{\n\t\t\t\t\tdisplay flex\n\t\t\t\t\talign-items center\n\t\t\t\t\tmargin-bottom 15px\n\t\t\t\t\t\n\t\t\t\t\t&:last-child{\n\t\t\t\t\t\tmargin-bottom 0\n\t\t\t\t\t}\n\t\t\t\t\tspan{\n\t\t\t\t\t\twidth 45px\n\t\t\t\t\t\tfont-size 12px\n\t\t\t\t\t\tcolor: #999\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}    \n\t\t.add-btn {\n\t\t\tmargin-top: 20px\n\t\t\t.btn{\n\t\t\t\twidth: 100%; \n\t\t\t\theight: 36px; \n\t\t\t\tborder-color:#EEEEEE; \n\t\t\t\tcolor: #666666;\n\t\t\t\t.iconfont{\n\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\tmargin-right: 5px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n    .title {\n\t\tpadding-top: 20px\n\t\tfont-size: 12px\n\t\tcolor: #999\n\t}\n    .iconfont-diy {\n\t\tcolor: #DDDDDD\n\t\tfont-size: 16px\n\t}\n", null]}