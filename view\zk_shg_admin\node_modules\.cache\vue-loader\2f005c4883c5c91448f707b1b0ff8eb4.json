{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { brandCascader, productBrand, productBrandrev } from \"@/api/product\";\n\nexport default {\n  name: \"menusFrom\",\n  props: {\n    formValidate: {\n      type: Object,\n      default: null,\n    },\n    fromName: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      ruleValidate: {\n        brand_name: [\n          { required: true, message: '请输入品牌名称', trigger: 'blur' }\n        ],\n      },\n      type: 1,\n      modals: false,\n      // authType:false,\n      FromData: [],\n      titleFrom: '',\n      grid: {\n        xl: 24,\n        lg: 24,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n    }\n  },\n  mounted() {\n    this.getAddFrom()\n  },\n  methods: {\n    // 获取新增表单\n    getAddFrom() {\n      brandCascader().then(async (res) => {\n        this.FromData = res.data;\n      })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    handleReset() {\n      this.modals = false;\n      this.$parent.getData()\n    },\n    handleSubmit(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if (this.type == 2) {\n            productBrandrev(this.formValidate.id, this.formValidate).then(res => {\n              this.$Message.success(res.msg)\n              this.getAddFrom()\n              this.$parent.getData()\n              this.modals = false\n            }).catch(err => {\n              this.$Message.error(err.msg)\n            })\n          } else {\n            productBrand(this.formValidate).then(res => {\n              this.$Message.success(res.msg)\n              this.getAddFrom()\n              if (this.fromName) {\n                this.$parent.getBrandList();\n              } else {\n                this.$parent.getData()\n              }\n              this.modals = false\n            }).catch(err => {\n              this.$Message.error(err.msg)\n            })\n          }\n        } else {\n          this.$Message.error('请输入品牌名称');\n        }\n      })\n\n    },\n    cancle() {\n      this.modals = false\n    }\n  }\n};\n", null]}