{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_set_up.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_set_up.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    export default {\n        name: 'c_set_up',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            }\n        },\n        data () {\n            return {\n                defaults: {},\n                configData: {},\n\t\t\t\tlist:['内容','样式'],\n\t\t\t\tcurrent:0\n            }\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n\t\t\t\t\tthis.current = this.configData.tabVal\n                },\n                deep: true\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj\n                this.configData = this.configObj[this.configNme]\n\t\t\t\tthis.current = this.configData.tabVal\n            })\n        },\n        methods: {\n\t\t\tonClickTab(index){\n\t\t\t\tthis.configData.tabVal = index;\n\t\t\t\tthis.current = index;\n\t\t\t}\n            // onClickTab (e) {\n            //     this.$emit('getConfig', e);\n            // }\n        }\n    }\n", null]}