{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\modal\\modal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\modal\\modal.vue", "mtime": 1725352513362}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Icon from '../icon';\nimport iButton from '../button/button.vue';\nimport TransferDom from '../../directives/transfer-dom';\nimport Locale from '../../mixins/locale';\nimport Emitter from '../../mixins/emitter';\nimport ScrollbarMixins from './mixins-scrollbar';\n\nimport { on, off } from '../../utils/dom';\nimport { findComponentsDownward } from '../../utils/assist';\n\nimport { transferIndex as modalIndex, transferIncrease as modalIncrease } from '../../utils/transfer-queue';\n\nconst prefixCls = 'ivu-modal';\n\nexport default {\n    name: 'Modal',\n    mixins: [ Locale, Emitter, ScrollbarMixins ],\n    components: { Icon, iButton },\n    directives: { TransferDom },\n    props: {\n        value: {\n            type: Boolean,\n            default: false\n        },\n        closable: {\n            type: Boolean,\n            default: true\n        },\n        maskClosable: {\n            type: Boolean,\n            default () {\n                return !this.$IVIEW || this.$IVIEW.modal.maskClosable === '' ? true : this.$IVIEW.modal.maskClosable;\n            }\n        },\n        title: {\n            type: String\n        },\n        width: {\n            type: [Number, String],\n            default: 520\n        },\n        okText: {\n            type: String\n        },\n        cancelText: {\n            type: String\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        styles: {\n            type: Object,\n            default() {\n                return {};\n            }\n        },\n        className: {\n            type: String\n        },\n        // for instance\n        footerHide: {\n            type: Boolean,\n            default: false\n        },\n        scrollable: {\n            type: Boolean,\n            default: false\n        },\n        transitionNames: {\n            type: Array,\n            default () {\n                return ['ease', 'fade'];\n            }\n        },\n        transfer: {\n            type: Boolean,\n            default () {\n                return !this.$IVIEW || this.$IVIEW.transfer === '' ? true : this.$IVIEW.transfer;\n            }\n        },\n        fullscreen: {\n            type: Boolean,\n            default: false\n        },\n        mask: {\n            type: Boolean,\n            default: true\n        },\n        draggable: {\n            type: Boolean,\n            default: false\n        },\n        zIndex: {\n            type: Number,\n            default: 1000\n        },\n    },\n    data () {\n        return {\n            prefixCls: prefixCls,\n            wrapShow: false,\n            showHead: true,\n            buttonLoading: false,\n            visible: this.value,\n            dragData: {\n                x: null,\n                y: null,\n                dragX: null,\n                dragY: null,\n                dragging: false\n            },\n            modalIndex: this.handleGetModalIndex(),  // for Esc close the top modal\n            isMouseTriggerIn: false, // #5800\n        };\n    },\n    computed: {\n        wrapClasses () {\n            return [\n                `${prefixCls}-wrap`,\n                {\n                    [`${prefixCls}-hidden`]: !this.wrapShow,\n                    [`${this.className}`]: !!this.className,\n                    [`${prefixCls}-no-mask`]: !this.showMask\n                }\n            ];\n        },\n        wrapStyles () {\n            return {\n                zIndex: this.modalIndex + this.zIndex\n            };\n        },\n        maskClasses () {\n            return `${prefixCls}-mask`;\n        },\n        classes () {\n            return [\n                `${prefixCls}`,\n                {\n                    [`${prefixCls}-fullscreen`]: this.fullscreen,\n                    [`${prefixCls}-fullscreen-no-header`]: this.fullscreen && !this.showHead,\n                    [`${prefixCls}-fullscreen-no-footer`]: this.fullscreen && this.footerHide\n                }\n            ];\n        },\n        contentClasses () {\n            return [\n                `${prefixCls}-content`,\n                {\n                    [`${prefixCls}-content-no-mask`]: !this.showMask,\n                    [`${prefixCls}-content-drag`]: this.draggable,\n                    [`${prefixCls}-content-dragging`]: this.draggable && this.dragData.dragging\n                }\n            ];\n        },\n        mainStyles () {\n            let style = {};\n\n            const width = parseInt(this.width);\n            const styleWidth = this.dragData.x !== null ? {\n                top: 0\n            } : {\n                width: width <= 100 ? `${width}%` : `${width}px`\n            };\n\n            const customStyle = this.styles ? this.styles : {};\n\n            Object.assign(style, styleWidth, customStyle);\n\n            return style;\n        },\n        contentStyles () {\n            let style = {};\n\n            if (this.draggable) {\n                let customTop = this.styles.top ? parseFloat(this.styles.top) : 0;\n                let customLeft = this.styles.left ? parseFloat(this.styles.left) : 0;\n                if (this.dragData.x !== null) style.left = `${this.dragData.x - customLeft}px`;\n                if (this.dragData.y !== null) style.top = `${this.dragData.y - customTop}px`;\n                const width = parseInt(this.width);\n                const styleWidth = {\n                    width: width <= 100 ? `${width}%` : `${width}px`\n                };\n\n                Object.assign(style, styleWidth);\n            }\n\n            return style;\n        },\n        localeOkText () {\n            if (this.okText === undefined) {\n                return this.t('i.modal.okText');\n            } else {\n                return this.okText;\n            }\n        },\n        localeCancelText () {\n            if (this.cancelText === undefined) {\n                return this.t('i.modal.cancelText');\n            } else {\n                return this.cancelText;\n            }\n        },\n        showMask () {\n            return this.draggable ? false : this.mask;\n        }\n    },\n    methods: {\n        close () {\n            this.visible = false;\n            this.$emit('input', false);\n            this.$emit('on-cancel');\n        },\n        handleMask () {\n            if (this.maskClosable && this.showMask) {\n                this.close();\n            }\n        },\n        handleWrapClick (event) {\n            if (this.isMouseTriggerIn) {\n                this.isMouseTriggerIn = false;\n                return;\n            }\n            // use indexOf,do not use === ,because ivu-modal-wrap can have other custom className\n            const className = event.target.getAttribute('class');\n            if (className && className.indexOf(`${prefixCls}-wrap`) > -1) this.handleMask();\n        },\n        handleMousedown () {\n            this.isMouseTriggerIn = true;\n        },\n        cancel () {\n            this.close();\n        },\n        ok () {\n            if (this.loading) {\n                this.buttonLoading = true;\n            } else {\n                this.visible = false;\n                this.$emit('input', false);\n            }\n            this.$emit('on-ok');\n        },\n        EscClose (e) {\n            if (this.visible && this.closable) {\n                if (e.keyCode === 27) {\n                    const $Modals = findComponentsDownward(this.$root, 'Modal').filter(item => item.$data.visible && item.$props.closable);\n\n                    const $TopModal = $Modals.sort((a, b) => {\n                        return a.$data.modalIndex < b.$data.modalIndex ? 1 : -1;\n                    })[0];\n\n                    setTimeout(() => {\n                        $TopModal.close();\n                    }, 0);\n                }\n            }\n        },\n        animationFinish() {\n            this.$emit('on-hidden');\n        },\n        handleMoveStart (event) {\n            if (!this.draggable) return false;\n\n            const $content = this.$refs.content;\n            const rect = $content.getBoundingClientRect();\n            this.dragData.x = rect.x || rect.left;\n            this.dragData.y = rect.y || rect.top;\n\n            const distance = {\n                x: event.clientX,\n                y: event.clientY\n            };\n\n            this.dragData.dragX = distance.x;\n            this.dragData.dragY = distance.y;\n\n            this.dragData.dragging = true;\n\n            on(window, 'mousemove', this.handleMoveMove);\n            on(window, 'mouseup', this.handleMoveEnd);\n        },\n        handleMoveMove (event) {\n            if (!this.dragData.dragging) return false;\n\n            const distance = {\n                x: event.clientX,\n                y: event.clientY\n            };\n\n            const diff_distance = {\n                x: distance.x - this.dragData.dragX,\n                y: distance.y - this.dragData.dragY\n            };\n\n            this.dragData.x += diff_distance.x;\n            this.dragData.y += diff_distance.y;\n\n            this.dragData.dragX = distance.x;\n            this.dragData.dragY = distance.y;\n        },\n        handleMoveEnd () {\n            this.dragData.dragging = false;\n            off(window, 'mousemove', this.handleMoveMove);\n            off(window, 'mouseup', this.handleMoveEnd);\n        },\n        handleGetModalIndex () {\n            modalIncrease();\n            return modalIndex;\n        },\n        handleClickModal () {\n            if (this.draggable) {\n                this.modalIndex = this.handleGetModalIndex();\n            }\n        }\n    },\n    mounted () {\n        if (this.visible) {\n            this.wrapShow = true;\n        }\n\n        let showHead = true;\n\n        if (this.$slots.header === undefined && !this.title) {\n            showHead = false;\n        }\n\n        this.showHead = showHead;\n\n        // ESC close\n        document.addEventListener('keydown', this.EscClose);\n    },\n    beforeDestroy () {\n        document.removeEventListener('keydown', this.EscClose);\n        this.removeScrollEffect();\n    },\n    watch: {\n        value (val) {\n            this.visible = val;\n        },\n        visible (val) {\n            if (val === false) {\n                this.buttonLoading = false;\n                this.timer = setTimeout(() => {\n                    this.wrapShow = false;\n                    this.removeScrollEffect();\n                }, 300);\n            } else {\n                this.modalIndex = this.handleGetModalIndex();\n\n                if (this.timer) clearTimeout(this.timer);\n                this.wrapShow = true;\n                if (!this.scrollable) {\n                    this.addScrollEffect();\n                }\n            }\n            this.broadcast('Table', 'on-visible-change', val);\n            this.broadcast('Slider', 'on-visible-change', val);  // #2852\n            this.$emit('on-visible-change', val);\n        },\n        loading (val) {\n            if (!val) {\n                this.buttonLoading = false;\n            }\n        },\n        scrollable (val) {\n            if (!val) {\n                this.addScrollEffect();\n            } else {\n                this.removeScrollEffect();\n            }\n        },\n        title (val) {\n            if (this.$slots.header === undefined) {\n                this.showHead = !!val;\n            }\n        }\n    }\n};\n", null]}