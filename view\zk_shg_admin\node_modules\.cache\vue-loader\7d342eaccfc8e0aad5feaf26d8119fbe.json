{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue?vue&type=template&id=3ebb3761&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Modal',{attrs:{\"width\":\"700\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":_vm.titleFrom,\"z-index\":1,\"class-name\":\"vertical-center-modal\"},on:{\"on-cancel\":_vm.handleReset},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('Form',{ref:\"formValidate\",attrs:{\"model\":_vm.formValidate,\"label-width\":110,\"rules\":_vm.ruleValidate},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"上级品牌：\"}},[_c('Cascader',{attrs:{\"data\":_vm.FromData,\"placeholder\":\"请选择上级品牌\",\"change-on-select\":\"\"},model:{value:(_vm.formValidate.fid),callback:function ($$v) {_vm.$set(_vm.formValidate, \"fid\", $$v)},expression:\"formValidate.fid\"}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"品牌名称：\",\"prop\":\"brand_name\"}},[_c('Input',{attrs:{\"maxlength\":\"7\",\"placeholder\":\"请输入品牌名称\",\"prop\":\"\"},model:{value:(_vm.formValidate.brand_name),callback:function ($$v) {_vm.$set(_vm.formValidate, \"brand_name\", $$v)},expression:\"formValidate.brand_name\"}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"品牌排序：\"}},[_c('InputNumber',{staticStyle:{\"width\":\"100%\"},attrs:{\"step\":1,\"placeholder\":\"请输入品牌排序\"},model:{value:(_vm.formValidate.sort),callback:function ($$v) {_vm.$set(_vm.formValidate, \"sort\", $$v)},expression:\"formValidate.sort\"}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"是否显示：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.formValidate.is_show),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_show\", $$v)},expression:\"formValidate.is_show\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"style-add\"},[_c('Button',{staticClass:\"mr14\",attrs:{\"type\":\"default\"},on:{\"click\":_vm.cancle}},[_vm._v(\"取消\")]),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.handleSubmit('formValidate')}}},[_vm._v(\"确认\")])],1)])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}