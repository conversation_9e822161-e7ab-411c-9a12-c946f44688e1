{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_nav_bar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_nav_bar.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    export default {\n        name: 'c_nav_bar',\n        componentsName: 'nav_bar',\n        cname: '导航',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            // formCreate: formCreate.$form()\n            ...toolCom,\n            rightBtn\n        },\n\t\tdata () {\n\t\t    return {\n\t\t        configObj: {},\n\t\t\t\trCom: [\n\t\t\t\t    {\n\t\t\t\t        components: toolCom.c_set_up,\n\t\t\t\t        configNme: 'setUp'\n\t\t\t\t    }\n\t\t\t\t],\n\t\t\t\toneStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'decorateColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle2: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'decorateColor2'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'textColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'textColor2'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfiveStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'textColor3'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsetUp: 0,\n\t\t\t\ttype: 0,\n\t\t\t\ttype2: 0\n\t\t    }\n\t\t},\n        watch: {\n            num (nVal) {\n                // debugger;\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n\t\t\t'configObj.setUp.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.setUp = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\tlet tempArr = [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_button_style,\n\t\t\t\t\t\t\t\tconfigNme: 'styleConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t\t\t    configNme: 'stickyConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleTab'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t  components: toolCom.c_tab_list,\n\t\t\t\t\t\t\t  configNme: 'tabListConfig'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t\tthis.rCom = arr.concat(tempArr);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\tif(this.type2 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(this.type == 1){\n\t\t\t\t\t\t\tif(this.type2 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle2,...this.fourStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type2 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fiveStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.styleConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\tif(this.type2 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(nVal == 1){\n\t\t\t\t\t\t\tif(this.type2 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle2,...this.fourStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type2 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fiveStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.toneConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(this.type == 1){\n\t\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle2,...this.fourStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fiveStyle,...this.currencyStyle];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n            // 获取组件参数\n            getConfig (data) {\n\n            },\n        }\n    }\n", null]}