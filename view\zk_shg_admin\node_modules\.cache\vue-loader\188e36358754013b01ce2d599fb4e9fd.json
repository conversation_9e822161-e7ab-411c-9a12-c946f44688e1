{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751247618901}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport request from '@/plugins/request';\n\nexport default {\n  name: 'TransferManagement',\n  data() {\n    return {\n      // 筛选条件\n      filterForm: {\n        order_no: '',\n        from_store_id: '',\n        to_store_id: '',\n        status: '',\n        date_range: []\n      },\n\n      // 列表数据\n      transferList: [],\n      storeList: [],\n      selectedRows: [],\n\n      // 分页\n      currentPage: 1,\n      pageSize: 20,\n      total: 0,\n      tableLoading: false,\n\n      // 表格列定义\n      tableColumns: [\n        {\n          type: 'selection',\n          width: 60,\n          align: 'center'\n        },\n        {\n          title: '调拨单号',\n          key: 'order_no',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '商品信息',\n          key: 'product_name',\n          minWidth: 280,\n          render: (h, params) => {\n            return h('div', {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '12px'\n              }\n            }, [\n              h('img', {\n                attrs: {\n                  src: params.row.product_image || 'https://img.shg.zkshlm.com/attach/2025/03/156b3202503241658034024.jpg',\n                  alt: '商品图片'\n                },\n                style: {\n                  width: '40px',\n                  height: '40px',\n                  borderRadius: '4px',\n                  objectFit: 'cover',\n                  border: '1px solid #e8eaec'\n                }\n              }),\n              h('div', {\n                style: {\n                  flex: 1\n                }\n              }, [\n                h('div', {\n                  style: {\n                    fontWeight: 'bold',\n                    marginBottom: '4px',\n                    fontSize: '13px'\n                  }\n                }, params.row.product_name || '未知商品'),\n                params.row.sku && params.row.sku !== '默认规格' ? h('div', {\n                  style: {\n                    color: '#999',\n                    fontSize: '12px',\n                    marginBottom: '4px'\n                  }\n                }, `规格: ${params.row.sku}`) : null,\n                h('div', {\n                  style: {\n                    color: '#2d8cf0',\n                    fontSize: '12px'\n                  }\n                }, `调拨数量: ${params.row.transfer_qty || 0}`)\n              ])\n            ]);\n          }\n        },\n        {\n          title: '调拨门店',\n          key: 'stores',\n          width: 250,\n          align: 'center',\n          render: (h, params) => {\n            return h('div', {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '4px'\n              }\n            }, [\n              h('div', {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                }\n              }, [\n                h('span', {\n                  style: {\n                    fontSize: '12px',\n                    color: '#666'\n                  }\n                }, params.row.from_store_name || '未知门店'),\n                h('Icon', {\n                  props: {\n                    type: 'ios-arrow-forward'\n                  },\n                  style: {\n                    color: '#2d8cf0'\n                  }\n                }),\n                h('span', {\n                  style: {\n                    fontSize: '12px',\n                    color: '#666'\n                  }\n                }, params.row.to_store_name || '未知门店')\n              ]),\n              h('div', {\n                style: {\n                  fontSize: '11px',\n                  color: '#999',\n                  marginTop: '2px'\n                }\n              }, `库存: ${params.row.from_store_stock || 0} → ${params.row.to_store_stock || 0}`)\n            ]);\n          }\n        },\n        {\n          title: '库存变化',\n          key: 'stock_change',\n          width: 180,\n          align: 'center',\n          render: (h, params) => {\n            return h('div', {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '4px'\n              }\n            }, [\n              h('div', {\n                style: {\n                  fontSize: '12px',\n                  color: '#ed4014'\n                }\n              }, `调出: ${params.row.from_store_stock || 0} → ${params.row.from_store_stock_after || 0}`),\n              h('div', {\n                style: {\n                  fontSize: '12px',\n                  color: '#19be6b'\n                }\n              }, `调入: ${params.row.to_store_stock || 0} → ${params.row.to_store_stock_after || 0}`)\n            ]);\n          }\n        },\n        {\n          title: '单据状态',\n          key: 'status',\n          width: 100,\n          align: 'center',\n          render: (h, params) => {\n            const statusMap = {\n              0: { text: '待审核', color: 'orange' },\n              1: { text: '已审核', color: 'blue' },\n              2: { text: '已执行', color: 'green' },\n              3: { text: '已拒绝', color: 'red' },\n              4: { text: '已撤销', color: 'default' }\n            };\n            const status = statusMap[params.row.status] || { text: '未知', color: 'default' };\n            return h('Tag', {\n              props: {\n                color: status.color\n              }\n            }, status.text);\n          }\n        },\n        {\n          title: '申请人',\n          key: 'applicant',\n          width: 100,\n          align: 'center'\n        },\n        {\n          title: '申请时间',\n          key: 'apply_time_text',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '审核时间',\n          key: 'approve_time_text',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 280,\n          align: 'center',\n          fixed: 'right',\n          render: (h, params) => {\n            const buttons = [];\n\n            // 查看详情按钮 - 所有状态都有\n            buttons.push(h('Button', {\n              props: {\n                type: 'primary',\n                size: 'small'\n              },\n              style: {\n                marginRight: '8px'\n              },\n              on: {\n                click: () => {\n                  this.viewDetail(params.row);\n                }\n              }\n            }, '查看'));\n\n            // 根据状态显示不同的操作按钮\n            if (params.row.status === 0) {\n              // 待审核状态：显示通过和拒绝按钮\n              buttons.push(h('Button', {\n                props: {\n                  type: 'success',\n                  size: 'small'\n                },\n                style: {\n                  marginRight: '8px'\n                },\n                on: {\n                  click: () => {\n                    this.approveTransfer(params.row, true);\n                  }\n                }\n              }, '通过'));\n\n              buttons.push(h('Button', {\n                props: {\n                  type: 'error',\n                  size: 'small'\n                },\n                style: {\n                  marginRight: '8px'\n                },\n                on: {\n                  click: () => {\n                    this.approveTransfer(params.row, false);\n                  }\n                }\n              }, '拒绝'));\n            } else if (params.row.status === 1) {\n              // 已审核状态：显示执行调拨和强制执行按钮\n              buttons.push(h('Button', {\n                props: {\n                  type: 'warning',\n                  size: 'small'\n                },\n                style: {\n                  marginRight: '8px'\n                },\n                on: {\n                  click: () => {\n                    this.executeTransfer(params.row);\n                  }\n                }\n              }, '执行'));\n\n              buttons.push(h('Button', {\n                props: {\n                  type: 'info',\n                  size: 'small'\n                },\n                style: {\n                  marginRight: '8px'\n                },\n                on: {\n                  click: () => {\n                    this.forceExecute(params.row);\n                  }\n                }\n              }, '强制执行'));\n            } else if (params.row.status === 3 || params.row.status === 4) {\n              // 已拒绝或已撤销状态：显示删除按钮\n              buttons.push(h('Button', {\n                props: {\n                  type: 'error',\n                  size: 'small'\n                },\n                on: {\n                  click: () => {\n                    this.deleteTransfer(params.row);\n                  }\n                }\n              }, '删除'));\n            }\n\n            return h('div', {\n              style: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: '4px'\n              }\n            }, buttons);\n          }\n        }\n      ],\n\n      // 统计数据\n      statistics: {\n        total_orders: 0,\n        pending_orders: 0,\n        approved_orders: 0,\n        completed_orders: 0\n      },\n\n      // 详情对话框\n      detailDialogVisible: false,\n      currentDetail: null,\n\n      // 审核对话框\n      approveDialogVisible: false,\n      approveLoading: false,\n      approveType: true, // true: 通过, false: 拒绝\n      approveForm: {\n        id: '',\n        remark: ''\n      },\n      approveRules: {\n        remark: [\n          { required: false, message: '请填写审核备注', trigger: 'blur' }\n        ]\n      },\n\n      // 添加调拨对话框\n      addDialogVisible: false,\n      addLoading: false,\n      addForm: {\n        from_store_id: '',\n        to_store_id: '',\n        remark: '',\n        details: []\n      },\n      addRules: {\n        from_store_id: [\n          { required: true, message: '请选择调出门店', trigger: 'change' }\n        ],\n        to_store_id: [\n          { required: true, message: '请选择调入门店', trigger: 'change' }\n        ]\n      },\n\n      // 商品选择\n      productList: [],\n      productLoading: false,\n      selectedProduct: null\n    };\n  },\n\n  created() {\n    console.log('调拨管理页面初始化');\n    this.initData();\n  },\n\n  methods: {\n    // 初始化数据\n    async initData() {\n      console.log('开始初始化数据');\n      await this.getStoreList();\n      await this.getTransferList();\n      await this.getStatistics();\n      console.log('数据初始化完成');\n    },\n\n    // 获取门店列表\n    async getStoreList() {\n      try {\n        const res = await request.get('/erp/transfer/store_options');\n        console.log('门店列表API响应:', res);\n        if (res && res.status === 200 && res.data) {\n          this.storeList = res.data || [];\n          console.log('门店列表数据:', this.storeList);\n        } else {\n          console.error('门店列表API响应格式错误:', res);\n          this.storeList = [];\n        }\n      } catch (error) {\n        console.error('获取门店列表失败:', error);\n        this.storeList = [];\n        // 不显示错误消息，因为门店列表不是必需的\n      }\n    },\n\n    // 获取调拨单列表\n    async getTransferList() {\n      this.tableLoading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: this.pageSize,\n          order_no: this.filterForm.order_no,\n          from_store_id: this.filterForm.from_store_id,\n          to_store_id: this.filterForm.to_store_id,\n          status: this.filterForm.status\n        };\n\n        // 处理日期范围\n        if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {\n          params.start_time = this.formatDate(this.filterForm.date_range[0]);\n          params.end_time = this.formatDate(this.filterForm.date_range[1]);\n        }\n\n        const res = await request.get('/erp/transfer/list', { params });\n        console.log('调拨列表API响应:', res);\n\n        if (res && res.status === 200 && res.data) {\n          const data = res.data;\n          console.log('调拨列表数据:', data);\n\n          // 使用$set强制更新数据\n          this.$set(this, 'transferList', data.list || []);\n          this.$set(this, 'total', data.count || 0);\n\n          console.log('设置列表数据:', this.transferList, '总数:', this.total);\n          console.log('最终列表数据:', this.transferList);\n          console.log('最终总数:', this.total);\n\n          // 强制触发Vue更新\n          this.$nextTick(() => {\n            console.log('nextTick后的列表数据:', this.transferList.length);\n            console.log('nextTick后的总数:', this.total);\n            this.$forceUpdate();\n          });\n        } else {\n          this.$Message.error(res.data.msg || '获取调拨单列表失败');\n        }\n      } catch (error) {\n        console.error('获取调拨单列表失败:', error);\n        this.$Message.error('获取调拨单列表失败');\n      } finally {\n        this.tableLoading = false;\n      }\n    },\n\n    // 获取统计数据\n    async getStatistics() {\n      try {\n        const res = await request.get('/erp/transfer/statistics');\n        console.log('统计数据API响应:', res);\n        if (res && res.status === 200 && res.data) {\n          const stats = res.data;\n          console.log('统计数据:', stats);\n          console.log('stats.total:', stats.total, 'stats.pending:', stats.pending);\n\n          // 使用$set强制更新统计数据\n          this.$set(this.statistics, 'total_orders', stats.total || 0);\n          this.$set(this.statistics, 'pending_orders', stats.pending || 0);\n          this.$set(this.statistics, 'approved_orders', stats.approved || 0);\n          this.$set(this.statistics, 'completed_orders', stats.executed || 0);\n\n          console.log('设置后的统计数据:', this.statistics);\n\n          this.$nextTick(() => {\n            console.log('nextTick后的统计数据:', this.statistics);\n            this.$forceUpdate();\n          });\n        } else {\n          console.error('统计数据API响应格式错误:', res);\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n      }\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '';\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n\n    // 格式化时间戳\n    formatTimestamp(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp * 1000);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      });\n    },\n\n    // 重置筛选条件\n    resetFilter() {\n      this.filterForm = {\n        order_no: '',\n        from_store_id: '',\n        to_store_id: '',\n        status: '',\n        date_range: []\n      };\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n\n    // 分页处理\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      this.getTransferList();\n    },\n\n    // 选择处理\n    handleSelectionChange(selection) {\n      this.selectedRows = selection;\n    },\n\n    clearSelection() {\n      this.$refs.table && this.$refs.table.clearSelection();\n      this.selectedRows = [];\n    },\n\n    // 状态处理\n    getStatusTagType(status) {\n      const typeMap = {\n        0: 'warning', // 待审核\n        1: 'success', // 已通过\n        2: 'info', // 已完成\n        3: 'danger', // 已拒绝\n        4: 'info' // 已撤销\n      };\n      return typeMap[status] || 'info';\n    },\n\n    getStatusText(status) {\n      const textMap = {\n        0: '待审核',\n        1: '已通过',\n        2: '已完成',\n        3: '已拒绝',\n        4: '已撤销'\n      };\n      return textMap[status] || '未知';\n    },\n\n    // 查看详情\n    viewDetail(row) {\n      this.currentDetail = { ...row };\n      this.detailDialogVisible = true;\n    },\n\n    // 审核调拨\n    approveTransfer(row, isApprove) {\n      this.approveType = isApprove;\n      this.approveForm = {\n        id: row.id,\n        remark: ''\n      };\n      this.approveDialogVisible = true;\n    },\n\n    async submitApprove() {\n      try {\n        this.approveLoading = true;\n\n        const data = {\n          is_approve: this.approveType,\n          remark: this.approveForm.remark\n        };\n\n        const res = await this.$http.post(`/adminapi/erp/transfer/approve/${this.approveForm.id}`, data);\n\n        if (res.data.status === 200) {\n          this.$message.success(res.data.msg || (this.approveType ? '审核通过成功' : '审核拒绝成功'));\n          this.approveDialogVisible = false;\n          this.getTransferList();\n          this.getStatistics();\n        } else {\n          this.$message.error(res.data.msg || '审核失败');\n        }\n      } catch (error) {\n        console.error('审核失败:', error);\n        this.$message.error('审核失败');\n      } finally {\n        this.approveLoading = false;\n      }\n    },\n\n    // 执行调拨\n    async executeTransfer(row) {\n      this.$confirm('确定要执行这个调拨申请吗？', '确认执行', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async() => {\n        try {\n          const res = await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);\n          if (res.data.status === 200) {\n            this.$message.success('调拨执行成功');\n            this.getTransferList();\n            this.getStatistics();\n          } else {\n            this.$message.error(res.data.msg || '执行失败');\n          }\n        } catch (error) {\n          console.error('执行调拨失败:', error);\n          this.$message.error('执行调拨失败');\n        }\n      });\n    },\n\n    // 强制执行\n    async forceExecute(row) {\n      this.$confirm('强制执行将忽略库存不足等限制，确定要强制执行吗？', '确认强制执行', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'error'\n      }).then(async() => {\n        try {\n          const res = await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);\n          if (res.data.status === 200) {\n            this.$message.success('强制执行成功');\n            this.getTransferList();\n            this.getStatistics();\n          } else {\n            this.$message.error(res.data.msg || '强制执行失败');\n          }\n        } catch (error) {\n          console.error('强制执行失败:', error);\n          this.$message.error('强制执行失败');\n        }\n      });\n    },\n\n    // 删除调拨单\n    async deleteTransfer(row) {\n      this.$confirm('确定要删除这个调拨单吗？', '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async() => {\n        try {\n          const res = await this.$http.delete(`/adminapi/erp/transfer/delete/${row.id}`);\n          if (res.data.status === 200) {\n            this.$message.success('删除成功');\n            this.getTransferList();\n            this.getStatistics();\n          } else {\n            this.$message.error(res.data.msg || '删除失败');\n          }\n        } catch (error) {\n          console.error('删除失败:', error);\n          this.$message.error('删除失败');\n        }\n      });\n    },\n\n    // 批量操作\n    async batchApprove(isApprove) {\n      if (this.selectedRows.length === 0) {\n        this.$message.warning('请先选择要审核的调拨单');\n        return;\n      }\n\n      const action = isApprove ? '通过' : '拒绝';\n      this.$confirm(`确定要批量${action}选中的调拨单吗？`, `确认批量${action}`, {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async() => {\n        try {\n          const data = {\n            ids: this.selectedRows.map(row => row.id),\n            is_approve: isApprove,\n            remark: ''\n          };\n\n          const res = await this.$http.post('/adminapi/erp/transfer/batch_approve', data);\n          if (res.data.status === 200) {\n            this.$message.success(`批量${action}成功`);\n            this.getTransferList();\n            this.getStatistics();\n            this.clearSelection();\n          } else {\n            this.$message.error(res.data.msg || `批量${action}失败`);\n          }\n        } catch (error) {\n          console.error(`批量${action}失败:`, error);\n          this.$message.error(`批量${action}失败`);\n        }\n      });\n    },\n\n    async batchExecute() {\n      if (this.selectedRows.length === 0) {\n        this.$message.warning('请先选择要执行的调拨单');\n        return;\n      }\n\n      this.$confirm('确定要批量执行选中的调拨单吗？', '确认批量执行', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async() => {\n        try {\n          // 逐个执行调拨单\n          for (const row of this.selectedRows) {\n            await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);\n          }\n\n          this.$message.success('批量执行成功');\n          this.getTransferList();\n          this.getStatistics();\n          this.clearSelection();\n        } catch (error) {\n          console.error('批量执行失败:', error);\n          this.$message.error('批量执行失败');\n        }\n      });\n    },\n\n    // 其他功能\n    showPendingOnly() {\n      this.filterForm.status = 0;\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n\n    async exportTransfer() {\n      this.$Loading.start();\n      try {\n        const params = {\n          order_no: this.filterForm.order_no,\n          from_store_id: this.filterForm.from_store_id,\n          to_store_id: this.filterForm.to_store_id,\n          status: this.filterForm.status\n        };\n\n        // 处理日期范围\n        if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {\n          params.start_time = this.formatDate(this.filterForm.date_range[0]);\n          params.end_time = this.formatDate(this.filterForm.date_range[1]);\n        }\n\n        const res = await request.get('/erp/transfer/export', { params });\n        if (res.status === 200 && res.data) {\n          // 创建下载链接\n          const downloadUrl = window.location.origin + res.data.download_url;\n          const link = document.createElement('a');\n          link.href = downloadUrl;\n          link.download = res.data.filename;\n          link.style.display = 'none';\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n\n          this.$Message.success('导出成功');\n          this.$Loading.finish();\n        } else {\n          this.$Message.error(res.msg || '导出失败');\n          this.$Loading.error();\n        }\n      } catch (error) {\n        console.error('导出失败:', error);\n        this.$Message.error('导出失败：' + (error.message || '未知错误'));\n        this.$Loading.error();\n      }\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        0: '待审核',\n        1: '已审核',\n        2: '已执行',\n        3: '已拒绝',\n        4: '已撤销'\n      };\n      return statusMap[status] || '未知状态';\n    },\n\n    // 获取状态标签颜色\n    getStatusTagColor(status) {\n      const colorMap = {\n        0: 'orange', // 待审核\n        1: 'blue', // 已审核\n        2: 'green', // 已执行\n        3: 'red', // 已拒绝\n        4: 'default' // 已撤销\n      };\n      return colorMap[status] || 'default';\n    },\n\n    // 显示添加调拨对话框\n    showAddDialog() {\n      console.log('显示添加调拨对话框');\n      console.log('当前门店列表:', this.storeList);\n      this.addForm = {\n        from_store_id: '',\n        to_store_id: '',\n        remark: '',\n        details: []\n      };\n      this.selectedProduct = null;\n      this.addDialogVisible = true;\n    },\n\n    // 添加调拨商品\n    addTransferProduct() {\n      if (!this.selectedProduct) {\n        this.$Message.warning('请先选择商品');\n        return;\n      }\n\n      // 检查是否已经添加过该商品\n      const exists = this.addForm.details.find(item =>\n        item.product_id === this.selectedProduct.product_id &&\n        item.unique === this.selectedProduct.unique\n      );\n\n      if (exists) {\n        this.$Message.warning('该商品规格已经添加过了');\n        return;\n      }\n\n      this.addForm.details.push({\n        product_id: this.selectedProduct.product_id,\n        product_name: this.selectedProduct.store_name,\n        unique: this.selectedProduct.unique,\n        sku: this.selectedProduct.sku,\n        stock: this.selectedProduct.stock,\n        transfer_num: 1,\n        cost_price: this.selectedProduct.cost || 0\n      });\n\n      this.selectedProduct = null;\n    },\n\n    // 删除调拨商品\n    removeTransferProduct(index) {\n      this.addForm.details.splice(index, 1);\n    },\n\n    // 提交添加调拨\n    async submitAddTransfer() {\n      console.log('开始提交调拨申请');\n      console.log('表单数据:', this.addForm);\n\n      try {\n        await this.$refs.addForm.validate();\n        console.log('表单验证通过');\n\n        if (this.addForm.details.length === 0) {\n          this.$Message.warning('请至少添加一个调拨商品');\n          return;\n        }\n\n        if (this.addForm.from_store_id === this.addForm.to_store_id) {\n          this.$Message.warning('调出门店和调入门店不能相同');\n          return;\n        }\n\n        this.addLoading = true;\n        console.log('发送创建调拨请求:', {\n          from_store_id: this.addForm.from_store_id,\n          to_store_id: this.addForm.to_store_id,\n          remark: this.addForm.remark,\n          details: this.addForm.details\n        });\n\n        const res = await request.post('/erp/transfer/create', {\n          from_store_id: this.addForm.from_store_id,\n          to_store_id: this.addForm.to_store_id,\n          remark: this.addForm.remark,\n          details: this.addForm.details\n        });\n\n        console.log('创建调拨响应:', res);\n        if (res.status === 200) {\n          this.$Message.success('调拨申请提交成功');\n          this.addDialogVisible = false;\n          this.getTransferList();\n          this.getStatistics();\n        } else {\n          this.$Message.error(res.msg || '提交失败');\n        }\n      } catch (error) {\n        console.error('提交调拨申请失败:', error);\n        this.$Message.error('提交失败：' + (error.message || '未知错误'));\n      } finally {\n        this.addLoading = false;\n      }\n    },\n\n    // 取消添加调拨\n    cancelAddTransfer() {\n      this.addDialogVisible = false;\n      this.addForm = {\n        from_store_id: '',\n        to_store_id: '',\n        remark: '',\n        details: []\n      };\n      this.selectedProduct = null;\n    },\n\n    // 搜索商品\n    async searchProducts(query) {\n      console.log('搜索商品:', query);\n      if (!query || query.length < 2) {\n        this.productList = [];\n        return;\n      }\n\n      this.productLoading = true;\n      try {\n        console.log('发送商品搜索请求:', query);\n        const res = await request.get('/erp/transfer/search_products', {\n          params: { keyword: query }\n        });\n\n        console.log('商品搜索响应:', res);\n        if (res.status === 200) {\n          this.productList = res.data || [];\n          console.log('搜索到的商品:', this.productList);\n        } else {\n          this.productList = [];\n          console.log('商品搜索失败:', res);\n        }\n      } catch (error) {\n        console.error('搜索商品失败:', error);\n        this.productList = [];\n      } finally {\n        this.productLoading = false;\n      }\n    },\n\n    // 处理选择变化\n    handleSelectionChange(selection) {\n      this.selectedRows = selection;\n    },\n\n    // 清除选择\n    clearSelection() {\n      this.selectedRows = [];\n    },\n\n    // 分页相关\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      this.getTransferList();\n    },\n\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n\n    // 重置筛选条件\n    resetFilter() {\n      this.filterForm = {\n        order_no: '',\n        from_store_id: '',\n        to_store_id: '',\n        status: '',\n        date_range: []\n      };\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n\n    // 查看详情\n    viewDetail(row) {\n      this.currentDetail = row;\n      this.detailDialogVisible = true;\n    },\n\n    // 审核调拨单\n    approveTransfer(row, approve) {\n      const action = approve ? '通过' : '拒绝';\n      this.$Modal.confirm({\n        title: `确认${action}调拨单`,\n        content: `确定要${action}调拨单 ${row.order_no} 吗？`,\n        onOk: async() => {\n          try {\n            const res = await request.post(`/erp/transfer/approve/${row.id}`, {\n              approve: approve ? 1 : 0,\n              remark: approve ? '审核通过' : '审核拒绝'\n            });\n\n            if (res.status === 200) {\n              this.$Message.success(`${action}成功`);\n              this.getTransferList();\n              this.getStatistics();\n            } else {\n              this.$Message.error(res.msg || `${action}失败`);\n            }\n          } catch (error) {\n            this.$Message.error(`${action}失败：${error.message}`);\n          }\n        }\n      });\n    },\n\n    // 执行调拨\n    executeTransfer(row) {\n      this.$Modal.confirm({\n        title: '确认执行调拨',\n        content: `确定要执行调拨单 ${row.order_no} 吗？执行后将实际调拨库存。`,\n        onOk: async() => {\n          try {\n            const res = await request.post(`/erp/transfer/execute/${row.id}`);\n\n            if (res.status === 200) {\n              this.$Message.success('执行成功');\n              this.getTransferList();\n              this.getStatistics();\n            } else {\n              this.$Message.error(res.msg || '执行失败');\n            }\n          } catch (error) {\n            this.$Message.error(`执行失败：${error.message}`);\n          }\n        }\n      });\n    },\n\n    // 强制执行调拨\n    forceExecute(row) {\n      this.$Modal.confirm({\n        title: '确认强制执行调拨',\n        content: `确定要强制执行调拨单 ${row.order_no} 吗？强制执行将忽略库存不足等限制。`,\n        onOk: async() => {\n          try {\n            const res = await request.post(`/erp/transfer/force_execute/${row.id}`);\n\n            if (res.status === 200) {\n              this.$Message.success('强制执行成功');\n              this.getTransferList();\n              this.getStatistics();\n            } else {\n              this.$Message.error(res.msg || '强制执行失败');\n            }\n          } catch (error) {\n            this.$Message.error(`强制执行失败：${error.message}`);\n          }\n        }\n      });\n    },\n\n    // 删除调拨单\n    deleteTransfer(row) {\n      this.$Modal.confirm({\n        title: '确认删除调拨单',\n        content: `确定要删除调拨单 ${row.order_no} 吗？删除后无法恢复。`,\n        onOk: async() => {\n          try {\n            const res = await request.delete(`/erp/transfer/delete/${row.id}`);\n\n            if (res.status === 200) {\n              this.$Message.success('删除成功');\n              this.getTransferList();\n              this.getStatistics();\n            } else {\n              this.$Message.error(res.msg || '删除失败');\n            }\n          } catch (error) {\n            this.$Message.error(`删除失败：${error.message}`);\n          }\n        }\n      });\n    }\n  }\n};\n", null]}