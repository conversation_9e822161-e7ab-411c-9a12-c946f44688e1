{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751017753657}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport request from '@/plugins/request';\r\n\r\nexport default {\r\n  name: 'TransferManagement',\r\n  data() {\r\n    return {\r\n      // 筛选条件\r\n      filterForm: {\r\n        order_no: '',\r\n        from_store_id: '',\r\n        to_store_id: '',\r\n        status: '',\r\n        date_range: []\r\n      },\r\n\r\n      // 列表数据\r\n      transferList: [],\r\n      storeList: [],\r\n      selectedRows: [],\r\n\r\n      // 分页\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      total: 0,\r\n      tableLoading: false,\r\n\r\n      // 表格列定义\r\n      tableColumns: [\r\n        {\r\n          type: 'selection',\r\n          width: 60,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '调拨单号',\r\n          key: 'order_no',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '商品信息',\r\n          key: 'product_name',\r\n          minWidth: 250\r\n        },\r\n        {\r\n          title: '调拨门店',\r\n          key: 'stores',\r\n          width: 250,\r\n          align: 'center',\r\n          render: (h, params) => {\r\n            return h('div', {\r\n              style: {\r\n                display: 'flex',\r\n                flexDirection: 'column',\r\n                alignItems: 'center',\r\n                gap: '4px'\r\n              }\r\n            }, [\r\n              h('div', {\r\n                style: {\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: '8px'\r\n                }\r\n              }, [\r\n                h('span', {\r\n                  style: {\r\n                    fontSize: '12px',\r\n                    color: '#666'\r\n                  }\r\n                }, params.row.from_store_name || '未知门店'),\r\n                h('Icon', {\r\n                  props: {\r\n                    type: 'ios-arrow-forward'\r\n                  },\r\n                  style: {\r\n                    color: '#2d8cf0'\r\n                  }\r\n                }),\r\n                h('span', {\r\n                  style: {\r\n                    fontSize: '12px',\r\n                    color: '#666'\r\n                  }\r\n                }, params.row.to_store_name || '未知门店')\r\n              ]),\r\n              h('div', {\r\n                style: {\r\n                  fontSize: '11px',\r\n                  color: '#999',\r\n                  marginTop: '2px'\r\n                }\r\n              }, `库存: ${params.row.from_store_stock || 0} → ${params.row.to_store_stock || 0}`)\r\n            ]);\r\n          }\r\n        },\r\n        {\r\n          title: '库存变化',\r\n          key: 'stock_change',\r\n          width: 180,\r\n          align: 'center',\r\n          render: (h, params) => {\r\n            return h('div', {\r\n              style: {\r\n                display: 'flex',\r\n                flexDirection: 'column',\r\n                gap: '4px'\r\n              }\r\n            }, [\r\n              h('div', {\r\n                style: {\r\n                  fontSize: '12px',\r\n                  color: '#ed4014'\r\n                }\r\n              }, `调出: ${params.row.from_store_stock || 0} → ${params.row.from_store_stock_after || 0}`),\r\n              h('div', {\r\n                style: {\r\n                  fontSize: '12px',\r\n                  color: '#19be6b'\r\n                }\r\n              }, `调入: ${params.row.to_store_stock || 0} → ${params.row.to_store_stock_after || 0}`)\r\n            ]);\r\n          }\r\n        },\r\n        {\r\n          title: '单据状态',\r\n          key: 'status',\r\n          width: 100,\r\n          align: 'center',\r\n          render: (h, params) => {\r\n            const statusMap = {\r\n              0: { text: '待审核', color: 'orange' },\r\n              1: { text: '已审核', color: 'blue' },\r\n              2: { text: '已执行', color: 'green' },\r\n              3: { text: '已拒绝', color: 'red' },\r\n              4: { text: '已撤销', color: 'default' }\r\n            };\r\n            const status = statusMap[params.row.status] || { text: '未知', color: 'default' };\r\n            return h('Tag', {\r\n              props: {\r\n                color: status.color\r\n              }\r\n            }, status.text);\r\n          }\r\n        },\r\n        {\r\n          title: '申请人',\r\n          key: 'applicant',\r\n          width: 100,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '申请时间',\r\n          key: 'apply_time_text',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '审核时间',\r\n          key: 'approve_time_text',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 250,\r\n          align: 'center',\r\n          fixed: 'right'\r\n        }\r\n      ],\r\n\r\n      // 统计数据\r\n      statistics: {\r\n        total_orders: 0,\r\n        pending_orders: 0,\r\n        approved_orders: 0,\r\n        completed_orders: 0\r\n      },\r\n\r\n      // 详情对话框\r\n      detailDialogVisible: false,\r\n      currentDetail: null,\r\n\r\n      // 审核对话框\r\n      approveDialogVisible: false,\r\n      approveLoading: false,\r\n      approveType: true, // true: 通过, false: 拒绝\r\n      approveForm: {\r\n        id: '',\r\n        remark: ''\r\n      },\r\n      approveRules: {\r\n        remark: [\r\n          { required: false, message: '请填写审核备注', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n\r\n  created() {\r\n    this.initData();\r\n  },\r\n\r\n  methods: {\r\n    // 初始化数据\r\n    async initData() {\r\n      await this.getStoreList();\r\n      await this.getTransferList();\r\n      await this.getStatistics();\r\n    },\r\n\r\n    // 获取门店列表\r\n    async getStoreList() {\r\n      try {\r\n        const res = await request.get('/erp/transfer/store_options');\r\n        console.log('门店列表API响应:', res);\r\n        if (res && res.status === 200 && res.data) {\r\n          this.storeList = res.data || [];\r\n          console.log('门店列表数据:', this.storeList);\r\n        } else {\r\n          console.error('门店列表API响应格式错误:', res);\r\n          this.storeList = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('获取门店列表失败:', error);\r\n        this.storeList = [];\r\n        // 不显示错误消息，因为门店列表不是必需的\r\n      }\r\n    },\r\n\r\n    // 获取调拨单列表\r\n    async getTransferList() {\r\n      this.tableLoading = true;\r\n      try {\r\n        const params = {\r\n          page: this.currentPage,\r\n          limit: this.pageSize,\r\n          order_no: this.filterForm.order_no,\r\n          from_store_id: this.filterForm.from_store_id,\r\n          to_store_id: this.filterForm.to_store_id,\r\n          status: this.filterForm.status,\r\n        };\r\n\r\n        // 处理日期范围\r\n        if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {\r\n          params.start_time = this.formatDate(this.filterForm.date_range[0]);\r\n          params.end_time = this.formatDate(this.filterForm.date_range[1]);\r\n        }\r\n\r\n        const res = await request.get('/erp/transfer/list', { params });\r\n        console.log('调拨列表API响应:', res);\r\n\r\n        if (res && res.status === 200 && res.data) {\r\n          const data = res.data;\r\n          console.log('调拨列表数据:', data);\r\n\r\n          // 使用$set强制更新数据\r\n          this.$set(this, 'transferList', data.list || []);\r\n          this.$set(this, 'total', data.count || 0);\r\n\r\n          console.log('设置列表数据:', this.transferList, '总数:', this.total);\r\n          console.log('最终列表数据:', this.transferList);\r\n          console.log('最终总数:', this.total);\r\n\r\n          // 强制触发Vue更新\r\n          this.$nextTick(() => {\r\n            console.log('nextTick后的列表数据:', this.transferList.length);\r\n            console.log('nextTick后的总数:', this.total);\r\n            this.$forceUpdate();\r\n          });\r\n        } else {\r\n          this.$Message.error(res.data.msg || '获取调拨单列表失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('获取调拨单列表失败:', error);\r\n        this.$Message.error('获取调拨单列表失败');\r\n      } finally {\r\n        this.tableLoading = false;\r\n      }\r\n    },\r\n\r\n    // 获取统计数据\r\n    async getStatistics() {\r\n      try {\r\n        const res = await request.get('/erp/transfer/statistics');\r\n        console.log('统计数据API响应:', res);\r\n        if (res && res.status === 200 && res.data) {\r\n          const stats = res.data;\r\n          console.log('统计数据:', stats);\r\n          console.log('stats.total:', stats.total, 'stats.pending:', stats.pending);\r\n\r\n          // 使用$set强制更新统计数据\r\n          this.$set(this.statistics, 'total_orders', stats.total || 0);\r\n          this.$set(this.statistics, 'pending_orders', stats.pending || 0);\r\n          this.$set(this.statistics, 'approved_orders', stats.approved || 0);\r\n          this.$set(this.statistics, 'completed_orders', stats.executed || 0);\r\n\r\n          console.log('设置后的统计数据:', this.statistics);\r\n\r\n          this.$nextTick(() => {\r\n            console.log('nextTick后的统计数据:', this.statistics);\r\n            this.$forceUpdate();\r\n          });\r\n        } else {\r\n          console.error('统计数据API响应格式错误:', res);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取统计数据失败:', error);\r\n      }\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '';\r\n      const d = new Date(date);\r\n      const year = d.getFullYear();\r\n      const month = String(d.getMonth() + 1).padStart(2, '0');\r\n      const day = String(d.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n    // 格式化时间戳\r\n    formatTimestamp(timestamp) {\r\n      if (!timestamp) return '';\r\n      const date = new Date(timestamp * 1000);\r\n      return date.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      });\r\n    },\r\n\r\n    // 重置筛选条件\r\n    resetFilter() {\r\n      this.filterForm = {\r\n        order_no: '',\r\n        from_store_id: '',\r\n        to_store_id: '',\r\n        status: '',\r\n        date_range: []\r\n      };\r\n      this.currentPage = 1;\r\n      this.getTransferList();\r\n    },\r\n\r\n    // 分页处理\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.getTransferList();\r\n    },\r\n\r\n    handleCurrentChange(page) {\r\n      this.currentPage = page;\r\n      this.getTransferList();\r\n    },\r\n\r\n    // 选择处理\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    clearSelection() {\r\n      this.$refs.table && this.$refs.table.clearSelection();\r\n      this.selectedRows = [];\r\n    },\r\n\r\n    // 状态处理\r\n    getStatusTagType(status) {\r\n      const typeMap = {\r\n        0: 'warning', // 待审核\r\n        1: 'success', // 已通过\r\n        2: 'info', // 已完成\r\n        3: 'danger', // 已拒绝\r\n        4: 'info' // 已撤销\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    getStatusText(status) {\r\n      const textMap = {\r\n        0: '待审核',\r\n        1: '已通过',\r\n        2: '已完成',\r\n        3: '已拒绝',\r\n        4: '已撤销'\r\n      };\r\n      return textMap[status] || '未知';\r\n    },\r\n\r\n    // 查看详情\r\n    viewDetail(row) {\r\n      this.currentDetail = { ...row };\r\n      this.detailDialogVisible = true;\r\n    },\r\n\r\n    // 审核调拨\r\n    approveTransfer(row, isApprove) {\r\n      this.approveType = isApprove;\r\n      this.approveForm = {\r\n        id: row.id,\r\n        remark: ''\r\n      };\r\n      this.approveDialogVisible = true;\r\n    },\r\n\r\n    async submitApprove() {\r\n      try {\r\n        this.approveLoading = true;\r\n\r\n        const data = {\r\n          is_approve: this.approveType,\r\n          remark: this.approveForm.remark\r\n        };\r\n\r\n        const res = await this.$http.post(`/adminapi/erp/transfer/approve/${this.approveForm.id}`, data);\r\n\r\n        if (res.data.status === 200) {\r\n          this.$message.success(res.data.msg || (this.approveType ? '审核通过成功' : '审核拒绝成功'));\r\n          this.approveDialogVisible = false;\r\n          this.getTransferList();\r\n          this.getStatistics();\r\n        } else {\r\n          this.$message.error(res.data.msg || '审核失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('审核失败:', error);\r\n        this.$message.error('审核失败');\r\n      } finally {\r\n        this.approveLoading = false;\r\n      }\r\n    },\r\n\r\n    // 执行调拨\r\n    async executeTransfer(row) {\r\n      this.$confirm('确定要执行这个调拨申请吗？', '确认执行', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);\r\n          if (res.data.status === 200) {\r\n            this.$message.success('调拨执行成功');\r\n            this.getTransferList();\r\n            this.getStatistics();\r\n          } else {\r\n            this.$message.error(res.data.msg || '执行失败');\r\n          }\r\n        } catch (error) {\r\n          console.error('执行调拨失败:', error);\r\n          this.$message.error('执行调拨失败');\r\n        }\r\n      });\r\n    },\r\n\r\n    // 强制执行\r\n    async forceExecute(row) {\r\n      this.$confirm('强制执行将忽略库存不足等限制，确定要强制执行吗？', '确认强制执行', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'error'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);\r\n          if (res.data.status === 200) {\r\n            this.$message.success('强制执行成功');\r\n            this.getTransferList();\r\n            this.getStatistics();\r\n          } else {\r\n            this.$message.error(res.data.msg || '强制执行失败');\r\n          }\r\n        } catch (error) {\r\n          console.error('强制执行失败:', error);\r\n          this.$message.error('强制执行失败');\r\n        }\r\n      });\r\n    },\r\n\r\n    // 删除调拨单\r\n    async deleteTransfer(row) {\r\n      this.$confirm('确定要删除这个调拨单吗？', '确认删除', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await this.$http.delete(`/adminapi/erp/transfer/delete/${row.id}`);\r\n          if (res.data.status === 200) {\r\n            this.$message.success('删除成功');\r\n            this.getTransferList();\r\n            this.getStatistics();\r\n          } else {\r\n            this.$message.error(res.data.msg || '删除失败');\r\n          }\r\n        } catch (error) {\r\n          console.error('删除失败:', error);\r\n          this.$message.error('删除失败');\r\n        }\r\n      });\r\n    },\r\n\r\n    // 批量操作\r\n    async batchApprove(isApprove) {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请先选择要审核的调拨单');\r\n        return;\r\n      }\r\n\r\n      const action = isApprove ? '通过' : '拒绝';\r\n      this.$confirm(`确定要批量${action}选中的调拨单吗？`, `确认批量${action}`, {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const data = {\r\n            ids: this.selectedRows.map(row => row.id),\r\n            is_approve: isApprove,\r\n            remark: ''\r\n          };\r\n\r\n          const res = await this.$http.post('/adminapi/erp/transfer/batch_approve', data);\r\n          if (res.data.status === 200) {\r\n            this.$message.success(`批量${action}成功`);\r\n            this.getTransferList();\r\n            this.getStatistics();\r\n            this.clearSelection();\r\n          } else {\r\n            this.$message.error(res.data.msg || `批量${action}失败`);\r\n          }\r\n        } catch (error) {\r\n          console.error(`批量${action}失败:`, error);\r\n          this.$message.error(`批量${action}失败`);\r\n        }\r\n      });\r\n    },\r\n\r\n    async batchExecute() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请先选择要执行的调拨单');\r\n        return;\r\n      }\r\n\r\n      this.$confirm('确定要批量执行选中的调拨单吗？', '确认批量执行', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          // 逐个执行调拨单\r\n          for (const row of this.selectedRows) {\r\n            await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);\r\n          }\r\n\r\n          this.$message.success('批量执行成功');\r\n          this.getTransferList();\r\n          this.getStatistics();\r\n          this.clearSelection();\r\n        } catch (error) {\r\n          console.error('批量执行失败:', error);\r\n          this.$message.error('批量执行失败');\r\n        }\r\n      });\r\n    },\r\n\r\n    // 其他功能\r\n    showPendingOnly() {\r\n      this.filterForm.status = 0;\r\n      this.currentPage = 1;\r\n      this.getTransferList();\r\n    },\r\n\r\n    async exportTransfer() {\r\n      try {\r\n        const params = {\r\n          order_no: this.filterForm.order_no,\r\n          from_store_id: this.filterForm.from_store_id,\r\n          to_store_id: this.filterForm.to_store_id,\r\n          status: this.filterForm.status,\r\n        };\r\n\r\n        // 处理日期范围\r\n        if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {\r\n          params.start_time = this.formatDate(this.filterForm.date_range[0]);\r\n          params.end_time = this.formatDate(this.filterForm.date_range[1]);\r\n        }\r\n\r\n        const res = await this.$http.get('/adminapi/erp/transfer/export', { params });\r\n        if (res.data.status === 200) {\r\n          this.$message.success('导出成功');\r\n          // TODO: 处理文件下载\r\n        } else {\r\n          this.$message.error(res.data.msg || '导出失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('导出失败:', error);\r\n        this.$message.error('导出失败');\r\n      }\r\n    },\r\n\r\n    showStatistics() {\r\n      // 跳转到统计分析页面或显示统计弹窗\r\n      this.$router.push('/erp/report');\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待审核',\r\n        1: '已审核',\r\n        2: '已执行',\r\n        3: '已拒绝',\r\n        4: '已撤销'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取状态标签颜色\r\n    getStatusTagColor(status) {\r\n      const colorMap = {\r\n        0: 'orange',    // 待审核\r\n        1: 'blue',      // 已审核\r\n        2: 'green',     // 已执行\r\n        3: 'red',       // 已拒绝\r\n        4: 'default'    // 已撤销\r\n      };\r\n      return colorMap[status] || 'default';\r\n    },\r\n\r\n    // 显示添加调拨对话框\r\n    showAddDialog() {\r\n      this.$Message.info('添加调拨功能开发中...');\r\n    },\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 清除选择\r\n    clearSelection() {\r\n      this.selectedRows = [];\r\n    },\r\n\r\n    // 分页相关\r\n    handleCurrentChange(page) {\r\n      this.currentPage = page;\r\n      this.getTransferList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.getTransferList();\r\n    },\r\n\r\n    // 重置筛选条件\r\n    resetFilter() {\r\n      this.filterForm = {\r\n        order_no: '',\r\n        from_store_id: '',\r\n        to_store_id: '',\r\n        status: '',\r\n        date_range: []\r\n      };\r\n      this.currentPage = 1;\r\n      this.getTransferList();\r\n    },\r\n\r\n    // 查看详情\r\n    viewDetail(row) {\r\n      this.currentDetail = row;\r\n      this.detailDialogVisible = true;\r\n    }\r\n  }\r\n};\r\n", null]}