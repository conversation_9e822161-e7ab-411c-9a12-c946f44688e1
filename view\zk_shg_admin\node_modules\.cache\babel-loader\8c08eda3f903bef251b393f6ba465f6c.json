{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nexport default {\n  name: 'attribute',\n  props: {\n    attrTemplate: {\n      type: Boolean\n    }\n  },\n  data: function data() {\n    return {\n      val: false,\n      specsVal: '',\n      specs: [],\n      attrVal: '',\n      attrList: []\n    };\n  },\n  watch: {\n    attrTemplate: function attrTemplate(n) {\n      this.val = n;\n    }\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 70;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  methods: {\n    cancel: function cancel() {\n      this.$emit('changeTemplate', false);\n    },\n    confirm: function confirm() {\n      var _this = this;\n\n      if (this.specsVal === '') {\n        this.$Message.error('请填写规格名称');\n      } else {\n        this.specs.push(this.specsVal);\n        this.attrList.push({\n          attr: this.specsVal,\n          inputVal: '',\n          attrVal: []\n        });\n        this.specsVal = '';\n\n        if (this.specsVal !== '') {\n          this.attrList.forEach(function (item) {\n            if (item.attrVal.length < 1) {\n              _this.$Message.error('请填写规格属性');\n            }\n          });\n        }\n      }\n    },\n    confirmAttr: function confirmAttr(index) {\n      var attrList = this.attrList[index];\n\n      if (attrList.inputVal === '') {\n        this.$Message.error('请填写规格属性');\n      } else {\n        attrList.attrVal.push(attrList.inputVal);\n        attrList.inputVal === '';\n      }\n    }\n  },\n  mounted: function mounted() {}\n};", null]}