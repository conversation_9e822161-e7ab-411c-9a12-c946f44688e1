{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { crawlFromApi, treeListApi, crawlSaveApi, productGetTemplateApi, copyConfigApi } from \"@/api/product\";\nimport { isLoginApi } from \"@/api/setting\";\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport Setting from '@/setting';\nexport default {\n  name: \"taoBao\",\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      // 批量设置表格data\n      oneFormBatch: [{\n        pic: \"\",\n        price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        bar_code: \"\",\n        weight: 0,\n        volume: 0\n      }],\n      columnsBatch: [{\n        title: \"图片\",\n        slot: \"pic\",\n        align: \"center\",\n        minWidth: 80\n      }, {\n        title: \"售价\",\n        slot: \"price\",\n        align: \"center\",\n        minWidth: 95\n      }, {\n        title: \"成本价\",\n        slot: \"cost\",\n        align: \"center\",\n        minWidth: 95\n      }, {\n        title: \"原价\",\n        slot: \"ot_price\",\n        align: \"center\",\n        minWidth: 95\n      }, {\n        title: \"库存\",\n        slot: \"stock\",\n        align: \"center\",\n        minWidth: 95\n      }, {\n        title: \"商品编号\",\n        slot: \"bar_code\",\n        align: \"center\",\n        minWidth: 120\n      }, {\n        title: \"重量（KG）\",\n        slot: \"weight\",\n        align: \"center\",\n        minWidth: 95\n      }, {\n        title: \"体积(m³)\",\n        slot: \"volume\",\n        align: \"center\",\n        minWidth: 95\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        fixed: 'right',\n        align: \"center\",\n        minWidth: 140\n      }],\n      modal_loading: false,\n      images: \"\",\n      soure_link: \"\",\n      modalPic: false,\n      isChoice: \"\",\n      spinShow: false,\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      copyConfig: {\n        copy_type: 2,\n        copy_num: 0\n      },\n      columns: [],\n      treeSelect: [],\n      ruleInline: {\n        cate_id: [{\n          required: true,\n          message: \"请选择商品分类\",\n          trigger: \"change\",\n          type: \"array\",\n          min: \"1\"\n        }],\n        temp_id: [{\n          required: true,\n          message: \"请选择运费模板\",\n          trigger: \"change\",\n          type: \"number\"\n        }]\n      },\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      grid2: {\n        xl: 12,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      formValidate: {\n        store_name: \"\",\n        cate_id: [],\n        temp_id: \"\",\n        keyword: \"\",\n        unit_name: \"\",\n        store_info: \"\",\n        image: \"\",\n        slider_image: [],\n        description: \"\",\n        ficti: 0,\n        give_integral: 0,\n        is_show: 0,\n        price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        soure_link: \"\",\n        description_images: \"\",\n        postage: 0,\n        attrs: [],\n        items: []\n      },\n      items: [{\n        pic: \"\",\n        price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        bar_code: \"\",\n        weight: 0,\n        volume: 0\n      }],\n      templateList: [],\n      isData: false,\n      artFrom: {\n        type: \"taobao\",\n        url: \"\"\n      },\n      tableIndex: 0\n    };\n  },\n  components: {\n    WangEditor: WangEditor,\n    uploadPictures: uploadPictures\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.goodsCategory();\n  },\n  mounted: function mounted() {\n    this.productGetTemplate();\n    this.getCopyConfig();\n  },\n  methods: {\n    getEditorContent: function getEditorContent(data) {\n      this.formValidate.description = data;\n    },\n    mealPay: function mealPay(val) {\n      var _this = this;\n\n      isLoginApi().then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n\n                  if (!data.status) {\n                    _this.$Message.warning(\"请先登录\");\n\n                    _this.$router.push(_this.roterPre + \"/setting/sms/sms_config/index\");\n                  } else {\n                    _this.$router.push({\n                      path: _this.roterPre + \"/setting/sms/sms_pay/index\",\n                      query: {\n                        type: val\n                      }\n                    });\n                  }\n\n                case 2:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    getCopyConfig: function getCopyConfig() {\n      var _this2 = this;\n\n      copyConfigApi().then(function (res) {\n        _this2.copyConfig.copy_type = res.data.copy_type;\n        _this2.copyConfig.copy_num = res.data.copy_num;\n      });\n    },\n    batchDel: function batchDel() {\n      this.oneFormBatch = [{\n        pic: \"\",\n        price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        bar_code: \"\",\n        weight: 0,\n        volume: 0\n      }];\n    },\n    batchAdd: function batchAdd() {\n      var formBatch = this.oneFormBatch[0];\n      this.$set(this.formValidate, \"attrs\", this.formValidate.attrs.map(function (item) {\n        if (formBatch.pic) {\n          item.pic = formBatch.pic;\n        }\n\n        if (formBatch.price > 0) {\n          item.price = formBatch.price;\n        }\n\n        if (formBatch.cost > 0) {\n          item.cost = formBatch.cost;\n        }\n\n        if (formBatch.ot_price > 0) {\n          item.ot_price = formBatch.ot_price;\n        }\n\n        if (formBatch.stock > 0) {\n          item.stock = formBatch.stock;\n        }\n\n        if (formBatch.bar_code) {\n          item.bar_code = formBatch.bar_code;\n        }\n\n        if (formBatch.weight) {\n          item.weight = formBatch.weight;\n        }\n\n        if (formBatch.volume) {\n          item.volume = formBatch.volume;\n        }\n\n        return item;\n      }));\n    },\n    // 删除表格中的属性\n    delAttrTable: function delAttrTable(index) {\n      this.items.splice(index, 1);\n    },\n    // 获取运费模板；\n    productGetTemplate: function productGetTemplate() {\n      var _this3 = this;\n\n      productGetTemplateApi().then(function (res) {\n        _this3.templateList = res.data;\n      });\n    },\n    // 删除图片\n    handleRemove: function handleRemove(i) {\n      this.formValidate.slider_image.splice(i, 1);\n    },\n    // 选择主图\n    checked: function checked(item, index) {\n      this.formValidate.image = item;\n    },\n    // 商品分类；\n    goodsCategory: function goodsCategory() {\n      var _this4 = this;\n\n      treeListApi(1).then(function (res) {\n        _this4.treeSelect = res.data;\n      }).catch(function (res) {\n        _this4.$Message.error(res.msg);\n      });\n    },\n    // 生成表单\n    add: function add() {\n      var _this5 = this;\n\n      if (this.soure_link) {\n        var reg = /(http|ftp|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/;\n\n        if (!reg.test(this.soure_link)) {\n          return this.$Message.warning(\"请输入以http开头的地址！\");\n        }\n\n        this.spinShow = true;\n        this.artFrom.url = this.soure_link;\n        crawlFromApi(this.artFrom).then(function (res) {\n          var info = res.data.info;\n          _this5.columns = info.info.header;\n          _this5.formValidate = info;\n          _this5.formValidate.soure_link = _this5.soure_link;\n          _this5.formValidate.attrs = info.info.value;\n\n          if (_this5.formValidate.image) {\n            _this5.oneFormBatch[0].pic = _this5.formValidate.image;\n          }\n\n          _this5.items = _this5.formValidate.attrs;\n          _this5.isData = true;\n          _this5.spinShow = false;\n        }).catch(function (res) {\n          _this5.spinShow = false;\n\n          _this5.$Message.error(res.msg);\n        });\n      } else {\n        this.$Message.warning(\"请输入链接地址！\");\n      }\n    },\n    // 提交\n    handleSubmit: function handleSubmit(name) {\n      var _this6 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this6.modal_loading = true; // this.formValidate.attrs = [\n          //     {\n          //         pic: this.images,\n          //         price: this.formValidate.price,\n          //         cost: this.formValidate.cost,\n          //         ot_price: this.formValidate.ot_price,\n          //         stock: this.formValidate.stock,\n          //         bar_code: this.formValidate.bar_code,\n          //         weight: this.formValidate.weight,\n          //         volume: this.formValidate.volume\n          //     }\n          // ];\n          // this.formValidate.items = [];\n\n          crawlSaveApi(_this6.formValidate).then(function (res) {\n            _this6.$Message.success(\"商品默认为不上架状态请手动上架商品!\");\n\n            setTimeout(function () {\n              _this6.modal_loading = false;\n            }, 500);\n            setTimeout(function () {\n              _this6.$emit(\"on-close\");\n            }, 600);\n          }).catch(function (res) {\n            _this6.modal_loading = false;\n\n            _this6.$Message.error(res.msg);\n          });\n        } else {\n          if (!_this6.formValidate.cate_id) {\n            _this6.$Message.warning(\"请填写商品分类！\");\n          }\n        }\n      });\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, index) {\n      this.modalPic = true;\n      this.isChoice = tit === \"dan\" ? \"单选\" : \"多选\";\n      this.tableIndex = index;\n    },\n    // 获取单张图片信息\n    getPic: function getPic(pc) {\n      if (this.tableIndex === \"duopi\") {\n        this.oneFormBatch[0].pic = pc.att_dir;\n      } else {\n        this.formValidate.attrs[this.tableIndex].pic = pc.att_dir;\n      }\n\n      this.modalPic = false;\n    },\n    handleDragStart: function handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd: function handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    // 首先把div变成可以放置的元素，即重写dragenter/dragover\n    handleDragOver: function handleDragOver(e) {\n      // e.dataTransfer.dropEffect=\"move\";//在dragenter中针对放置目标来设置!\n      e.dataTransfer.dropEffect = \"move\";\n    },\n    handleDragEnter: function handleDragEnter(e, item) {\n      // 为需要移动的元素设置dragstart事件\n      e.dataTransfer.effectAllowed = \"move\";\n\n      if (item === this.dragging) {\n        return;\n      }\n\n      var newItems = _toConsumableArray(this.formValidate.slider_image);\n\n      var src = newItems.indexOf(this.dragging);\n      var dst = newItems.indexOf(item);\n      newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n      this.formValidate.slider_image = newItems;\n    },\n    // 添加自定义弹窗\n    addCustomDialog: function addCustomDialog(editorId) {\n      window.UE.registerUI(\"test-dialog\", function (editor, uiName) {\n        var dialog = new window.UE.ui.Dialog({\n          iframeUrl: \"/admin/widget.images/index.html?fodder=dialog\",\n          editor: editor,\n          name: uiName,\n          title: \"上传图片\",\n          cssRules: \"width:1200px;height:500px;padding:20px;\"\n        });\n        this.dialog = dialog;\n        var btn = new window.UE.ui.Button({\n          name: \"dialog-button\",\n          title: \"上传图片\",\n          cssRules: \"background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;\",\n          onclick: function onclick() {\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 37);\n    }\n  }\n};", null]}