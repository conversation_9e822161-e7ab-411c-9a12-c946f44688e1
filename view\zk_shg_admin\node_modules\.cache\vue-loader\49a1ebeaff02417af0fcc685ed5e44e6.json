{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_search.vue?vue&type=template&id=87af8756&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_search.vue", "mtime": 1689129842000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 此组件目前没用，留着方便以后开发再用 -->\n<div class=\"acea-row row-top\" style=\"margin-bottom: 20px\" v-if=\"configData\">\n\t<CheckboxGroup v-model=\"configData.type\" @on-change=\"checkboxChange($event)\">\n\t\t<div>\n\t\t\t<Checkbox :label=\"1\">\n\t\t\t    <span>商品分类</span>\n\t\t\t</Checkbox>\n\t\t\t<Cascader\n\t\t\t\t :data=\"configData.list\"\n\t\t\t\t placeholder=\"请选择商品分类\"\n\t\t\t\t change-on-select\n\t\t\t\t v-model=\"configData.activeValue\"\n\t\t\t\t filterable\n\t\t\t\t @on-change=\"sliderChange\"\n\t\t\t></Cascader>\n\t\t</div>\n\t\t<div>\n\t\t\t<Checkbox :label=\"2\">\n\t\t\t    <span>Twitter2</span>\n\t\t\t</Checkbox>\n\t\t</div>\n\t</CheckboxGroup>\n</div>\n", null]}