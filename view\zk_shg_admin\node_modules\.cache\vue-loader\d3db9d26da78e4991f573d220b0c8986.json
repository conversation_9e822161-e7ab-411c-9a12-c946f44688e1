{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue?vue&type=template&id=ed720464&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<Modal v-model=\"modals\"  scrollable title=\"库存管理\" width=\"800\" \n @on-cancel=\"cancel\" footer-hide\n class-name=\"vertical-center-modal\">\n    <Card :bordered=\"false\" dis-hover class=\"cards\">\n\t\t\t\t<div class=\"batch\" v-if=\"specType\">\n\t\t\t\t\t<div class=\"name\" @click=\"batchTap\">批量<span class=\"iconfont iconxiayi\"></span></div>\n\t\t\t\t\t<div class=\"input acea-row row-center-wrapper\" v-if=\"batchShow\">\n\t\t\t\t\t\t  <Input v-model=\"batchStock\" type='number' class=\"width15\" @on-change=\"inputTap\">\n\t\t\t\t\t\t\t<Select v-model=\"batchPm\" slot=\"append\" style=\"width: 60px\" @on-change=\"batchStockTap\">\n\t\t\t\t\t\t\t   <Option :value=\"1\">入库</Option>\n\t\t\t\t\t\t\t   <Option :value=\"0\">出库</Option>\n\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t</Input>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n        <Table :columns=\"columns\" border ref=\"selection\" :data=\"stockData\" :loading=\"loading\" no-data-text=\"暂无数据\"\n               highlight-row no-filtered-data-text=\"暂无筛选结果\" max-height=\"450\">\n\t\t\t\t\t\t\t <template slot-scope=\"{ row, index }\" slot=\"image\">\n\t\t\t\t\t\t\t \t<div class=\"product-data\">\n\t\t\t\t\t\t\t \t\t<img class=\"image\" :src=\"row.image\" />\n\t\t\t\t\t\t\t \t</div>\n\t\t\t\t\t\t\t </template>\n\t\t\t\t\t\t\t <template slot-scope=\"{ row, index }\" slot=\"num\">\n\t\t\t\t\t\t\t\t <div class=\"acea-row row-middle\">\n\t\t\t\t\t\t\t\t <Input v-model=\"row.changeNum\" type=\"number\" class=\"width15\" @on-change=\"changeTap(row)\">\n\t\t\t\t\t\t\t\t\t \t<Select v-model=\"row.pm\" slot=\"append\" style=\"width: 60px\" @on-change=\"stockTap(row)\">\n\t\t\t\t\t\t\t\t\t \t   <Option :value=\"1\">入库</Option>\n\t\t\t\t\t\t\t\t\t \t   <Option :value=\"0\">出库</Option>\n\t\t\t\t\t\t\t\t\t \t</Select>\n\t\t\t\t\t\t\t\t\t </Input>\n\t\t\t\t\t\t\t\t\t <span class=\"ml20\">={{row.resultNum}}</span>\n\t\t\t\t\t\t\t\t </div>\n\t\t\t\t\t\t\t </template>\n\t\t\t\t</Table>\n\t\t\t\t<div class=\"footer acea-row row-right\">\n\t\t\t\t\t<Button class=\"mr\" @click=\"cancel\">取消</Button>\n\t\t\t\t\t<Button type=\"primary\" @click=\"productSaveStocks\">提交</Button>\n\t\t\t\t</div>\n    </Card>\n</Modal>\n", null]}