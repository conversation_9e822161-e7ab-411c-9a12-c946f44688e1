{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\searchFrom\\searchFrom.vue?vue&type=template&id=0539c218&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\searchFrom\\searchFrom.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"table_box\">\n    <Form ref=\"DataList\" :model=\"DataList\" :rules=\"rules\" :label-width=\"labelWidth\" :label-position=\"labelPosition\" class=\"tabform\">\n        <Row :gutter=\"24\" type=\"flex\" justify=\"end\">\n            <Col span=\"24\" class=\"ivu-text-left\">\n                <FormItem label=\"订单状态：\">\n                    <RadioGroup v-model=\"DataList.status\" type=\"button\"  @on-change=\"selectChange(DataList.status)\">\n                        <Radio :label=item.label v-for=\"(item,i) in typeName\" :key=\"i\">{{item.name+'('+item.num+')'}}</Radio>\n                    </RadioGroup>\n                </FormItem>\n            </Col>\n            <Col span=\"24\" class=\"ivu-text-left\">\n                <Col  v-bind=\"grid\">\n                    <FormItem label=\"创建时间：\">\n                        <RadioGroup v-model=\"DataList.data\" type=\"button\"  @on-change=\"timeChange(DataList.data)\">\n                            <Radio label=\"today\">今天</Radio>\n                            <Radio label=\"yesterday\">昨天</Radio>\n                            <Radio label=\"lately7\">最近7天</Radio>\n                            <Radio label=\"lately30\">最近30天</Radio>\n                        </RadioGroup>\n                    </FormItem>\n                </Col>\n                <Col  v-bind=\"grid\" >\n                    <FormItem class=\"tab_data\">\n                        <DatePicker :editable=\"false\" :value=\"value2\" format=\"yyyy/MM/dd\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"Select date\" class=\"width20\"></DatePicker>\n                    </FormItem>\n                </Col>\n            </Col>\n            <Col span=\"24\" class=\"ivu-text-left\" v-if=\"$route.path==='/echarts/trade/order'\">\n                <FormItem label=\"订单类型：\">\n                    <RadioGroup v-model=\"currentTab\" type=\"button\"  @on-change=\"onClickTab(currentTab)\">\n                        <Radio label=\"\">全部</Radio>\n                        <Radio label=\"1\">普通</Radio>\n                        <Radio label=\"2\">拼团</Radio>\n                        <Radio label=\"3\">砍价</Radio>\n                        <Radio label=\"4\">秒杀</Radio>\n                    </RadioGroup>\n                </FormItem>\n            </Col>\n        </Row>\n    </Form>\n\n</div>\n", null]}