{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue?vue&type=style&index=0&id=1d6e0732&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue", "mtime": 1750985197206}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1725352506768}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.report-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n  .header-left {\r\n    .page-title {\r\n      margin: 0 0 8px 0;\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #303133;\r\n    }\r\n\r\n    .page-desc {\r\n      margin: 0;\r\n      color: #909399;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .header-right {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.overview-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -10px -10px 10px -10px;\r\n  margin-bottom: 20px;\r\n\r\n  .overview-card {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 24px;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    width: calc(25% - 20px);\r\n    margin: 10px;\r\n\r\n    .card-icon {\r\n      width: 60px;\r\n      height: 60px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 16px;\r\n      font-size: 24px;\r\n      color: white;\r\n\r\n      &.inventory { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n      &.transfer { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n      &.warning { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n      &.efficiency { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n    }\r\n\r\n    .card-content {\r\n      .card-title {\r\n        color: #909399;\r\n        font-size: 14px;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .card-number {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        color: #303133;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .card-change {\r\n        font-size: 12px;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        &.positive { color: #67c23a; }\r\n        &.negative { color: #f56c6c; }\r\n\r\n        i { margin-right: 2px; }\r\n      }\r\n\r\n      .card-desc {\r\n        color: #909399;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.charts-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -10px -10px 10px -10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chart-card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  width: calc(50% - 20px);\r\n  margin: 10px;\r\n\r\n  .chart-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20px 20px 0 20px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      color: #303133;\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .chart-content {\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.report-tables {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -10px -10px 10px -10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  width: calc(50% - 20px);\r\n  margin: 10px;\r\n\r\n  .table-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20px 20px 0 20px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      color: #303133;\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .el-table {\r\n    margin: 20px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.money {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n.warning-text { color: #e6a23c; }\r\n.danger-text { color: #f56c6c; }\r\n.success-text { color: #67c23a; }\r\n\r\n.progress-text {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-left: 8px;\r\n}\r\n\r\n.analysis-section {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n\r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      color: #303133;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n}\r\n\r\n.analysis-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -10px;\r\n}\r\n\r\n.analysis-card {\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  width: calc(50% - 20px);\r\n  margin: 10px;\r\n\r\n  .card-header {\r\n    margin-bottom: 16px;\r\n\r\n    h4 {\r\n      margin: 0;\r\n      color: #303133;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.product-list {\r\n  .product-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 0;\r\n    border-bottom: 1px solid #ebeef5;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .product-rank {\r\n      width: 30px;\r\n      height: 30px;\r\n      border-radius: 50%;\r\n      background: #409eff;\r\n      color: white;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 12px;\r\n      font-weight: 600;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    .product-info {\r\n      flex: 1;\r\n\r\n      .product-name {\r\n        color: #303133;\r\n        font-weight: 500;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .product-stats {\r\n        color: #909399;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n\r\n    .product-trend {\r\n      .trend-up { color: #67c23a; }\r\n      .trend-down { color: #f56c6c; }\r\n    }\r\n  }\r\n}\r\n\r\n.warning-stats {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -8px;\r\n\r\n  .warning-item {\r\n    text-align: center;\r\n    padding: 16px;\r\n    background: white;\r\n    border-radius: 8px;\r\n    width: calc(50% - 16px);\r\n    margin: 8px;\r\n\r\n    .warning-label {\r\n      color: #909399;\r\n      font-size: 12px;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .warning-value {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n\r\n      &.danger { color: #f56c6c; }\r\n      &.warning { color: #e6a23c; }\r\n      &.success { color: #67c23a; }\r\n      &.info { color: #409eff; }\r\n    }\r\n  }\r\n}\r\n", null]}