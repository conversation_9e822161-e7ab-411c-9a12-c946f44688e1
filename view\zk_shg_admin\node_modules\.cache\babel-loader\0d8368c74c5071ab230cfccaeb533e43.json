{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\addClientGroup.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\addClientGroup.vue", "mtime": 1710488036000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from \"vuex\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport department from \"@/components/department/index.vue\";\nimport userLabel from \"@/components/labelList\";\nimport { workLabel, workGroupTemplateSave, workClientCount } from \"@/api/work\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport { getNewFormBuildRuleApi } from \"@/api/setting\";\nimport Setting from \"@/setting\";\nexport default {\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      formItem: {\n        template_type: \"0\",\n        //0=立即发送，1=定时发送\n        name: \"\",\n        type: \"0\",\n        //客户群发\n        client_type: \"0\",\n        //0=全部客户，1=所选客户\n        where_time: \"\",\n        //客户筛选时间\n        where_label: [],\n        //标签\n        userids: [],\n        //所选群发账号\n        send_time: \"\",\n        //定时发送账号\n        welcome_words: {\n          text: {\n            content: \"\"\n          },\n          attachments: []\n        } //欢迎语\n\n      },\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '名称不能为空',\n          trigger: 'blur'\n        }],\n        client_type: [{\n          required: true,\n          message: '请选择客户类型',\n          trigger: 'change'\n        }],\n        template_type: [{\n          required: true,\n          message: '请选择发送类型',\n          trigger: 'change'\n        }]\n      },\n      options: timeOptions,\n      timeVal: [],\n      //客户标签列表\n      labelList: [],\n      newLabelList: [],\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      rontineObj: {\n        msgtype: \"miniprogram\",\n        miniprogram: {\n          pic_url: \"\",\n          pic_media_id: \"\",\n          title: \"\",\n          appid: \"\",\n          page: \"\"\n        }\n      },\n      imageObj: {\n        msgtype: \"image\",\n        image: {\n          media_id: \"\",\n          pic_url: \"\"\n        }\n      },\n      picTit: \"\",\n      modalPic: false,\n      modalRoutine: false,\n      isChoice: \"单选\",\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: [],\n      clientCount: 0,\n      labelShow: false,\n      dataLabel: [],\n      notDataLabel: []\n    };\n  },\n  components: {\n    uploadPictures: uploadPictures,\n    department: department,\n    userLabel: userLabel\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    }\n  }),\n  watch: {\n    \"formItem.where_label\": function formItemWhere_label(val, oldVal) {\n      if (val !== oldVal) {\n        this.getClientCount();\n      }\n    },\n    \"formItem.where_not_label\": function formItemWhere_not_label(val, oldVal) {\n      if (val !== oldVal) {\n        this.getClientCount();\n      }\n    },\n    \"formItem.where_time\": function formItemWhere_time(val, oldVal) {\n      if (val !== oldVal) {\n        this.getClientCount();\n      }\n    },\n    \"formItem.userids\": function formItemUserids(val, oldVal) {\n      if (val !== oldVal) {\n        this.getClientCount();\n      }\n    },\n    dataLabel: function dataLabel(val) {\n      this.formItem.where_label = val.map(function (item) {\n        return item.tag_id;\n      });\n    },\n    notDataLabel: function notDataLabel(val) {\n      this.formItem.where_not_label = val.map(function (item) {\n        return item.tag_id;\n      });\n    }\n  },\n  mounted: function mounted() {\n    this.setCopyrightShow({\n      value: false\n    }); // this.getWorkLabel();\n\n    this.getClientCount();\n  },\n  destroyed: function destroyed() {\n    this.setCopyrightShow({\n      value: true\n    });\n  },\n  methods: _objectSpread({}, mapMutations(\"admin/layout\", [\"setCopyrightShow\"]), {\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formItem.where_time = this.timeVal.join(\"-\");\n    },\n    snedChangeTime: function snedChangeTime(val) {\n      this.formItem.send_time = val;\n    },\n    modalPicTap: function modalPicTap(picTit) {\n      this.modalPic = true;\n      this.picTit = picTit;\n    },\n    //获取客户标签\n    getWorkLabel: function getWorkLabel() {\n      var _this = this;\n\n      workLabel().then(function (res) {\n        _this.labelList = res.data.map(function (org) {\n          return _this.mapTree(org);\n        });\n        _this.newLabelList = _this.deepClone(_this.labelList);\n      });\n    },\n    mapTree: function mapTree(org) {\n      var _this2 = this;\n\n      var haveChildren = Array.isArray(org.children) && org.children.length > 0;\n      return {\n        //分别将我们查询出来的值做出改变他的key\n        title: org.label,\n        expand: true,\n        value: org.value,\n        selected: false,\n        checked: false,\n        children: haveChildren ? org.children.map(function (i) {\n          return _this2.mapTree(i);\n        }) : []\n      };\n    },\n    addRoutine: function addRoutine() {\n      var _this3 = this;\n\n      getNewFormBuildRuleApi('routine').then(function (res) {\n        var data = res.data;\n        _this3.rontineObj.miniprogram.pic_url = '';\n        _this3.rontineObj.miniprogram.title = data.routine_name.value;\n        _this3.rontineObj.miniprogram.appid = data.routine_appId.value;\n        _this3.rontineObj.miniprogram.page = '/pages/index/index';\n      });\n      this.modalRoutine = true;\n    },\n    addUser: function addUser() {\n      this.userList = this.formItem.userids;\n      this.$refs.department.memberStatus = true;\n    },\n    //tag标签删除成员\n    handleDel: function handleDel(id) {\n      var index = this.formItem.userids.findIndex(function (val) {\n        return val.id === id;\n      });\n      this.formItem.userids.splice(index, 1);\n      this.getClientCount();\n    },\n    //欢迎语tag删除\n    wordsDel: function wordsDel(name) {\n      var index = this.formItem.welcome_words.attachments.indexOf(name);\n      this.formItem.welcome_words.attachments.splice(index, 1);\n    },\n    changeMastart: function changeMastart(arr, type) {\n      this.formItem.userids = arr.map(function (item) {\n        return {\n          userid: item.userid,\n          name: item.name,\n          id: item.id\n        };\n      });\n    },\n    // 选中图片\n    getPic: function getPic(pc) {\n      switch (this.picTit) {\n        case \"image\":\n          this.imageObj.image.pic_url = pc.att_dir;\n          this.formItem.welcome_words.attachments.push(this.imageObj);\n          break;\n\n        case \"routine\":\n          this.rontineObj.miniprogram.pic_url = pc.att_dir;\n          break;\n      }\n\n      this.modalPic = false;\n    },\n    insertName: function insertName() {\n      this.formItem.welcome_words.text.content = this.formItem.welcome_words.text.content.concat(\"##客户名称##\");\n    },\n    routineConfirm: function routineConfirm() {\n      var routine = this.deepClone(this.rontineObj);\n      this.formItem.welcome_words.attachments.push(routine);\n    },\n    submit: function submit() {\n      var _this4 = this;\n\n      if (!this.formItem.userids.length) return this.$Message.error(\"请选择成员\");\n      var formData = this.deepClone(this.formItem);\n      formData.userids = formData.userids.map(function (item) {\n        return item.userid;\n      });\n\n      if (formData.client_type === '0') {\n        formData.where_time = '';\n        formData.where_label = [];\n        formData.where_not_label = [];\n      }\n\n      this.$refs.formItem.validate(function (valid) {\n        if (valid) {\n          workGroupTemplateSave(formData).then(function (res) {\n            _this4.$Message.success(res.msg);\n\n            _this4.$router.push(_this4.roterPre + \"/work/client/group\");\n          }).catch(function (err) {\n            _this4.$Message.error(err.msg);\n          });\n        }\n      });\n    },\n    //深克隆\n    deepClone: function deepClone(obj) {\n      var newObj = Array.isArray(obj) ? [] : {};\n\n      if (obj && _typeof(obj) === \"object\") {\n        for (var key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            newObj[key] = obj && _typeof(obj[key]) === \"object\" ? this.deepClone(obj[key]) : obj[key];\n          }\n        }\n      }\n\n      return newObj;\n    },\n    getClientCount: function getClientCount() {\n      var _this5 = this;\n\n      workClientCount({\n        is_all: this.formItem.client_type == 1 ? 0 : 1,\n        label: this.formItem.where_label,\n        notLabel: this.formItem.where_not_label,\n        time: this.formItem.where_time,\n        userid: this.formItem.userids.map(function (item) {\n          return item.userid;\n        })\n      }).then(function (res) {\n        _this5.clientCount = res.data.sum_count;\n      });\n    },\n    openLabel: function openLabel(label) {\n      this.labelActive = label;\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this[label])));\n    },\n    activeData: function activeData(dataLabel) {\n      this.labelShow = false;\n      this[this.labelActive] = dataLabel;\n    },\n    // 标签弹窗关闭\n    labelClose: function labelClose() {\n      this.labelShow = false;\n    },\n    closeLabel: function closeLabel(label, name) {\n      var index = this[name].indexOf(this[name].filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this[name].splice(index, 1);\n    }\n  })\n};", null]}