{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nimport { templatesCityListApi } from '@/api/setting';\nexport default {\n    name: 'city',\n    props: {\n        type: {\n            type: Number,\n            default: 0\n        },\n        selectArr:{\n            type:Array,\n            default: []\n        }\n    },\n    data () {\n        return {\n            iSselect: false,\n            addressModal: false,\n            cityList: [],\n            activeCity: -1,\n            loading: false\n        }\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 120;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'right';\n        }\n    },\n    methods: {\n        enter (index) {\n            this.activeCity = index;\n        },\n        leave () {\n            this.activeCity = null;\n        },\n        getCityList () {\n            this.loading = true;\n            templatesCityListApi().then(res => {\n                this.loading = false;\n                res.data.forEach((el,index,arr)=>{\n                    el.isShow = true\n                    el.children.forEach((child,j)=>{\n                        child.isShow = true\n                        if(this.selectArr.length>0){\n                            this.selectArr.forEach((sel,sindex)=>{\n                                sel.children.forEach((sitem,sj)=>{\n                                    if(child.city_id==sitem.city_id){\n                                        child.isShow = false\n                                    }\n                                })\n                            })\n                        }\n                    })\n                })\n                res.data.forEach((el,index,arr)=>{\n                    let num = 0\n                    let oldNum = 0\n                    el.children.forEach((child,j)=>{\n                       if(!child.isShow){\n                           num++\n                       }else{\n                           oldNum++\n                       }\n                    })\n                    if(num == el.children.length){\n                        el.isShow = false\n                    }\n                    el.childNum = oldNum\n                })\n                this.cityList = res.data;\n            })\n        },\n        /**\n         * 全选或者反选\n         * @param checked\n         */\n        allCheckbox: function () {\n            let that = this, checked = this.iSselect;\n            that.cityList.forEach(function (item, key) {\n                that.$set(that.cityList[key], 'checked', checked);\n                if (checked) {\n                    that.$set(that.cityList[key], 'count', that.cityList[key].children.length);\n                } else {\n                    that.$set(that.cityList[key], 'count', 0);\n                }\n                that.cityList[key].children.forEach(function (val, k) {\n                    that.$set(that.cityList[key].children[k], 'checked', checked);\n                })\n            });\n            // this.render();\n        },\n        // 清空；\n        empty () {\n            let that = this;\n            that.cityList.forEach(function (item, key) {\n                that.$set(that.cityList[key], 'checked', false);\n                that.cityList[key].children.forEach(function (val, k) {\n                    that.$set(that.cityList[key].children[k], 'checked', false);\n                });\n                that.$set(that.cityList[key], 'count', 0);\n            });\n            this.iSselect = false;\n        },\n        /**\n         * 点击省\n         * @param index\n         */\n        checkedClick: function (index) {\n            let that = this;\n            if (that.cityList[index].checked) {\n                that.$set(that.cityList[index], 'count', that.cityList[index].childNum);\n                that.cityList[index].children.forEach(function (item, key) {\n                    that.$set(that.cityList[index].children[key], 'checked', true);\n                });\n            } else {\n                that.$set(that.cityList[index], 'count', 0);\n                that.$set(that.cityList[index], 'checked', false);\n                that.cityList[index].children.forEach(function (item, key) {\n                    that.$set(that.cityList[index].children[key], 'checked', false);\n                });\n                that.iSselect = false;\n            }\n            // this.render();\n        },\n        /**\n         * 点击市区\n         * @param index\n         * @param ind\n         */\n        primary: function (index, ind) {\n            let checked = false, count = 0;\n            this.cityList[index].children.forEach(function (item, key) {\n                if (item.checked) {\n                    checked = true;\n                    count++;\n                }\n            });\n            this.$set(this.cityList[index], 'count', count);\n            this.$set(this.cityList[index], 'checked', checked);\n            // this.render();\n        },\n        // 确定;\n        confirm () {\n            let that = this;\n            // 被选中的省市；\n            let selectList = [];\n            that.cityList.forEach(function (item, key) {\n                let data = {};\n                if (item.checked) {\n                    data = {\n                        name: item.name,\n                        city_id: item.city_id,\n                        children: []\n                    };\n                }\n                that.cityList[key].children.forEach(function (i, k) {\n                    if (i.checked) {\n                        data.children.push({\n                            city_id: i.city_id\n                        })\n                    }\n                });\n                if (data.city_id !== undefined) {\n                    selectList.push(data);\n                }\n            });\n            if (selectList.length === 0) {\n                return that.$Message.error('至少选择一个省份或者城市');\n            } else {\n                this.$emit('selectCity', selectList, this.type);\n                that.addressModal = false;\n                this.cityList = []\n            }\n        },\n        close () {\n            this.addressModal = false;\n            this.cityList = []\n        }\n    },\n    mounted () {\n        // this.getCityList();\n    }\n}\n", null]}