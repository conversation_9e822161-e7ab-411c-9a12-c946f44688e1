{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsList\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { cascaderListApi, changeListApi, allLabelApi } from \"@/api/product\";\nimport { liveGoods } from \"@/api/live\";\nexport default {\n  name: \"index\",\n  props: {\n    goodsType: {\n      type: Number,\n      default: 0\n    },\n    storeType: {\n      type: Number,\n      default: 0\n    },\n    is_new: {\n      type: String,\n      default: \"\"\n    },\n    diy: {\n      type: Boolean,\n      default: false\n    },\n    isdiy: {\n      type: Boolean,\n      default: false\n    },\n    ischeckbox: {\n      type: Boolean,\n      default: false\n    },\n    liveStatus: {\n      type: Boolean,\n      default: false\n    },\n    isLive: {\n      type: Boolean,\n      default: false\n    },\n    datas: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      //选中商品集合\n      selectEquips: [],\n      // 选中的id集合\n      selectEquipsIds: [],\n      labelSelect: [],\n      cateIds: [],\n      modal_loading: false,\n      treeSelect: [],\n      formValidate: {\n        page: 1,\n        limit: 10,\n        cate_id: \"\",\n        store_name: \"\",\n        is_new: this.is_new,\n        store_label_id: \"\",\n        is_integral: 1\n      },\n      total: 0,\n      modals: false,\n      loading: false,\n      grid: {\n        xl: 10,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      tableList: [],\n      currentid: 0,\n      productRow: {},\n      columns4: [{\n        title: \"商品ID\",\n        key: \"id\"\n      }, {\n        title: \"图片\",\n        slot: \"image\",\n        width: 60\n      }, {\n        title: \"商品名称\",\n        slot: \"store_name\",\n        minWidth: 200\n      }, {\n        title: \"商品类型\",\n        slot: \"product_type\",\n        minWidth: 100\n      }, {\n        title: \"商品分类\",\n        key: \"cate_name\",\n        minWidth: 150\n      }],\n      columns5: [{\n        title: \"商品ID\",\n        key: \"id\"\n      }, {\n        title: \"图片\",\n        slot: \"image\"\n      }, {\n        title: \"商品名称\",\n        key: \"name\",\n        minWidth: 250\n      }],\n      images: [],\n      many: \"\"\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    var _this = this;\n\n    var radio = {\n      title: \"选择\",\n      width: 70,\n      align: \"center\",\n      render: function render(h, params) {\n        var id = params.row.id;\n        var flag = false;\n\n        if (_this.currentid === id) {\n          flag = true;\n        } else {\n          flag = false;\n        }\n\n        var self = _this;\n        return h(\"div\", [h(\"Radio\", {\n          props: {\n            value: flag\n          },\n          on: {\n            \"on-change\": function onChange() {\n              self.currentid = id;\n              _this.productRow = params.row;\n\n              _this.$emit(\"getProductId\", _this.productRow);\n\n              if (_this.productRow.id) {\n                if (_this.$route.query.fodder === \"image\") {\n                  /* eslint-disable */\n                  var imageObject = {\n                    image: _this.productRow.image,\n                    product_id: _this.productRow.id,\n                    name: _this.productRow.name\n                  };\n                  form_create_helper.set(\"image\", imageObject);\n                  form_create_helper.close(\"image\");\n                }\n              } else {\n                _this.$Message.warning(\"请先选择商品\");\n              }\n            }\n          }\n        })]);\n      }\n    };\n    var checkbox = {\n      type: \"selection\",\n      width: 60,\n      align: \"center\"\n    };\n    var many = \"\";\n\n    if (this.ischeckbox) {\n      many = \"many\";\n    } else {\n      many = this.$route.query.type;\n    }\n\n    this.many = many;\n\n    if (many === \"many\") {\n      this.columns4.unshift(checkbox);\n      this.columns5.unshift(checkbox);\n    } else {\n      this.columns4.unshift(radio);\n      this.columns5.unshift(radio);\n    }\n  },\n  mounted: function mounted() {\n    this.goodsCategory();\n    this.getList();\n    this.getAllLabelApi();\n  },\n  methods: {\n    // 判断是否选中\n    sortData: function sortData() {\n      var _this2 = this;\n\n      if (this.selectEquipsIds.length) {\n        this.tableList.forEach(function (ele) {\n          if (_this2.selectEquipsIds.includes(ele.id)) ele._checked = true;\n        });\n      }\n    },\n    // 选中一行\n    TableSelectRow: function TableSelectRow(selection, row) {\n      if (!this.selectEquipsIds.includes(row.id)) {\n        this.selectEquipsIds.push(row.id);\n        this.selectEquips.push(row);\n      }\n    },\n    // 取消选中一行\n    TableSelectCancelRow: function TableSelectCancelRow(selection, row) {\n      var _index = this.selectEquipsIds.indexOf(row.id);\n\n      if (_index != -1) {\n        this.selectEquipsIds.splice(_index, 1);\n        this.selectEquips.splice(_index, 1);\n      }\n    },\n    // 选中所有\n    selectAll: function selectAll() {\n      for (var i = this.tableList.length - 1; i >= 0; i--) {\n        this.TableSelectRow(null, this.tableList[i]);\n      }\n    },\n    // 取消选中所有\n    cancelAll: function cancelAll() {\n      for (var i = this.tableList.length - 1; i >= 0; i--) {\n        this.TableSelectCancelRow(null, this.tableList[i]);\n      }\n    },\n    getAllLabelApi: function getAllLabelApi() {\n      var _this3 = this;\n\n      allLabelApi().then(function (res) {\n        _this3.labelSelect = res.data;\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    handleSelectAll: function handleSelectAll() {\n      this.$refs.table.selectAll(false);\n    },\n    // 商品分类；\n    goodsCategory: function goodsCategory() {\n      var _this4 = this;\n\n      cascaderListApi(1).then(function (res) {\n        _this4.treeSelect = res.data;\n      }).catch(function (res) {\n        _this4.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this5 = this;\n\n      this.loading = true;\n\n      if (!this.liveStatus) {\n        if (this.isLive) {\n          this.formValidate.is_live = 1;\n        }\n\n        if (this.goodsType) {\n          this.formValidate.is_presale_product = 0;\n          this.formValidate.is_vip_product = 0;\n        }\n\n        if (this.storeType) {\n          this.formValidate.is_supplier = 0;\n        }\n\n        this.formValidate.cate_id = this.cateIds[this.cateIds.length - 1];\n        changeListApi(this.formValidate).then(\n        /*#__PURE__*/\n        function () {\n          var _ref = _asyncToGenerator(\n          /*#__PURE__*/\n          _regeneratorRuntime.mark(function _callee(res) {\n            var data;\n            return _regeneratorRuntime.wrap(function _callee$(_context) {\n              while (1) {\n                switch (_context.prev = _context.next) {\n                  case 0:\n                    data = res.data;\n                    _this5.tableList = data.list;\n                    _this5.total = res.data.count;\n\n                    _this5.sortData();\n\n                    _this5.loading = false;\n\n                  case 5:\n                  case \"end\":\n                    return _context.stop();\n                }\n              }\n            }, _callee);\n          }));\n\n          return function (_x) {\n            return _ref.apply(this, arguments);\n          };\n        }()).catch(function (res) {\n          _this5.loading = false;\n\n          _this5.$Message.error(res.msg);\n        });\n      } else {\n        liveGoods({\n          is_show: \"1\",\n          status: \"1\",\n          live_id: this.datas.id,\n          kerword: this.formValidate.store_name,\n          page: this.formValidate.page,\n          limit: this.formValidate.limit\n        }).then(\n        /*#__PURE__*/\n        function () {\n          var _ref2 = _asyncToGenerator(\n          /*#__PURE__*/\n          _regeneratorRuntime.mark(function _callee2(res) {\n            var data;\n            return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n              while (1) {\n                switch (_context2.prev = _context2.next) {\n                  case 0:\n                    data = res.data;\n                    data.list.forEach(function (el) {\n                      el.image = el.cover_img;\n                    });\n                    _this5.tableList = data.list;\n                    _this5.total = res.data.count;\n\n                    _this5.sortData();\n\n                    _this5.loading = false;\n\n                  case 6:\n                  case \"end\":\n                    return _context2.stop();\n                }\n              }\n            }, _callee2);\n          }));\n\n          return function (_x2) {\n            return _ref2.apply(this, arguments);\n          };\n        }()).catch(function (res) {\n          _this5.loading = false;\n\n          _this5.$Message.error(res.msg);\n        });\n      }\n    },\n    changeCheckbox: function changeCheckbox(selection) {\n      var images = [];\n      selection.forEach(function (item) {\n        var imageObject = {\n          image: item.image,\n          product_id: item.id,\n          store_name: item.store_name,\n          temp_id: item.temp_id\n        };\n        images.push(imageObject);\n      });\n      this.images = images;\n      this.$emit(\"getProductDiy\", selection);\n    },\n    ok: function ok() {\n      var images = [];\n      this.selectEquips.forEach(function (item) {\n        var imageObject = {\n          image: item.image,\n          product_id: item.id,\n          store_name: item.store_name,\n          temp_id: item.temp_id\n        };\n        images.push(imageObject);\n      });\n\n      if (images.length > 0) {\n        if (this.$route.query.fodder === \"image\") {\n          var imageValue = form_create_helper.get(\"image\");\n          form_create_helper.set(\"image\", imageValue.concat(images));\n          form_create_helper.close(\"image\");\n        } else {\n          if (this.isdiy) {\n            this.$emit(\"getProductId\", this.selectEquips);\n          } else {\n            this.$emit(\"getProductId\", images);\n          }\n        }\n      } else {\n        this.$Message.warning(\"请先选择商品\");\n      }\n    },\n    treeSearchs: function treeSearchs(value) {\n      this.cateIds = value;\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    clear: function clear() {\n      this.productRow.id = \"\";\n      this.currentid = \"\";\n    }\n  }\n};", null]}