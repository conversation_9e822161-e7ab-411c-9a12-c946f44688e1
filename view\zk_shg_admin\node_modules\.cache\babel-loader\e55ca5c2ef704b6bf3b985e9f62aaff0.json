{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addDelivery\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addDelivery\\index.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from \"vuex\";\nimport { savePromotions, discountInfo } from \"@/api/marketing\";\nimport { userLabelAddApi } from \"@/api/user\";\nimport { brandList } from \"@/api/product\";\nimport storeList from \"@/components/storeList\";\nimport storeLabelList from \"@/components/storeLabelList\";\nimport userLabel from \"@/components/labelList\";\nimport goodsList from '@/components/goodsList';\nimport goodsAttr from '@/components/goodsAttr';\nimport couponList from \"@/components/couponList\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"addPiecesDiscount\",\n  components: {\n    userLabel: userLabel,\n    goodsList: goodsList,\n    goodsAttr: goodsAttr,\n    couponList: couponList,\n    storeLabelList: storeLabelList,\n    storeList: storeList\n  },\n  data: function data() {\n    var _this = this;\n\n    return {\n      storesList: [],\n      storeModals: false,\n      roterPre: Setting.roterPre,\n      storeDataLabel: [],\n      storeLabelShow: false,\n      props: {\n        emitPath: false,\n        multiple: true\n      },\n      brandData: [],\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      sattrModals: false,\n      modals: false,\n      tableData: [],\n      headTab: [{\n        name: \"基础设置\",\n        type: '1'\n      }, {\n        name: \"添加商品\",\n        type: '2'\n      }, {\n        name: \"适用门店\",\n        type: '3'\n      }],\n      discountType: [{\n        name: \"满N元\",\n        title: \"例：满100元送小样\",\n        id: 1\n      }, {\n        name: \"满N件\",\n        title: \"例：满3件送小样\",\n        id: 2\n      }],\n      activityType: [{\n        name: '优惠券',\n        type: '5'\n      }, {\n        name: '满减满折',\n        type: '3'\n      }, {\n        name: '第N件N折',\n        type: '2'\n      }, {\n        name: '限时折扣',\n        type: '1'\n      }],\n      currentTab: '1',\n      dataLabel: [],\n      labelShow: false,\n      formValidate: {\n        applicable_type: 1,\n        name: '',\n        section_time: [],\n        is_label: 0,\n        label_id: [],\n        is_overlay: '0',\n        overlay: [],\n        product_partake_type: 1,\n        product_id: [],\n        threshold_type: 1,\n        promotions_cate: '1',\n        promotions: [],\n        brand_id: [],\n        store_label_id: []\n      },\n      promotionsData: [{\n        threshold: 0,\n        give_integral: 0,\n        checkIntegral: false,\n        checkCoupon: false,\n        checkGoods: false,\n        giveProducts: [],\n        giveCoupon: []\n      }],\n      indexCoupon: 0,\n      indexGoods: 0,\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '请输入活动名称',\n          trigger: 'blur'\n        }],\n        section_time: [{\n          required: true,\n          type: 'array',\n          message: '请选择活动时间',\n          trigger: 'change'\n        }],\n        promotions_cate: [{\n          required: true,\n          message: '请设置满送方式',\n          trigger: 'change'\n        }],\n        is_overlay: [{\n          required: true,\n          message: '请设置优惠叠加',\n          trigger: 'change'\n        }]\n      },\n      columns: [{\n        title: '优惠券名称',\n        key: 'title',\n        minWidth: 150\n      }, {\n        title: '类型',\n        slot: 'coupon_type',\n        minWidth: 80\n      }, {\n        title: '面值',\n        slot: 'coupon_price',\n        minWidth: 100\n      }, {\n        title: '最低消费额',\n        key: 'use_min_price',\n        minWidth: 100\n      }, {\n        title: '限量',\n        key: 'limit_num',\n        width: 120,\n        render: function render(h, params) {\n          return h(\"div\", [h(\"InputNumber\", {\n            props: {\n              value: params.row.limit_num,\n              placeholder: '请输入',\n              precision: 0,\n              min: 0\n            },\n            on: {\n              \"on-change\": function onChange(e) {\n                params.row.limit_num = e;\n                _this.promotionsData[params.row.indexCoupon].giveCoupon[params.index].limit_num = e;\n              }\n            }\n          })]);\n        }\n      }, {\n        title: '操作',\n        slot: 'status',\n        align: 'center',\n        minWidth: 80\n      }],\n      colGoods: [{\n        title: '商品信息',\n        slot: 'info',\n        minWidth: 200\n      }, {\n        title: '售价',\n        key: 'price',\n        minWidth: 80\n      }, {\n        title: '库存',\n        key: 'stock',\n        minWidth: 80\n      }, {\n        title: '单次赠送',\n        key: 'onceNum',\n        minWidth: 80\n      }, {\n        title: '总数量',\n        key: 'limit_num',\n        width: 120,\n        render: function render(h, params) {\n          return h(\"div\", [h(\"InputNumber\", {\n            props: {\n              value: params.row.limit_num,\n              placeholder: '请输入',\n              precision: 0,\n              min: 0\n            },\n            on: {\n              \"on-change\": function onChange(e) {\n                params.row.limit_num = e;\n                _this.promotionsData[params.row.indexGoods].giveProducts[params.index].limit_num = e;\n              }\n            }\n          })]);\n        }\n      }, {\n        title: '操作',\n        slot: 'status',\n        align: 'center',\n        minWidth: 80\n      }]\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\", \"menuCollapse\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n    labelBottom: function labelBottom() {\n      return this.isMobile ? undefined : 15;\n    }\n  }),\n  created: function created() {},\n  mounted: function mounted() {\n    this.setCopyrightShow({\n      value: false\n    });\n\n    if (this.$route.params.id != 0) {\n      this.getDiscountInfo();\n    }\n  },\n  destroyed: function destroyed() {\n    this.setCopyrightShow({\n      value: true\n    });\n  },\n  methods: _objectSpread({}, mapMutations('admin/layout', ['setCopyrightShow']), {\n    //删除门店\n    delte: function delte(row) {\n      var _this2 = this;\n\n      this.storesList.forEach(function (item, index) {\n        if (row.id == item.id) {\n          _this2.storesList.splice(index, 1);\n        }\n      });\n    },\n    //添加门店\n    addStore: function addStore() {\n      this.storeModals = true;\n    },\n    //关闭门店弹窗\n    cancelStore: function cancelStore() {\n      this.storeModals = false;\n    },\n    getStoreId: function getStoreId(data) {\n      this.storeModals = false;\n      var list = this.storesList.concat(data);\n      var uni = this.unique(list);\n      this.storesList = uni;\n    },\n    closeStoreLabel: function closeStoreLabel(label) {\n      var index = this.storeDataLabel.indexOf(this.storeDataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.storeDataLabel.splice(index, 1);\n    },\n    openStoreLabel: function openStoreLabel() {\n      this.storeLabelShow = true;\n      this.$refs.storeLabel.storeLabel(JSON.parse(JSON.stringify(this.storeDataLabel)));\n    },\n    activeStoreData: function activeStoreData(storeDataLabel) {\n      this.storeLabelShow = false;\n      this.storeDataLabel = storeDataLabel;\n    },\n    // 标签弹窗关闭\n    storeLabelClose: function storeLabelClose() {\n      this.storeLabelShow = false;\n    },\n    goodTap: function goodTap(e) {\n      if (e == 4) {\n        this.getBrandList();\n      }\n    },\n    // 品牌列表\n    getBrandList: function getBrandList() {\n      var _this3 = this;\n\n      brandList().then(function (res) {\n        _this3.brandData = res.data;\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    changeFull: function changeFull() {\n      this.promotionsData = [];\n      this.addLevel();\n    },\n    addLevel: function addLevel() {\n      var obj = {\n        threshold: 0,\n        give_integral: 0,\n        checkIntegral: false,\n        checkCoupon: false,\n        checkGoods: false,\n        giveProducts: [],\n        giveCoupon: []\n      };\n      this.promotionsData.push(obj);\n    },\n    delCoupon: function delCoupon(index, indexw) {\n      this.promotionsData[indexw].giveCoupon.splice(index, 1);\n    },\n    delGoods: function delGoods(index, indexw) {\n      this.promotionsData[indexw].giveProducts.splice(index, 1);\n    },\n    getCouponList: function getCouponList(data) {\n      var indexCoupon = this.indexCoupon;\n      this.$refs.couponTemplates.isTemplate = false;\n      data.forEach(function (j) {\n        j.limit_num = 0;\n        j.indexCoupon = indexCoupon;\n      });\n      var list = this.promotionsData[indexCoupon].giveCoupon.concat(data);\n      var uni = this.unique(list);\n      this.promotionsData[indexCoupon].giveCoupon = uni;\n    },\n    // 添加优惠券\n    addCoupon: function addCoupon(index) {\n      this.indexCoupon = index;\n      this.$refs.couponTemplates.isTemplate = true;\n      this.$refs.couponTemplates.tableList();\n    },\n    discountTypeTap: function discountTypeTap(item) {\n      this.formValidate.threshold_type = item.id;\n\n      if (item.id == 2) {\n        this.promotionsData.forEach(function (item) {\n          item.threshold = 0;\n        });\n      }\n    },\n    getDiscountInfo: function getDiscountInfo() {\n      var _this4 = this;\n\n      discountInfo(this.$route.params.id).then(function (res) {\n        _this4.formValidate = res.data.info;\n        _this4.storesList = res.data.info.stores || [];\n        _this4.formValidate.promotions_cate = _this4.formValidate.promotions_cate.toString();\n        _this4.formValidate.is_overlay = _this4.formValidate.is_overlay.toString();\n        _this4.tableData = res.data.info.products;\n        _this4.dataLabel = res.data.info.label_id;\n        _this4.storeDataLabel = res.data.info.store_label_id;\n\n        if (res.data.info.product_partake_type == 4) {\n          _this4.getBrandList();\n        }\n\n        var promotionsData = res.data.info.promotions;\n        promotionsData.forEach(function (item, index) {\n          item.checkCoupon = item.giveCoupon.length ? true : false;\n          item.checkGoods = item.giveProducts.length ? true : false;\n          item.checkIntegral = parseFloat(item.give_integral) > 0 ? true : false;\n          item.giveCoupon.forEach(function (j) {\n            j.indexCoupon = index;\n          });\n          item.giveProducts.forEach(function (i) {\n            i.indexGoods = index;\n            i.onceNum = 1;\n          });\n        });\n        _this4.promotionsData = promotionsData;\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    },\n    del: function del(row) {\n      var _this5 = this;\n\n      this.tableData.forEach(function (i, index) {\n        if (row.id == i.id) {\n          return _this5.tableData.splice(index, 1);\n        } else {\n          i.attrValue.forEach(function (j, indexn) {\n            if (row.id == j.id) {\n              if (i.attrValue.length == 1) {\n                return _this5.tableData.splice(index, 1);\n              } else {\n                return i.attrValue.splice(indexn, 1);\n              }\n            }\n          });\n        }\n      });\n    },\n    addLabel: function addLabel() {\n      this.$modalForm(userLabelAddApi(0)).then(function () {});\n    },\n    addGoods: function addGoods(index) {\n      this.modals = true;\n    },\n    addGoodsSattr: function addGoodsSattr(index) {\n      this.sattrModals = true;\n      this.indexGoods = index;\n    },\n    cancel: function cancel() {\n      this.modals = false;\n    },\n    //对象数组去重；\n    unique: function unique(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.id) && res.set(arr.id, 1);\n      });\n    },\n    getProductId: function getProductId(data) {\n      this.modals = false;\n      var list = this.tableData.concat(data);\n      var uni = this.unique(list);\n      uni.forEach(function (i) {\n        i.attrValue.forEach(function (j) {\n          j.cate_name = i.cate_name;\n          j.store_label = i.store_label;\n        });\n      });\n      this.tableData = uni;\n    },\n    getAtterId: function getAtterId(data) {\n      this.sattrModals = false;\n      var indexGoods = this.indexGoods;\n      data.forEach(function (i) {\n        i.limit_num = 0;\n        i.indexGoods = indexGoods;\n        i.onceNum = 1;\n      });\n      var list = this.promotionsData[indexGoods].giveProducts.concat(data);\n      var uni = this.unique(list);\n      this.promotionsData[indexGoods].giveProducts = uni;\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.formValidate.section_time = e;\n    },\n    closeLabel: function closeLabel(label) {\n      var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.dataLabel.splice(index, 1);\n    },\n    activeData: function activeData(dataLabel) {\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    openLabel: function openLabel(row) {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    // 标签弹窗关闭\n    labelClose: function labelClose() {\n      this.labelShow = false;\n    },\n    delGrade: function delGrade(index) {\n      this.promotionsData.splice(index, 1);\n    },\n    // 上一页：\n    upTab: function upTab() {\n      if (this.currentTab == 1 && this.formValidate.product_type != 0) {\n        this.currentTab = (Number(this.currentTab) - 2).toString();\n      } else {\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    // 下一页；\n    downTab: function downTab(name) {\n      var _this6 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this6.formValidate.is_label && !_this6.dataLabel.length) {\n            return _this6.$Message.warning(\"请选择用户关联标签\");\n          }\n\n          if (parseInt(_this6.formValidate.is_overlay) && !_this6.formValidate.overlay.length) {\n            return _this6.$Message.warning(\"请选择叠加的营销活动\");\n          }\n\n          if (_this6.formValidate.section_time[0] == '') {\n            return _this6.$Message.warning(\"请选择活动时间\");\n          }\n\n          _this6.currentTab = (Number(_this6.currentTab) + 1).toString();\n        } else {\n          _this6.$Message.warning(\"请完善数据\");\n        }\n      });\n    },\n    handleSubmit: function handleSubmit(name) {\n      var _this7 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this7.formValidate.is_label && !_this7.dataLabel.length) {\n            return _this7.$Message.warning(\"请选择用户关联标签\");\n          }\n\n          if (parseInt(_this7.formValidate.is_overlay) && !_this7.formValidate.overlay.length) {\n            return _this7.$Message.warning(\"请选择叠加的营销活动\");\n          }\n\n          if (_this7.formValidate.section_time[0] == '') {\n            return _this7.$Message.warning(\"请选择活动时间\");\n          }\n\n          if (_this7.formValidate.product_partake_type == 4 && !_this7.formValidate.brand_id.length) {\n            return _this7.$Message.error('请添加指定品牌');\n          }\n\n          if (_this7.formValidate.product_partake_type == 5) {\n            var labelIds = [];\n\n            _this7.storeDataLabel.forEach(function (item) {\n              labelIds.push(item.id);\n            });\n\n            if (!labelIds.length) {\n              return _this7.$Message.error('请添加指定标签');\n            }\n\n            _this7.formValidate.store_label_id = labelIds;\n          }\n\n          var promotions = []; //优惠内容\n\n          var flag = true;\n\n          _this7.promotionsData.forEach(function (item) {\n            if (item.threshold == null || item.threshold == 0) {\n              _this7.flag = false;\n              return _this7.$Message.warning(\"请填写优惠门槛\");\n            }\n\n            if (item.checkIntegral && (item.give_integral == null || item.give_integral == 0)) {\n              _this7.flag = false;\n              return _this7.$Message.warning(\"请填写赠送积分\");\n            }\n\n            if (item.checkCoupon && !item.giveCoupon.length) {\n              _this7.flag = false;\n              return _this7.$Message.warning(\"请添加优惠券\");\n            }\n\n            if (item.checkGoods && !item.giveProducts.length) {\n              _this7.flag = false;\n              return _this7.$Message.warning(\"请添加赠品\");\n            }\n\n            _this7.flag = true;\n            var obj = {\n              threshold: item.threshold,\n              give_integral: 0,\n              give_coupon_id: [],\n              give_product_id: []\n            };\n\n            if (item.checkIntegral) {\n              obj.give_integral = item.give_integral;\n            }\n\n            if (item.checkCoupon) {\n              item.giveCoupon.forEach(function (j) {\n                var objVal = {\n                  give_coupon_num: j.limit_num,\n                  give_coupon_id: j.id\n                };\n                obj.give_coupon_id.push(objVal);\n              });\n            }\n\n            if (item.checkGoods) {\n              item.giveProducts.forEach(function (v) {\n                var goodsVal = {\n                  give_product_num: v.limit_num,\n                  give_product_id: v.product_id,\n                  unique: v.unique\n                };\n                obj.give_product_id.push(goodsVal);\n              });\n            }\n\n            promotions.push(obj);\n          });\n\n          if (!_this7.flag) {\n            return;\n          } // 用户标签\n\n\n          var activeIds = [];\n\n          _this7.dataLabel.forEach(function (item) {\n            activeIds.push(item.id);\n          });\n\n          if (_this7.formValidate.is_label == 0) {\n            _this7.formValidate.label_id = [];\n          } else {\n            _this7.formValidate.label_id = activeIds;\n          }\n\n          var product_id = [];\n\n          _this7.tableData.forEach(function (item) {\n            var obj = {\n              product_id: item.id,\n              unique: []\n            };\n\n            if (item.attrValue.length) {\n              item.attrValue.forEach(function (j) {\n                obj.unique.push(j.unique);\n              });\n            }\n\n            product_id.push(obj);\n          });\n\n          if (_this7.formValidate.product_partake_type == 2 && !product_id.length) {\n            return _this7.$Message.error('请添加商品');\n          }\n\n          _this7.formValidate.promotions = promotions;\n          _this7.formValidate.product_id = product_id;\n          var storeId = [];\n\n          _this7.storesList.forEach(function (item) {\n            storeId.push(item.id);\n          });\n\n          if (_this7.formValidate.applicable_type == 2 && !storeId.length) {\n            return _this7.$Message.warning('请添加适用门店');\n          }\n\n          _this7.formValidate.applicable_store_id = storeId;\n          savePromotions(4, _this7.$route.params.id, _this7.formValidate).then(function (res) {\n            _this7.$router.push({\n              path: \"\".concat(_this7.roterPre, \"/marketing/discount/give\")\n            });\n\n            _this7.$Message.success(res.msg);\n          }).catch(function (err) {\n            _this7.$Message.error(err.msg);\n          });\n        } else {\n          _this7.$Message.warning(\"请完善数据\");\n        }\n      });\n    }\n  })\n};", null]}