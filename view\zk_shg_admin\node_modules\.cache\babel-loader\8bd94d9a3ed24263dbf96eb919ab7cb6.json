{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\addAuthGroup.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\addAuthGroup.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport department from \"@/components/department/index.vue\";\nimport userLabel from \"@/components/labelList\";\nimport { workGroupChat, workLabel, groupChatAuthSave, getGroupChatInfo, UpdateGroupChat } from \"@/api/work\";\nimport Setting from \"@/setting\";\nexport default {\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      formItem: {\n        name: \"\",\n        //二维码名称\n        chat_id: [],\n        //群聊chat_id\n        group_name: \"\",\n        //群名称\n        group_num: 0,\n        //群序列号\n        //owner:[], //群主userid\n        //admin_user:[], //群管理员userid\n        label: [],\n        //客户标签\n        auth_group_chat: 0 //自动建群0=关闭，1=开启\n        //welcome_words:{\n        //  text:{\n        //    content: \"\",\n        //  },\n        //  attachments: [],\n        //}\n\n      },\n      labelList: [],\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      groupColumn: [{\n        type: \"selection\",\n        width: 60,\n        align: \"center\"\n      }, {\n        title: \"群名称\",\n        key: \"name\",\n        minWidth: 80,\n        align: 'center'\n      }, {\n        title: \"群主\",\n        slot: \"ownerInfo\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"群公告\",\n        slot: \"notice\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"管理员\",\n        slot: \"admin_user_list\",\n        minWidth: 80,\n        align: 'center'\n      }, {\n        title: \"创建时间\",\n        key: \"group_create_time\",\n        minWidth: 110,\n        align: 'center'\n      }, {\n        title: \"群人数\",\n        key: \"member_num\",\n        minWidth: 80,\n        align: 'center'\n      }, {\n        title: \"退群人数\",\n        key: \"retreat_group_num\",\n        minWidth: 80,\n        align: 'center'\n      }],\n      groupData: [],\n      groupForm: {\n        page: 1,\n        limit: 15\n      },\n      rontineObj: {\n        msgtype: \"miniprogram\",\n        miniprogram: {\n          pic_url: \"\",\n          pic_media_id: \"\",\n          title: \"\",\n          appid: \"\",\n          page: \"\"\n        }\n      },\n      imageObj: {\n        msgtype: \"image\",\n        image: {\n          media_id: \"\",\n          pic_url: \"\"\n        }\n      },\n      groupStatus: false,\n      userLoading: false,\n      picTit: \"\",\n      modalPic: false,\n      modalRoutine: false,\n      isChoice: \"单选\",\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: [],\n      selectGroup: [],\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '二维码名称不能为空',\n          trigger: 'blur'\n        }]\n      },\n      labelShow: false,\n      dataLabel: []\n    };\n  },\n  components: {\n    uploadPictures: uploadPictures,\n    department: department,\n    userLabel: userLabel\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\", \"menuCollapse\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    }\n  }),\n  watch: {\n    dataLabel: function dataLabel(val) {\n      this.formItem.label = val.map(function (item) {\n        return item.tag_id;\n      });\n    }\n  },\n  mounted: function mounted() {\n    if (this.$route.params.id !== \"0\" && this.$route.params.id) {\n      this.getInfo();\n    }\n\n    this.getWorkGroupChat(); // this.getWorkLabel();\n  },\n  methods: {\n    getWorkGroupChat: function getWorkGroupChat() {\n      var _this = this;\n\n      this.userLoading = true;\n      workGroupChat(this.groupForm).then(function (res) {\n        _this.groupData = res.data;\n        _this.userLoading = false;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n\n        _this.userLoading = false;\n      });\n    },\n    addGroup: function addGroup() {\n      this.groupStatus = true;\n    },\n    selectAll: function selectAll(row) {\n      if (row.length) {\n        this.selectGroup = row;\n      }\n    },\n    handleSelectRow: function handleSelectRow(row) {\n      this.selectGroup = row;\n    },\n    groupConfirm: function groupConfirm() {\n      this.formItem.chat_id = this.selectGroup.map(function (item) {\n        return {\n          chat_id: item.chat_id,\n          name: item.name\n        };\n      });\n    },\n    groupChange: function groupChange(index) {\n      this.groupForm.page = index;\n      this.getWorkGroupChat();\n    },\n    modalPicTap: function modalPicTap(picTit) {\n      this.modalPic = true;\n      this.picTit = picTit;\n    },\n    addRoutine: function addRoutine() {\n      this.rontineObj.miniprogram.pic_url = \"\";\n      this.rontineObj.miniprogram.title = \"\";\n      this.rontineObj.miniprogram.appid = \"\";\n      this.rontineObj.miniprogram.page = \"\";\n      this.modalRoutine = true;\n    },\n    addUser: function addUser(type, index) {\n      this.$refs.department.memberStatus = true;\n\n      switch (type) {\n        case 'one':\n          // this.userList = this.formItem.userids;\n          this.$refs.department.openType = 'one';\n          break;\n\n        case 'two':\n          // this.userList = this.formItem.reserve_userid;\n          this.$refs.department.openType = 'two';\n          break;\n\n        default:\n          break;\n      }\n    },\n    //确认人员\n    changeMastart: function changeMastart(arr, type) {\n      if (type == 'one') {\n        if (arr.length && arr.length > 1) {\n          this.$Message.warning(\"群主只能选择一个\");\n          var newArr = arr.slice(0, 1);\n          this.formItem.owner = newArr.map(function (item) {\n            return {\n              userid: item.userid,\n              name: item.name\n            };\n          });\n        } else {\n          this.formItem.owner = arr.map(function (item) {\n            return {\n              userid: item.userid,\n              name: item.name\n            };\n          });\n        }\n      } else if (type == 'two') {\n        this.formItem.admin_user = arr.map(function (item) {\n          return {\n            userid: item.userid,\n            name: item.name\n          };\n        });\n      }\n    },\n    //tag标签删除成员\n    handleDel: function handleDel(e, name) {\n      var i = this.formItem.chat_id.findIndex(function (item) {\n        return item.name == name;\n      });\n      this.formItem.chat_id.splice(i, 1);\n    },\n    //欢迎语tag删除\n    wordsDel: function wordsDel(name) {\n      var index = this.formItem.welcome_words.attachments.indexOf(name);\n      this.formItem.welcome_words.attachments.splice(index, 1);\n    },\n    // 选中图片\n    getPic: function getPic(pc) {\n      switch (this.picTit) {\n        case \"image\":\n          this.imageObj.image.pic_url = pc.att_dir;\n          this.formItem.welcome_words.attachments.push(this.imageObj);\n          break;\n\n        case \"routine\":\n          this.rontineObj.miniprogram.pic_url = pc.att_dir;\n          break;\n      }\n\n      this.modalPic = false;\n    },\n    insertName: function insertName() {\n      this.formItem.welcome_words.text.content = \"##客户名称##\";\n    },\n    routineConfirm: function routineConfirm() {\n      var routine = this.deepClone(this.rontineObj);\n      this.formItem.welcome_words.attachments.push(routine);\n    },\n    routineCancel: function routineCancel() {},\n    //获取客户标签\n    getWorkLabel: function getWorkLabel() {\n      var _this2 = this;\n\n      workLabel().then(function (res) {\n        // this.labelList = res.data.map((org) => this.mapTree(org));\n        _this2.mapTree(res.data);\n      });\n    },\n    mapTree: function mapTree(org) {\n      for (var i = 0; i < org.length; i++) {\n        for (var j = 0; j < this.formItem.label.length; j++) {\n          if (this.formItem.label[j] === org[i].value) {\n            this.dataLabel.push({\n              label_name: org[i].label,\n              id: org[i].id,\n              tag_id: org[i].value\n            });\n          }\n        }\n\n        Array.isArray(org[i].children) && this.mapTree(org[i].children);\n      } //   const haveChildren =\n      //     Array.isArray(org.children) && org.children.length > 0;\n      //   return {\n      //     //分别将我们查询出来的值做出改变他的key\n      //     title: org.label,\n      //     expand: true,\n      //     value: org.value,\n      //     selected: false,\n      //     checked: false,\n      //     children: haveChildren ? org.children.map((i) => this.mapTree(i)) : [],\n      //   };\n\n    },\n    getInfo: function getInfo() {\n      var _this3 = this;\n\n      getGroupChatInfo(this.$route.params.id).then(function (res) {\n        _this3.formItem = res.data;\n        _this3.formItem.chat_id = _this3.formItem.chatList;\n\n        _this3.getWorkLabel();\n      });\n    },\n    submit: function submit() {\n      var _this4 = this;\n\n      if (!this.formItem.chat_id.length) return this.$Message.error(\"请添加群聊\");\n      var formData = this.deepClone(this.formItem);\n      formData.chat_id = formData.chat_id.map(function (item) {\n        return item.chat_id;\n      }); // formData.admin_user = formData.admin_user.map(item=>{\n      //   return item.userid\n      // })\n      // formData.owner = formData.owner[0].userid;\n\n      if (this.$route.params.id) {\n        delete formData.chatList;\n        delete formData.labelList;\n        UpdateGroupChat(this.$route.params.id, formData).then(function (res) {\n          _this4.$Message.success(\"修改自动拉群成功\");\n\n          _this4.$router.push(_this4.roterPre + '/work/auth_group');\n        });\n      } else {\n        groupChatAuthSave(formData).then(function (res) {\n          _this4.$Message.success(\"保存成功\");\n\n          _this4.$router.push(_this4.roterPre + '/work/auth_group');\n        }).catch(function (err) {\n          _this4.$Message.error(err.msg);\n        });\n      }\n    },\n    //深克隆\n    deepClone: function deepClone(obj) {\n      var newObj = Array.isArray(obj) ? [] : {};\n\n      if (obj && _typeof(obj) === \"object\") {\n        for (var key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            newObj[key] = obj && _typeof(obj[key]) === 'object' ? this.deepClone(obj[key]) : obj[key];\n          }\n        }\n      }\n\n      return newObj;\n    },\n    openLabel: function openLabel() {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    activeData: function activeData(dataLabel) {\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    // 标签弹窗关闭\n    labelClose: function labelClose() {\n      this.labelShow = false;\n    },\n    closeLabel: function closeLabel(label) {\n      var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.dataLabel.splice(index, 1);\n    }\n  }\n};", null]}