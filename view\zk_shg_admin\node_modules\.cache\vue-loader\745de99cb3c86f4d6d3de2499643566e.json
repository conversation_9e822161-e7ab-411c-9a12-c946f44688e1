{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue", "mtime": 1693985518000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import { getUserLabel,putUserLabel,putUsersSetLabel } from '@/api/user'\n    export default {\n        name: \"userLabel\",\n        props:{\n            uid:{\n                type:String | Number | Object,\n                default:''\n            }\n        },\n        data(){\n            return {\n\t\t\t\tisUser:false,\n                labelList:[],\n                activeIds:[],\n\t\t\t\tinfo:{},\n\t\t\t\toid:''\n            }\n        },\n        watch:{\n            uid:{\n                handler(nVal,oVal){\n                    if(nVal!=oVal){\n                        this.getList()\n                    }\n                },\n                deep:true\n            }\n        },\n        mounted() {\n            this.getList()\n        },\n        methods:{\n            getList(){\n\t\t\t\tlet uid = 0;\n\t\t\t\tif(this.uid instanceof Object){\n\t\t\t\t\tthis.info = this.uid\n\t\t\t\t\tif((!this.uid.all && this.uid.uids.length>1) || this.uid.all ){\n\t\t\t\t\t\tuid = 0\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuid = this.uid.uids[0]\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tuid = this.uid\n\t\t\t\t}\n\t\t\t\tthis.oid = uid;\n                getUserLabel(uid).then(res=>{\n                    res.data.map(el=>{\n\t\t\t\t\t\tthis.isUser = true;\n                        el.label.map(label=>{\n                            if(label.disabled){\n                                this.activeIds.push(label.id)\n                            }\n                        })\n                    })\n                    this.labelList =res.data\n                })\n            },\n            selectLabel(label){\n                if(label.disabled){\n                    let index = this.activeIds.indexOf(label.id)\n                    this.activeIds.splice(index,1)\n                    label.disabled = false\n                }else{\n                    this.activeIds.push(label.id)\n                    label.disabled = true\n                }\n            },\n            // 确定\n            subBtn(){\n                let unLaberids = [];\n                this.labelList.map(item=>{\n                    item.label.map( i => {\n                        if(i.disabled == false){\n                            unLaberids.push(i.id);\n                        }\n                    });\n                });\n\t\t\t\tlet obj = this.info;\n\t\t\t\tobj.label_id  = this.activeIds;\n\t\t\t\t(this.oid==0?putUsersSetLabel(obj):putUserLabel(this.oid,{\n                    label_ids: this.activeIds,\n                    un_label_ids: unLaberids\n                }))\n               .then(res=>{\n                    this.activeIds = []\n                    this.labelList = []\n                    this.$Message.success(res.msg)\n                    this.$emit('close',this.oid)\n                }).catch(error=>{\n                    this.$Message.error(error.msg)\n                })\n            },\n            cancel(){\n                this.activeIds = []\n                this.labelList = []\n                this.$emit('close')\n            }\n        }\n    }\n", null]}