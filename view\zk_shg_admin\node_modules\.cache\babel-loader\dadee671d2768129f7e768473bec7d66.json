{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\add_template.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\add_template.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from \"vuex\";\nimport uploadPictures from \"@/components/uploadPictures\"; // import department from \"@/components/department/index.vue\";\n\nimport { workGroupChat, workGroupTemplateChatSave } from \"@/api/work\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport { getNewFormBuildRuleApi } from \"@/api/setting\";\nimport Setting from \"@/setting\";\nexport default {\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      userLoading: false,\n      groupStatus: false,\n      formItem: {\n        template_type: \"0\",\n        //0=立即发送，1=定时发送\n        name: \"\",\n        type: \"1\",\n        //客户群发\n        client_type: \"0\",\n        //0=全部客户，1=所选客户\n        where_time: \"\",\n        //客户筛选时间\n        where_label: [],\n        //标签\n        ownerInfo: [],\n        //所选群发账号\n        send_time: \"\",\n        //定时发送账号\n        welcome_words: {\n          text: {\n            content: \"\"\n          },\n          attachments: []\n        }\n      },\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '群发名称不能为空',\n          trigger: 'blur'\n        }],\n        template_type: [{\n          required: true,\n          message: '请选择发送类型',\n          trigger: 'change'\n        }]\n      },\n      options: timeOptions,\n      timeVal: [],\n      //客户标签列表\n      labelList: [],\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      rontineObj: {\n        msgtype: \"miniprogram\",\n        miniprogram: {\n          pic_url: \"\",\n          pic_media_id: \"\",\n          title: \"\",\n          appid: \"\",\n          page: \"\"\n        }\n      },\n      imageObj: {\n        msgtype: \"image\",\n        image: {\n          media_id: \"\",\n          pic_url: \"\"\n        }\n      },\n      groupColumn: [{\n        type: \"selection\",\n        width: 60,\n        align: \"center\"\n      }, {\n        title: \"群名称\",\n        key: \"name\",\n        minWidth: 80,\n        align: 'center'\n      }, {\n        title: \"群主\",\n        slot: \"ownerInfo\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"群公告\",\n        slot: \"notice\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"管理员\",\n        slot: \"admin_user_list\",\n        minWidth: 80,\n        align: 'center'\n      }, {\n        title: \"创建时间\",\n        key: \"group_create_time\",\n        minWidth: 110,\n        align: 'center'\n      }, {\n        title: \"群人数\",\n        key: \"member_num\",\n        minWidth: 80,\n        align: 'center'\n      }, {\n        title: \"退群人数\",\n        key: \"retreat_group_num\",\n        minWidth: 80,\n        align: 'center'\n      }],\n      groupData: [],\n      groupForm: {\n        page: 1,\n        limit: 15\n      },\n      picTit: \"\",\n      modalPic: false,\n      modalRoutine: false,\n      isChoice: \"单选\",\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: []\n    };\n  },\n  components: {\n    uploadPictures: uploadPictures\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    }\n  }),\n  mounted: function mounted() {\n    this.setCopyrightShow({\n      value: false\n    });\n    this.getWorkGroupChat();\n  },\n  destroyed: function destroyed() {\n    this.setCopyrightShow({\n      value: true\n    });\n  },\n  methods: _objectSpread({}, mapMutations(\"admin/layout\", [\"setCopyrightShow\"]), {\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formItem.where_time = this.timeVal.join(\"-\");\n    },\n    snedChangeTime: function snedChangeTime(val) {\n      this.formItem.send_time = val;\n    },\n    modalPicTap: function modalPicTap(picTit) {\n      this.modalPic = true;\n      this.picTit = picTit;\n    },\n    selectAll: function selectAll(row) {\n      if (row.length) {\n        this.selectGroup = row;\n      }\n    },\n    handleSelectRow: function handleSelectRow(row) {\n      this.selectGroup = row;\n    },\n    groupConfirm: function groupConfirm() {\n      // this.formItem.userids = this.selectGroup.map(item=>{\n      //   return {\n      //     userid:item.ownerInfo.userid,\n      //     name:item.ownerInfo.name\n      //   }\n      // })\n      this.formItem.ownerInfo = this.selectGroup.map(function (item) {\n        return {\n          userid: item.ownerInfo.userid,\n          name: item.ownerInfo.name\n        };\n      });\n    },\n    getWorkGroupChat: function getWorkGroupChat() {\n      var _this = this;\n\n      this.userLoading = true;\n      workGroupChat(this.groupForm).then(function (res) {\n        _this.groupData = res.data;\n        _this.userLoading = false;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n\n        _this.userLoading = false;\n      });\n    },\n    mapTree: function mapTree(org) {\n      var _this2 = this;\n\n      var haveChildren = Array.isArray(org.children) && org.children.length > 0;\n      return {\n        //分别将我们查询出来的值做出改变他的key\n        title: org.label,\n        expand: true,\n        value: org.value,\n        selected: false,\n        checked: false,\n        children: haveChildren ? org.children.map(function (i) {\n          return _this2.mapTree(i);\n        }) : []\n      };\n    },\n    addRoutine: function addRoutine() {\n      var _this3 = this;\n\n      getNewFormBuildRuleApi('routine').then(function (res) {\n        var data = res.data;\n        _this3.rontineObj.miniprogram.pic_url = '';\n        _this3.rontineObj.miniprogram.title = data.routine_name.value;\n        _this3.rontineObj.miniprogram.appid = data.routine_appId.value;\n        _this3.rontineObj.miniprogram.page = '/pages/index/index';\n      });\n      this.modalRoutine = true;\n    },\n    addUser: function addUser() {\n      this.groupStatus = true;\n    },\n    //tag标签删除成员\n    handleDel: function handleDel(e, name) {\n      var index = this.formItem.ownerInfo.indexOf(name);\n      this.formItem.ownerInfo.splice(index, 1);\n    },\n    //群发内容tag删除\n    wordsDel: function wordsDel(name) {\n      var index = this.formItem.welcome_words.attachments.indexOf(name);\n      this.formItem.welcome_words.attachments.splice(index, 1);\n    },\n    groupChange: function groupChange(index) {\n      this.groupForm.page = index;\n      this.getWorkGroupChat();\n    },\n    // 选中图片\n    getPic: function getPic(pc) {\n      switch (this.picTit) {\n        case \"image\":\n          this.imageObj.image.pic_url = pc.att_dir;\n          this.formItem.welcome_words.attachments.push(this.imageObj);\n          break;\n\n        case \"routine\":\n          this.rontineObj.miniprogram.pic_url = pc.att_dir;\n          break;\n      }\n\n      this.modalPic = false;\n    },\n    // insertName() {\n    //   this.formItem.welcome_words.text.content =\n    //     this.formItem.welcome_words.text.content.concat(\"##客户名称##\");\n    // },\n    routineConfirm: function routineConfirm() {\n      var routine = this.deepClone(this.rontineObj);\n      this.formItem.welcome_words.attachments.push(routine);\n    },\n    submit: function submit() {\n      var _this4 = this;\n\n      var formData = this.deepClone(this.formItem);\n      formData.userids = formData.ownerInfo.map(function (item) {\n        return item.userid;\n      });\n      this.$refs.formItem.validate(function (valid) {\n        if (valid) {\n          workGroupTemplateChatSave(formData).then(function (res) {\n            _this4.$Message.success(res.msg);\n\n            _this4.$router.push(_this4.roterPre + \"/work/group/template\");\n          }).catch(function (err) {\n            _this4.$Message.error(err.msg);\n          });\n        }\n      });\n    },\n    //分页\n    ownerChange: function ownerChange(index) {\n      this.clientForm.page = index;\n      this.getownerList();\n    },\n    //深克隆\n    deepClone: function deepClone(obj) {\n      var newObj = Array.isArray(obj) ? [] : {};\n\n      if (obj && _typeof(obj) === \"object\") {\n        for (var key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            newObj[key] = obj && _typeof(obj[key]) === \"object\" ? this.deepClone(obj[key]) : obj[key];\n          }\n        }\n      }\n\n      return newObj;\n    }\n  })\n};", null]}