{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_card_2.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_card_2.vue", "mtime": 1734512157889}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { getProduct } from '@/api/diy';\nexport default {\n  name: 'c_home_card_2',\n  componentsName: 'home_card_2',\n  cname: '商品卡片二',\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      oneContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleLeft'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'styleConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'slideConfig'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleTab'\n      }, {\n        components: toolCom.c_promotion,\n        configNme: 'tabConfig'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleCart'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'cartConfig'\n      }],\n      twoContent: [{\n        components: toolCom.c_button_img,\n        configNme: 'bntStyleConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'bntConfig'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneConfig'\n      }],\n      twoStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'decorateColor'\n      }],\n      twoStyle2: [{\n        components: toolCom.c_bg_color,\n        configNme: 'decorateColor2'\n      }],\n      threeStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'textColor2'\n      }],\n      threeStyle2: [{\n        components: toolCom.c_bg_color,\n        configNme: 'textColor'\n      }],\n      threeStyle3: [{\n        components: toolCom.c_bg_color,\n        configNme: 'textColor3'\n      }],\n      fourStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCart'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneCartConfig'\n      }],\n      fourStyle2: [{\n        components: toolCom.c_bg_color,\n        configNme: 'bntBgColor'\n      }],\n      currencyStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'moduleColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'mbConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      setUp: 0,\n      type: 0,\n      type2: 0,\n      type3: 0,\n      type4: 0\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      // debugger;\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          this.getRComContent(arr, this.type3);\n        } else {\n          this.getRComStyle(arr, this.type, this.type2, this.type3, this.type4);\n        }\n      },\n      deep: true\n    },\n    'configObj.styleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          this.getRComStyle(arr, nVal, this.type2, this.type3, this.type4);\n        }\n      }\n    },\n    'configObj.cartConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type3 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp == 0) {\n          this.getRComContent(arr, nVal);\n        } else {\n          this.getRComStyle(arr, this.type, this.type2, nVal, this.type4);\n        }\n      }\n    },\n    'configObj.toneConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type2 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          this.getRComStyle(arr, this.type, nVal, this.type3, this.type4);\n        }\n      }\n    },\n    'configObj.toneCartConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type4 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          this.getRComStyle(arr, this.type, this.type2, this.type3, nVal);\n        }\n      }\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    getRComContent: function getRComContent(arr, type3) {\n      if (type3 == 0) {\n        this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.twoContent));\n      } else {\n        this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent));\n      }\n    },\n    getRComStyle: function getRComStyle(arr, type, type2, type3, type4) {\n      var obj = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n      var obj2 = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n\n      if (type == 0) {\n        if (type2 == 0) {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = obj2;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = obj;\n          }\n        } else {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n          }\n        }\n      } else if (type == 1) {\n        if (type2 == 0) {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = obj2;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = obj;\n          }\n        } else {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle2), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle2), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle2), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n          }\n        }\n      } else if (type == 2) {\n        if (type2 == 0) {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = obj2;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = obj;\n          }\n        } else {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle2), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle2), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle2), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n          }\n        }\n      } else {\n        if (type2 == 0) {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = obj2;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = obj;\n          }\n        } else {\n          if (type3 == 0) {\n            if (type4 == 0) {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle3), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle3), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle3), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n          }\n        }\n      }\n    },\n    getConfig: function getConfig(data) {\n      var configObj = this.configObj.tabConfig.list[this.configObj.tabConfig.tabCur];\n      var activeValue = configObj.selectConfig.activeValue;\n\n      if (!data.name && data == 1) {\n        configObj.goodsList.list = [];\n        return;\n      }\n\n      if (!data.name && data == 0 && !activeValue.length) {\n        configObj.goodsList.list = [];\n        return;\n      }\n\n      var type = configObj.tabVal;\n      var dataObj = {\n        page: 1,\n        limit: data.values || configObj.numConfig.val,\n        priceOrder: configObj.goodsSort == 2 ? 'desc' : '',\n        salesOrder: configObj.goodsSort == 1 ? 'desc' : ''\n      };\n\n      if (type == 1) {\n        this.configObj.productList.list = [];\n        return;\n      } else if (type == 2) {\n        dataObj.brand_id = configObj.brandConfig.brandVal;\n      } else if (type == 3) {\n        dataObj.id = activeValue;\n      } else {\n        dataObj.store_label_id = configObj.goodsLabel.activeValue;\n      }\n\n      getProduct(dataObj).then(function (res) {\n        configObj.productList.list = res.data;\n      });\n    }\n  }\n};", null]}