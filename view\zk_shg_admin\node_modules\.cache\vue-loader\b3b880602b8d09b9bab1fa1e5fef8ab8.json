{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nexport default {\n    name: 'attribute',\n    props: {\n        attrTemplate: {\n            type: Boolean\n        }\n    },\n    data () {\n        return {\n            val: false,\n            specsVal: '',\n            specs: [],\n            attrVal: '',\n            attrList: []\n        }\n    },\n    watch: {\n        attrTemplate: function (n) {\n            this.val = n\n        }\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 70;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'right';\n        }\n    },\n    methods: {\n        cancel () {\n            this.$emit('changeTemplate', false)\n        },\n        confirm () {\n            if (this.specsVal === '') {\n                this.$Message.error('请填写规格名称');\n            } else {\n                this.specs.push(this.specsVal);\n                this.attrList.push({\n                    attr: this.specsVal,\n                    inputVal: '',\n                    attrVal: []\n                });\n                this.specsVal = '';\n                if (this.specsVal !== '') {\n                    this.attrList.forEach(item => {\n                        if (item.attrVal.length < 1) {\n                            this.$Message.error('请填写规格属性');\n                        }\n                    })\n                }\n            }\n\n        },\n        confirmAttr (index) {\n            let attrList = this.attrList[index];\n            if (attrList.inputVal === '') {\n                this.$Message.error('请填写规格属性');\n            } else {\n                attrList.attrVal.push(attrList.inputVal);\n                attrList.inputVal === '';\n            }\n        }\n    },\n    mounted () {}\n}\n", null]}