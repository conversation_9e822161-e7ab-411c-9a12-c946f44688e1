{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue?vue&type=template&id=6b24a4ca&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"hot_imgs\">\n  <div class=\"title\" v-if=\"configData.title\">\n    {{ configData.title }}\n  </div>\n  <div class=\"list-box\">\n    <draggable\n      class=\"dragArea list-group\"\n      :list=\"configData.list\"\n      group=\"peoples\"\n      handle=\".move-icon\"\n    >\n      <div class=\"item\" v-for=\"(item, index) in configData.list\" :key=\"index\">\n        <div\n          class=\"delect-btn\"\n          @click.stop=\"bindDelete(item, index)\"\n          v-if=\"!configData.isCube\"\n        >\n          <span class=\"iconfont-diy icondel_1\"></span>\n        </div>\n        <div class=\"move-icon\">\n          <span class=\"iconfont-diy iconxingzhuangjiehe\"></span>\n        </div>\n        <div class=\"img-box\" @click=\"modalPicTap('单选', index)\">\n          <img :src=\"item.img\" alt=\"\" v-if=\"item.img\" />\n          <div class=\"upload-box\" v-else>\n            <Icon type=\"ios-add\" size=\"50\" />\n          </div>\n        </div>\n        <div class=\"info\">\n          <div class=\"info-item\" v-for=\"(infos, key) in item.info\" :key=\"key\">\n            <span class=\"span\">{{ infos.title }}</span>\n            <div class=\"input-box\" @click=\"getLink(index, key, item.info)\">\n              <Input\n                :icon=\"key == item.info.length - 1 ? 'ios-arrow-forward' : ''\"\n                v-model=\"infos.value\"\n                :readonly=\"key == item.info.length - 1 ? true : false\"\n                :placeholder=\"infos.tips\"\n                :maxlength=\"infos.max\"\n                v-if=\"configData.isCube\"\n                @on-blur=\"onBlur\"\n              />\n              <Input\n                :icon=\"key == item.info.length - 1 ? 'ios-arrow-forward' : ''\"\n                v-model=\"infos.value\"\n                :readonly=\"key == item.info.length - 1 ? true : false\"\n                :placeholder=\"infos.tips\"\n                :maxlength=\"infos.max\"\n                show-word-limit\n                v-else\n              />\n            </div>\n          </div>\n          <div class=\"info-item\" v-if=\"configData.type\">\n            <span class=\"span\">状态</span>\n            <i-switch v-model=\"item.show\" />\n          </div>\n        </div>\n      </div>\n    </draggable>\n    <div>\n      <Modal\n        v-model=\"modalPic\"\n        width=\"960px\"\n        scrollable\n        footer-hide\n        closable\n        title=\"上传图片\"\n        :mask-closable=\"false\"\n        :z-index=\"1\"\n      >\n        <uploadPictures\n          :isChoice=\"isChoice\"\n          @getPic=\"getPic\"\n          :gridBtn=\"gridBtn\"\n          :gridPic=\"gridPic\"\n          v-if=\"modalPic\"\n        ></uploadPictures>\n      </Modal>\n    </div>\n  </div>\n  <template v-if=\"configData.list\">\n    <div class=\"add-btn\" v-if=\"configData.list.length < configData.maxList\">\n      <Button class=\"btn\" type=\"primary\" ghost @click=\"addBox\">\n        <span class=\"iconfont iconjiahao\"></span>{{ configData.bnt }}\n      </Button>\n    </div>\n  </template>\n  <linkaddress ref=\"linkaddres\" @linkUrl=\"linkUrl\"></linkaddress>\n</div>\n", null]}