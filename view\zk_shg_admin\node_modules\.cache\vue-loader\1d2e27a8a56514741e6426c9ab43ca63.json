{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue?vue&type=template&id=230761d4&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"carMywrapper\">\n\t\t<div class=\"type-radio\">\n\t\t  <Form :label-width=\"80\">\n\t\t    <FormItem label=\"卡密类型：\">\n\t\t      <RadioGroup v-model=\"cartMyType\" size=\"large\">\n\t\t        <Radio :label=\"1\">固定卡密</Radio>\n\t\t        <Radio :label=\"2\">一次性卡密</Radio>\n\t\t      </RadioGroup>\n\t\t      <div v-if=\"cartMyType == 1\">\n\t\t\t\t  <div class=\"stock-disk\">\n\t\t\t\t\t  <Input\n\t\t\t\t\t    v-model=\"fixedCar.disk_info\"\n\t\t\t\t\t    size=\"large\"\n\t\t\t\t\t    type=\"textarea\"\n\t\t\t\t\t    :rows=\"4\"\n\t\t\t\t\t    placeholder=\"填写卡密信息\"\n\t\t\t\t\t  />\n\t\t\t\t  </div>\n\t\t\t\t  <div class=\"stock-input\">\n\t\t\t\t\t  <Input\n\t\t\t\t\t    type=\"number\"\n\t\t\t\t\t    v-model=\"fixedCar.stock\"\n\t\t\t\t\t    size=\"large\"\n\t\t\t\t\t    placeholder=\"填写库存数量\"\n\t\t\t\t\t  >\n\t\t\t\t\t    <span slot=\"append\">件</span>\n\t\t\t\t\t  </Input>\n\t\t\t\t  </div>\n\t\t      </div>\n\t\t      <div class=\"scroll-virtual\" v-if=\"cartMyType == 2\">\n\t\t        <div\n\t\t          class=\"acea-row row-middle mb10\"\n\t\t          v-for=\"(item, index) in virtualList\"\n\t\t          :key=\"index\"\n\t\t        >\n\t\t          <span class=\"mr10 virtual-title\">卡号{{ index + 1 }}：</span>\n\t\t          <Input\n\t\t            class=\"mr10 width15\"\n\t\t            type=\"text\"\n\t\t            v-model.trim=\"item.key\"\n\t\t            placeholder=\"请输入卡号(非必填)\"\n\t\t          ></Input>\n\t\t          <span class=\"mr10 virtual-title\">卡密{{ index + 1 }}：</span>\n\t\t          <Input\n\t\t            class=\"mr10 width15\"\n\t\t            type=\"text\"\n\t\t            v-model.trim=\"item.value\"\n\t\t            placeholder=\"请输入卡密\"\n\t\t          ></Input>\n\t\t          <span class=\"deteal-btn\" @click=\"removeVirtual(index)\"\n\t\t            >删除</span\n\t\t          >\n\t\t        </div>\n\t\t      </div>\n\t\t      <div class=\"add-more\" v-if=\"cartMyType == 2\">\n\t\t        <Button type=\"primary\" @click=\"handleAdd\"\n\t\t          >添加卡密</Button\n\t\t        >\n\t\t        <Upload\n\t\t\t\t  ref=\"upload\"\n\t\t          class=\"ml10\"\n\t\t          :action=\"cardUrl\"\n\t\t          :before-upload=\"beforeUpload\"\n\t\t          :headers=\"header\"\n\t\t          :on-success=\"upFile\"\n\t\t\t\t  :format=\"['xlsx']\"\n\t\t\t\t  :on-format-error=\"handleFormatError\"\n\t\t        >\n\t\t          <Button type=\"success\">导入卡密</Button>\n\t\t        </Upload>\n\t\t\t\t<Button class=\"download\" type=\"default\" icon=\"ios-download-outline\" @click=\"getCarMyList\">下载卡密模板</Button>\n\t\t      </div>\n\t\t    </FormItem>\n\t\t  </Form>\n\t\t</div>\n        <div class=\"footer\">\n\t\t\t<Button type=\"primary\" class=\"btns\" ghost @click=\"cancel\">取消</Button>\n            <Button type=\"primary\" class=\"btns\" @click=\"subBtn\">确定</Button>\n        </div>\n    </div>\n", null]}