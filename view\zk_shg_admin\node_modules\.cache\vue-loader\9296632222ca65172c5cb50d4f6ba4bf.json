{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addReply.vue?vue&type=template&id=2261081c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addReply.vue", "mtime": 1690874758000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<Modal\n  :value=\"visible\"\n  :z-index=\"2\"\n  title=\"添加自评\"\n  width=\"700\"\n  @on-ok=\"onOk\"\n  @on-cancel=\"onCancel\"\n>\n  <Form :model=\"formData\" :label-width=\"125\">\n    <FormItem label=\"商品\" required>\n      <div class=\"upload-box\" @click=\"callGoods\">\n        <img v-if=\"goods.id\" :src=\"goods.image\" class=\"image\" />\n        <Icon v-else type=\"ios-add\" />\n      </div>\n    </FormItem>\n    <FormItem v-if=\"goods.id\" label=\"商品规格\" required>\n      <div class=\"upload-box\" @click=\"callAttr\">\n        <img v-if=\"attr.unique\" :src=\"attr.image\" class=\"image\" />\n        <Icon v-else type=\"ios-add\" />\n      </div>\n    </FormItem>\n    <FormItem label=\"用户头像\" required>\n      <div class=\"upload-box\" @click=\"callPicture('单选')\">\n        <img v-if=\"avatar.att_dir\" :src=\"avatar.att_dir\" class=\"image\" />\n        <Button\n          v-if=\"avatar.att_dir\"\n          shape=\"circle\"\n          icon=\"md-close\"\n          class=\"btn\"\n          @click.stop=\"removeUser\"\n        ></Button>\n        <Icon v-else type=\"ios-add\" />\n      </div>\n    </FormItem>\n    <FormItem label=\"用户名称\" required>\n      <Input v-model=\"formData.nickname\" placeholder=\"请输入用户名称\"></Input>\n    </FormItem>\n    <FormItem label=\"评价文字\" required>\n      <Input\n        v-model=\"formData.comment\"\n        type=\"textarea\"\n        :autosize=\"{ minRows: 2 }\"\n        placeholder=\"请输入评价文字\"\n      ></Input>\n    </FormItem>\n    <FormItem label=\"商品分数\" required>\n      <Rate v-model=\"product_score\" />\n    </FormItem>\n    <FormItem label=\"服务分数\" required>\n      <Rate v-model=\"service_score\" />\n    </FormItem>\n    <FormItem label=\"评价图片\">\n      <div v-for=\"item in picture\" :key=\"item.att_id\" class=\"upload-box\">\n        <img :src=\"item.att_dir\" class=\"image\" />\n        <Button\n          shape=\"circle\"\n          icon=\"md-close\"\n          class=\"btn\"\n          @click=\"removePicture(item.att_id)\"\n        ></Button>\n      </div>\n      <div\n        v-if=\"picture.length < 8\"\n        class=\"upload-box\"\n        @click=\"callPicture('多选')\"\n      >\n        <Icon type=\"ios-add\" />\n      </div>\n    </FormItem>\n    <FormItem label=\"评价时间\">\n      <DatePicker\n        :value=\"add_time\"\n        type=\"datetime\"\n        placeholder=\"请选择评论时间(不选择默认当前添加时间)\"\n        style=\"width: 200px\"\n        @on-change=\"onChange\"\n      />\n    </FormItem>\n  </Form>\n  <template slot=\"footer\">\n    <Button @click=\"onCancel\">取消</Button>\n    <Button type=\"primary\" @click=\"onOk\">确定</Button>\n  </template>\n</Modal>\n", null]}