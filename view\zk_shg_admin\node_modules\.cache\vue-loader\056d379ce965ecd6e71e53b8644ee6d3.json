{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\setupUser\\index.vue?vue&type=template&id=85284970&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\setupUser\\index.vue", "mtime": 1689129842000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n      <Tabs v-model=\"tabVal\" @on-click=\"tabChange\">\n        <!-- ---------------------------------基础信息-------------------------------- -->\n        <TabPane label=\"基础信息\" name=\"basic\">\n          <Form :model=\"basicsForm\" :label-width=\"120\">\n            <Row :gutter=\"24\" type=\"flex\">\n              <Col span=\"24\">\n                <div class=\"basics\">用户设置</div>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem label=\"用户默认头像：\">\n                  <div\n                    class=\"uploadPictrue\"\n                    v-if=\"authorizedPicture\"\n                    @click=\"modalPicTap('单选')\"\n                  >\n                    <img v-lazy=\"authorizedPicture\" />\n                  </div>\n                  <div\n                    class=\"uploadPictrue\"\n                    @click=\"modalPicTap('单选')\"\n                    v-else\n                  >\n                    <span class=\"iconfont iconshangpinshuliang-jia\"></span>\n                  </div>\n                  <div class=\"upload-text\">建议尺寸：120*120px</div>\n                </FormItem>\n              </Col>\n              <Col span=\"14\" class=\"mt10\">\n                <FormItem label=\"用户信息设置：\">\n                  <!-- 用户表格 -->\n                  <Table\n                    :data=\"listOne\"\n                    :columns=\"columns\"\n                    ref=\"table\"\n                    class=\"mt25 goods\"\n                    highlight-row\n                    :draggable=\"true\"\n                    @on-drag-drop=\"onDragDrop\"\n                  >\n                    <template slot-scope=\"{ row, index }\" slot=\"drag\">\n                      <div class=\"iconfont icondrag\"></div>\n                    </template>\n                    <!-- 使用 -->\n                    <template slot-scope=\"{ row, index }\" slot=\"use\">\n                      <Checkbox\n                        v-model=\"listOne[index].use\"\n                        :true-value=\"1\"\n                        :false-value=\"0\"\n                      ></Checkbox>\n                    </template>\n                    <!-- 必填 -->\n                    <template slot-scope=\"{ row, index }\" slot=\"required\">\n                      <Checkbox\n                        v-model=\"listOne[index].required\"\n                        :disabled=\"listOne[index].use == 0\"\n                        :true-value=\"1\"\n                        :false-value=\"0\"\n                      ></Checkbox>\n                    </template>\n                    <!-- 用户端展示 -->\n                    <template slot-scope=\"{ row, index }\" slot=\"user_show\">\n                      <Checkbox\n                        v-model=\"listOne[index].user_show\"\n                        :disabled=\"listOne[index].use == 0\"\n                        :true-value=\"1\"\n                        :false-value=\"0\"\n                      ></Checkbox>\n                    </template>\n\n                    <template slot-scope=\"{ row, index }\" slot=\"action\">\n                      <a @click=\"delInfo(index)\" v-if=\"!listOne[index].param\">删除</a>\n                    </template>\n                  </Table>\n                  <div class=\"upload-text goods\">\n                    开启使用后，后台添加用户时可填写此信息；开启必填后，后台添加用户时此信息必须填写；开启用户端展示后，在商城用户个人信息中展示\n                  </div>\n                  <div class=\"addInfo\" @click=\"addModel = true\">新增信息</div>\n                  <div class=\"subBtn mt20\" @click=\"handleSubmit('basic')\">\n                    保存\n                  </div>\n                </FormItem>\n              </Col>\n            </Row>\n          </Form>\n        </TabPane>\n        <!-- ---------------------------------登录注册-------------------------------- -->\n        <TabPane label=\"登录注册\" name=\"register\">\n          <Alert type=\"warning\" show-icon>{{loginForm.register_notice}}</Alert>\n          <Form :model=\"loginForm\" :label-width=\"120\">\n            <Row :gutter=\"24\" type=\"flex\">\n              <Col span=\"24\">\n                <div class=\"basics\">登录设置</div>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem label=\"强制手机号绑定：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"loginForm.store_user_mobile\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">商城登录时强制手机号登陆/绑定</div>\n                </FormItem>\n              </Col>\n\t\t\t  <Col span=\"24\" class=\"mt10\">\n\t\t\t    <FormItem label=\"用户协议：\">\n\t\t\t\t\t<RadioGroup v-model=\"loginForm.store_user_agreement\">\n\t\t\t\t\t  <Radio label=\"0\">\n\t\t\t\t\t    <span>自动同意</span>\n\t\t\t\t\t  </Radio>\n\t\t\t\t\t  <Radio label=\"1\">\n\t\t\t\t\t    <span>手动同意</span>\n\t\t\t\t\t  </Radio>\n\t\t\t\t\t</RadioGroup>\n\t\t\t\t\t<div class=\"upload-text\">商城登录时用户协议选定</div>\n\t\t\t    </FormItem>\n\t\t\t  </Col>\n              <Col span=\"24\">\n                <div class=\"basics\">注册有礼</div>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem label=\"注册有礼启用：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"loginForm.newcomer_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">新用户注册后，会给用户赠送礼品</div>\n                </FormItem>\n              </Col>\n              <Col span=\"24\" v-if=\"loginForm.newcomer_status === 1\">\n                <FormItem\n                  label=\"是否限时：\"\n                  v-if=\"loginForm.newcomer_status === 1\"\n                >\n                  <RadioGroup v-model=\"loginForm.newcomer_limit_status\">\n                    <Radio label=\"0\">\n                      <span>不限时</span>\n                    </Radio>\n                    <Radio label=\"1\">\n                      <span>限时</span>\n                    </Radio>\n                  </RadioGroup>\n                  <div class=\"upload-text\">新人注册活动的时间设置</div>\n                  <div\n                    class=\"mt10\"\n                    v-if=\"loginForm.newcomer_limit_status == 1\"\n                  >\n                    <Input\n                      v-model=\"loginForm.newcomer_limit_time\"\n                      placeholder=\"请输入限时天数\"\n                      class=\"inputw\"\n                    ></Input>\n                    <span\n                      class=\"span-text\"\n                      v-if=\"loginForm.newcomer_limit_status == 1\"\n                    >\n                      天\n                    </span>\n                  </div>\n                </FormItem>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem\n                  label=\"赠送积分：\"\n                  v-if=\"loginForm.newcomer_status === 1\"\n                >\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"loginForm.register_integral_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">用户注册后即赠送一定数额的积分</div>\n                  <Input\n                    v-model=\"loginForm.register_give_integral\"\n                    placeholder=\"请输入赠送积分\"\n                    class=\"inputw mt10\"\n                    v-if=\"loginForm.register_integral_status === 1\"\n                  ></Input>\n                  <span\n                    class=\"span-text\"\n                    v-if=\"loginForm.register_integral_status === 1\"\n                  >\n                    积分\n                  </span>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"loginForm.newcomer_status === 1\"\n              >\n                <FormItem\n                  label=\"赠送余额：\"\n                  v-if=\"loginForm.newcomer_status === 1\"\n                >\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"loginForm.register_money_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    用户注册后即赠送一定数额的储值余额\n                  </div>\n                  <Input\n                    v-model=\"loginForm.register_give_money\"\n                    placeholder=\"请输入赠送余额\"\n                    class=\"inputw mt10\"\n                    v-if=\"loginForm.register_money_status === 1\"\n                  ></Input>\n                  <span\n                    class=\"span-text\"\n                    v-if=\"loginForm.register_money_status === 1\"\n                  >\n                    元\n                  </span>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"loginForm.newcomer_status === 1\"\n              >\n                <div\n                  class=\"item\"\n                  v-for=\"(item, indexw) in promotionsData\"\n                  :key=\"indexw\"\n                >\n                  <!-- ----赠送优惠券------ -->\n                  <FormItem\n                    label=\"赠送优惠券：\"\n                    v-if=\"loginForm.newcomer_status === 1\"\n                  >\n                    <i-switch\n                      size=\"large\"\n                      v-model=\"loginForm.register_coupon_status\"\n                      :true-value=\"1\"\n                      :false-value=\"0\"\n                    >\n                      <span slot=\"open\">开启</span>\n                      <span slot=\"close\">关闭</span>\n                    </i-switch>\n                    <div class=\"upload-text\">用户注册后即赠送优惠券</div>\n                    <Table\n                      border\n                      :columns=\"columns1\"\n                      :data=\"item.giveCoupon\"\n                      ref=\"table\"\n                      class=\"table mt10\"\n                      width=\"700\"\n                      v-if=\"\n                        loginForm.register_coupon_status === 1 &&\n                        item.giveCoupon.length > 0\n                      \"\n                    >\n                      <template slot-scope=\"{ row }\" slot=\"coupon_price\">\n                        <span v-if=\"row.coupon_type == 1\">\n                          {{ row.coupon_price }}元\n                        </span>\n                        <span v-if=\"row.coupon_type == 2\">\n                          {{ parseFloat(row.coupon_price) / 10 }}折（{{\n                            row.coupon_price.toString().split('.')[0]\n                          }}%）\n                        </span>\n                      </template>\n                      <template slot-scope=\"{ row }\" slot=\"coupon_type\">\n                        <span v-if=\"row.coupon_type === 1\">满减券</span>\n                        <span v-else>折扣券</span>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"status\">\n                        <a @click=\"delCoupon(index, indexw)\">删除</a>\n                      </template>\n                    </Table>\n                    <div\n                      class=\"add-coupon\"\n                      @click=\"addCoupon(indexw)\"\n                      v-if=\"loginForm.register_coupon_status === 1\"\n                    >\n                      + 添加优惠券\n                    </div>\n                  </FormItem>\n                </div>\n              </Col>\n              <!-- ----赠送优惠券------ -->\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"loginForm.newcomer_status === 1\"\n              >\n                <FormItem label=\"首单优惠：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"loginForm.first_order_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    新用户下单时可享折扣，折扣仅对商品打折，运费无折扣\n                  </div>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"\n                  loginForm.newcomer_status === 1 &&\n                  loginForm.first_order_status === 1\n                \"\n              >\n                <FormItem label=\"折扣力度：\">\n                  <Input\n                    v-model=\"loginForm.first_order_discount\"\n                    placeholder=\"请输入折扣力度\"\n                    class=\"inputw\"\n                  >\n                  </Input>\n                  <span class=\"span-text\">%</span>\n                  <div class=\"upload-text\">\n                    折扣力度为：0-100%，1折为10%\n                  </div>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"\n                  loginForm.newcomer_status === 1 &&\n                  loginForm.first_order_status === 1\n                \"\n              >\n                <FormItem label=\"折扣上限：\">\n                  <Input\n                    v-model=\"loginForm.first_order_discount_limit\"\n                    placeholder=\"请输入折扣上限\"\n                    class=\"inputw\"\n                  >\n                  </Input>\n                  <span class=\"span-text\">元</span>\n                  <div class=\"upload-text\">\n                    首单优惠最高金额，单位：元\n                  </div>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"loginForm.newcomer_status === 1\"\n              >\n                <FormItem label=\"新人专享价：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"loginForm.register_price_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    新用户可购买一件新人商品，购买后移动端不再显示新人专区\n                  </div>\n\n                  <!-- ----添加商品----- -->\n                  <vxe-table\n                    border=\"inner\"\n                    ref=\"xTree\"\n                    :column-config=\"{ resizable: true }\"\n                    row-id=\"id\"\n                    :tree-config=\"{ children: 'attrValue', reserve: true }\"\n                    :data=\"tableData\"\n                    @checkbox-all=\"selectAll\"\n                    @checkbox-change=\"selectAll\"\n                    class=\"goods mt10\"\n                    :header-cell-style=\"{\n                      background: '#F7F7F7',\n                      height: '40px',\n                    }\"\n                    v-if=\"loginForm.register_price_status === 1\"\n                  >\n                    <vxe-column\n                      type=\"checkbox\"\n                      title=\"多选\"\n                      width=\"90\"\n                      tree-node\n                    ></vxe-column>\n                    <vxe-column\n                      field=\"id\"\n                      title=\"ID\"\n                      min-width=\"80\"\n                    ></vxe-column>\n                    <vxe-column field=\"info\" title=\"商品信息\" min-width=\"200\">\n                      <template v-slot=\"{ row }\">\n                        <div class=\"imgPic acea-row row-middle\">\n                          <viewer>\n                            <div class=\"pictrue\">\n                              <img v-lazy=\"row.image\" />\n                            </div>\n                          </viewer>\n                          <div class=\"info\">\n                            <Tooltip\n                              max-width=\"200\"\n                              placement=\"bottom\"\n                              transfer>\n                              <span class=\"line2\">{{ row.store_name }}{{ row.suk }}</span>\n                              <p slot=\"content\">{{ row.store_name }}{{ row.suk }}</p>\n                            </Tooltip>\n                          </div>\n                        </div>\n                      </template>\n                    </vxe-column>\n\n                    <vxe-column\n                      field=\"price\"\n                      title=\"售价\"\n                      min-width=\"80\"\n                    ></vxe-column>\n\n                    <vxe-column\n                      field=\"stock\"\n                      title=\"库存\"\n                      min-width=\"80\"\n                    ></vxe-column>\n\n                    <!-- 活动价 -->\n                    <vxe-column field=\"date\" title=\"活动价\" min-width=\"200\">\n                      <template v-slot=\"{ row }\">\n                        <Input\n                          v-model=\"row.ativity_price\"\n                          :border=\"false\"\n                          placeholder=\"请输入活动价\"\n                          @on-change=\"inputChange(row)\"\n                        />\n                      </template>\n                    </vxe-column>\n                    <!-- 活动价 -->\n                    <vxe-column\n                      field=\"date\"\n                      title=\"操作\"\n                      min-width=\"50\"\n                      fixed=\"right\"\n                      align=\"center\"\n                    >\n                      <template v-slot=\"{ row }\">\n                        <a @click=\"del(row)\">删除</a>\n                      </template>\n                    </vxe-column>\n                  </vxe-table>\n                  <div\n                    class=\"add-goods\"\n                    v-if=\"loginForm.register_price_status === 1\"\n                  >\n                    <Button @click=\"addGoods\">添加商品</Button>\n                    <Button @click=\"activityShowFn\" class=\"goods-btn\">\n                      设置活动价\n                    </Button>\n                    <Button @click=\"delAll\">批量删除</Button>\n                  </div>\n                  <!-- ----添加商品----- -->\n                </FormItem>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem\n                  label=\"规则详情：\"\n                  v-if=\"loginForm.newcomer_status === 1\"\n                >\n                  <WangEditor\n                    class=\"goods\"\n                    :content=\"loginForm.newcomer_agreement\"\n                    @editorContent=\"getEditorContent\"\n                  ></WangEditor>\n                </FormItem>\n              </Col>\n              <Col>\n                <FormItem>\n                  <div\n                    class=\"subBtn\"\n                    style=\"margin-top: 0px;\"\n                    @click=\"handleSubmit('register')\"\n                  >\n                    保存\n                  </div>\n                </FormItem>\n              </Col>\n            </Row>\n          </Form>\n        </TabPane>\n        <!-- ---------------------------------会员等级-------------------------------- -->\n        <TabPane label=\"会员等级\" name=\"level\">\n          <Form :model=\"vipForm\" :label-width=\"120\">\n            <Row :gutter=\"24\" type=\"flex\">\n              <Col span=\"24\">\n                <div class=\"basics\">基础设置</div>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem label=\"会员等级启用：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"vipForm.member_func_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    开启会员等级后，可以获得经验值\n                  </div>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                v-if=\"vipForm.member_func_status === 1\"\n              >\n                <FormItem label=\"下单获得经验：\">\n                  <Input\n                    v-model=\"vipForm.order_give_exp\"\n                    placeholder=\"请输入获得经验值\"\n                    class=\"inputw\"\n                  />\n                  <div class=\"upload-text\">\n                    用户实际支付1元，可以获得多少经验值\n                  </div>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                v-if=\"vipForm.member_func_status === 1\"\n              >\n                <FormItem label=\"签到获得经验：\">\n                  <Input\n                    v-model=\"vipForm.sign_give_exp\"\n                    placeholder=\"请输入签到获得经验值\"\n                    class=\"inputw\"\n                  />\n                  <div class=\"upload-text\">用户签到一次，赠送多少经验值</div>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                v-if=\"vipForm.member_func_status === 1\"\n              >\n                <FormItem label=\"邀请新用户获得经验：\">\n                  <Input\n                    v-model=\"vipForm.invite_user_exp\"\n                    placeholder=\"请输入获取新用户获得经验值\"\n                    class=\"inputw\"\n                  />\n                  <div class=\"upload-text\">\n                    邀请一个新用户注册，赠送多少经验值\n                  </div>\n                </FormItem>\n              </Col>\n              <Col span=\"24\">\n                <div class=\"basics\">激活有礼</div>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem label=\"会员卡激活：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"vipForm.level_activate_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    开启后用户等级功能不能直接使用，需要用户填写信息，激活后才能使用用户等级\n                  </div>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"vipForm.level_activate_status === 1\"\n              >\n                <FormItem label=\"会员卡信息：\">\n                  <Table\n                    :columns=\"columns3\"\n                    :data=\"listVip\"\n                    ref=\"table\"\n                    class=\"mt10 mb10 goods\"\n                    :loading=\"loading\"\n                    highlight-row\n                    no-userFrom-text=\"暂无数据\"\n                    no-filtered-userFrom-text=\"暂无筛选结果\"\n                    v-if=\"listVip.length > 0\"\n                  >\n                    <!-- 必填 -->\n                    <template slot-scope=\"{ row, index }\" slot=\"required\">\n                      <Checkbox v-model=\"listVip[index].required\" @on-change=\"tapCheckbox\"></Checkbox>\n                    </template>\n                    <template slot-scope=\"{ row, index }\" slot=\"action\">\n                      <a @click=\"delVip(row, index)\">删除</a>\n                    </template>\n                  </Table>\n                  <Button @click=\"informationTap\">\n                    选择信息\n                  </Button>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"vipForm.level_activate_status === 1\"\n              >\n                <FormItem label=\"赠送积分：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"vipForm.level_integral_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    用户激活会员卡后即赠送一定数额的积分\n                  </div>\n                  <Input\n                    v-model=\"vipForm.level_give_integral\"\n                    placeholder=\"请输入赠送的积分\"\n                    class=\"inputw mt10\"\n                    v-if=\"vipForm.level_integral_status === 1\"\n                  ></Input>\n                  <span\n                    class=\"span-text\"\n                    v-if=\"vipForm.level_integral_status === 1\"\n                  >\n                    积分\n                  </span>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"vipForm.level_activate_status === 1\"\n              >\n                <FormItem label=\"赠送余额：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"vipForm.level_money_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    用户激活会员卡后即赠送一定数额的储值余额\n                  </div>\n                  <Input\n                    v-model=\"vipForm.level_give_money\"\n                    placeholder=\"请输入赠送的余额\"\n                    class=\"inputw mt10\"\n                    v-if=\"vipForm.level_money_status === 1\"\n                  />\n                  <span\n                    class=\"span-text\"\n                    v-if=\"vipForm.level_money_status === 1\"\n                  >\n                    元\n                  </span>\n                </FormItem>\n              </Col>\n              <Col\n                span=\"24\"\n                class=\"mt10\"\n                v-if=\"vipForm.level_activate_status === 1\"\n              >\n                <FormItem label=\"赠送优惠券：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"vipForm.level_coupon_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                  <div class=\"upload-text\">\n                    用户激活会员卡后即赠送优惠券\n                  </div>\n\n                  <div\n                    class=\"item\"\n                    v-for=\"(item, indexw) in promotionsData\"\n                    :key=\"indexw\"\n                  >\n                    <div\n                      class=\"add-coupon\"\n                      @click=\"addCoupon(indexw)\"\n                      v-if=\"vipForm.level_coupon_status === 1\"\n                    >\n                      + 添加优惠券\n                    </div>\n                    <Table\n                      border\n                      :columns=\"columns1\"\n                      :data=\"vipCopon\"\n                      ref=\"table\"\n                      class=\"table\"\n                      width=\"700\"\n                      v-if=\"\n                        vipCopon.length > 0 && vipForm.level_coupon_status === 1\n                      \"\n                    >\n                      <template slot-scope=\"{ row }\" slot=\"coupon_price\">\n                        <span v-if=\"row.coupon_type == 1\">\n                          {{ row.coupon_price }}元\n                        </span>\n                        <span v-if=\"row.coupon_type == 2\">\n                          {{ parseFloat(row.coupon_price) / 10 }}折（{{\n                            row.coupon_price.toString().split('.')[0]\n                          }}%）\n                        </span>\n                      </template>\n                      <template slot-scope=\"{ row }\" slot=\"coupon_type\">\n                        <span v-if=\"row.coupon_type === 1\">满减券</span>\n                        <span v-else>折扣券</span>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"status\">\n                        <a @click=\"delCoupon(index, indexw)\">删除</a>\n                      </template>\n                    </Table>\n                  </div>\n                </FormItem>\n              </Col>\n              <Col>\n                <FormItem>\n                  <div class=\"subBtn mt10\" @click=\"handleSubmit('level')\">\n                    保存\n                  </div>\n                </FormItem>\n              </Col>\n            </Row>\n          </Form>\n        </TabPane>\n        <!-- ---------------------------------付费会员-------------------------------- -->\n        <TabPane label=\"付费会员\" name=\"svip\">\n          <Form :model=\"basicsForm\" :label-width=\"120\">\n            <Row :gutter=\"24\" type=\"flex\">\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem label=\"付费会员启用：\">\n                  <i-switch\n                    size=\"large\"\n                    v-model=\"member_card_status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                </FormItem>\n                <FormItem label=\"付费会员价展示：\" v-if=\"member_card_status == 1\">\n                  <i-switch\n                      size=\"large\"\n                      v-model=\"svip_price_status\"\n                      :true-value=\"1\"\n                      :false-value=\"0\"\n                  >\n                    <span slot=\"open\">开启</span>\n                    <span slot=\"close\">关闭</span>\n                  </i-switch>\n                </FormItem>\n              </Col>\n              <Col span=\"24\" class=\"mt10\">\n                <FormItem>\n                  <div\n                    style=\"margin-top: 0px;\"\n                    class=\"subBtn\"\n                    @click=\"handleSubmit('svip')\"\n                  >\n                    保存\n                  </div>\n                </FormItem>\n              </Col>\n            </Row>\n          </Form>\n        </TabPane>\n      </Tabs>\n    </Card>\n    <!-- 选择图片弹窗 -->\n    <Modal\n      v-model=\"modalPic\"\n      width=\"960px\"\n      scrollable\n      footer-hide\n      closable\n      title=\"上传用户图片\"\n      :mask-closable=\"false\"\n      :z-index=\"1\"\n    >\n      <uploadPictures\n        :isChoice=\"isChoice\"\n        @getPic=\"getPic\"\n        :gridBtn=\"gridBtn\"\n        :gridPic=\"gridPic\"\n        v-if=\"modalPic\"\n      ></uploadPictures>\n    </Modal>\n    <!-- 新增信息 -->\n    <Modal\n      v-model=\"addModel\"\n      title=\"新增信息\"\n      class-name=\"vertical-center-modal\"\n      scrollable\n      @on-cancel=\"cancelSubmit\"\n    >\n      <Form\n        ref=\"formValidate\"\n        :model=\"formItem\"\n        :rules=\"ruleValidate\"\n        :label-width=\"90\"\n      >\n        <Row>\n          <Col>\n            <FormItem label=\"信息名称：\" prop=\"info\">\n              <Input\n                v-model=\"formItem.info\"\n                placeholder=\"请输入信息名称\"\n                style=\"width: 300px;\"\n              />\n            </FormItem>\n          </Col>\n          <Col>\n            <FormItem label=\"信息格式 ：\" prop=\"format\">\n              <Select v-model=\"formItem.format\" style=\"width: 300px;\">\n                <Option\n                  v-for=\"item in formatList\"\n                  :value=\"item.value\"\n                  :key=\"item.value\"\n                >\n                  {{ item.label }}\n                </Option>\n              </Select>\n            </FormItem>\n          </Col>\n          <Col>\n            <FormItem\n              label=\"单选项 ：\"\n              prop=\"singlearr\"\n              v-if=\"formItem.format === 'radio'\"\n            >\n              <div class=\"arrbox\">\n                <Tag\n                  @on-close=\"handleClose\"\n                  :name=\"item\"\n                  :closable=\"true\"\n                  v-for=\"(item, index) in formItem.singlearr\"\n                  :key=\"index\"\n                >\n                  {{ item }}\n                </Tag>\n                <input\n                  class=\"arrbox_ip percentage9\"\n                  v-model=\"formItem.single\"\n                  placeholder=\"请输入选项，回车确认\"\n                  @keyup.enter=\"addlabel\"\n                />\n              </div>\n            </FormItem>\n          </Col>\n          <Col>\n            <FormItem label=\"提示文案：\" prop=\"tip\">\n              <Input\n                v-model=\"formItem.tip\"\n                placeholder=\"请输入提示文案\"\n                style=\"width: 300px;\"\n              />\n            </FormItem>\n          </Col>\n        </Row>\n      </Form>\n\n      <div slot=\"footer\" class=\"acea-row row-right\">\n        <Button @click=\"cancelSubmit\">取消</Button>\n        <Button type=\"primary\" @click=\"addSubmit\">提交</Button>\n      </div>\n    </Modal>\n    <!-- 添加优惠券 -->\n    <coupon-list\n      ref=\"couponTemplates\"\n      @getCouponList=\"getCouponList\"\n      :discount=\"true\"\n    ></coupon-list>\n    <!-- 添加商品 -->\n    <Modal\n      v-model=\"modals\"\n      title=\"商品列表\"\n      footerHide\n      class=\"paymentFooter\"\n      scrollable\n      width=\"900\"\n    >\n      <goods-list\n        ref=\"goodslist\"\n        :ischeckbox=\"true\"\n        :isdiy=\"true\"\n        @getProductId=\"getProductId\"\n        v-if=\"modals\"\n      ></goods-list>\n    </Modal>\n    <!-- 选择信息 -->\n\n    <information\n      ref=\"information\"\n      @getInfoList=\"getInfoList\"\n      :listOne=\"listOne\"\n    ></information>\n\n    <!-- 设置活动价 -->\n    <Modal\n      v-model=\"activityShow\"\n      title=\"设置\"\n      class=\"paymentFooter\"\n      width=\"600\"\n      :closable=\"false\"\n      :mask-closable=\"false\"\n      footer-hide\n    >\n      <Form :model=\"formActive\" :rules=\"ruleActive\" ref=\"activityShow\" :label-width=\"100\">\n        <FormItem label=\"设置活动价：\" prop=\"activeInput\">\n          <InputNumber\n            v-model=\"formActive.activeInput\"\n            placeholder=\"请输入活动价格\"\n            class=\"inputw\"\n            :min=\"0\"\n          >\n          </InputNumber>\n        </FormItem>\n        <div class=\"acea-row row-right\">\n          <Button @click=\"cancel('activityShow')\">取消</Button>\n          <Button class=\"ml15 mr5\" type=\"primary\" @click=\"ok('activityShow')\">提交</Button>\n        </div>\n      </Form>\n    </Modal>\n  </div>\n", null]}