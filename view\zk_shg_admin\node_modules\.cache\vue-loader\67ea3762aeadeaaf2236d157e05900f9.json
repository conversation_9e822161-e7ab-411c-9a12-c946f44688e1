{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState, mapMutations } from 'vuex';\nimport { taskListApi, setTaskShowApi, setTaskMustApi, createTaskApi } from '@/api/user';\nimport editFrom from '@/components/from/from';\nexport default {\n    name: 'task',\n    components: { editFrom },\n    data () {\n        return {\n            // levelIds: this.levelId,\n            grid: {\n                xl: 10,\n                lg: 10,\n                md: 12,\n                sm: 24,\n                xs: 24\n            },\n            modals: false,\n            levelFrom: {\n                is_show: '',\n                name: '',\n                page: 1,\n                limit: 20\n            },\n            total: 0,\n            levelLists: [],\n            loading: false,\n            columns1: [\n                {\n                    title: 'ID',\n                    key: 'id',\n                    sortable: true,\n                    width: 80\n                },\n                {\n                    title: '等级名称',\n                    key: 'level_name',\n                    minWidth: 100\n                },\n                {\n                    title: '任务名称',\n                    key: 'name',\n                    minWidth: 120\n                },\n                {\n                    title: '排序',\n                    sort: 'grade',\n                    sortable: true,\n                    minWidth: 100\n                },\n                {\n                    title: '是否显示',\n                    slot: 'is_shows',\n                    minWidth: 110\n                },\n                {\n                    title: '务必达成',\n                    slot: 'is_musts',\n                    minWidth: 135\n                },\n                {\n                    title: '任务说明',\n                    key: 'illustrate',\n                    minWidth: 120\n                },\n                {\n                    title: '操作',\n                    slot: 'action',\n                    fixed: 'right',\n                    minWidth: 120\n                }\n            ],\n            FromData: null,\n            ids: 0,\n            modalTitleSs: '',\n            titleType: 'task'\n        }\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        ...mapState('admin/userLevel', [\n            'levelId'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 75;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'right';\n        }\n    },\n    methods: {\n        ...mapMutations('admin/userLevel', [\n            'getTaskId',\n            'getlevelId'\n        ]),\n        // 添加\n        add () {\n            this.ids = '';\n            this.getFrom();\n        },\n        // 新建 编辑表单\n        getFrom () {\n            let data = {\n                id: this.ids,\n                level_id: this.levelId\n            };\n            this.$modalForm(createTaskApi(data)).then(\n                () => this.getList()\n            );\n        },\n        // 编辑\n        edit (row) {\n            this.ids = row.id;\n            this.getFrom()\n        },\n        // 关闭模态框\n        handleReset () {\n            this.modals = false;\n        },\n        // 表格搜索\n        userSearchs () {\n            this.getList();\n        },\n        // 任务列表\n        getList () {\n            this.loading = true;\n            this.levelFrom.is_show = this.levelFrom.is_show || '';\n            taskListApi(this.levelId, this.levelFrom).then(async res => {\n                let data = res.data\n                this.levelLists = data.list;\n                this.total = res.data.count;\n                this.loading = false;\n            }).catch(res => {\n                this.loading = false;\n                this.$Message.error(res.msg);\n            })\n        },\n        pageChange (index) {\n            this.levelFrom.page = index;\n            this.getList();\n        },\n        // 修改显示隐藏\n        onchangeIsShow (row) {\n            let data = {\n                id: row.id,\n                is_show: row.is_show\n            }\n            setTaskShowApi(data).then(async res => {\n                this.$Message.success(res.msg);\n            }).catch(res => {\n                this.$Message.error(res.msg);\n            })\n        },\n        // 设置任务是否达成\n        onchangeIsMust (row) {\n            let data = {\n                id: row.id,\n                is_must: row.is_must\n            }\n            setTaskMustApi(data).then(async res => {\n                this.$Message.success(res.msg);\n            }).catch(res => {\n                this.$Message.error(res.msg);\n            })\n        },\n        // 新建编辑提交成功\n        submitFail () {\n            this.getList();\n        },\n        // 删除任务\n        del (row, tit, num) {\n            let delfromData = {\n                title: tit,\n                num: num,\n                url: `user/user_level/delete_task/${row.id}`,\n                method: 'DELETE',\n                ids: ''\n            };\n            this.$modalSure(delfromData).then((res) => {\n                this.$Message.success(res.msg);\n                this.levelLists.splice(num, 1);\n            }).catch(res => {\n                this.$Message.error(res.msg);\n            });\n        }\n    }\n}\n", null]}