{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue?vue&type=template&id=4c7f6892&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"c_product\" v-if=\"configData\">\n        <div class=\"title\" v-if=\"configData.title\">{{configData.title}}</div>\n        <div class=\"list-box\">\n            <draggable\n                    class=\"dragArea list-group\"\n                    :list=\"configData.list\"\n                    group=\"peoples\"\n                    handle=\".move-icon\"\n            >\n                <div class=\"item\" v-for=\"(item,index) in configData.list\" :key=\"index\" @click=\"activeBtn(index)\" v-model=\"configData.tabCur\">\n                    <div class=\"move-icon\">\n                        <span class=\"iconfont-diy iconxingzhuangjiehe\"></span>\n                    </div>\n                    <div class=\"content\">\n                        <div class=\"con-item\" v-for=\"(list,key) in item.chiild\" :key=\"key\">\n                            <span>{{list.title}}</span>\n                            <div style=\"width: 100%\" @click=\"getLink(index,key,item)\">\n                                <Input :icon=\"key && !item.link?'ios-arrow-forward':''\" :show-word-limit='!key?true:false' :readonly=\"key && !item.link?true:false\" v-model=\"list.val\" :placeholder=\"list.pla\" :maxlength=\"list.max\"/>\n                            </div>\n                        </div>\n\t\t\t\t\t\t<div class=\"con-item\" v-if=\"configData.type\">\n\t\t\t\t\t\t\t<span>状态</span>\n\t\t\t\t\t\t\t<i-switch v-model=\"item.show\"/>\n\t\t\t\t\t\t</div>\n                        <div class=\"con-item\" v-if=\"item.link\">\n                            <span>{{item.link.title}}</span>\n                            <Select v-model=\"item.link.activeVal\" style=\"\" @on-change=\"sliderChange(index)\">\n                                <Option v-for=\"(item,j) in item.link.optiops\" :value=\"item.value\" :key=\"j\" >{{ item.label\n                                    }}\n                                </Option>\n                            </Select>\n                        </div>\n                    </div>\n                    <div class=\"delete\" @click.stop=\"bindDelete(index)\">\n                        <Icon type=\"ios-close-circle\" size=\"26\"/>\n                    </div>\n                </div>\n            </draggable>\n        </div>\n        <div v-if=\"configData.list\">\n            <div class=\"add-btn\" @click=\"addHotTxt\" v-if=\"configData.list.length < configData.max\">\n                <Button class=\"btn\" type=\"primary\" ghost>\n\t\t\t\t\t<span class=\"iconfont iconjiahao\"></span>添加\n\t\t\t\t</Button>\n            </div>\n        </div>\n        <linkaddress ref=\"linkaddres\" @linkUrl=\"linkUrl\"></linkaddress>\n    </div>\n", null]}