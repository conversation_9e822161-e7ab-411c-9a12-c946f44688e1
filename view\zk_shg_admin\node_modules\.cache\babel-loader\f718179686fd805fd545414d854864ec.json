{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\mixins\\link.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\mixins\\link.js", "mtime": 1725352513851}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport { oneOf } from '../utils/assist';\nexport default {\n  props: {\n    to: {\n      type: [Object, String]\n    },\n    replace: {\n      type: Boolean,\n      default: false\n    },\n    target: {\n      type: String,\n      validator: function validator(value) {\n        return oneOf(value, ['_blank', '_self', '_parent', '_top']);\n      },\n      default: '_self'\n    },\n    append: {\n      type: Boolean,\n      required: false,\n      default: false\n    }\n  },\n  computed: {\n    linkUrl: function linkUrl() {\n      var type = _typeof(this.to);\n\n      if (type !== 'string') {\n        return null;\n      }\n\n      if (this.to.includes('//')) {\n        /* Absolute URL, we do not need to route this */\n        return this.to;\n      }\n\n      var router = this.$router;\n\n      if (router) {\n        var current = this.$route;\n        var route = router.resolve(this.to, current, this.append);\n        return route ? route.href : this.to;\n      }\n\n      return this.to;\n    }\n  },\n  methods: {\n    handleClick: function handleClick() {\n      var new_window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var router = this.$router;\n\n      if (new_window) {\n        var to = this.to;\n\n        if (router) {\n          var current = this.$route;\n          var route = router.resolve(this.to, current, this.append);\n          to = route ? route.href : this.to;\n        }\n\n        window.open(to);\n      } else {\n        if (router) {\n          this.replace ? this.$router.replace(this.to) : this.$router.push(this.to);\n        } else {\n          window.location.href = this.to;\n        }\n      }\n    },\n    handleCheckClick: function handleCheckClick(event) {\n      var new_window = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (this.to) {\n        if (this.target === '_blank') {\n          return false;\n        } else {\n          event.preventDefault();\n          this.handleClick(new_window);\n        }\n      }\n    }\n  }\n};", null]}