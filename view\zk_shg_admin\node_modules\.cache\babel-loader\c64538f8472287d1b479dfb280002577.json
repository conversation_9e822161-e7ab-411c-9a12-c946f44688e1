{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport linkaddress from '@/components/linkaddress';\nimport uploadPictures from '@/components/uploadPictures';\nexport default {\n  name: 'c_upload_img',\n  components: {\n    uploadPictures: uploadPictures,\n    linkaddress: linkaddress\n  },\n  computed: _objectSpread({}, mapState({\n    tabVal: function tabVal(state) {\n      return state.admin.mobildConfig.searchConfig.data.tabVal;\n    }\n  })),\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    }\n  },\n  data: function data() {\n    return {\n      defaultList: [{\n        'name': 'a42bdcc1178e62b4694c830f028db5c0',\n        'url': 'https://o5wwk8baw.qnssl.com/a42bdcc1178e62b4694c830f028db5c0/avatar'\n      }, {\n        'name': 'bc7521e033abdd1e92222d733590f104',\n        'url': 'https://o5wwk8baw.qnssl.com/bc7521e033abdd1e92222d733590f104/avatar'\n      }],\n      defaults: {},\n      configData: {},\n      modalPic: false,\n      isChoice: '单选',\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      activeIndex: 0\n    };\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  created: function created() {\n    this.defaults = this.configObj;\n    this.configData = this.configObj[this.configNme];\n  },\n  methods: {\n    linkUrl: function linkUrl(e) {\n      this.configData.link = e;\n    },\n    getLink: function getLink() {\n      this.$refs.linkaddres.modals = true;\n    },\n    bindDelete: function bindDelete() {\n      this.configData.url = '';\n    },\n    // 点击图文封面\n    modalPicTap: function modalPicTap(title) {\n      this.modalPic = true;\n    },\n    // 添加自定义弹窗\n    addCustomDialog: function addCustomDialog(editorId) {\n      window.UE.registerUI('test-dialog', function (editor, uiName) {\n        var dialog = new window.UE.ui.Dialog({\n          iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n          editor: editor,\n          name: uiName,\n          title: '上传图片',\n          cssRules: 'width:1200px;height:500px;padding:20px;'\n        });\n        this.dialog = dialog; // 参考上面的自定义按钮\n\n        var btn = new window.UE.ui.Button({\n          name: 'dialog-button',\n          title: '上传图片',\n          cssRules: \"background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;\",\n          onclick: function onclick() {\n            // 渲染dialog\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 37);\n    },\n    // 获取图片信息\n    getPic: function getPic(pc) {\n      var _this = this;\n\n      this.$nextTick(function () {\n        _this.configData.url = pc.att_dir;\n        _this.modalPic = false;\n      });\n    }\n  }\n};", null]}