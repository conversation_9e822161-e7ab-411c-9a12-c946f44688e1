{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue?vue&type=style&index=0&id=4c2bb400&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/deep/ .ivu-divider-horizontal {\n  margin: 12px 0;\n}\n\nimg {\n  object-fit: cover;\n  width: 100%;\n  height: 100%;\n}\n\n.empty-box {\n  color: #8c8c8c;\n  font-size: 12px;\n  border-radius: 0;\n  background-color: #eee;\n  width: 100%;\n  border: 1px solid #ddd;\n  text-align: center;\n}\n\n.mobile-page {\n  .tip {\n    font-size: 12px;\n    color: rgba(0, 0, 0, 0.45);\n    margin-bottom: 2px;\n  }\n\n  .advert {\n    cursor: pointer;\n    padding: 0 15px 20px 15px;\n\n    .advertItem07 {\n      height: 185px;\n\n      &.on {\n        img {\n          border: 1px solid #1890ff !important;\n        }\n\n        .empty-box {\n          border: 1px solid #1890ff !important;\n          color: #1890ff;\n        }\n      }\n    }\n\n    .advertItem08 {\n      .item {\n        height: 185px;\n\n        .pic {\n          width: 50%;\n\t\t  height: 100%;\n\n          &.on {\n            img {\n              border: 1px solid #1890ff !important;\n            }\n\n            .empty-box {\n              border: 1px solid #1890ff !important;\n              color: #1890ff;\n            }\n          }\n        }\n\n        &.on {\n          img {\n            border: 1px solid #1890ff !important;\n          }\n\n          .empty-box {\n            border: 1px solid #1890ff !important;\n            color: #1890ff;\n          }\n        }\n      }\n\n      .items {\n        .pic {\n          width: 33.33333%;\n          height: 185px;\n\n          &.on {\n            img {\n              border: 1px solid #1890ff !important;\n            }\n\n            .empty-box {\n              border: 1px solid #1890ff !important;\n              color: #1890ff;\n            }\n          }\n        }\n      }\n    }\n\n    .advertItem01 {\n      width: 100%;\n      height: 100%;\n\n      .empty-box {\n        height: 366px;\n        border: 1px solid #ddd;\n      }\n\n      img {\n        width: 100%;\n        height: 100%;\n        border: 1px solid #1890ff;\n      }\n    }\n\n    .advertItem02 {\n      width: 370px;\n      height: 370px;\n\n      .item {\n        width: 50%;\n        height: 370px;\n\n        img {\n          width: 100%;\n          height: 100%;\n        }\n\n        &.on {\n          img {\n            border: 1px solid #1890ff !important;\n          }\n\n          .empty-box {\n            border: 1px solid #1890ff !important;\n            color: #1890ff;\n          }\n        }\n\n        .empty-box {\n          border-right: 1px solid #eee;\n        }\n\n        &:nth-child(2) {\n          .empty-box {\n            border-right: 1px solid #ddd;\n          }\n        }\n      }\n    }\n\n    .advertItem03 {\n      .item {\n        width: 33.3333%;\n        height: 370px;\n\n        &.on {\n          img {\n            border: 1px solid #1890ff !important;\n          }\n\n          .empty-box {\n            border: 1px solid #1890ff !important;\n            color: #1890ff;\n          }\n        }\n\n        .empty-box {\n          border-right: 1px solid #eee;\n        }\n\n        &:nth-child(2) {\n          .empty-box {\n            border-right: 1px solid #eee;\n          }\n        }\n\n        &:nth-child(3) {\n          .empty-box {\n            border-right: 1px solid #ddd;\n          }\n        }\n      }\n    }\n\n    .advertItem04 {\n      .item {\n        width: 50%;\n        height: 370px;\n\n        .empty-box {\n          height: 100%;\n        }\n\n        img {\n          width: 100%;\n          height: 100%;\n        }\n\n        &.on {\n          img {\n            border: 1px solid #1890ff !important;\n          }\n\n          .empty-box {\n            border: 1px solid #1890ff !important;\n            color: #1890ff;\n          }\n        }\n\n        .pic {\n          width: 100%;\n          height: 185px;\n\n          .picItem {\n            width: 50%;\n\t\t\theight: 100%;\n\n            &.on {\n              img {\n                border: 1px solid #1890ff !important;\n              }\n\n              .empty-box {\n                border: 1px solid #1890ff !important;\n                color: #1890ff;\n              }\n            }\n          }\n\n          &.on {\n            img {\n              border: 1px solid #1890ff !important;\n            }\n\n            .empty-box {\n              border: 1px solid #1890ff !important;\n              color: #1890ff;\n            }\n          }\n        }\n\n        &:nth-child(1) {\n          .empty-box {\n            border-right: 1px solid #eee;\n          }\n        }\n\n        &:nth-child(2) {\n          .pic {\n            &:nth-child(2) {\n              .empty-box {\n                border-top: 1px solid #eee;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .advertItem05 {\n      .item {\n        width: 25%;\n\n        &.on {\n          img {\n            border: 1px solid #1890ff !important;\n          }\n\n          .empty-box {\n            border: 1px solid #1890ff !important;\n            color: #1890ff;\n          }\n        }\n\n        .empty-box {\n          height: 94.75px;\n        }\n\n        &:nth-child(4) {\n          .empty-box {\n            border-right: 1px solid #ddd;\n          }\n        }\n\n        &:nth-child(2) {\n          .empty-box {\n            border-right: 1px solid #eee;\n          }\n        }\n      }\n    }\n\n    .advertItem06 {\n      .item {\n        width: 50%;\n        height: 185px;\n\n        img {\n          width: 100%;\n          height: 100%;\n        }\n\n        &.on {\n          img {\n            border: 1px solid #1890ff !important;\n          }\n\n          .empty-box {\n            border: 1px solid #1890ff !important;\n            color: #1890ff;\n          }\n        }\n\n        .empty-box {\n          height: 100%;\n          border-right: 1px solid #eee;\n          border-bottom: 1px solid #eee;\n        }\n\n        &:nth-child(2) {\n          .empty-box {\n            border-right: 1px solid #ddd;\n          }\n        }\n\n        &:nth-child(3) {\n          .empty-box {\n            border-bottom: 1px solid #ddd;\n          }\n        }\n\n        &:nth-child(4) {\n          .empty-box {\n            border-right: 1px solid #ddd;\n            border-bottom: 1px solid #ddd;\n          }\n        }\n      }\n    }\n\n    .advertItem11 {\n      width: 375px;\n      height: 375px;\n      visibility: visible;\n      position: relative;\n\n      .seled {\n        border: 1px solid red;\n        background-color: #D6DFF7;\n      }\n\n      .lay-item {\n        width: 93.75px;\n        height: 93.75px;\n\n        .empty-box {\n          height: 100%;\n        }\n\n        img {\n          width: 100%;\n          height: 100%;\n        }\n\n        &.on {\n          img {\n            border: 1px solid #1890ff !important;\n          }\n\n          .empty-box {\n            border: 1px solid #1890ff !important;\n            color: #1890ff;\n          }\n        }\n\n        .pic {\n          width: 100%;\n          height: 100%;\n\n          .picItem {\n            width: 50%;\n\n            &.on {\n              img {\n                border: 1px solid #1890ff !important;\n              }\n\n              .empty-box {\n                border: 1px solid #1890ff !important;\n                color: #1890ff;\n              }\n            }\n          }\n\n          &.on {\n            img {\n              border: 1px solid #1890ff !important;\n            }\n\n            .empty-box {\n              border: 1px solid #1890ff !important;\n              color: #1890ff;\n            }\n          }\n        }\n\n        .empty-box {\n          border-right: 1px solid #ddd;\n        }\n\n        .empty-box {\n          border-top: 0px solid #ddd;\n        }\n      }\n    }\n\n    .pic-box {\n      position: relative;\n    }\n\n    .areaBox.active {\n      border: 1px solid #1890FF;\n    }\n\n    .areaBox {\n      position: absolute;\n      background: #eee;\n      border: 1px solid #DDDDDD;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      color: #1890FF;\n      font-size: 12px;\n      cursor: pointer;\n      z-index: 11;\n\n      .prompt-text {\n        overflow: hidden;\n        display: flex;\n        flex-wrap: wrap;\n        justify-content: center;\n        max-width: 100%;\n        max-height: 100%;\n        text-align: center;\n        align-items: center;\n        color: #1890FF;\n      }\n\n      .del {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 16px;\n        height: 16px;\n        line-height: 16px;\n        font-size: 12px;\n        background: #1890FF;\n        color: #fff;\n        text-align: center;\n        border-radius: 0 0 0 3px;\n        position: absolute;\n        right: 7px;\n        top: 7px;\n        transform: translate3d(50%, -50%, 0);\n        cursor: default;\n      }\n\n      .del:hover {\n        width: 16px;\n        height: 16px;\n        line-height: 16px;\n      }\n    }\n  }\n}\n", null]}