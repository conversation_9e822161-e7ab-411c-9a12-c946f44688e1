{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\create.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\create.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport goodsList from '@/components/goodsList/index';\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nimport uploadPictures from '@/components/uploadPictures';\nimport freightTemplate from \"@/components/freightTemplate\";\nimport { combinationInfoApi, combinationCreatApi, productAttrsApi } from '@/api/marketing';\nimport { productGetTemplateApi, productAllUnit, productUnitCreate } from '@/api/product';\nimport Setting from \"@/setting\";\nexport default {\n  name: 'storeCombinationCreate',\n  components: {\n    goodsList: goodsList,\n    uploadPictures: uploadPictures,\n    WangEditor: WangEditor,\n    freightTemplate: freightTemplate\n  },\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      template: false,\n      submitOpen: false,\n      spinShow: false,\n      isChoice: '',\n      current: 0,\n      modalPic: false,\n      grid: {\n        xl: 12,\n        lg: 20,\n        md: 24,\n        sm: 24,\n        xs: 24\n      },\n      grid2: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      modals: false,\n      modal_loading: false,\n      images: [],\n      templateList: [],\n      columns: [],\n      specsData: [],\n      picTit: '',\n      tableIndex: 0,\n      unitNameList: [],\n      formValidate: {\n        is_support_refund: 0,\n        product_type: 0,\n        freight: 1,\n        //运费设置\n        delivery_type: [],\n        images: [],\n        info: '',\n        title: '',\n        image: '',\n        unit_name: '',\n        price: 0,\n        effective_time: 24,\n        stock: 1,\n        sales: 0,\n        sort: 0,\n        postage: 0,\n        is_postage: 0,\n        is_host: 0,\n        is_show: 0,\n        section_time: [],\n        description: '',\n        id: 0,\n        product_id: 0,\n        people: 2,\n        once_num: 1,\n        num: 1,\n        temp_id: '',\n        attrs: [],\n        items: [],\n        virtual: 100,\n        peopleNum: 0\n      },\n      description: '',\n      ruleValidate: {\n        image: [{\n          required: true,\n          message: '请选择主图',\n          trigger: 'change'\n        }],\n        images: [{\n          required: true,\n          type: 'array',\n          message: '请选择主图',\n          trigger: 'change'\n        }, {\n          type: 'array',\n          min: 1,\n          message: 'Choose two hobbies at best',\n          trigger: 'change'\n        }],\n        title: [{\n          required: true,\n          message: '请输入拼团名称',\n          trigger: 'blur'\n        }],\n        info: [{\n          required: true,\n          message: '请输入拼团简介',\n          trigger: 'blur'\n        }],\n        section_time: [{\n          required: true,\n          type: 'array',\n          message: '请选择活动时间',\n          trigger: 'change'\n        }],\n        unit_name: [{\n          required: true,\n          message: \"请输入单位\",\n          trigger: \"change\"\n        }],\n        price: [{\n          required: true,\n          type: 'number',\n          message: '请输入拼团价',\n          trigger: 'blur'\n        }],\n        cost: [{\n          required: true,\n          type: 'number',\n          message: '请输入成本价',\n          trigger: 'blur'\n        }],\n        stock: [{\n          required: true,\n          type: 'number',\n          message: '请输入库存',\n          trigger: 'blur'\n        }],\n        give_integral: [{\n          required: true,\n          type: 'number',\n          message: '请输入赠送积分',\n          trigger: 'blur'\n        }],\n        effective_time: [{\n          required: true,\n          type: 'number',\n          message: '请输入拼团时效',\n          trigger: 'blur'\n        }],\n        people: [{\n          required: true,\n          type: 'number',\n          message: '请输入拼团人数',\n          trigger: 'blur'\n        }],\n        num: [{\n          required: true,\n          type: 'number',\n          message: '请输入购买数量限制',\n          trigger: 'blur'\n        }],\n        once_num: [{\n          required: true,\n          type: 'number',\n          message: '请输入单次购买数量限制',\n          trigger: 'blur'\n        }],\n        virtual: [{\n          required: true,\n          type: 'number',\n          message: '请输入虚拟成团比例',\n          trigger: 'blur'\n        }]\n      },\n      copy: 0\n    };\n  },\n  watch: {\n    'formValidate.peopleNum': function formValidatePeopleNum(n) {}\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile', 'menuCollapse']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 155;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  mounted: function mounted() {\n    if (this.$route.params.id) {\n      this.copy = this.$route.params.copy;\n      this.current = 1;\n      this.getInfo();\n    }\n\n    this.getAllUnit();\n  },\n  methods: {\n    changeTemplate: function changeTemplate(msg) {\n      this.template = msg;\n    },\n    // 添加运费模板\n    addTemp: function addTemp() {\n      this.$refs.templates.isTemplate = true;\n    },\n    addUnit: function addUnit() {\n      var _this = this;\n\n      this.$modalForm(productUnitCreate()).then(function () {\n        return _this.getAllUnit();\n      });\n    },\n    getAllUnit: function getAllUnit() {\n      var _this2 = this;\n\n      productAllUnit().then(function (res) {\n        _this2.unitNameList = res.data;\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    getEditorContent: function getEditorContent(data) {\n      this.description = data;\n    },\n    peopleChange: function peopleChange(n) {\n      if (n < 2) {\n        this.formValidate.people = 2;\n        this.$set(this.formValidate, 'people', 2);\n      }\n    },\n    peopleNumchange: function peopleNumchange(n) {\n      if (n != 0) {\n        this.formValidate.virtual = Math.floor((this.formValidate.people - n) / this.formValidate.people * 100);\n      }\n    },\n    // 拼团规格；\n    productAttrs: function productAttrs(row) {\n      var that = this;\n      productAttrsApi(row.id, 3).then(function (res) {\n        var data = res.data.info;\n        var selection = {\n          type: 'selection',\n          width: 60,\n          align: 'center'\n        };\n        that.specsData = data.attrs;\n        that.specsData.forEach(function (item, index) {\n          that.$set(that.specsData[index], 'id', index);\n        });\n        that.formValidate.items = data.items;\n        that.columns = data.header;\n        that.columns.unshift(selection);\n        that.inputChange(data);\n      }).catch(function (res) {\n        that.$Message.error(res.msg);\n      });\n    },\n    inputChange: function inputChange(data) {\n      var that = this;\n      var $index = [];\n      data.header.forEach(function (item, index) {\n        if (item.type === 1) {\n          $index.push({\n            index: index,\n            key: item.key,\n            title: item.title\n          });\n        }\n      });\n      $index.forEach(function (item, index) {\n        var title = item.title;\n        var key = item.key;\n        var row = {\n          title: title,\n          key: key,\n          align: 'center',\n          minWidth: 100,\n          render: function render(h, params) {\n            return h('div', [h('InputNumber', {\n              props: {\n                min: 0,\n                value: key === 'price' ? params.row.price : params.row.quota\n              },\n              on: {\n                'on-change': function onChange(e) {\n                  key === 'price' ? params.row.price = e : params.row.quota = e;\n                  that.specsData[params.index] = params.row;\n\n                  if (!!that.formValidate.attrs && that.formValidate.attrs.length) {\n                    that.formValidate.attrs.forEach(function (v, index) {\n                      if (v.id === params.row.id) {\n                        that.formValidate.attrs.splice(index, 1, params.row);\n                      }\n                    });\n                  }\n                }\n              }\n            })]);\n          }\n        };\n        that.columns.splice(item.index, 1, row);\n      });\n    },\n    // 多选\n    changeCheckbox: function changeCheckbox(selection) {\n      this.formValidate.attrs = selection;\n    },\n    // 获取运费模板；\n    productGetTemplate: function productGetTemplate(id) {\n      var _this3 = this;\n\n      productGetTemplateApi({\n        id: id\n      }).then(function (res) {\n        _this3.templateList = res.data;\n      });\n    },\n    // 表单验证\n    validate: function validate(prop, status, error) {\n      if (status === false) {\n        this.$Message.error(error);\n      }\n    },\n    // 商品id\n    getProductId: function getProductId(row) {\n      var _this4 = this;\n\n      this.modal_loading = false;\n      this.modals = false;\n      setTimeout(function () {\n        _this4.formValidate = {\n          is_support_refund: row.is_support_refund,\n          product_type: row.product_type,\n          images: row.slider_image,\n          info: row.store_info,\n          title: row.store_name,\n          image: row.image,\n          unit_name: row.unit_name,\n          price: 0,\n          // 不取商品中的原价\n          effective_time: 24,\n          stock: row.stock,\n          sales: row.sales,\n          sort: row.sort,\n          postage: row.postage,\n          is_postage: row.is_postage,\n          is_host: row.is_hot,\n          is_show: 0,\n          section_time: [],\n          description: row.description,\n          // 不取商品中的\n          id: 0,\n          people: 2,\n          num: 1,\n          once_num: 1,\n          product_id: row.id,\n          temp_id: row.temp_id,\n          virtual: 100,\n          peopleNum: 0,\n          freight: 1,\n          //运费设置\n          delivery_type: []\n        };\n\n        _this4.productGetTemplate(row.id);\n\n        _this4.productAttrs(row);\n      }, 500);\n    },\n    cancel: function cancel() {\n      this.modals = false;\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.formValidate.section_time = e;\n    },\n    // 详情\n    getInfo: function getInfo() {\n      var _this5 = this;\n\n      this.spinShow = true;\n      combinationInfoApi(this.$route.params.id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var that, info, selection, data, attr, index;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  that = _this5;\n                  info = res.data.info;\n                  selection = {\n                    type: 'selection',\n                    width: 60,\n                    align: 'center'\n                  };\n                  _this5.formValidate = info;\n                  _this5.description = info.description;\n\n                  if (parseInt(_this5.formValidate.virtual) !== 100) {\n                    _this5.formValidate.peopleNum = Math.floor(_this5.formValidate.people - _this5.formValidate.virtual / 100 * _this5.formValidate.people);\n                  } else {\n                    _this5.formValidate.peopleNum = 0;\n                  }\n\n                  _this5.$set(_this5.formValidate, 'items', info.attrs.items);\n\n                  _this5.columns = info.attrs.header;\n\n                  _this5.columns.unshift(selection);\n\n                  _this5.specsData = info.attrs.value;\n                  that.specsData.forEach(function (item, index) {\n                    that.$set(that.specsData[index], 'id', index);\n                  });\n                  data = info.attrs;\n                  attr = [];\n\n                  for (index in info.attrs.value) {\n                    if (info.attrs.value[index]._checked) {\n                      attr.push(info.attrs.value[index]);\n                    }\n                  }\n\n                  that.formValidate.attrs = attr;\n                  that.inputChange(data);\n                  _this5.spinShow = false;\n\n                case 17:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this5.spinShow = false;\n\n        _this5.$Message.error(res.msg);\n      });\n    },\n    // 下一步\n    next: function next(name) {\n      var _this6 = this;\n\n      var that = this;\n\n      if (this.current === 2) {\n        this.formValidate.description = this.description;\n        this.$refs[name].validate(function (valid) {\n          if (valid) {\n            if (_this6.copy == 1) _this6.formValidate.copy = 1;\n            _this6.formValidate.id = Number(_this6.$route.params.id) || 0;\n            _this6.submitOpen = true;\n\n            if (!_this6.formValidate.product_type) {\n              _this6.formValidate.is_support_refund = 1;\n            }\n\n            combinationCreatApi(_this6.formValidate).then(\n            /*#__PURE__*/\n            function () {\n              var _ref2 = _asyncToGenerator(\n              /*#__PURE__*/\n              _regeneratorRuntime.mark(function _callee2(res) {\n                return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n                  while (1) {\n                    switch (_context2.prev = _context2.next) {\n                      case 0:\n                        _this6.submitOpen = false;\n\n                        _this6.$Message.success(res.msg);\n\n                        setTimeout(function () {\n                          _this6.$router.push({\n                            path: \"\".concat(_this6.roterPre, \"/marketing/store_combination/index\")\n                          });\n                        }, 500);\n\n                      case 3:\n                      case \"end\":\n                        return _context2.stop();\n                    }\n                  }\n                }, _callee2);\n              }));\n\n              return function (_x2) {\n                return _ref2.apply(this, arguments);\n              };\n            }()).catch(function (res) {\n              _this6.submitOpen = false;\n\n              _this6.$Message.error(res.msg);\n            });\n          } else {\n            return false;\n          }\n        });\n      } else if (this.current === 1) {\n        this.$refs[name].validate(function (valid) {\n          if (valid) {\n            if (that.formValidate.people < 2) {\n              return that.$Message.error('拼团人数必须大于2');\n            }\n\n            if (that.formValidate.num < 0) {\n              return that.$Message.error('购买数量限制必须大于0');\n            }\n\n            if (that.formValidate.once_num < 0) {\n              return that.$Message.error('单次购买数量限制必须大于0');\n            }\n\n            if (!that.formValidate.attrs) {\n              return that.$Message.error('请选择属性规格');\n            } else {\n              for (var index in that.formValidate.attrs) {\n                if (that.formValidate.attrs[index].quota <= 0) {\n                  return that.$Message.error('拼团限量必须大于0');\n                }\n              }\n            }\n\n            if (_this6.formValidate.product_type == 0 && !_this6.formValidate.delivery_type.length) {\n              return _this6.$Message.warning(\"请选择配送方式\");\n            }\n\n            if (_this6.formValidate.product_type == 0 && _this6.formValidate.freight == 2 && _this6.formValidate.postage <= 0) {\n              return _this6.$Message.warning(\"物流设置-固定邮费不能为0\");\n            }\n\n            if (_this6.formValidate.product_type == 0 && _this6.formValidate.freight == 3 && !_this6.formValidate.temp_id) {\n              return _this6.$Message.warning(\"物流设置-运费模板不能为空\");\n            }\n\n            _this6.current += 1;\n          } else {\n            return _this6.$Message.warning('请完善商品信息');\n          }\n        });\n      } else {\n        if (this.formValidate.image) {\n          this.current += 1;\n        } else {\n          this.$Message.warning('请选择商品');\n        }\n      }\n    },\n    // 上一步\n    step: function step() {\n      this.current--;\n    },\n    // 内容\n    getContent: function getContent(val) {\n      this.formValidate.description = val;\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, picTit, index) {\n      this.modalPic = true;\n      this.isChoice = tit === 'dan' ? '单选' : '多选';\n      this.picTit = picTit;\n      this.tableIndex = index;\n    },\n    // 获取单张图片信息\n    getPic: function getPic(pc) {\n      switch (this.picTit) {\n        case 'danFrom':\n          this.formValidate.image = pc.att_dir;\n          break;\n\n        default:\n          if (!!this.formValidate.attrs && this.formValidate.attrs.length) {\n            this.$set(this.specsData[this.tableIndex], '_checked', true);\n          }\n\n          this.specsData[this.tableIndex].pic = pc.att_dir;\n      }\n\n      this.modalPic = false;\n    },\n    // 获取多张图信息\n    getPicD: function getPicD(pc) {\n      var _this7 = this;\n\n      this.images = pc;\n      this.images.map(function (item) {\n        _this7.formValidate.images.push(item.att_dir);\n\n        _this7.formValidate.images = _this7.formValidate.images.splice(0, 10);\n      });\n      this.modalPic = false;\n    },\n    handleRemove: function handleRemove(i) {\n      this.images.splice(i, 1);\n      this.formValidate.images.splice(i, 1);\n    },\n    // 选择商品\n    changeGoods: function changeGoods() {\n      this.modals = true;\n    },\n    // 移动\n    handleDragStart: function handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd: function handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    // 首先把div变成可以放置的元素，即重写dragenter/dragover\n    handleDragOver: function handleDragOver(e) {\n      e.dataTransfer.dropEffect = 'move';\n    },\n    handleDragEnter: function handleDragEnter(e, item) {\n      e.dataTransfer.effectAllowed = 'move';\n\n      if (item === this.dragging) {\n        return;\n      }\n\n      var newItems = _toConsumableArray(this.formValidate.images);\n\n      var src = newItems.indexOf(this.dragging);\n      var dst = newItems.indexOf(item);\n      newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n      this.formValidate.images = newItems;\n    },\n    // 添加自定义弹窗\n    addCustomDialog: function addCustomDialog(editorId) {\n      window.UE.registerUI('test-dialog', function (editor, uiName) {\n        // 创建 dialog\n        var dialog = new window.UE.ui.Dialog({\n          // 指定弹出层中页面的路径，这里只能支持页面，路径参考常见问题 2\n          iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n          // 需要指定当前的编辑器实例\n          editor: editor,\n          // 指定 dialog 的名字\n          name: uiName,\n          // dialog 的标题\n          title: '上传图片',\n          // 指定 dialog 的外围样式\n          cssRules: 'width:1200px;height:500px;padding:20px;'\n        });\n        this.dialog = dialog; // 参考上面的自定义按钮\n\n        var btn = new window.UE.ui.Button({\n          name: 'dialog-button',\n          title: '上传图片',\n          cssRules: \"background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;\",\n          onclick: function onclick() {\n            // 渲染dialog\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 37);\n    }\n  }\n};", null]}