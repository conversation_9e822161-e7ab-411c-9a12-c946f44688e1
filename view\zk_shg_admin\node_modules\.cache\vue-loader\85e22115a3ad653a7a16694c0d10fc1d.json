{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_txt_tab.vue?vue&type=template&id=5021fba3&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_txt_tab.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"txt_tab\" v-if=\"configData\">\n    <div class=\"c_row-item\">\n        <Col class=\"c_label\">\n            {{configData.title}}\n            <span>{{configData.list[configData.type].val}}</span>\n        </Col>\n        <Col class=\"color-box\">\n            <RadioGroup v-model=\"configData.type\" type=\"button\" @on-change=\"radioChange($event)\">\n                <Radio :label=\"key\" v-for=\"(radio,key) in configData.list\" :key=\"key\">\n                    <span class=\"iconfont-diy\" :class=\"radio.icon\" v-if=\"radio.icon\"></span>\n                    <span v-else>{{radio.val}}</span>\n                </Radio>\n            </RadioGroup>\n        </Col>\n    </div>\n</div>\n\n", null]}