{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\utils\\dom.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\utils\\dom.js", "mtime": 1725352514148}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["import Vue from 'vue';\nvar isServer = Vue.prototype.$isServer;\n/* istanbul ignore next */\n\nexport var on = function () {\n  if (!isServer && document.addEventListener) {\n    return function (element, event, handler) {\n      var useCapture = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n      if (element && event && handler) {\n        element.addEventListener(event, handler, useCapture);\n      }\n    };\n  } else {\n    return function (element, event, handler) {\n      if (element && event && handler) {\n        element.attachEvent('on' + event, handler);\n      }\n    };\n  }\n}();\n/* istanbul ignore next */\n\nexport var off = function () {\n  if (!isServer && document.removeEventListener) {\n    return function (element, event, handler) {\n      var useCapture = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n      if (element && event) {\n        element.removeEventListener(event, handler, useCapture);\n      }\n    };\n  } else {\n    return function (element, event, handler) {\n      if (element && event) {\n        element.detachEvent('on' + event, handler);\n      }\n    };\n  }\n}();", null]}