{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_brand.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_brand.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\timport { brandList } from \"@/api/product\";\n    export default {\n        name: 'c_brand',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            },\n            number: {\n                type: null\n            },\n        },\n        data () {\n            return {\n                defaults: {},\n                configData: {},\n\t\t\t\tprops: { emitPath: false, multiple: true },\n\t\t\t\tbrandData: []\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj\n                this.configData = this.configObj[this.configNme]\n\t\t\t\tthis.getBrandList();\n            })\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n                },\n                deep: true\n            }\n        },\n        methods: {\n\t\t\tsliderChange(){\n\t\t\t\tthis.$emit('getConfig',{ name: 'brands'})\n\t\t\t},\n\t\t\tgetBrandList(){\n\t\t\t  brandList().then(res=>{\n\t\t\t    this.brandData = res.data\n\t\t\t  }).catch(err=>{\n\t\t\t    this.$Message.error(err.msg);\n\t\t\t  })\n\t\t\t}\n        }\n    }\n", null]}