{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue?vue&type=template&id=07a10b5d&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue", "mtime": 1677832908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Modal',{attrs:{\"scrollable\":\"\",\"title\":_vm.title,\"class-name\":\"vertical-center-modal\",\"width\":\"950\"},on:{\"on-cancel\":_vm.onCancel},model:{value:(_vm.modal),callback:function ($$v) {_vm.modal=$$v},expression:\"modal\"}},[_c('Form',{ref:\"formDynamic\",staticClass:\"attrFrom\",attrs:{\"model\":_vm.formDynamic,\"rules\":_vm.rules,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Row',{attrs:{\"gutter\":24}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('Col',{staticClass:\"mb15\",attrs:{\"span\":\"8\"}},[_c('FormItem',{attrs:{\"label\":\"分类名称：\",\"prop\":\"rule_name\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入分类名称\",\"maxlength\":20},model:{value:(_vm.formDynamic.rule_name),callback:function ($$v) {_vm.$set(_vm.formDynamic, \"rule_name\", $$v)},expression:\"formDynamic.rule_name\"}})],1)],1)],1),_vm._l((_vm.formDynamic.spec),function(item,index){return _c('Col',{key:index,staticClass:\"noForm\",attrs:{\"span\":\"23\"}},[_c('FormItem',[_c('div',{staticClass:\"acea-row row-middle\"},[_c('span',{staticClass:\"mr5\"},[_vm._v(_vm._s(item.value))]),_c('Icon',{attrs:{\"type\":\"ios-close-circle\"},on:{\"click\":function($event){return _vm.handleRemove(index)}}})],1),_c('div',{staticClass:\"rulesBox\"},[_vm._l((item.detail),function(j,indexn){return _c('Tag',{key:indexn,attrs:{\"type\":\"dot\",\"closable\":\"\",\"color\":\"primary\",\"name\":j},on:{\"on-close\":function($event){return _vm.handleRemove2(item.detail,indexn)}}},[_vm._v(_vm._s(j))])}),_c('Input',{staticClass:\"width20\",attrs:{\"maxlength\":\"30\",\"show-word-limit\":\"\",\"search\":\"\",\"enter-button\":\"添加\",\"placeholder\":\"请输入属性名称\"},on:{\"on-search\":function($event){return _vm.createAttr(item.detail.attrsVal,index)}},model:{value:(item.detail.attrsVal),callback:function ($$v) {_vm.$set(item.detail, \"attrsVal\", $$v)},expression:\"item.detail.attrsVal\"}})],2)])],1)}),(_vm.isBtn)?_c('Col',{staticClass:\"mt10\",attrs:{\"span\":\"24\"}},[_c('Col',{staticClass:\"mt10 mr15\",attrs:{\"span\":\"8\"}},[_c('FormItem',{attrs:{\"label\":\"规格名称：\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入规格名称\",\"maxlength\":\"30\",\"show-word-limit\":\"\"},model:{value:(_vm.attrsName),callback:function ($$v) {_vm.attrsName=$$v},expression:\"attrsName\"}})],1)],1),_c('Col',{staticClass:\"mt10 mr20\",attrs:{\"span\":\"8\"}},[_c('FormItem',{attrs:{\"label\":\"规格值：\"}},[_c('Input',{attrs:{\"maxlength\":\"30\",\"show-word-limit\":\"\",\"placeholder\":\"请输入规格值\"},model:{value:(_vm.attrsVal),callback:function ($$v) {_vm.attrsVal=$$v},expression:\"attrsVal\"}})],1)],1),_c('Col',{staticClass:\"mr20\",attrs:{\"span\":\"8\"}},[_c('div',{staticClass:\"sub\"},[_c('Button',{staticClass:\"mr20\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.createAttrName}},[_vm._v(\"确定\")]),_c('Button',{on:{\"click\":_vm.offAttrName}},[_vm._v(\"取消\")])],1)])],1):_vm._e(),(_vm.spinShow)?_c('Spin',{attrs:{\"size\":\"large\",\"fix\":\"\"}}):_vm._e()],2),(!_vm.isBtn)?_c('Button',{staticClass:\"ml95 mt10\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.addBtn}},[_vm._v(\"添加新规格\")]):_vm._e()],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{on:{\"click\":_vm.cancle}},[_vm._v(\"取消\")]),_c('Button',{attrs:{\"type\":\"primary\",\"loading\":_vm.modal_loading},on:{\"click\":function($event){return _vm.handleSubmit('formDynamic')}}},[_vm._v(\"确定\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}