{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport vuedraggable from 'vuedraggable';\nimport linkaddress from '@/components/linkaddress';\nexport default {\n  name: 'c_product',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    },\n    index: {\n      type: null\n    }\n  },\n  components: {\n    linkaddress: linkaddress,\n    draggable: vuedraggable\n  },\n  data: function data() {\n    return {\n      defaults: {},\n      configData: {},\n      itemObj: {},\n      activeIndex: 0\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.defaults = _this.configObj;\n      _this.configData = _this.configObj[_this.configNme];\n    });\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n      },\n      deep: true\n    }\n  },\n  methods: {\n    linkUrl: function linkUrl(e) {\n      this.configData.list[this.activeIndex].chiild[1].val = e;\n    },\n    getLink: function getLink(index, key, item) {\n      if (!key || item.link) {\n        return;\n      }\n\n      this.activeIndex = index;\n      this.$refs.linkaddres.modals = true;\n    },\n    addHotTxt: function addHotTxt() {\n      if (this.configData.list.length == 0) {\n        var storage = window.localStorage;\n        this.itemObj = JSON.parse(storage.getItem('itemObj'));\n\n        if (this.itemObj.link) {\n          this.itemObj.link.activeVal = 0;\n        }\n\n        this.itemObj.chiild[0].val = '';\n        this.itemObj.chiild[1].val = '';\n        this.configData.list.push(this.itemObj);\n      } else {\n        var obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]));\n\n        if (obj.chiild[0].empty) {\n          obj.chiild[0].val = '';\n          obj.chiild[1].val = '';\n        }\n\n        this.configData.list.push(obj);\n      }\n    },\n    // 删除数组\n    bindDelete: function bindDelete(index) {\n      if (this.configData.list.length == 1) {\n        var itemObj = this.configData.list[0];\n        this.itemObj = itemObj;\n        var storage = window.localStorage;\n        storage.setItem('itemObj', JSON.stringify(itemObj));\n      }\n\n      this.configData.list.splice(index, 1);\n      this.configData.tabCur = 0;\n      this.$emit('getConfig', {\n        name: 'delete',\n        indexs: 0\n      });\n    },\n    sliderChange: function sliderChange(index) {\n      this.configData.tabCur = index;\n      this.$emit('getConfig', {\n        name: 'product',\n        indexs: index\n      });\n    },\n    activeBtn: function activeBtn(index) {\n      this.configData.tabCur = index;\n      this.$emit('getConfig', {\n        name: 'product',\n        indexs: index\n      });\n    }\n  }\n};", null]}