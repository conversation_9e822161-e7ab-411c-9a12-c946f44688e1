{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableFrom.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableFrom.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState, mapMutations } from \"vuex\";\nimport { integralGetOrdes } from \"@/api/marketing\";\n\nimport {\n  putWrite,\n  storeOrderApi,\n  handBatchDelivery,\n  otherBatchDelivery,\n  exportExpressList,\n  storeIntegralOrder,\n} from \"@/api/order\";\nimport autoSend from \"../handle/autoSend\";\nimport queueList from \"../handle/queueList\";\nimport Setting from \"@/setting\";\nimport util from \"@/libs/util\";\nimport QueueList from \"../handle/queueList.vue\";\nimport exportExcel from \"@/utils/newToExcel.js\";\n// import XLSX from 'xlsx';\n// const make_cols = refstr => Array(XLSX.utils.decode_range(refstr).e.c + 1).fill(0).map((x,i) => ({name:XLSX.utils.encode_col(i), key:i}));\nexport default {\n  name: \"table_from\",\n  components: {\n    autoSend,\n    queueList,\n  },\n  props: [\"formSelection\", \"autoDisabled\", \"isAll\"],\n  data() {\n    const codeNum = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error(\"请填写核销码\"));\n      }\n      // 模拟异步验证效果\n      if (!Number.isInteger(value)) {\n        callback(new Error(\"请填写12位数字\"));\n      } else {\n        // const reg = /[0-9]{12}/;\n        const reg = /\\b\\d{12}\\b/;\n        if (!reg.test(value)) {\n          callback(new Error(\"请填写12位数字\"));\n        } else {\n          callback();\n        }\n      }\n    };\n    return {\n      fromList: {\n        title: \"选择时间\",\n        custom: true,\n        fromTxt: [\n          { text: \"全部\", val: \"\" },\n          { text: \"今天\", val: \"today\" },\n          { text: \"昨天\", val: \"yesterday\" },\n          { text: \"最近7天\", val: \"lately7\" },\n          { text: \"最近30天\", val: \"lately30\" },\n          { text: \"本月\", val: \"month\" },\n          { text: \"本年\", val: \"year\" },\n        ],\n      },\n      currentTab: \"\",\n      // 搜索条件\n      orderData: {\n        status: \"\",\n        data: \"\",\n        real_name: \"\",\n        field_key: \"all\",\n        pay_type: \"\",\n      },\n      modalTitleSs: \"\",\n      statusType: \"\",\n      time: \"\",\n      value2: [],\n      isDelIdList: [],\n      modals2: false,\n      timeVal: [],\n      options: {\n        shortcuts: [\n          {\n            text: \"今天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                new Date(\n                  new Date().getFullYear(),\n                  new Date().getMonth(),\n                  new Date().getDate()\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"昨天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 1\n                  )\n                )\n              );\n              end.setTime(\n                end.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 1\n                  )\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"最近7天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 6\n                  )\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"最近30天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 29\n                  )\n                )\n              );\n              return [start, end];\n            },\n          },\n\t\t  {\n\t\t    text: \"上月\",\n\t\t    value() {\n\t\t      const end = new Date();\n\t\t      const start = new Date();\n\t\t  \tconst day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n\t\t      start.setTime(\n\t\t        start.setTime(\n\t\t          new Date(new Date().getFullYear(), new Date().getMonth()-1, 1)\n\t\t        )\n\t\t      );\n\t\t  \tend.setTime(\n\t\t  \t  end.setTime(\n\t\t  \t    new Date(new Date().getFullYear(), new Date().getMonth()-1, day)\n\t\t  \t  )\n\t\t  \t);\n\t\t      return [start, end];\n\t\t    },\n\t\t  },\n          {\n            text: \"本月\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(new Date().getFullYear(), new Date().getMonth(), 1)\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"本年\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(new Date(new Date().getFullYear(), 0, 1))\n              );\n              return [start, end];\n            },\n          },\n        ],\n      },\n      payList: [\n        { label: \"全部\", val: \"\" },\n        { label: \"微信支付\", val: \"1\" },\n        { label: \"支付宝支付\", val: \"4\" },\n        { label: \"余额支付\", val: \"2\" },\n        { label: \"线下支付\", val: \"3\" },\n      ],\n      manualModal: false,\n      uploadAction: `${Setting.apiBaseURL}/file/upload/1`,\n      uploadHeaders: {},\n      file: \"\",\n      autoModal: false,\n      isShow: false,\n      recordModal: false,\n      sendOutValue: \"\",\n      exportList: [\n        {\n          name: \"1\",\n          label: \"导出发货单\",\n        },\n        {\n          name: \"0\",\n          label: \"导出订单\",\n        },\n      ],\n      exportListOn: 0,\n      fileList: [],\n      //orderChartType: {},\n      // modal5: false,\n      // data5: [],\n      // cols5: []\n      // orderStatus: false,\n      // orderInfo:''\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    ...mapState(\"admin/integralOrder\", [\"orderChartType\",\"isDels\", \"delIdList\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n    today() {\n      const end = new Date();\n      const start = new Date();\n      var datetimeStart =\n        start.getFullYear() +\n        \"/\" +\n        (start.getMonth() + 1) +\n        \"/\" +\n        start.getDate();\n      var datetimeEnd =\n        end.getFullYear() + \"/\" + (end.getMonth() + 1) + \"/\" + end.getDate();\n      return [datetimeStart, datetimeEnd];\n    },\n  },\n  watch: {\n    $route() {\n      if (this.$route.fullPath === \"/order/list?status=1\") {\n        this.getPath();\n      }\n    },\n  },\n  created() {\n    // this.timeVal = this.today;\n    // this.orderData.data = this.timeVal.join('-');\n    if (this.$route.fullPath === \"/order/list?status=1\") {\n      this.getPath();\n    }\n    this.getToken();\n    this.$parent.$emit(\"add\");\n    // let searchData = {\n    //   status: this.orderData.status,\n    //   product_id: this.$route.query.product_id || \"\",\n    // };\n    // integralGetOrdes(searchData)\n    //   .then((res) => {\n    //     this.orderChartType = res.data;\n    //   })\n    //   .catch((res) => {});\n  },\n  methods: {\n    ...mapMutations(\"admin/integralOrder\", [\n      \"getOrderStatus\",\n      \"getOrderType\",\n      \"getOrderTime\",\n      \"getOrderNum\",\n      \"getfieldKey\",\n      \"getOrderRealName\"\n    ]),\n    getPath() {\n      this.orderData.status = this.$route.query.status.toString();\n      this.getOrderStatus(this.orderData.status);\n      this.$emit(\"getList\", 1);\n      this.$emit(\"order-data\", this.orderData);\n    },\n    // 导出\n    // exports(value) {\n    //   this.exportListOn = this.exportList.findIndex(\n    //     (item) => item.name === value\n    //   );\n    //   let formValidate = this.orderData;\n    //   let data = {\n    //     status: formValidate.status,\n    //     data: formValidate.data,\n    //     real_name: formValidate.real_name,\n    //     type: value,\n    //   };\n    //   storeOrderApi(data)\n    //     .then((res) => {\n    //       location.href = res.data[0];\n    //     })\n    //     .catch((res) => {\n    //       this.$Message.error(res.msg);\n    //     });\n    // },\n    // 数据导出；\n    async exports() {\n      let [th, filekey, data, fileName] = [[], [], [], \"\"];\n      let excelData = JSON.parse(JSON.stringify(this.orderData));\n      excelData.page = 1;\n      excelData.product_id= this.$route.query.product_id || \"\";\n      for (let i = 0; i < excelData.page + 1; i++) {\n        let lebData = await this.getExcelData(excelData);\n        if (!fileName) fileName = lebData.filename;\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) th = lebData.header;\n        if (lebData.export.length) {\n          data = data.concat(lebData.export);\n          excelData.page++;\n        }\n      }\n      exportExcel(th, filekey, fileName, data);\n    },\n    getExcelData(excelData) {\n      return new Promise((resolve, reject) => {\n        storeIntegralOrder(excelData).then((res) => {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.orderData.data = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.$store.dispatch(\"admin/integralOrder/getOrderTabs\", {\n        data: this.orderData.data,\n      });\n      this.getOrderTime(this.orderData.data);\n      this.$emit(\"getList\", 1);\n      this.$emit(\"order-data\", this.orderData);\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.$store.dispatch(\"admin/integralOrder/getOrderTabs\", { data: tab });\n      this.orderData.data = tab;\n      this.getOrderTime(this.orderData.data);\n      this.timeVal = [];\n      this.$emit(\"getList\");\n      this.$emit(\"order-data\", this.orderData);\n    },\n    // 订单选择状态\n    selectChange2(tab) {\n      this.getOrderStatus(tab);\n      this.$emit(\"getList\", 1);\n    },\n    userSearchs(type) {\n      this.getOrderType(type);\n      this.$emit(\"getList\", 1);\n    },\n    // 时间状态\n    timeChange(time) {\n      this.getOrderTime(time);\n      this.$emit(\"getList\");\n    },\n    // 订单号搜索\n    orderSearch() {\n      this.getOrderRealName(this.orderData.real_name);\n      this.getfieldKey(this.orderData.field_key);\n      this.$emit(\"getList\", 1);\n    },\n    // 点击订单类型\n    onClickTab() {\n      this.$emit(\"onChangeType\", this.currentTab);\n    },\n    // 批量删除\n    delAll() {\n      if (this.delIdList.length === 0) {\n        this.$Message.error(\"请先选择删除的订单！\");\n      } else {\n        if (this.isDels) {\n          this.delIdList.filter((item) => {\n            this.isDelIdList.push(item.id);\n          });\n          let idss = {\n            ids: this.isDelIdList,\n            all: this.isAll,\n            where: this.orderData,\n          };\n          let delfromData = {\n            title: \"删除订单\",\n            url: `/order/dels`,\n            method: \"post\",\n            ids: idss,\n          };\n          this.$modalSure(delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.tabList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n        } else {\n          const title = \"错误！\";\n          const content =\n            \"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>\";\n          this.$Modal.error({\n            title: title,\n            content: content,\n          });\n        }\n      }\n    },\n    del(name) {\n      // this.orderInfo = ''\n      this.modals2 = false;\n      this.writeOffFrom.confirm = 0;\n      this.$refs[name].resetFields();\n    },\n    handleSubmit() {\n      this.$emit(\"on-submit\", this.data);\n    },\n    // 刷新\n    Refresh() {\n      this.$emit(\"getList\");\n    },\n    //\n    handleReset() {\n      this.$refs.form.resetFields();\n      this.$emit(\"on-reset\");\n    },\n    // 上传头部token\n    getToken() {\n      this.uploadHeaders[\"Authori-zation\"] =\n        \"Bearer \" + util.cookies.get(\"token\");\n    },\n    // beforeUpload(file){\n    //     /* Boilerplate to set up FileReader */\n    // \tconst reader = new FileReader();\n    // \treader.onload = (e) => {\n    // \t\t/* Parse data */\n    // \t\tconst bstr = e.target.result;\n    // \t\tconst wb = XLSX.read(bstr, {type:'binary'});\n    // \t\t/* Get first worksheet */\n    // \t\tconst wsname = wb.SheetNames[0];\n    // \t\tconst ws = wb.Sheets[wsname];\n    // \t\t/* Convert array of arrays */\n    // \t\tconst data = XLSX.utils.sheet_to_json(ws, {header:1});\n    // \t\t/* Update state */\n    // \t\tthis.data5 = data;\n    //         this.cols5 = make_cols(ws['!ref']);\n    //         this.modal5 = true;\n    // \t};\n    // \treader.readAsBinaryString(file);\n    // },\n    // 上传成功\n    uploadSuccess(res, file, fileList) {\n      if (res.status === 200) {\n        this.$Message.success(res.msg);\n        this.file = res.data.src;\n        this.fileList = fileList;\n      } else {\n        this.$Message.error(res.msg);\n      }\n    },\n    //移除文件\n    removeFile(file, fileList) {\n      this.file = \"\";\n      this.fileList = fileList;\n    },\n    // 手动批量发货-确定\n    manualModalOk() {\n      this.$refs.upload.clearFiles();\n      handBatchDelivery({\n        file: this.file,\n      })\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.fileList = [];\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n          this.fileList = [];\n        });\n    },\n    // 手动批量发货-取消\n    manualModalCancel() {\n      this.fileList = [];\n      this.$refs.upload.clearFiles();\n    },\n    // 自动批量发货-取消\n    autoModalOk() {\n      if (this.isAll == \"全部\" || this.formSelection.length) {\n        this.$refs.send.modals = true;\n        this.$refs.send.getList();\n        this.$refs.send.getDeliveryList();\n      } else {\n        this.$Message.error(\"请选择本页订单\");\n      }\n    },\n    // 自动批量发货-取消\n    autolModalCancel() {},\n    submitFail() {\n      otherBatchDelivery();\n    },\n    queuemModal() {\n      // this.$router.push({ path: 'queue/list' });\n      this.$refs.queue.modal = true;\n    },\n    onAuto() {\n      this.$refs.sends.modals = true;\n      this.$refs.sends.getList();\n      this.$refs.sends.getDeliveryList();\n    },\n    // 下载物流公司对照表\n    getExpressList() {\n      exportExpressList()\n        .then((res) => {\n          window.open(res.data[0]);\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n  },\n};\n", null]}