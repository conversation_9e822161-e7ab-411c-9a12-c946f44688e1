{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\attrList.vue?vue&type=style&index=0&id=80e1fd3c&lang=stylus&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\attrList.vue", "mtime": 1642123010000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.label-wrapper{\n\t .list{\n\t\t display flex\n\t\t flex-wrap wrap\n\t\t .label-item{\n\t\t\t margin 10px 8px 10px 0\n\t\t\t padding: 3px 14px;\n\t\t\t background #EEEEEE\n\t\t\t color #333333\n\t\t\t border-radius 2px\n\t\t\t cursor pointer\n\t\t\t font-size 12px\n\t\t\t border 1px solid #EEEEEE\n\t\t\t &.on{\n\t\t\t\t color #1890FF\n\t\t\t\t border-color #1890FF\n\t\t\t\t background-color #fff;\n\t\t\t }\n\t\t }\n\t }\n\t .footer{\n\t\t display flex\n\t\t justify-content flex-end\n\t\t margin-top 40px\n\t\t button{\n\t\t\t  margin-left 10px\n\t\t }\n\t }\n}\n.btn{\n\twidth 60px\n\theight 24px\n}\t\n.title{\n\tfont-size 13px\n}\n.list-box{\n\toverflow-y auto\n\toverflow-x hidden\n\tmax-height 240px\n}\t\n", null]}