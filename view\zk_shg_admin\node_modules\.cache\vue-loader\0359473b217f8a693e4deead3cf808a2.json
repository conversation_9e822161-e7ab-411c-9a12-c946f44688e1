{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_1.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_1.vue", "mtime": 1735005448490}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { mapState } from 'vuex';\r\n// import theme from \"@/mixins/theme\";\r\nexport default {\r\n    name: 'home_card_1',\r\n    cname: '卡片一',\r\n    configName: 'c_home_card_1',\r\n    icon: '#iconzujian-shangpinxuanxiangka',\r\n    type: 0, // 0 基础组件 1 营销组件 2工具组件\r\n    defaultName: 'productCard1', // 外面匹配名称\r\n    computed: {\r\n        ...mapState('admin/mobildConfig', ['defaultArray']),\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    // mixins: [theme],\r\n    data() {\r\n        return {\r\n            // 默认初始化数据禁止修改\r\n            defaultConfig: {\r\n                cname: '卡片一',\r\n                name: 'productCard1',\r\n                timestamp: this.num,\r\n                isHide: false,\r\n                setUp: {\r\n                    tabVal: 0\r\n                },\r\n                // 配置项\r\n                titleLeft: '展示设置',\r\n                titleTab: '选项卡设置',\r\n                titleRight: '选项卡样式',\r\n                titleCurrency: '通用样式',\r\n                titleCart: '购物车按钮',\r\n                styleConfig: {\r\n                    title: '选择风格',\r\n                    tabVal: 1,\r\n                    tabList: [\r\n                        {\r\n                            name: '样式一'\r\n                        },\r\n                        {\r\n                            name: '样式二'\r\n                        },\r\n                        {\r\n                            name: '样式三'\r\n                        },\r\n                        {\r\n                            name: '样式四'\r\n                        },\r\n                        {\r\n                            name: '样式五'\r\n                        }\r\n                    ]\r\n                },\r\n                slideConfig: {\r\n                    title: '滑动置顶',\r\n                    tabVal: 1,\r\n                    tabList: [\r\n                        {\r\n                            name: '启用'\r\n                        },\r\n                        {\r\n                            name: '不启用'\r\n                        }\r\n                    ]\r\n                },\r\n                tabConfig: {\r\n                    title: '点击下方选项卡可进行编辑；鼠标拖拽版块可调整顺序',\r\n                    max: '',\r\n                    tabCur: 0,\r\n                    classList: [],\r\n                    list: [\r\n                        {\r\n                            chiild: [\r\n                                {\r\n                                    title: '标题',\r\n                                    val: '首发新品',\r\n                                    max: 4,\r\n                                    pla: '选填，不超过四个字'\r\n                                },\r\n                                {\r\n                                    title: '简介',\r\n                                    val: '最新出炉',\r\n                                    max: 4,\r\n                                    pla: '选填，不超过四个字'\r\n                                }\r\n                            ],\r\n                            image: '',\r\n                            tabVal: 1,\r\n                            brandConfig: {\r\n                                brandVal: []\r\n                            },\r\n                            selectConfig: {\r\n                                activeValue: []\r\n                            },\r\n                            goodsLabel: {\r\n                                activeValue: [],\r\n                                list: []\r\n                            },\r\n                            goodsSort: 0,\r\n                            numConfig: {\r\n                                val: 6\r\n                            },\r\n                            goodsList: {\r\n                                max: 20,\r\n                                list: []\r\n                            },\r\n                            productList: {\r\n                                list: []\r\n                            }\r\n                        }\r\n                    ]\r\n                },\r\n                cartConfig: {\r\n                    title: '是否显示',\r\n                    tabVal: 0,\r\n                    tabList: [\r\n                        {\r\n                            name: '显示'\r\n                        },\r\n                        {\r\n                            name: '隐藏'\r\n                        }\r\n                    ]\r\n                },\r\n                bntConfig: {\r\n                    title: '按钮效果',\r\n                    tabVal: 1,\r\n                    tabList: [\r\n                        {\r\n                            name: '进入商品详情页'\r\n                        },\r\n                        {\r\n                            name: '商品加购'\r\n                        }\r\n                    ]\r\n                },\r\n                bntStyleConfig: {\r\n                    typeFrom: 'bnt',\r\n                    title: '按钮样式',\r\n                    tabVal: 0\r\n                },\r\n                toneConfig: {\r\n                    title: '色调',\r\n                    tabVal: 0,\r\n                    tabList: [\r\n                        {\r\n                            name: '跟随主题风格'\r\n                        },\r\n                        {\r\n                            name: '自定义'\r\n                        }\r\n                    ]\r\n                },\r\n                decorateColor: {\r\n                    title: \"装饰元素\",\r\n                    default: [\r\n                        {\r\n                            item: \"#E93323\",\r\n                        },\r\n                        {\r\n                            item: \"#FF7931\",\r\n                        }\r\n                    ],\r\n                    color: [\r\n                        {\r\n                            item: \"#E93323\",\r\n                        },\r\n                        {\r\n                            item: \"#FF7931\",\r\n                        }\r\n                    ]\r\n                },\r\n                decorateColor2: {\r\n                    title: \"装饰元素\",\r\n                    default: [\r\n                        {\r\n                            item: \"#E93323\",\r\n                        }\r\n                    ],\r\n                    color: [\r\n                        {\r\n                            item: \"#E93323\",\r\n                        }\r\n                    ]\r\n                },\r\n                textColor: {\r\n                    title: \"选中文字\",\r\n                    default: [\r\n                        {\r\n                            item: \"#333333\",\r\n                        }\r\n                    ],\r\n                    color: [\r\n                        {\r\n                            item: \"#333333\",\r\n                        }\r\n                    ]\r\n                },\r\n                textColor2: {\r\n                    title: \"选中文字\",\r\n                    default: [\r\n                        {\r\n                            item: \"#E93323\",\r\n                        }\r\n                    ],\r\n                    color: [\r\n                        {\r\n                            item: \"#E93323\",\r\n                        }\r\n                    ]\r\n                },\r\n                textColor3: {\r\n                    title: \"选中文字\",\r\n                    default: [\r\n                        {\r\n                            item: \"#FFFFFF\",\r\n                        }\r\n                    ],\r\n                    color: [\r\n                        {\r\n                            item: \"#FFFFFF\",\r\n                        }\r\n                    ]\r\n                },\r\n                toneCartConfig: {\r\n                    title: '色调',\r\n                    tabVal: 0,\r\n                    tabList: [\r\n                        {\r\n                            name: '跟随主题风格'\r\n                        },\r\n                        {\r\n                            name: '自定义'\r\n                        }\r\n                    ]\r\n                },\r\n                bntBgColor: {\r\n                    title: '按钮颜色',\r\n                    name: 'bntBgColor',\r\n                    default: [\r\n                        {\r\n                            item: '#E93323'\r\n                        },\r\n                        {\r\n                            item: '#FF7931'\r\n                        }\r\n                    ],\r\n                    color: [\r\n                        {\r\n                            item: '#E93323'\r\n                        },\r\n                        {\r\n                            item: '#FF7931'\r\n                        }\r\n                    ]\r\n                },\r\n                bottomBgColor: {\r\n                    title: '底部背景',\r\n                    default: [\r\n                        {\r\n                            item: '#f5f5f5'\r\n                        }\r\n                    ],\r\n                    color: [\r\n                        {\r\n                            item: '#f5f5f5'\r\n                        }\r\n                    ]\r\n                },\r\n                topConfig: {\r\n                    title: '上边距',\r\n                    val: 0,\r\n                    min: 0\r\n                },\r\n                bottomConfig: {\r\n                    title: '下边距',\r\n                    val: 0,\r\n                    min: 0\r\n                },\r\n                prConfig: {\r\n                    title: '左右边距',\r\n                    val: 10,\r\n                    min: 0\r\n                },\r\n                mbConfig: {\r\n                    title: '页面间距',\r\n                    val: 0,\r\n                    min: 0\r\n                },\r\n                fillet: {\r\n                    title: '背景圆角',\r\n                    type: 0,\r\n                    list: [\r\n                        {\r\n                            val: \"全部\",\r\n                            icon: \"iconcaozuo-zhengti\",\r\n                        },\r\n                        {\r\n                            val: \"单个\",\r\n                            icon: \"iconcaozuo-bianjiao\",\r\n                        }\r\n                    ],\r\n                    valName: '圆角值',\r\n                    val: 0,\r\n                    min: 0,\r\n                    valList: [\r\n                        { val: 0 },\r\n                        { val: 0 },\r\n                        { val: 0 },\r\n                        { val: 0 }\r\n                    ]\r\n                }\r\n            },\r\n            // 样式\r\n            bottomBgColor: '',\r\n            mTop: 0,\r\n            topConfig: 0,\r\n            bottomConfig: 0,\r\n            prConfig: 0,\r\n\r\n        };\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    methods: {\r\n        setConfig(data) {\r\n        }\r\n    }\r\n};\r\n", null]}