{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\addAuthGroup.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\addAuthGroup.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport department from \"@/components/department/index.vue\";\nimport userLabel from \"@/components/labelList\";\nimport {workGroupChat,workLabel,groupChatAuthSave,getGroupChatInfo,UpdateGroupChat} from \"@/api/work\"\nimport Setting from \"@/setting\";\nexport default {\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      formItem: {\n        name: \"\", //二维码名称\n        chat_id:[], //群聊chat_id\n        group_name: \"\", //群名称\n        group_num: 0, //群序列号\n        //owner:[], //群主userid\n        //admin_user:[], //群管理员userid\n        label: [], //客户标签\n        auth_group_chat: 0, //自动建群0=关闭，1=开启\n        //welcome_words:{\n        //  text:{\n        //    content: \"\",\n        //  },\n        //  attachments: [],\n        //}\n      },\n      labelList:[],\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      groupColumn:[\n        {\n          type: \"selection\",\n          width: 60,\n          align: \"center\",\n        },\n        {\n          title: \"群名称\",\n          key: \"name\",\n          minWidth: 80,\n          align: 'center'\n        },\n        {\n          title: \"群主\",\n          slot: \"ownerInfo\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"群公告\",\n          slot: \"notice\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"管理员\",\n          slot: \"admin_user_list\",\n          minWidth: 80,\n          align: 'center'\n        },\n        {\n          title: \"创建时间\",\n          key: \"group_create_time\",\n          minWidth: 110,\n          align: 'center'\n        },\n        {\n          title: \"群人数\",\n          key: \"member_num\",\n          minWidth: 80,\n          align: 'center'\n        },\n        {\n          title: \"退群人数\",\n          key: \"retreat_group_num\",\n          minWidth: 80,\n          align: 'center'\n        },\n      ],\n      groupData:[],\n      groupForm:{\n        page:1,\n        limit:15\n      },\n      rontineObj: {\n        msgtype: \"miniprogram\",\n        miniprogram: {\n          pic_url: \"\",\n          pic_media_id: \"\",\n          title: \"\",\n          appid: \"\",\n          page: \"\",\n        },\n      },\n      imageObj: {\n        msgtype: \"image\",\n        image: {\n          media_id: \"\",\n          pic_url: \"\",\n        },\n      },\n      groupStatus:false,\n      userLoading:false,\n      picTit: \"\",\n      modalPic: false,\n      modalRoutine: false,\n      isChoice: \"单选\",\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: [],\n      selectGroup:[],\n      ruleValidate:{\n        name: [\n            { required: true, message: '二维码名称不能为空', trigger: 'blur' }\n        ],\n      },\n      labelShow: false,\n      dataLabel: []\n    };\n  },\n  components: { uploadPictures, department, userLabel },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\",\"menuCollapse\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    },\n  },\n  watch: {\n    dataLabel(val) {\n        this.formItem.label = val.map(item => item.tag_id);\n    }\n  },\n  mounted() {\n    if (this.$route.params.id !== \"0\" && this.$route.params.id) {\n      this.getInfo();\n    }\n    this.getWorkGroupChat();\n    // this.getWorkLabel();\n  },\n  methods: {\n    getWorkGroupChat(){\n      this.userLoading = true;\n      workGroupChat(this.groupForm).then(res=>{\n        this.groupData = res.data;\n        this.userLoading = false;\n      }).catch(err=>{\n        this.$Message.error(err.msg)\n        this.userLoading = false;\n      })\n    },\n    addGroup(){\n      this.groupStatus = true;\n    },\n    selectAll(row) {\n      if (row.length) {\n        this.selectGroup = row; \n      }\n    },\n    handleSelectRow(row){\n      this.selectGroup = row; \n    },\n    groupConfirm(){\n      this.formItem.chat_id = this.selectGroup.map(item=>{\n        return {\n          chat_id:item.chat_id,\n          name:item.name\n        }\n      })\n    },\n    groupChange(index){\n      this.groupForm.page =index;\n      this.getWorkGroupChat();\n    },\n    modalPicTap(picTit) {\n      this.modalPic = true;\n      this.picTit = picTit;\n    },\n    addRoutine() {\n      this.rontineObj.miniprogram.pic_url = \"\";\n      this.rontineObj.miniprogram.title = \"\";\n      this.rontineObj.miniprogram.appid = \"\";\n      this.rontineObj.miniprogram.page = \"\";\n      this.modalRoutine = true;\n    },\n    addUser(type,index) {\n        this.$refs.department.memberStatus = true;\n        switch (type) {\n          case 'one':\n            // this.userList = this.formItem.userids;\n            this.$refs.department.openType = 'one';\n            break;\n          case 'two':\n            // this.userList = this.formItem.reserve_userid;\n            this.$refs.department.openType = 'two';\n            break;\n          default:\n              break;\n        }\n    },\n    //确认人员\n    changeMastart(arr,type){\n        if(type == 'one'){\n         if(arr.length && arr.length > 1){\n           this.$Message.warning(\"群主只能选择一个\");\n           let newArr = arr.slice(0,1);\n           this.formItem.owner = newArr.map(item=>{\n              return {\n                userid:item.userid,\n                name:item.name\n              }\n            })\n         }else{\n            this.formItem.owner = arr.map(item=>{\n              return {\n                userid:item.userid,\n                name:item.name\n              }\n            })\n         }\n        }else if(type == 'two'){\n          this.formItem.admin_user = arr.map(item=>{\n              return {\n                userid:item.userid,\n                name:item.name\n              }\n          })\n        }\n    },\n    //tag标签删除成员\n    handleDel(e, name) {\n      let i = this.formItem.chat_id.findIndex((item) => item.name == name);\n      this.formItem.chat_id.splice(i,1);\n    },\n    //欢迎语tag删除\n    wordsDel(name){\n      let index = this.formItem.welcome_words.attachments.indexOf(name);\n      this.formItem.welcome_words.attachments.splice(index,1);\n    },\n    // 选中图片\n    getPic(pc) {\n      switch (this.picTit) {\n        case \"image\":\n          this.imageObj.image.pic_url = pc.att_dir;\n          this.formItem.welcome_words.attachments.push(this.imageObj);\n          break;\n        case \"routine\":\n          this.rontineObj.miniprogram.pic_url = pc.att_dir;\n          break;\n      }\n      this.modalPic = false;\n    },\n    insertName() {\n      this.formItem.welcome_words.text.content = \"##客户名称##\";\n    },\n    routineConfirm() {\n      const routine = this.deepClone(this.rontineObj);\n      this.formItem.welcome_words.attachments.push(routine);\n    },\n    routineCancel() {},\n    //获取客户标签\n    getWorkLabel() {\n      workLabel().then((res) => {\n        // this.labelList = res.data.map((org) => this.mapTree(org));\n        this.mapTree(res.data)\n      });\n    },\n     mapTree(org) {\n        for (let i = 0; i < org.length; i++) {\n            for (let j = 0; j < this.formItem.label.length; j++) {\n                if (this.formItem.label[j] === org[i].value) {\n                    this.dataLabel.push({\n                        label_name: org[i].label,\n                        id: org[i].id,\n                        tag_id: org[i].value\n                    });\n                }\n            }\n            Array.isArray(org[i].children) && this.mapTree(org[i].children);\n        }\n    //   const haveChildren =\n    //     Array.isArray(org.children) && org.children.length > 0;\n    //   return {\n    //     //分别将我们查询出来的值做出改变他的key\n    //     title: org.label,\n    //     expand: true,\n    //     value: org.value,\n    //     selected: false,\n    //     checked: false,\n    //     children: haveChildren ? org.children.map((i) => this.mapTree(i)) : [],\n    //   };\n    },\n    getInfo(){\n      getGroupChatInfo(this.$route.params.id).then(res=>{\n        this.formItem = res.data;\n        this.formItem.chat_id = this.formItem.chatList;\n        this.getWorkLabel();\n      })\n    },\n    submit(){\n      if(!this.formItem.chat_id.length) return this.$Message.error(\"请添加群聊\")\n      const formData = this.deepClone(this.formItem);\n      formData.chat_id = formData.chat_id.map(item=>{\n        return item.chat_id\n      })\n      // formData.admin_user = formData.admin_user.map(item=>{\n      //   return item.userid\n      // })\n      // formData.owner = formData.owner[0].userid;\n      if(this.$route.params.id){\n        delete formData.chatList;\n        delete formData.labelList;\n        UpdateGroupChat(this.$route.params.id,formData).then(res=>{\n          this.$Message.success(\"修改自动拉群成功\");\n          this.$router.push(this.roterPre + '/work/auth_group')\n        })\n      }else{\n        groupChatAuthSave(formData).then(res=>{\n          this.$Message.success(\"保存成功\");\n          this.$router.push(this.roterPre + '/work/auth_group')\n        }).catch(err=>{\n          this.$Message.error(err.msg)\n        })\n      }\n    },\n    //深克隆\n    deepClone(obj) {\n        let newObj = Array.isArray(obj) ? [] : {}\n        if (obj && typeof obj === \"object\") {\n            for (let key in obj) {\n                if (obj.hasOwnProperty(key)) {\n                    newObj[key] = (obj && typeof obj[key] === 'object') ? this.deepClone(obj[key]) : obj[key];\n                }\n            }\n        } \n        return newObj\n    },\n    openLabel() {\n        this.labelShow = true;\n        this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    activeData(dataLabel){\n        this.labelShow = false;\n        this.dataLabel = dataLabel;\n    },\n    // 标签弹窗关闭\n    labelClose() {\n        this.labelShow = false;\n    },\n    closeLabel(label){\n        let index = this.dataLabel.indexOf(this.dataLabel.filter(d=>d.id == label.id)[0]);\n        this.dataLabel.splice(index,1);\n    },\n  },\n};\n", null]}