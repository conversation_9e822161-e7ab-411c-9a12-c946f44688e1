{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue?vue&type=style&index=0&id=230761d4&lang=stylus&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.carMywrapper{\n\t.download{\n\t\tposition absolute;\n\t\ttop:2px;\n\t\tleft:180px;\n\t}\n\t.stock-disk{\n\t\tmargin 10px 0 15px 0;\n\t}\n\t.scroll-virtual {\n\t  max-height: 320px;\n\t  overflow-y: auto;\n\t  margin-top: 10px;\n\t}\n\t.virtual-title {\n\t  width: 50px;\n\t}\n\t.deteal-btn {\n\t  color: #5179ea;\n\t  cursor pointer;\n\t}\n\t.add-more {\n\t  margin-top: 20px;\n\t  display: flex;\n\t  position relative;\n\t}\n\t.footer{\n\t\t\t display flex\n\t\t\t justify-content flex-end\n\t\t\t margin-top 40px\n\t\t\t button{\n\t\t\t\t  margin-left 10px\n\t\t\t }\n\t}\n}\n", null]}