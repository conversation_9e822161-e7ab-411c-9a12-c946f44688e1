{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\customerBase\\list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\customerBase\\list.vue", "mtime": 1693985518000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport department from \"@/components/department/index.vue\";\nimport { getWorkClientList, workClientSynch, workLabel, workClientBatchLabel, workClientEdit } from \"@/api/work\";\nimport { userList, userGroupApi, putUsersSetLabel, editOtherApi, giveLevelTimeApi } from \"@/api/user\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport userDetails from \"../../user/list/handle/userDetails\";\nimport sendFrom from \"@/components/sendCoupons/index\";\nimport editFrom from \"@/components/from/from\";\nexport default {\n  name: \"\",\n  data: function data() {\n    return {\n      loading: false,\n      formInline: {},\n      options: timeOptions,\n      columns1: [{\n        type: \"selection\",\n        width: 60,\n        align: \"center\"\n      }, {\n        title: \"客户信息\",\n        slot: \"avatar\",\n        minWidth: 120\n      }, {\n        title: \"所属客服\",\n        slot: \"followOne\",\n        minWidth: 80\n      }, {\n        title: \"客服所属部门\",\n        slot: \"external_userid\",\n        minWidth: 80\n      }, {\n        title: \"标签\",\n        slot: \"corp_id\",\n        minWidth: 120\n      }, {\n        title: \"性别\",\n        slot: \"gender\",\n        minWidth: 80\n      }, {\n        title: \"客户类型\",\n        slot: \"type\",\n        minWidth: 80\n      }, // {\n      //   title: \"职位\",\n      //   key: \"position\",\n      //   minWidth: 100,\n      // },\n      // {\n      //   title: \"所在企业主体名称\",\n      //   key: \"corp_full_name\",\n      //   minWidth: 100,\n      // },\n      {\n        title: \"备注\",\n        key: \"remark\",\n        minWidth: 110\n      }, {\n        title: \"添加时间\",\n        key: \"create_time\",\n        minWidth: 110\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        // fixed: \"right\",\n        minWidth: 130\n      }],\n      columns2: [{\n        type: \"selection\",\n        width: 60,\n        align: \"center\"\n      }, {\n        title: \"客户信息\",\n        slot: \"avatars\",\n        minWidth: 120\n      }, {\n        title: \"付费会员\",\n        slot: \"isMember\",\n        minWidth: 90\n      }, {\n        title: \"用户等级\",\n        key: \"level\",\n        minWidth: 90\n      }, {\n        title: \"分组\",\n        key: \"group_id\",\n        minWidth: 100\n      }, {\n        title: \"手机号\",\n        key: \"phone\",\n        minWidth: 100\n      }, {\n        title: \"用户类型\",\n        key: \"user_type\",\n        minWidth: 100\n      }, {\n        title: \"内部联系人\",\n        slot: \"follow_list\",\n        minWidth: 100\n      }, {\n        title: \"余额\",\n        key: \"now_money\",\n        minWidth: 100\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        // fixed: \"right\",\n        minWidth: 80\n      }],\n      tableData: [],\n      tableData1: [],\n      tabIndex: 0,\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      timeVal: [],\n      //客户标签列表\n      labelList: [],\n      labelShow: false,\n      activeLabel: {\n        add_tag: [],\n        removeTag: [],\n        userid: [],\n        is_all: 0\n      },\n      activeLabel2: {\n        label_id: [],\n        uids: [],\n        all: 0\n      },\n      tableFrom: {\n        userid: [],\n        label: [],\n        time: \"\",\n        name: \"\",\n        page: 1,\n        limit: 15\n      },\n      labelIds: [],\n      usersName: \"\",\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: [],\n      selectGroup: [],\n      selectUser: [],\n      groupList: [],\n      user_ids: \"\",\n      dataLabel: [],\n      setLabelIndex: null,\n      labelFormList: [],\n      markShow: false,\n      remark: \"\",\n      selectRow: {},\n      FromData: null\n    };\n  },\n  components: {\n    department: department,\n    userDetails: userDetails,\n    sendFrom: sendFrom,\n    editFrom: editFrom\n  },\n  filters: {\n    genderFilter: function genderFilter(value) {\n      if (value == 0) {\n        return \"未知\";\n      } else if (value == 1) {\n        return \"男\";\n      } else if (value == 2) {\n        return \"女\";\n      }\n    }\n  },\n  watch: {\n    usersName: function usersName(val, oldVal) {\n      if (!val) {\n        this.tableFrom.userid = [];\n      }\n    }\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    }\n  }),\n  created: function created() {\n    this.getList();\n    this.userGroup();\n    this.getWorkLabel();\n    this.getUserList();\n  },\n  methods: {\n    selectAll: function selectAll(row) {\n      if (row.length) {\n        this.selectGroup = row;\n      }\n    },\n    handleSelectRow: function handleSelectRow(row) {\n      this.selectGroup = row;\n    },\n    userAll: function userAll(row) {\n      if (row.length) {\n        this.selectUser = row;\n      }\n    },\n    userSelectRow: function userSelectRow(row) {\n      this.selectUser = row;\n    },\n    getList: function getList() {\n      var _this = this;\n\n      this.loading = true;\n      getWorkClientList(this.tableFrom).then(function (res) {\n        _this.tableData = res.data;\n        _this.loading = false;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n\n        _this.loading = false;\n      });\n    },\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.time = this.timeVal.join(\"-\");\n    },\n    dateChange: function dateChange() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    clientSynch: function clientSynch() {\n      var _this2 = this;\n\n      workClientSynch().then(function (res) {\n        _this2.$Message.success(res.msg);\n      });\n    },\n    searchData: function searchData() {\n      this.tableFrom.page = 1;\n\n      if (this.tabIndex == 0) {\n        this.tableFrom.label = this.labelFormList.map(function (item) {\n          return item.value;\n        });\n        this.getList();\n      } else {\n        this.getUserList();\n      }\n    },\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n\n      if (this.tabIndex == 0) {\n        this.getList();\n      } else {\n        this.getUserList();\n      }\n    },\n    //获取客户标签\n    getWorkLabel: function getWorkLabel() {\n      var _this3 = this;\n\n      workLabel().then(function (res) {\n        _this3.labelList = res.data.map(function (org) {\n          return _this3.mapTree(org);\n        });\n      });\n    },\n    mapTree: function mapTree(org) {\n      var _this4 = this;\n\n      var haveChildren = Array.isArray(org.children) && org.children.length > 0;\n      return {\n        //分别将我们查询出来的值做出改变他的key\n        title: org.label,\n        expand: true,\n        value: org.value,\n        id: org.id,\n        selected: false,\n        checked: false,\n        children: haveChildren ? org.children.map(function (i) {\n          return _this4.mapTree(i);\n        }) : []\n      };\n    },\n    addUser: function addUser() {\n      this.$refs.department.memberStatus = true;\n    },\n    changeMastart: function changeMastart(arr, type) {\n      this.tableFrom.userid = arr.map(function (item) {\n        return item.userid;\n      });\n      var name = arr.map(function (item1) {\n        return item1.name;\n      });\n      this.usersName = name.toString();\n    },\n    getUserList: function getUserList() {\n      var _this5 = this;\n\n      this.loading = true;\n      userList({\n        page: this.tableFrom.page,\n        limit: this.tableFrom.limit,\n        user_time_type: \"all\",\n        user_time: this.tableFrom.time,\n        nickname: this.tableFrom.name,\n        label_ids: this.labelIds.toString()\n      }).then(function (res) {\n        var data = res.data;\n        _this5.tableData1 = data;\n        _this5.loading = false;\n      }).catch(function (res) {\n        _this5.loading = false;\n\n        _this5.$Message.error(res.msg);\n      });\n    },\n    changeMenu: function changeMenu(row, name) {\n      switch (name) {\n        case \"2\":\n          this.getOtherFrom(row.uid);\n          break;\n\n        case \"3\":\n          this.giveLevelTime(row.uid);\n          break;\n\n        default:\n          break;\n      }\n    },\n    // 获取积分余额表单\n    getOtherFrom: function getOtherFrom(id) {\n      var _this6 = this;\n\n      editOtherApi(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context.next = 2;\n                    break;\n                  }\n\n                  return _context.abrupt(\"return\", _this6.$authLapse(res.data));\n\n                case 2:\n                  res.data.rules[1].props.max = 999999;\n                  _this6.FromData = res.data;\n                  _this6.$refs.edits.modals = true;\n\n                case 5:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this6.$Message.error(res.msg);\n      });\n    },\n    submitFail: function submitFail(p) {\n      if (this.$refs.userDetails.modals) {\n        this.$refs.userDetails.getDetails(this.selectRow.uid);\n      }\n    },\n    giveLevelTime: function giveLevelTime(id) {\n      var _this7 = this;\n\n      giveLevelTimeApi(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context2.next = 2;\n                    break;\n                  }\n\n                  return _context2.abrupt(\"return\", _this7.$authLapse(res.data));\n\n                case 2:\n                  _this7.FromData = res.data;\n                  _this7.$refs.edits.modals = true;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this7.$Message.error(res.msg);\n      });\n    },\n    onChangeType: function onChangeType() {\n      this.selectGroup = [];\n      this.selectUser = [];\n      this.dataLabel = [];\n      this.tableFrom.time = \"\";\n      this.tableFrom.name = \"\";\n    },\n    selectLabel: function selectLabel(label) {\n      if (label.selected) {\n        var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n          return d.id == label.id;\n        })[0]);\n        this.dataLabel.splice(index, 1);\n        this.activeLabel.removeTag.push(label.value);\n        label.selected = false;\n      } else {\n        this.dataLabel.push({\n          id: label.id,\n          value: label.value,\n          title: label.title\n        });\n        label.selected = true;\n      }\n    },\n    onSend: function onSend() {\n      if (this.selectUser.length === 0) {\n        this.$Message.warning(\"请选择要发送优惠券的用户\");\n      } else {\n        var arr = this.selectUser.map(function (item) {\n          return item.uid;\n        });\n        this.user_ids = arr.join();\n        this.$refs.sends.modals = true;\n        this.$refs.sends.getList();\n      }\n    },\n    setLabel: function setLabel(index) {\n      this.setLabelIndex = index;\n      this.labelShow = true;\n    },\n    labeSet: function labeSet(row) {\n      var _this8 = this;\n\n      // this.getWorkLabel();\n      if (row.followOne && row.followOne.tags.length) {\n        this.labelList.forEach(function (item1) {\n          item1.children.forEach(function (item2) {\n            row.followOne.tags.forEach(function (item) {\n              if (item.tag_id == item2.value) {\n                item2.selected = true;\n\n                _this8.dataLabel.push({\n                  id: item2.id,\n                  value: item2.value,\n                  title: item2.title\n                });\n              }\n            });\n          });\n        });\n      }\n\n      this.labelShow = true;\n      this.selectGroup[0] = row;\n    },\n    labelConfirm: function labelConfirm() {\n      var _this9 = this;\n\n      if (this.setLabelIndex == 0) {\n        //顶部卡片客户标签作为搜索条件时打开\n        this.labelFormList = this.dataLabel;\n        this.labelIds = this.labelFormList.map(function (item) {\n          return item.id;\n        }); //打开标签弹窗\n\n        this.labelShow = false;\n      } else {\n        if (this.tabIndex == 0) {\n          //企微客户设置标签\n          //selectGroup是选中的用户，可以是多个，也可以是单个\n          this.activeLabel.userid = this.selectGroup.map(function (item) {\n            //将选中用户的user_id push到activeLabel.userid；\n            return item.external_userid;\n          }); //设置要添加的tab标签value\n\n          this.activeLabel.add_tag = this.dataLabel.map(function (item) {\n            return item.value;\n          }); //从add_tag标签中剔除要删除的标签，在编辑时使用\n\n          this.activeLabel.add_tag.forEach(function (i) {\n            _this9.activeLabel.removeTag.forEach(function (j) {\n              if (i == j) {\n                _this9.activeLabel.add_tag.splice(i, 1);\n              }\n            });\n          });\n          workClientBatchLabel(this.activeLabel).then(function (res) {\n            _this9.$Message.success(res.msg);\n\n            _this9.labelShow = false;\n          }).catch(function (err) {\n            _this9.$Message.error(err.msg);\n\n            _this9.labelShow = false;\n          });\n        } else {\n          this.activeLabel2.uids = this.selectUser.map(function (item) {\n            return item.uid;\n          });\n          this.activeLabel2.label_id = this.dataLabel.map(function (item) {\n            return item.id;\n          });\n          putUsersSetLabel(this.activeLabel2).then(function (res) {\n            _this9.$Message.success(res.msg);\n\n            _this9.labelShow = false;\n          }).catch(function (err) {\n            _this9.$Message.error(err.msg);\n\n            _this9.labelShow = false;\n          });\n        }\n      }\n    },\n    labelCancel: function labelCancel() {\n      this.labelShow = false;\n\n      if (this.tabIndex == 0) {\n        this.getList();\n      } else {\n        this.getUserList();\n      }\n\n      this.getWorkLabel();\n    },\n    handleClose2: function handleClose2(e, name) {\n      var index = this.labelFormList.indexOf(name);\n      this.labelFormList.splice(index, 1);\n      this.tableFrom.label.splice(index, 1);\n      this.labelIds.splice(index, 1);\n    },\n    userGroup: function userGroup() {\n      var _this10 = this;\n\n      var data = {\n        page: 1,\n        limit: \"\"\n      };\n      userGroupApi(data).then(function (res) {\n        _this10.groupList = res.data.list;\n      });\n    },\n    changeInfo: function changeInfo(row) {\n      this.$refs.userDetails.modals = true;\n      this.$refs.userDetails.activeName = \"info\";\n      this.$refs.userDetails.getDetails(row.uid);\n    },\n    workInfo: function workInfo(row) {\n      this.selectRow = row;\n      this.$refs.userDetails.modals = true;\n      this.$refs.userDetails.activeName = \"info\";\n      this.$refs.userDetails.getDetails(row.uid);\n    },\n    editMark: function editMark(row) {\n      this.remark = row.remark;\n      this.selectRow = row;\n      this.markShow = true;\n    },\n    editMaekConfirm: function editMaekConfirm() {\n      var _this11 = this;\n\n      workClientEdit(this.selectRow.id, {\n        remark: this.remark\n      }).then(function (res) {\n        _this11.$Message.success(res.msg);\n\n        _this11.getList();\n      }).catch(function (err) {\n        _this11.$Message.error(err.msg);\n      });\n    },\n    editMarkCancel: function editMarkCancel() {\n      this.remark = \"\";\n    }\n  }\n};", null]}