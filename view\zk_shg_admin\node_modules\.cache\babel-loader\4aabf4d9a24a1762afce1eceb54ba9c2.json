{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\utils\\Excel.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\utils\\Excel.js", "mtime": 1640264910000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\nimport { saveAs } from 'file-saver';\nimport XLSX from 'xlsx';\n\nfunction datenum(v, date1904) {\n  if (date1904) v += 1462;\n  var epoch = Date.parse(v);\n  return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);\n}\n\nfunction data2ws(data) {\n  var ws = {};\n  var range = {\n    s: {\n      c: 10000000,\n      r: 10000000\n    },\n    e: {\n      c: 0,\n      r: 0\n    }\n  };\n\n  for (var R = 0; R !== data.length; ++R) {\n    for (var C = 0; C !== data[R].length; ++C) {\n      if (range.s.r > R) range.s.r = R;\n      if (range.s.c > C) range.s.c = C;\n      if (range.e.r < R) range.e.r = R;\n      if (range.e.c < C) range.e.c = C;\n      var cell = {\n        v: data[R][C]\n      };\n      if (cell.v == null) continue;\n      var cellRef = XLSX.utils.encode_cell({\n        c: C,\n        r: R\n      });\n      if (typeof cell.v === 'number') cell.t = 'n';else if (typeof cell.v === 'boolean') cell.t = 'b';else if (cell.v instanceof Date) {\n        cell.t = 'n';\n        cell.z = XLSX.SSF._table[14];\n        cell.v = datenum(cell.v);\n      } else cell.t = 's';\n      ws[cellRef] = cell;\n    }\n  }\n\n  if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);\n  return ws;\n}\n\nfunction Workbook() {\n  if (!(this instanceof Workbook)) return new Workbook();\n  this.SheetNames = [];\n  this.Sheets = {};\n}\n\nfunction s2ab(s) {\n  var buf = new ArrayBuffer(s.length);\n  var view = new Uint8Array(buf);\n\n  for (var i = 0; i !== s.length; ++i) {\n    view[i] = s.charCodeAt(i) & 0xFF;\n  }\n\n  return buf;\n}\n/*\n* th => 表头\n* data => 数据\n* fileName => 文件名\n* fileType => 文件类型\n* sheetName => sheet页名\n*/\n\n\nexport default function toExcel(_ref) {\n  var th = _ref.th,\n      data = _ref.data,\n      fileName = _ref.fileName,\n      fileType = _ref.fileType,\n      sheetName = _ref.sheetName;\n  data.unshift(th);\n  var wb = new Workbook();\n  var ws = data2ws(data);\n  sheetName = sheetName || 'sheet1';\n  wb.SheetNames.push(sheetName);\n  wb.Sheets[sheetName] = ws;\n  fileType = fileType || 'xlsx';\n  var wbout = XLSX.write(wb, {\n    bookType: fileType,\n    bookSST: false,\n    type: 'binary'\n  });\n  fileName = fileName || '列表';\n  saveAs(new Blob([s2ab(wbout)], {\n    type: 'application/octet-stream'\n  }), \"\".concat(fileName, \".\").concat(fileType));\n}", null]}