{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue?vue&type=template&id=a7ba3238&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue", "mtime": 1709630624583}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt box\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Form',{ref:\"formValidate\",attrs:{\"inline\":\"\",\"model\":_vm.formValidate,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"创建时间：\"}},[_c('DatePicker',{staticClass:\"input-add\",attrs:{\"editable\":false,\"value\":_vm.timeVal,\"format\":\"yyyy/MM/dd\",\"type\":\"datetimerange\",\"placement\":\"bottom-start\",\"placeholder\":\"自定义时间\",\"options\":_vm.options},on:{\"on-change\":_vm.onchangeTime}})],1),_c('FormItem',{attrs:{\"label\":\"订单搜索：\",\"label-for\":\"status1\"}},[_c('Input',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请输入交易单号/交易人\"},model:{value:(_vm.formValidate.keyword),callback:function ($$v) {_vm.$set(_vm.formValidate, \"keyword\", $$v)},expression:\"formValidate.keyword\"}})],1),_c('FormItem',[_c('Button',{staticStyle:{\"margin-left\":\"-75px\",\"margin-right\":\"14px\"},attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.search()}}},[_vm._v(\"查询\")]),_c('Button',{on:{\"click\":_vm.exports}},[_vm._v(\"导出\")])],1)],1)],1),_c('Card',{staticClass:\"ive-mt tablebox\",attrs:{\"bordered\":false,\"dis-hover\":\"\",\"padding\":20}},[_c('div',{staticClass:\"table\"},[_c('Table',{ref:\"table\",attrs:{\"columns\":_vm.columns,\"data\":_vm.orderList,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"number\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.pm == 0)?_c('span',{staticClass:\"colorgreen\"},[_vm._v(\"- \"+_vm._s(row.number))]):_vm._e(),(row.pm == 1)?_c('span',{staticClass:\"colorred\"},[_vm._v(\"+ \"+_vm._s(row.number))]):_vm._e()]}},{key:\"user_nickname\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('span',[_vm._v(_vm._s(row.uid ? row.user_nickname : '游客'))])]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.remark(row)}}},[_vm._v(\"备注\")])]}}])})],1),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"current\":_vm.formValidate.page,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.formValidate.limit},on:{\"on-change\":_vm.pageChange}})],1)]),_c('Modal',{staticClass:\"order_box\",attrs:{\"scrollable\":\"\",\"title\":\"请修改内容\",\"closable\":false,\"mask-closable\":false},model:{value:(_vm.modalmark),callback:function ($$v) {_vm.modalmark=$$v},expression:\"modalmark\"}},[_c('Form',{ref:\"remarks\",attrs:{\"model\":_vm.remarks,\"label-width\":80},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"备注：\"}},[_c('Input',{staticStyle:{\"width\":\"100%\"},attrs:{\"maxlength\":\"200\",\"show-word-limit\":\"\",\"type\":\"textarea\",\"placeholder\":\"请填写备注~\"},model:{value:(_vm.remarks.mark),callback:function ($$v) {_vm.$set(_vm.remarks, \"mark\", $$v)},expression:\"remarks.mark\"}})],1)],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.putRemark()}}},[_vm._v(\"提交\")]),_c('Button',{on:{\"click\":function($event){return _vm.cancel()}}},[_vm._v(\"取消\")])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}