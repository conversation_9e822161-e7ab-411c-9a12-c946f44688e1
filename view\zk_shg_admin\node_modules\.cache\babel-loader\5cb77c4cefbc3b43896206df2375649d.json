{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_nav_bar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_nav_bar.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nimport rightBtn from '@/components/rightBtn/index.vue';\nexport default {\n  name: 'c_nav_bar',\n  componentsName: 'nav_bar',\n  cname: '导航',\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneConfig'\n      }],\n      twoStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'decorateColor'\n      }],\n      twoStyle2: [{\n        components: toolCom.c_bg_color,\n        configNme: 'decorateColor2'\n      }],\n      threeStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'textColor'\n      }],\n      fourStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'textColor2'\n      }],\n      fiveStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'textColor3'\n      }],\n      currencyStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'moduleColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'mbConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      setUp: 0,\n      type: 0,\n      type2: 0\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      // debugger;\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          var tempArr = [{\n            components: toolCom.c_title,\n            configNme: 'titleLeft'\n          }, {\n            components: toolCom.c_button_style,\n            configNme: 'styleConfig'\n          }, {\n            components: toolCom.c_radio,\n            configNme: 'stickyConfig'\n          }, {\n            components: toolCom.c_title,\n            configNme: 'titleTab'\n          }, {\n            components: toolCom.c_tab_list,\n            configNme: 'tabListConfig'\n          }];\n          this.rCom = arr.concat(tempArr);\n        } else {\n          if (this.type == 0) {\n            if (this.type2 == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.currencyStyle));\n            }\n          } else if (this.type == 1) {\n            if (this.type2 == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle2), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            if (this.type2 == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n            }\n          }\n        }\n      },\n      deep: true\n    },\n    'configObj.styleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          if (nVal == 0) {\n            if (this.type2 == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.currencyStyle));\n            }\n          } else if (nVal == 1) {\n            if (this.type2 == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle2), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            if (this.type2 == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n            }\n          }\n        }\n      },\n      deep: true\n    },\n    'configObj.toneConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type2 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          if (this.type == 0) {\n            if (nVal == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.currencyStyle));\n            }\n          } else if (this.type == 1) {\n            if (nVal == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle2), _toConsumableArray(this.fourStyle), _toConsumableArray(this.currencyStyle));\n            }\n          } else {\n            if (nVal == 0) {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.currencyStyle));\n            } else {\n              this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n            }\n          }\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    // 获取组件参数\n    getConfig: function getConfig(data) {}\n  }\n};", null]}