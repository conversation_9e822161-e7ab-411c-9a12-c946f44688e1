{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\label\\cate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\label\\cate.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {\n  userLabelAll,\n  userLabelApi,\n  userLabelAddApi,\n  userLabelEdit,\n  userLabelCreate,\n  workLabelSync\n} from \"@/api/user\";\nexport default {\n  name: \"user_label\",\n  data() {\n    return {\n      grid: {\n        xl: 24,\n        lg: 24,\n        md: 24,\n        sm: 24,\n        xs: 24,\n      },\n      loading: false,\n      columns1: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          Width: 80,\n          minWidth: 50,\n          maxWidth: 80\n        },\n        {\n          title: \"标签名称\",\n          key: \"label_name\",\n          // minWidth: 600,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          // fixed: \"right\",\n          align: 'center',\n          minWidth: 120,\n          maxWidth: 140\n        },\n      ],\n      labelFrom: {\n        page: 1,\n        limit: 10,\n        label_cate: \"\",\n      },\n      labelLists: [],\n      total: 0,\n      theme3: \"light\",\n      labelSort: [],\n      sortName: \"\",\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 75;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getUserLabelAll();\n  },\n  methods: {\n    // 添加\n    add() {\n\t\t\tlet id = this.labelFrom.label_cate?this.labelFrom.label_cate:0;\n      this.$modalForm(userLabelAddApi(0,{label_cate:id})).then(() => this.getList());\n    },\n    // 分组列表\n    getList() {\n      this.loading = true;\n      userLabelApi(this.labelFrom)\n        .then(async (res) => {\n          let data = res.data;\n          this.labelLists = data.list;\n          this.total = data.count;\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.labelFrom.page = index;\n      this.getList();\n    },\n    // 修改\n    edit(id) {\n      this.$modalForm(userLabelAddApi(id)).then(() => this.getList());\n    },\n    // 删除\n    del(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `user/user_label/del/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.labelLists.splice(num, 1);\n          if (!this.labelLists.length) {\n            this.labelFrom.page =\n                this.labelFrom.page == 1 ? 1 : this.labelFrom.page - 1;\n          }\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 标签分类\n    getUserLabelAll(key) {\n      userLabelAll().then((res) => {\n        let obj = {\n          name: \"全部\",\n          id: \"\",\n        };\n        res.data.unshift(obj);\n        res.data.forEach((el) => {\n          el.status = false;\n        });\n        if (!key) {\n          this.sortName = res.data[0].id;\n          this.labelFrom.label_cate = res.data[0].id;\n          this.getList();\n        }\n        this.labelSort = res.data;\n      });\n    },\n    // 显示标签小菜单\n    showMenu(item) {\n      this.labelSort.forEach((el) => {\n        if (el.id == item.id) {\n          el.status = item.status ? false : true;\n        } else {\n          el.status = false;\n        }\n      });\n    },\n    //编辑标签\n    labelEdit(item) {\n      this.$modalForm(userLabelEdit(item.id)).then(() =>\n        this.getUserLabelAll(1)\n      );\n    },\n    // 添加分类\n    addSort() {\n      this.$modalForm(userLabelCreate()).then(() => this.getUserLabelAll());\n    },\n    deleteSort(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `user/user_label_cate/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.labelSort.splice(num, 1);\n          this.labelSort = [];\n          this.getUserLabelAll();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    bindMenuItem(name) {\n      this.labelSort.forEach((el) => {\n        el.status = false;\n      });\n      this.labelFrom.page = 1;\n      this.labelFrom.label_cate = name.id;\n      this.getList();\n    },\n    userLabeSync(){\n      workLabelSync().then(res=>{\n        this.$Message.success(res.msg)\n      }).catch(err=>{\n        this.$Message.error(err.msg)\n      })\n    }\n  },\n};\n", null]}