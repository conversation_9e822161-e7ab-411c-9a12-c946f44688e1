{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue?vue&type=template&id=95adbe0c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<Modal v-model=\"modals\" :z-index=\"1\"  scrollable  footer-hide closable title=\"等级任务\" :mask-closable=\"false\" width=\"950\" @on-cancel=\"handleReset\">\n    <Form ref=\"levelFrom\" :model=\"levelFrom\"  :label-width=\"labelWidth\" :label-position=\"labelPosition\" @submit.native.prevent>\n        <Row type=\"flex\" :gutter=\"24\">\n            <Col v-bind=\"grid\">\n                <FormItem label=\"状态：\">\n                    <Select v-model=\"levelFrom.is_show\" placeholder=\"是否显示\" clearable @on-change=\"userSearchs\">\n                        <Option value=\"1\">显示</Option>\n                        <Option value=\"0\">不显示</Option>\n                    </Select>\n                </FormItem>\n            </Col>\n            <Col v-bind=\"grid\">\n                <FormItem label=\"任务名称：\" prop=\"status2\" label-for=\"status2\">\n                    <Input search enter-button  v-model=\"levelFrom.name\" placeholder=\"请输入任务名称\" @on-search=\"userSearchs\" style=\"width: 100%\"/>\n                </FormItem>\n            </Col>\n        </Row>\n    </Form>\n    <Divider dashed />\n    <Row type=\"flex\">\n        <Col v-bind=\"grid\" class=\"mb15\">\n            <Button type=\"primary\"  icon=\"md-add\" @click=\"add\">添加等级任务</Button>\n        </Col>\n        <Col span=\"24\" class=\"userAlert\">\n            <Alert show-icon closable>添加等级任务,任务类型中的{$num}会自动替换成限定数量+系统预设的单位生成任务名</Alert>\n        </Col>\n    </Row>\n    <Divider dashed />\n    <Table :columns=\"columns1\" :data=\"levelLists\" ref=\"table\"\n           :loading=\"loading\"\n           no-userFrom-text=\"暂无数据\"\n           no-filtered-userFrom-text=\"暂无筛选结果\">\n        <template slot-scope=\"{ row, index }\" slot=\"is_shows\">\n            <i-switch v-model=\"row.is_show\" :value=\"row.is_show\" :true-value=\"1\" :false-value=\"0\"  size=\"large\" @on-change=\"onchangeIsShow(row)\">\n                <span slot=\"open\">显示</span>\n                <span slot=\"close\">隐藏</span>\n            </i-switch>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"is_musts\">\n            <i-switch v-model=\"row.is_must\" :value=\"row.is_must\" :true-value=\"1\" :false-value=\"0\"  size=\"large\" @on-change=\"onchangeIsMust(row)\">\n                <span slot=\"open\">全部完成</span>\n                <span slot=\"close\">达成其一</span>\n            </i-switch>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"action\">\n            <a @click=\"edit(row)\">编辑  | </a>\n            <a @click=\"del(row,'删除等级任务',index)\">  删除</a>\n        </template>\n    </Table>\n    <div class=\"acea-row row-right page\">\n        <Page :total=\"total\" show-elevator show-total @on-change=\"pageChange\"\n              :page-size=\"levelFrom.limit\"/>\n    </div>\n    <!-- 新建 编辑表单-->\n    <edit-from ref=\"edits\" :FromData=\"FromData\" @submitFail=\"submitFail\" :titleType=\"titleType\"></edit-from>\n</Modal>\n", null]}