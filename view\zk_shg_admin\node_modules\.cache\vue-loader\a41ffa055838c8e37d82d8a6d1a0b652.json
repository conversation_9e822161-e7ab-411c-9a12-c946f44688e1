{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue?vue&type=template&id=4ead133c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n  <Form\n    ref=\"orderData\"\n    inline\n    :model=\"orderData\"\n    :label-width=\"labelWidth\"\n    :label-position=\"labelPosition\"\n    class=\"tabform\"\n    @submit.native.prevent\n  >\n    <Row>\n      <!-- <Col>\n        <FormItem label=\"订单状态：\">\n          <Select\n            v-model=\"orderData.status\"\n            class=\"input-add\"\n            clearable\n            @on-change=\"selectChange2\"\n            placeholder=\"全部\"\n          >\n            <Option value=\"\">全部</Option>\n            <Option value=\"0\">未支付</Option>\n            <Option value=\"1\">未发货</Option>\n            <Option value=\"2\">待收货</Option>\n            <Option value=\"5\">待核销</Option>\n            <Option value=\"6\">已核销</Option>\n            <Option value=\"3\">待评价</Option>\n            <Option value=\"4\">已完成</Option>\n            <Option value=\"-2\">已退款</Option>\n            <Option value=\"-4\">已删除</Option>\n          </Select>\n        </FormItem>\n      </Col> -->\n      <Col>\n        <FormItem label=\"订单类型：\">\n          <RadioGroup v-model=\"currentTab\" type=\"button\" @on-change=\"onClickTab\">\n              <Radio label=\"-1\">所有订单</Radio>\n              <Radio label=\"0\">平台订单</Radio>\n              <Radio label=\"1\">门店订单</Radio>\n              <Radio label=\"2\">供应商订单</Radio>\n          </RadioGroup>\n        </FormItem>\n      </Col>\n      <Col>\n        <FormItem label=\"支付方式：\">\n          <Select\n            v-model=\"orderData.pay_type\"\n            clearable\n          class=\"input-add\"\n            @on-change=\"userSearchs\"\n            placeholder=\"全部\"\n          >\n            <Option\n              v-for=\"item in payList\"\n              :value=\"item.val\"\n              :key=\"item.id\"\n              >{{ item.label }}</Option\n            >\n          </Select>\n        </FormItem>\n      </Col>\n      <Col>\n        <FormItem label=\"创建时间：\">\n          <DatePicker\n            :editable=\"false\"\n            :clearable=\"true\"\n            @on-change=\"onchangeTime\"\n            :value=\"timeVal\"\n            format=\"yyyy/MM/dd HH:mm:ss\"\n            type=\"datetimerange\"\n            placement=\"bottom-start\"\n            placeholder=\"自定义时间\"\n            class=\"input-add mr20\"\n            :options=\"options\"\n          ></DatePicker>\n        </FormItem>\n      </Col>\n    </Row>\n    <Row>\n      <Col>\n        <FormItem label=\"活动类型：\">\n          <Select\n            v-model=\"orderData.type\"\n           class=\"input-add\"\n            clearable\n            @on-change=\"typeChange\"\n            placeholder=\"全部\"\n          >\n            <Option value=\"0\">普通订单</Option>\n            <Option value=\"1\">秒杀订单</Option>\n            <Option value=\"2\">砍价订单</Option>\n            <Option value=\"3\">拼团订单</Option>\n            <Option value=\"4\">积分订单</Option>\n            <Option value=\"5\">套餐订单</Option>\n            <Option value=\"6\">预售订单</Option>\n            <Option value=\"7\">新人订单</Option>\n            <Option value=\"8\">抽奖订单</Option>\n            <Option value=\"9\">拼单订单</Option>\n            <Option value=\"10\">桌码订单</Option>\n          </Select>\n        </FormItem>\n      </Col>\n      <Col v-if=\"orderType == '1'\">\n        <FormItem label=\"选择门店：\">\n          <Select\n            v-model=\"orderData.store_id\"\n            clearable\n            filterable\n            @on-change=\"storeChange\"\n            class=\"input-add\"\n          >\n            <Option v-for=\"item in staffData\" :value=\"item.id\" :key=\"item.id\"\n              >{{ item.name }}\n            </Option>\n          </Select>\n        </FormItem>\n      </Col>\n      <Col v-if=\"orderType == '2'\">\n        <FormItem label=\"供应商：\">\n          <Select\n            v-model=\"orderData.supplier_id\"\n            clearable\n            filterable\n            @on-change=\"supplierChange\"\n           class=\"input-add\"\n          >\n            <Option\n              v-for=\"item in supplierName\"\n              :value=\"item.id\"\n              :key=\"item.id\"\n              >{{ item.supplier_name }}</Option\n            >\n          </Select>\n        </FormItem>\n      </Col>\n      <Col>\n        <FormItem label=\"订单搜索：\" prop=\"real_name\" label-for=\"real_name\">\n          <Input\n            v-model=\"orderData.real_name\"\n            placeholder=\"请输入\"\n            element-id=\"name\"\n            clearable\n           class=\"input-add\"\n            maxlength=\"20\"\n            @on-change=\"clearTap\"\n          >\n            <Select\n              v-model=\"orderData.field_key\"\n              slot=\"prepend\"\n              style=\"width: 80px\"\n              default-label=\"全部\"\n            >\n              <Option value=\"all\">全部</Option>\n              <Option value=\"order_id\">订单号</Option>\n              <Option value=\"uid\">用户UID</Option>\n              <Option value=\"real_name\">用户姓名</Option>\n              <Option value=\"user_phone\">用户电话</Option>\n              <Option value=\"title\">商品名称</Option>\n              <Option value=\"total_num\">商品件数</Option>\n            </Select>\n          </Input>\n        </FormItem>\n        <FormItem>\n          <Button\n            type=\"primary\"\n            @click=\"orderSearch(orderData.real_name)\"\n\n                          style=\"margin-left: -90px\"\n            >查询</Button\n          >\n        </FormItem>\n      </Col>\n    </Row>\n  </Form>\n</div>\n", null]}