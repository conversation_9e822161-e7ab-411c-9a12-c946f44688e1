{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\systemStore\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\systemStore\\index.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { storeApi, keyApi, storeAddApi, storeGetInfoApi } from '@/api/setting';\nimport { mapState } from 'vuex';\nimport city from '@/utils/city';\nimport uploadPictures from '@/components/uploadPictures';\nimport Setting from \"@/setting\";\nexport default {\n    name: 'systemStore',\n    components: { uploadPictures },\n    props: { },\n    data () {\n        const validatePhone = (rule, value, callback) => {\n            if (!value) {\n                return callback(new Error('请填写手机号'));\n            } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n                callback(new Error('手机号格式不正确!'));\n            } else {\n                callback();\n            }\n        };\n        const validateUpload = (rule, value, callback) => {\n            if (!this.formItem.image) {\n                callback(new Error('请上传提货点logo'))\n            } else {\n                callback()\n            }\n        };\n        return {\n          roterPre: Setting.roterPre,\n            isTemplate: false,\n            spinShow: false,\n            modalMap: false,\n            addresData: [],\n            formItem: {\n                name: '',\n                introduction: '',\n                phone: '',\n                address: [],\n                address2: [],\n                detailed_address: '',\n                valid_time: [],\n                day_time: [],\n                latlng: '',\n                id: 0\n            },\n            ruleValidate: {\n                name: [\n                    { required: true, message: '请输入提货点名称', trigger: 'blur' }\n                ],\n                mail: [\n                    { required: true, message: 'Mailbox cannot be empty', trigger: 'blur' },\n                    { type: 'email', message: 'Incorrect email format', trigger: 'blur' }\n                ],\n                address: [\n                    { required: true, message: '请选择提货点地址', type: 'array', trigger: 'change' }\n                ],\n                valid_time: [\n                    {\n                        required: true,\n                        type: 'array',\n                        message: '请选择核销时效',\n                        trigger: 'change',\n                        fields: {\n                            0: { type: 'date', required: true, message: '请选择年度范围' },\n                            1: { type: 'date', required: true, message: '请选择年度范围' }\n                        }\n                    }\n\n                ],\n                day_time: [\n                    { required: true, type: 'array', message: '请选择提货点营业时间', trigger: 'change' }\n                ],\n                phone: [\n                    { required: true, validator: validatePhone, trigger: 'blur' }\n                ],\n                detailed_address: [\n                    { required: true, message: '请输入详细地址', trigger: 'blur' }\n                ],\n                image: [\n                    { required: true, validator: validateUpload, trigger: 'change' }\n                ],\n                latlng: [\n                    { required: true, message: '请选择经纬度', trigger: 'blur' }\n                ]\n            },\n            keyUrl: '',\n            grid: {\n                xl: 20,\n                lg: 20,\n                md: 20,\n                sm: 24,\n                xs: 24\n            },\n            gridPic: {\n                xl: 6,\n                lg: 8,\n                md: 12,\n                sm: 12,\n                xs: 12\n            },\n            gridBtn: {\n                xl: 4,\n                lg: 8,\n                md: 8,\n                sm: 8,\n                xs: 8\n            },\n            modalPic: false,\n            isChoice: '单选'\n        }\n    },\n    created () {\n        let that = this;\n        city.map((item) => {\n            item.value = item.label;\n            if (item.children && item.children.length) {\n                item.children.map((j) => {\n                    j.value = j.label;\n                    if (j.children && j.children.length) {\n                        j.children.map((o) => {\n                            o.value = o.label;\n                        });\n                    }\n                });\n            }\n        });\n        setTimeout(function () {\n            that.addresData = city;\n        }, 10);\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 120;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'right';\n        }\n    },\n    mounted: function () {\n        window.addEventListener('message', function (event) {\n            // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息\n            var loc = event.data;\n            if (loc && loc.module === 'locationPicker') { // 防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'\n                window.parent.selectAdderss(loc);\n            }\n        }, false);\n        window.selectAdderss = this.selectAdderss;\n    },\n    methods: {\n        cancel () {\n            this.$refs['formItem'].resetFields();\n            this.clearFrom();\n        },\n        clearFrom () {\n            this.formItem.introduction = ''\n            this.formItem.day_time = []\n        },\n        // 选择经纬度\n        selectAdderss (data) {\n            this.formItem.latlng = data.latlng.lat + ',' + data.latlng.lng;\n            this.modalMap = false;\n        },\n        // key值\n        getKey () {\n            keyApi().then(async res => {\n                this.modalMap = true;\n                let keys = res.data.key;\n                this.keyUrl = `https://apis.map.qq.com/tools/locpicker?type=1&key=${keys}&referer=myapp`;\n            }).catch(res => {\n                this.$Modal.confirm({\n                    title: '提示',\n                    content: '<p>'+res.msg+'</p>',\n                    onOk: () => {\n                        this.$router.push({ path: this.roterPre + '/setting/system_config'});\n                    },\n                    onCancel: () => {\n                    }\n                });\n                // this.$Message.error(res.msg);\n            })\n        },\n        // 详情\n        getInfo (id) {\n            let that = this;\n            that.formItem.id = id;\n            that.spinShow = true;\n            storeGetInfoApi(id).then(res => {\n                let info = res.data.info || null;\n                that.formItem = info || that.formItem;\n                that.formItem.address = info.address2;\n                that.spinShow = false;\n            }).catch(function (res) {\n                that.spinShow = false;\n                that.$Message.error(res.msg);\n            })\n        },\n        // 选择图片\n        modalPicTap () {\n            this.modalPic = true;\n        },\n        // 选中图片\n        getPic (pc) {\n            this.formItem.image = pc.att_dir;\n            this.modalPic = false;\n        },\n        // 选择地址\n        handleChange (value, selectedData) {\n            this.formItem.address = selectedData.map(o => o.label);\n            //  this.formItem.address2 = selectedData.map(o => o.value);\n        },\n        // 核销时效\n        onchangeDate (e) {\n            this.formItem.valid_time = e;\n        },\n        // 营业时间\n        onchangeTime (e) {\n            this.formItem.day_time = e;\n        },\n        onSearch () {\n            this.getKey();\n        },\n        // 提交\n        handleSubmit (name) {\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                    storeAddApi(this.formItem).then(async res => {\n                        this.$Message.success(res.msg);\n                        this.isTemplate = false;\n                        this.$parent.getList();\n                        this.$refs[name].resetFields();\n                        this.clearFrom();\n                    }).catch(res => {\n                        this.$Message.error(res.msg);\n                    })\n                } else {\n                    return false;\n                }\n            })\n        }\n    }\n}\n", null]}