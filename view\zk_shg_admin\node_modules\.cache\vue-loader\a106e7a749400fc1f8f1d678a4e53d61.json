{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_cascader.vue?vue&type=template&id=33f4c2d5&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_cascader.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"slider-box\">\n        <div class=\"c_row-item\">\n            <Col class=\"label\" span=\"4\" v-if=\"configData.title\">\n                {{configData.title}}\n            </Col>\n            <Col span=\"19\" class=\"slider-box\">\n\t\t\t\t<Cascader\n\t\t\t\t\t :data=\"configData.list\"\n\t\t\t\t\t placeholder=\"请选择商品分类\"\n\t\t\t\t\t change-on-select\n\t\t\t\t\t v-model=\"configData.activeValue\"\n\t\t\t\t\t filterable\n\t\t\t\t\t @on-change=\"sliderChange\"\n\t\t\t\t></Cascader>\n            </Col>\n        </div>\n    </div>\n", null]}