<template>
  <div class="sales-stats-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">商品销售统计</h2>
        <p class="page-desc">查看各门店商品销售量、销售金额和当前库存统计</p>
      </div>
      <div class="header-right">
        <Button type="success" icon="ios-download" @click="exportSales">导出统计</Button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-container">
      <Form :model="filterForm" inline>
        <FormItem label="门店：">
          <Select v-model="filterForm.store_id" placeholder="选择门店" filterable clearable style="width: 200px">
            <Option
              v-for="store in storeList"
              :key="store.value"
              :value="store.value"
              :label="store.label">
              {{ store.label }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="日期范围：">
          <DatePicker
            v-model="dateRange"
            type="daterange"
            split-panels
            placeholder="选择日期范围"
            style="width: 300px"
            @on-change="handleDateChange" />
        </FormItem>

        <FormItem label="商品名称：">
          <Input
            v-model="filterForm.keyword"
            placeholder="输入商品名称"
            style="width: 200px"
            search
            enter-button
            @on-search="getSalesStatistics" />
        </FormItem>

        <FormItem>
          <Button type="primary" @click="getSalesStatistics">查询</Button>
          <Button @click="resetFilter" style="margin-left: 8px">重置</Button>
        </FormItem>
      </Form>
    </div>

    <!-- 动态表格 -->
    <Table
      :loading="tableLoading"
      :columns="columns"
      :data="tableData"
      border
      highlight-row
    >
    </Table>

    <!-- 分页 -->
    <div class="pagination-container">
      <Page
        :total="total"
        :current.sync="currentPage"
        :page-size="pageSize"
        :page-size-opts="[20, 50, 100, 200]"
        show-total
        show-sizer
        show-elevator
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange">
      </Page>
    </div>
  </div>
</template>

<script>
import { getSalesStatistics, getStoreOptions, exportSalesStatistics } from '@/api/erp';
import exportExcel from '@/utils/newToExcel.js'

export default {
  name: 'SalesStatistics',
  data() {
    return {
      // 筛选表单
      filterForm: {
        store_id: '',
        keyword: '',
        start_date: '',
        end_date: ''
      },
      // 日期范围
      dateRange: [],
      // 列表数据
      storeList: [],
      columns: [],
      tableData: [],
      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,
      // 状态
      tableLoading: false
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.fetchStoreOptions();
      await this.getSalesStatistics();
    },
    // 获取门店选项
    async fetchStoreOptions() {
      try {
        const { data } = await getStoreOptions();
        this.storeList = data || [];
      } catch (error) {
        console.error('获取门店列表失败:', error);
      }
    },
    // 获取销售统计数据
    async getSalesStatistics() {
      this.tableLoading = true;
      try {
        const params = {
          ...this.filterForm,
          page: this.currentPage,
          limit: this.pageSize
        };
        const { data } = await getSalesStatistics(params);
        this.tableData = data.data;
        this.columns = data.columns;
        this.total = data.total;
      } catch (error) {
        console.error('获取销售统计失败:', error);
        this.$Message.error('获取销售统计失败');
      } finally {
        this.tableLoading = false;
      }
    },
    // 处理日期变化
    handleDateChange(value) {
      if (value && value.length === 2) {
        this.filterForm.start_date = value[0];
        this.filterForm.end_date = value[1];
      } else {
        this.filterForm.start_date = '';
        this.filterForm.end_date = '';
      }
    },
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        store_id: '',
        keyword: '',
        start_date: '',
        end_date: ''
      };
      this.dateRange = [];
      this.currentPage = 1;
      this.getSalesStatistics();
    },
    // 分页变化
    handleCurrentChange(page) {
      this.currentPage = page;
      this.getSalesStatistics();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.getSalesStatistics();
    },
    // 导出销售数据
    async exportSales() {
      this.exportListOn = this.exportList.findIndex(
          (item) => item.name === value
      )
      let [th, filekey, data, fileName] = [[], [], [], '']
      let excelData = {
        ...this.where,
        page: 1,
        export_type: value,
        ids: this.checkUidList.join()
      }
      for (let i = 0; i < excelData.page; i++) {
        let lebData = await this.downOrderData(excelData)
        if (!lebData.export.length) {
          break;
        }
        if (!fileName) {
          fileName = lebData.filename
        }
        if (!filekey.length) {
          filekey = lebData.filekey
        }
        if (!th.length) {
          th = lebData.header
        }
        data = data.concat(lebData.export)
        excelData.page++
      }
      let sheetData = []
      for (let j = 0; j < data.length; j++) {
        let goodsList = data[j].goods_name.split('\n')
        for (let k = 0; k < goodsList.length; k++) {
          let row = {...data[j]}
          row.goods_name = goodsList[k]
          if (k) {
            for (const key in row) {
              if (Object.hasOwnProperty.call(row, key)) {
                if (key !== 'goods_name') {
                  row[key] = null
                }
              }
            }
          }
          sheetData.push(row)
        }
      }
      exportExcel(th, filekey, fileName, sheetData)
    }
  }
};
</script>

<style scoped lang="less">
.sales-stats-container {
  padding: 16px;
  background: #f5f7fa;
  min-height: calc(100vh - 64px);
}

// 页面头部
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;

  .header-left {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 8px 0;
    }

    .page-desc {
      font-size: 14px;
      color: #6b7280;
      margin: 0;
    }
  }
}

// 筛选容器
.filter-container {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;

  :deep(.ivu-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;

    .ivu-form-item-label {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
    }
  }
}

// 表格与分页
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 响应式设计
@media (max-width: 768px) {
  .sales-stats-container {
    padding: 8px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
</style>
