{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\discount\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\discount\\index.vue", "mtime": 1684232862000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import { mapState } from 'vuex';\n    import { discountList, discountsetStatus } from '@/api/marketing';\n    import Setting from \"@/setting\";\n    export default {\n        name: \"discount\",\n        data() {\n            return {\n              roterPre: Setting.roterPre,\n                grid: {\n                    xl: 7,\n                    lg: 7,\n                    md: 12,\n                    sm: 24,\n                    xs: 24\n                },\n                loading: false,\n                columns1: [\n                    {\n                        title: 'ID',\n                        key: 'id',\n                        width: 80\n                    },\n                    {\n                        title: '活动名称',\n                        key: 'name',\n                        minWidth: 100\n                    },\n                    {\n                      title: '参与商品数',\n                      key: 'product_count',\n                      minWidth: 100\n                    },\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '订单实付金额(元)',\n\t\t\t\t\t\t\t\t\t\t\t\tkey: 'sum_pay_price',\n                        minWidth: 100\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '优惠总金额',\n\t\t\t\t\t\t\t\t\t\t\t\tkey: 'sum_promotions_price',\n\t\t\t\t\t\t\t\t\t\t\t\tminWidth: 100\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t    title: '支付订单',\n\t\t\t\t\t\t\t\t\t\t    key: 'sum_order',\n\t\t\t\t\t\t\t\t\t\t    minWidth: 100\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '参与客户',\n\t\t\t\t\t\t\t\t\t\t\t\tkey: 'sum_user',\n                      minWidth: 100\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '老成交用户数',\n\t\t\t\t\t\t\t\t\t\t\t\tkey: 'old_user',\n\t\t\t\t\t\t\t\t\t\t\t\tminWidth: 100\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '新成交用户数',\n\t\t\t\t\t\t\t\t\t\t\t\tkey: 'new_user',\n                      minWidth: 100\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '是否开启',\n\t\t\t\t\t\t\t\t\t\t\t\tslot: 'status',\n\t\t\t\t\t\t\t\t\t\t\t\tminWidth: 100\n\t\t\t\t\t\t\t\t\t\t},\n                    {\n                        title: '操作',\n                        slot: 'action',\n                        fixed: 'right',\n                        minWidth: 120\n                    }\n                ],\n                discountFrom: {\n                    page: 1,\n                    limit: 15,\n\t\t\t\t\t          name:'',\n\t\t\t\t\t\t\t\t\t\tstatus:''\n                },\n                list: [],\n                total:0\n            }\n        },\n        computed: {\n            ...mapState('admin/layout', [\n                'isMobile'\n            ]),\n            labelWidth () {\n                return this.isMobile ? undefined : 96;\n            },\n            labelPosition () {\n                return this.isMobile ? 'top' : 'right';\n            }\n        },\n        created () {\n            this.getList();\n        },\n        methods:{\n\t\t\t\t\t  // 删除\n\t\t\t\t\t  del(row, tit, num) {\n\t\t\t\t\t    let delfromData = {\n\t\t\t\t\t      title: tit,\n\t\t\t\t\t      num: num,\n\t\t\t\t\t      url: `marketing/promotions/del/${row.id}`,\n\t\t\t\t\t      method: \"DELETE\",\n\t\t\t\t\t      ids: \"\",\n\t\t\t\t\t    };\n\t\t\t\t\t    this.$modalSure(delfromData)\n\t\t\t\t\t      .then((res) => {\n\t\t\t\t\t        this.$Message.success(res.msg);\n                  this.list.splice(num, 1);\n                  if (!this.list.length) {\n                    this.discountFrom.page =\n                        this.discountFrom.page == 1 ? 1 : this.discountFrom.page - 1;\n                  }\n\t\t\t\t\t        this.getList();\n\t\t\t\t\t      })\n\t\t\t\t\t      .catch((res) => {\n\t\t\t\t\t        this.$Message.error(res.msg);\n\t\t\t\t\t      });\n\t\t\t\t\t  },\n\t\t\t\t\t  onchangeIsShow (row) {\n\t\t\t\t\t\t\tdiscountsetStatus(row.id,row.status).then(res=>{\n\t\t\t\t\t\t\t\tthis.$Message.success(res.msg);\n\t\t\t\t\t\t\t}).catch(err=>{\n\t\t\t\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t},\n\t\t\t\t\t\t// 添加\n\t\t\t\t\t\tadd () {\n\t\t\t\t\t\t    this.$router.push({ path: this.roterPre + \"/marketing/discount/add/\" + 0 });\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdiscountSearchs(){\n\t\t\t\t\t\t\tthis.discountFrom.page = 1;\n\t\t\t\t\t\t\tthis.list = [];\n\t\t\t\t\t\t\tthis.getList();\n\t\t\t\t\t\t},\n            // 列表\n            getList () {\n                this.loading = true;\n                discountList(1,this.discountFrom).then(res => {\n                    let data = res.data;\n                    this.list = data.list;\n                    this.total = data.count;\n                    this.loading = false;\n                }).catch(err => {\n                    this.loading = false;\n                    this.$Message.error(err.msg);\n                })\n            },\n            pageChange (index) {\n                this.discountFrom.page = index;\n                this.getList();\n            },\n\t\t\t// 搜索\n\t\t\torderSearch() {\n\t\t\t\tthis.discountFrom.page = 1;\n\t\t\t\tthis.getList();\n\t\t\t},\n            //修改\n            edit(id){\n\t\t\t\t\t\t\t  this.$router.push({ path: this.roterPre + \"/marketing/discount/add/\" + id });\n            }\n        }\n    }\n", null]}