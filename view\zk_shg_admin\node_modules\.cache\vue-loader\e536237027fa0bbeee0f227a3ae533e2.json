{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue", "mtime": 1709630624583}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nimport {\n  supplierFinanceInfo,\n  supplierFlowingWaterMark,\n  getSupplierList,\n  exportTableList,\n} from '@/api/supplier';\nimport exportExcel from '@/utils/newToExcel.js';\nimport timeOptions from '@/utils/timeOptions';\nexport default {\n  name: 'orderStatistics',\n  data() {\n    return {\n      cardLists: [],\n      extractStatistics: {},\n      // 旧\n      modalmark: false,\n      remarks: {\n        mark: '',\n      },\n      supplierList: [],\n      total: 0,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      loading: false,\n      columns: [\n        {\n          title: '交易单号',\n          key: 'order_id',\n          minWidth: 180,\n        },\n        {\n          title: '关联订单',\n          key: 'link_id',\n          minWidth: 180,\n        },\n        {\n          title: '交易时间',\n          key: 'trade_time',\n          minWidth: 150,\n        },\n        {\n          title: '交易金额',\n          slot: 'number',\n          minWidth: 80,\n        },\n        {\n          title: '交易人',\n          slot: 'user_nickname',\n          ellipsis: true,\n          minWidth: 80,\n        },\n        {\n          title: '供应商',\n          key: 'supplier_name',\n          minWidth: 80,\n        },\n        {\n          title: '交易类型',\n          key: 'type_name',\n          minWidth: 80,\n        },\n        {\n          title: '支付方式',\n          key: 'pay_type_name',\n          minWidth: 80,\n        },\n        {\n          title: '备注',\n          key: 'remark',\n          minWidth: 120,\n        },\n        {\n          title: '操作',\n          slot: 'action',\n          fixed: 'right',\n          minWidth: 80,\n          align: 'center',\n        },\n      ],\n      orderList: [],\n      formValidate: {\n        supplier_id: 2,\n        keyword: '',\n        data: '',\n        page: 1,\n        limit: 20,\n      },\n      timeVal: [],\n      options: timeOptions,\n    };\n  },\n  computed: {\n    ...mapState('admin/layout', ['isMobile']),\n    labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition() {\n      return this.isMobile ? 'top' : 'left';\n    },\n  },\n  mounted() {\n    this.getList();\n    this.getSupplierList();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      supplierFinanceInfo(this.formValidate).then((res) => {\n        this.orderList = res.data.list;\n        this.total = res.data.count;\n        this.loading = false;\n      });\n    },\n    getSupplierList() {\n      getSupplierList().then((res) => {\n        this.supplierList = res.data;\n      });\n    },\n    search() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    reset() {\n      this.formValidate = {\n        supplier_id: '',\n        keyword: '',\n        data: '',\n        page: 1,\n        limit: 20,\n      };\n      this.timeVal = [];\n      this.getList();\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.formValidate.page = 1;\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join('-') : '';\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    //分页\n    pageChange(status) {\n      this.formValidate.page = status;\n      this.getList();\n    },\n    remark(e) {\n      this.remarkId = e.id;\n      this.modalmark = true;\n      this.remarks.mark = e.remark;\n    },\n    //备注的提交\n    putRemark() {\n      this.modalmark = false;\n      supplierFlowingWaterMark(this.remarkId, this.remarks)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.remarks = { mark: '' };\n          this.modalmark = false;\n          this.getList();\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n          this.modalmark = false;\n        });\n    },\n    // 取消备注按钮\n    cancel() {\n      this.remarks = { mark: '' };\n      this.modalmark = false;\n    },\n    //导出\n    async exports() {\n      let [th, filekey, data, fileName] = [[], [], [], ''];\n      let supplier_id = this.formValidate.supplier_id,\n        keyword = this.formValidate.keyword,\n        date = this.formValidate.data;\n      let lebData = await this.getExcelData({ supplier_id, keyword, date });\n      if (!fileName) fileName = lebData.filename;\n      filekey = lebData.filekey;\n      if (!th.length) th = lebData.header; //表头\n      data = data.concat(lebData.export);\n      exportExcel(th, filekey, fileName, data);\n    },\n    getExcelData(data) {\n      return new Promise((resolve) => {\n        exportTableList(data).then((res) => resolve(res.data));\n      });\n    },\n  },\n};\n", null]}