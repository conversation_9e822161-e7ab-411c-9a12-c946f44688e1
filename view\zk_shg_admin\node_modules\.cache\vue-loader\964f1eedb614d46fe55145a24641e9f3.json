{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\index.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {\n  userMemberBatch,\n  memberBatchSave,\n  memberBatchSetValue,\n  exportMemberCard,\n  userMemberScan,\n} from \"@/api/user\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      columns: [\n        {\n          title: \"编号\",\n          key: \"id\",\n        },\n        {\n          title: \"批次名称\",\n          key: \"title\",\n        },\n        {\n          title: \"体验天数\",\n          key: \"use_day\",\n        },\n        {\n          title: \"发卡总数量\",\n          key: \"total_num\",\n        },\n        {\n          title: \"使用数量\",\n          key: \"use_num\",\n        },\n        {\n          title: \"制卡时间\",\n          key: \"add_time\",\n        },\n        {\n          title: \"是否激活\",\n          slot: \"status\",\n        },\n        {\n          title: \"备注\",\n          key: \"remark\",\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n        },\n      ],\n      tbody: [],\n      total: 0,\n      gradeFrom: {\n        title: \"\",\n        page: 1,\n        limit: 15,\n      },\n      loading: false,\n      modal: false,\n      rule: [\n        {\n          type: \"input\",\n          field: \"title\",\n          title: \"批次名称\",\n          validate: [\n            {\n              required: true,\n              message: \"请输入批次名称\",\n              trigger: \"blur\",\n            },\n          ],\n        },\n        {\n          type: \"InputNumber\",\n          field: \"total_num\",\n          title: \"制卡数量\",\n          value: 1,\n          props: {\n            min: 1,\n          },\n        },\n        {\n          type: \"InputNumber\",\n          field: \"use_day\",\n          title: \"体验天数\",\n          value: 1,\n          props: {\n            min: 1,\n          },\n        },\n        {\n          type: \"radio\",\n          field: \"status\",\n          title: \"是否激活\",\n          value: \"0\",\n          options: [\n            {\n              value: \"0\",\n              label: \"冻结\",\n            },\n            {\n              value: \"1\",\n              label: \"激活\",\n            },\n          ],\n        },\n        {\n          type: \"input\",\n          field: \"remark\",\n          title: \"备注\",\n          props: {\n            type: \"textarea\",\n          },\n        },\n      ],\n      modal2: false,\n      rule2: [\n        {\n          type: \"hidden\",\n          field: \"id\",\n          value: \"\",\n        },\n        {\n          type: \"input\",\n          field: \"title\",\n          title: \"批次名称\",\n          value: \"\",\n          validate: [\n            {\n              required: true,\n              message: \"请输入批次名称\",\n              trigger: \"blur\",\n            },\n          ],\n        },\n      ],\n      modal3: false,\n      qrcode: null,\n      fapi: {},\n    };\n  },\n  computed: {\n\t\t...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 75;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getMemberBatch(this.gradeFrom);\n  },\n  methods: {\n    // 批次列表\n    getMemberBatch() {\n      this.loading = true;\n      userMemberBatch(this.gradeFrom)\n        .then((res) => {\n          this.loading = false;\n          this.tbody = res.data.list;\n          this.total = res.data.count;\n        })\n        .catch((err) => {\n          this.loading = false;\n          this.$Message.error(err.msg);\n        });\n    },\n    // 批次名称查询\n    userSearchs() {\n      this.gradeFrom.page = 1;\n      this.getMemberBatch();\n    },\n    // 激活 | 冻结\n    onchangeIsShow(row) {\n      memberBatchSetValue(row.id, {\n        field: \"status\",\n        value: row.status,\n      })\n        .then((res) => {\n          this.$Message.success(res.msg);\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    // 查看\n    // exportExcel(row) {\n    //     this.$Spin.show();\n    //     exportMemberCard(row.id)\n    //         .then(res => {\n    //             this.$Spin.hide();\n    //             location.href = res.data[0];\n    //         })\n    //         .catch(err => {\n    //             this.$Spin.hide();\n    //             this.$Message.error(err.msg);\n    //         });\n    // },\n    // 数据导出；\n    async exports(row) {\n      let [th, filekey, data] = [[], [], []];\n      let fileName = \"\";\n      let excelData = JSON.parse(JSON.stringify(this.gradeFrom));\n      excelData.page = 1;\n      for (let i = 0; i < excelData.page + 1; i++) {\n        let lebData = await this.getExcelData(row.id, excelData);\n        if (!fileName) fileName = lebData.filename;\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) th = lebData.header;\n        if (lebData.export.length) {\n          data = data.concat(lebData.export);\n          excelData.page++;\n        } else {\n          exportExcel(th, filekey, fileName, data);\n          return;\n        }\n      }\n    },\n    getExcelData(id, excelData) {\n      return new Promise((resolve, reject) => {\n        exportMemberCard(id, excelData).then((res) => {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 更多\n    changeMenu(row, name) {\n      switch (name) {\n        case \"1\":\n          this.rule2[0].value = row.id;\n          this.rule2[1].value = row.title;\n          this.modal2 = true;\n          break;\n        case \"2\":\n          this.$router.push({\n            path: `${this.roterPre}/vipuser/grade/list/${row.id}`,\n          });\n          break;\n        case \"3\":\n          this.exports(row);\n          break;\n      }\n    },\n    // 分页\n    pageChange(index) {\n      this.gradeFrom.page = index;\n      this.getMemberBatch();\n    },\n    // 添加批次弹窗\n    addBatch() {\n      this.fapi.resetFields();\n      this.modal = true;\n    },\n    // 提交批次\n    onSubmit(formData) {\n      memberBatchSave(0, formData)\n        .then((res) => {\n          this.modal = false;\n          this.$Message.success(res.msg);\n          this.getMemberBatch();\n          this.fapi.resetFields();\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    onSubmit2(formData) {\n      memberBatchSetValue(formData.id, {\n        field: \"title\",\n        value: formData.title,\n      })\n        .then((res) => {\n          this.modal2 = false;\n          this.$Message.success(res.msg);\n          this.getMemberBatch();\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    // 会员卡二维码\n    getMemberScan() {\n      this.$Spin.show();\n      userMemberScan()\n        .then((res) => {\n          this.$Spin.hide();\n          this.qrcode = res.data;\n          this.modal3 = true;\n        })\n        .catch((err) => {\n          this.$Spin.hide();\n          this.$Message.error(err.msg);\n        });\n    },\n  },\n};\n", null]}