{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_imgs.vue?vue&type=template&id=d456676e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_imgs.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"hot_imgs\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"\\n        最多可添加4个版块，图片建议尺寸140 * 140px；鼠标拖拽左侧圆点可\\n        调整版块顺序\\n    \")]),_c('div',{staticClass:\"list-box\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.defaults.menu,\"group\":\"people\",\"handle\":\".move-icon\"}},_vm._l((_vm.defaults.menu),function(item,index){return _c('div',{key:index,staticClass:\"item\"},[_c('div',{staticClass:\"move-icon\"},[_c('Icon',{attrs:{\"type\":\"ios-keypad-outline\",\"size\":\"22\"}})],1),_c('div',{staticClass:\"img-box\",on:{\"click\":function($event){return _vm.modalPicTap('单选',index)}}},[(item.img)?_c('img',{attrs:{\"src\":item.img,\"alt\":\"\"}}):_c('div',{staticClass:\"upload-box\"},[_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"36\"}})],1),_c('div',[_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"上传图片\",\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1)],1)]),_c('div',{staticClass:\"info\"},_vm._l((item.info),function(infos,key){return _c('div',{key:key,staticClass:\"info-item\"},[_c('span',[_vm._v(_vm._s(infos.title))]),_c('div',{staticClass:\"input-box\"},[_c('Input',{attrs:{\"placeholder\":infos.tips,\"maxlength\":infos.max},model:{value:(infos.value),callback:function ($$v) {_vm.$set(infos, \"value\", $$v)},expression:\"infos.value\"}})],1)])}),0)])}),0)],1),(_vm.defaults.menu.length < 4)?_c('div',{staticClass:\"add-btn\"},[_c('Button',{staticStyle:{\"width\":\"100%\",\"height\":\"40px\"},on:{\"click\":_vm.addBox}},[_vm._v(\"添加板块\")])],1):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}