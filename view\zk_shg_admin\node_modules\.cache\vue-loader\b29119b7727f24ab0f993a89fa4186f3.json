{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_classify.vue?vue&type=template&id=0ee2be9c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_classify.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"slider-box\">\n        <div class=\"c_row-item\" v-if=\"configData.title\">\n            <Col class=\"label\" span=\"4\">\n                {{configData.title}}\n            </Col>\n            <Col span=\"18\">\n\t\t\t\t<el-cascader\n\t\t\t\t        @change=\"sliderChange\"\n\t\t\t\t        placeholder=\"请选择分类\"\n\t\t\t\t        size=\"mini\"\n\t\t\t\t        v-model=\"configData.classVal\"\n\t\t\t\t        :options=\"treeSelect\"\n\t\t\t\t        :props=\"props\"\n\t\t\t\t        filterable\n\t\t\t\t        clearable>\n\t\t\t\t</el-cascader>\n            </Col>\n        </div>\n    </div>\n", null]}