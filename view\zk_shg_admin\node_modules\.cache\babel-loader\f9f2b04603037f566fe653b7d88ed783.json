{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\authGroup\\index.vue", "mtime": 1685091240000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { getGroupChatList } from \"@/api/work\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"\",\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      formInline: {},\n      options: timeOptions,\n      columns1: [{\n        title: \"二维码\",\n        slot: \"qr_code\",\n        minWidth: 80,\n        align: 'center'\n      }, {\n        title: \"二维码名称\",\n        key: \"name\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"群名称\",\n        key: \"group_name\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"群聊\",\n        slot: \"chat_list\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"自动创建\",\n        slot: \"auth_group_chat\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"标签\",\n        slot: \"label_list\",\n        minWidth: 100,\n        align: 'center'\n      }, {\n        title: \"创建时间\",\n        key: \"create_time\",\n        minWidth: 130,\n        align: 'center'\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        // fixed: \"right\",\n        minWidth: 130,\n        align: 'center'\n      }],\n      tableData: [],\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      timeVal: [],\n      tableFrom: {\n        name: \"\",\n        create_time: \"\",\n        page: 1,\n        limit: 15\n      },\n      total: 0\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    }\n  }),\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n\n      this.loading = true;\n      getGroupChatList(this.tableFrom).then(function (res) {\n        _this.tableData = res.data;\n        _this.loading = false;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n\n        _this.loading = false;\n      });\n    },\n    search: function search() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.create_time = this.timeVal.join(\"-\");\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    downItem: function downItem(row, index) {\n      var link = document.createElement('a');\n      var url = row.qr_code; //codeIMG  要下载的路径\n      // 这里是将url转成blob地址，\n\n      fetch(url).then(function (res) {\n        return res.blob();\n      }).then(function (blob) {\n        // 将链接地址字符内容转变成blob地址\n        link.href = URL.createObjectURL(blob);\n        link.download = row.name;\n        document.body.appendChild(link);\n        link.click();\n      });\n    },\n    editData: function editData(row, index) {\n      this.$router.push({\n        path: this.roterPre + \"/work/addAuthGroup/\" + row.id\n      });\n    },\n    delItem: function delItem(row, index) {\n      var _this2 = this;\n\n      var delfromData = {\n        title: '删除该自动拉群',\n        num: index,\n        url: \"work/group_chat_auth/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this2.$Message.success(res.msg);\n\n        _this2.tableData.list.splice(index, 1);\n\n        if (!_this2.tableData.list.length) {\n          _this2.tableFrom.page = _this2.tableFrom.page == 1 ? 1 : _this2.tableFrom.page - 1;\n        }\n\n        _this2.getList();\n      }).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    }\n  }\n};", null]}