{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue?vue&type=template&id=745b9304&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue", "mtime": 1698031562000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\t<div>\n\t\t<Drawer :closable=\"false\" width=\"1000\" class-name=\"order_box\" v-model=\"modals\" :styles=\"{ padding: 0 }\">\n\t\t    <div v-if=\"orderDatalist\">\n\t\t        <div class=\"head\">\n\t\t            <div class=\"full\">\n\t\t                <Icon custom=\"iconfont icondingdan\" size=\"60\"/>\n\t\t                <div class=\"text\">\n\t\t                    <div class=\"title\">积分订单</div>\n\t\t                    <div>订单编号：{{ orderDatalist.orderInfo.order_id }}</div>\n\t\t                </div>\n\t\t                <div>\n\t\t                    <Button :class=\"openErp?'on':''\" :disabled=\"openErp\" v-if=\"orderData.status === 1\" @click=\"sendOrder\">发送货</Button>\n\t\t                    <Button :class=\"openErp?'on':''\" :disabled=\"openErp\" v-if=\"orderData.status === 2\" @click=\"delivery\">配送信息</Button>\n\t\t                    <Button v-if=\"orderData.status >= 1\" @click=\"changeMenu('10')\">小票打印</Button>\n\t\t\t\t\t\t\t<Button :class=\"openErp?'on':''\" :disabled=\"openErp\" v-if=\"orderData.status >= 1 && orderData.express_dump\" @click=\"changeMenu('11')\">电子面单打印</Button>\n\t\t                    <Button :class=\"openErp?'on':''\" :disabled=\"openErp\" v-if=\"orderData.is_del == 1\" @click=\"changeMenu('9')\">删除订单</Button>\n\t\t                    <Dropdown @on-click=\"changeMenu\" v-if=\"orderData.status !== 4\">\n\t\t                        <Button icon=\"ios-more\"></Button>\n\t\t                        <DropdownMenu slot=\"list\">\n\t\t                            <DropdownItem v-if=\"orderData.status !== 4\" name=\"4\">订单备注</DropdownItem>\n\t\t                            <DropdownItem :disabled=\"openErp\" v-if=\"orderData.status === 2\" name=\"8\">已收货</DropdownItem>\n\t\t                        </DropdownMenu>\n\t\t                    </Dropdown>\n\t\t                </div>\n\t\t            </div>\n\t\t            <ul class=\"list\">\n\t\t                <li class=\"item\">\n\t\t                    <div class=\"title\">订单状态</div>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"value1\">{{orderDatalist.orderInfo.status_name || '-'}}</div>\n\t\t                </li>\n\t\t                <li class=\"item\">\n\t\t                    <div class=\"title\">兑换积分</div>\n\t\t                    <div>{{ orderDatalist.orderInfo.integral || '-'}}</div>\n\t\t                </li>\n                    <li class=\"item\">\n                      <div class=\"title\">兑换金额</div>\n                      <div>{{ orderDatalist.orderInfo.price || '-'}}</div>\n                    </li>\n\t\t                <!-- <li class=\"item\">\n\t\t                    <div class=\"title\">支付方式</div>\n\t\t                    <div>{{ orderDatalist.orderInfo._status._payType }}</div>\n\t\t                </li> -->\n\t\t                <li class=\"item\">\n\t\t                    <div class=\"title\">兑换时间</div>\n\t\t                    <div>{{ orderDatalist.orderInfo.add_time || '-' }}</div>\n\t\t                </li>\n\t\t            </ul>\n\t\t        </div>\n\t\t        <Tabs v-model=\"activeName\">\n\t\t            <TabPane label=\"订单信息\" name=\"detail\">\n\t\t                <div class=\"section\">\n\t\t                    <div class=\"title\">用户信息</div>\n\t\t                    <ul class=\"list\">\n\t\t                        <li class=\"item\">\n\t\t                            <div>用户昵称：</div>\n\t\t                            <div class=\"value\">{{orderDatalist.userInfo.uid?orderDatalist.userInfo.nickname:'游客'}}</div>\n\t\t                        </li>\n\t\t                        <li class=\"item\">\n\t\t                            <div>绑定电话：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.userInfo.phone || '-'}}</div>\n\t\t                        </li>\n\t\t                    </ul>\n\t\t                </div>\n\t\t                <div class=\"section\">\n\t\t                    <div class=\"title\">收货信息</div>\n\t\t                    <ul class=\"list\">\n\t\t                        <!-- <li class=\"item\">\n\t\t                            <div>用户昵称：</div>\n\t\t                            <div class=\"value\">{{orderDatalist.userInfo.uid?orderDatalist.userInfo.nickname:'游客'}}</div>\n\t\t                        </li> -->\n\t\t                        <li class=\"item\">\n\t\t                            <div>收货人：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo.real_name || '-'}}</div>\n\t\t                        </li>\n\t\t                        <li class=\"item\">\n\t\t                            <div>收货电话：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo.user_phone || '-'}}</div>\n\t\t                        </li>\n\t\t                        <li class=\"item\">\n\t\t                            <div>收货地址：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo.user_address || '-'}}</div>\n\t\t                        </li>\n\t\t                    </ul>\n\t\t                </div>\n\t\t                <div class=\"section\" v-if=\"orderDatalist.orderInfo.fictitious_content && orderDatalist.orderInfo.product_type !=1\">\n\t\t                    <div class=\"title\">虚拟发货</div>\n\t\t                    <ul class=\"list\">\n\t\t                        <li class=\"item\">\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo.fictitious_content }}</div>\n\t\t                        </li>\n\t\t                    </ul>\n\t\t                </div>\n\t\t                <div class=\"section\" v-if=\"orderDatalist.orderInfo.product_type ==1\">\n\t\t                    <div class=\"title\">卡密发货</div>\n\t\t\t\t\t\t\t<div v-if='orderDatalist.orderInfo.virtual.length'>\n\t\t\t\t\t\t\t\t<div class=\"list\" v-for=\"(item,index) in orderDatalist.orderInfo.virtual\" :key='index'>\n\t\t\t\t\t\t\t\t    <div class=\"item\">\n\t\t\t\t\t\t\t\t        <div>卡号{{index+1}}：</div>\n\t\t\t\t\t\t\t\t        <div class=\"value\">{{item.card_no}}</div>\n\t\t\t\t\t\t\t\t    </div>\n\t\t\t\t\t\t\t\t\t<div class=\"item\">\n\t\t\t\t\t\t\t\t\t    <div>密码{{index+1}}：</div>\n\t\t\t\t\t\t\t\t\t    <div class=\"value\">{{item.card_pwd}}</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<ul class=\"list\" v-else>\n\t\t\t\t\t\t\t    <li class=\"item\">\n\t\t\t\t\t\t\t        <div class=\"value\">{{ orderDatalist.orderInfo.virtual_info || '-'}}</div>\n\t\t\t\t\t\t\t    </li>\n\t\t\t\t\t\t\t</ul>\n\t\t                </div>\n\t\t                <div class=\"section\">\n\t\t                    <div class=\"title\">订单信息</div>\n\t\t                    <ul class=\"list\">\n\t\t                        <li class=\"item\">\n\t\t                            <div>创建时间：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo._add_time || '-'}}</div>\n\t\t                        </li>\n\t\t                        <li class=\"item\">\n\t\t                            <div>商品总数：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo.total_num || '-'}}</div>\n\t\t                        </li>\n\t\t                        <li class=\"item\">\n\t\t                            <div>商品兑换积分：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo.total_integral || '-'}}</div>\n\t\t                        </li>\n                          <li class=\"item\">\n                            <div>商品兑换金额：</div>\n                            <div class=\"value\">{{ orderDatalist.orderInfo.total_price || '-'}}</div>\n                          </li>\n\t\t                    </ul>\n\t\t                </div>\n\t\t\t\t\t\t<div class=\"section\" v-if=\"orderDatalist.orderInfo.delivery_type==='express'\">\n\t\t\t\t\t\t    <div class=\"title\">物流信息</div>\n\t\t\t\t\t\t    <ul class=\"list\">\n\t\t\t\t\t\t        <li class=\"item\">\n\t\t\t\t\t\t            <div>快递公司：</div>\n\t\t\t\t\t\t            <div class=\"value\">{{orderDatalist.orderInfo.delivery_name || '-'}}</div>\n\t\t\t\t\t\t        </li>\n\t\t\t\t\t\t\t\t<li class=\"item\">\n\t\t\t\t\t\t\t\t    <div>快递单号：</div>\n\t\t\t\t\t\t\t\t    <div class=\"value\">{{orderDatalist.orderInfo.delivery_id}}<span class=\"logisticsLook\" @click=\"openLogistics\">查询</span></div>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t    </ul>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"section\" v-if=\"orderDatalist.orderInfo.delivery_type==='send'\">\n\t\t\t\t\t\t    <div class=\"title\">配送信息</div>\n\t\t\t\t\t\t    <ul class=\"list\">\n\t\t\t\t\t\t        <li class=\"item\">\n\t\t\t\t\t\t            <div>送货人姓名：</div>\n\t\t\t\t\t\t            <div class=\"value\">{{orderDatalist.orderInfo.delivery_name || '-'}}</div>\n\t\t\t\t\t\t        </li>\n\t\t\t\t\t\t\t\t<li class=\"item\">\n\t\t\t\t\t\t\t\t    <div>送货人电话：</div>\n\t\t\t\t\t\t\t\t    <div class=\"value\">{{orderDatalist.orderInfo.delivery_id || '-'}}</div>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t    </ul>\n\t\t\t\t\t\t</div>\n\t\t                <div v-if=\"orderDatalist.orderInfo.custom_form.length\" class=\"section\">\n\t\t                    <div class=\"title\">自定义留言</div>\n\t\t                    <ul class=\"list\">\n\t\t                        <li v-for=\"(item, index) in orderDatalist.orderInfo.custom_form\" :key=\"index\" class=\"item\" v-if=\"(item.value && ['uploadPicture','dateranges'].indexOf(item.name) == -1) || (item.value.length && ['uploadPicture','dateranges'].indexOf(item.name) != -1)\">\n\t\t                            <div class=\"txtVal\">{{ item.titleConfig.value }}：</div>\n                                <div v-if=\"item.name == 'dateranges'\" class=\"value\">{{item.value[0]+'/'+item.value[1]}}</div>\n\t\t                            <div v-else-if=\"item.name === 'uploadPicture'\" class=\"value\">\n\t\t                                <div v-for=\"(img, i) in item.value\" :key=\"i\" class=\"image\" v-viewer>\n\t\t                                    <img v-lazy=\"img\">\n\t\t                                </div>\n\t\t                            </div>\n\t\t                            <div v-else class=\"value\">{{ item.value || '-' }}</div>\n\t\t                        </li>\n\t\t                    </ul>\n\t\t                </div>\n\t\t                <div class=\"section\" v-if=\"orderDatalist.orderInfo.mark\">\n\t\t                    <div class=\"title\">买家留言</div>\n\t\t                    <ul class=\"list\">\n\t\t                        <li class=\"item\">\n\t\t                            <div class=\"value\">{{orderDatalist.orderInfo.mark}}</div>\n\t\t                        </li>\n\t\t                    </ul>\n\t\t                </div>\n\t\t                <div class=\"section\" v-if=\"orderDatalist.orderInfo.remark\">\n\t\t                    <div class=\"title\">订单备注</div>\n\t\t                    <ul class=\"list\">\n\t\t                        <li class=\"item\">\n\t\t                            <div>备注：</div>\n\t\t                            <div class=\"value\">{{ orderDatalist.orderInfo.remark || '-' }}</div>\n\t\t                        </li>\n\t\t                    </ul>\n\t\t                </div>\n\t\t            </TabPane>\n\t\t            <TabPane label=\"商品信息\" name=\"product\">\n\t\t                <Table :columns=\"columns1\" :data=\"orderDatalist.orderInfo.cart_info\" border highlight-row>\n\t\t                    <template slot-scope=\"{ row }\" slot=\"product\">\n\t\t                        <div class=\"product\">\n\t\t                            <div class=\"image\" v-viewer>\n\t\t                                <img v-lazy=\"row.attrInfo ? row.attrInfo.image : row.image\">\n\t\t                            </div>\n\t\t                            <div class=\"title\">{{ row.title }} | {{ row.attrInfo ? row.attrInfo.suk : '' }}</div>\n\t\t                        </div>\n\t\t                    </template>\n                        <template slot-scope=\"{ row }\" slot=\"price\">\n                          <div>{{row.attrInfo?row.attrInfo.price:row.price}}</div>\n                        </template>\n                        <template slot-scope=\"{ row }\" slot=\"integral\">\n                          <div>{{row.attrInfo?row.attrInfo.integral:row.integral}}</div>\n                        </template>\n\t\t                </Table>\n\t\t            </TabPane>\n\t\t            <TabPane label=\"订单记录\" name=\"record\">\n\t\t                <Table :columns=\"columns2\" :data=\"recordData\" border :loading=\"loading\" no-data-text=\"暂无数据\" highlight-row   no-filtered-data-text=\"暂无筛选结果\"></Table>\n\t\t            </TabPane>\n\t\t        </Tabs>\n\t\t    </div>\n\t\t</Drawer>\n\t\t<Modal v-model=\"modal2\" scrollable title=\"物流查询\"  width=\"350\" class=\"order_box2\">\n\t\t    <div class=\"logistics acea-row row-top\" v-if=\"orderDatalist\">\n\t\t        <div class=\"logistics_img\"><img src=\"../../../../assets/images/expressi.jpg\"></div>\n\t\t        <div class=\"logistics_cent\">\n\t\t            <span>物流公司：{{orderDatalist.orderInfo.delivery_name}}</span>\n\t\t            <span>物流单号：{{orderDatalist.orderInfo.delivery_id}}</span>\n\t\t        </div>\n\t\t    </div>\n\t\t    <div class=\"acea-row row-column-around trees-coadd\">\n\t\t        <div class=\"scollhide\">\n\t\t            <Timeline>\n\t\t                <TimelineItem v-for=\"(item,i) in result\" :key=\"i\">\n\t\t                    <p class=\"time\" v-text=\"item.time\"></p>\n\t\t                    <p class=\"content\" v-text=\"item.status\"></p>\n\t\t                </TimelineItem>\n\t\t            </Timeline>\n\t\t        </div>\n\t\t    </div>\n\t\t</Modal>\n\t</div>\n", null]}