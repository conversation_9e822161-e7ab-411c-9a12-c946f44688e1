{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue?vue&type=style&index=0&id=163c60bb&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.upload-box\n    display flex\n    align-items center\n    justify-content center\n    width 60px\n    height 60px\n    background #ccc\n/deep/.ivu-input{\n    font-size: 13px!important;\n}\n.slider-box .title{\n    color: #999999;\n    font-size 13px;\n    margin-bottom 5px;\n}\n.c_row-item{\n    padding: 0 15px;\n    margin-top 22px;\n}\n.slider-box .color{\n    margin-bottom 15px;\n}\n.boxs\n    width 60px\n    height 60px\n    margin-bottom 10px\n    position relative\n    .replace\n        background: rgba(0,0,0,0.4);\n        border-radius: 0 0 6px 6px;\n        position absolute;\n        bottom 0;\n        left:0;\n        width 100%;\n        color #fff;\n        font-size: 12px;\n        text-align center;\n        height 24px;\n        line-height 24px;\n    .iconfont-diy\n        position absolute\n        top -15px\n        right -8px\n        font-size 25px\n        color #999\n    img\n        width 100%\n        height 100%\n        border-radius 6px\n", null]}