{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue?vue&type=template&id=ca20d04a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue", "mtime": 1750985338988}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"report-container\"},[_c('div',{staticClass:\"page-header\"},[_vm._m(0),_c('div',{staticClass:\"header-right\"},[_c('el-date-picker',{staticStyle:{\"margin-right\":\"12px\"},attrs:{\"type\":\"monthrange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始月份\",\"end-placeholder\":\"结束月份\"},on:{\"change\":_vm.handleDateChange},model:{value:(_vm.dateRange),callback:function ($$v) {_vm.dateRange=$$v},expression:\"dateRange\"}}),_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refreshData}},[_vm._v(\"刷新数据\")]),_c('el-button',{attrs:{\"type\":\"success\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.exportReport}},[_vm._v(\"导出报表\")])],1)]),_c('div',{staticClass:\"overview-cards\"},[_c('div',{staticClass:\"overview-card\"},[_vm._m(1),_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-title\"},[_vm._v(\"库存总值\")]),_c('div',{staticClass:\"card-number\"},[_vm._v(\"¥\"+_vm._s(_vm.formatMoney(_vm.overview.inventory_value)))]),_c('div',{staticClass:\"card-change\",class:_vm.overview.inventory_change >= 0 ? 'positive' : 'negative'},[_c('i',{class:_vm.overview.inventory_change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'}),_vm._v(\"\\n          \"+_vm._s(Math.abs(_vm.overview.inventory_change))+\"%\\n        \")])])]),_c('div',{staticClass:\"overview-card\"},[_vm._m(2),_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-title\"},[_vm._v(\"调拨单数\")]),_c('div',{staticClass:\"card-number\"},[_vm._v(_vm._s(_vm.overview.transfer_count))]),_c('div',{staticClass:\"card-change\",class:_vm.overview.transfer_change >= 0 ? 'positive' : 'negative'},[_c('i',{class:_vm.overview.transfer_change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'}),_vm._v(\"\\n          \"+_vm._s(Math.abs(_vm.overview.transfer_change))+\"%\\n        \")])])]),_c('div',{staticClass:\"overview-card\"},[_vm._m(3),_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-title\"},[_vm._v(\"预警商品\")]),_c('div',{staticClass:\"card-number\"},[_vm._v(_vm._s(_vm.overview.warning_count))]),_c('div',{staticClass:\"card-desc\"},[_vm._v(\"需要关注\")])])]),_c('div',{staticClass:\"overview-card\"},[_vm._m(4),_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-title\"},[_vm._v(\"调拨效率\")]),_c('div',{staticClass:\"card-number\"},[_vm._v(_vm._s(_vm.overview.efficiency)+\"%\")]),_c('div',{staticClass:\"card-desc\"},[_vm._v(\"平均处理时间\")])])])]),_c('div',{staticClass:\"charts-container\"},[_c('div',{staticClass:\"chart-card\"},[_c('div',{staticClass:\"chart-header\"},[_c('h3',[_vm._v(\"库存变动趋势\")]),_c('div',{staticClass:\"chart-controls\"},[_c('el-radio-group',{attrs:{\"size\":\"small\"},on:{\"change\":_vm.loadInventoryTrend},model:{value:(_vm.inventoryPeriod),callback:function ($$v) {_vm.inventoryPeriod=$$v},expression:\"inventoryPeriod\"}},[_c('el-radio-button',{attrs:{\"label\":\"week\"}},[_vm._v(\"近7天\")]),_c('el-radio-button',{attrs:{\"label\":\"month\"}},[_vm._v(\"近30天\")]),_c('el-radio-button',{attrs:{\"label\":\"quarter\"}},[_vm._v(\"近3个月\")])],1)],1)]),_c('div',{staticClass:\"chart-content\"},[_c('div',{ref:\"inventoryChart\",staticStyle:{\"width\":\"100%\",\"height\":\"300px\"}})])]),_c('div',{staticClass:\"chart-card\"},[_c('div',{staticClass:\"chart-header\"},[_c('h3',[_vm._v(\"调拨统计分析\")]),_c('div',{staticClass:\"chart-controls\"},[_c('el-select',{staticStyle:{\"width\":\"120px\"},attrs:{\"size\":\"small\"},on:{\"change\":_vm.loadTransferStats},model:{value:(_vm.transferType),callback:function ($$v) {_vm.transferType=$$v},expression:\"transferType\"}},[_c('el-option',{attrs:{\"label\":\"按状态\",\"value\":\"status\"}}),_c('el-option',{attrs:{\"label\":\"按门店\",\"value\":\"store\"}}),_c('el-option',{attrs:{\"label\":\"按时间\",\"value\":\"time\"}})],1)],1)]),_c('div',{staticClass:\"chart-content\"},[_c('div',{ref:\"transferChart\",staticStyle:{\"width\":\"100%\",\"height\":\"300px\"}})])])]),_c('div',{staticClass:\"report-tables\"},[_c('div',{staticClass:\"table-card\"},[_c('div',{staticClass:\"table-header\"},[_c('h3',[_vm._v(\"门店库存报表\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":_vm.exportStoreReport}},[_vm._v(\"导出\")])],1),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.storeLoading),expression:\"storeLoading\"}],attrs:{\"data\":_vm.storeReport,\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"store_name\",\"label\":\"门店名称\",\"width\":\"150\"}}),_c('el-table-column',{attrs:{\"prop\":\"total_products\",\"label\":\"商品总数\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"total_stock\",\"label\":\"库存总量\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"total_value\",\"label\":\"库存总值\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"money\"},[_vm._v(\"¥\"+_vm._s(_vm.formatMoney(scope.row.total_value)))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"warning_count\",\"label\":\"预警商品\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{class:scope.row.warning_count > 0 ? 'warning-text' : ''},[_vm._v(\"\\n              \"+_vm._s(scope.row.warning_count)+\"\\n            \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"out_stock_count\",\"label\":\"缺货商品\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{class:scope.row.out_stock_count > 0 ? 'danger-text' : ''},[_vm._v(\"\\n              \"+_vm._s(scope.row.out_stock_count)+\"\\n            \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"库存状态\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-progress',{attrs:{\"percentage\":_vm.getStockHealth(scope.row),\"color\":_vm.getProgressColor(scope.row),\"stroke-width\":8,\"show-text\":false}}),_c('span',{staticClass:\"progress-text\"},[_vm._v(_vm._s(_vm.getStockHealth(scope.row))+\"%\")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"120\",\"align\":\"center\",\"fixed\":\"right\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewStoreDetail(scope.row)}}},[_vm._v(\"查看详情\")])]}}])})],1)],1),_c('div',{staticClass:\"table-card\"},[_c('div',{staticClass:\"table-header\"},[_c('h3',[_vm._v(\"调拨效率报表\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":_vm.exportTransferReport}},[_vm._v(\"导出\")])],1),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.transferLoading),expression:\"transferLoading\"}],attrs:{\"data\":_vm.transferReport,\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"date\",\"label\":\"日期\",\"width\":\"120\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"total_orders\",\"label\":\"调拨单数\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"approved_orders\",\"label\":\"已审核\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"completed_orders\",\"label\":\"已完成\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"label\":\"完成率\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{class:_vm.getEfficiencyClass(scope.row)},[_vm._v(\"\\n              \"+_vm._s(_vm.getCompletionRate(scope.row))+\"%\\n            \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"avg_process_time\",\"label\":\"平均处理时间\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\"\\n            \"+_vm._s(scope.row.avg_process_time)+\"小时\\n          \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"total_quantity\",\"label\":\"调拨总量\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"label\":\"状态\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getEfficiencyTagType(scope.row),\"size\":\"small\"}},[_vm._v(\"\\n              \"+_vm._s(_vm.getEfficiencyText(scope.row))+\"\\n            \")])]}}])})],1)],1)]),_c('div',{staticClass:\"analysis-section\"},[_c('div',{staticClass:\"section-header\"},[_c('h3',[_vm._v(\"商品分析\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":_vm.refreshProductAnalysis}},[_vm._v(\"刷新\")])],1),_c('div',{staticClass:\"analysis-cards\"},[_c('div',{staticClass:\"analysis-card\"},[_vm._m(5),_c('div',{staticClass:\"product-list\"},_vm._l((_vm.hotProducts),function(product,index){return _c('div',{key:product.id,staticClass:\"product-item\"},[_c('div',{staticClass:\"product-rank\"},[_vm._v(_vm._s(index + 1))]),_c('div',{staticClass:\"product-info\"},[_c('div',{staticClass:\"product-name\"},[_vm._v(_vm._s(product.name))]),_c('div',{staticClass:\"product-stats\"},[_vm._v(\"调拨次数: \"+_vm._s(product.transfer_count))])]),_c('div',{staticClass:\"product-trend\"},[_c('i',{class:product.trend === 'up' ? 'el-icon-arrow-up trend-up' : 'el-icon-arrow-down trend-down'})])])}),0)]),_c('div',{staticClass:\"analysis-card\"},[_vm._m(6),_c('div',{staticClass:\"warning-stats\"},[_c('div',{staticClass:\"warning-item\"},[_c('div',{staticClass:\"warning-label\"},[_vm._v(\"严重缺货\")]),_c('div',{staticClass:\"warning-value danger\"},[_vm._v(_vm._s(_vm.warningStats.severe))])]),_c('div',{staticClass:\"warning-item\"},[_c('div',{staticClass:\"warning-label\"},[_vm._v(\"库存偏低\")]),_c('div',{staticClass:\"warning-value warning\"},[_vm._v(_vm._s(_vm.warningStats.low))])]),_c('div',{staticClass:\"warning-item\"},[_c('div',{staticClass:\"warning-label\"},[_vm._v(\"库存正常\")]),_c('div',{staticClass:\"warning-value success\"},[_vm._v(_vm._s(_vm.warningStats.normal))])]),_c('div',{staticClass:\"warning-item\"},[_c('div',{staticClass:\"warning-label\"},[_vm._v(\"库存充足\")]),_c('div',{staticClass:\"warning-value info\"},[_vm._v(_vm._s(_vm.warningStats.abundant))])])])])])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_vm._v(\"数据报表\")]),_c('p',{staticClass:\"page-desc\"},[_vm._v(\"ERP系统数据分析与报表展示\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"card-icon inventory\"},[_c('i',{staticClass:\"el-icon-goods\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"card-icon transfer\"},[_c('i',{staticClass:\"el-icon-sort\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"card-icon warning\"},[_c('i',{staticClass:\"el-icon-warning\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"card-icon efficiency\"},[_c('i',{staticClass:\"el-icon-data-analysis\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"card-header\"},[_c('h4',[_vm._v(\"热门调拨商品 TOP10\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"card-header\"},[_c('h4',[_vm._v(\"库存预警分析\")])])}]\n\nexport { render, staticRenderFns }"]}