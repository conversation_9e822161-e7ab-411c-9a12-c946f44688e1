{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue?vue&type=template&id=60cd1378&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue", "mtime": 1728874543955}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <!-- 用户-用户列表 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n      <div class=\"padding-add\">\n        <!-- 筛选条件 -->\n        <Form\n            ref=\"userFrom\"\n            :model=\"userFrom\"\n            :label-width=\"labelWidth\"\n            :label-position=\"labelPosition\"\n            @submit.native.prevent\n        >\n          <Row :gutter=\"24\">\n            <Col span=\"18\">\n              <Row>\n                <Col span=\"24\">\n                  <Row>\n                    <Col>\n                      <FormItem label=\"用户搜索：\" label-for=\"nickname\">\n                        <Input\n                            v-model=\"userFrom.nickname\"\n                            placeholder=\"请输入\"\n                            element-id=\"nickname\"\n                            clearable\n                            class=\"input-add\"\n                        >\n                          <Select\n                              v-model=\"field_key\"\n                              slot=\"prepend\"\n                              style=\"width: 80px\"\n                          >\n                            <Option value=\"all\">全部</Option>\n                            <Option value=\"uid\">UID</Option>\n                            <Option value=\"phone\">手机号</Option>\n                            <Option value=\"nickname\">用户昵称</Option>\n                          </Select>\n                        </Input>\n                      </FormItem>\n                    </Col>\n                  </Row>\n                </Col>\n              </Row>\n            </Col>\n            <template v-if=\"collapse\">\n              <Col span=\"18\">\n                <Row>\n                  <Col>\n                    <FormItem label=\"用户分组：\" label-for=\"group_id\">\n                      <Select\n                          v-model=\"userFrom.group_id\"\n                          placeholder=\"请选择\"\n                          element-id=\"group_id\"\n                          clearable\n                          class=\"input-add\"\n\t\t\t\t\t\t  @on-change=\"userSearchs\"\n                      >\n                        <!--                        <Option value=\"\">全部</Option>-->\n                        <Option\n                            :value=\"item.id\"\n                            v-for=\"(item, index) in groupList\"\n                            :key=\"index\"\n                        >{{ item.group_name }}</Option\n                        >\n                      </Select>\n                    </FormItem>\n                  </Col>\n                  <Col>\n                    <FormItem label=\"用户标签：\" label-for=\"label_id\">\n                      <div\n                          class=\"labelInput acea-row row-between-wrapper input-add\"\n                          @click=\"openLabelList\"\n                      >\n                        <div>\n                          <div v-if=\"dataLabel.length\">\n                            <Tag\n                                closable\n                                v-for=\"(item, index) in dataLabel\"\n                                :key=\"index\"\n                                @on-close=\"closeLabel(item)\"\n                            >{{ item.label_name }}</Tag\n                            >\n                          </div>\n                          <span class=\"span\" v-else>请选择</span>\n                        </div>\n                        <div class=\"iconfont iconxiayi\"></div>\n                      </div>\n                    </FormItem>\n                  </Col>\n                  <Col>\n                    <FormItem label=\"性别：\" label-for=\"sex\">\n                      <Select\n                          v-model=\"userFrom.sex\"\n                          placeholder=\"请选择\"\n                          clearable\n                          class=\"input-add\"\n\t\t\t\t\t\t  @on-change=\"userSearchs\"\n                      >\n                        <!--                        <Option value=\"\">全部</Option>-->\n                        <Option value=\"1\">男</Option>\n                        <Option value=\"2\">女</Option>\n                        <Option value=\"0\">未知</Option>\n                      </Select>\n                    </FormItem>\n                  </Col>\n                </Row>\n              </Col>\n              <Col span=\"18\">\n                <Row>\n                  <Col>\n                    <FormItem label=\"会员等级：\" label-for=\"level\">\n                      <Select\n                          v-model=\"userFrom.level\"\n                          placeholder=\"请选择\"\n                          element-id=\"level\"\n                          clearable\n                          class=\"input-add\"\n\t\t\t\t\t\t  @on-change=\"userSearchs\"\n                      >\n                        <!--                        <Option value=\"\">全部</Option>-->\n                        <Option\n                            :value=\"item.id\"\n                            v-for=\"(item, index) in levelList\"\n                            :key=\"index\"\n                        >{{ item.name }}</Option\n                        >\n                      </Select>\n                    </FormItem>\n                  </Col>\n                  <Col>\n                    <FormItem label=\"付费会员：\" label-for=\"isMember\">\n                      <Select\n                          v-model=\"userFrom.isMember\"\n                          placeholder=\"请选择\"\n                          clearable\n                          class=\"input-add\"\n\t\t\t\t\t\t  @on-change=\"userSearchs\"\n                      >\n                        <!--                        <Option value=\"\">全部</Option>-->\n                        <Option value=\"1\">是</Option>\n                        <Option value=\"0\">否</Option>\n                      </Select>\n                    </FormItem>\n                  </Col>\n                  <Col>\n                    <FormItem label=\"身份：\">\n                      <Select\n                          v-model=\"userFrom.is_promoter\"\n                          placeholder=\"请选择\"\n                          clearable\n                          class=\"input-add\"\n\t\t\t\t\t\t  @on-change=\"userSearchs\"\n                      >\n                        <!--                        <Option value=\"\">全部</Option>-->\n                        <Option value=\"1\">推广员</Option>\n                        <Option value=\"0\">普通用户</Option>\n                      </Select>\n                    </FormItem>\n                  </Col>\n                </Row>\n              </Col>\n\n              <Col span=\"18\">\n                <Row>\n                  <Col class=\"dateMedia\">\n                    <FormItem label=\"访问时间：\" label-for=\"user_time\">\n                      <DatePicker\n                          :editable=\"false\"\n                          @on-change=\"onchangeTime\"\n                          :value=\"timeVal\"\n                          format=\"yyyy/MM/dd\"\n                          type=\"daterange\"\n                          placement=\"bottom-start\"\n                          placeholder=\"自定义时间\"\n                          :options=\"options\"\n                          class=\"input-add\"\n                      ></DatePicker>\n                    </FormItem>\n                  </Col>\n                  <Col>\n                    <FormItem label=\"访问情况：\" label-for=\"user_time_type\">\n                      <Select\n                          v-model=\"userFrom.user_time_type\"\n                          placeholder=\"请选择\"\n                          element-id=\"user_time_type\"\n                          clearable\n                          class=\"input-add\"\n\t\t\t\t\t\t  @on-change=\"userSearchs\"\n                      >\n                        <!--                        <Option value=\"all\">全部</Option>-->\n                        <Option value=\"visitno\">时间段未访问</Option>\n                        <Option value=\"visit\">时间段访问过</Option>\n                        <Option value=\"add_time\">首次访问</Option>\n                      </Select>\n                    </FormItem>\n                  </Col>\n                  <Col>\n                    <FormItem label=\"下单次数：\" label-for=\"pay_count\">\n                      <Select\n                          v-model=\"userFrom.pay_count\"\n                          placeholder=\"请选择\"\n                          element-id=\"pay_count\"\n                          clearable\n                          class=\"input-add\"\n\t\t\t\t\t\t  @on-change=\"userSearchs\"\n                      >\n                        <!--                        <Option value=\"\">全部</Option>-->\n                        <Option value=\"-1\">0次</Option>\n                        <Option value=\"0\">1次以上</Option>\n                        <Option value=\"1\">2次以上</Option>\n                        <Option value=\"2\">3次以上</Option>\n                        <Option value=\"3\">4次以上</Option>\n                        <Option value=\"4\">5次以上</Option>\n                      </Select>\n                    </FormItem>\n                  </Col>\n                </Row>\n              </Col>\n              <Col span=\"18\">\n                <Row>\n                  <Col>\n                    <FormItem label=\"国家：\" label-for=\"country\">\n                      <Select\n                          v-model=\"userFrom.country\"\n                          placeholder=\"请选择\"\n                          element-id=\"country\"\n                          clearable\n                          @on-change=\"changeCountry\"\n                          class=\"input-add\"\n                      >\n                        <!--                        <Option value=\"\">全部</Option>-->\n                        <Option value=\"domestic\">中国</Option>\n                        <Option value=\"abroad\">其他</Option>\n                      </Select>\n                    </FormItem>\n                  </Col>\n                  <Col v-if=\"userFrom.country === 'domestic'\">\n                    <FormItem label=\"省份：\">\n                      <Cascader\n                          :data=\"addresData\"\n\t\t\t\t\t\t  change-on-select\n                          :value=\"address\"\n                          v-model=\"address\"\n                          @on-change=\"handleChange\"\n                          class=\"input-add\"\n                      ></Cascader>\n                    </FormItem>\n                  </Col>\n                </Row>\n              </Col>\n\n            </template>\n            <Col span=\"6\" class=\"ivu-text-right userFrom\">\n              <FormItem>\n                <Button\n                    type=\"primary\"\n                    label=\"default\"\n                    class=\"mr15\"\n                    @click=\"userSearchs\"\n                >搜索</Button\n                >\n                <Button class=\"ResetSearch\" @click=\"reset('userFrom')\"\n                >重置</Button\n                >\n                <a v-font=\"14\" class=\"ivu-ml-8\" @click=\"collapse = !collapse\">\n                  <template v-if=\"!collapse\">\n                    展开 <Icon type=\"ios-arrow-down\" />\n                  </template>\n                  <template v-else>\n                    收起 <Icon type=\"ios-arrow-up\" />\n                  </template>\n                </a>\n              </FormItem>\n            </Col>\n          </Row>\n        </Form>\n      </div>\n    </Card>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt listbox\">\n      <div class=\"new_tab\">\n        <!-- Tab栏切换 -->\n        <Tabs v-model=\"headeType\" @on-click=\"onClickTab\">\n          <TabPane\n              :label=\"item.name\"\n              :name=\"item.type\"\n              v-for=\"(item, index) in headeNum\"\n              :key=\"index\"\n          />\n        </Tabs>\n      </div>\n      <Row type=\"flex\" justify=\"space-between\">\n        <!-- 相关操作 -->\n        <Col span=\"24\">\n          <Button\n              v-auth=\"['admin-user-save']\"\n              type=\"primary\"\n              class=\"mr20\"\n              @click=\"save\"\n          >添加用户</Button\n          >\n          <Tooltip\n              content=\"本页至少选中一项\"\n              :disabled=\"!!checkUidList.length && isAll==0\"\n          >\n            <Button\n                v-auth=\"['admin-user-coupon']\"\n                type=\"primary\"\n                class=\"mr20\"\n                :disabled=\"!checkUidList.length && isAll==0\"\n                @click=\"onSend\"\n            >发送优惠券</Button\n            >\n          </Tooltip>\n          <Tooltip\n              content=\"本页至少选中一项\"\n              :disabled=\"!!checkUidList.length && isAll==0\"\n          >\n            <Button\n                v-auth=\"['admin-wechat-news']\"\n                class=\"greens mr20\"\n                size=\"default\"\n                :disabled=\"!checkUidList.length && isAll==0\"\n                @click=\"onSendPic\"\n                v-if=\"userFrom.user_type === 'wechat'\"\n            >\n              <Icon type=\"md-list\"></Icon>\n              发送图文消息\n            </Button>\n          </Tooltip>\n          <Tooltip\n              content=\"本页至少选中一项\"\n              :disabled=\"!!checkUidList.length && isAll==0\"\n          >\n            <Button\n                v-auth=\"['admin-user-set_label']\"\n                class=\"mr20\"\n                :disabled=\"!checkUidList.length && isAll==0\"\n                @click=\"setBatch\"\n            >批量设置</Button\n            >\n          </Tooltip>\n        </Col>\n      </Row>\n      <!-- 用户列表表格 -->\n      <vxe-table\n          ref=\"xTable\"\n          class=\"mt25\"\n          :loading=\"loading\"\n          row-id=\"uid\"\n          :expand-config=\"{accordion: true}\"\n          :checkbox-config=\"{reserve: true}\"\n          @checkbox-all=\"checkboxAll\"\n          @checkbox-change=\"checkboxItem\"\n          :data=\"userLists\">\n        <vxe-column type=\"\" width=\"0\"></vxe-column>\n        <vxe-column type=\"expand\" width=\"35\">\n          <template #content=\"{ row }\">\n            <div class=\"tdinfo\">\n              <Row class=\"expand-row\">\n                <Col span=\"6\">\n                  <span class=\"expand-key\">首次访问：</span>\n                  <span class=\"expand-value\"> {{row.add_time | formatDate}}</span>\n                </Col>\n                <Col span=\"6\">\n                  <span class=\"expand-key\">近次访问：</span>\n                  <span class=\"expand-value\">{{row.last_time  | formatDate}}</span>\n                </Col>\n                <Col span=\"6\">\n                  <span class=\"expand-key\">身份证号：</span>\n                  <span class=\"expand-value\">{{row.card_id}}</span>\n                </Col>\n                <Col span=\"6\">\n                  <span class=\"expand-key\">真实姓名：</span>\n                  <span class=\"expand-value\">{{row.real_name}}</span>\n                </Col>\n                <!-- <Col span=\"6\">\n                    <span class=\"expand-key\">手机号：</span>\n                    <span class=\"expand-value\">{{row.phone}}</span>\n                </Col> -->\n              </Row>\n              <Row class=\"expand-row\">\n                <!-- <Col span=\"6\">\n                    <span class=\"expand-key\">真实姓名：</span>\n                    <span class=\"expand-value\">{{row.real_name}}</span>\n                </Col> -->\n                <Col span=\"6\">\n                  <span class=\"expand-key\">标签：</span>\n                  <span class=\"expand-value\">{{row.labels}}</span>\n                </Col>\n                <Col span=\"6\">\n                  <span class=\"expand-key\">生日：</span>\n                  <span class=\"expand-value\">{{row.birthday}}</span>\n                </Col>\n                <Col span=\"6\">\n                  <span class=\"expand-key\">地址：</span>\n                  <span class=\"expand-value\">{{row.addres}}</span>\n                </Col>\n              </Row>\n              <Row class=\"expand-row\">\n                <Col span=\"6\">\n                  <span class=\"expand-key\">备注：</span>\n                  <span class=\"expand-value\">{{row.mark}}</span>\n                </Col>\n              </Row>\n            </div>\n          </template>\n        </vxe-column>\n        <vxe-column type=\"checkbox\" width=\"100\">\n          <template #header>\n            <div>\n              <Dropdown transfer @on-click=\"allPages\">\n                <a href=\"javascript:void(0)\" class=\"acea-row row-middle\">\n                  <span>全选({{isAll==1?(total-checkUidList.length):checkUidList.length}})</span>\n                  <Icon type=\"ios-arrow-down\"></Icon>\n                </a>\n                <template #list>\n                  <DropdownMenu>\n                    <DropdownItem name=\"0\">当前页</DropdownItem>\n                    <DropdownItem name=\"1\">所有页</DropdownItem>\n                  </DropdownMenu>\n                </template>\n              </Dropdown>\n            </div>\n          </template>\n        </vxe-column>\n        <vxe-column field=\"uid\" title=\"UID\" width=\"60\"></vxe-column>\n        <vxe-column field=\"avatars\" title=\"头像\" width=\"50\">\n          <template v-slot=\"{ row }\">\n            <viewer>\n              <div class=\"tabBox_img\">\n                <img v-lazy=\"row.avatar\" />\n              </div>\n            </viewer>\n          </template>\n        </vxe-column>\n        <vxe-column field=\"nickname\" title=\"昵称\" min-width=\"150\">\n          <template v-slot=\"{ row }\">\n            <div class=\"acea-row\">\n              <Icon\n                  type=\"md-male\"\n                  v-show=\"row.sex === '男'\"\n                  color=\"#2db7f5\"\n                  size=\"15\"\n                  class=\"mr5\"\n              />\n              <Icon\n                  type=\"md-female\"\n                  v-show=\"row.sex === '女'\"\n                  color=\"#ed4014\"\n                  size=\"15\"\n                  class=\"mr5\"\n              />\n              <div v-if=\"row.delete_time != null\" style=\"color:#ed4014;\">{{row.nickname}} (已注销)</div>\n              <div v-else v-text=\"row.nickname\"></div>\n            </div>\n          </template>\n        </vxe-column>\n        <vxe-column field=\"isMember\" title=\"付费会员\" min-width=\"90\">\n          <template v-slot=\"{ row }\">\n            <div>{{ row.isMember ? \"是\" : \"否\" }}</div>\n          </template>\n        </vxe-column>\n        <vxe-column field=\"level\" title=\"用户等级\" min-width=\"90\"></vxe-column>\n        <vxe-column field=\"group_id\" title=\"分组\" min-width=\"100\"></vxe-column>\n        <vxe-column field=\"phone\" title=\"手机号\" min-width=\"110\"></vxe-column>\n        <vxe-column field=\"user_type\" title=\"用户类型\" min-width=\"100\"></vxe-column>\n        <vxe-column field=\"spread_uid_nickname\" title=\"推荐人\" min-width=\"100\"></vxe-column>\n        <vxe-column field=\"now_money\" title=\"余额\" min-width=\"100\"></vxe-column>\n        <vxe-column field=\"action\" title=\"操作\" align=\"center\" width=\"180\" fixed=\"right\">\n          <template v-slot=\"{ row }\">\n            <span v-if=\"row.delete_time != null\" style=\"color: #c5c8ce;\">编辑</span>\n            <a v-else @click=\"edit(row)\">编辑</a>\n            <Divider type=\"vertical\" v-if=\"row.is_extend_info\"/>\n            <a @click=\"extendInfo(row)\" v-if=\"row.is_extend_info\">信息补充</a>\n            <Divider type=\"vertical\" />\n            <a @click=\"changeMenu(row, '1')\">详情</a>\n          </template>\n        </vxe-column>\n      </vxe-table>\n      <vxe-pager class=\"mt20\" border size=\"medium\" :page-size=\"userFrom.limit\" :current-page=\"userFrom.page\" :total=\"total\"\n                 :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Total']\" @page-change=\"pageChange\">\n      </vxe-pager>\n    </Card>\n    <!-- 用户标签 -->\n    <Modal\n        v-model=\"labelListShow\"\n        scrollable\n        title=\"选择用户标签\"\n        :closable=\"true\"\n        width=\"540\"\n        :footer-hide=\"true\"\n        :mask-closable=\"false\"\n    >\n      <labelList\n          ref=\"labelList\"\n          @activeData=\"activeData\"\n          @close=\"labelListClose\"\n      ></labelList>\n    </Modal>\n    <!-- 编辑表单 积分余额-->\n    <edit-from\n        ref=\"edits\"\n        :FromData=\"FromData\"\n        :userEdit=\"1\"\n        @submitFail=\"submitFail\"\n    ></edit-from>\n    <!-- 发送优惠券-->\n    <send-from\n        ref=\"sends\"\n        :is-all=\"isAll\"\n        :where=\"userFrom\"\n        :userIds=\"checkUidList.join(',')\"\n    ></send-from>\n    <!-- 会员详情-->\n    <user-details ref=\"userDetails\" :group-list=\"groupList\"></user-details>\n    <!--发送图文消息 -->\n    <Modal\n        v-model=\"modal13\"\n        scrollable\n        title=\"发送消息\"\n        width=\"1200\"\n        height=\"800\"\n        footer-hide\n        class=\"modelBox\"\n    >\n      <news-category\n          v-if=\"modal13\"\n          :isShowSend=\"isShowSend\"\n          :is-all=\"isAll\"\n          :where=\"userFrom\"\n          :userIds=\"checkUidList.join(',')\"\n          :scrollerHeight=\"scrollerHeight\"\n          :contentTop=\"contentTop\"\n          :contentWidth=\"contentWidth\"\n          :maxCols=\"maxCols\"\n      ></news-category>\n    </Modal>\n    <!--修改推广人-->\n    <Modal\n        v-model=\"promoterShow\"\n        scrollable\n        title=\"修改推广人\"\n        class=\"order_box\"\n        :closable=\"false\"\n    >\n      <Form\n          ref=\"formInline\"\n          :model=\"formInline\"\n          :label-width=\"100\"\n          @submit.native.prevent\n      >\n        <FormItem label=\"用户头像：\" prop=\"image\">\n          <div class=\"picBox\" @click=\"customer\">\n            <div class=\"pictrue\" v-if=\"formInline.image\">\n              <img v-lazy=\"formInline.image\" />\n            </div>\n            <div class=\"upLoad acea-row row-center-wrapper\" v-else>\n              <Icon type=\"ios-camera-outline\" size=\"26\" />\n            </div>\n          </div>\n        </FormItem>\n      </Form>\n      <div slot=\"footer\">\n        <Button type=\"primary\" @click=\"putSend('formInline')\">提交</Button>\n        <Button @click=\"cancel('formInline')\">取消</Button>\n      </div>\n    </Modal>\n    <Modal\n        v-model=\"customerShow\"\n        scrollable\n        title=\"请选择商城用户\"\n        :closable=\"false\"\n        width=\"900\"\n    >\n      <customerInfo\n          v-if=\"customerShow\"\n          @imageObject=\"imageObject\"\n      ></customerInfo>\n    </Modal>\n    <Modal\n        v-model=\"labelShow\"\n        scrollable\n        title=\"选择用户标签\"\n        :closable=\"true\"\n        width=\"540\"\n        :footer-hide=\"true\"\n    >\n      <userLabel :uid=\"labelActive.uid\" @close=\"labelClose\"></userLabel>\n    </Modal>\n    <!-- 批量设置 -->\n    <Modal v-model=\"batchModal\" title=\"批量设置\" width=\"750\" class-name=\"batch-modal\" @on-visible-change=\"batchVisibleChange\">\n      <Alert show-icon>每次只能修改一项，如需修改多项，请多次操作。</Alert>\n      <Row type=\"flex\" align=\"middle\">\n        <Col span=\"4\">\n          <Menu :active-name=\"menuActive\" width=\"auto\" @on-select=\"menuSelect\">\n            <MenuItem :name=\"1\">用户分组</MenuItem>\n            <MenuItem :name=\"2\">用户标签</MenuItem>\n            <MenuItem :name=\"3\">用户等级</MenuItem>\n            <MenuItem :name=\"4\">积分余额</MenuItem>\n            <MenuItem :name=\"5\">赠送会员</MenuItem>\n            <MenuItem :name=\"6\">上级推广人</MenuItem>\n          </Menu>\n        </Col>\n        <Col span=\"20\">\n          <Form :model=\"batchData\" :label-width=\"122\">\n            <FormItem v-if=\"menuActive === 1\" label=\"用户分组：\">\n              <Select v-model=\"batchData.group_id\">\n                <Option v-for=\"item in groupList\" :key=\"item.id\" :value=\"item.id\">{{ item.group_name }}</Option>\n              </Select>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 2\" label=\"用户标签：\">\n              <div class=\"select-tag\" @click=\"openLabelList\">\n                <div v-if=\"batchLabel.length\">\n                  <Tag v-for=\"item in batchLabel\" :key=\"item.id\" closable @on-close=\"tagClose(item.id)\">{{ item.label_name }}</Tag>\n                </div>\n                <span v-else class=\"placeholder\">请选择</span>\n                <Icon type=\"ios-arrow-down\" />\n              </div>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 3\" label=\"用户等级：\">\n              <Select v-model=\"batchData.level_id\">\n                <Option v-for=\"item in levelList\" :key=\"item.id\" :value=\"item.id\">{{ item.name }}</Option>\n              </Select>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 4\" label=\"修改余额：\">\n              <RadioGroup v-model=\"batchData.money_status\">\n                <Radio :label=\"1\">增加</Radio>\n                <Radio :label=\"2\">减少</Radio>\n              </RadioGroup>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 4\" label=\"余额：\">\n              <InputNumber v-model=\"batchData.money\" :min=\"0\" :max=\"999999\"></InputNumber>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 4\" label=\"修改积分：\">\n              <RadioGroup v-model=\"batchData.integration_status\">\n                <Radio :label=\"1\">增加</Radio>\n                <Radio :label=\"2\">减少</Radio>\n              </RadioGroup>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 4\" label=\"积分：\">\n              <InputNumber v-model=\"batchData.integration\" :min=\"0\" :max=\"999999\"></InputNumber>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 5\" label=\"修改时长：\">\n              <RadioGroup v-model=\"batchData.days_status\">\n                <Radio :label=\"1\">增加</Radio>\n                <Radio :label=\"2\">减少</Radio>\n              </RadioGroup>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 5\" label=\"修改时长(天)：\">\n              <InputNumber v-model=\"batchData.day\" :min=\"0\" :max=\"999999\"></InputNumber>\n            </FormItem>\n            <FormItem v-if=\"menuActive === 6\" label=\"上级推广员：\">\n              <Input :value=\"spread_name\" placeholder=\"请选择\" icon=\"ios-arrow-down\" @on-click=\"customer\" @on-focus=\"customer\"></Input>\n            </FormItem>\n          </Form>\n        </Col>\n      </Row>\n      <div slot=\"footer\">\n        <Button @click=\"cancelBatch\">取消</Button>\n        <Button type=\"primary\" @click=\"saveBatch\">保存</Button>\n      </div>\n    </Modal>\n  </div>\n", null]}