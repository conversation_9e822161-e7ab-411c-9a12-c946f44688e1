{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\orderlistDetails.vue?vue&type=template&id=084f29f8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\orderlistDetails.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 订单列表 -->\n    <div>\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n            <!-- 订单列表-筛选条件 -->\n            <div class=\"new_card_pd\">\n                <table-form :is-all=\"isAll\" :auto-disabled=\"autoDisabled\" :form-selection=\"selection\" :orderDataStatus=\"orderDataStatus\" @getList=\"getData\" @order-data=\"orderDatas\" @onChangeType=\"onChangeType\" />\n            </div>\n        </Card>\n        <cards-data :cardLists=\"cardLists\" v-if=\"cardLists.length >= 0\"></cards-data>\n        <Card :bordered=\"false\" dis-hover>\n            <!-- 订单列表-表格组件-->\n            <table-list\n                ref=\"table\"\n                :where=\"orderData\"\n                :is-all=\"isAll\"\n                :currentTab=\"currentTab\"\n                @on-all=\"onAll\"\n                @auto-disabled=\"onAutoDisabled\"\n                @order-data=\"onOrderData\"\n                @on-changeCards=\"getCards\"\n                @changeGetTabs=\"changeGetTabs\"\n                @order-select=\"orderSelect\"\n                @selectChange2=\"selectChange2\"\n            />\n        </Card>\n    </div>\n", null]}