{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue?vue&type=template&id=8d17409a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue", "mtime": 1693985518000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"label-wrapper\">\n\t\t<div class=\"list-box\">\n\t\t\t<div class=\"label-box\" v-for=\"(item,index) in labelList\" :key=\"index\" v-if=\"isUser\">\n\t\t\t    <div class=\"title\">{{item.name}}</div>\n\t\t\t    <div class=\"list\">\n\t\t\t        <div class=\"label-item\" :class=\"{on:label.disabled}\" v-for=\"(label,j) in item.label\" :key=\"j\" @click=\"selectLabel(label)\">{{label.label_name}}</div>\n\t\t\t    </div>\n\t\t\t</div>\n\t\t\t<div v-if=\"!isUser\">暂无标签</div>\n\t\t</div>\n        <div class=\"footer\">\n            <Button type=\"primary\" class=\"btns\" @click=\"subBtn\">确定</Button>\n            <Button type=\"primary\" class=\"btns\" ghost @click=\"cancel\">取消</Button>\n        </div>\n    </div>\n", null]}