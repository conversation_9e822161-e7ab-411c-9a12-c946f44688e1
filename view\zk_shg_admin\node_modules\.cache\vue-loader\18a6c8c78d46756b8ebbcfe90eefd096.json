{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport vuedraggable from \"vuedraggable\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport linkaddress from \"@/components/linkaddress\";\nexport default {\n  name: \"c_menu_list\",\n  props: {\n    configObj: {\n      type: Object,\n    },\n    configNme: {\n      type: String,\n    },\n    index: {\n      type: null,\n    },\n  },\n  components: {\n    draggable: vuedraggable,\n    linkaddress,\n    uploadPictures,\n  },\n  data() {\n    return {\n      defaults: {},\n      configData: {},\n      menus: [],\n      list: [\n        {\n          title: \"aa\",\n          val: \"\",\n        },\n      ],\n      modalPic: false,\n      isChoice: \"单选\",\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      activeIndex: 0,\n      indexLast: 0,\n      lastObj: {},\n    };\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.defaults = this.configObj;\n      this.configData = this.configObj[this.configNme];\n    });\n  },\n  watch: {\n    configObj: {\n      handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n      },\n      deep: true,\n    },\n  },\n  methods: {\n    linkUrl(e) {\n      this.configData.list[this.activeIndex].info[this.indexLast].value = e;\n      if (this.defaults.name == \"pictureCube\") {\n        this.defaults.picStyle.picList[this.defaults.picStyle.tabVal].link = e;\n      }\n    },\n    getLink(index, key, item) {\n      this.indexLast = item.length - 1;\n      if (key != item.length - 1) {\n        return;\n      }\n      this.activeIndex = index;\n      this.$refs.linkaddres.modals = true;\n    },\n    addBox() {\n      if (this.configData.list.length == 0) {\n        this.lastObj.img = \"\";\n        this.lastObj.info[0].value = \"\";\n        this.lastObj.info[1].value = \"\";\n        this.configData.list.push(this.lastObj);\n      } else {\n        let obj = JSON.parse(\n          JSON.stringify(this.configData.list[this.configData.list.length - 1])\n        );\n        obj.img = \"\";\n        obj.info[0].value = \"\";\n        obj.info[1].value = \"\";\n        this.configData.list.push(obj);\n      }\n    },\n    // 点击图文封面\n    modalPicTap(title, index) {\n      this.activeIndex = index;\n      this.modalPic = true;\n    },\n    // 添加自定义弹窗\n    addCustomDialog(editorId) {\n      window.UE.registerUI(\n        \"test-dialog\",\n        function (editor, uiName) {\n          let dialog = new window.UE.ui.Dialog({\n            iframeUrl: \"/admin/widget.images/index.html?fodder=dialog\",\n            editor: editor,\n            name: uiName,\n            title: \"上传图片\",\n            cssRules: \"width:1200px;height:500px;padding:20px;\",\n          });\n          this.dialog = dialog;\n          // 参考上面的自定义按钮\n          var btn = new window.UE.ui.Button({\n            name: \"dialog-button\",\n            title: \"上传图片\",\n            cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,\n            onclick: function () {\n              // 渲染dialog\n              dialog.render();\n              dialog.open();\n            },\n          });\n\n          return btn;\n        },\n        37\n      );\n    },\n    // 获取图片信息\n    getPic(pc) {\n      this.$nextTick(() => {\n        this.configData.list[this.activeIndex].img = pc.att_dir;\n        let data = this.defaults.menuConfig;\n        if (data && data.isCube) {\n          this.defaults.picStyle.picList.splice(\n            this.defaults.picStyle.tabVal,\n            1,\n            {\n              image: pc.att_dir,\n              link: data.list[0].info[0].value,\n            }\n          );\n        }\n        this.modalPic = false;\n      });\n    },\n    onBlur() {\n      let data = this.defaults.menuConfig;\n      this.defaults.picStyle.picList[this.defaults.picStyle.tabVal].link =\n        data.list[0].info[0].value;\n    },\n    // 删除\n    bindDelete(item, index) {\n      if (this.configData.list.length == 1) {\n        this.lastObj = this.configData.list[0];\n      }\n      this.configData.list.splice(index, 1);\n    },\n  },\n};\n", null]}