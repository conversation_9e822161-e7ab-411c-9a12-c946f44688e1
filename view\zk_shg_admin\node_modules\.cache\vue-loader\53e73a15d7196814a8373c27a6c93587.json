{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import orderDetall from './orderDetall'\n    import { orderInvoiceChart, orderInvoiceList, orderInvoiceSet,exportInvoiceList } from '@/api/order'\n    import { mapState } from 'vuex';\n    import exportExcel from \"@/utils/newToExcel.js\";\n    export default {\n        name: 'invoice',\n        components: {\n            orderDetall\n        },\n        computed: {\n            ...mapState('media', ['isMobile']),\n            labelWidth () {\n                return this.isMobile ? undefined : 96;\n            },\n            labelPosition () {\n                return this.isMobile ? 'top' : 'right';\n            }\n        },\n        data () {\n            return {\n                orderShow: false,\n                invoiceShow: false,\n                invoiceDetails: {},\n                formInline: {\n                    is_invoice: 0,\n                    invoice_number: '',\n                    remark: '',\n                    invoice_amount: '',\n                    // pay_price: ''\n                },\n                loading: false,\n                currentTab: '',\n                tablists: null,\n                timeVal: [],\n                options: {\n                    shortcuts: [\n                        {\n                            text: '今天',\n                            value () {\n                                const end = new Date()\n                                const start = new Date()\n                                start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()))\n                                return [start, end]\n                            }\n                        },\n                        {\n                            text: '昨天',\n                            value () {\n                                const end = new Date()\n                                const start = new Date()\n                                start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)))\n                                end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)))\n                                return [start, end]\n                            }\n                        },\n                        {\n                            text: '最近7天',\n                            value () {\n                                const end = new Date()\n                                const start = new Date()\n                                start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 6)))\n                                return [start, end]\n                            }\n                        },\n                        {\n                            text: '最近30天',\n                            value () {\n                                const end = new Date()\n                                const start = new Date()\n                                start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 29)))\n                                return [start, end]\n                            }\n                        },\n\t\t\t\t\t\t{\n\t\t\t\t\t\t  text: \"上月\",\n\t\t\t\t\t\t  value() {\n\t\t\t\t\t\t    const end = new Date();\n\t\t\t\t\t\t    const start = new Date();\n\t\t\t\t\t\t\tconst day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n\t\t\t\t\t\t    start.setTime(\n\t\t\t\t\t\t      start.setTime(\n\t\t\t\t\t\t        new Date(new Date().getFullYear(), new Date().getMonth()-1, 1)\n\t\t\t\t\t\t      )\n\t\t\t\t\t\t    );\n\t\t\t\t\t\t\tend.setTime(\n\t\t\t\t\t\t\t  end.setTime(\n\t\t\t\t\t\t\t    new Date(new Date().getFullYear(), new Date().getMonth()-1, day)\n\t\t\t\t\t\t\t  )\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t    return [start, end];\n\t\t\t\t\t\t  },\n\t\t\t\t\t\t},\n                        {\n                            text: '本月',\n                            value () {\n                                const end = new Date()\n                                const start = new Date()\n                                start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)))\n                                return [start, end]\n                            }\n                        },\n                        {\n                            text: '本年',\n                            value () {\n                                const end = new Date()\n                                const start = new Date()\n                                start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)))\n                                return [start, end]\n                            }\n                        }\n                    ]\n                },\n                grid: {\n                    xl: 8,\n                    lg: 8,\n                    md: 12,\n                    sm: 24,\n                    xs: 24\n                },\n                columns: [\n                    {\n                        title: '订单号',\n                        key: 'order_id',\n                        minWidth: 170\n                    },\n                    // {\n                    //     title: '订单类型',\n                    //     key: 'pink_name',\n                    //     minWidth: 150\n                    // },\n                    {\n                        title: '订单金额',\n                        slot: 'pay_price',\n                        minWidth: 100\n                    },\n                    {\n                        title: '发票类型',\n                        slot: 'type',\n                        minWidth: 120,\n                        filters: [\n                            {\n                                label: '电子普通发票',\n                                value: 1\n                            },\n                            {\n                                label: '纸质专用发票',\n                                value: 2\n                            }\n                        ],\n                        filterMultiple: false,\n                        filterMethod (value, row) {\n                            if (value === 1) {\n                                return row.type === 1;\n                            } else if (value === 2) {\n                                return row.type === 2;\n                            }\n                        }\n                    },\n                    {\n                        title: '发票抬头名称',\n                        key: 'name',\n                        minWidth: 150,\n                    },\n                    {\n                        title: '发票抬头类型',\n                        slot: 'header_type',\n                        minWidth: 110,\n                        filters: [\n                            {\n                                label: '个人',\n                                value: 1\n                            },\n                            {\n                                label: '企业',\n                                value: 2\n                            }\n                        ],\n                        filterMultiple: false,\n                        filterMethod (value, row) {\n                            if (value === 1) {\n                                return row.header_type === 1;\n                            } else if (value === 2) {\n                                return row.header_type === 2;\n                            }\n                        }\n                    },\n                    // {\n                    //     title: '支付状态',\n                    //     key: 'pay_type_name',\n                    //     minWidth: 90\n                    // },\n                    {\n                        title: '下单时间',\n                        key: 'add_time',\n                        minWidth: 150,\n                        sortable: true\n                    },\n                    {\n                        title: '开票状态',\n                        slot: 'is_invoice',\n                        minWidth: 80\n                    },\n                    {\n                        title: '订单状态',\n                        slot: 'status',\n                        minWidth: 80\n                    },\n                    {\n                        title: '操作',\n                        slot: 'action',\n                        fixed: 'right',\n                        minWidth: 150,\n                        align: 'center'\n                    }\n                ],\n                orderList: [],\n                total: 0, // 总条数\n                orderData: {\n                    page: 1, // 当前页\n                    limit: 10, // 每页显示条数\n                    status: '',\n                    data: '',\n                    real_name: '',\n                    field_key: '',\n                    type: ''\n                },\n                orderId: 0\n            }\n        },\n        created () {\n            this.getTabs();\n            this.getList();\n        },\n        mounted () {\n        },\n        methods: {\n            // keyUp(e, key, money){\n            //     if(!this.formInline[key]) {\n            //         return (this.formInline[key] = \"\")\n            //     }\n            //     let num = \"\";\n            //     if(money) {\n            //         num = this.formInline[key].match(/^\\d*(\\.?\\d{0,2})/g)[0];\n            //     } else {\n            //         num = this.formInline[key].replace(/^[^\\d]+$/g, \"\").split('.')[0]\n            //     }\n            //     this.formInline[key] = `${num}`\n            //     //  this.$set(this.formInline,key,num)\n            // },\n            detall (e) {\n                this.orderShow = e;\n            },\n            orderInfo (id) {\n                this.orderId = id;\n                this.orderShow = true;\n            },\n            empty () {\n                this.formInline = {\n                    is_invoice: 0,\n                    invoice_number: '',\n                    remark: ''\n                };\n            },\n            cancel () {\n                this.invoiceShow = false;\n                this.empty();\n            },\n            kaiInvoice (invoice) {\n                if (invoice !== 1) {\n                    this.formInline.invoice_number = '';\n                    this.formInline.remark = '';\n                }\n            },\n            handleSubmit () {\n                if (this.formInline.is_invoice === 1) {\n                    if (this.formInline.invoice_number.trim() === '') return this.$Message.error('请填写发票编号');\n                }\n                \n                orderInvoiceSet(this.invoiceDetails.invoice_id, this.formInline).then(res => {\n                    this.$Message.success(res.msg);\n                    this.invoiceShow = false;\n                    this.getList();\n                    this.empty();\n                }).catch(err => {\n                    this.$Message.error(err.msg);\n                })\n            },\n            edit (row) {\n                this.invoiceShow = true;\n                this.invoiceDetails = row;\n                // this.formInline = row;\n                this.formInline.invoice_number = row.invoice_number;\n                this.formInline.invoice_amount = row.invoice_amount;\n                this.formInline.remark = row.invoice_reamrk;\n                this.formInline.is_invoice = row.is_invoice;\n                // this.formInline.pay_price = row.pay_price;\n                this.formInline.invoice_amount = row.invoice_amount == '0.00' ? row.pay_price : row.invoice_amount;\n            },\n            // 订单列表\n            getList () {\n                this.loading = true;\n                orderInvoiceList(this.orderData).then(async res => {\n                    this.loading = false\n                    let data = res.data;\n                    this.orderList = data.list;\n                    this.total = data.count;\n                }).catch(res => {\n                    this.loading = false;\n                    this.$Message.error(res.msg);\n                })\n            },\n            pageChange (index) {\n                this.orderData.page = index;\n                this.getList();\n            },\n            getTabs () {\n                orderInvoiceChart().then(res => {\n                    this.tablists = res.data;\n                }).catch(err => {\n                    this.$Message.error(err.msg)\n                })\n            },\n            // 精确搜索()\n            orderSearch () {\n                this.orderData.page = 1;\n                this.getList();\n            },\n            // 具体日期搜索()；\n            onchangeTime (e) {\n                this.orderData.page = 1;\n                this.timeVal = e;\n                this.orderData.data = this.timeVal[0] ? this.timeVal.join('-') : '';\n                this.getList();\n            },\n            // 订单状态搜索()\n            selectChange () {\n                this.orderData.page = 1;\n                this.getList();\n            },\n            // 订单搜索()\n            onClickTab () {\n                this.orderData.page = 1;\n                this.orderData.type = this.currentTab;\n                this.getList();\n            },\n           //导出\n\t\t\tasync exports() {\n\t\t\t\tlet [th, filekey, data, fileName] = [[], [], [], \"\"];\n\t\t\t\tlet lebData = await this.getExcelData(this.orderData); \n\t\t\t\tif (!fileName) fileName = lebData.filename;\n\t\t\t\tfilekey = lebData.filekey;\n\t\t\t\tif (!th.length) th = lebData.header; //表头\n\t\t\t\tdata = data.concat(lebData.export);\n          \t\texportExcel(th, filekey, fileName, data);\n\t\t\t},\n\t\t\tgetExcelData(excelData) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\texportInvoiceList(excelData).then((res) => {\n\t\t\t\t\t\treturn resolve(res.data);\n\t\t\t\t\t});\n\t\t\t\t});\n            },\n\n            inputEnter() {\n\tthis.formInline.pay_price = this.formInline.pay_price.replace(/[^\\d.]/g, \"\"); // 清除\"数字\"和\".\"以外的字符 只能输入数字和小数点\n\tthis.formInline.pay_price = this.formInline.pay_price.replace(/\\.{2,}/g, \".\"); // 不能连续输入两个及以上小数点\n\tthis.formInline.pay_price = this.formInline.pay_price\n\t\t.replace(\".\", \"$#$\")\n\t\t.replace(/\\./g, \"\")\n\t\t.replace(\"$#$\", \".\"); // 只保留第一个\".\", 清除多余的\".\"\n\tthis.formInline.pay_price = this.formInline.pay_price.replace(\n\t\t/^(-)*(\\d+)\\.(\\d\\d).*$/,\n\t\t\"$1$2.$3\"\n\t); // 只能输入两位小数\n\tif (\n\t\tthis.formInline.pay_price &&\n\t\tthis.formInline.pay_price.indexOf(\".\") < 0 &&\n\t\tthis.formInline.pay_price != \"\"\n\t) {\n\t\tthis.formInline.pay_price = parseFloat(this.formInline.pay_price);\n\t\tthis.formInline.pay_price = this.formInline.pay_price + \"\";\n\t} // 如果没有小数点，首位不能为类似于 01、02的值\n\t// 输入过程中，只能输入六位小数且六位小数都为零，则清空小数点和后面的六个零（如果输入完成了只输入四个零，则在blur事件中处理）\n\tif (\n\t\tthis.formInline.pay_price.indexOf(\".\") > 0 &&\n\t\tthis.formInline.pay_price.length - this.formInline.pay_price.indexOf(\".\") > 6\n\t) {\n\t\tlet str = this.formInline.pay_price.slice(\n\t\t\tthis.formInline.pay_price.indexOf(\".\"),\n\t\t\tthis.formInline.pay_price.length\n\t\t);\n\t\tif (str / 1 <= 0) {\n\t\t\tthis.formInline.pay_price = this.formInline.pay_price.replace(str, \"\");\n\t\t}\n\t}\n\tif (this.formInline.pay_price / 1 > 256) {\n\t\tthis.formInline.pay_price = this.formInline.pay_price + \"\";\n\t\tthis.formInline.pay_price = this.formInline.pay_price.slice(0, this.formInline.pay_price.length - 1);\n    }\n    \n},\n    inputBlur() {\n        // 若小数点后面全是零，则清楚小数点和后面的零\n        if (this.formInline.pay_price.indexOf(\".\") > 0) {\n            let str = this.formInline.pay_price.slice(\n                this.formInline.pay_price.indexOf(\".\"),\n                this.formInline.pay_price.length\n            );\n            if (str / 1 <= 0) {\n                this.formInline.pay_price = this.formInline.pay_price.replace(str, \"\");\n            }\n        }\n    },\n        }\n    }\n", null]}