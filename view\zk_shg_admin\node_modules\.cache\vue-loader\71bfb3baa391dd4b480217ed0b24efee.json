{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\t  import { productAttrsApi, productSaveStocksApi } from '@/api/product.js'\n    export default {\n        name: 'stockEdit',\n\t\t\t\tprops: {\n\t\t\t\t},\n        data () {\n            return {\n\t\t\t\t\t\t\tid:0,\n\t\t\t\t\t\t\tspecType:0,\n\t\t\t\t\t\t\tbatchShow:false,\n\t\t\t\t\t\t\tbatchStock:0,\n\t\t\t\t\t\t\tbatchPm:1,\n\t\t\t\t\t\t\tmodals: false,\n\t\t\t\t\t\t\tloading: false,\n\t\t\t\t\t\t\tstockData:[],\n\t\t\t\t\t\t\tcolumns: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttitle: \"图片\",\n\t\t\t\t\t\t\t\t\t\tslot: \"image\",\n\t\t\t\t\t\t\t\t\t\tminWidth: 20\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttitle: \"产品规格\",\n\t\t\t\t\t\t\t\t\t\tkey: \"suk\",\n\t\t\t\t\t\t\t\t\t\tminWidth: 90\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttitle: \"商品条形码\",\n\t\t\t\t\t\t\t\t\t\tkey: \"bar_code\",\n\t\t\t\t\t\t\t\t\t\tminWidth: 35\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttitle: \"商品编码\",\n\t\t\t\t\t\t\t\t\t\tkey: \"code\",\n\t\t\t\t\t\t\t\t\t\tminWidth: 35\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttitle: \"当前库存\",\n\t\t\t\t\t\t\t\t\t\tkey: \"stock\",\n\t\t\t\t\t\t\t\t\t\tminWidth: 10\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttitle: \"入/出库数量\",\n\t\t\t\t\t\t\t\t\t\tslot: \"num\",\n\t\t\t\t\t\t\t\t\t\tminWidth: 200\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t]\n            }\n        },\n        methods: {\n\t\t\t\t\t// 批量设置；\n\t\t\t\t\tcountBatch(){\n\t\t\t\t\t\tthis.batchStock = Math.abs(this.batchStock);\n\t\t\t\t\t\tthis.stockData.forEach(item=>{\n\t\t\t\t\t\t\titem.changeNum = this.batchStock;\n\t\t\t\t\t\t\tif(this.batchPm){\n\t\t\t\t\t\t\t\titem.pm = 1;\n\t\t\t\t\t\t\t\titem.resultNum = parseInt(item.stock) + parseInt(item.changeNum);\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\titem.pm = 0;\n\t\t\t\t\t\t\t\tif(parseInt(item.stock)<=0){\n\t\t\t\t\t\t\t\t\titem.resultNum = 0\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet num = parseInt(item.stock) - parseInt(item.changeNum)\n\t\t\t\t\t\t\t\t\titem.resultNum = num<=0?0:num;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\t// 批量加减库存\n\t\t\t\t\tinputTap(){\n\t\t\t\t\t\tthis.batchStock = this.batchStock.replace(/^\\d{10}$/g,\"0\")\n\t\t\t\t\t\tthis.countBatch();\n\t\t\t\t\t},\n\t\t\t\t\t// 批量设置入库或是出库\n\t\t\t\t\tbatchStockTap(){\n\t\t\t\t\t\tthis.countBatch();\n\t\t\t\t\t},\n\t\t\t\t\t// 单个设置\n\t\t\t\t\tcountStock(row){\n\t\t\t\t\t\tif(row.pm){\n\t\t\t\t\t\t\trow.resultNum = parseInt(row.stock) + parseInt(row.changeNum);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(parseInt(row.stock)<=0){\n\t\t\t\t\t\t\t\trow.resultNum = 0;\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tlet num = parseInt(row.stock) - parseInt(row.changeNum);\n\t\t\t\t\t\t\t\trow.resultNum = num<=0?0:num;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.stockData.forEach(item=>{\n\t\t\t\t\t\t\tif(row.id==item.id){\n\t\t\t\t\t\t\t\titem.changeNum = row.changeNum;\n\t\t\t\t\t\t\t\titem.resultNum = row.resultNum;\n\t\t\t\t\t\t\t\titem.pm = row.pm;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\t// 设置加减库存\n\t\t\t\t\tstockTap(row){\n\t\t\t\t\t\tthis.countStock(row);\n\t\t\t\t\t},\n\t\t\t\t\t// 设置入库或是出库\n\t\t\t\t\tchangeTap(row){\n\t\t\t\t\t\trow.changeNum = row.changeNum.replace(/^\\d{10}$/g,\"0\")\n\t\t\t\t\t\tthis.countStock(row);\n\t\t\t\t\t},\n\t\t\t\t\tbatchTap(){\n\t\t\t\t\t\tthis.batchShow = !this.batchShow;\n\t\t\t\t\t},\n\t\t\t\t\tproductAttrs(data){\n\t\t\t\t\t\tthis.specType = data.spec_type;\n\t\t\t\t\t\tthis.id = data.id;\n\t\t\t\t\t\tproductAttrsApi(data.id).then(res=>{\n\t\t\t\t\t\t\tlet data = res.data;\n\t\t\t\t\t\t\tdata.forEach(item=>{\n\t\t\t\t\t\t\t\titem.resultNum = item.stock;\n\t\t\t\t\t\t\t\titem.changeNum = 0;\n\t\t\t\t\t\t\t\titem.pm = 1;\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tthis.stockData = data;\n\t\t\t\t\t\t}).catch(err=>{\n\t\t\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\tproductSaveStocks(){\n\t\t\t\t\t\tlet attrs = [];\n\t\t\t\t\t\tthis.stockData.forEach(item=>{\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\t\"unique\":item.unique,\n\t\t\t\t\t\t\t\t\"pm\":item.pm,\n\t\t\t\t\t\t\t\t\"stock\":item.changeNum\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tattrs.push(data)\n\t\t\t\t\t\t})\n\t\t\t\t\t\tproductSaveStocksApi({attrs:attrs},this.id).then(res=>{\n\t\t\t\t\t\t\tthis.$Message.success('修改成功');\n\t\t\t\t\t\t\tthis.cancel();\n\t\t\t\t\t\t\tthis.$emit('stockChange',res.data.stock)\n\t\t\t\t\t\t}).catch(err=>{\n\t\t\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\tcancel(){\n\t\t\t\t\t\tthis.modals = false;\n\t\t\t\t\t\tthis.batchShow = false;\n\t\t\t\t\t\tthis.batchPm = 1;\n\t\t\t\t\t\tthis.batchStock = 0;\n\t\t\t\t\t}\n        }\n    }\n", null]}