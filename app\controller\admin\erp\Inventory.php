<?php
/**
 * 后台库存管理控制器
 * User: 系统生成
 * Date: 2024-12-16 18:00:00
 */

namespace app\controller\admin\erp;

use app\controller\admin\AuthController;
use app\services\erp\InventoryServices;
use think\facade\App;

/**
 * 后台库存管理控制器
 * Class Inventory
 * @package app\controller\admin\erp
 */
class Inventory extends AuthController
{
    /**
     * @var InventoryServices
     */
    protected $services;

    /**
     * 构造方法
     * Inventory constructor.
     * @param App $app
     * @param InventoryServices $services
     */
    public function __construct(App $app, InventoryServices $services)
    {
        parent::__construct($app);
        $this->services = $services;
    }

    /**
     * 获取库存列表
     * @return mixed
     */
    public function index()
    {
        $where = $this->request->getMore([
            ['keyword', ''],
            ['product_id', 0],
            ['stock_status', ''], // 库存状态：out(缺货)、warning(预警)、normal(正常)
            ['is_show', ''],
        ]);

        // 根据库存状态设置查询条件
        if ($where['stock_status'] === 'out') {
            $where[] = ['stock', '<=', 0];
        } elseif ($where['stock_status'] === 'warning') {
            $where[] = ['stock', '>', 0];
            $where[] = ['stock', '<=', 10];
        } elseif ($where['stock_status'] === 'normal') {
            $where[] = ['stock', '>', 10];
        }
        unset($where['stock_status']);

        // 关键词搜索
        if ($where['keyword']) {
            $where[] = ['sku|bar_code', 'like', '%' . $where['keyword'] . '%'];
        }
        unset($where['keyword']);

        $result = $this->services->getInventoryList($where);
        
        return $this->success($result);
    }

    /**
     * 获取库存详情
     * @param string $unique 规格唯一值
     * @return mixed
     */
    public function detail(string $unique)
    {
        if (!$unique) {
            return $this->fail('缺少商品规格参数');
        }

        try {
            $info = $this->services->getInventoryDetail($unique);
            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 库存调整
     * @return mixed
     */
    public function adjust()
    {
        $data = $this->request->postMore([
            ['unique', ''],
            ['adjust_stock', 0],
            ['remark', ''],
        ]);

        if (!$data['unique']) {
            return $this->fail('缺少商品规格参数');
        }

        if ($data['adjust_stock'] == 0) {
            return $this->fail('调整数量不能为0');
        }

        try {
            $result = $this->services->adjustStock(
                $data['unique'], 
                (int)$data['adjust_stock'], 
                $data['remark'] ?: '手动调整', 
                $this->adminId
            );

            if ($result) {
                return $this->success([], '库存调整成功');
            } else {
                return $this->fail('库存调整失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量库存调整
     * @return mixed
     */
    public function batchAdjust()
    {
        $adjustData = $this->request->post('adjust_data', []);

        if (empty($adjustData)) {
            return $this->fail('调整数据不能为空');
        }

        try {
            $result = $this->services->batchAdjustStock($adjustData, $this->adminId);
            
            if ($result) {
                return $this->success([], '批量调整成功');
            } else {
                return $this->fail('批量调整失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取库存预警列表
     * @return mixed
     */
    public function warning()
    {
        $warningStock = (int)$this->request->get('warning_stock', 10);
        
        try {
            $list = $this->services->getStockWarningList($warningStock);
            return $this->success([
                'list' => $list,
                'count' => count($list),
                'warning_stock' => $warningStock,
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取库存变动记录
     * @return mixed
     */
    public function record()
    {
        $where = $this->request->getMore([
            ['product_id', 0],
            ['unique', ''],
            ['type', ''], // 变动类型
            ['pm', ''], // 进出库：1入库 0出库
            ['start_time', ''],
            ['end_time', ''],
        ]);

        // 时间筛选
        if ($where['start_time']) {
            $where[] = ['add_time', '>=', strtotime($where['start_time'])];
        }
        if ($where['end_time']) {
            $where[] = ['add_time', '<=', strtotime($where['end_time'] . ' 23:59:59')];
        }
        unset($where['start_time'], $where['end_time']);

        try {
            $result = $this->services->getStockRecords($where);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取库存统计
     * @return mixed
     */
    public function statistics()
    {
        $where = $this->request->getMore([
            ['product_id', 0],
            ['cate_id', 0],
        ]);

        try {
            $stats = $this->services->getInventoryStats($where);
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取门店库存分布
     * @return mixed
     */
    public function storeDistribution()
    {
        $productId = (int)$this->request->get('product_id', 0);
        $unique = $this->request->get('unique', '');

        if (!$productId) {
            return $this->fail('缺少商品ID');
        }

        try {
            $distribution = $this->services->getStoreStockDistribution($productId, $unique);
            return $this->success($distribution);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 导出库存数据
     * @return mixed
     */
    public function export()
    {
        $where = $this->request->getMore([
            ['keyword', ''],
            ['product_id', 0],
            ['stock_status', ''],
        ]);

        // 处理查询条件
        if ($where['stock_status'] === 'out') {
            $where[] = ['stock', '<=', 0];
        } elseif ($where['stock_status'] === 'warning') {
            $where[] = ['stock', '>', 0];
            $where[] = ['stock', '<=', 10];
        } elseif ($where['stock_status'] === 'normal') {
            $where[] = ['stock', '>', 10];
        }
        unset($where['stock_status']);

        if ($where['keyword']) {
            $where[] = ['sku|bar_code', 'like', '%' . $where['keyword'] . '%'];
        }
        unset($where['keyword']);

        try {
            $exportData = $this->services->exportInventoryData($where);
            
            // 这里应该集成导出服务，简化处理
            return $this->success([
                'data' => $exportData,
                'filename' => '库存数据_' . date('YmdHis') . '.xlsx'
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取库存状态选项
     * @return mixed
     */
    public function statusOptions()
    {
        try {
            $options = [
                ['value' => '', 'label' => '全部'],
                ['value' => 'out', 'label' => '缺货'],
                ['value' => 'warning', 'label' => '预警'],
                ['value' => 'normal', 'label' => '正常'],
            ];
            
            return $this->success($options);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取变动类型选项
     * @return mixed
     */
    public function typeOptions()
    {
        try {
            $options = [
                ['value' => '', 'label' => '全部'],
                ['value' => 'manual', 'label' => '手动调整'],
                ['value' => 'sale', 'label' => '销售出库'],
                ['value' => 'transfer_in', 'label' => '调拨入库'],
                ['value' => 'transfer_out', 'label' => '调拨出库'],
                ['value' => 'adjust', 'label' => '库存调整'],
            ];
            
            return $this->success($options);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取门店选项
     * @return mixed
     */
    public function storeOptions()
    {
        try {
            $storeList = $this->services->getStoreOptions();
            return $this->success($storeList);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取商品选项
     * @return mixed
     */
    public function productOptions()
    {
        try {
            $productList = $this->services->getProductOptions();
            return $this->success($productList);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取商品销售统计（多门店多级表头结构）
     * @return mixed
     */
    public function salesStatistics()
    {
        $where = $this->request->getMore([
            ['keyword', ''],
            ['store_id', ''],
            ['start_date', ''],
            ['end_date', ''],
            ['page', 1],
            ['limit', 20]
        ]);

        try {
            $result = $this->services->getErpInventoryData($where);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 导出销售统计数据
     * @return mixed
     */
    public function exportSales()
    {
        $where = $this->request->getMore([
            ['keyword', ''],
            ['store_id', ''],
            ['start_date', ''],
            ['end_date', '']
        ]);

        try {
            $exportData = $this->services->exportSalesData($where);
            
            return $this->success([
                'data' => $exportData,
                'filename' => '商品销售统计_' . date('YmdHis') . '.xlsx'
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取门店选项
     */
    public function store_options()
    {
        $options = $this->services->getStoreOptions();
        return app('json')->success($options);
    }
    
    /**
     * 导出销售统计
     */
    public function export_sales_statistics()
    {
        $where = $this->request->getMore([
            ['keyword', ''],
            ['store_id', ''],
            ['start_date', ''],
            ['end_date', ''],
            ['sort', 0]
        ]);
        return $this->services->exportErpInventoryData($where);
    }
} 