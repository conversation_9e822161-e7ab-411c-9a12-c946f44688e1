{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\staffList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\staffList\\index.vue", "mtime": 1685091240000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { workDepartment, workMember } from \"@/api/work\";\nimport sideMenuItem from \"./menuChild.vue\";\nimport { workSynchMember } from \"@/api/work\";\nexport default {\n  name: \"\",\n  data: function data() {\n    return {\n      activeName: 1,\n      menuList: [],\n      tableFrom: {\n        department: \"\",\n        name: \"\",\n        page: 1,\n        limit: 15\n      },\n      loading: false,\n      columns1: [{\n        title: \"ID\",\n        key: \"id\",\n        minWidth: 60\n      }, {\n        title: \"员工名称\",\n        key: \"name\",\n        minWidth: 100\n      }, {\n        title: \"所属部门\",\n        slot: \"department_list\",\n        minWidth: 140\n      }, {\n        title: \"客户数量\",\n        slot: \"clientFollow\",\n        minWidth: 110\n      }, {\n        title: \"所在外部群数量\",\n        slot: \"chat\",\n        minWidth: 110\n      }, {\n        title: \"邮箱\",\n        key: \"biz_mail\",\n        minWidth: 130\n      }, {\n        title: \"激活状态\",\n        slot: \"status\",\n        minWidth: 60\n      }, {\n        title: \"创建时间\",\n        key: \"create_time\",\n        minWidth: 120\n      }],\n      tableData: [],\n      openMenu: []\n    };\n  },\n  components: {\n    sideMenuItem: sideMenuItem\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    }\n  }),\n  filters: {\n    typeFilter: function typeFilter(status) {\n      var statusMap = {\n        1: \"已激活\",\n        2: \"已禁用\",\n        4: \"未激活\",\n        5: \"退出企业\"\n      };\n      return statusMap[status];\n    }\n  },\n  created: function created() {\n    this.getWorkTree();\n    this.getMemberList();\n  },\n  methods: {\n    synchMember: function synchMember() {\n      var _this = this;\n\n      workSynchMember().then(function (res) {\n        _this.$Message.success(\"同步成功\");\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n      });\n    },\n    getMemberList: function getMemberList() {\n      var _this2 = this;\n\n      this.loading = true;\n      workMember(this.tableFrom).then(function (res) {\n        _this2.tableData = res.data;\n        _this2.loading = false;\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n\n        _this2.loading = false;\n      });\n    },\n    search: function search() {\n      this.tableFrom.page = 1;\n      this.getMemberList();\n    },\n    setDefault: function setDefault() {\n      this.tableFrom.page = 1;\n      this.tableFrom.department = \"\";\n      this.tableFrom.name = \"\";\n      this.getMemberList();\n    },\n    onOpenChange: function onOpenChange(name) {\n      var _this3 = this;\n\n      if (!name.length) {\n        this.tableFrom.department = \"\";\n        this.tableFrom.page = 1;\n        this.getMemberList();\n      } else {\n        this.$nextTick(function () {\n          _this3.tableFrom.department = _this3.activeName == 1 ? \"\" : _this3.activeName;\n\n          _this3.getMemberList();\n        });\n      }\n    },\n    onselect: function onselect(name) {\n      this.activeName = name;\n      this.tableFrom.department = name;\n      this.tableFrom.page = 1;\n      this.getMemberList();\n    },\n    getWorkTree: function getWorkTree() {\n      var _this4 = this;\n\n      workDepartment().then(function (res) {\n        // let obj = {\n        //     name: \"全部\",\n        //     department_id: \"\",\n        //     parentid: 1\n        // };\n        // res.data[0].children.unshift(obj);\n        _this4.menuList = res.data;\n        _this4.activeName == res.data[0].department_id;\n\n        _this4.getTreeId(_this4.menuList);\n\n        _this4.$nextTick(function () {\n          _this4.$refs.side_menu.updateOpened();\n\n          _this4.$refs.side_menu.updateActiveName();\n        });\n      });\n    },\n    getTreeId: function getTreeId(datas) {\n      for (var i in datas) {\n        this.openMenu.push(datas[i].department_id);\n\n        if (datas[i].children) {\n          this.getTreeId(datas[i].children);\n        }\n      }\n    },\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n      this.getMemberList();\n    }\n  }\n};", null]}