{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_img.vue?vue&type=template&id=0446237e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n\t<div class=\"button-style acea-row row-middle\">\n\t    <div class=\"title-tips\" v-if=\"configData\">\n\t        <span>{{configData.title}}</span>\n\t    </div>\n\t    <div class=\"style-box acea-row row-middle\" v-for=\"(item, index) in list\" :key=\"index\">\n\t\t\t<div class=\"pictrue acea-row row-center-wrapper\" :class=\"current == index?'on':''\" @click=\"tap(index)\">\n\t\t\t\t<img :src=\"item.url\" :style=\"{\n\t\t\t\t\twidth:item.width+'px',\n\t\t\t\t\theight:item.height+'px'\n\t\t\t\t}\"/>\n\t\t\t</div>\n\t    </div>\n\t</div>\n</div>\n", null]}