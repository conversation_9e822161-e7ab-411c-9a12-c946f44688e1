{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport AreaBox from \"./AreaBox\";\nimport linkaddress from \"@/components/linkaddress\";\n\nexport default {\n  name: \"OperationFloor\",\n  components: {\n    AreaBox,\n    linkaddress,\n  },\n  props: {\n    /**\n     * @description 图片数据对象\n     * @type {ImgData}\n     */\n    imgs: {\n      type: String, // 图片类型\n      default: () => \"\", // 默认值为空字符串\n    },\n    /**\n     * @description 是否为热门汤品\n     * @type {boolean}\n     */\n    isHotPot: {\n      type: Boolean, // 布尔类型\n      default: () => false, // 默认值为false\n    },\n    /**\n     * @description 图片区域数据对象\n     * @type {AreaData[]}\n     */\n    imgAreaData: {\n      type: Array, // 数组类型\n      default: () => [], // 默认值为空数组\n    },\n    /**\n     * @description 链接输入框样式对象\n     * @type {LinkInputStyle}\n     */\n    linkInputStyle: {\n      type: Object, // 对象类型\n      default: () => ({\n        // 默认值为一个包含width属性的对象\n        width: \"300px\",\n      }),\n    },\n  },\n  data() {\n    return {\n      /**\n       * @description 对话框是否可见\n       * @type {boolean}\n       */\n      dialogVisible: false,\n      /**\n       * @description 开始的x坐标\n       * @type {number}\n       */\n      starX: 0,\n      /**\n       * @description 开始的y坐标\n       * @type {number}\n       */\n      starY: 0,\n      /**\n       * @description 区域宽度\n       * @type {number}\n       */\n      areaWidth: 0,\n      /**\n       * @description 区域高度\n       * @type {number}\n       */\n      areaHeight: 0,\n      /**\n       * @description 当前显示的图片索引\n       * @type {boolean}\n       */\n      caseShow: false,\n      /**\n       * @description 当前图片的宽度\n       * @type {null}\n       */\n      nowImgWidth: null,\n      /**\n       * @description 区域数据\n       * @type {Array}\n       */\n      areaData: [],\n      /**\n       * @description 当前显示的图片编号\n       * @type {number}\n       */\n      imgNum: 1,\n      /**\n       * @description 父元素宽度\n       * @type {number}\n       */\n      parentWidth: 0,\n      /**\n       * @description 父元素高度\n       * @type {number}\n       */\n      parentHeight: 0,\n      /**\n       * @description 默认宽度\n       * @type {number}\n       */\n      defaultWidth: 750,\n      /**\n       * @description 当前显示的图片索引\n       * @type {number}\n       */\n      itemIndex: 0,\n    };\n  },\n  computed: {},\n  watch: {\n    imgAreaData(val) {\n      console.log(val, \"1111\");\n      this.areaData = [...val];\n    },\n  },\n  mounted() {\n    this.areaData = [...this.imgAreaData];\n  },\n  methods: {\n    openModal(type) {\n      if (type) {\n        this.$nextTick(() => {\n          const parentDiv = document.querySelector(\"#img-box-container\");\n          //获取元素的宽高\n          this.parentWidth = this.defaultWidth;\n          // this.parentWidth = parentDiv.clientWidth;\n          this.parentHeight = parentDiv.clientHeight;\n          // console.log(\"this.parentWidth\", this.parentWidth, this.parentHeight)\n        });\n      }\n    },\n    closeModal() {\n      this.$Modal.confirm({\n        title: \"提示信息\",\n        content: \"<p>未保存内容，是否在离开前放弃保存？</p>\",\n        okText: \"确认\",\n        cancelText: \"取消\",\n        onOk: () => {\n          this.$Modal.remove();\n          this.dialogVisible = false;\n        },\n      });\n    },\n    // 绘画热区开始\n    mouseDown(e) {\n      e.preventDefault();\n      this.caseShow = true;\n      // 记录滑动的初始值\n      this.starX = e.layerX;\n      this.starY = e.layerY;\n      // 鼠标滑动的过程\n      if (!document.onmousemove) {\n        let maxWidth = this.defaultWidth - e.layerX;\n        document.onmousemove = (ev) => {\n          if (ev.layerX - this.starX < maxWidth) {\n            this.areaWidth = ev.layerX - this.starX;\n          } else {\n            this.areaWidth = maxWidth;\n          }\n          this.areaHeight = ev.layerY - this.starY;\n        };\n      }\n    },\n    // 绘画热区结束\n    changeStop() {\n      document.onmousemove = null;\n      this.imgNum = this.areaData.length + 1;\n      if (this.caseShow && this.areaWidth > 10 && this.areaHeight > 10) {\n        const data = {\n          number: this.imgNum,\n          starX: this.starX,\n          starY: this.starY,\n          areaWidth: this.areaWidth,\n          areaHeight: this.areaHeight,\n          nowImgWidth: this.defaultWidth,\n          link: \"\",\n        };\n        this.areaData.push(data);\n      }\n      // 初始化绘图\n      this.caseShow = false;\n      this.starX = 0;\n      this.starY = 0;\n      this.areaWidth = 0;\n      this.areaHeight = 0;\n    },\n    // 删除指定热区\n    delAreaBox(index) {\n      /* 删除某个热区 */\n      this.areaData.splice(index, 1);\n      this.$emit(\"delAreaData\", this.areaData);\n      /* 删除后 每个热区按顺序重新编号 */\n      if (this.areaData) {\n        const arr = this.areaData.filter((i) => i.number > index);\n        if (!arr) return;\n        arr.forEach((i) => i.number--);\n        if (this.areaData[this.areaData.length - 1]) {\n          this.imgNum = this.areaData[this.areaData.length - 1].number + 1;\n        } else {\n          this.imgNum = 1;\n        }\n      }\n    },\n    // 添加网址\n    addURL(index, url) {\n      console.log(index, url);\n      let obj = {\n        ...this.areaData[index],\n        link: url,\n      };\n      this.$set(this.areaData, index, obj);\n    },\n    // 保存热区信息\n    saveAreaData() {\n      if (\n        (this.areaData && !this.areaData.length) ||\n        !this.checkData(this.areaData)\n      ) {\n        this.$Message.error(\"热区是否配置链接、是否至少添加一个热区?\");\n        return;\n      }\n      this.$emit(\"saveAreaData\", this.areaData);\n      this.dialogVisible = false;\n      this.$Message.success(\"编辑成功!\");\n    },\n    /**\n     * 检查列表中每个元素是否都有 link 属性\n     * @param {Array} list - 待检查的列表\n     * @returns {Boolean} - 是否所有元素都有 link 属性\n     */\n    checkData(list) {\n      let isCheck = true;\n      list.some((val) => {\n        if (!val.link) {\n          isCheck = false;\n        }\n      });\n      return isCheck;\n    },\n    /**\n     * @description 获取链接地址并打开添加链接的模态框\n     * @param {number} index - 当前项的索引值\n     */\n    getLink(index) {\n      // 设置当前项的索引值\n      this.itemIndex = index;\n      // 打开添加链接的模态框\n      this.$refs.linkaddres.modals = true;\n    },\n    /**\n     * @description 处理链接地址的输入事件\n     * @param {string} e - 链接地址\n     */\n    linkUrl(e) {\n      // 将链接地址存储到对应的数据项中\n      this.areaData[this.itemIndex].link = e;\n    },\n  },\n};\n", null]}