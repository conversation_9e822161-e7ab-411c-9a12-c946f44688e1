{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\lazyCascader\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\lazyCascader\\index.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  props: {\n    value: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    separator: {\n      type: String,\n      default: '/'\n    },\n    placeholder: {\n      type: String,\n      default: '请选择'\n    },\n    width: {\n      type: String,\n      default: '400px'\n    },\n    filterable: Boolean,\n    clearable: Boolean,\n    disabled: Boolean,\n    props: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    suggestionsPopperClass: {\n      type: String,\n      default: 'suggestions-popper-class'\n    },\n    searchWidth: {\n      type: String\n    },\n    searchEmptyText: {\n      type: String,\n      default: '暂无数据'\n    }\n  },\n  data: function data() {\n    return {\n      isSearchEmpty: false,\n      keyword: '',\n      options: [],\n      current: [],\n      labelObject: {\n        label: [],\n        value: []\n      },\n      labelArray: [],\n      currentProps: {\n        multiple: this.props.multiple,\n        checkStrictly: this.props.checkStrictly,\n        value: this.props.value,\n        label: this.props.label,\n        leaf: this.props.leaf,\n        lazy: true,\n        lazyLoad: this.lazyLoad\n      }\n    };\n  },\n  computed: {\n    placeholderVisible: function placeholderVisible() {\n      if (this.current) {\n        return this.current.length == 0;\n      } else {\n        return true;\n      }\n    }\n  },\n  watch: {\n    current: function current() {\n      this.getLabelArray();\n    },\n    value: function value(v) {\n      this.current = v;\n    },\n    keyword: function keyword() {\n      this.isSearchEmpty = false;\n    }\n  },\n  created: function created() {\n    this.initOptions();\n  },\n  methods: {\n    // 搜索是否选中\n    isChecked: function isChecked(value) {\n      // 多选\n      if (this.props.multiple) {\n        var index = this.current.findIndex(function (item) {\n          return item.join() == value.join();\n        });\n\n        if (index > -1) {\n          return 'el-link el-link--primary';\n        } else {\n          return '';\n        }\n      } else {\n        if (value.join() == this.current.join()) {\n          return 'el-link el-link--primary';\n        } else {\n          return '';\n        }\n      }\n    },\n    // 搜索\n    querySearch: function querySearch(query, callback) {\n      var _this = this;\n\n      this.props.lazySearch(query, function (list) {\n        callback(list);\n        if (!list || !list.length) _this.isSearchEmpty = true;\n      });\n    },\n    // 选中搜索下拉搜索项\n    handleSelect: function handleSelect(item) {\n      var _this2 = this;\n\n      if (this.props.multiple) {\n        var index = this.current.findIndex(function (obj) {\n          return obj.join() == item[_this2.props.value].join();\n        });\n\n        if (index == -1) {\n          this.$refs.panel.clearCheckedNodes();\n          this.current.push(item[this.props.value]);\n          this.$emit('change', this.current);\n        }\n      } else {\n        // 选中下拉选变更值\n        if (this.current == null || item[this.props.value].join() !== this.current.join()) {\n          this.$refs.panel.activePath = [];\n          this.current = item[this.props.value];\n          this.$emit('change', this.current);\n        }\n      }\n\n      this.keyword = '';\n    },\n    // 初始化数据\n    initOptions: function () {\n      var _initOptions = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        var _this3 = this;\n\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                this.props.lazyLoad(0, function (list) {\n                  _this3.$set(_this3, 'options', list);\n\n                  if (_this3.props.multiple) {\n                    _this3.current = _toConsumableArray(_this3.value);\n                  } else {\n                    _this3.current = _this3.value;\n                  }\n                });\n\n              case 1:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function initOptions() {\n        return _initOptions.apply(this, arguments);\n      }\n\n      return initOptions;\n    }(),\n    getLabelArray: function () {\n      var _getLabelArray = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee2() {\n        var _this4 = this;\n\n        var array, i, obj;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!this.props.multiple) {\n                  _context2.next = 16;\n                  break;\n                }\n\n                array = [];\n                i = 0;\n\n              case 3:\n                if (!(i < this.current.length)) {\n                  _context2.next = 11;\n                  break;\n                }\n\n                _context2.next = 6;\n                return this.getObject(this.current[i]);\n\n              case 6:\n                obj = _context2.sent;\n                array.push(obj);\n\n              case 8:\n                i++;\n                _context2.next = 3;\n                break;\n\n              case 11:\n                this.labelArray = array;\n                this.$emit('input', this.current);\n\n                if (!this.disabled) {\n                  this.$nextTick(function () {\n                    _this4.$refs.popover.updatePopper();\n                  });\n                }\n\n                _context2.next = 20;\n                break;\n\n              case 16:\n                _context2.next = 18;\n                return this.getObject(this.current || []);\n\n              case 18:\n                this.labelObject = _context2.sent;\n                this.$emit('input', this.current);\n\n              case 20:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function getLabelArray() {\n        return _getLabelArray.apply(this, arguments);\n      }\n\n      return getLabelArray;\n    }(),\n\n    /** 格式化id=>object */\n    getObject: function () {\n      var _getObject = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee3(id) {\n        var _this5 = this;\n\n        var options, nameArray, _loop, i, _ret;\n\n        return _regeneratorRuntime.wrap(function _callee3$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                options = this.options;\n                nameArray = [];\n                _loop =\n                /*#__PURE__*/\n                _regeneratorRuntime.mark(function _loop(i) {\n                  var index, list;\n                  return _regeneratorRuntime.wrap(function _loop$(_context3) {\n                    while (1) {\n                      switch (_context3.prev = _context3.next) {\n                        case 0:\n                          index = options.findIndex(function (item) {\n                            return item[_this5.props.value] == id[i];\n                          });\n\n                          if (!(index < 0)) {\n                            _context3.next = 3;\n                            break;\n                          }\n\n                          return _context3.abrupt(\"return\", \"continue\");\n\n                        case 3:\n                          nameArray.push(options[index][_this5.props.label]);\n\n                          if (!(i < id.length - 1 && !options[index].children.length)) {\n                            _context3.next = 15;\n                            break;\n                          }\n\n                          list = new Promise(function (resolve) {\n                            _this5.props.lazyLoad(id[i], function (list) {\n                              resolve(list);\n                            });\n                          });\n                          _context3.t0 = _this5;\n                          _context3.t1 = options[index];\n                          _context3.next = 10;\n                          return list;\n\n                        case 10:\n                          _context3.t2 = _context3.sent;\n\n                          _context3.t0.$set.call(_context3.t0, _context3.t1, 'children', _context3.t2);\n\n                          options = options[index].children;\n                          _context3.next = 16;\n                          break;\n\n                        case 15:\n                          options = options[index].children;\n\n                        case 16:\n                        case \"end\":\n                          return _context3.stop();\n                      }\n                    }\n                  }, _loop);\n                });\n                i = 0;\n\n              case 5:\n                if (!(i < id.length)) {\n                  _context4.next = 13;\n                  break;\n                }\n\n                return _context4.delegateYield(_loop(i), \"t0\", 7);\n\n              case 7:\n                _ret = _context4.t0;\n\n                if (!(_ret === \"continue\")) {\n                  _context4.next = 10;\n                  break;\n                }\n\n                return _context4.abrupt(\"continue\", 10);\n\n              case 10:\n                i++;\n                _context4.next = 5;\n                break;\n\n              case 13:\n                return _context4.abrupt(\"return\", {\n                  value: id,\n                  label: nameArray\n                });\n\n              case 16:\n                _context4.prev = 16;\n                _context4.t1 = _context4[\"catch\"](0);\n                this.current = [];\n                return _context4.abrupt(\"return\", {\n                  value: [],\n                  label: []\n                });\n\n              case 20:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee3, this, [[0, 16]]);\n      }));\n\n      function getObject(_x) {\n        return _getObject.apply(this, arguments);\n      }\n\n      return getObject;\n    }(),\n    // 懒加载数据\n    lazyLoad: function () {\n      var _lazyLoad = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee4(node, resolve) {\n        var _this6 = this;\n\n        var current;\n        return _regeneratorRuntime.wrap(function _callee4$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                current = this.current;\n\n                if (this.props.multiple) {\n                  current = _toConsumableArray(this.current);\n                }\n\n                if (node.root) {\n                  resolve();\n                } else if (node.data[this.props.leaf]) {\n                  resolve();\n                } else if (node.data.children && node.data.children.length) {\n                  if (this.props.multiple) {\n                    this.current = current;\n                  }\n\n                  resolve();\n                } else {\n                  this.props.lazyLoad(node.value, function (list) {\n                    node.data.children = list;\n\n                    if (_this6.props.multiple) {\n                      _this6.current = current;\n                    }\n\n                    resolve(list);\n                  });\n                }\n\n              case 3:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee4, this);\n      }));\n\n      function lazyLoad(_x2, _x3) {\n        return _lazyLoad.apply(this, arguments);\n      }\n\n      return lazyLoad;\n    }(),\n    // 删除多选值\n\n    /** 删除**/\n    handleClose: function handleClose(item) {\n      var index = this.current.findIndex(function (obj) {\n        return obj.join() == item.value.join();\n      });\n\n      if (index > -1) {\n        this.$refs.panel.clearCheckedNodes();\n        this.current.splice(index, 1);\n        this.$emit('change', this.current);\n      }\n    },\n    // 点击清空按钮\n    clearBtnClick: function clearBtnClick() {\n      this.$refs.panel.clearCheckedNodes();\n      this.current = [];\n      this.$emit('change', this.current);\n    },\n    change: function change() {\n      this.$emit('change', this.current);\n    }\n  }\n};", null]}