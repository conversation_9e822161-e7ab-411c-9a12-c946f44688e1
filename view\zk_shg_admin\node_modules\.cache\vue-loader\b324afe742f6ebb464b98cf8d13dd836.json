{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\refund\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\refund\\index.vue", "mtime": 1717468541000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {\n  orderRefundList,\n  orderList,\n  getOrdeDatas,\n  getDataInfo,\n  getRefundDataInfo,\n  getRefundFrom,\n  getRefundOrderFrom,\n  getnoRefund,\n  refundIntegral,\n  getDistribution,\n  writeUpdate,\n} from \"@/api/order\";\nimport { erpConfig } from \"@/api/erp\";\nimport editFrom from \"@/components/from/from\";\nimport detailsFrom from \"../orderList/handle/orderDetails\";\nimport orderRemark from \"../orderList/handle/orderRemark\";\nimport orderRecord from \"../orderList/handle/orderRecord\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  components: { editFrom, detailsFrom, orderRemark, orderRecord },\n  data() {\n    return {\n\t  openErp:false,\n      thead: [\n        {\n          title: \"订单号\",\n          align: \"center\",\n          slot: \"order_id\",\n          minWidth: 150,\n        },\n        {\n          title: \"用户信息\",\n          slot: \"nickname\",\n          minWidth: 130,\n        },\n        {\n          title: \"商品信息\",\n          slot: \"info\",\n          minWidth: 300,\n        },\n        {\n          title: \"实际支付\",\n          key: \"pay_price\",\n          minWidth: 70,\n        },\n        {\n          title: \"发起退款时间\",\n          key: \"add_time\",\n          minWidth: 110,\n        },\n        // {\n        //   title: \"订单类型\",\n        //   key: \"type\",\n        //   minWidth: 100,\n        // },\n        {\n          title: \"售后类型\",\n          slot: \"apply_type\",\n          minWidth: 180,\n        },\n        {\n          title: \"退款状态\",\n          slot: \"refund_type\",\n          minWidth: 180,\n        },\n        {\n          title: \"退款信息\",\n          slot: \"statusName\",\n          minWidth: 100,\n        },\n        {\n          title: \"售后备注\",\n          key: \"remark\",\n          minWidth: 80,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n          minWidth: 150,\n          align: \"center\",\n        },\n      ],\n      tbody: [],\n      num: [],\n      orderDatalist: null,\n      loading: false,\n      FromData: null,\n      total: 0,\n      orderId: 0,\n      animal: 1,\n      pagination: {\n        page: 1,\n        limit: 15,\n        order_id: \"\",\n        time: \"\",\n        refund_type: 0,\n      },\n      options: timeOptions,\n      timeVal: [],\n      modal: false,\n      qrcode: null,\n      name: \"\",\n      spin: false,\n      rowActive: {},\n    };\n  },\n  computed: {\n    ...mapState(\"order\", [\"orderChartType\"]),\n    // ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n\tthis.getErpConfig();  \n    this.getOrderList();\n  },\n  methods: {\n\t//erp配置\n\tgetErpConfig(){\n\t\terpConfig().then(res=>{\n\t\t\tthis.openErp = res.data.open_erp;\n\t\t}).catch(err=>{\n\t\t\tthis.$Message.error(err.msg);\n\t\t})\n\t},  \n    onchangeCode(e) {\n      this.animal = e;\n      this.qrcodeShow();\n    },\n    // 具体日期搜索()；\n    onchangeTime(e) {\n      this.pagination.page = 1;\n      this.timeVal = e;\n      this.pagination.time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n\t  this.getOrderList();\n    },\n    // 获取详情表单数据\n    getData(id, type) {\n      getRefundDataInfo(id)\n        .then(async (res) => {\n          if (!type) {\n            this.$refs.detailss.modals = true;\n          }\n          this.$refs.detailss.activeName = \"detail\";\n          this.orderDatalist = res.data;\n          // if (this.orderDatalist.orderInfo.refund_img) {\n          //   try {\n          //     this.orderDatalist.orderInfo.refund_img = JSON.parse(\n          //       this.orderDatalist.orderInfo.refund_img\n          //     );\n          //   } catch (e) {\n          //     this.orderDatalist.orderInfo.refund_img = [];\n          //   }\n          // }\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 操作\n    changeMenu(row, name) {\n      this.orderId = row.id;\n      switch (name) {\n        case \"1\":\n          this.delfromData = {\n            title: \"修改立即支付\",\n            url: `/order/pay_offline/${row.id}`,\n            method: \"post\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.getOrderList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          // this.modalTitleSs = '修改立即支付';\n          break;\n        case \"2\":\n          this.rowActive = row;\n          this.getData(row.id);\n          break;\n        case \"3\":\n          this.$refs.record.modals = true;\n          this.$refs.record.getList(row.store_order_id);\n          break;\n        case \"4\":\n          this.$refs.remarks.modals = true;\n          this.$refs.remarks.formValidate.remark = row.remark;\n          break;\n        case \"5\":\n          this.getRefundData(row.id, row.refund_type);\n          break;\n        case '55':\n          this.getRefundGoodsData(row.id, row.refund_type)\n          break\n        case \"6\":\n          this.getRefundIntegral(row.id);\n          break;\n        case \"7\":\n          this.getNoRefundData(row.id);\n          break;\n        case \"8\":\n          this.delfromData = {\n            title: \"修改确认收货\",\n            url: `/order/take/${row.id}`,\n            method: \"put\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.getOrderList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          // this.modalTitleSs = '修改确认收货';\n          break;\n        case \"10\":\n          this.delfromData = {\n            title: \"立即打印订单\",\n            info: \"您确认打印此订单吗?\",\n            url: `/order/print/${row.id}`,\n            method: \"get\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.$emit(\"changeGetTabs\");\n              this.getOrderList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          break;\n        case \"11\":\n          this.delfromData = {\n            title: \"立即打印电子面单\",\n            info: \"您确认打印此电子面单吗?\",\n            url: `/order/order_dump/${row.id}`,\n            method: \"get\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.getOrderList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          break;\n        default:\n          this.delfromData = {\n            title: \"删除订单\",\n            url: `/order/del/${row.id}`,\n            method: \"DELETE\",\n            ids: \"\",\n          };\n          // this.modalTitleSs = '删除订单';\n          this.delOrder(row, this.delfromData);\n      }\n    },\n    // 获取退款表单数据\n    getRefundData(id, refund_type) {\n      if (refund_type == 2) {\n        this.delfromData = {\n          title: \"是否立即退货\",\n          url: `/refund/agree/${id}`,\n          method: \"get\",\n        };\n        this.$modalSure(this.delfromData)\n          .then((res) => {\n            this.$Message.success(res.msg);\n            this.getOrderList();\n            this.getData(this.orderId, 1);\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n      } else {\n        this.$modalForm(getRefundOrderFrom(id)).then(() => {\n          this.getOrderList();\n          this.getData(this.orderId, 1);\n          this.$emit(\"changeGetTabs\");\n        });\n      }\n    },\n    //同意退货\n    getRefundGoodsData(id) {\n      this.delfromData = {\n        title: '是否立即退货',\n        url: `/refund/agree/${id}`,\n        method: 'get',\n      }\n      this.$modalSure(this.delfromData)\n          .then((res) => {\n            this.$Message.success(res.msg)\n            this.getOrderList()\n            this.getData(this.orderId, 1)\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg)\n          })\n    },\n    // 获取退积分表单数据\n    getRefundIntegral(id) {\n      refundIntegral(id)\n        .then(async (res) => {\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 删除单条订单\n    delOrder(row, data) {\n      if (row.is_del === 1) {\n        this.$modalSure(data)\n          .then((res) => {\n            this.$Message.success(res.msg);\n            this.getOrderList();\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n      } else {\n        const title = \"错误！\";\n        const content =\n          \"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>\";\n        this.$Modal.error({\n          title: title,\n          content: content,\n        });\n      }\n    },\n    // 修改成功\n    submitFail() {\n      this.getOrderList();\n      this.getData(this.orderId, 1);\n    },\n    // 订单选择状态\n    selectChange2(tab) {\n      this.pagination.page = 1;\n      this.pagination.refund_type = tab;\n      this.getOrderList(tab);\n    },\n    // 不退款表单数据\n    getNoRefundData(id) {\n      this.$modalForm(getnoRefund(id)).then(() => {\n        this.getOrderList();\n        this.getData(this.orderId);\n        this.$emit(\"changeGetTabs\");\n      });\n    },\n    // 订单列表\n    getOrderList() {\n      this.loading = true;\n      orderRefundList(this.pagination)\n        .then((res) => {\n          this.loading = false;\n          const { count, list, num } = res.data;\n          this.total = count;\n          this.tbody = list;\n          this.num = num;\n          list.forEach((item) => {\n            if (item.id == this.orderId) {\n              this.rowActive = item;\n            }\n          });\n        })\n        .catch((err) => {\n          this.loading = false;\n          this.$Message.error(err.msg);\n        });\n    },\n    // 分页\n    pageChange(index) {\n      this.pagination.page = index;\n      this.getOrderList();\n    },\n    nameSearch() {\n      this.pagination.page = 1;\n      this.getOrderList();\n    },\n    // 订单搜索\n    orderSearch() {\n      this.pagination.page = 1;\n      this.getOrderList();\n    },\n    // 配送信息表单数据\n    delivery(row) {\n      getDistribution(row.id)\n        .then(async (res) => {\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n  },\n};\n", null]}