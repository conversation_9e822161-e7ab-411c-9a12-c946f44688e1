{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue", "mtime": 1690191972000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import {releasedListApi} from '@/api/marketing'\n    import {formatDate} from '@/utils/validate'\n\n    export default {\n        name: 'index',\n        filters: {\n            formatDate(time) {\n                if (time !== 0) {\n                    let date = new Date(time * 1000)\n                    return formatDate(date, 'yyyy-MM-dd hh:mm')\n                }\n            }\n        },\n        props: {\n            couponids: {\n                type: Array\n            },\n            updateIds:{\n                type:Array\n            },\n            updateName:{\n                type:Array\n            },\n            luckDraw: {\n                type: Boolean,\n                default: false,\n            },\n\t\t\t\t\t\tdiscount: {\n\t\t\t\t\t\t    type: Boolean,\n\t\t\t\t\t\t    default: false,\n\t\t\t\t\t\t}\n        },\n        data() {\n            return {\n                currentid: 0,\n                productRow: {},\n                isTemplate: false,\n                loading: false,\n                tableFrom: {\n                    receive_type: 3,\n                    type: 'send',\n                    page: 1,\n                    limit: 10\n                },\n                total: 0,\n                ids: [],\n                texts: [],\n                columns: [\n                    {\n                        title: 'ID',\n                        key: 'id',\n                        width: 60\n                    },\n                    {\n                        title: '优惠券名称',\n                        key: 'title',\n                        minWidth: 150\n                    },\n                    {\n                        title: '适用类型',\n                        slot: 'type',\n                        minWidth: 80\n                    },\n                    {\n                        title: '面值',\n                        slot: 'coupon_price',\n                        minWidth: 100\n                    },\n                    {\n                        title: '最低消费额',\n                        key: 'use_min_price',\n                        minWidth: 100\n                    },\n                    {\n                        title: '发布数量',\n                        slot: 'count',\n                        minWidth: 120\n                    },\n                    {\n                        title: '有效期限',\n                        slot: 'start_time',\n                        minWidth: 120\n                    },\n                    {\n                        title: '状态',\n                        slot: 'status',\n                        minWidth: 80\n                    }\n                ],\n                couponList: [],\n                selectedIds: new Set(),\n                selectedNames: new Set(),\n\t\t\t\t\t\t\t\tcouponVal: []\n            }\n        },\n        mounted() {\n        },\n        watch: {\n            'updateIds': function(newVal){\n                this.selectedIds = new Set(newVal);\n            },\n            'updateName': function (newVal) {\n                this.selectedNames = new Set(newVal);\n            }\n        },\n        created(){\n            let radio = {\n                width: 60,\n                align: \"center\",\n                render: (h, params) => {\n                    let id = params.row.id;\n                    let flag = false;\n                    if (this.currentid === id) {\n                        flag = true;\n                    } else {\n                        flag = false;\n                    }\n                    let self = this;\n                    return h(\"div\", [\n                        h(\"Radio\", {\n                            props: {\n                                value: flag,\n                            },\n                            on: {\n                                \"on-change\": () => {\n                                    self.currentid = id;\n                                    this.productRow = params.row;\n                                },\n                            },\n                        }),\n                    ]);\n                },\n            };\n\n            let checkbox = {\n                type: \"selection\",\n                width: 60,\n                align: \"center\",\n            };\n            if(this.luckDraw){\n                this.columns.unshift(radio);\n            }else {\n                this.columns.unshift(checkbox);\n            }\n        },\n        methods: {\n            //对象数组去重；\n            unique(arr) {\n                const res = new Map();\n                return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1))\n            },\n\t\t\t\t\t\tchangeCheckbox(selection) {\n\t\t\t\t\t\t\tthis.couponVal = selection;\n\t\t\t\t\t\t},\n            handleSelectAll (selection) {\n\t\t\t\t\t\t\t  if(this.discount) return\n                // 取消全选 数组为空\n                if (selection.length === 0) {\n                    // cy 若取消全选，删除保存在selectedIds里和当前table数据的id一致的数据，达到，当前页取消全选的效果\n                    // 当前页的table数据\n                    let that = this;\n                    let data = that.$refs.table.data;\n                    data.forEach((item) => {\n                        if (that.selectedIds.has(item.id)) {\n                            that.selectedIds.delete(item.id);\n                            let nameList = that.unique(Array.from(that.selectedNames));\n                            that.unique(Array.from(that.selectedNames)).forEach((j,index)=>{\n                                if(j.id === item.id){\n                                    nameList.splice(index,1);\n                                }\n                            });\n                            that.selectedNames = new Set(nameList);\n                            // this.selectedNames.clear();\n                        }\n                    })\n                } else {\n                    selection.forEach(item => {\n                        this.selectedIds.add(item.id)\n                        this.selectedNames.add({id:item.id,title:item.title})\n                    })\n                }\n                this.$nextTick(() => {//确保dom加载完毕\n                    this.setChecked();\n                });\n            },\n\t\t\t\t\t\t\n            //  选中某一行\n            handleSelectRow (selection,row) {\n\t\t\t\t\t\t\t  if(this.discount) return\n                this.selectedIds.add(row.id);\n                this.selectedNames.add({id:row.id,title:row.title})\n                this.$nextTick(() => {//确保dom加载完毕\n                    this.setChecked();\n                });\n            },\n            //  取消某一行\n            handleCancelRow (selection,row) {\n\t\t\t\t\t\t\t  if(this.discount) return\n                let that = this;\n                that.selectedIds.delete(row.id);\n                let nameList = Array.from(that.selectedNames);\n                Array.from(that.selectedNames).forEach((item,index)=>{\n                    if(item.id === row.id){\n                        nameList.splice(index,1);\n                    }\n                });\n                that.selectedNames = new Set(nameList);\n                this.$nextTick(() => {//确保dom加载完毕\n                    this.setChecked();\n                });\n            },\n            setChecked () {\n                this.ids = [...this.selectedIds];\n                this.texts = [...this.selectedNames];\n                // 找到绑定的table的ref对应的dom，找到table的objData对象，objData保存的是当前页的数据\n                let objData = this.$refs.table.objData\n                for (let index in objData) {\n                    if (this.selectedIds.has(objData[index].id)) {\n                        // cy 弊端 每次切换select都会触发table的on-select事件\n                        // this.$refs.purchaseTable.toggleSelect(index) // 在保存选中的ids的set合集里找与当前页数据id一样的行，使用toggleSelect（index），将这一行选中\n                        // cy 改进\n                        objData[index]._isChecked = true;\n                    }\n                }\n            },\n            cancel() {\n                this.isTemplate = false\n                if(this.luckDraw){\n                    this.currentid = 0;\n                }\n            },\n            tableList() {\n                this.loading = true\n                releasedListApi(this.tableFrom).then(res => {\n                    let data = res.data\n                    this.couponList = data.list\n\t\t\t\t\t\t\t\t\t\tif(!this.discount){\n\t\t\t\t\t\t\t\t\t\t\tthis.$nextTick(() => {//确保dom加载完毕\n\t\t\t\t\t\t\t\t\t\t\t    this.setChecked();\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n                    this.total = data.count\n                    this.loading = false\n                })\n            },\n            ok() {\n                if(this.luckDraw){\n                    this.$emit(\"getCouponId\", this.productRow);\n                    this.currentid = 0;\n                }else if(this.discount){\n\t\t\t\t\t\t\t\t\tthis.$emit(\"getCouponList\", this.couponVal);\n\t\t\t\t\t\t\t\t}else {\n                    this.$emit('nameId', this.ids,this.texts)\n                }\n            },\n            receivePageChange(index) {\n                this.tableFrom.page = index\n                this.tableList()\n            }\n        }\n    }\n", null]}