{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_wechat_live.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_wechat_live.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { mapMutations } from 'vuex'\n    export default {\n        name: 'c_wechat_live',\n        componentsName: 'wechat_live',\n        cname: '小程序直播',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        data () {\n            return {\n                // 组件参数配置\n                option: {\n                    submitBtn: false\n                },\n                configObj: {}, // 配置对象\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ] // 当前页面组件\n            }\n        },\n        watch: {\n            num (nVal) {\n                // debugger;\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n                        let tempArr = [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t\t\t    configNme: 'styleConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleContent'\n\t\t\t\t\t\t\t},\n                            {\n                                components: toolCom.c_slider,\n                                configNme: 'numberConfig'\n                            },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_checkbox,\n\t\t\t\t\t\t\t    configNme: 'checkboxInfo'\n\t\t\t\t\t\t\t}\n                        ]\n                        this.rCom = arr.concat(tempArr)\n                    } else {\n                        let tempArr = [\n                            {\n                            \tcomponents: toolCom.c_title,\n                            \tconfigNme: 'titleRight'\n                            },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'liveConfig'\n\t\t\t\t\t\t\t},\n                            {\n                                components: toolCom.c_fillet,\n                                configNme: 'filletImg'\n                            },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t\t\t}\n                        ]\n                        this.rCom = arr.concat(tempArr)\n                    }\n                },\n                deep: true\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n            getConfig (data) {\n\n            }\n        }\n    }\n", null]}