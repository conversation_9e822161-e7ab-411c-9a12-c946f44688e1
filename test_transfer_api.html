<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调拨管理API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>调拨管理API测试</h1>
    
    <div class="test-section">
        <div class="test-title">1. 测试门店选项接口</div>
        <button onclick="testStoreOptions()">测试门店选项</button>
        <div id="storeResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">2. 测试商品搜索接口</div>
        <input type="text" id="searchKeyword" placeholder="输入商品关键词" value="枸杞">
        <button onclick="testSearchProducts()">搜索商品</button>
        <div id="searchResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">3. 测试调拨列表接口</div>
        <button onclick="testTransferList()">获取调拨列表</button>
        <div id="listResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">4. 测试统计数据接口</div>
        <button onclick="testStatistics()">获取统计数据</button>
        <div id="statsResult" class="result"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8080/adminapi/erp/transfer';
        
        // 模拟的token，实际使用时需要从登录获取
        const token = 'your-admin-token-here';
        
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'result success';
                element.innerHTML = `<strong>成功:</strong><br><pre>${JSON.stringify(result.data, null, 2)}</pre>`;
            } else {
                element.className = 'result error';
                element.innerHTML = `<strong>错误:</strong> ${result.error}`;
            }
        }
        
        async function testStoreOptions() {
            const result = await apiRequest(`${baseUrl}/store_options`);
            displayResult('storeResult', result);
        }
        
        async function testSearchProducts() {
            const keyword = document.getElementById('searchKeyword').value;
            const result = await apiRequest(`${baseUrl}/search_products?keyword=${encodeURIComponent(keyword)}`);
            displayResult('searchResult', result);
        }
        
        async function testTransferList() {
            const result = await apiRequest(`${baseUrl}/list?page=1&limit=10`);
            displayResult('listResult', result);
        }
        
        async function testStatistics() {
            const result = await apiRequest(`${baseUrl}/statistics`);
            displayResult('statsResult', result);
        }
    </script>
</body>
</html>
