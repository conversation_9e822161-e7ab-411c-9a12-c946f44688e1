{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue", "mtime": 1698031562000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import { getExpress, getIntegralOrderRecord } from '@/api/marketing';\n    export default {\n        name: 'orderDetails',\n        data () {\n            return {\n                modal2: false,\n                modals: false,\n                grid: {\n                    xl: 8,\n                    lg: 8,\n                    md: 12,\n                    sm: 24,\n                    xs: 24\n                },\n                result: [],\n\t\t\t\t\t\t\t\tcolumns1: [\n\t\t\t\t\t\t\t\t        {\n\t\t\t\t\t\t\t\t            title: '商品信息',\n\t\t\t\t\t\t\t\t            slot: 'product',\n\t\t\t\t\t\t\t\t            align: 'center',\n\t\t\t\t\t\t\t\t            minWidth: 400\n\t\t\t\t\t\t\t\t        },\n\t\t\t\t\t\t\t\t        {\n\t\t\t\t\t\t\t\t            title: '积分兑换',\n                            slot: 'integral',\n\t\t\t\t\t\t\t\t            align: 'center'\n\t\t\t\t\t\t\t\t        },\n                        {\n                            title: '金额兑换',\n                            slot: 'price',\n                            align: 'center'\n                        },\n\t\t\t\t\t\t\t\t        {\n\t\t\t\t\t\t\t\t            title: '数量',\n\t\t\t\t\t\t\t\t            key: 'cart_num',\n\t\t\t\t\t\t\t\t            align: 'center'\n\t\t\t\t\t\t\t\t        },\n\t\t\t\t\t\t\t\t        {\n\t\t\t\t\t\t\t\t            title: '小计',\n\t\t\t\t\t\t\t\t            align: 'center',\n\t\t\t\t\t\t\t\t            render: (h, params) => {\n\t\t\t\t\t\t\t\t\treturn h('div', (params.row.attrInfo?params.row.attrInfo.integral:params.row.integral) * params.row.cart_num + '积分+￥' + (params.row.attrInfo?params.row.attrInfo.price:params.row.price) * params.row.cart_num);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t        }\n\t\t\t\t\t\t\t\t    ],\n\t\t\t\t\t\t\t\t    columns2: [\n\t\t\t\t\t\t\t\t        {\n\t\t\t\t\t\t\t\t            title: '订单ID',\n\t\t\t\t\t\t\t\t            key: 'oid',\n\t\t\t\t\t\t\t\t            align: 'center',\n\t\t\t\t\t\t\t\t            minWidth: 40\n\t\t\t\t\t\t\t\t        },\n\t\t\t\t\t\t\t\t        {\n\t\t\t\t\t\t\t\t            title: '操作记录',\n\t\t\t\t\t\t\t\t            key: 'change_message',\n\t\t\t\t\t\t\t\t            align: 'center',\n\t\t\t\t\t\t\t\t            minWidth: 280\n\t\t\t\t\t\t\t\t        },\n\t\t\t\t\t\t\t\t        {\n\t\t\t\t\t\t\t\t            title: '操作时间',\n\t\t\t\t\t\t\t\t            key: 'change_time',\n\t\t\t\t\t\t\t\t            align: 'center',\n\t\t\t\t\t\t\t\t            minWidth: 100\n\t\t\t\t\t\t\t\t        }\n\t\t\t\t\t\t\t\t    ],\n\t\t\t\t\t\t\t\t    recordData: [],\n\t\t\t\t\t\t\t\t\t\tactiveName:'detail',\n\t\t\t\t\t\t\t\t\t\torderData:{}\n            }\n        },\n        props: {\n            orderDatalist: Object,\n            orderId: Number,\n\t\t\trowActive: Object,\n\t\t\topenErp:{\n\t\t\t\ttype:Boolean,\n\t\t\t\tdefault:false,\n\t\t\t}\n        },\n\t\t\t\twatch: {\n\t\t\t\t\torderDatalist (value) {\n\t\t\t\t\t\tthis.orderData = value.orderInfo;\n\t\t\t\t\t\tthis.getList(value.orderInfo.id);\n\t\t\t\t\t}\n\t\t\t\t},\n        methods: {\n            openLogistics () {\n                this.getOrderData()\n                this.modal2 = true;\n            },\n            // 获取订单物流信息\n            getOrderData () {\n                getExpress(this.orderId).then(async res => {\n                    this.result = res.data.result;\n                }).catch(res => {\n                    this.$Message.error(res.msg);\n                })\n            },\n\t\t\t\t\t\tgetList (id) {\n\t\t\t\t\t\t    let data = {\n\t\t\t\t\t\t        id: id,\n\t\t\t\t\t\t        datas: this.page\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t    this.loading = true;\n\t\t\t\t\t\t    getIntegralOrderRecord(data).then(async res => {\n\t\t\t\t\t\t        this.recordData = res.data;\n\t\t\t\t\t\t        this.loading = false;\n\t\t\t\t\t\t    }).catch(res => {\n\t\t\t\t\t\t        this.loading = false;\n\t\t\t\t\t\t        this.$Message.error(res.msg);\n\t\t\t\t\t\t    })\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchangeMenu (value) {\n\t\t\t\t\t\t    this.$parent.changeMenu(this.rowActive, value);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsendOrder () {\n\t\t\t\t\t\t    this.$parent.sendOrder(this.rowActive);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdelivery () {\n\t\t\t\t\t\t    this.$parent.delivery(this.rowActive);\n\t\t\t\t\t\t}\n        },\n        computed: {\n        }\n    }\n", null]}