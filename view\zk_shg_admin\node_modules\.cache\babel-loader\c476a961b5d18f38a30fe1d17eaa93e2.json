{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableList.vue", "mtime": 1733823002405}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport Distribution from './distribution.vue';\nimport expandRow from './tableExpand.vue';\nimport { orderList, getOrdeDatas, getDataInfo, getRefundFrom, getnoRefund, refundIntegral, getDistribution, writeUpdate, storeOrderApi, handBatchDelivery, putWrite, exportExpressList, remindOrder, putOpenRefund as _putOpenRefund } from '@/api/order';\nimport { erpConfig } from '@/api/erp';\nimport { mapState, mapMutations } from 'vuex';\nimport editFrom from '../../../../components/from/from';\nimport detailsFrom from '../handle/orderDetails';\nimport orderRemark from '../handle/orderRemark';\nimport orderRecord from '../handle/orderRecord';\nimport orderSend from '../handle/orderSend';\nimport userDetails from '@/pages/user/list/handle/userDetails';\nimport autoSend from '../handle/autoSend';\nimport queueList from '../handle/queueList';\nimport Setting from '@/setting';\nimport util from '@/libs/util';\nimport exportExcel from '@/utils/newToExcel.js';\nimport Template from '../../../setting/devise/template.vue';\nimport printJS from 'print-js';\nexport default {\n  name: 'table_list',\n  components: {\n    expandRow: expandRow,\n    editFrom: editFrom,\n    detailsFrom: detailsFrom,\n    orderRemark: orderRemark,\n    orderRecord: orderRecord,\n    orderSend: orderSend,\n    userDetails: userDetails,\n    Distribution: Distribution,\n    autoSend: autoSend,\n    queueList: queueList,\n    Template: Template\n  },\n  props: ['where', 'currentTab'],\n  data: function data() {\n    var codeNum = function codeNum(rule, value, callback) {\n      if (!value) {\n        return callback(new Error('请填写核销码'));\n      } // 模拟异步验证效果\n\n\n      if (!Number.isInteger(value)) {\n        callback(new Error('请填写12位数字'));\n      } else {\n        // const reg = /[0-9]{12}/;\n        var reg = /\\b\\d{12}\\b/;\n\n        if (!reg.test(value)) {\n          callback(new Error('请填写12位数字'));\n        } else {\n          callback();\n        }\n      }\n    };\n\n    return {\n      roterPre: Setting.roterPre,\n      openErp: false,\n      // currentTab: '-1',\n      distshow: false,\n      //分配的弹窗\n      delfromData: {},\n      modal: false,\n      orderList: [],\n      pay_type: '',\n      orderCards: [],\n      loading: false,\n      orderId: 0,\n      page: {\n        total: 0,\n        // 总条数\n        pageNum: 1,\n        // 当前页\n        pageSize: 10 // 每页显示条数\n\n      },\n      data: [],\n      FromData: null,\n      orderDatalist: null,\n      modalTitleSs: '',\n      isDelIdList: [],\n      checkBox: false,\n      formSelection: [],\n      display: 'none',\n      autoDisabled: false,\n      status: 0,\n      //发货状态判断\n      isAll: 0,\n      rowActive: {},\n      tablists: {},\n      selectArr: [],\n      exportList: [{\n        name: '1',\n        label: '导出发货单'\n      }, {\n        name: '0',\n        label: '导出订单'\n      }],\n      exportListOn: 0,\n      manualModal: false,\n      uploadAction: \"\".concat(Setting.apiBaseURL, \"/file/upload/1\"),\n      uploadHeaders: {},\n      autoModal: false,\n      isShow: false,\n      recordModal: false,\n      sendOutValue: '',\n      fileList: [],\n      file: '',\n      modals2: false,\n      writeOffRules: {\n        code: [{\n          validator: codeNum,\n          trigger: 'blur',\n          required: true\n        }]\n      },\n      writeOffFrom: {\n        code: '',\n        confirm: 0\n      },\n      orderConNum: 0,\n      orderConId: 0,\n      checkUidList: [],\n      refundModal: false,\n      refundColumns: [{\n        type: 'selection',\n        width: 60,\n        align: 'center'\n      }, {\n        title: '商品信息',\n        width: 210,\n        slot: 'product'\n      }, {\n        title: '规格',\n        render: function render(h, params) {\n          return h('div', params.row.productInfo.attrInfo.suk);\n        }\n      }, {\n        title: '售价',\n        render: function render(h, params) {\n          return h('div', params.row.productInfo.attrInfo.price);\n        }\n      }, {\n        title: '优惠价',\n        key: 'refundPrice'\n      }, {\n        title: '总数',\n        key: 'cart_num'\n      }, {\n        title: '退款数量',\n        slot: 'action',\n        width: 160\n      }],\n      refundProduct: [],\n      refundSelection: [],\n      refundMoney: 0,\n      is_split_order: 0,\n      orderDataStatus: ''\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {}, mapState('admin/order', ['orderPayType', 'orderStatus', 'orderTime', 'orderNum', 'fieldKey', 'orderType', 'orderChartType', 'supplier_id', 'store_id', 'type_id']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    },\n    refundProductNum: function refundProductNum() {\n      return this.refundProduct.reduce(function (total, _ref) {\n        var refundNum = _ref.refundNum;\n        return total + refundNum;\n      }, 0);\n    }\n  }),\n  mounted: function mounted() {},\n  created: function created() {\n    this.getList();\n    this.getToken();\n    this.getErpConfig();\n  },\n  watch: {\n    currentTab: function currentTab() {\n      this.onClickTab();\n    },\n    orderType: function orderType() {\n      this.page.pageNum = 1;\n      this.getList();\n    },\n    formSelection: function formSelection(value) {// this.$emit('order-select', value)\n      // if (value.length) {\n      //   this.$emit('auto-disabled', 0)\n      // } else {\n      //   this.$emit('auto-disabled', 1)\n      // }\n      // let isDel = value.some((item) => {\n      //   return item.is_del === 1\n      // })\n      // this.getIsDel(isDel)\n      // this.getisDelIdListl(value)\n    },\n    orderList: {\n      deep: true,\n      handler: function handler(value) {\n        var _this = this;\n\n        value.forEach(function (item) {\n          _this.formSelection.forEach(function (itm) {\n            if (itm.id === item.id) {\n              item.checkBox = true;\n            }\n          });\n        });\n        var arr = this.orderList.filter(function (item) {\n          return item.checkBox;\n        });\n\n        if (this.orderList.length) {\n          this.checkBox = this.orderList.length === arr.length;\n        } else {\n          this.checkBox = false;\n        }\n      }\n    },\n    refundSelection: {\n      handler: function handler(value) {\n        var _this2 = this;\n\n        this.refundMoney = value.reduce(function (total, _ref2) {\n          var refundPrice = _ref2.refundPrice,\n              refundNum = _ref2.refundNum;\n          return _this2.$computes.Add(total, _this2.$computes.Mul(refundPrice, refundNum));\n        }, 0);\n      },\n      deep: true\n    },\n    is_split_order: function is_split_order(value) {\n      var _this3 = this;\n\n      this.$nextTick(function () {\n        _this3.$refs.refundTable.selectAll(!!value);\n      });\n    },\n    refundMoney: function refundMoney(value) {\n      var _this4 = this;\n\n      this.$nextTick(function () {\n        if (typeof value != 'number') {\n          return;\n        }\n\n        if (parseFloat(value) == parseInt(value)) {\n          return;\n        }\n\n        if (value.toString().length - (value.toString().indexOf('.') + 1) > 2) {\n          _this4.refundMoney = Number(value.toFixed(2));\n        }\n      });\n    }\n  },\n  methods: _objectSpread({}, mapMutations('admin/order', ['getIsDel', 'getisDelIdListl', 'onChangeTabs', 'getStore_id', 'getSupplier_id']), {\n    visibleChange: function visibleChange(visible) {\n      this.is_split_order = 0;\n\n      if (!visible) {\n        this.refundSelection = [];\n      }\n    },\n    cancelRefundModal: function cancelRefundModal() {\n      this.refundModal = false;\n    },\n    putOpenRefund: function putOpenRefund() {\n      var _this5 = this;\n\n      var data = {\n        id: this.orderId,\n        refund_price: this.refundMoney,\n        type: 1,\n        is_split_order: this.is_split_order\n      };\n\n      if (this.is_split_order) {\n        if (!this.refundSelection.length) {\n          return this.$Message.error('请选择需要退款的商品');\n        }\n\n        data.cart_ids = this.refundSelection.map(function (_ref3) {\n          var id = _ref3.id,\n              refundNum = _ref3.refundNum;\n          return {\n            cart_id: id,\n            cart_num: refundNum\n          };\n        });\n      }\n\n      _putOpenRefund(data).then(function (res) {\n        _this5.$Message.success(res.msg);\n\n        _this5.refundModal = false;\n\n        _this5.getData(_this5.orderDatalist.orderInfo.id);\n      }).catch(function (err) {\n        _this5.$Message.error(err.msg);\n      });\n    },\n    refundSelectionChange: function refundSelectionChange(selection) {\n      this.refundSelection = selection;\n    },\n    refundNumChange: function refundNumChange(_ref4) {\n      var id = _ref4.id,\n          refundNum = _ref4.refundNum;\n      var result = this.refundSelection.find(function (item) {\n        return item.id === id;\n      });\n\n      if (result) {\n        result.refundNum = refundNum;\n      }\n    },\n    checkboxItem: function checkboxItem(e) {\n      var id = parseInt(e.rowid);\n      var index = this.checkUidList.indexOf(id);\n\n      if (index !== -1) {\n        this.checkUidList = this.checkUidList.filter(function (item) {\n          return item !== id;\n        });\n      } else {\n        this.checkUidList.push(id);\n      }\n    },\n    checkboxAll: function checkboxAll() {\n      // 获取选中当前值\n      var obj2 = this.$refs.xTable.getCheckboxRecords(true); // 获取之前选中值\n\n      var obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\n      if (this.isAll == 0 && this.checkUidList.length <= obj.length && !this.isCheckBox) {\n        obj = [];\n      }\n\n      obj = obj.concat(obj2);\n      var ids = [];\n      obj.forEach(function (item) {\n        ids.push(parseInt(item.id));\n      });\n      this.checkUidList = ids;\n\n      if (!obj2.length) {\n        this.isCheckBox = false;\n      }\n    },\n    allPages: function allPages(e) {\n      this.isAll = e;\n\n      if (e == 0) {\n        this.$refs.xTable.toggleAllCheckboxRow(); // this.checkboxAll();\n      } else {\n        if (!this.isCheckBox) {\n          this.$refs.xTable.setAllCheckboxRow(true);\n          this.isCheckBox = true;\n          this.isAll = 1;\n        } else {\n          this.$refs.xTable.setAllCheckboxRow(false);\n          this.isCheckBox = false;\n          this.isAll = 0;\n        }\n\n        this.checkUidList = [];\n      }\n    },\n    //erp配置\n    getErpConfig: function getErpConfig() {\n      var _this6 = this;\n\n      erpConfig().then(function (res) {\n        _this6.openErp = res.data.open_erp;\n      }).catch(function (err) {\n        _this6.$Message.error(err.msg);\n      });\n    },\n    printOreder: function printOreder() {\n      if (this.checkUidList.length > 10 || this.isAll == 1 && this.page.total > 10) {\n        return this.$Message.error('最多批量打印10个订单');\n      }\n\n      var ids = [];\n\n      if (this.isAll == 1 && this.page.total <= 10) {\n        this.orderList.forEach(function (item) {\n          ids.push(parseInt(item.id));\n        });\n      }\n\n      var pathInfo = this.$router.resolve({\n        path: this.roterPre + '/supplier/order/distribution',\n        query: {\n          id: this.isAll == 1 ? ids.join(',') : this.checkUidList.join(','),\n          status: 2\n        }\n      });\n      window.open(pathInfo.href, '_blank');\n    },\n    delAll: function delAll() {\n      var _this7 = this;\n\n      if (this.checkUidList.length === 0 && this.isAll == 0) {\n        return this.$Message.error('请先选择删除的订单！');\n      }\n\n      var idss = {\n        all: this.isAll,\n        ids: this.checkUidList\n      };\n      var delfromData = {\n        title: '删除订单',\n        url: \"/order/dels\",\n        method: 'post',\n        ids: idss\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this7.$Message.success(res.msg);\n\n        _this7.checkUidList = [];\n\n        _this7.getList();\n      }).catch(function (res) {\n        _this7.$Message.error(res.msg);\n      });\n    },\n    onAuto: function onAuto() {\n      this.$refs.sends.modals = true;\n      this.$refs.sends.getList();\n      this.$refs.sends.getDeliveryList();\n    },\n    // 提醒发货\n    btnClick: function btnClick(row) {\n      var _this8 = this;\n\n      var data = {\n        supplier_id: row.supplier_id,\n        id: row.id\n      };\n      remindOrder(data).then(\n      /*#__PURE__*/\n      function () {\n        var _ref5 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this8.$Message.success(res.msg);\n\n                case 1:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref5.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this8.$Message.error(res.msg);\n      });\n    },\n    queuemModal: function queuemModal() {\n      this.$refs.queue.modal = true;\n    },\n    // 下载物流公司对照表\n    getExpressList: function () {\n      var _getExpressList = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee2() {\n        var th, filekey, data, fileName, lebData;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                th = [], filekey = [], data = [], fileName = '';\n                _context2.next = 3;\n                return this.getExcelData();\n\n              case 3:\n                lebData = _context2.sent;\n                if (!fileName) fileName = lebData.filename;\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) th = lebData.header;\n                data = lebData.export;\n                exportExcel(th, filekey, fileName, data);\n\n              case 9:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function getExpressList() {\n        return _getExpressList.apply(this, arguments);\n      }\n\n      return getExpressList;\n    }(),\n    getExcelData: function getExcelData() {\n      return new Promise(function (resolve, reject) {\n        exportExpressList().then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 订单核销\n    writeOff: function writeOff() {\n      this.modals2 = true;\n    },\n    // 验证\n    search: function search(name) {\n      var _this9 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this9.writeOffFrom.confirm = 0;\n          putWrite(_this9.writeOffFrom).then(\n          /*#__PURE__*/\n          function () {\n            var _ref6 = _asyncToGenerator(\n            /*#__PURE__*/\n            _regeneratorRuntime.mark(function _callee3(res) {\n              return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n                while (1) {\n                  switch (_context3.prev = _context3.next) {\n                    case 0:\n                      if (res.status === 200) {\n                        // this.orderInfo = res.data;\n                        _this9.$Message.success(res.msg);\n                      } else {\n                        _this9.$Message.error(res.msg);\n                      }\n\n                    case 1:\n                    case \"end\":\n                      return _context3.stop();\n                  }\n                }\n              }, _callee3);\n            }));\n\n            return function (_x2) {\n              return _ref6.apply(this, arguments);\n            };\n          }()).catch(function (res) {\n            _this9.$Message.error(res.msg);\n          });\n        } else {\n          _this9.$Message.error('请填写正确的核销码');\n        }\n      });\n    },\n    // 订单核销\n    ok: function ok() {\n      var _this10 = this;\n\n      if (!this.writeOffFrom.code) {\n        this.$Message.warning('请先验证订单！');\n      } else {\n        this.writeOffFrom.confirm = 1;\n        putWrite(this.writeOffFrom).then(\n        /*#__PURE__*/\n        function () {\n          var _ref7 = _asyncToGenerator(\n          /*#__PURE__*/\n          _regeneratorRuntime.mark(function _callee4(res) {\n            return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n              while (1) {\n                switch (_context4.prev = _context4.next) {\n                  case 0:\n                    if (res.status === 200) {\n                      _this10.$Message.success(res.msg);\n\n                      _this10.modals2 = false;\n\n                      _this10.$refs[name].resetFields();\n\n                      _this10.$emit('getList');\n                    } else {\n                      _this10.$Message.error(res.msg);\n                    }\n\n                  case 1:\n                  case \"end\":\n                    return _context4.stop();\n                }\n              }\n            }, _callee4);\n          }));\n\n          return function (_x3) {\n            return _ref7.apply(this, arguments);\n          };\n        }()).catch(function (res) {\n          _this10.$Message.error(res.msg);\n        });\n      }\n    },\n    del: function del(name) {\n      // this.orderInfo = ''\n      this.modals2 = false;\n      this.writeOffFrom.confirm = 0;\n      this.$refs[name].resetFields();\n    },\n    // 上传头部token\n    getToken: function getToken() {\n      this.uploadHeaders['Authori-zation'] = 'Bearer ' + util.cookies.get('token');\n    },\n    // 上传成功\n    uploadSuccess: function uploadSuccess(res, file, fileList) {\n      if (res.status === 200) {\n        this.$Message.success(res.msg);\n        this.file = res.data.src;\n        this.fileList = fileList;\n      } else {\n        this.$Message.error(res.msg);\n      }\n    },\n    //移除文件\n    removeFile: function removeFile(file, fileList) {\n      this.file = '';\n      this.fileList = fileList;\n    },\n    // 手动批量发货-确定\n    manualModalOk: function manualModalOk() {\n      var _this11 = this;\n\n      this.$refs.upload.clearFiles();\n      handBatchDelivery({\n        file: this.file\n      }).then(function (res) {\n        _this11.$Message.success(res.msg);\n\n        _this11.fileList = [];\n      }).catch(function (err) {\n        _this11.$Message.error(err.msg);\n\n        _this11.fileList = [];\n      });\n    },\n    // 手动批量发货-取消\n    manualModalCancel: function manualModalCancel() {\n      this.fileList = [];\n      this.$refs.upload.clearFiles();\n    },\n    getTabs: function getTabs() {\n      var _this12 = this;\n\n      this.spinShow = true;\n      this.$store.dispatch('admin/order/getOrderTabs', {\n        status: this.orderStatus,\n        pay_type: this.orderPayType,\n        data: this.orderTime,\n        real_name: this.orderNum,\n        field_key: this.fieldKey,\n        type: this.type_id,\n        plat_type: this.currentTab,\n        store_id: this.store_id,\n        supplier_id: this.supplier_id\n      }).then(function (res) {\n        _this12.tablists = res.data; // this.onChangeChart(this.tablists)\n\n        _this12.spinShow = false;\n      }).catch(function (res) {\n        _this12.spinShow = false;\n\n        _this12.$Message.error(res.msg);\n      });\n    },\n    onClickTab: function onClickTab() {\n      this.onChangeTabs(this.currentTab);\n      this.isAll = 0;\n      this.isCheckBox = false;\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = [];\n\n      if (this.currentTab == 1) {\n        this.getSupplier_id('');\n      }\n\n      if (this.currentTab == 2) {\n        this.getStore_id('');\n      }\n\n      this.getList();\n      this.$store.dispatch('admin/order/getOrderTabs', {\n        type: this.currentTab\n      });\n    },\n    closeDetail: function closeDetail() {\n      this.$refs.detailss.modals = false;\n    },\n    distribution: function distribution(row) {\n      this.$refs.distshow.modals = true;\n      this.$refs.distshow.formValidate.keywords = '';\n      this.$refs.distshow.getList(row.id);\n    },\n    showUserInfo: function showUserInfo(row) {\n      this.$refs.userDetails.modals = true;\n      this.$refs.userDetails.activeName = 'info';\n      this.$refs.userDetails.getDetails(row.uid);\n    },\n    //修改增加打印方法\n    printImg: function printImg(url) {\n      printJS({\n        printable: url,\n        type: 'image',\n        documentTitle: '快递信息',\n        style: \"img{\\n\\t      width: 100%;\\n\\t      height: 476px;\\n\\t    }\"\n      });\n    },\n    // 操作\n    changeMenu: function changeMenu(row, name, num) {\n      var _this13 = this;\n\n      this.orderId = row.id;\n      this.orderConId = row.pid > 0 ? row.pid : row.id;\n      this.orderConNum = num;\n\n      switch (name) {\n        case '1':\n          this.delfromData = {\n            title: '修改立即支付',\n            url: \"/order/pay_offline/\".concat(row.id),\n            method: 'post',\n            ids: ''\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this13.$Message.success(res.msg);\n\n            _this13.$emit('changeGetTabs');\n\n            _this13.getData(row.id, 1);\n\n            _this13.getList();\n          }).catch(function (res) {\n            _this13.$Message.error(res.msg);\n          }); // this.modalTitleSs = '修改立即支付';\n\n          break;\n\n        case '2':\n          this.rowActive = row;\n          this.getData(row.id);\n          break;\n\n        case '3':\n          this.$refs.record.modals = true;\n          this.$refs.record.getList(row.id);\n          break;\n\n        case '4':\n          this.$refs.remarks.formValidate.remark = row.remark;\n          this.$refs.remarks.modals = true;\n          break;\n\n        case '5':\n          this.getOnlyrefundData(row.id, row.refund_type);\n          break;\n\n        case '55':\n          this.getrefundData(row.id, row.refund_type);\n          break;\n\n        case '6':\n          this.getRefundIntegral(row.id);\n          break;\n\n        case '7':\n          this.getNorefundData(row.id);\n          break;\n\n        case '8':\n          this.delfromData = {\n            title: '修改确认收货',\n            url: \"/order/take/\".concat(row.id),\n            method: 'put',\n            ids: ''\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this13.$Message.success(res.msg);\n\n            _this13.$emit('changeGetTabs');\n\n            _this13.getList();\n\n            if (num) {\n              _this13.$refs.detailss.getSplitOrder(row.pid);\n            } else {\n              _this13.getData(row.id, 1);\n            }\n          }).catch(function (res) {\n            _this13.$Message.error(res.msg);\n          }); // this.modalTitleSs = '修改确认收货';\n\n          break;\n\n        case '10':\n          this.delfromData = {\n            title: '立即打印订单',\n            info: '您确认打印此订单吗?',\n            url: \"/order/print/\".concat(row.id),\n            method: 'get',\n            ids: ''\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this13.$Message.success(res.msg);\n\n            _this13.$emit('changeGetTabs');\n\n            _this13.getList();\n          }).catch(function (res) {\n            _this13.$Message.error(res.msg);\n          });\n          break;\n\n        case '11':\n          this.delfromData = {\n            title: '立即打印电子面单',\n            info: '您确认打印此电子面单吗?',\n            url: \"/order/order_dump/\".concat(row.id),\n            method: 'get',\n            ids: ''\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this13.$Message.success(res.msg);\n\n            _this13.getList();\n          }).catch(function (res) {\n            _this13.$Message.error(res.msg);\n          });\n          break;\n\n        case '12':\n          var pathInfo = this.$router.resolve({\n            path: this.roterPre + '/supplier/order/distribution',\n            query: {\n              id: row.id,\n              status: 2\n            }\n          });\n          window.open(pathInfo.href, '_blank');\n          break;\n\n        case '13':\n          this.printImg(row.kuaidi_label);\n          break;\n\n        default:\n          this.delfromData = {\n            title: '删除订单',\n            url: \"/order/del/\".concat(row.id),\n            method: 'DELETE',\n            ids: ''\n          }; // this.modalTitleSs = '删除订单';\n\n          this.delOrder(row, this.delfromData);\n      }\n    },\n    // 立即支付 /确认收货//删除单条订单\n    submitModel: function submitModel() {\n      this.getList();\n    },\n    pageChange: function pageChange(index) {\n      this.page.pageNum = index;\n      this.getList();\n    },\n    limitChange: function limitChange(limit) {\n      this.page.pageSize = limit;\n      this.getList();\n    },\n    // 订单列表\n    getList: function getList(res) {\n      var _this14 = this;\n\n      if (res == 1) {\n        this.isAll = 0;\n        this.$refs.xTable.setAllCheckboxRow(false);\n        this.checkUidList = [];\n      }\n\n      this.page.pageNum = res === 1 ? 1 : this.page.pageNum;\n      this.loading = true;\n      orderList({\n        page: this.page.pageNum,\n        limit: this.page.pageSize,\n        status: this.orderStatus,\n        pay_type: this.orderPayType,\n        data: this.orderTime,\n        real_name: this.orderNum,\n        field_key: this.fieldKey,\n        type: this.type_id,\n        plat_type: this.currentTab,\n        store_id: this.store_id,\n        supplier_id: this.supplier_id,\n        user_reserve_type: 1\n      }).then(\n      /*#__PURE__*/\n      function () {\n        var _ref8 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee5(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n            while (1) {\n              switch (_context5.prev = _context5.next) {\n                case 0:\n                  data = res.data;\n                  data.data.forEach(function (item) {\n                    item.checkBox = _this14.isAll == 1;\n\n                    if (item.id == _this14.orderId) {\n                      _this14.rowActive = item;\n                    }\n                  }); // this.orderList = data.data;\n\n                  _this14.$set(_this14, 'orderList', data.data);\n\n                  _this14.orderCards = data.stat;\n                  _this14.page.total = data.count;\n\n                  _this14.$emit('on-changeCards', data.stat);\n\n                  _this14.loading = false;\n\n                  _this14.getTabs();\n\n                  _this14.$nextTick(function () {\n                    if (this.isAll == 1) {\n                      if (this.isCheckBox) {\n                        this.$refs.xTable.setAllCheckboxRow(true);\n                      } else {\n                        this.$refs.xTable.setAllCheckboxRow(false);\n                      }\n                    } else {\n                      var obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\n                      if (!this.checkUidList.length || this.checkUidList.length <= obj.length) {\n                        this.$refs.xTable.setAllCheckboxRow(false);\n                      }\n                    }\n                  });\n\n                case 9:\n                case \"end\":\n                  return _context5.stop();\n              }\n            }\n          }, _callee5);\n        }));\n\n        return function (_x4) {\n          return _ref8.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this14.loading = false;\n\n        _this14.$Message.error(res.msg);\n      });\n    },\n    // 编辑\n    edit: function edit(row) {\n      this.getOrderData(row.id);\n    },\n    splitOrderDetail: function splitOrderDetail(row) {\n      this.$router.push({\n        path: this.roterPre + 'split_list',\n        query: {\n          id: row.id,\n          orderChartType: this.orderStatus\n        }\n      });\n    },\n    // 删除单条订单\n    delOrder: function delOrder(row, data) {\n      var _this15 = this;\n\n      if (row.is_del === 1) {\n        this.$modalSure(data).then(function (res) {\n          _this15.$Message.success(res.msg);\n\n          _this15.getList();\n\n          _this15.$refs.detailss.modals = false;\n\n          _this15.$emit('changeGetTabs');\n        }).catch(function (res) {\n          _this15.$Message.error(res.msg);\n        });\n      } else {\n        var title = '错误！';\n        var content = '<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>';\n        this.$Modal.error({\n          title: title,\n          content: content\n        });\n      }\n    },\n    // 获取编辑表单数据\n    getOrderData: function getOrderData(id) {\n      var _this16 = this;\n\n      getOrdeDatas(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref9 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee6(res) {\n          return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n            while (1) {\n              switch (_context6.prev = _context6.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context6.next = 2;\n                    break;\n                  }\n\n                  return _context6.abrupt(\"return\", _this16.$authLapse(res.data));\n\n                case 2:\n                  _this16.$authLapse(res.data);\n\n                  _this16.FromData = res.data;\n                  _this16.$refs.edits.modals = true;\n\n                case 5:\n                case \"end\":\n                  return _context6.stop();\n              }\n            }\n          }, _callee6);\n        }));\n\n        return function (_x5) {\n          return _ref9.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this16.$Message.error(res.msg);\n      });\n    },\n    // 获取详情表单数据\n    getData: function getData(id, type) {\n      var _this17 = this;\n\n      // this.$refs.detailss.modals = true;\n      getDataInfo(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref10 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee7(res) {\n          return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n            while (1) {\n              switch (_context7.prev = _context7.next) {\n                case 0:\n                  if (!type) {\n                    _this17.$refs.detailss.modals = true;\n                  } // this.$refs.detailss.activeName = 'detail'\n\n\n                  _this17.orderDatalist = res.data;\n\n                  if (_this17.orderDatalist.orderInfo.refund_reason_wap_img) {\n                    try {\n                      _this17.orderDatalist.orderInfo.refund_reason_wap_img = JSON.parse(_this17.orderDatalist.orderInfo.refund_reason_wap_img);\n                    } catch (e) {\n                      _this17.orderDatalist.orderInfo.refund_reason_wap_img = [];\n                    }\n                  }\n\n                case 3:\n                case \"end\":\n                  return _context7.stop();\n              }\n            }\n          }, _callee7);\n        }));\n\n        return function (_x6) {\n          return _ref10.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this17.$Message.error(res.msg);\n      });\n    },\n    // 修改成功\n    submitFail: function submitFail(type) {\n      this.status = 0;\n      this.getList();\n\n      if (this.orderConNum != 1) {\n        this.getData(this.orderId, 1);\n      } else {\n        this.$refs.detailss.getSplitOrder(this.orderConId);\n      }\n\n      if (type) {\n        this.$emit('changeGetTabs');\n      }\n    },\n    // 仅退款\n    getOnlyrefundData: function getOnlyrefundData(id, refund_type) {\n      var _this18 = this;\n\n      // this.$modalForm(getRefundFrom(id)).then(() => {\n      //   this.getList()\n      //   this.$emit('changeGetTabs')\n      //   this.$refs.detailss.modals = false\n      // })\n      var _info = this.rowActive._info;\n      var cart_info = Object.keys(_info).map(function (key) {\n        return _info[key].cart_info;\n      });\n      cart_info.forEach(function (value) {\n        value.refundPrice = _this18.$computes.Div(value.refund_price, value.cart_num);\n        value.refundNum = value.cart_num - value.refund_num;\n        value._disabled = !value.refundNum;\n      });\n      this.refundProduct = cart_info;\n\n      if (this.refundProductNum === 1) {\n        this.refundSelection = cart_info;\n      }\n\n      this.refundModal = true;\n    },\n    // 退货退款\n    getrefundData: function getrefundData(id, refund_type) {\n      var _this19 = this;\n\n      this.delfromData = {\n        title: '是否立即退货退款',\n        url: \"/refund/agree/\".concat(id),\n        method: 'get'\n      };\n      this.$modalSure(this.delfromData).then(function (res) {\n        _this19.$Message.success(res.msg);\n\n        _this19.getList();\n\n        _this19.$emit('changeGetTabs');\n      }).catch(function (res) {\n        _this19.$Message.error(res.msg);\n      });\n    },\n    // 获取退积分表单数据\n    getRefundIntegral: function getRefundIntegral(id) {\n      var _this20 = this;\n\n      refundIntegral(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref11 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee8(res) {\n          return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n            while (1) {\n              switch (_context8.prev = _context8.next) {\n                case 0:\n                  _this20.FromData = res.data;\n                  _this20.$refs.edits.modals = true;\n\n                case 2:\n                case \"end\":\n                  return _context8.stop();\n              }\n            }\n          }, _callee8);\n        }));\n\n        return function (_x7) {\n          return _ref11.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this20.$Message.error(res.msg);\n      });\n    },\n    // 不退款表单数据\n    getNorefundData: function getNorefundData(id) {\n      var _this21 = this;\n\n      this.$modalForm(getnoRefund(id)).then(function () {\n        _this21.getList();\n\n        _this21.$emit('changeGetTabs');\n      });\n    },\n    // 发送货\n    sendOrder: function sendOrder(row, num) {\n      var _this22 = this;\n\n      this.orderConId = row.pid;\n      this.orderConNum = num;\n      this.$store.commit('admin/order/setSplitOrder', row.total_num);\n      this.$refs.send.modals = true;\n      this.orderId = row.id;\n      this.status = row._status;\n      this.pay_type = row.pay_type;\n      this.$refs.send.getList();\n      this.$refs.send.getDeliveryList();\n      this.$nextTick(function (e) {\n        _this22.$refs.send.getCartInfo(row._status, row.id);\n      });\n    },\n    // 配送信息表单数据\n    delivery: function delivery(row, num) {\n      var _this23 = this;\n\n      getDistribution(row.id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref12 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee9(res) {\n          return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n            while (1) {\n              switch (_context9.prev = _context9.next) {\n                case 0:\n                  _this23.orderConNum = num;\n                  _this23.orderConId = row.pid;\n                  _this23.FromData = res.data;\n                  _this23.$refs.edits.modals = true;\n\n                  if (num != 1) {\n                    _this23.getData(_this23.orderId, 1);\n                  }\n\n                case 5:\n                case \"end\":\n                  return _context9.stop();\n              }\n            }\n          }, _callee9);\n        }));\n\n        return function (_x8) {\n          return _ref12.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this23.$Message.error(res.msg);\n      });\n    },\n    // 订单导出\n    change: function change(status) {},\n    exports: function () {\n      var _exports = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee10(value) {\n        var th, filekey, data, fileName, excelData, i, lebData, sheetData, j, goodsList, k, row, key;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                this.exportListOn = this.exportList.findIndex(function (item) {\n                  return item.name === value;\n                });\n                th = [], filekey = [], data = [], fileName = '';\n                excelData = _objectSpread({}, this.where, {\n                  page: 1,\n                  export_type: value,\n                  ids: this.checkUidList.join()\n                });\n                i = 0;\n\n              case 4:\n                if (!(i < excelData.page)) {\n                  _context10.next = 18;\n                  break;\n                }\n\n                _context10.next = 7;\n                return this.downOrderData(excelData);\n\n              case 7:\n                lebData = _context10.sent;\n\n                if (lebData.export.length) {\n                  _context10.next = 10;\n                  break;\n                }\n\n                return _context10.abrupt(\"break\", 18);\n\n              case 10:\n                if (!fileName) {\n                  fileName = lebData.filename;\n                }\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) {\n                  th = lebData.header;\n                }\n\n                data = data.concat(lebData.export);\n                excelData.page++;\n\n              case 15:\n                i++;\n                _context10.next = 4;\n                break;\n\n              case 18:\n                sheetData = [];\n\n                for (j = 0; j < data.length; j++) {\n                  goodsList = data[j].goods_name.split('\\n');\n\n                  for (k = 0; k < goodsList.length; k++) {\n                    row = _objectSpread({}, data[j]);\n                    row.goods_name = goodsList[k];\n\n                    if (k) {\n                      for (key in row) {\n                        if (Object.hasOwnProperty.call(row, key)) {\n                          if (key !== 'goods_name') {\n                            row[key] = null;\n                          }\n                        }\n                      }\n                    }\n\n                    sheetData.push(row);\n                  }\n                }\n\n                exportExcel(th, filekey, fileName, sheetData);\n\n              case 21:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this);\n      }));\n\n      function exports(_x9) {\n        return _exports.apply(this, arguments);\n      }\n\n      return exports;\n    }(),\n    downOrderData: function downOrderData(excelData) {\n      return new Promise(function (resolve, reject) {\n        storeOrderApi(excelData).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 核销订单\n    bindWrite: function bindWrite(row) {\n      var self = this;\n      this.$Modal.confirm({\n        title: '提示',\n        content: '确定要核销该订单吗？',\n        cancelText: '取消',\n        closable: true,\n        maskClosable: true,\n        onOk: function onOk() {\n          writeUpdate(row.order_id).then(function (res) {\n            self.$Message.success(res.msg);\n            self.getList();\n          });\n        },\n        onCancel: function onCancel() {}\n      });\n    },\n    selectChange2: function selectChange2() {\n      this.$emit('selectChange2', this.orderDataStatus);\n    }\n  })\n};", null]}