{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\splitList.vue?vue&type=template&id=3ce73bd8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\splitList.vue", "mtime": 1733822761343}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <div>\n    <div class=\"i-layout-page-header\">\n      <PageHeader class=\"product_tabs\" title=\"子订单列表\" hidden-breadcrumb>\n      </PageHeader>\n    </div>\n    <Table\n      :columns=\"columns\"\n      :data=\"orderList\"\n      ref=\"table\"\n      :loading=\"loading\"\n      highlight-row\n      no-data-text=\"暂无数据\"\n      no-filtered-data-text=\"暂无筛选结果\"\n      @on-selection-change=\"onSelectTab\"\n      @on-select-all=\"selectAll\"\n      @on-select-all-cancel=\"selectAll\"\n      @on-select-cancel=\"onSelectCancel\"\n      class=\"orderData mt25\"\n    >\n      <template slot-scope=\"{ row, index }\" slot=\"order_id\">\n        <span v-text=\"row.order_id\" style=\"display: block\"></span>\n        <span v-show=\"row.is_del === 1\" class=\"span-del\"\n          >用户已删除</span\n        >\n      </template>\n      <template slot-scope=\"{ row, index }\" slot=\"nickname\">\n        <a @click=\"showUserInfo(row)\">{{ row.nickname }}</a>\n      </template>\n      <template slot-scope=\"{ row, index }\" slot=\"info\">\n        <div class=\"tabBox\" v-for=\"(val, i) in row._info\" :key=\"i\">\n          <div class=\"tabBox_img\" v-viewer>\n            <img\n              v-lazy=\"\n                val.cart_info.productInfo.attrInfo\n                  ? val.cart_info.productInfo.attrInfo.image\n                  : val.cart_info.productInfo.image\n              \"\n            />\n          </div>\n          <span class=\"tabBox_tit\">\n            {{ val.cart_info.productInfo.store_name + \" | \"\n            }}{{\n              val.cart_info.productInfo.attrInfo\n                ? val.cart_info.productInfo.attrInfo.suk\n                : \"\"\n            }}\n          </span>\n          <span class=\"tabBox_pice\">{{\n            \"￥\" + val.cart_info.truePrice + \" x \" + val.cart_info.cart_num\n          }}</span>\n        </div>\n      </template>\n\t  <template slot-scope=\"{ row, index }\" slot=\"statusName\">\n\t    <div v-html=\"row.status_name.status_name\" class=\"pt5\"></div>\n\t    <div class=\"pictrue-box\">\n\t      <div\n\t        v-viewer\n\t        v-if=\"row.status_name.pics\"\n\t        v-for=\"(item, index) in row.status_name.pics || []\"\n\t        :key=\"index\"\n\t      >\n\t        <img class=\"pictrue mr10\" v-lazy=\"item\" :src=\"item\" />\n\t      </div>\n\t    </div>\n\t  </template>\n      <template slot-scope=\"{ row, index }\" slot=\"action\">\n        <a @click=\"edit(row)\" v-if=\"row._status === 1\">编辑</a>\n        <a\n          @click=\"sendOrder(row)\"\n          v-if=\"\n            (row._status === 2 || row._status === 8) &&\n            row.shipping_type === 1 &&\n            (row.pinkStatus === null || row.pinkStatus === 2)\n          \"\n          >发送货</a\n        >\n        <a @click=\"delivery(row)\" v-if=\"row._status === 4\">配送信息</a>\n        <a\n          @click=\"bindWrite(row)\"\n          v-if=\"\n            row.shipping_type == 2 &&\n            row.status == 0 &&\n            row.paid == 1 &&\n            row.refund_status === 0\n          \"\n          >立即核销</a\n        >\n        <Divider\n          type=\"vertical\"\n          v-if=\"row._status === 2 || row._status === 8\"\n        />\n        <a @click=\"splitOrderDetail(row)\" v-if=\"row._status === 8\"\n          >查看子订单</a\n        >\n\n        <Divider\n          type=\"vertical\"\n          v-if=\"\n            row._status === 1 ||\n            ((row._status === 2 || row._status === 8) &&\n              (row.pinkStatus === null || row.pinkStatus === 2)) ||\n            row._status === 4 ||\n            (row.shipping_type == 2 &&\n              row.status == 0 &&\n              row.paid == 1 &&\n              row.refund_status === 0)\n          \"\n        />\n        <template>\n          <Dropdown @on-click=\"changeMenu(row, $event)\">\n            <a href=\"javascript:void(0)\">\n              更多\n              <Icon type=\"ios-arrow-down\"></Icon>\n            </a>\n            <DropdownMenu slot=\"list\">\n              <DropdownItem\n                name=\"1\"\n                ref=\"ones\"\n                v-show=\"\n                  row._status === 1 &&\n                  row.paid === 0 &&\n                  row.pay_type === 'offline'\n                \"\n                >立即支付</DropdownItem\n              >\n              <DropdownItem name=\"2\">订单详情</DropdownItem>\n              <DropdownItem name=\"3\">订单记录</DropdownItem>\n              <DropdownItem\n                name=\"11\"\n                v-show=\"row._status >= 3 && row.express_dump\"\n                >电子面单打印</DropdownItem\n              >\n              <DropdownItem name=\"10\" v-show=\"row._status >= 2\"\n                >小票打印</DropdownItem\n              >\n              <!-- <DropdownItem name=\"10\" v-show=\"row._status >= 2\">订单打印</DropdownItem> -->\n              <DropdownItem\n                name=\"4\"\n                v-show=\"\n                  row._status !== 1 ||\n                  (row._status === 3 &&\n                    row.use_integral > 0 &&\n                    row.use_integral >= row.back_integral)\n                \"\n                >订单备注</DropdownItem\n              >\n\t\t\t\t\t\t\t<DropdownItem\n\t\t\t\t\t\t\t  name=\"5\"\n\t\t\t\t\t\t\t  v-show=\"row.refund_type != 2 && row.refund_type != 4 && row.refund_type != 6 && row.paid==1 && row.refund_status !==2 && parseFloat(row.pay_price) > 0\"\n\t\t\t\t\t\t\t  >立即退款</DropdownItem\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<DropdownItem\n\t\t\t\t\t\t\t  name=\"55\"\n\t\t\t\t\t\t\t  v-show=\"row.refund_type == 2\"\n\t\t\t\t\t\t\t  >同意退货</DropdownItem\n\t\t\t\t\t\t\t>\n              <!-- <DropdownItem\n                name=\"5\"\n                v-show=\"\n                  row._status === 3 &&\n                  (parseFloat(row.pay_price) > parseFloat(row.refund_price) ||\n                    (row.pay_price == 0 &&\n                      [0, 1].indexOf(row.refund_status) !== -1))\n                \"\n                >立即退款</DropdownItem\n              >\n              <DropdownItem\n                name=\"6\"\n                v-show=\"\n                  row._status !== 1 &&\n                  row.use_integral > 0 &&\n                  row.use_integral >= row.back_integral\n                \"\n                >退积分</DropdownItem\n              >\n              <DropdownItem name=\"7\" v-show=\"row._status === 3\"\n                >不退款</DropdownItem\n              > -->\n              <DropdownItem name=\"8\" v-show=\"row._status === 4\"\n                >已收货</DropdownItem\n              >\n\t\t\t  <DropdownItem name=\"9\" v-if=\"row.is_del == 1\"\n\t\t\t    >删除订单</DropdownItem>\n            </DropdownMenu>\n          </Dropdown>\n        </template>\n      </template>\n    </Table>\n    <div class=\"acea-row row-right page\">\n      <!-- <Page\n        :total=\"page.total\"\n        :current=\"page.pageNum\"\n        show-elevator\n        show-total\n        @on-change=\"pageChange\"\n        :page-size=\"page.pageSize\"\n        @on-page-size-change=\"limitChange\"\n        show-sizer\n      /> -->\n    </div>\n    <!-- 编辑 退款 退积分 不退款-->\n    <edit-from\n      ref=\"edits\"\n      :FromData=\"FromData\"\n      @submitFail=\"submitFail\"\n    ></edit-from>\n    <!-- 会员详情-->\n    <user-details ref=\"userDetails\"></user-details>\n    <!-- 详情 -->\n    <details-from\n      ref=\"detailss\"\n      :orderDatalist=\"orderDatalist\"\n      :orderId=\"orderId\"\n    ></details-from>\n    <!-- 备注 -->\n    <order-remark\n      ref=\"remarks\"\n      :orderId=\"orderId\"\n      @submitFail=\"submitFail\"\n    ></order-remark>\n    <!-- 记录 -->\n    <order-record ref=\"record\"></order-record>\n    <!-- 发送货 -->\n    <order-send\n      ref=\"send\"\n      :orderId=\"orderId\"\n      :status=\"status\"\n      @submitFail=\"submitFail\"\n    ></order-send>\n  </div>\n", null]}