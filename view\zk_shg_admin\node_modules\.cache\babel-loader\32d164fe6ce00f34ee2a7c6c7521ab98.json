{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue", "mtime": 1709543824155}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport { supplierFinanceInfo, exportTableList } from '@/api/supplier';\nimport cardsData from \"@/components/cards/cards\";\nimport timeOptions from '@/utils/timeOptions';\nimport { getBalance, getBalanceChange } from \"@/api/vop\";\nexport default {\n  name: 'orderStatistics',\n  components: {\n    cardsData: cardsData\n  },\n  data: function data() {\n    return {\n      cardLists: [],\n      extractStatistics: {},\n      // 旧\n      modalmark: false,\n      supplierList: [],\n      total: 0,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns: [{\n        title: '备注',\n        key: 'notePub',\n        minWidth: 300\n      }, {\n        title: '交易时间',\n        key: 'createdDate',\n        minWidth: 100\n      }, {\n        title: '交易金额',\n        slot: 'number',\n        minWidth: 40\n      }, {\n        title: '交易账号',\n        key: 'pin',\n        minWidth: 40\n      }, {\n        title: '交易号',\n        key: 'tradeNo',\n        minWidth: 40\n      }, {\n        title: '业务类型',\n        slot: 'type',\n        minWidth: 40\n      }, {\n        title: '业务类型名称',\n        key: 'tradeTypeName',\n        minWidth: 80\n      }],\n      orderList: [],\n      formValidate: {\n        keyword: '',\n        data: '',\n        page: 1,\n        limit: 20\n      },\n      timeVal: [],\n      options: timeOptions\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'left';\n    }\n  }),\n  mounted: function mounted() {\n    var _this = this;\n\n    this.getList();\n    getBalance().then(\n    /*#__PURE__*/\n    function () {\n      var _ref = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee(res) {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _this.cardLists = res.data;\n\n              case 1:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee);\n      }));\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }()).catch(function (res) {\n      _this.loading = false;\n\n      _this.$Message.error(res.msg);\n    });\n  },\n  methods: {\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      getBalanceChange(this.formValidate).then(function (res) {\n        var data = res.data;\n        _this2.orderList = data.items;\n        _this2.total = data.pageItemTotal;\n        _this2.loading = false;\n      }).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      });\n      ;\n    },\n    search: function search() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    reset: function reset() {\n      this.formValidate = {\n        keyword: '',\n        data: '',\n        page: 1,\n        limit: 20\n      };\n      this.timeVal = [];\n      this.getList();\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.formValidate.page = 1;\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join('-') : '';\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    //分页\n    pageChange: function pageChange(status) {\n      this.formValidate.page = status;\n      this.getList();\n    },\n    getExcelData: function getExcelData(data) {\n      return new Promise(function (resolve) {\n        exportTableList(data).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    }\n  }\n};", null]}