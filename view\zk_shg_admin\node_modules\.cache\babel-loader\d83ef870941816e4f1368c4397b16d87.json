{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue", "mtime": 1675991652000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport expandRow from \"./tableExpand.vue\";\nimport { orderList, getOrdeDatas, getDataInfo, getRefundFrom, getnoRefund, refundIntegral, getDistribution, writeUpdate } from \"@/api/order\";\nimport { getIntegralOrderDataInfo, integralOrderList, getIntegralOrderDistribution } from \"@/api/marketing\";\nimport { erpConfig } from \"@/api/erp\";\nimport { mapState, mapMutations } from \"vuex\";\nimport editFrom from \"../../../../components/from/from\";\nimport detailsFrom from \"../handle/orderDetails\";\nimport orderRemark from \"../handle/orderRemark\";\nimport orderRecord from \"../handle/orderRecord\";\nimport orderSend from \"../handle/orderSend\";\nimport userDetails from \"@/pages/user/list/handle/userDetails\";\nexport default {\n  name: \"table_list\",\n  components: {\n    expandRow: expandRow,\n    editFrom: editFrom,\n    detailsFrom: detailsFrom,\n    orderRemark: orderRemark,\n    orderRecord: orderRecord,\n    orderSend: orderSend,\n    userDetails: userDetails\n  },\n  props: [\"where\", \"isAll\"],\n  data: function data() {\n    return {\n      openErp: false,\n      delfromData: {},\n      modal: false,\n      orderList: [],\n      orderCards: [],\n      loading: false,\n      orderId: 0,\n      columns: [{\n        type: \"expand\",\n        width: 30,\n        render: function render(h, params) {\n          return h(expandRow, {\n            props: {\n              row: params.row\n            }\n          });\n        }\n      }, {\n        title: \"订单号\",\n        align: \"center\",\n        slot: \"order_id\",\n        minWidth: 160\n      }, {\n        title: \"用户信息\",\n        slot: \"nickname\",\n        minWidth: 110\n      }, {\n        title: \"商品信息\",\n        slot: \"info\",\n        minWidth: 330\n      }, {\n        title: \"兑换积分\",\n        key: \"total_integral\",\n        minWidth: 70\n      }, {\n        title: \"兑换金额\",\n        key: \"total_price\",\n        minWidth: 70\n      }, {\n        title: \"订单状态\",\n        slot: \"status_name\",\n        minWidth: 100\n      }, {\n        title: \"下单时间\",\n        key: \"add_time\",\n        minWidth: 130\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        fixed: \"right\",\n        minWidth: 150,\n        align: \"center\"\n      }],\n      page: {\n        total: 0,\n        // 总条数\n        pageNum: 1,\n        // 当前页\n        pageSize: 10 // 每页显示条数\n\n      },\n      data: [],\n      FromData: null,\n      orderDatalist: null,\n      modalTitleSs: \"\",\n      isDelIdList: [],\n      checkBox: false,\n      formSelection: [],\n      selectionCopy: [],\n      display: \"none\",\n      autoDisabled: false,\n      // isAll: -1,\n      rowActive: {}\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/integralOrder\", [\"orderPayType\", \"orderStatus\", \"orderTime\", \"orderNum\", \"fieldKey\", \"orderType\", \"realName\"])),\n  mounted: function mounted() {},\n  created: function created() {\n    this.getErpConfig();\n    this.getList();\n  },\n  watch: {\n    orderType: function orderType() {\n      this.page.pageNum = 1;\n      this.getList();\n    },\n    formSelection: function formSelection(value) {\n      this.$emit(\"order-select\", value);\n\n      if (value.length) {\n        this.$emit(\"auto-disabled\", 0);\n      } else {\n        this.$emit(\"auto-disabled\", 1);\n      }\n\n      var isDel = value.some(function (item) {\n        return item.is_del === 1;\n      });\n      this.getIsDel(isDel);\n      this.getisDelIdListl(value);\n    },\n    orderList: {\n      deep: true,\n      handler: function handler(value) {\n        var _this = this;\n\n        value.forEach(function (item) {\n          _this.formSelection.forEach(function (itm) {\n            if (itm.id === item.id) {\n              item.checkBox = true;\n            }\n          });\n        });\n        var arr = this.orderList.filter(function (item) {\n          return item.checkBox;\n        });\n\n        if (this.orderList.length) {\n          this.checkBox = this.orderList.length === arr.length;\n        } else {\n          this.checkBox = false;\n        }\n      }\n    }\n  },\n  methods: _objectSpread({}, mapMutations(\"admin/integralOrder\", [\"getIsDel\", \"getisDelIdListl\"]), {\n    getErpConfig: function getErpConfig() {\n      var _this2 = this;\n\n      erpConfig().then(function (res) {\n        _this2.openErp = res.data.open_erp;\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    selectAll: function selectAll(row) {\n      var _this3 = this;\n\n      if (row.length) {\n        this.formSelection = row;\n        this.selectionCopy = row;\n      }\n\n      this.selectionCopy.forEach(function (item, index) {\n        item.checkBox = _this3.checkBox;\n\n        _this3.$set(_this3.orderList, index, item);\n      });\n    },\n    showUserInfo: function showUserInfo(row) {\n      this.$refs.userDetails.modals = true;\n      this.$refs.userDetails.activeName = 'info';\n      this.$refs.userDetails.getDetails(row.uid);\n    },\n    // 操作\n    changeMenu: function changeMenu(row, name) {\n      var _this4 = this;\n\n      this.orderId = row.id;\n\n      switch (name) {\n        case \"2\":\n          this.rowActive = row;\n          this.getData(row.id);\n          break;\n\n        case \"3\":\n          this.$refs.record.modals = true;\n          this.$refs.record.getList(row.id);\n          break;\n\n        case \"4\":\n          this.$refs.remarks.formValidate.remark = row.remark;\n          this.$refs.remarks.modals = true;\n          break;\n\n        case \"5\":\n          this.getRefundData(row.id);\n          break;\n\n        case \"6\":\n          this.getRefundIntegral(row.id);\n          break;\n\n        case \"7\":\n          this.getNoRefundData(row.id);\n          break;\n\n        case \"8\":\n          this.delfromData = {\n            title: \"修改确认收货\",\n            url: \"marketing/integral/order/take/\".concat(row.id),\n            method: \"put\",\n            ids: \"\"\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this4.$Message.success(res.msg);\n\n            _this4.getList();\n\n            _this4.$emit(\"changeGetTabs\");\n\n            _this4.getData(row.id, 1);\n          }).catch(function (res) {\n            _this4.$Message.error(res.msg);\n          }); // this.modalTitleSs = '修改确认收货';\n\n          break;\n\n        case \"10\":\n          this.delfromData = {\n            title: \"立即打印订单\",\n            info: \"您确认打印此订单吗?\",\n            url: \"marketing/integral/order/print/\".concat(row.id),\n            method: \"get\",\n            ids: \"\"\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this4.$Message.success(res.msg);\n\n            _this4.$emit(\"changeGetTabs\");\n\n            _this4.getList();\n          }).catch(function (res) {\n            _this4.$Message.error(res.msg);\n          });\n          break;\n\n        case \"11\":\n          this.delfromData = {\n            title: \"立即打印电子面单\",\n            info: \"您确认打印此电子面单吗?\",\n            url: \"/order/order_dump/\".concat(row.id),\n            method: \"get\",\n            ids: \"\"\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this4.$Message.success(res.msg);\n\n            _this4.getList();\n          }).catch(function (res) {\n            _this4.$Message.error(res.msg);\n          });\n          break;\n\n        default:\n          this.delfromData = {\n            title: \"删除订单\",\n            url: \"marketing/integral/order/del/\".concat(row.id),\n            method: \"DELETE\",\n            ids: \"\"\n          }; // this.modalTitleSs = '删除订单';\n\n          this.delOrder(row, this.delfromData);\n      }\n    },\n    // 立即支付 /确认收货//删除单条订单\n    submitModel: function submitModel() {\n      this.getList();\n    },\n    pageChange: function pageChange(index) {\n      this.page.pageNum = index;\n      this.getList();\n    },\n    limitChange: function limitChange(limit) {\n      this.page.pageSize = limit;\n      this.getList();\n    },\n    // 订单列表\n    getList: function getList(res) {\n      var _this5 = this;\n\n      this.page.pageNum = res === 1 ? 1 : this.page.pageNum;\n      this.loading = true;\n      integralOrderList({\n        page: this.page.pageNum,\n        limit: this.page.pageSize,\n        status: this.orderStatus,\n        pay_type: this.orderPayType,\n        data: this.orderTime,\n        real_name: this.realName,\n        field_key: this.fieldKey,\n        type: this.orderType === 0 ? \"\" : this.orderType,\n        product_id: this.$route.query.product_id\n      }).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  data.data.forEach(function (item) {\n                    if (item.id == _this5.orderId) {\n                      _this5.rowActive = item;\n                    }\n                  }); // this.orderList = data.data;\n\n                  _this5.orderList = data.data.map(function (item) {\n                    // item.checkBox = false;\n                    if (_this5.isAll === 1) {\n                      item.checkBox = true;\n                    } else {\n                      item.checkBox = false;\n                    }\n\n                    return item;\n                  });\n                  _this5.orderCards = data.stat;\n                  _this5.page.total = data.count;\n\n                  _this5.$emit(\"on-changeCards\", data.stat);\n\n                  _this5.loading = false;\n\n                case 7:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this5.loading = false;\n\n        _this5.$Message.error(res.msg);\n      });\n    },\n    // 全选\n    onSelectTab: function onSelectTab(selection) {\n      this.formSelection = selection;\n      var isDel = selection.some(function (item) {\n        return item.is_del === 1;\n      });\n      this.getIsDel(isDel);\n      this.getisDelIdListl(selection);\n    },\n    // 编辑\n    edit: function edit(row) {\n      this.getOrderData(row.id);\n    },\n    // 删除单条订单\n    delOrder: function delOrder(row, data) {\n      var _this6 = this;\n\n      if (row.is_del === 1) {\n        this.$modalSure(data).then(function (res) {\n          _this6.$Message.success(res.msg);\n\n          _this6.getList();\n        }).catch(function (res) {\n          _this6.$Message.error(res.msg);\n        });\n      } else {\n        var title = \"错误！\";\n        var content = \"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>\";\n        this.$Modal.error({\n          title: title,\n          content: content\n        });\n      }\n    },\n    // 获取编辑表单数据\n    getOrderData: function getOrderData(id) {\n      var _this7 = this;\n\n      getOrdeDatas(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context2.next = 2;\n                    break;\n                  }\n\n                  return _context2.abrupt(\"return\", _this7.$authLapse(res.data));\n\n                case 2:\n                  _this7.$authLapse(res.data);\n\n                  _this7.FromData = res.data;\n                  _this7.$refs.edits.modals = true;\n\n                case 5:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this7.$Message.error(res.msg);\n      });\n    },\n    // 获取详情表单数据\n    getData: function getData(id, type) {\n      var _this8 = this;\n\n      getIntegralOrderDataInfo(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref3 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee3(res) {\n          return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n            while (1) {\n              switch (_context3.prev = _context3.next) {\n                case 0:\n                  if (!type) {\n                    _this8.$refs.detailss.modals = true;\n                  }\n\n                  _this8.$refs.detailss.activeName = 'detail';\n                  _this8.orderDatalist = res.data;\n\n                  if (_this8.orderDatalist.orderInfo.refund_reason_wap_img) {\n                    try {\n                      _this8.orderDatalist.orderInfo.refund_reason_wap_img = JSON.parse(_this8.orderDatalist.orderInfo.refund_reason_wap_img);\n                    } catch (e) {\n                      _this8.orderDatalist.orderInfo.refund_reason_wap_img = [];\n                    }\n                  }\n\n                case 4:\n                case \"end\":\n                  return _context3.stop();\n              }\n            }\n          }, _callee3);\n        }));\n\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this8.$Message.error(res.msg);\n      });\n    },\n    // 修改成功\n    submitFail: function submitFail(type) {\n      this.getList();\n      this.getData(this.orderId, 1);\n\n      if (type) {\n        this.$emit(\"changeGetTabs\");\n      }\n    },\n    // 发送货\n    sendOrder: function sendOrder(row) {\n      this.$refs.send.modals = true;\n      this.$refs.send.getList();\n      this.$refs.send.getDeliveryList(); // this.$refs.send.getSheetInfo();\n\n      this.orderId = row.id;\n    },\n    // 配送信息表单数据\n    delivery: function delivery(row) {\n      var _this9 = this;\n\n      getIntegralOrderDistribution(row.id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref4 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee4(res) {\n          return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n            while (1) {\n              switch (_context4.prev = _context4.next) {\n                case 0:\n                  _this9.FromData = res.data;\n                  _this9.$refs.edits.modals = true;\n\n                  _this9.getData(_this9.orderId, 1);\n\n                case 3:\n                case \"end\":\n                  return _context4.stop();\n              }\n            }\n          }, _callee4);\n        }));\n\n        return function (_x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this9.$Message.error(res.msg);\n      });\n    },\n    change: function change(status) {},\n    // 数据导出；\n    exportData: function exportData() {\n      this.$refs.table.exportCsv({\n        filename: \"商品列表\"\n      });\n    },\n    onSelectCancel: function onSelectCancel(selection, row) {}\n  })\n};", null]}