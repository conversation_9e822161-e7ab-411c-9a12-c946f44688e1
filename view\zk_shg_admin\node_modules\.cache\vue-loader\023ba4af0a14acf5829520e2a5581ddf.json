{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_card_1.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_card_1.vue", "mtime": 1734946199553}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { getProduct } from '@/api/diy'\n    export default {\n        name: 'c_home_card_1',\n        componentsName: 'home_card_1',\n        cname: '商品卡片一',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ], // 内容 样式\n\t\t\t\toneContent: [\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title, // 选项卡设置\n\t\t\t\t\t\tconfigNme: 'titleTab'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_promotion, // 标题商品选择\n\t\t\t\t\t    configNme: 'tabConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContent:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_button_img,\n\t\t\t\t\t    configNme: 'bntStyleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'bntConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'decorateColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle2: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'decorateColor2'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'textColor2'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle2: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'textColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle3: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'textColor3'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCart'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneCartConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourStyle2:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bntBgColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsetUp:0,\n\t\t\t\ttype:0,\n\t\t\t\ttype2:0,\n\t\t\t\ttype3:0,\n\t\t\t\ttype4:0\n            }\n        },\n        watch: {\n            num (nVal) {\n                // debugger;\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n\t\t\t\t\tthis.setUp = nVal;\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n                        this.getRComContent(arr,this.type3)\n                    } else {\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type2,this.type3,this.type4)\n                    }\n                },\n                deep: true\n            },\n\t\t\t'configObj.styleConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tthis.getRComStyle(arr,nVal,this.type2,this.type3,this.type4)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t'configObj.cartConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type3 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tthis.getRComContent(arr,nVal)\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type2,nVal,this.type4)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t'configObj.toneConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,nVal,this.type3,this.type4)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t'configObj.toneCartConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type4 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type2,this.type3,nVal)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n\t\t\tgetRComContent(arr,type3){\n\t\t\t\tif(type3 == 0){\n\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.twoContent]\n\t\t\t\t}else{\n\t\t\t\t\tthis.rCom = [...arr,...this.oneContent]\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetRComStyle(arr,type,type2,type3,type4){\n\t\t\t\tlet obj = [...arr,...this.oneStyle,...this.currencyStyle];\n\t\t\t\tlet obj2 = [...arr,...this.oneStyle,...this.fourStyle,...this.currencyStyle];\n\t\t\t\tif(type == 0){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}else if(type == 1){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle2,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle2,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle2,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}else if(type == 2){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle2,...this.threeStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle2,...this.threeStyle,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle2,...this.threeStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle3,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle3,...this.fourStyle,...this.fourStyle2,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle3,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n            getConfig (data) {\n\t\t\t\tlet configObj = this.configObj.tabConfig.list[this.configObj.tabConfig.tabCur]\n\t\t\t\tlet activeValue = configObj.selectConfig.activeValue\n\t\t\t\tif (!data.name && data == 1) {\n\t\t\t\t    configObj.goodsList.list = []\n\t\t\t\t    return\n\t\t\t\t}\n\t\t\t\tif (!data.name && data == 0 && !activeValue.length) {\n\t\t\t\t    configObj.goodsList.list = []\n\t\t\t\t    return\n\t\t\t\t}\n\t\t\t\tlet type = configObj.tabVal\n\t\t\t\tlet dataObj = {\n\t\t\t\t\tpage: 1,\n\t\t\t\t\tlimit: data.values || configObj.numConfig.val,\n\t\t\t\t\tpriceOrder: configObj.goodsSort == 2 ? 'desc' : '',\n\t\t\t\t\tsalesOrder: configObj.goodsSort == 1 ? 'desc' : ''\n\t\t\t\t}\n\t\t\t\tif(type == 1){\n\t\t\t\t\tthis.configObj.productList.list = []\n\t\t\t\t\treturn\n\t\t\t\t}else if(type == 2){\n\t\t\t\t\tdataObj.brand_id = configObj.brandConfig.brandVal;\n\t\t\t\t}else if(type == 3){\n\t\t\t\t\tdataObj.id = activeValue;\n\t\t\t\t}else {\n\t\t\t\t\tdataObj.store_label_id = configObj.goodsLabel.activeValue;\n\t\t\t\t}\n\t\t\t\tgetProduct(dataObj).then(res => {\n\t\t\t\t    configObj.productList.list = res.data;\n\t\t\t\t})\n            }\n        }\n    }\n", null]}