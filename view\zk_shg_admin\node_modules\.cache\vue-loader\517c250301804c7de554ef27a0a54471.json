{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue", "mtime": 1718268362000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState, mapMutations } from 'vuex';\nimport Setting from '@/setting';\nimport util from '@/libs/util';\nimport vuedraggable from 'vuedraggable';\nimport uploadPictures from '@/components/uploadPictures';\nimport freightTemplate from '@/components/freightTemplate';\nimport wangeditor from '@/components/wangEditor/index.vue';\nimport storeList from \"@/components/storeList\";\nimport menusFrom from '../productBrand/components/menusFrom';\nimport userLabel from '@/components/labelList';\nimport storeLabelList from '@/components/storeLabelList';\nimport couponList from '@/components/couponList';\nimport goodsList from '@/components/goodsList/index';\nimport addAttr from '../productAttr/addAttr';\nimport attrList from './attrList';\nimport addCarMy from './addCarMy';\nimport taoBao from '../productAdd/taoBao';\nimport replyList from './replyList.vue';\nimport {\n  productInfoApi,\n  cascaderListApi,\n  productAddApi,\n  generateAttrApi,\n  productGetRuleApi,\n  productGetTemplateApi,\n  productGetTempKeysApi,\n  checkActivityApi,\n  labelListApi,\n  productCache,\n  cacheDelete,\n  brandList,\n  productCreateApi,\n  productAllUnit,\n  productUnitCreate,\n  uploadType,\n  productAllEnsure,\n  productLabelAdd,\n  productAllSpecs,\n  setReplyApi,\n  replyListApi,\n  allSystemForm\n} from '@/api/product';\nimport { systemFormInfo } from '@/api/setting'\nimport { getSupplierList } from '@/api/supplier';\nimport { erpConfig } from '@/api/erp';\nimport { uploadByPieces } from '@/utils/upload'; //引入uploadByPieces方法\nexport default {\n  name: 'product_productAdd',\n  components: {\n    storeList,\n    uploadPictures,\n    freightTemplate,\n    goodsList,\n    addAttr,\n    couponList,\n    taoBao,\n    userLabel,\n    menusFrom,\n    attrList,\n    addCarMy,\n    storeLabelList,\n    wangeditor,\n    draggable: vuedraggable,\n    replyList,\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false,\n    },\n    productId: {\n      type: Number,\n      default: 0,\n    },\n  },\n  data() {\n    return {\n      formTypeList: [],\n      formColumns: [\n        {\n          title: '表单标题',\n          key: 'title',\n          // align:'center',\n          minWidth: 100\n        },\n        {\n          title: '表单类型',\n          key: 'name',\n          // align:'center',\n          minWidth: 100\n        },\n        {\n          title: '是否必填',\n          slot: 'require',\n          // align:'center',\n          minWidth: 100\n        }\n      ],\n      storesList:[],\n      tableData:[],\n      storeModals: false,\n      roterPre: Setting.roterPre,\n      specsList: [],\n      supplierList: [],\n      specsColumns: [\n        {\n          title: '参数名称',\n          key: 'name',\n          align: 'center',\n          width: 150,\n          render: (h, params) => {\n            return h('div', [\n              h('Input', {\n                props: {\n                  value: params.row.name,\n                  placeholder: '请输入参数名称',\n                },\n                on: {\n                  'on-change': (e) => {\n                    params.row.name = e.target.value;\n                    this.specsList[params.index].name = e.target.value;\n                  },\n                },\n              }),\n            ]);\n          },\n        },\n        {\n          title: '参数值',\n          key: 'value',\n          align: 'center',\n          width: 300,\n          render: (h, params) => {\n            return h('div', [\n              h('Input', {\n                props: {\n                  value: params.row.value,\n                  placeholder: '请输入参数值',\n                },\n                on: {\n                  'on-change': (e) => {\n                    params.row.value = e.target.value;\n                    this.specsList[params.index].value = e.target.value;\n                  },\n                },\n              }),\n            ]);\n          },\n        },\n        {\n          title: '排序',\n          key: 'sort',\n          align: 'center',\n          width: 100,\n          render: (h, params) => {\n            return h('div', [\n              h('InputNumber', {\n                props: {\n                  value: parseInt(params.row.sort) || 0,\n                  placeholder: '排序',\n                  precision: 0,\n                },\n                on: {\n                  'on-change': (e) => {\n                    params.row.sort = e;\n                    this.specsList[params.index].sort = e;\n                  },\n                },\n              }),\n            ]);\n          },\n        },\n        {\n          title: '操作',\n          slot: 'action',\n          align: 'center',\n          minWidth: 120,\n        },\n      ],\n      //自定义留言下拉选择\n      customList: [\n        {\n          value: 'text',\n          label: '文本框',\n        },\n        {\n          value: 'number',\n          label: '数字',\n        },\n        {\n          value: 'email',\n          label: '邮件',\n        },\n        {\n          value: 'data',\n          label: '日期',\n        },\n        {\n          value: 'time',\n          label: '时间',\n        },\n        {\n          value: 'id',\n          label: '身份证',\n        },\n        {\n          value: 'phone',\n          label: '手机号',\n        },\n        {\n          value: 'img',\n          label: '图片',\n        },\n      ],\n      headTab: [\n        { title: '基础信息', name: '1' },\n        { title: '规格库存', name: '2' },\n        { title: '商品详情', name: '3' },\n        { title: '物流设置', name: '4' },\n        { title: '营销设置', name: '5' },\n        { title: '其他设置', name: '6' },\n        { title: \"适用门店\", name: \"8\" },\n        { title: '商品评论', name: '7' },\n      ],\n      productType: [\n        { name: '普通商品', title: '物流发货', id: 0 },\n        { name: '卡密/网盘', title: '自动发货', id: 1 },\n        { name: '虚拟商品', title: '虚拟发货', id: 3 },\n        { name: \"次卡商品\", title: \"到店核销\", id: 4 },\n      ],\n      virtualList: [],\n      carMyShow: false, //是否开启卡密弹窗\n      recommend: [], //商品推荐\n      customBtn: false, //自定义留言开关\n      attrShow: false,\n      content: '',\n      contents: '',\n      seletVideo: 0,\n      fileUrl: Setting.apiBaseURL + '/file/upload',\n      fileUrl2: Setting.apiBaseURL + '/file/video_upload',\n      upload_type: '', //视频上传类型 1 本地上传 2 3 4 OSS上传\n      uploadData: {}, // 上传参数\n      header: {},\n      dataLabel: [],\n      storeDataLabel: [],\n      labelShow: false,\n      storeLabelShow: false,\n      props: { emitPath: false, multiple: true, checkStrictly: true },\n      type: 0,\n      goodsModals: false,\n      off_show: 0,\n      modals: false,\n      spinShow: false,\n      openSubimit: false,\n      grid2: {\n        xl: 10,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      grid3: {\n        xl: 18,\n        lg: 18,\n        md: 20,\n        sm: 24,\n        xs: 24,\n      },\n      // 批量设置表格data\n      oneFormBatch: [\n        {\n          attr: '全部',\n          pic: '',\n          price: 0,\n\t\t  settle_price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: '',\n          code: '',\n          weight: 0,\n          volume: 0,\n        },\n      ],\n      // 规格数据\n      formDynamic: {\n        attrsName: '',\n        attrsVal: '',\n      },\n      formDynamicNameData: [],\n      isBtn: false,\n      columnsCarMy: [],\n      columns2: [\n        {\n          title: '商品规格',\n          slot: 'attr',\n          align: 'center',\n          minWidth: 80,\n        },\n        {\n          title: '图片',\n          slot: 'pic',\n          align: 'center',\n          minWidth: 80,\n        },\n        {\n          title: '售价',\n          slot: 'price',\n          align: 'center',\n          minWidth: 95,\n        },\n        {\n          title: '成本价',\n          slot: 'cost',\n          align: 'center',\n          minWidth: 95,\n        },\n        {\n          title: '原价',\n          slot: 'ot_price',\n          align: 'center',\n          minWidth: 95,\n        },\n        {\n          title: '库存',\n          slot: 'stock',\n          align: 'center',\n          minWidth: 95,\n        },\n        {\n          title: '商品条形码',\n          slot: 'bar_code',\n          align: 'center',\n          minWidth: 120,\n        },\n        {\n          title: '商品编号',\n          slot: 'code',\n          align: 'center',\n          minWidth: 120,\n        },\n        {\n          title: '重量（KG）',\n          slot: 'weight',\n          align: 'center',\n          minWidth: 95,\n        },\n        {\n          title: '体积(m³)',\n          slot: 'volume',\n          align: 'center',\n          minWidth: 95,\n        },\n        {\n          title: '操作',\n          slot: 'action',\n          // fixed: \"right\",\n          align: 'center',\n          minWidth: 140,\n        },\n      ],\n      columns: [],\n      columnsInstall: [],\n      columnsInstal2: [],\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      formValidate: {\n        system_form_id: 0, //自定义表单\n        supplier_id: 0, //供应商\n        is_presale_product: 0, //预售商品开关\n        is_limit: 0, //是否限购开关\n        limit_type: 1, //1单次限购，2长期限购\n        limit_num: 1, //限购数量\n        is_vip_product: 0, //付费会员专属开关\n        is_support_refund: 0,\n        disk_info: '', //卡密简介\n        presale_day: 1, //预售发货时间-结束\n        presale_time: [],\n        auto_on_time: '',\n        video_open: false, //视频按钮是否显示\n        store_name: '',\n        freight: 1, //运费设置\n        postage: 0, //设置运费金额\n        custom_form: [], //自定义留言\n        cate_id: [],\n        label_id: [],\n        ensure_id: [],\n        keyword: '',\n        applicable_type: 1,\n        unit_name: '',\n        specs_id: 0,\n        store_info: '',\n        bar_code: '',\n        code: '',\n        image: '',\n        recommend_image: '',\n        slider_image: [],\n        description: '',\n        ficti: 0,\n        give_integral: 0,\n        sort: 0,\n        is_show: 1,\n        is_hot: 0,\n        is_benefit: 0,\n        is_best: 0,\n        is_new: 0,\n        is_good: 0,\n        is_postage: 0,\n        is_sub: [],\n        id: 0,\n        spec_type: 0,\n        video_link: '',\n        temp_id: '',\n        attrs: [],\n        items: [\n          {\n            pic: '',\n            price: 0,\n            cost: 0,\n            ot_price: 0,\n            stock: 0,\n            bar_code: '',\n            code: '',\n          },\n        ],\n        activity: ['默认', '秒杀', '砍价', '拼团'],\n        couponName: [],\n        header: [],\n        selectRule: '',\n        coupon_ids: [],\n        command_word: '',\n        delivery_type: ['1'],\n        specs: [],\n        recommend_list: [],\n        brand_id: [],\n        product_type: 0,\n      },\n      ruleList: [],\n      templateList: [],\n      createBnt: false,\n      showIput: false,\n      manyFormValidate: [],\n      // 单规格表格data\n      oneFormValidate: [\n        {\n          pic: '',\n          price: 0,\n\t\t  settle_price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: '',\n          code: '',\n          weight: 0,\n          volume: 0,\n          brokerage: 0,\n          brokerage_two: 0,\n          vip_price: 0,\n          virtual_list: [],\n          write_times: 0, //核销次数\n          write_valid: 1, //核销时效\n          days: 1\n        },\n      ],\n      images: [],\n      imagesTable: '',\n      currentTab: '1',\n      isChoice: '',\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      loading: false,\n      modalPic: false,\n      template: false,\n      uploadList: [],\n      treeSelect: [],\n      labelSelect: [],\n      ensureData: [],\n      specsData: [],\n      picTit: '',\n      tableIndex: 0,\n      ruleValidate: {\n        store_name: [\n          { required: true, message: '请输入商品名称', trigger: 'blur' },\n        ],\n        cate_id: [\n          {\n            required: true,\n            message: '请选择商品分类',\n            trigger: 'change',\n            type: 'array',\n          },\n        ],\n        keyword: [\n          { required: true, message: '请输入商品关键字', trigger: 'blur' },\n        ],\n        unit_name: [\n          {\n            required: true,\n            message: '请输入单位',\n            trigger: 'change',\n          },\n        ],\n        store_info: [\n          { required: true, message: '请输入商品简介', trigger: 'blur' },\n        ],\n        //image: [{ required: true, message: \"请上传商品图\", trigger: \"change\" }],\n        slider_image: [\n          {\n            required: true,\n            message: '请上传商品轮播图',\n            type: 'array',\n            trigger: 'change',\n          },\n        ],\n        spec_type: [\n          { required: true, message: '请选择商品规格', trigger: 'change' },\n        ],\n        selectRule: [\n          { required: true, message: '请选择商品规格属性', trigger: 'change' },\n        ],\n        give_integral: [{ type: 'integer', message: '请输入整数' }],\n        // delivery_type:[\n        //   { required: true, type: 'array', min: 1, message: '请选择配送方式', trigger: 'change' },\n        // ]\n      },\n      manyBrokerage: 0,\n      manyBrokerageTwo: 0,\n      manyVipPrice: 0,\n      upload: {\n        videoIng: false, // 是否显示进度条；\n      },\n      videoIng: false, // 是否显示进度条；\n      progress: 0, // 进度条默认0\n      videoLink: '',\n      attrs: [],\n      activity: {\n        默认: 'colorBlue',\n        秒杀: 'colorBlue',\n        砍价: 'colorBlue',\n        拼团: 'colorBlue',\n      },\n      couponName: [],\n      updateIds: [],\n      updateName: [],\n      rakeBack: [\n        {\n          title: '一级返佣',\n          slot: 'brokerage',\n          align: 'center',\n          width: 95,\n        },\n        {\n          title: '二级返佣',\n          slot: 'brokerage_two',\n          align: 'center',\n          width: 95,\n        },\n      ],\n      member: [\n        {\n          title: '会员价',\n          slot: 'vip_price',\n          align: 'center',\n          width: 95,\n        },\n      ],\n      headerCarMy: {\n        title: '卡密设置',\n        slot: 'fictitious',\n        align: 'center',\n        width: 95,\n      },\n      columnsInstalM: [],\n      moveIndex: '',\n      goodsData: [],\n      brandData: [],\n      unitNameList: [],\n      formBrand: {},\n      attrsList: [],\n      activeAtter: [],\n      tabIndex: 0,\n      tabName: '',\n      attrData: [],\n      datePickerOptions: {\n        disabledDate(date) {\n          return date && date.valueOf() < Date.now() - 86400000;\n        },\n      },\n      openErp: false,\n      replyColumns: [\n        {\n          title: '评论ID',\n          key: 'id',\n          width: 80,\n        },\n        {\n          title: '商品信息',\n          slot: 'info',\n          minWidth: 250,\n        },\n        // {\n        //   title: '用户名称',\n        //   key: 'nickname',\n        //   minWidth: 150,\n        // },\n        // {\n        //   title: '评分',\n        //   key: 'score',\n        //   sortable: true,\n        //   minWidth: 70,\n        // },\n        {\n          title: '评价内容',\n          slot: 'content',\n          minWidth: 300,\n        },\n        // {\n        //   title: '回复内容',\n        //   slot: 'reply',\n        //   minWidth: 160,\n        // },\n        {\n          title: '评价时间',\n          key: 'add_time',\n          sortable: true,\n          width: 150,\n        },\n        {\n          title: '操作',\n          slot: 'action',\n          // fixed: 'right',\n          width: 150,\n        },\n      ],\n      replyData: [],\n      replyLoading: false,\n      replyModal: false,\n      rows: {},\n      replyForm: {\n        content: '',\n      },\n      ruleInline: {\n        content: [\n          { required: true, message: '请输入回复内容', trigger: 'blur' },\n        ],\n      },\n      replyValidate: {\n        is_reply: '',\n        data: '',\n        store_name: '',\n        account: '',\n        product_id: this.productId,\n        page: 1,\n        limit: 15,\n      },\n      total: 0,\n      merchantType: 0,\n      formList: [],\n      storeColumns: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          width: 60,\n        },\n        {\n          title: \"门店图片\",\n          slot: \"image\",\n          minWidth: 80,\n        },\n        {\n          title: \"门店分类\",\n          key: \"cate_name\",\n          minWidth: 80,\n        },\n        {\n          title: \"门店名称\",\n          key: \"name\",\n          minWidth: 80,\n        },\n        {\n          title: \"联系电话\",\n          key: \"phone\",\n          minWidth: 90,\n        },\n        {\n          title: \"门店地址\",\n          key: \"address\",\n          ellipsis: true,\n          minWidth: 150,\n        },\n        {\n          title: \"营业时间\",\n          key: \"day_time\",\n          minWidth: 120,\n        },\n        {\n          title: \"营业状态\",\n          key: \"status_name\",\n          minWidth: 80,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          width: 100,\n        }\n      ]\n    };\n  },\n  computed: {\n    ...mapState('admin/layout', ['isMobile', 'menuCollapse']),\n    labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    },\n    labelBottom() {\n      return this.isMobile ? undefined : 15;\n    },\n    startPickOptions() {\n      const that = this;\n      return {\n        disabledDate(time) {\n          if (that.formValidate.auto_off_time) {\n            return (\n                time.getTime() >\n                new Date(that.formValidate.auto_off_time).getTime() - 86400000\n            );\n          }\n          return '';\n        },\n      };\n    },\n    endPickOptions() {\n      const that = this;\n      return {\n        disabledDate(time) {\n          if (that.formValidate.is_show == '1') {\n            return time.getTime() < Date.now();\n          }\n          if (that.formValidate.auto_on_time) {\n            return (\n                time.getTime() <\n                new Date(that.formValidate.auto_on_time).getTime() + 86400000\n            );\n          }\n          return '';\n        },\n      };\n    },\n  },\n  created() {\n    this.getSupplierList();\n    this.columns = this.columns2.slice(1, 10);\n    let data = JSON.parse(JSON.stringify(this.columns2));\n    data.splice(8, 2, this.headerCarMy);\n    this.columnsCarMy = data;\n    let fictitious = JSON.parse(JSON.stringify(this.columns2));\n    fictitious.splice(8, 2);\n    this.columnsFictitious = fictitious;\n    this.getToken();\n    this.getErpConfig();\n    // this.columnsInstall = this.columns2.slice(0, 4).concat(this.columnsInstall);\n    // this.columnsInsta8 = this.columns2.slice(0, 4).concat(this.columnsInsta8);\n  },\n  mounted() {\n    this.setCopyrightShow({ value: false });\n    // if (this.productId !== '0' && this.productId) {\n    //   this.getInfo();\n    // } else if (this.productId === '0') {\n    //   productCache()\n    //       .then((res) => {\n    //         let data = res.data.info;\n    //         if (!Array.isArray(data)) {\n    //           let cate_id = data.cate_id.map(Number);\n    //           let label_id = data.label_id.map(Number);\n    //           this.attrs = data.items || [];\n    //           let ids = [];\n    //           // let names = [];\n    //           if (data.coupons) {\n    //             data.coupons.map((item) => {\n    //               ids.push(item.id);\n    //               // names.push(item.title);\n    //             });\n    //             this.couponName = data.coupons;\n    //           }\n    //           this.storesList = data.stores || [];\n    //           let brandIds = [];\n    //           data.brand_id.forEach(item=>{\n    //             brandIds.push(item.toString())\n    //           })\n    //           this.formValidate = data;\n    //           // this.couponName = data.coupons;\n    //           // that.couponName = names;\n    //           this.formValidate.brand_id = brandIds;\n    //           this.formValidate.coupon_ids = ids;\n    //           this.formValidate.is_limit = this.formValidate.is_limit ? 1 : 0;\n    //           this.formValidate.limit_type = parseInt(\n    //               this.formValidate.limit_type\n    //           );\n    //           this.formValidate.is_support_refund = parseInt(\n    //               this.formValidate.is_support_refund\n    //           );\n    //           this.updateIds = ids;\n    //           this.updateName = data.coupons;\n    //           this.formValidate.cate_id = cate_id;\n    //           this.dataLabel = data.label_id;\n    //           this.storeDataLabel = data.store_label_id;\n    //           this.specsList = data.specs;\n    //           this.oneFormValidate = data.attrs;\n    //           this.formValidate.header = [];\n    //           this.generate(0);\n    //           this.addmanyData(data.attrs);\n    //           this.productTypeTap(2);\n    //           this.columns = this.columns2.slice(1, 10);\n    //           //this.manyFormValidate = data.attrs;\n    //           this.formValidate.system_form_id = data.system_form_id || 0;\n    //           if (this.formValidate.system_form_id) {\n    //             this.customBtn = true;\n    //           }\n    //           this.spec_type = data.spec_type;\n    //           if (data.spec_type === 0) {\n    //             this.manyFormValidate = [];\n    //           } else {\n    //             this.createBnt = true;\n    //             this.oneFormValidate = [\n    //               {\n    //                 pic: data.slider_image[0],\n    //                 price: 0,\n    //                 cost: 0,\n    //                 ot_price: 0,\n    //                 stock: 0,\n    //                 bar_code: '',\n    //                 code: '',\n    //                 weight: 0,\n    //                 volume: 0,\n    //                 brokerage: 0,\n    //                 brokerage_two: 0,\n    //                 vip_price: 0,\n    //                 virtual_list: [],\n    //                 write_times: 0, //核销次数\n    //                 write_valid: 1, //核销时效\n    //                 days: 1\n    //               },\n    //             ];\n    //           }\n    //           this.spinShow = false;\n    //         }\n    //       })\n    //       .catch((err) => {\n    //         this.$Message.error(err.msg);\n    //       });\n    // }\n  },\n  destroyed() {\n    this.setCopyrightShow({ value: true });\n  },\n  methods: {\n    ...mapMutations('admin/layout', ['setCopyrightShow']),\n    changeForm(e){\n      this.getSystemFormInfo(e,{type:1});\n    },\n    getSystemFormInfo(e,data){\n      systemFormInfo(e,data).then(res=>{\n        this.formTypeList = res.data.info;\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n      })\n    },\n    //删除门店\n    delte(index){\n      this.storesList.splice(index, 1)\n    },\n    //添加门店\n    addStore(){\n      this.storeModals = true;\n    },\n    //关闭门店弹窗\n    cancelStore(){\n      this.storeModals = false;\n    },\n    allFormList(){\n      allSystemForm().then(res=>{\n        this.formList = res.data;\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n      })\n    },\n    limitTap(e) {\n      if (e) {\n        this.formValidate.limit_type =\n            this.formValidate.is_limit && !this.formValidate.limit_type ? 1 : 0;\n        this.formValidate.limit_num =\n            this.formValidate.is_limit && this.formValidate.limit_num == 0\n                ? 1\n                : 0;\n      } else {\n        this.formValidate.limit_type = 0;\n        this.formValidate.limit_num = 0;\n      }\n    },\n    //erp配置\n    getErpConfig() {\n      erpConfig()\n          .then((res) => {\n            this.openErp = res.data.open_erp;\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n    },\n    delSpecs(index) {\n      this.specsList.splice(index, 1);\n    },\n    addSpecs() {\n      let obj = { name: '', value: '', sort: 0 };\n      this.specsList.push(obj);\n    },\n    specsInfo(e) {\n      this.specsData.forEach((item) => {\n        if (item.id == e) {\n          this.specsList = item.specs;\n        }\n      });\n    },\n    getProductAllSpecs() {\n      productAllSpecs()\n          .then((res) => {\n            this.specsData = res.data;\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n    },\n    getProductAllEnsure() {\n      productAllEnsure()\n          .then((res) => {\n            this.ensureData = res.data;\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n    },\n    //添加倒入卡密的值\n    changeVirtual(e) {\n      this.virtualList = this.virtualList.concat(e);\n    },\n    //添加卡密\n    addVirtual(index, name) {\n      this.tabIndex = index;\n      this.tabName = name;\n      this.virtualListClear();\n      this.$refs.addCarMy.fixedCar = {\n        disk_info: '',\n        stock: 0,\n      };\n      this.$refs.addCarMy.cartMyType = 1;\n      this.carMyShow = true;\n    },\n    //确认提交卡密\n    fixdBtn(e) {\n      if (e.cartMyType == 1) {\n        this.$set(this[this.tabName][this.tabIndex], 'disk_info', e.disk_info);\n        this.$set(this[this.tabName][this.tabIndex], 'stock', Number(e.stock));\n        this[this.tabName][this.tabIndex].virtual_list = [];\n      } else {\n        this.$set(\n            this[this.tabName][this.tabIndex],\n            'virtual_list',\n            e.virtualList\n        );\n        this.$set(\n            this[this.tabName][this.tabIndex],\n            'stock',\n            e.virtualList.length\n        );\n        this[this.tabName][this.tabIndex].disk_info = '';\n      }\n      this.carMyShow = false;\n    },\n    closeCarMy() {\n      this.carMyShow = false;\n    },\n    //清空卡密\n    virtualListClear() {\n      this.virtualList = [\n        {\n          key: '',\n          value: '',\n        },\n      ];\n    },\n    seeVirtual(data, name, index) {\n      this.tabName = name;\n      this.tabIndex = index;\n      this.virtualListClear();\n      this.$refs.addCarMy.fixedCar = {\n        disk_info: '',\n        stock: 0,\n      };\n      if (data.virtual_list && data.virtual_list.length) {\n        this.$refs.addCarMy.cartMyType = 2;\n        this.virtualList = data.virtual_list;\n      } else if (data.disk_info) {\n        this.$refs.addCarMy.cartMyType = 1;\n        this.$refs.addCarMy.fixedCar.disk_info = data.disk_info;\n        this.$refs.addCarMy.fixedCar.stock = data.stock;\n      }\n      this.carMyShow = true;\n    },\n    //动态添加组件\n    addAssembly() {\n      this.formValidate.custom_form.push({\n        title: '',\n        label: 'text',\n        value: '',\n        status: 0,\n      });\n    },\n    customMessBtn(e) {\n      if (!e) {\n        this.formValidate.system_form_id = 0;\n      }\n    },\n    addcustom() {\n      if (this.formValidate.custom_form.length > 9) {\n        this.$Message.warning('最多添加10条');\n      } else {\n        this.addAssembly();\n      }\n    },\n    delcustom(index) {\n      this.formValidate.custom_form.splice(index, 1);\n    },\n    // 预售具体日期\n    onchangeTime(e) {\n      this.formValidate.presale_time = e;\n    },\n    //定时上架\n    onchangeShow(e) {\n      this.formValidate.auto_on_time = e;\n    },\n    //定时下架\n    onchangeOff(e) {\n      this.formValidate.auto_off_time = e;\n    },\n    // 获取供应商内容\n    getSupplierList() {\n      getSupplierList()\n          .then(async (res) => {\n            this.supplierList = res.data;\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    //打开属性\n    batchAttr() {\n      this.attrShow = true;\n      // if(!this.activeAtter.length){\n      //  let data = this.attrs;\n      //  data.map(el=>{\n      //  \t\t\tel.details = [];\n      //  \tel.detail.map(label=>{\n      //  \t\tel.details.push({\n      //  \t\t\tname:label,\n      //  \t\t\tselect:false\n      //  \t\t})\n      //  \t})\n      //  })\n      //  this.attrsList = data;\n      // }\n    },\n    //获取属性\n    getAttr() {\n      this.oneFormBatch[0].attr = '全部';\n      let data = this.attrs;\n      data.map((el) => {\n        el.details = [];\n        el.detail.map((label) => {\n          el.details.push({\n            name: label,\n            select: false,\n          });\n        });\n      });\n      this.attrsList = data;\n    },\n    //选中属性\n    activeAttr(e) {\n      this.attrsList = e;\n    },\n    //关闭属性弹窗\n    labelAttr() {\n      this.attrShow = false;\n    },\n    //多属性为空\n    manyEmpty(j) {\n      j.pic = '';\n      j.price = 0;\n\t  j.settle_price = 0;\n      j.cost = 0;\n      j.ot_price = 0;\n      j.stock = 0;\n      j.bar_code = '';\n      j.code = '';\n      j.weight = 0;\n      j.volume = 0;\n      j.virtual_list = [];\n    },\n\n    doCombination(arr) {\n      var count = arr.length - 1; //数组长度(从0开始)\n      var tmp = [];\n      var totalArr = []; // 总数组\n\n      return doCombinationCallback(arr, 0); //从第一个开始\n      //js 没有静态数据，为了避免和外部数据混淆，需要使用闭包的形式\n      function doCombinationCallback(arr, curr_index) {\n        for (let val of arr[curr_index]) {\n          tmp[curr_index] = val; //以curr_index为索引，加入数组\n          //当前循环下标小于数组总长度，则需要继续调用方法\n          if (curr_index < count) {\n            doCombinationCallback(arr, curr_index + 1); //继续调用\n          } else {\n            totalArr.push(tmp.join(',')); //(直接给push进去，push进去的不是值，而是值的地址)\n          }\n\n          //js  对象都是 地址引用(引用关系)，每次都需要重新初始化，否则 totalArr的数据都会是最后一次的 tmp 数据；\n          let oldTmp = tmp;\n          tmp = [];\n          for (let index of oldTmp) {\n            tmp.push(index);\n          }\n        }\n        return totalArr;\n      }\n    },\n\n    //提交属性值；\n    subAttrs(e) {\n      let selectData = [];\n      this.attrsList.forEach((el, index) => {\n        let obj = [];\n        el.details.forEach((label) => {\n          if (label.select) {\n            obj.push(label.name);\n          }\n        });\n        if (obj.length) {\n          selectData.push(obj);\n        }\n      });\n      let newData = [];\n      if (selectData.length) {\n        newData = this.doCombination(selectData);\n      }\n      this.attrShow = false;\n      this.activeAtter = selectData;\n      this.oneFormBatch[0].attr = newData.length ? newData.join(';') : '全部';\n      this.manyFormValidate.forEach((j) => {\n        j.select = false;\n        if (newData.length) {\n          newData.forEach((item) => {\n            if (j.values.split('').length == item.split('').length) {\n              if (j.values == item) {\n                j.select = true;\n              }\n            } else {\n              if (j.values.indexOf(item) != -1) {\n                j.select = true;\n              }\n            }\n          });\n        } else {\n          j.select = true;\n        }\n      });\n      this.$set(this, 'manyFormValidate', this.manyFormValidate);\n    },\n    goodsOn(e) {\n      if (e == 0 || e == 1) {\n        this.formValidate.auto_on_time = '';\n      }\n    },\n    goodsOff(e) {\n      if (!e) {\n        this.formValidate.auto_off_time = '';\n      }\n    },\n    addBrand() {\n      this.$refs.menusFrom.modals = true;\n      this.$refs.menusFrom.titleFrom = '添加品牌分类';\n      this.formBrand = {\n        sort: 0,\n        is_show: 1,\n      };\n      this.formBrand.fid = [0];\n      this.$refs.menusFrom.type = 1;\n    },\n    getAllUnit() {\n      productAllUnit()\n          .then((res) => {\n            this.unitNameList = res.data;\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n    },\n    addClass() {\n      this.$modalForm(productCreateApi()).then(() => this.goodsCategory());\n    },\n    addUnit() {\n      this.$modalForm(productUnitCreate()).then(() => this.getAllUnit());\n    },\n    addStoreLabel() {\n      this.$modalForm(productLabelAdd()).then(() => { });\n    },\n    productTypeTap(num, item) {\n      if (num == 1) {\n        if (this.productId) return this.$Message.error('商品类型不能切换！');\n        this.formValidate.product_type = item.id;\n      }\n      if (this.formValidate.product_type && this.formValidate.product_type != 4) {\n        this.headTab = [\n          { title: '基础信息', name: '1' },\n          { title: '规格库存', name: '2' },\n          { title: '商品详情', name: '3' },\n          { title: '营销设置', name: '5' },\n          { title: '其他设置', name: '6' },\n          { title: '商品评论', name: '7' },\n        ];\n        this.formValidate.postage = 0;\n        this.formValidate.supplier_id = 0;\n      } else if(this.formValidate.product_type == 4){\n        this.headTab = [\n          { title: \"基础信息\", name: \"1\" },\n          { title: \"规格库存\", name: \"2\" },\n          { title: \"商品详情\", name: \"3\" },\n          { title: \"营销设置\", name: \"5\" },\n          { title: \"其他设置\", name: \"6\" },\n          { title: '商品评论', name: '7' },\n          { title: \"适用门店\", name: \"8\" },\n        ]\n      }else {\n        this.headTab = [\n          { title: '基础信息', name: '1' },\n          { title: '规格库存', name: '2' },\n          { title: '商品详情', name: '3' },\n          { title: '物流设置', name: '4' },\n          { title: '营销设置', name: '5' },\n          { title: '其他设置', name: '6' },\n          { title: \"适用门店\", name: \"8\" },\n          { title: '商品评论', name: '7' },\n        ];\n      }\n    },\n    closeLabel(label) {\n      let index = this.dataLabel.indexOf(\n          this.dataLabel.filter((d) => d.id == label.id)[0]\n      );\n      this.dataLabel.splice(index, 1);\n    },\n    activeData(dataLabel) {\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    openLabel(row) {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(\n          JSON.parse(JSON.stringify(this.dataLabel))\n      );\n    },\n    // 标签弹窗关闭\n    labelClose() {\n      this.labelShow = false;\n    },\n    closeStoreLabel(label) {\n      let index = this.storeDataLabel.indexOf(\n          this.storeDataLabel.filter((d) => d.id == label.id)[0]\n      );\n      this.storeDataLabel.splice(index, 1);\n    },\n    activeStoreData(storeDataLabel) {\n      this.storeLabelShow = false;\n      this.storeDataLabel = storeDataLabel;\n    },\n    openStoreLabel(row) {\n      this.storeLabelShow = true;\n      this.$refs.storeLabel.storeLabel(\n          JSON.parse(JSON.stringify(this.storeDataLabel))\n      );\n    },\n    // 标签弹窗关闭\n    storeLabelClose() {\n      this.storeLabelShow = false;\n    },\n    // 品牌列表\n    getBrandList() {\n      brandList()\n          .then((res) => {\n            //initBran()函数作用iview中规定value必须是字符串，后台返回成了数字，用于处理这个，给了个递归；\n            this.initBran(res.data);\n            this.brandData = res.data;\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n    },\n    initBran(data){\n      data.map(item=>{\n        item.value = item.value.toString();\n        if(item.children && item.children.length){\n          this.initBran(item.children);\n        }\n      })\n    },\n    getProductId(e) {\n      this.goodsModals = false;\n      let nArr = this.goodsData.concat(e).filter((element, index, self) => {\n        return (\n            self.findIndex((x) => x.product_id == element.product_id) == index\n        );\n      });\n\n      this.goodsData = nArr.slice(0, 12);\n    },\n    goodCancel() {\n      this.goodsModals = false;\n    },\n    goodsTap() {\n      this.goodsModals = true;\n      this.$refs.goodslist.handleSelectAll();\n    },\n    bindDelete(index) {\n      this.goodsData.splice(index, 1);\n    },\n    cancel() {\n      this.$router.push({ path: this.roterPre + '/product/product_list' });\n    },\n\n    videoSaveToUrl(file) {\n      let imgTypeArr = ['video/mp4'];\n      let imgType = imgTypeArr.indexOf(file.type) !== -1;\n      if (!imgType) {\n        return this.$Message.warning({\n          content: '文件  ' + file.name + '  格式不正确, 请选择格式正确的视频',\n          duration: 5,\n        });\n      }\n      uploadByPieces({\n        randoms: '', // 随机数，这里作为给后端处理分片的标识 根据项目看情况 是否要加\n        file: file, // 视频实体\n        pieceSize: 3, // 分片大小\n        success: (data) => {\n          this.formValidate.video_link = data.file_path;\n          this.progress = 100;\n        },\n        error: (e) => {\n          this.$Message.error(e.msg);\n        },\n        uploading: (chunk, allChunk) => {\n          this.videoIng = true;\n          let st = Math.floor((chunk / allChunk) * 100);\n          this.progress = st;\n        },\n      });\n      return false;\n    },\n\n    // 上传头部token\n    getToken() {\n      this.header['Authori-zation'] = 'Bearer ' + util.cookies.get('token');\n    },\n    // beforeUpload() {\n    //   this.uploadData = {};\n    //   let promise = new Promise((resolve) => {\n    //     this.$nextTick(function () {\n    //       resolve(true);\n    //     });\n    //   });\n    //   return promise;\n    // },\n    // 上传成功\n    handleSuccess(res, file, fileList) {\n      if (res.status === 200) {\n        this.formValidate.video_link = res.data.src;\n        this.$Message.success(res.msg);\n      } else {\n        this.$Message.error(res.msg);\n      }\n    },\n    //获取视频上传类型\n    uploadType() {\n      uploadType().then((res) => {\n        this.upload_type = res.data.upload_type;\n      });\n    },\n    getEditorContent(data) {\n      this.content = data;\n    },\n    infoData(data) {\n      this.storesList = data.stores || [];\n      let cate_id = data.cate_id.map(Number);\n      this.attrs = data.items || [];\n      let ids = [];\n      data.coupons.map((item) => {\n        ids.push(item.id);\n      });\n      this.goodsData = data.recommend_list;\n      if (data.auto_off_time) {\n        this.off_show = 1;\n      } else {\n        this.off_show = 0;\n      }\n      let brandIds = [];\n      data.brand_id.forEach(item=>{\n        brandIds.push(item.toString())\n      })\n      this.formValidate = data;\n      this.formTypeList = data.custom_form_info;\n      this.formValidate.brand_id = brandIds;\n      if (data.type == 2) {\n        this.formValidate.supplier_id = data.relation_id;\n      }\n      this.formValidate.is_limit = this.formValidate.is_limit ? 1 : 0;\n      this.formValidate.limit_type = parseInt(data.limit_type);\n      this.formValidate.is_support_refund = parseInt(\n          this.formValidate.is_support_refund\n      );\n      this.contents = this.content = data.description;\n      this.couponName = data.coupons;\n      this.formValidate.coupon_ids = ids;\n      this.updateIds = ids;\n      this.updateName = data.coupons;\n      this.formValidate.cate_id = cate_id;\n      this.dataLabel = data.label_id;\n      this.storeDataLabel = data.store_label_id;\n      this.specsList = data.specs;\n      if (data.attr) {\n        this.oneFormValidate = [data.attr];\n      }\n      this.formValidate.header = [];\n      this.generate(0);\n      //this.manyFormValidate = data.attrs;\n      this.addmanyData(data.attrs);\n      this.productTypeTap(2);\n      this.formValidate.system_form_id = data.system_form_id || 0;\n      if (this.formValidate.system_form_id) {\n        this.customBtn = true;\n      }else{\n        this.customBtn = false;\n      }\n      this.spec_type = data.spec_type;\n      if (data.spec_type === 0) {\n        this.manyFormValidate = [];\n      } else {\n        this.createBnt = true;\n        this.oneFormValidate = [\n          {\n            pic: '',\n            price: 0,\n\t\t\tsettle_price: 0,\n            cost: 0,\n            ot_price: 0,\n            stock: 0,\n            bar_code: '',\n            code: '',\n            weight: 0,\n            volume: 0,\n            brokerage: 0,\n            brokerage_two: 0,\n            vip_price: 0,\n            virtual_list: [],\n            write_times: 0, //核销次数\n            write_valid: 1, //核销时效\n            days: 1\n          },\n        ];\n      }\n    },\n    //关闭淘宝弹窗并生成数据；\n    onClose(data) {\n      this.modals = false;\n      this.infoData(data);\n    },\n\n    checkMove(evt) {\n      this.moveIndex = evt.draggedContext.index;\n    },\n    end() {\n      this.moveIndex = '';\n    },\n    checkAllGroupChange(data) {\n      this.checkAllGroup(data);\n    },\n    checkAllGroup(data) {\n      if (this.formValidate.spec_type === 0) {\n        if (data.indexOf(0) > -1) {\n          this.columnsInstall = this.columns2.slice(1, 5).concat(this.member);\n        } else if (data.indexOf(1) > -1) {\n          this.columnsInstall = this.columns2.slice(1, 5).concat(this.rakeBack);\n        } else {\n          this.columnsInstall = this.columns2.slice(1, 5);\n        }\n        if (data.length === 2) {\n          this.columnsInstall = this.columns2\n              .slice(1, 5)\n              .concat(this.rakeBack)\n              .concat(this.member);\n        }\n      } else {\n        if (data.indexOf(0) > -1) {\n          this.columnsInstal2 = this.columnsInstalM\n              .slice(0, 4)\n              .concat(this.member);\n        } else if (data.indexOf(1) > -1) {\n          this.columnsInstal2 = this.columnsInstalM\n              .slice(0, 4)\n              .concat(this.rakeBack);\n        } else {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, 4);\n        }\n        if (data.length === 2) {\n          this.columnsInstal2 = this.columnsInstalM\n              .slice(0, 4)\n              .concat(this.rakeBack)\n              .concat(this.member);\n        }\n      }\n    },\n    // 添加优惠券\n    addCoupon() {\n      this.$refs.couponTemplates.isTemplate = true;\n      this.$refs.couponTemplates.tableList();\n    },\n    //对象数组去重；\n    unique(arr) {\n      const res = new Map();\n      return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1));\n    },\n    nameId(id, names) {\n      this.formValidate.coupon_ids = id;\n      this.couponName = this.unique(names);\n    },\n    handleClose(name) {\n      let index = this.couponName.indexOf(name);\n      this.couponName.splice(index, 1);\n      let couponIds = this.formValidate.coupon_ids;\n      couponIds.splice(index, 1);\n      this.updateIds = couponIds;\n      this.updateName = this.couponName;\n    },\n    getStoreId (data) {\n      this.storeModals = false;\n      let list = this.storesList.concat(data);\n      let uni = this.unique(list);\n      this.storesList = uni;\n    },\n    // 运费模板\n    getList() {\n      this.productGetTemplate();\n    },\n    // 添加运费模板\n    addTemp() {\n      this.$refs.templates.isTemplate = true;\n    },\n    // 删除视频；\n    delVideo() {\n      let that = this;\n      that.$set(that.formValidate, 'video_link', '');\n      that.$set(that, 'progress', 0);\n      that.videoIng = false;\n      that.upload.videoIng = false;\n      that.$refs.refid.value = '';\n    },\n    zh_uploadFile() {\n      if (this.seletVideo == 1) {\n        if (this.videoLink && this.$getFileType(this.videoLink) == 'video') {\n          this.formValidate.video_link = this.videoLink;\n        } else {\n          return this.$Message.error('请输入正确的视频链接');\n        }\n      } else {\n        this.$refs.refid.click();\n      }\n    },\n    zh_uploadFile_change(evfile) {\n      let that = this;\n      let suffix = evfile.target.files[0].name.substr(\n          evfile.target.files[0].name.indexOf('.')\n      );\n      if (suffix.indexOf('.mp4') === -1) {\n        return that.$Message.error('只能上传MP4文件');\n      }\n\t  let types = {\n\t\t  key: evfile.target.files[0].name,\n\t\t  contentType: evfile.target.files[0].type,\n\t  };\n      productGetTempKeysApi(types)\n          .then((res) => {\n            that.$videoCloud\n                .videoUpload({\n                  type: res.data.type,\n                  evfile: evfile,\n                  res: res,\n                  uploading(status, progress) {\n                    that.upload.videoIng = status;\n                    if (res.status == 200) {\n                      that.progress = 100;\n                    }\n                  },\n                })\n                .then((res) => {\n                  that.formValidate.video_link = res.url;\n                  that.$Message.success('视频上传成功');\n                  that.upload.videoIng = false;\n                })\n                .catch((res) => {\n                  that.$Message.error(res);\n                });\n          })\n          .catch((res) => {\n            that.$Message.error(res.msg);\n          });\n    },\n    // 上一页；\n    upTab() {\n      if (this.currentTab == 5 && this.formValidate.product_type != 0) {\n        this.currentTab = (Number(this.currentTab) - 2).toString();\n      } else {\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    // 下一页；\n    downTab(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if (\n              this.formValidate.is_show == 2 &&\n              !this.formValidate.auto_on_time\n          ) {\n            return this.$Message.warning('请填写定时上架时间');\n          }\n          if (this.off_show == 1 && !this.formValidate.auto_off_time) {\n            return this.$Message.warning('请填写定时下架时间');\n          }\n          if (this.currentTab == 4 && !this.formValidate.delivery_type.length) {\n            return this.$Message.warning('请选择配送方式');\n          }\n          if (this.currentTab == 3 && this.formValidate.product_type != 0) {\n            this.currentTab = (Number(this.currentTab) + 2).toString();\n          } else {\n            this.currentTab = (Number(this.currentTab) + 1).toString();\n          }\n        } else {\n          this.$Message.warning('请完善数据');\n        }\n      });\n    },\n    // 属性弹窗回调函数；\n    userSearchs() {\n      this.productGetRule();\n    },\n    // 添加规则；\n    addRule() {\n      this.$refs.addattr.modal = true;\n    },\n    // 批量设置分佣；\n    brokerageSetUp() {\n      let that = this;\n      if (that.formValidate.is_sub.indexOf(1) > -1) {\n        if (that.manyBrokerage <= 0 || that.manyBrokerageTwo <= 0) {\n          return that.$Message.error('请填写返佣金额后进行批量添加');\n        }\n      } else if (that.formValidate.is_sub.indexOf(0) > -1) {\n        if (that.manyVipPrice <= 0) {\n          return that.$Message.error('请填写会员价后进行批量添加');\n        }\n      }\n      if (this.formValidate.is_sub.length === 2) {\n        if (\n            that.manyBrokerage <= 0 ||\n            that.manyBrokerageTwo <= 0 ||\n            that.manyVipPrice <= 0\n        ) {\n          return that.$Message.error('请填写完金额后进行批量添加');\n        }\n      }\n      for (let val of that.manyFormValidate) {\n        this.$set(val, 'brokerage', that.manyBrokerage);\n        this.$set(val, 'brokerage_two', that.manyBrokerageTwo);\n        this.$set(val, 'vip_price', that.manyVipPrice);\n      }\n      // let that = this;\n      // if (that.manyBrokerage <= 0 || that.manyBrokerageTwo <= 0) {\n      //     return that.$Message.error('请填写返佣金额在进行批量添加');\n      // } else {\n      //     for (let val of that.manyFormValidate) {\n      //         this.$set(val, 'brokerage', that.manyBrokerage);\n      //         this.$set(val, 'brokerage_two', that.manyBrokerageTwo);\n      //     }\n      // }\n    },\n    // 批量设置会员价\n    vipPriceSetUp() {\n      let that = this;\n      if (that.manyVipPrice <= 0) {\n        return that.$Message.error('请填写会员价在进行批量添加');\n      } else {\n        for (let val of that.manyFormValidate) {\n          this.$set(val, 'vip_price', that.manyVipPrice);\n        }\n      }\n    },\n    batchDel() {\n      this.oneFormBatch = [\n        {\n          attr: '全部',\n          pic: '',\n          price: 0,\n\t\t  settle_price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: '',\n          code: '',\n          weight: 0,\n          volume: 0,\n          virtualList: [],\n          disk_info: '',\n        },\n      ];\n      this.activeAtter = [];\n      for (let val of this.manyFormValidate) {\n        val.select = true;\n      }\n    },\n    confirm() {\n      let that = this;\n      that.createBnt = true;\n      if (that.formValidate.selectRule.trim().length <= 0) {\n        return that.$Message.error('请选择属性');\n      }\n      that.ruleList.forEach(function (item, index) {\n        if (item.rule_name === that.formValidate.selectRule) {\n          that.attrs = item.rule_value;\n        }\n      });\n    },\n    // 获取商品属性模板；\n    productGetRule() {\n      productGetRuleApi().then((res) => {\n        this.ruleList = res.data;\n      });\n    },\n    // 获取运费模板；\n    productGetTemplate() {\n      productGetTemplateApi({id:this.productId}).then((res) => {\n        this.templateList = res.data;\n      });\n    },\n    // 删除表格中的属性\n    delAttrTable(index) {\n      let id = this.productId;\n      if (id) {\n        checkActivityApi(id)\n            .then((res) => {\n              this.manyFormValidate.splice(index, 1);\n              this.$Message.success(res.msg);\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n      } else {\n        this.manyFormValidate.splice(index, 1);\n      }\n    },\n    // 批量添加\n    batchAdd() {\n      for (let val of this.manyFormValidate) {\n        //this.manyEmpty(val);\n        if (val.select) {\n          if (this.oneFormBatch[0].pic) {\n            this.$set(val, 'pic', this.oneFormBatch[0].pic);\n          }\n          if (this.oneFormBatch[0].price > 0) {\n            this.$set(val, 'price', this.oneFormBatch[0].price);\n          }\n\t\t  if (this.oneFormBatch[0].settle_price > 0 && this.merchantType == 2) {\n\t\t    this.$set(val, \"settle_price\", this.oneFormBatch[0].settle_price);\n\t\t  }\n          if (this.oneFormBatch[0].cost > 0) {\n            this.$set(val, 'cost', this.oneFormBatch[0].cost);\n          }\n          if (this.oneFormBatch[0].ot_price > 0) {\n            this.$set(val, 'ot_price', this.oneFormBatch[0].ot_price);\n          }\n          if (this.oneFormBatch[0].stock > 0) {\n            this.$set(val, 'stock', this.oneFormBatch[0].stock);\n          }\n          if (this.oneFormBatch[0].bar_code !== '') {\n            this.$set(val, 'bar_code', this.oneFormBatch[0].bar_code);\n          }\n          if (this.oneFormBatch[0].code !== '') {\n            this.$set(val, 'code', this.oneFormBatch[0].code);\n          }\n          if (this.oneFormBatch[0].weight > 0) {\n            this.$set(val, 'weight', this.oneFormBatch[0].weight);\n          }\n          if (this.oneFormBatch[0].volume > 0) {\n            this.$set(val, 'volume', this.oneFormBatch[0].volume);\n          }\n          if (this.formValidate.product_type == 1) {\n            if (\n                this.oneFormBatch[0].virtual_list &&\n                this.oneFormBatch[0].virtual_list.length\n            ) {\n              this.$set(val, 'virtual_list', this.oneFormBatch[0].virtual_list);\n            } else if (this.oneFormBatch[0].disk_info) {\n              this.$refs.addCarMy.cartMyType = 1;\n              this.$set(val, 'disk_info', this.oneFormBatch[0].disk_info);\n            }\n          }\n        }\n      }\n    },\n    // 添加按钮\n    addBtn() {\n      let id = this.productId;\n      if (id) {\n        checkActivityApi(id)\n            .then((res) => {\n              this.clearAttr();\n              this.createBnt = false;\n              this.showIput = true;\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n      }\n    },\n    addmanyData(data) {\n      data.forEach((item) => {\n        item.select = true;\n      });\n      this.manyFormValidate = data;\n    },\n    // 立即生成\n    generate(type) {\n      generateAttrApi(\n          { attrs: this.attrs, product_type: this.formValidate.product_type },\n          this.formValidate.id,\n          type\n      )\n          .then((res) => {\n            let info = res.data.info,\n                header1 = JSON.parse(JSON.stringify(info.header));\n            if (this.productId !== '0') {\n              this.addmanyData(info.value);\n            }\n            this.formValidate.header = header1;\n            this.attrData = res.data.info.attr;\n            let header = info.header;\n            header.pop();\n            this.columnsInstalM = info.header;\n            this.checkAllGroup(this.formValidate.is_sub);\n            if (!this.productId && this.formValidate.spec_type === 1) {\n              this.manyFormValidate.map((item) => {\n                item.pic = this.formValidate.slider_image[0];\n              });\n              this.oneFormBatch[0].pic = this.formValidate.slider_image[0];\n            } else if (this.productId) {\n              this.manyFormValidate.map((item) => {\n                if (!item.pic) {\n                  item.pic = this.formValidate.slider_image[0];\n                }\n              });\n              this.oneFormBatch[0].pic = this.formValidate.slider_image[0];\n            }\n            this.getAttr();\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    // 取消\n    offAttrName() {\n      this.showIput = false;\n      this.createBnt = true;\n    },\n    clearAttr() {\n      this.formDynamic.attrsName = '';\n      this.formDynamic.attrsVal = '';\n    },\n    // 删除规格\n    handleRemoveRole(index) {\n      this.attrs.splice(index, 1);\n      this.manyFormValidate.splice(index, 1);\n    },\n    // 删除属性\n    handleRemove2(item, index) {\n      item.splice(index, 1);\n    },\n    // 添加规则名称\n    createAttrName() {\n      if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {\n        let data = {\n          value: this.formDynamic.attrsName,\n          detail: [this.formDynamic.attrsVal],\n        };\n        this.attrs.push(data);\n        var hash = {};\n        this.attrs = this.attrs.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next.value] ? '' : (hash[next.value] = true && item.push(next));\n          return item;\n        }, []);\n        this.clearAttr();\n        this.showIput = false;\n        this.createBnt = true;\n      } else {\n        this.$Message.warning('请添加完整的规格！');\n      }\n    },\n    // 添加属性\n    createAttr(num, idx) {\n      if (num) {\n        this.attrs[idx].detail.push(num);\n        var hash = {};\n        this.attrs[idx].detail = this.attrs[idx].detail.reduce(function (\n                item,\n                next\n            ) {\n              /* eslint-disable */\n              hash[next] ? '' : (hash[next] = true && item.push(next));\n              return item;\n            },\n            []);\n      } else {\n        this.$Message.warning('请添加属性');\n      }\n    },\n    // 商品分类；\n    goodsCategory() {\n      cascaderListApi(1)\n          .then((res) => {\n            this.treeSelect = res.data;\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    //视视上传类型\n    changeVideo(e) {\n      this.formValidate.video_link = '';\n      this.videoLink = '';\n    },\n    // 改变规格\n    changeSpec() {\n      this.formValidate.is_sub = [];\n      let id = this.productId;\n      if (id) {\n        checkActivityApi(id)\n            .then((res) => { })\n            .catch((res) => {\n              this.formValidate.spec_type = this.spec_type;\n              this.$Message.error(res.msg);\n            });\n      }\n    },\n    // 详情\n    getInfo() {\n      let that = this;\n      that.spinShow = true;\n      productInfoApi(that.productId)\n          .then(async (res) => {\n            let data = res.data.productInfo;\n            this.merchantType = parseInt(data.type);\n\t\t\tif(this.merchantType == 2){\n\t\t\t\tlet obj = {\n\t\t\t\t\t  title: \"结算价\",\n\t\t\t\t\t  slot: \"settle_price\",\n\t\t\t\t\t  align: \"center\",\n\t\t\t\t\t  minWidth: 95,\n\t\t\t\t\t}\n\t\t\t\tlet found = this.columns2.some(element => element.slot === obj.slot);\n\t\t\t\tif(!found){\n\t\t\t\t\tthis.columns2.splice(3,0,obj)\n\t\t\t\t}\n\t\t\t}\n            this.infoData(data);\n            // let cate_id = data.cate_id.map(Number);\n            // let label_id = data.label_id.map(Number);\n            // this.attrs = data.items || [];\n            // let ids = [];\n            // data.coupons.map((item) => {\n            //   ids.push(item.id);\n            // });\n            // that.formValidate = data;\n            // that.couponName = data.coupons;\n            // that.formValidate.coupon_ids = ids;\n            // that.updateIds = ids;\n            // that.updateName = data.coupons;\n            // that.formValidate.cate_id = cate_id;\n            // that.formValidate.label_id = label_id;\n            // that.oneFormValidate = [data.attr];\n            // that.formValidate.header = [];\n            // that.manyFormValidate = data.attrs;\n            // that.generate(0);\n            // that.spec_type = data.spec_type;\n            // if (data.spec_type === 0) {\n            //   that.manyFormValidate = [];\n            // } else {\n            //   that.createBnt = true;\n            //   that.oneFormValidate = [\n            //     {\n            //       pic: data.image,\n            //       price: 0,\n            //       cost: 0,\n            //       ot_price: 0,\n            //       stock: 0,\n            //       bar_code: \"\",\n            //       weight: 0,\n            //       volume: 0,\n            //       brokerage: 0,\n            //       brokerage_two: 0,\n            //       vip_price: 0,\n            //     },\n            //   ];\n            // }\n            this.spinShow = false;\n          })\n          .catch((res) => {\n            this.spinShow = false;\n            this.$Message.error(res.msg);\n          });\n    },\n    // tab切换\n    onhangeTab(name) {\n      this.currentTab = name;\n    },\n    handleRemove(i) {\n      this.images.splice(i, 1);\n      this.formValidate.slider_image.splice(i, 1);\n      this.oneFormValidate[0].pic = this.formValidate.slider_image[0];\n    },\n    // 关闭图片上传模态框\n    changeCancel(msg) {\n      this.modalPic = false;\n    },\n    // 点击商品图\n    modalPicTap(tit, picTit, index) {\n      this.modalPic = true;\n      this.isChoice = tit === 'dan' ? '单选' : '多选';\n      this.picTit = picTit;\n      this.tableIndex = index;\n    },\n    // 获取单张图片信息\n    getPic(pc) {\n      switch (this.picTit) {\n        case 'danFrom':\n          this.formValidate.image = pc.att_dir;\n          if (!this.productId) {\n            if (this.formValidate.spec_type === 0) {\n              this.oneFormValidate[0].pic = pc.att_dir;\n            } else {\n              this.manyFormValidate.map((item) => {\n                item.pic = pc.att_dir;\n              });\n              this.oneFormBatch[0].pic = pc.att_dir;\n            }\n          }\n          break;\n        case 'danTable':\n          this.oneFormValidate[this.tableIndex].pic = pc.att_dir;\n          break;\n        case 'duopi':\n          this.oneFormBatch[this.tableIndex].pic = pc.att_dir;\n          break;\n        case 'recommend_image':\n          this.formValidate.recommend_image = pc.att_dir;\n          break;\n        default:\n          this.manyFormValidate[this.tableIndex].pic = pc.att_dir;\n      }\n      this.modalPic = false;\n    },\n    // 获取多张图信息\n    getPicD(pc) {\n      this.images = pc;\n      this.images.map((item) => {\n        this.formValidate.slider_image.push(item.att_dir);\n        this.formValidate.slider_image = this.formValidate.slider_image.splice(\n            0,\n            10\n        );\n      });\n      this.oneFormValidate[0].pic = this.formValidate.slider_image[0];\n      this.modalPic = false;\n    },\n    // 提交\n    handleSubmit(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if (!this.formValidate.store_name.trim()) {\n            return this.$Message.warning('基础信息-商品名称不能为空');\n          }\n          if (\n              this.formValidate.is_show == 2 &&\n              !this.formValidate.auto_on_time\n          ) {\n            return this.$Message.warning('基础信息-定时上架时间不能为空');\n          }\n          if (this.off_show == 1 && !this.formValidate.auto_off_time) {\n            return this.$Message.warning('基础信息-定时下架时间不能为空');\n          }\n          if(this.formValidate.product_type == 4 && !this.oneFormValidate[0].write_times){\n            return this.$Message.warning(\"规格库存-核销次数必须大于0\");\n          }\n          if(this.formValidate.product_type == 4 && this.oneFormValidate[0].write_valid==2 && !this.oneFormValidate[0].days){\n            return this.$Message.warning(\"规格库存-有效天数必须大于0\");\n          }\n          if(this.formValidate.product_type == 4 && this.oneFormValidate[0].write_valid==3 && !this.oneFormValidate[0].section_time.length){\n            return this.$Message.warning(\"规格库存-请输入固定有效期\");\n          }\n          if (\n              this.formValidate.freight == 2 &&\n              this.formValidate.product_type == 0 &&\n              this.formValidate.postage <= 0\n          ) {\n            return this.$Message.warning('物流设置-固定邮费不能为0');\n          }\n          if (\n              this.formValidate.freight == 3 &&\n              this.formValidate.product_type == 0 &&\n              !this.formValidate.temp_id\n          ) {\n            return this.$Message.warning('物流设置-运费模板不能为空');\n          }\n          if (\n              this.formValidate.product_type == 0 &&\n              this.formValidate.is_presale_product &&\n              !this.formValidate.presale_time[0]\n          ) {\n            return this.$Message.warning('营销设置-预售时间不能为空');\n          }\n          // for (let i = 0; i < this.formValidate.custom_form.length; i++) {\n          //   const element = this.formValidate.custom_form[i];\n          //   if (!element.title) {\n          //     return this.$Message.warning('其他设置-留言标题不能为空');\n          //   }\n          // }\n          if(this.customBtn && this.formValidate.system_form_id == 0){\n            return this.$Message.warning('其他设置-请选择自定义表单模板');\n          }\n          let storeId = []\n          this.storesList.forEach(item=>{\n            storeId.push(item.id)\n          })\n          if(this.formValidate.applicable_type==2 && !storeId.length){\n            return this.$Message.warning('适用门店-请选择适用门店');\n          }\n          this.formValidate.applicable_store_id = storeId;\n          this.formValidate.type = this.type;\n          if (this.formValidate.spec_type === 0) {\n            this.formValidate.attrs = this.oneFormValidate;\n            this.formValidate.header = [];\n            this.formValidate.items = [];\n          } else {\n            this.formValidate.items = this.attrs;\n            this.formValidate.attrs = this.manyFormValidate;\n          }\n          if (\n              this.formValidate.spec_type === 1 &&\n              this.manyFormValidate.length === 0\n          ) {\n            return this.$Message.warning('规格库存-请点击生成多规格');\n            // return this.$Message.warning('请点击生成规格！');\n          }\n          let item = this.formValidate.attrs;\n          if (this.formValidate.is_sub.indexOf(1) != -1) {\n            for (let i = 0; i < item.length; i++) {\n              if (\n                  item[i].brokerage === null ||\n                  item[i].brokerage_two === null\n              ) {\n                return this.$Message.warning('营销设置- 一二级返佣不能为空');\n              }\n            }\n          }\n          if (this.formValidate.is_sub.indexOf(0) != -1) {\n            for (let i = 0; i < item.length; i++) {\n              if (item[i].vip_price === null) {\n                return this.$Message.warning('营销设置-会员价不能为空');\n              }\n              if (item[i].vip_price === 0) {\n                return this.$Message.warning('营销设置-会员价不能为0');\n              }\n            }\n          }\n          if (this.formValidate.is_sub.length === 2) {\n            for (let i = 0; i < item.length; i++) {\n              if (\n                  item[i].brokerage === null ||\n                  item[i].brokerage_two === null ||\n                  item[i].vip_price === null\n              ) {\n                return this.$Message.error(\n                    '营销设置- 一二级返佣和会员价不能为空'\n                );\n              }\n            }\n          }\n          for (let i = 0; i < this.specsList.length; i++) {\n            let data = this.specsList[i];\n            if (!data.name.trim()) {\n              return this.$Message.error('请输入参数名称');\n            }\n            if (!data.value.trim()) {\n              return this.$Message.error('请输入参数值');\n            }\n          }\n          if (!this.formValidate.product_type) {\n            this.formValidate.is_support_refund = 1;\n          }\n          this.openSubimit = false;\n          this.formValidate.description = this.formatRichText(this.content);\n          let goodsId = [];\n          this.goodsData.forEach((item) => {\n            goodsId.push(item.product_id);\n          });\n          this.formValidate.recommend_list = goodsId;\n          // 用户标签\n          let activeIds = [];\n          this.dataLabel.forEach((item) => {\n            activeIds.push(item.id);\n          });\n          this.formValidate.label_id = activeIds;\n          // 商品标签\n          let storeActiveIds = [];\n          this.storeDataLabel.forEach((item) => {\n            storeActiveIds.push(item.id);\n          });\n          this.formValidate.store_label_id = storeActiveIds;\n          // 商品参数\n          this.formValidate.specs = this.specsList;\n          if(this.formValidate.product_type == 4){\n            this.formValidate.delivery_type = 2\n          }\n          productAddApi(this.formValidate)\n              .then(async (res) => {\n                this.openSubimit = true;\n                this.$Message.success(res.msg);\n                if (this.productId === '0') {\n                  cacheDelete().catch((err) => {\n                    this.$Message.error(err.msg);\n                  });\n                }\n                // this.$refs[name].resetFields();\n                // setTimeout(() => {\n                //   this.$router.push({ path: '/product/product_list' });\n                // }, 500);\n                this.$emit('saved');\n              })\n              .catch((res) => {\n                this.openSubimit = false;\n                this.$Message.error(res.msg);\n              });\n        } else {\n          if (!this.formValidate.store_name) {\n            return this.$Message.warning('基础信息-商品名称不能为空');\n          } else if (!this.formValidate.cate_id.length) {\n            return this.$Message.warning('基础信息-商品分类不能为空');\n          } else if (!this.formValidate.unit_name) {\n            return this.$Message.warning('基础信息-商品单位不能为空');\n          } else if (!this.formValidate.slider_image.length) {\n            return this.$Message.warning('基础信息-商品轮播图不能为空');\n          }\n          //    if(!this.formValidate.store_name || !this.formValidate.cate_id || !this.formValidate.keyword\n          //    || !this.formValidate.unit_name || !this.formValidate.store_info\n          //        || !this.formValidate.image || !this.formValidate.slider_image){\n          //        this.$Message.warning(\"请填写完整商品信息！\");\n          //    }\n        }\n      });\n    },\n    changeTemplate(msg) {\n      this.template = msg;\n    },\n    // 表单验证\n    validate(prop, status, error) {\n      if (status === false) {\n        this.$Message.warning(error);\n      }\n    },\n    // 移动\n    handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    handleDragOver(e) {\n      e.dataTransfer.dropEffect = 'move';\n    },\n    handleDragEnter(e, item) {\n      e.dataTransfer.effectAllowed = 'move';\n      if (item === this.dragging) {\n        return;\n      }\n      const newItems = [...this.formValidate.slider_image];\n      const src = newItems.indexOf(this.dragging);\n      const dst = newItems.indexOf(item);\n      newItems.splice(dst, 0, ...newItems.splice(src, 1));\n      this.formValidate.slider_image = newItems;\n    },\n    // 添加自定义弹窗\n    addCustomDialog(editorId) {\n      window.UE.registerUI(\n          'test-dialog',\n          function (editor, uiName) {\n            // 创建 dialog\n            let dialog = new window.UE.ui.Dialog({\n              iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n              editor: editor,\n              name: uiName,\n              title: '上传图片',\n              cssRules: 'width:1200px;height:500px;padding:20px;',\n            });\n            this.dialog = dialog;\n            let btn = new window.UE.ui.Button({\n              name: 'dialog-button',\n              title: '上传图片',\n              cssRules: `background-image: url(https://cdn.oss.9gt.net/prov1.1/1/icons.png);background-position: -726px -77px;`,\n              onclick: function () {\n                // 渲染dialog\n                dialog.render();\n                dialog.open();\n              },\n            });\n            return btn;\n          },\n          37\n      );\n      window.UE.registerUI(\n          'video-dialog',\n          function (editor, uiName) {\n            let dialog = new window.UE.ui.Dialog({\n              iframeUrl: '/admin/widget.video/index.html?fodder=video',\n              editor: editor,\n              name: uiName,\n              title: '上传视频',\n              cssRules: 'width:1000px;height:500px;padding:20px;',\n            });\n            this.dialog = dialog;\n            let btn = new window.UE.ui.Button({\n              name: 'video-button',\n              title: '上传视频',\n              cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -320px -20px;`,\n              onclick: function () {\n                // 渲染dialog\n                dialog.render();\n                dialog.open();\n              },\n            });\n            return btn;\n          },\n          38\n      );\n    },\n    formatRichText(html) {\n      let newContent = html.replace(/<img[^>]*>/gi, function (match, capture) {\n        match = match\n            .replace(/style=\"[^\"]+\"/gi, '')\n            .replace(/style='[^']+'/gi, '');\n        match = match\n            .replace(/width=\"[^\"]+\"/gi, '')\n            .replace(/width='[^']+'/gi, '');\n        match = match\n            .replace(/height=\"[^\"]+\"/gi, '')\n            .replace(/height='[^']+'/gi, '');\n        return match;\n      });\n      newContent = newContent.replace(\n          /style=\"[^\"]+\"/gi,\n          function (match, capture) {\n            match = match\n                .replace(/width:[^;]+;/gi, 'max-width:100%;')\n                .replace(/width:[^;]+;/gi, 'max-width:100%;');\n            return match;\n          }\n      );\n      // newContent = newContent.replace(/<br[^>]*\\/>/gi, '');\n      newContent = newContent.replace(\n          /\\<img/gi,\n          '<img style=\"max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;\"'\n      );\n      return newContent;\n    },\n    drawerChange(e) {\n      if (e) {\n        this.currentTab = '1';\n        this.type = 0;\n\t\tthis.batchDel();\n        this.getInfo();\n        this.getReplyList();\n        this.goodsCategory();\n        this.productGetRule();\n        this.productGetTemplate();\n        this.getBrandList();\n        this.getAllUnit();\n        this.uploadType();\n        this.getProductAllEnsure();\n        this.getProductAllSpecs();\n        this.allFormList();\n      } else {\n        this.$emit('update:visible', false);\n      }\n    },\n    // 查看评论列表\n    seeReply(row) {\n      this.$refs.replyList.modals = true;\n      this.$refs.replyList.getList(row.id);\n    },\n    // 回复评论\n    reply(row) {\n      this.replyModal = true;\n      this.rows = row;\n      this.replyForm.content = row.replyComment ? row.replyComment.content : '';\n    },\n    // 删除评论\n    delReply(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `product/reply/${row.id}`,\n        method: 'DELETE',\n        ids: '',\n      };\n      this.$modalSure(delfromData)\n          .then((res) => {\n            this.$Message.success(res.msg);\n            this.replyData.splice(num, 1);\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    cancels() {\n      this.replyModal = false;\n      this.$refs['replyForm'].resetFields();\n    },\n    oks() {\n      this.replyModal = true;\n      this.$refs['replyForm'].validate((valid) => {\n        if (valid) {\n          setReplyApi(this.replyForm, this.rows.id)\n              .then(async (res) => {\n                this.$Message.success(res.msg);\n                this.replyModal = false;\n                this.$refs['replyForm'].resetFields();\n                this.getReplyList();\n              })\n              .catch((res) => {\n                this.$Message.error(res.msg);\n              });\n        } else {\n          return false;\n        }\n      });\n    },\n    // 商品评论\n    getReplyList() {\n      this.replyLoading = true;\n      // this.formValidate.is_reply = this.formValidate.is_reply || \"\";\n      // this.formValidate.store_name = this.formValidate.store_name || \"\";\n      this.replyValidate.product_id = this.productId;\n      replyListApi(this.replyValidate)\n          .then(async (res) => {\n            let data = res.data;\n            this.replyData = data.list;\n            this.total = res.data.count;\n            this.replyLoading = false;\n          })\n          .catch((res) => {\n            this.replyLoading = false;\n            this.$Message.error(res.msg);\n          });\n    },\n  },\n};\n", null]}