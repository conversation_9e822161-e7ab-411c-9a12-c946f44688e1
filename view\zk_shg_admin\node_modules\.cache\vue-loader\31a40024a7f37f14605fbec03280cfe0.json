{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue?vue&type=template&id=08b87947&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div\n  :style=\"{\n    width: areaInit.areaWidth + 'px',\n    height: areaInit.areaHeight + 'px',\n    left: areaInit.starX + 'px',\n    top: areaInit.starY + 'px',\n  }\"\n  class=\"areaBox\"\n  @dblclick=\"editBoxShow = true\"\n  @mousedown.left.stop=\"mouseDownLint($event)\"\n  @mouseup.left.stop=\"mouseUp($event)\"\n>\n  <div class=\"prompt-text\">\n    <div class=\"prompt-item num\">热区 {{ areaInit.number }}</div>\n    <div class=\"prompt-item\" :style=\"{ color: isSet ? '#2d8cf0' : '#f00' }\">\n      {{ isSet ? \"(已设置)\" : \"(未设置)\" }}\n    </div>\n  </div>\n  <!--删除-->\n  <div class=\"del\" @click.stop=\"del()\">\n    <Icon type=\"ios-close\" size=\"16\" />\n  </div>\n  <!--形变点-->\n  <div\n    class=\"shape\"\n    @mousedown.left.stop=\"shapeDown($event)\"\n    @mouseup.left.stop=\"mouseUp($event)\"\n  />\n  <!--编辑框-->\n\n  <div @mousedown.stop=\"\">\n    <Modal v-model=\"editBoxShow\" title=\"设置热区\" width=\"560px\">\n      <div class=\"area-set\">\n        <div class=\"area-label\">热区跳转链接：</div>\n        <div class=\"area-content\" @click=\"getLink()\">\n          <Input\n            icon=\"ios-arrow-forward\"\n            v-model=\"url\"\n            readonly\n            style=\"width: 100%\"\n            placeholder=\"选择跳转链接\"\n          />\n        </div>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <Button @click.stop=\"editBoxShow = false\">取 消</Button>\n        <Button type=\"primary\" @click.stop=\"addURL\">确 定</Button>\n      </span>\n    </Modal>\n    <linkaddress ref=\"linkaddres\" @linkUrl=\"linkUrl\"></linkaddress>\n  </div>\n</div>\n", null]}