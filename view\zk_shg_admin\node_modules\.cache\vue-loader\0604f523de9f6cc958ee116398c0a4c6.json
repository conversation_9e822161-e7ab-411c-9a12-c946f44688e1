{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: \"c_pictrue\",\n  props: {\n    configObj: {\n      type: Object,\n    },\n    configNme: {\n      type: String,\n    },\n  },\n  data() {\n    return {\n      defaults: {},\n      configData: {},\n      style: 0,\n      isUpdate: false, // 重新渲染\n      currentIndex: 0,\n      arrayObj: {\n        image: \"\",\n        link: \"\",\n      },\n      list: undefined,\n      select: false,\n      lis: undefined,\n      rect: null, // 定义移动元素div\n      // 记录鼠标按下时的坐标\n      downX: 0,\n      downY: 0,\n      // 记录鼠标抬起时候的坐标\n      mouseX2: 0,\n      mouseY2: 0,\n      imgNum: 0,\n      selPicBox: 0, // 当前选中的图片盒子\n    };\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.defaults = this.configObj;\n      if (this.configObj.hasOwnProperty(\"timestamp\")) {\n        this.isUpdate = true;\n      } else {\n        this.isUpdate = false;\n      }\n      this.$set(this, \"configData\", this.configObj[this.configNme]);\n      this.style = this.configObj.styleConfig.tabVal;\n      this.count = this.defaults.styleConfig.count;\n      this.picArrayConcat(this.count);\n\n      if (this.style == 11) {\n        this.lis = document.getElementsByClassName(\"lay-item\");\n      }\n      this.currentTab(0, this.configData);\n    });\n  },\n  computed: {\n    selBoxList() {\n      return this.configObj.picStyle.docPicList;\n    },\n  },\n  watch: {\n    configObj: {\n      handler(nVal) {\n        this.defaults = nVal;\n        this.$set(this, \"configData\", nVal[this.configNme]);\n        this.style = nVal.styleConfig.tabVal;\n        this.isUpdate = true;\n        this.$set(this, \"isUpdate\", true);\n      },\n      deep: true,\n    },\n    \"configObj.styleConfig.tabVal\": {\n      handler() {\n        this.count = this.defaults.styleConfig.count;\n        this.picArrayConcat(this.count);\n        this.configData.picList.splice(this.count);\n        this.currentIndex = 0;\n        let list = this.defaults.menuConfig.list[0];\n        if (this.configData.picList[0]) {\n          list.img = this.configData.picList[0].image;\n          list.info[0].value = this.configData.picList[0].link;\n        }\n        this.lis = document.getElementsByClassName(\"lay-item\");\n      },\n      deep: true,\n      immediate: true,\n    },\n    \"configObj.picStyle.docPicList\": {\n      handler() {\n        if (this.configObj.styleConfig.tabVal == 11) {\n          this.configObj.picStyle.docPicList.map((e, i) => {\n            this.configObj.picStyle.docPicList[i].img =\n              this.configObj.picStyle.picList[i].image;\n            this.configObj.picStyle.docPicList[i].link =\n              this.configObj.picStyle.picList[i].link;\n          });\n        }\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n  methods: {\n    currentTab(e, data) {\n      this.selPicBox = e;\n      this.currentIndex = e;\n      this.configData.tabVal = e;\n      if (this.defaults.menuConfig.isCube) {\n        if (this.configData.tabVal !== 11) {\n          let list = this.defaults.menuConfig.list[0];\n          if (data.picList[e] && data.picList[e].image) {\n            list.img = data.picList[e].image;\n            list.info[0].value = data.picList[e].link;\n          } else {\n            list.img = \"\";\n            list.info[0].value = \"\";\n          }\n        } else {\n          this.selPicBox = e;\n          let list = this.defaults.docPicList;\n          if (data.menuConfig.picStyle.picList[e].image) {\n            list[e].img = data.menuConfig.picStyle.picList[e].image;\n            list[e].info[0].value =\n              data.menuConfig.picStyle.docPicList[e].value;\n          } else {\n            list[0].img = \"\";\n            list[0].info[0].value = \"\";\n          }\n        }\n      }\n    },\n    picArrayConcat(count) {\n      for (let i = this.configData.picList.length; i < count; i++) {\n        this.configData.picList.push(this.arrayObj);\n      }\n    },\n    // 删除指定热区\n    delAreaBox(index) {\n      /* 删除某个热区 */\n      this.selBoxList.splice(index, 1);\n      this.configObj.picStyle.picList.splice(index, 1);\n      this.configObj.picStyle.picList.push({ image: \"\", link: \"\" });\n      if (this.selBoxList.length)\n        this.currentTab(this.selBoxList.length - 1, this.configData);\n      //   /* 删除后 重新编号 */\n      //   if (this.selBoxList) {\n      //     const arr = this.selBoxList.filter((i) => i.number > index);\n      //     if (!arr) return;\n      //     arr.forEach((i) => i.number--);\n      //     if (this.selBoxList[this.selBoxList.length - 1]) {\n      //       this.imgNum = this.selBoxList[this.selBoxList.length - 1].number + 1;\n      //     } else {\n      //       this.imgNum = 1;\n      //     }\n      //   }\n    },\n    initRect() {\n      if (this.rect) {\n        document.getElementById(\"lay1\").removeChild(this.rect);\n      }\n    },\n    //处理鼠标按下事件\n    clickBox(event) {\n      if (this.select) {\n        let boxData = this.up();\n        try {\n          if (\n            this.selBoxList.length &&\n            this.selBoxList.length == 1 &&\n            this.selBoxList[0].doc.w === 0\n          ) {\n            this.selBoxList[0].doc = boxData;\n          } else {\n            this.selBoxList.push({\n              img: \"\",\n              link: \"\",\n              doc: boxData,\n            });\n          }\n          this.currentTab(this.selBoxList.length - 1, this.configData);\n        } catch (error) {\n          console.log(error);\n        }\n\n        this.selPicBox = this.selBoxList.length\n          ? this.selBoxList.length - 1\n          : 0;\n        return;\n      }\n      // 鼠标按下时才允许处理鼠标的移动事件\n      this.select = true;\n      this.rect = document.createElement(\"div\");\n      // 框选div 样式\n      this.rect.style.cssText =\n        \"position:absolute;width:0px;height:0px;font-size:0px;margin:0px;padding:0px;border:1px dashed #0099FF;background-color:#C3D5ED;z-index:1000;filter:alpha(opacity:60);opacity:0.6;display:none;\";\n      this.rect.id = \"selectDiv\";\n      // 添加到lay1下\n      document.getElementById(\"lay1\").appendChild(this.rect);\n      // 取得鼠标按下时的坐标位置\n      this.downX = event.layerX;\n      this.downY = event.layerY;\n      this.rect.style.left = this.downX + \"px\";\n      this.rect.style.top = this.downY + \"px\";\n      //设置你要画的矩形框的起点位置\n      this.rect.style.left = this.downX + \"px\";\n      this.rect.style.top = this.downY + \"px\";\n    },\n\n    //鼠标抬起事件\n    up() {\n      let topList = [];\n      let leftList = [];\n      for (let i = 0; i < this.lis.length; i++) {\n        //将移动的div的四个点和和div元素的四个点进行比较\n        if (\n          //判断div元素 右边框的位置大于移动div的左起始点\n          this.rect.offsetLeft <\n            this.lis[i].offsetLeft + this.lis[i].offsetWidth &&\n          //判断div元素 下边框的位置大于移动div的上起始点\n          this.lis[i].offsetTop + this.lis[i].offsetHeight >\n            this.rect.offsetTop &&\n          // 判断div元素左边框的位置小于移动div的右起始点\n          this.rect.offsetLeft + this.rect.offsetWidth >\n            this.lis[i].offsetLeft &&\n          // 判断div元素上边框的位置小于移动div的下起始点\n          this.rect.offsetTop + this.rect.offsetHeight > this.lis[i].offsetTop\n        ) {\n          //将已选中的样式改变\n          if (this.lis[i].className.indexOf(\"seled\") == -1) {\n            topList.push(this.lis[i].offsetTop);\n            leftList.push(this.lis[i].offsetLeft);\n          }\n        } else {\n          //如果没有选中则清除样式\n          if (this.lis[i].className.indexOf(\"seled\") != -1) {\n            this.lis[i].className = \"lay-item\";\n          }\n        }\n        //鼠标抬起,就不允许在处理鼠标移动事件\n        this.select = false;\n      }\n\n      //隐藏图层\n      if (this.rect) {\n        document.getElementById(\"lay1\").removeChild(this.rect);\n      }\n\n      return {\n        startX: this.getMin(leftList),\n        startY: this.getMin(topList),\n        w: this.getMax(leftList) - this.getMin(leftList) + 93.75,\n        h: this.getMax(topList) - this.getMin(topList) + 93.75,\n      };\n    },\n    // 删除\n    del() {\n      this.$emit(\"delAreaBox\", this.areaDataIndex);\n    },\n    getMin(arr) {\n      let min = arr[0];\n      for (let i = 1; i < arr.length; i++) {\n        if (arr[i] < min) {\n          min = arr[i];\n        }\n      }\n      return min;\n    },\n    getMax(arr) {\n      let max = arr[0];\n      for (let i = 1; i < arr.length; i++) {\n        if (arr[i] > max) {\n          max = arr[i];\n        }\n      }\n      return max;\n    },\n    out() {\n      if (this.rect) {\n        this.select = false;\n        document.getElementById(\"lay1\").removeChild(this.rect);\n      }\n    },\n    //鼠标移动事件,最主要的事件\n    move(event) {\n      event.preventDefault();\n      if (!this.select) return;\n      /*\n            这个部分,根据你鼠标按下的位置,和你拉框时鼠标松开的位置关系,可以把区域分为四个部分,根据四个部分的不同,\n            我们可以分别来画框,否则的话,就只能向一个方向画框,也就是点的右下方画框.\n           \n            */\n      if (this.select) {\n        console.log(event.layerX, event.layerY, event);\n        window.requestAnimationFrame(() => {\n          // 取得鼠标移动时的坐标位置\n          this.mouseX2 = event.layerX - 5;\n          this.mouseY2 = event.layerY - 5;\n          // 显示框选元素\n          if (this.rect.style.display == \"none\") {\n            this.rect.style.display = \"\";\n          }\n          this.rect.style.left = Math.min(this.mouseX2, this.downX) + \"px\";\n          this.rect.style.top = Math.min(this.mouseY2, this.downY) + \"px\";\n          this.rect.style.width = this.mouseX2 - this.downX + \"px\";\n          this.rect.style.height = this.mouseY2 - this.downY + \"px\";\n          // // A part\n          // if (this.mouseX2 < this.downX && this.mouseY2 < this.downY) {\n          //   this.rect.style.left = this.mouseX2;\n          //   this.rect.style.top = this.mouseY2;\n          // }\n\n          // // B part\n          // if (this.mouseX2 > this.downX && this.mouseY2 < this.downY) {\n          //   this.rect.style.left = this.downX;\n          //   this.rect.style.top = this.mouseY2;\n          // }\n\n          // // C part\n          // if (this.mouseX2 < this.downX && this.mouseY2 > this.downY) {\n          //   this.rect.style.left = this.mouseX2;\n          //   this.rect.style.top = this.downY;\n          // }\n\n          // // D part\n          // if (this.mouseX2 > this.downX && this.mouseY2 > this.downY) {\n          //   this.rect.style.left = this.downX;\n          //   this.rect.style.top = this.downY;\n          // }\n        //   this.rect.style.left = this.downX;\n        //   this.rect.style.top = this.downY;\n        });\n      }\n\n      // 阻止事件上传\n      window.event.cancelBubble = true;\n      // 阻止默认事件\n      window.event.returnValue = false;\n    },\n  },\n};\n", null]}