{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_set_up.vue?vue&type=style&index=0&id=14d7b0c7&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_set_up.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\t.setUpTop{\n\t\theight: 6px;\n\t\tbackground: rgb(240, 242, 245);\n\t}\n\t.setUp{\n\t\tdisplay flex;\n\t\tjustify-content space-between;\n\t\talign-items center;\n\t\theight: 56px;\n\t\tpadding: 0 15px;\n\t\t\n\t\t.label{\n\t\t\tfont-size: 16px;\n\t\t\tcolor: #333333;\n\t\t}\n\t\t\n\t\t.title{\n\t\t\twidth: 140px;\n\t\t\theight: 28px;\n\t\t\tbackground: #F9F9F9;\n\t\t\tborder-radius: 14px;\n\t\t\tline-height: 28px;\n\t\t\tfont-size: 12px;\n\t\t\tcolor: #333333;\n\t\t\t.item{\n\t\t\t\twidth: 50%;\n\t\t\t\ttext-align: center;\n\t\t\t\tcursor: pointer;\n\t\t\t\t&.on{\n\t\t\t\t\twidth: 70px;\n\t\t\t\t\tbackground: #1890FF;\n\t\t\t\t\tborder-radius: 14px;\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n    .setUp /deep/.ivu-tabs-nav-scroll{\n        padding 0 30px;\n    }\n    .setUp /deep/.ivu-tabs-nav .ivu-tabs-tab{\n        padding: 8px 45px;\n    }\n", null]}