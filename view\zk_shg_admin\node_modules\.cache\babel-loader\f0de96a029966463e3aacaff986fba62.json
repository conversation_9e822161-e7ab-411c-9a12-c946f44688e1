{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\piecesDiscount\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\piecesDiscount\\index.vue", "mtime": 1684232862000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport { discountList, discountsetStatus } from '@/api/marketing';\nimport Setting from \"@/setting\";\nexport default {\n  name: \"piecesDiscount\",\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns1: [{\n        title: 'ID',\n        key: 'id',\n        width: 80\n      }, {\n        title: '活动名称',\n        key: 'name',\n        minWidth: 100\n      }, {\n        title: '参与商品数',\n        key: 'product_count',\n        minWidth: 100\n      }, {\n        title: '活动类型',\n        slot: 'n_piece_n_discount',\n        minWidth: 100\n      }, {\n        title: '支付订单',\n        key: 'sum_order',\n        minWidth: 100\n      }, {\n        title: '参与客户',\n        key: 'sum_user',\n        minWidth: 100\n      }, {\n        title: '实付金额',\n        key: 'sum_pay_price',\n        minWidth: 100\n      }, {\n        title: '是否开启',\n        slot: 'status',\n        width: 120\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        minWidth: 120\n      }],\n      discountFrom: {\n        page: 1,\n        limit: 15,\n        name: '',\n        status: '',\n        n_piece_n_discount: ''\n      },\n      list: [],\n      total: 0\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    // 删除\n    del: function del(row, tit, num) {\n      var _this = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"marketing/promotions/del/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this.$Message.success(res.msg);\n\n        _this.list.splice(num, 1);\n\n        if (!_this.list.length) {\n          _this.discountFrom.page = _this.discountFrom.page == 1 ? 1 : _this.discountFrom.page - 1;\n        }\n\n        _this.getList();\n      }).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this2 = this;\n\n      discountsetStatus(row.id, row.status).then(function (res) {\n        _this2.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    // 添加\n    add: function add() {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/discount/add_pieces/\" + 0\n      });\n    },\n    discountSearchs: function discountSearchs() {\n      this.discountFrom.page = 1;\n      this.list = [];\n      this.getList();\n    },\n    // 单位列表\n    getList: function getList() {\n      var _this3 = this;\n\n      this.loading = true;\n      discountList(2, this.discountFrom).then(function (res) {\n        var data = res.data;\n        _this3.list = data.list;\n        _this3.total = data.count;\n        _this3.loading = false;\n      }).catch(function (err) {\n        _this3.loading = false;\n\n        _this3.$Message.error(err.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.discountFrom.page = index;\n      this.getList();\n    },\n    //修改\n    edit: function edit(id) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/discount/add_pieces/\" + id\n      });\n    }\n  }\n};", null]}