{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue?vue&type=template&id=c4aa0076&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue", "mtime": 1717551943000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"c_product\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.configData.title))]),_c('div',{staticClass:\"list-box\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.configData.list,\"group\":\"peoples\",\"handle\":\".move-icon\"}},_vm._l((_vm.configData.list),function(item,index){return _c('div',{key:index,staticClass:\"item\",on:{\"click\":function($event){return _vm.activeBtn(index)}},model:{value:(_vm.configData.tabCur),callback:function ($$v) {_vm.$set(_vm.configData, \"tabCur\", $$v)},expression:\"configData.tabCur\"}},[_c('div',{staticClass:\"acea-row\"},[_c('div',{staticClass:\"move-icon\"},[_c('span',{staticClass:\"iconfont-diy iconxingzhuangjiehe\"})]),_c('div',{staticClass:\"content\"},_vm._l((item.chiild),function(list,key){return (key<(_vm.tabIndex == 0?2:1))?_c('div',{key:key,staticClass:\"con-item\"},[_c('span',[_vm._v(_vm._s(list.title))]),_c('div',{staticStyle:{\"width\":\"100%\"}},[_c('Input',{attrs:{\"placeholder\":list.pla,\"maxlength\":list.max},model:{value:(list.val),callback:function ($$v) {_vm.$set(list, \"val\", $$v)},expression:\"list.val\"}})],1)]):_vm._e()}),0)]),(_vm.configData.tabCur == index)?_c('div',{staticClass:\"acea-row row-right\"},[_c('div',{staticClass:\"conter\"},[(_vm.tabIndex == 4)?_c('div',{staticClass:\"c_row-item\"},[_c('div',{staticClass:\"c_label\"},[_vm._v(\"\\n\\t\\t\\t\\t\\t\\t\\t\\t    上传图片\\n\\t\\t\\t\\t\\t\\t\\t\\t\")]),_c('div',{staticClass:\"color-box\"},[_c('div',{staticClass:\"box\",on:{\"click\":function($event){return _vm.modalPicTap('单选')}}},[(item.image)?_c('div',{staticClass:\"pictrue acea-row row-center-wrapper\"},[_c('img',{attrs:{\"src\":item.image,\"alt\":\"\"}}),_c('div',{staticClass:\"iconfont icondel_1\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindPicDelete($event)}}})]):_c('div',{staticClass:\"upload-box\"},[_c('Icon',{attrs:{\"type\":\"ios-add\",\"size\":\"40\"}})],1)])])]):_vm._e(),_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"c_label\"},[_vm._v(\"\\n\\t\\t\\t\\t\\t\\t\\t        选择方式\\n\\t\\t\\t\\t\\t\\t\\t    \")]),_c('Col',{staticClass:\"color-box\"},[_c('Select',{on:{\"on-change\":_vm.tabChange},model:{value:(item.tabVal),callback:function ($$v) {_vm.$set(item, \"tabVal\", $$v)},expression:\"item.tabVal\"}},_vm._l((_vm.typeList),function(itemn,indexn){return _c('Option',{key:indexn,attrs:{\"value\":itemn.activeValue}},[_vm._v(_vm._s(itemn.title))])}),1)],1)],1),(item.tabVal == 1)?_c('div',{staticClass:\"goods-box acea-row\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"选择商品\")]),_c('div',{staticClass:\"list\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":item.goodsList.list,\"group\":\"peoples\"}},[_vm._l((item.goodsList.list),function(goods,gIndex){return (item.goodsList.list.length)?_c('div',{key:gIndex,staticClass:\"items\"},[_c('img',{attrs:{\"src\":goods.image,\"alt\":\"\"}}),_c('span',{staticClass:\"iconfont-diy icondel_1\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindGoodDelete(gIndex)}}})]):_vm._e()}),_c('div',{staticClass:\"add-item items\",on:{\"click\":function($event){return _vm.openGoods(index)}}},[_c('span',{staticClass:\"iconfont-diy iconaddto\"})])],2)],1)]):_c('div',[(item.tabVal == 2)?_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"品牌名称\")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"19\"}},[_c('el-cascader',{attrs:{\"placeholder\":\"请选择品牌\",\"size\":\"mini\",\"options\":_vm.brandData,\"props\":_vm.props,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.brandChange},model:{value:(item.brandConfig.brandVal),callback:function ($$v) {_vm.$set(item.brandConfig, \"brandVal\", $$v)},expression:\"item.brandConfig.brandVal\"}})],1)],1):(item.tabVal == 3)?_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"商品分类\")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"19\"}},[_c('el-cascader',{attrs:{\"placeholder\":\"请选择分类\",\"size\":\"mini\",\"options\":_vm.treeSelect,\"props\":_vm.props,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.sliderChange},model:{value:(item.selectConfig.activeValue),callback:function ($$v) {_vm.$set(item.selectConfig, \"activeValue\", $$v)},expression:\"item.selectConfig.activeValue\"}})],1)],1):_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"商品标签\")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"19\"}},[_c('div',{staticClass:\"labelInput acea-row row-between-wrapper\",on:{\"click\":function($event){return _vm.openStoreLabel(item.goodsLabel.list,index)}}},[_c('div',{staticStyle:{\"width\":\"90%\"}},[(item.goodsLabel.list.length)?_c('div',_vm._l((item.goodsLabel.list),function(j,jindex){return _c('Tag',{key:jindex,attrs:{\"closable\":\"\"},on:{\"on-close\":function($event){return _vm.closeStoreLabel(j)}}},[_vm._v(_vm._s(j.label_name))])}),1):_c('span',{staticClass:\"span\"},[_vm._v(\"选择商品标签\")])]),_c('div',{staticClass:\"iconfont iconxiayi\"})])])],1),_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_c('span',[_vm._v(\"商品数量\")])]),_c('Col',{staticClass:\"slider-box on\",attrs:{\"span\":\"19\"}},[_c('Slider',{attrs:{\"show-input\":\"\",\"max\":100,\"min\":1},on:{\"on-change\":function($event){return _vm.radioChange($event)}},model:{value:(item.numConfig.val),callback:function ($$v) {_vm.$set(item.numConfig, \"val\", $$v)},expression:\"item.numConfig.val\"}})],1)],1),_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"c_label\"},[_vm._v(\"\\n\\t\\t\\t\\t\\t\\t\\t\\t        商品排序\\n\\t\\t\\t\\t\\t\\t\\t\\t    \")]),_c('Col',{staticClass:\"color-box\"},[_c('RadioGroup',{on:{\"on-change\":function($event){return _vm.radioChange($event)}},model:{value:(item.goodsSort),callback:function ($$v) {_vm.$set(item, \"goodsSort\", $$v)},expression:\"item.goodsSort\"}},[_c('Radio',{attrs:{\"label\":0}},[_c('span',[_vm._v(\"综合\")])]),_c('Radio',{attrs:{\"label\":1}},[_c('span',[_vm._v(\"销量\")])]),_c('Radio',{attrs:{\"label\":2}},[_c('span',[_vm._v(\"价格\")])])],1)],1)],1)])])]):_vm._e(),_c('div',{staticClass:\"delete\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete(index)}}},[_c('Icon',{attrs:{\"type\":\"ios-close-circle\",\"size\":\"26\"}})],1)])}),0)],1),(_vm.configData.list)?_c('div',[_c('div',{staticClass:\"add-btn\",on:{\"click\":_vm.addHotTxt}},[_c('Button',{staticStyle:{\"width\":\"100%\",\"height\":\"40px\"}},[_vm._v(\"+ 添加\")])],1)]):_vm._e(),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"选择商品标签\",\"closable\":true,\"width\":\"540\",\"footer-hide\":true,\"mask-closable\":false},model:{value:(_vm.storeLabelShow),callback:function ($$v) {_vm.storeLabelShow=$$v},expression:\"storeLabelShow\"}},[_c('storeLabelList',{ref:\"storeLabel\",on:{\"activeData\":_vm.activeStoreData,\"close\":_vm.storeLabelClose}})],1),_c('Modal',{staticClass:\"paymentFooter\",attrs:{\"title\":\"商品列表\",\"footerHide\":\"\",\"scrollable\":\"\",\"width\":\"900\"},on:{\"on-cancel\":_vm.cancel},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[(_vm.modals)?_c('goods-list',{ref:\"goodslist\",attrs:{\"ischeckbox\":true,\"isdiy\":true},on:{\"getProductId\":_vm.getProductId}}):_vm._e()],1),_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":_vm.configData.header?_vm.configData.header:'上传图片',\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}