{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue?vue&type=style&index=0&id=1cf002c8&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue", "mtime": 1716618646000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.onePictrue {\n  padding: 0 15px;\n\n  .info {\n    font-size: 12px;\n    color: #BBBBBB;\n  }\n\n  .bnt {\n    width: 100%;\n    height: 36px;\n    border-radius: 3px;\n    opacity: 1;\n    border: 1px solid #EEEEEE;\n    color: #666666;\n    font-size: 12px;\n    text-align: center;\n    line-height: 36px;\n    margin-top: 20px;\n    cursor: pointer;\n  }\n\n  .pictrues {\n    width: 370px;\n    height: 100%;\n    margin-top: 20px;\n\n    img {\n      width: 100%;\n      height: 100%;\n    }\n\n    .emptyBox {\n      height: 164px;\n      background: #F9F9F9;\n      border-radius: 3px 3px 3px 3px;\n      font-size: 12px;\n      color: #BBBBBB;\n      text-align: center;\n      line-height: 164px;\n    }\n  }\n\n  .uploadImg {\n    display: flex;\n    align-items: center;\n    height: 96px;\n    background: #F9F9F9;\n    border-radius: 3px;\n    width: 100%;\n    margin-top: 20px;\n    padding: 0 20px;\n\n    .name {\n      font-size: 12px;\n      color: #999999;\n      margin-right: 16px;\n    }\n\n    .picTxt {\n      display: flex;\n      align-items: center;\n\n      .box {\n        width: 64px;\n        height: 64px;\n        position: relative;\n        background: url('../../assets/images/transparents.jpg') no-repeat;\n        background-size: 100% 100%;\n        border-radius: 3px;\n\n        .upload-box {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 64px;\n          height: 64px;\n          background: #fff;\n          border-radius: 4px;\n          border: 1px solid #EEEEEE;\n\n          .ivu-icon {\n            color: #ccc;\n          }\n        }\n\n        .pictrue {\n          position: relative;\n          width: 100%;\n          height: 100%;\n\n          .iconfont {\n            position: absolute;\n            right: -12px;\n            top: -19px;\n            font-size: 24px;\n            color: #CCCCCC;\n          }\n        }\n\n        img {\n          width: 64px;\n          border-radius: 3px;\n\t\t  max-height: 64px;\n\t\t  object-fit: cover;\n        }\n      }\n\n      .tip {\n        color: #BBBBBB;\n        font-size: 12px;\n        margin-left: 20px;\n      }\n    }\n  }\n}\n", null]}