{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\bargainList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\bargainList.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// import cardsData from '@/components/cards/cards';\nimport { mapState } from \"vuex\";\nimport { formatDate as _formatDate } from \"@/utils/validate\";\nimport { bargainUserListApi, bargainUserInfoApi } from \"@/api/marketing\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: \"bargainList\",\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    }\n  },\n  // components: { cardsData },\n  data: function data() {\n    return {\n      cardLists: [],\n      modals: false,\n      options: timeOptions,\n      fromList: {\n        title: \"选择时间\",\n        custom: true,\n        fromTxt: [{\n          text: \"全部\",\n          val: \"\"\n        }, {\n          text: \"今天\",\n          val: \"today\"\n        }, {\n          text: \"昨天\",\n          val: \"yesterday\"\n        }, {\n          text: \"最近7天\",\n          val: \"lately7\"\n        }, {\n          text: \"最近30天\",\n          val: \"lately30\"\n        }, {\n          text: \"本月\",\n          val: \"month\"\n        }, {\n          text: \"本年\",\n          val: \"year\"\n        }]\n      },\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 12,\n        xs: 24\n      },\n      loading: false,\n      formValidate: {\n        status: \"\",\n        data: \"\",\n        page: 1,\n        limit: 15\n      },\n      columns1: [{\n        title: \"头像\",\n        slot: \"avatar\",\n        minWidth: 100\n      }, {\n        title: \"发起用户\",\n        slot: \"nickname\",\n        minWidth: 170\n      }, {\n        title: \"开启时间\",\n        key: \"add_time\",\n        minWidth: 150\n      }, {\n        title: \"砍价商品\",\n        key: \"title\",\n        minWidth: 300\n      }, {\n        title: \"最低价\",\n        key: \"bargain_price_min\",\n        minWidth: 120\n      }, {\n        title: \"当前价\",\n        key: \"now_price\",\n        minWidth: 100\n      }, {\n        title: \"总砍价次数\",\n        key: \"people_num\",\n        minWidth: 100\n      }, {\n        title: \"剩余砍价次数\",\n        key: \"num\",\n        minWidth: 100\n      }, {\n        title: \"结束时间\",\n        key: \"datatime\",\n        minWidth: 150\n      }, {\n        title: \"状态\",\n        slot: \"status\",\n        minWidth: 100\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        fixed: \"right\",\n        minWidth: 170\n      }],\n      tableList: [],\n      total: 0,\n      timeVal: [],\n      loading2: false,\n      tabList3: [],\n      columns2: [{\n        title: \"用户ID\",\n        key: \"uid\",\n        width: 80\n      }, {\n        title: \"用户头像\",\n        slot: \"avatar\"\n      }, {\n        title: \"用户名称\",\n        slot: \"nickname\",\n        minWidth: 150\n      }, {\n        title: \"砍价金额\",\n        key: \"price\"\n      }, {\n        title: \"砍价时间\",\n        key: \"add_time\"\n      }],\n      rows: {}\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    // 查看详情\n    Info: function Info(row) {\n      var _this = this;\n\n      this.modals = true;\n      this.rows = row;\n      bargainUserInfoApi(row.id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this.tabList3 = data.list; // this.total = res.data.count;\n\n                  _this.loading = false;\n\n                case 3:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.loading = false;\n\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\"; // this.formValidate.data = this.timeVal.join('-');\n\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.formValidate.page = 1;\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      this.formValidate.status = this.formValidate.status || \"\";\n      bargainUserListApi(this.formValidate).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  data = res.data;\n                  _this2.tableList = data.list;\n                  _this2.total = res.data.count;\n                  _this2.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    }\n  }\n};", null]}