{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\index.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { userMemberBatch, memberBatchSave, memberBatchSetValue, exportMemberCard, userMemberScan } from \"@/api/user\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"index\",\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      columns: [{\n        title: \"编号\",\n        key: \"id\"\n      }, {\n        title: \"批次名称\",\n        key: \"title\"\n      }, {\n        title: \"体验天数\",\n        key: \"use_day\"\n      }, {\n        title: \"发卡总数量\",\n        key: \"total_num\"\n      }, {\n        title: \"使用数量\",\n        key: \"use_num\"\n      }, {\n        title: \"制卡时间\",\n        key: \"add_time\"\n      }, {\n        title: \"是否激活\",\n        slot: \"status\"\n      }, {\n        title: \"备注\",\n        key: \"remark\"\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        fixed: \"right\"\n      }],\n      tbody: [],\n      total: 0,\n      gradeFrom: {\n        title: \"\",\n        page: 1,\n        limit: 15\n      },\n      loading: false,\n      modal: false,\n      rule: [{\n        type: \"input\",\n        field: \"title\",\n        title: \"批次名称\",\n        validate: [{\n          required: true,\n          message: \"请输入批次名称\",\n          trigger: \"blur\"\n        }]\n      }, {\n        type: \"InputNumber\",\n        field: \"total_num\",\n        title: \"制卡数量\",\n        value: 1,\n        props: {\n          min: 1\n        }\n      }, {\n        type: \"InputNumber\",\n        field: \"use_day\",\n        title: \"体验天数\",\n        value: 1,\n        props: {\n          min: 1\n        }\n      }, {\n        type: \"radio\",\n        field: \"status\",\n        title: \"是否激活\",\n        value: \"0\",\n        options: [{\n          value: \"0\",\n          label: \"冻结\"\n        }, {\n          value: \"1\",\n          label: \"激活\"\n        }]\n      }, {\n        type: \"input\",\n        field: \"remark\",\n        title: \"备注\",\n        props: {\n          type: \"textarea\"\n        }\n      }],\n      modal2: false,\n      rule2: [{\n        type: \"hidden\",\n        field: \"id\",\n        value: \"\"\n      }, {\n        type: \"input\",\n        field: \"title\",\n        title: \"批次名称\",\n        value: \"\",\n        validate: [{\n          required: true,\n          message: \"请输入批次名称\",\n          trigger: \"blur\"\n        }]\n      }],\n      modal3: false,\n      qrcode: null,\n      fapi: {}\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 75;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getMemberBatch(this.gradeFrom);\n  },\n  methods: {\n    // 批次列表\n    getMemberBatch: function getMemberBatch() {\n      var _this = this;\n\n      this.loading = true;\n      userMemberBatch(this.gradeFrom).then(function (res) {\n        _this.loading = false;\n        _this.tbody = res.data.list;\n        _this.total = res.data.count;\n      }).catch(function (err) {\n        _this.loading = false;\n\n        _this.$Message.error(err.msg);\n      });\n    },\n    // 批次名称查询\n    userSearchs: function userSearchs() {\n      this.gradeFrom.page = 1;\n      this.getMemberBatch();\n    },\n    // 激活 | 冻结\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this2 = this;\n\n      memberBatchSetValue(row.id, {\n        field: \"status\",\n        value: row.status\n      }).then(function (res) {\n        _this2.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    // 查看\n    // exportExcel(row) {\n    //     this.$Spin.show();\n    //     exportMemberCard(row.id)\n    //         .then(res => {\n    //             this.$Spin.hide();\n    //             location.href = res.data[0];\n    //         })\n    //         .catch(err => {\n    //             this.$Spin.hide();\n    //             this.$Message.error(err.msg);\n    //         });\n    // },\n    // 数据导出；\n    exports: function () {\n      var _exports = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee(row) {\n        var th, filekey, data, fileName, excelData, i, lebData;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                th = [], filekey = [], data = [];\n                fileName = \"\";\n                excelData = JSON.parse(JSON.stringify(this.gradeFrom));\n                excelData.page = 1;\n                i = 0;\n\n              case 5:\n                if (!(i < excelData.page + 1)) {\n                  _context.next = 22;\n                  break;\n                }\n\n                _context.next = 8;\n                return this.getExcelData(row.id, excelData);\n\n              case 8:\n                lebData = _context.sent;\n                if (!fileName) fileName = lebData.filename;\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) th = lebData.header;\n\n                if (!lebData.export.length) {\n                  _context.next = 17;\n                  break;\n                }\n\n                data = data.concat(lebData.export);\n                excelData.page++;\n                _context.next = 19;\n                break;\n\n              case 17:\n                exportExcel(th, filekey, fileName, data);\n                return _context.abrupt(\"return\");\n\n              case 19:\n                i++;\n                _context.next = 5;\n                break;\n\n              case 22:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function exports(_x) {\n        return _exports.apply(this, arguments);\n      }\n\n      return exports;\n    }(),\n    getExcelData: function getExcelData(id, excelData) {\n      return new Promise(function (resolve, reject) {\n        exportMemberCard(id, excelData).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 更多\n    changeMenu: function changeMenu(row, name) {\n      switch (name) {\n        case \"1\":\n          this.rule2[0].value = row.id;\n          this.rule2[1].value = row.title;\n          this.modal2 = true;\n          break;\n\n        case \"2\":\n          this.$router.push({\n            path: \"\".concat(this.roterPre, \"/vipuser/grade/list/\").concat(row.id)\n          });\n          break;\n\n        case \"3\":\n          this.exports(row);\n          break;\n      }\n    },\n    // 分页\n    pageChange: function pageChange(index) {\n      this.gradeFrom.page = index;\n      this.getMemberBatch();\n    },\n    // 添加批次弹窗\n    addBatch: function addBatch() {\n      this.fapi.resetFields();\n      this.modal = true;\n    },\n    // 提交批次\n    onSubmit: function onSubmit(formData) {\n      var _this3 = this;\n\n      memberBatchSave(0, formData).then(function (res) {\n        _this3.modal = false;\n\n        _this3.$Message.success(res.msg);\n\n        _this3.getMemberBatch();\n\n        _this3.fapi.resetFields();\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    onSubmit2: function onSubmit2(formData) {\n      var _this4 = this;\n\n      memberBatchSetValue(formData.id, {\n        field: \"title\",\n        value: formData.title\n      }).then(function (res) {\n        _this4.modal2 = false;\n\n        _this4.$Message.success(res.msg);\n\n        _this4.getMemberBatch();\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    },\n    // 会员卡二维码\n    getMemberScan: function getMemberScan() {\n      var _this5 = this;\n\n      this.$Spin.show();\n      userMemberScan().then(function (res) {\n        _this5.$Spin.hide();\n\n        _this5.qrcode = res.data;\n        _this5.modal3 = true;\n      }).catch(function (err) {\n        _this5.$Spin.hide();\n\n        _this5.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}