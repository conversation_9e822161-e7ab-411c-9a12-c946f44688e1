{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\lottery\\addGoods.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\lottery\\addGoods.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport couponList from \"@/components/couponList\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport goodsList from \"@/components/goodsList/index\";\nimport freightTemplate from \"@/components/freightTemplate\";\nimport { changeListApi } from \"@/api/product\";\nexport default {\n  components: { uploadPictures, goodsList, freightTemplate, couponList },\n  data() {\n    return {\n      modalPic: false,\n      modals: false,\n      isChoice: \"单选\",\n      updateIds: [],\n      updateName: [],\n      goodsData: {\n        pic: \"\",\n        product_id: \"\",\n        img: \"\",\n        coverImg: \"\",\n      },\n      formValidate: {\n        type: 5, //类型 1：未中奖2：积分  3:余额  4：红包 5:优惠券 6：站内商品\n        name: \"\", //活动名称\n        num: 0, //奖品数量\n        image: \"\", //奖品图片\n        chance: 0, //中奖权重\n        product_id: 0, //商品id\n        coupon_id: 0, //优惠券id\n        total: 0, //奖品数量\n        prompt: \"\", //提示语\n        goods_image: \"\", //自用商品图\n        unique: \"\", //商品规格\n        coupon_title:'' //优惠券名称\n      },\n      ruleValidate: {\n        name: [\n          {\n            required: true,\n            message: \"奖品名称\",\n            trigger: \"blur\",\n          },\n        ],\n        goods_image: [\n          {\n            required: true,\n            message: \"请添加商品\",\n            trigger: \"blur\",\n          },\n        ],\n        // attr_image: [\n        //   {\n        //     required: true,\n        //     message: \"请添加商品规格\",\n        //     trigger: \"blur\",\n        //   },\n        // ],\n        num: [\n          {\n            required: true,\n            type: \"number\",\n            message: \"请输入金额数量\",\n            trigger: \"blur\",\n          },\n        ],\n        chance: [\n          {\n            required: true,\n            type: \"number\",\n            message: \"请输入奖品权重\",\n            trigger: \"blur\",\n          },\n        ],\n        image: [\n          {\n            required: true,\n            message: \"请选择奖品图片\",\n            trigger: \"blur\",\n          },\n        ],\n        prompt: [\n          {\n            required: true,\n            message: \"请输入提示语\",\n            trigger: \"blur\",\n          },\n        ],\n      },\n      couponName: [],\n      attrColumns: [\n        {\n          width: 60,\n          align: 'center',\n          render: (h, { row }) => {\n            return h('Radio', {\n              props: {\n                value: row.unique === this.formValidate.unique\n              },\n              on: {\n                'on-change': () => {\n                  this.attrModal = false;\n                  this.attrImage = row.image;\n                  this.formValidate.unique = row.unique;\n                }\n              }\n            });\n          }\n        },\n        {\n          title: \"图片\",\n          slot: \"image\",\n          width: 120,\n          align: \"center\",\n        },\n        {\n          title: \"规格\",\n          key: \"suk\",\n          align: \"center\",\n          minWidth: 120,\n        },\n      ],\n      attrData: [],\n      attrModal: false,\n      attrImage: \"\", //自用商品规格图\n    };\n  },\n  props: {\n    editData: {\n      type: Object,\n      default: () => {},\n    },\n  },\n  watch: {\n    editData(data) {\n    },\n  },\n  mounted() {\n    let keys = Object.keys(this.editData);\n    keys.forEach((item) => {\n      this.formValidate[item] = this.editData[item];\n      if( item === 'coupon_title' && this.editData[item]){\n        this.couponName.push({title:this.editData[item],id:this.editData.coupon_id})\n      }\n    });\n    // this.getList();\n  },\n  methods: {\n    getCouponId(e){\n      this.formValidate.coupon_id = e.id;\n      this.formValidate.coupon_title = e.coupon_title;\n      let couponName = []\n      couponName.push(e)\n      this.couponName = couponName\n    },\n    handleSubmit(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.$emit(\"addGoodsData\", this.formValidate);\n          this.$Message.success(\"添加成功\");\n        } else {\n          this.$Message.warning(\"请完善数据\");\n        }\n      });\n    },\n    // 获取单张图片信息\n    getPic(pc) {\n      this.formValidate.image = pc.att_dir;\n      this.modalPic = false;\n    },\n    // 点击商品图\n    modalPicTap() {\n      this.modalPic = true;\n    },\n    cancel() {\n      this.modals = false;\n    },\n    // 选择的商品\n    getProductId(productList) {\n      // if (productList.length > 1) {\n      //   this.$Message.warning(\"最多添加一个商品\");\n      //   return;\n      // }\n      this.formValidate.product_id = productList.id;\n      this.formValidate.goods_image = productList.image;\n      this.modals = false;\n      // productList.forEach((value) => {\n      //   this.formValidate.product_id = value.product_id;\n      //   this.formValidate.goods_image = value.image;\n      // });\n      this.attrData = productList.attrValue;\n    },\n    removeGoods() {\n      this.formValidate.product_id = \"\";\n      this.formValidate.goods_image = \"\";\n      this.removeAttr();\n    },\n    remove() {\n      this.formValidate.image = \"\";\n    },\n    // 添加优惠券\n    addCoupon() {\n      this.$refs.couponTemplates.isTemplate = true;\n      this.$refs.couponTemplates.tableList();\n    },\n    handleClose(name) {\n      this.couponName.splice(0, 1);\n      this.formValidate.coupon_id = 0;\n      // let index = this.couponName.indexOf(name);\n      // this.couponName.splice(index, 1);\n      //\n      // let couponIds = this.formValidate.coupon_id;\n      // couponIds.splice(index, 1);\n      // this.updateIds = couponIds;\n      // this.updateName = this.couponName;\n    },\n    // nameId(id, names) {\n    //   this.formValidate.coupon_id = id[0];\n    //   this.couponName = this.unique(names);\n    // },\n    //对象数组去重；\n    unique(arr) {\n      const res = new Map();\n      return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1));\n    },\n    removeAttr() {\n      this.attrImage = '';\n      this.formValidate.unique = '';\n    },\n    callAttr() {\n      this.attrModal = true;\n    },\n    getList() {\n      changeListApi().then(({ data }) => {\n        const { list } = data;\n        for (let i = 0; i < list.length; i++) {\n          if (list[i].id === this.formValidate.product_id) {\n            this.attrData = list[i].attrValue;\n            for (let j = 0; j < this.attrData.length; j++) {\n              if (this.attrData[j].unique === this.formValidate.unique) {\n                this.attrImage = this.attrData[j].image;\n                break;\n              }\n            }\n            break;\n          }\n        }\n      });\n    }\n  },\n};\n", null]}