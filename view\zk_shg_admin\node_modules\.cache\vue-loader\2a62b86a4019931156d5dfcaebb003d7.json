{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue", "mtime": 1677832908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport { ruleAddApi, ruleInfoApi } from \"@/api/product\";\nexport default {\n  name: \"addAttr\",\n  data() {\n    return {\n      spinShow: false,\n      modal_loading: false,\n      grid: {\n        xl: 3,\n        lg: 3,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      modal: false,\n      index: 1,\n      rules: {\n        rule_name: [\n          { required: true, message: \"请输入分类名称\", trigger: \"blur\" },\n        ],\n      },\n      formDynamic: {\n        rule_name: \"\",\n        spec: [],\n      },\n      attrsName: \"\",\n      attrsVal: \"\",\n      formDynamicNameData: [],\n      isBtn: false,\n      formDynamicName: [],\n      results: [],\n      result: [],\n      ids: 0,\n      title: \"添加商品规格\",\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 110;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  methods: {\n    onCancel() {\n      this.clear();\n    },\n    // 添加按钮\n    addBtn() {\n\t\t\tif(this.formDynamic.rule_name.trim() == ''){\n\t\t\t\treturn this.$Message.error('请输入分类名称');\n\t\t\t}\n      this.isBtn = true;\n    },\n    // 详情\n    getIofo(row) {\n      this.spinShow = true;\n      this.ids = row.id;\n      this.title = \"编辑商品规格\";\n      ruleInfoApi(row.id)\n        .then((res) => {\n          this.formDynamic = res.data.info;\n          this.spinShow = false;\n        })\n        .catch((res) => {\n          this.spinShow = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 提交\n    handleSubmit(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if (this.formDynamic.spec.length === 0) {\n            return this.$Message.warning(\"请至少添加一条商品规格！\");\n          }\n          this.modal_loading = true;\n          setTimeout(() => {\n            ruleAddApi(this.formDynamic, this.ids)\n              .then((res) => {\n                this.$Message.success(res.msg);\n                setTimeout(() => {\n                  this.modal = false;\n                  this.modal_loading = false;\n                }, 500);\n                setTimeout(() => {\n                  this.$emit(\"getList\");\n                  this.clear();\n                }, 600);\n              })\n              .catch((res) => {\n                this.modal_loading = false;\n                this.$Message.error(res.msg);\n              });\n          }, 1200);\n        } else {\n          return false;\n        }\n      });\n    },\n    clear() {\n      this.$refs[\"formDynamic\"].resetFields();\n      this.formDynamic.spec = [];\n      this.isBtn = false;\n      this.ids = 0;\n      this.title = \"添加商品规格\";\n      this.attrsName = \"\";\n      this.attrsVal = \"\";\n    },\n    // 取消\n    offAttrName() {\n      this.isBtn = false;\n    },\n    cancle() {\n      this.modal = false;\n      this.clear()\n    },\n    // 删除\n    handleRemove(index) {\n      this.formDynamic.spec.splice(index, 1);\n    },\n    // 删除属性\n    handleRemove2(item, index) {\n      item.splice(index, 1);\n    },\n    // 添加规则名称\n    createAttrName() {\n      if (this.attrsName && this.attrsVal) {\n        let data = {\n          value: this.attrsName,\n          detail: [this.attrsVal],\n        };\n        this.formDynamic.spec.push(data);\n        var hash = {};\n        this.formDynamic.spec = this.formDynamic.spec.reduce(function(\n          item,\n          next\n        ) {\n          /* eslint-disable */\n          hash[next.value] ? \"\" : (hash[next.value] = true && item.push(next));\n          return item;\n        },\n        []);\n        this.attrsName = \"\";\n        this.attrsVal = \"\";\n        this.isBtn = false;\n      } else {\n        this.$Message.warning(\"请添加规格名称或规格值\");\n      }\n    },\n    // 添加属性\n    createAttr(num, idx) {\n      if (num) {\n        this.formDynamic.spec[idx].detail.push(num);\n        var hash = {};\n        this.formDynamic.spec[idx].detail = this.formDynamic.spec[\n          idx\n        ].detail.reduce(function(item, next) {\n          /* eslint-disable */\n          hash[next] ? \"\" : (hash[next] = true && item.push(next));\n          return item;\n        }, []);\n      } else {\n        this.$Message.warning(\"请添加属性\");\n      }\n    },\n  },\n};\n", null]}