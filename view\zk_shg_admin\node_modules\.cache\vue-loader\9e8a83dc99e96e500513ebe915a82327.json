{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_fillet.vue?vue&type=template&id=54ca097e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_fillet.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"fillets\" v-if=\"configData\">\n    <div class=\"c_row-item\">\n        <Col class=\"c_label\">\n            {{configData.title}}\n            <span>{{configData.list[configData.type].val}}</span>\n        </Col>\n        <Col class=\"color-box\">\n            <RadioGroup v-model=\"configData.type\" type=\"button\" @on-change=\"radioChange($event)\">\n                <Radio :label=\"key\" v-for=\"(radio,key) in configData.list\" :key=\"key\">\n                    <span class=\"iconfont-diy\" :class=\"radio.icon\" v-if=\"radio.icon\"></span>\n                    <span v-else>{{radio.val}}</span>\n                </Radio>\n            </RadioGroup>\n        </Col>\n    </div>\n\t<div class=\"c_row-item on\" v-if=\"configData.type\">\n\t\t<div class=\"c_label\">{{configData.valName}}</div>\n\t\t<div class=\"right\">\n\t\t\t<Input class=\"input\" :class=\"index>1?'':'on'\" v-model=\"configData.valList[index].val\" v-for=\"(item,index) in configData.valList\" :key=\"index\">\n\t\t\t\t<template #prefix>\n\t\t\t\t\t<span class=\"iconfont iconzuoshangjiao\" v-if=\"index == 0\"></span>\n\t\t\t\t\t<span class=\"iconfont iconyoushangjiao\" v-if=\"index == 1\"></span>\n\t\t\t\t\t<span class=\"iconfont iconzuoxiajiao\" v-if=\"index == 2\"></span>\n\t\t\t\t\t<span class=\"iconfont iconyouxiajiao\" v-if=\"index == 3\"></span>\n\t\t\t\t</template>\n\t\t\t</Input>\n\t\t</div>\n\t</div>\n\t<div class=\"c_row-item\" v-else>\n\t    <Col class=\"c_label\" span=\"4\" v-if=\"configData.valName\">\n\t        {{configData.valName}}\n\t    </Col>\n\t    <Col span=\"18\">\n\t        <Slider v-model=\"configData.val\" show-input @on-change=\"sliderChange($event)\" :min=\"configData.min\"></Slider>\n\t    </Col>\n\t</div>\n</div>\n", null]}