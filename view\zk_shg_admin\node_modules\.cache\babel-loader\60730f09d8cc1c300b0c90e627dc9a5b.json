{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\bill\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\bill\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { billTypeApi, billListApi, userFinanceApi } from \"@/api/finance\";\nimport exportExcel from \"../../../../utils/newToExcel.js\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: \"bill\",\n  data: function data() {\n    return {\n      billList: [],\n      formValidate: {\n        nickname: \"\",\n        start_time: \"\",\n        end_time: \"\",\n        type: \"\",\n        page: 1,\n        // 当前页\n        limit: 20 // 每页显示条数\n\n      },\n      options: timeOptions,\n      loading: false,\n      tabList: [],\n      total: 0,\n      columns: [{\n        title: \"用户ID\",\n        key: \"uid\",\n        sortable: true,\n        width: 80\n      }, {\n        title: \"昵称\",\n        key: \"nickname\",\n        minWidth: 150\n      }, {\n        title: \"金额\",\n        // sortable: true,\n        minWidth: 150,\n        slot: \"number\" // render: (h, params) => {\n        //     return h('div', {\n        //         style: {\n        //             color: '#FF5722'\n        //         }\n        //     })\n        // }\n\n      }, {\n        title: \"类型\",\n        key: \"title\",\n        minWidth: 100\n      }, {\n        title: \"备注\",\n        key: \"mark\",\n        minWidth: 150\n      }, {\n        title: \"创建时间\",\n        key: \"add_time\",\n        minWidth: 200\n      }]\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.selList();\n    this.getList();\n  },\n  methods: {\n    // 时间\n    onchangeTime: function onchangeTime(e) {\n      this.formValidate.start_time = e[0];\n      this.formValidate.end_time = e[1];\n      this.userSearchs();\n    },\n    // 获取筛选类型\n    selList: function selList() {\n      var _this = this;\n\n      billTypeApi().then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this.billList = res.data.list;\n\n                case 1:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      billListApi(this.formValidate).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  data = res.data;\n                  _this2.tabList = data.data;\n                  _this2.total = data.count;\n                  _this2.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 搜索\n    userSearchs: function userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 导出\n    //   let formValidate = this.formValidate;\n    //   let data = {\n    //     start_time: formValidate.start_time,\n    //     end_time: formValidate.end_time,\n    //     nickname: formValidate.nickname,\n    //     type: formValidate.type,\n    //   };\n    //   userFinanceApi(data)\n    //     .then((res) => {\n    //       location.href = res.data[0];\n    //     })\n    //     .catch((res) => {\n    //       this.$Message.error(res.msg);\n    //     });\n    // 数据导出；\n    exports: function () {\n      var _exports = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee3() {\n        var th, filekey, data, fileName, excelData, i, lebData;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                th = [], filekey = [], data = [], fileName = \"\"; //   let fileName = \"\";\n\n                excelData = JSON.parse(JSON.stringify(this.formValidate));\n                excelData.page = 1;\n                i = 0;\n\n              case 4:\n                if (!(i < excelData.page + 1)) {\n                  _context3.next = 21;\n                  break;\n                }\n\n                _context3.next = 7;\n                return this.getExcelData(excelData);\n\n              case 7:\n                lebData = _context3.sent;\n                if (!fileName) fileName = lebData.filename;\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) th = lebData.header;\n\n                if (!lebData.export.length) {\n                  _context3.next = 16;\n                  break;\n                }\n\n                data = data.concat(lebData.export);\n                excelData.page++;\n                _context3.next = 18;\n                break;\n\n              case 16:\n                exportExcel(th, filekey, fileName, data);\n                return _context3.abrupt(\"return\");\n\n              case 18:\n                i++;\n                _context3.next = 4;\n                break;\n\n              case 21:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this);\n      }));\n\n      function exports() {\n        return _exports.apply(this, arguments);\n      }\n\n      return exports;\n    }(),\n    getExcelData: function getExcelData(excelData) {\n      return new Promise(function (resolve, reject) {\n        userFinanceApi(excelData).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    }\n  }\n};", null]}