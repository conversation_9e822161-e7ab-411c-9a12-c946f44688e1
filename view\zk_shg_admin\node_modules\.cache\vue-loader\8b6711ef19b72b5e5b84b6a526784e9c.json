{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue?vue&type=template&id=4455e17a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.footConfig)?_c('div',{staticClass:\"footer\"},[_c('p',{staticClass:\"tips\"},[_vm._v(\"图片建议宽度81*81px；鼠标拖拽左侧圆点可调整导航顺序\")]),_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.footConfig,\"group\":\"peoples\",\"handle\":\".iconfont\"}},_vm._l((_vm.footConfig),function(item,index){return _c('div',{key:index,staticClass:\"box-item\"},[_c('div',{staticClass:\"left-tool\"},[_c('span',{staticClass:\"iconfont iconxingzhuangjiehe\"})]),_c('div',{staticClass:\"right-wrapper\"},[(_vm.navStyle != 1)?_c('div',{staticClass:\"acea-row\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"图标\")]),_c('div',{staticClass:\"img-wrapper\"},_vm._l((item.imgList),function(img,j){return _c('div',{staticClass:\"img-item\",on:{\"click\":function($event){return _vm.modalPicTap(index,j)}}},[(img)?_c('div',{staticClass:\"pictrue\"},[_c('img',{attrs:{\"src\":img,\"alt\":\"\"}}),_c('p',{staticClass:\"txt\"},[_vm._v(\"替换\")])]):_c('div',{staticClass:\"empty-img\"},[_c('span',{staticClass:\"iconfont iconjiahao\"})]),_c('div',{staticClass:\"name\"},[_vm._v(_vm._s(j==0?'选中':'未选中'))])])}),0)]):_vm._e(),(_vm.navStyle != 2)?_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"\\n                            名称\\n                        \")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"20\"}},[_c('Input',{attrs:{\"placeholder\":\"选填不超过10个字\"},model:{value:(item.name),callback:function ($$v) {_vm.$set(item, \"name\", $$v)},expression:\"item.name\"}})],1)],1):_vm._e(),_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"\\n                            链接\\n                        \")]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":\"20\"}},[_c('div',{on:{\"click\":function($event){return _vm.getLink(index)}}},[_c('Input',{attrs:{\"icon\":\"ios-arrow-forward\",\"readonly\":\"\",\"placeholder\":\"选填不超过10个字\"},model:{value:(item.link),callback:function ($$v) {_vm.$set(item, \"link\", $$v)},expression:\"item.link\"}})],1)])],1)]),_c('div',{staticClass:\"del-box\",on:{\"click\":function($event){return _vm.deleteMenu(index)}}},[_c('span',{staticClass:\"iconfont iconcha\"})])])}),0),(_vm.footConfig.length<5)?_c('Button',{staticClass:\"add-btn\",on:{\"click\":_vm.addMenu}},[_vm._v(\"+ 添加板块\")]):_vm._e(),_c('div',[_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"上传底部菜单\",\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1)],1),_c('linkaddress',{ref:\"linkaddres\",on:{\"linkUrl\":_vm.linkUrl}})],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}