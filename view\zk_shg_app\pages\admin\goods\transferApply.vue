<template>
	<view class="transfer-apply-container">
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<text class="iconfont icon-ic_leftarrow"></text>
			</view>
			<view class="nav-title">申请调拨</view>
		</view>
		
		<!-- 商品基础信息卡片 -->
		<view class="product-card">
			<view class="product-header">
				<text class="section-title">商品信息</text>
			</view>
			<view class="product-info">
				<image :src="baseInfo.image" class="product-image" mode="aspectFill" />
				<view class="product-details">
					<text class="product-name">{{ baseInfo.store_name }}</text>
					<text class="product-price">¥{{ baseInfo.price }}</text>
					<text class="spec-count">共{{ baseInfo.total_specs }}个规格</text>
				</view>
			</view>
		</view>

		<!-- 规格调拨列表 -->
		<view class="spec-list">
			<view v-for="(spec, index) in specList" :key="spec.spec_summary.unique" class="spec-item">
				<!-- 规格头部信息 -->
				<view class="spec-header" @click="toggleSpec(index)">
					<view class="spec-info">
						<view class="spec-name-row">
							<text class="spec-name">{{ spec.spec_summary.spec_name }}</text>
							<view v-if="spec.submitted" class="status-indicator submitted">
								<text class="status-text">已提交</text>
							</view>
							<view v-else-if="spec.submitting" class="status-indicator submitting">
								<text class="status-text">提交中...</text>
							</view>
						</view>
						<text class="spec-stock">当前库存: {{ spec.current_product.stock }}</text>
					</view>
					<view class="expand-icon" :class="{ 'expanded': spec.expanded }">
						<text class="iconfont icon-ic_rightarrow"></text>
					</view>
				</view>
				
				<!-- 规格调拨表单 -->
				<view v-if="spec.expanded" class="spec-form">
					<!-- 调拨类型 -->
					<view class="form-item">
						<text class="label">调拨类型</text>
						<view class="radio-group">
							<label class="radio-item" :class="{ 'radio-item-checked': spec.formData.type === 'out' }" @click="selectSpecType(index, 'out')">
								<radio :value="'out'" :checked="spec.formData.type === 'out'" />
								<text class="radio-text">调出</text>
							</label>
							<label class="radio-item" :class="{ 'radio-item-checked': spec.formData.type === 'in' }" @click="selectSpecType(index, 'in')">
								<radio :value="'in'" :checked="spec.formData.type === 'in'" />
								<text class="radio-text">调入</text>
							</label>
						</view>
					</view>

					<!-- 调出模式：当前门店调出到选择门店 -->
					<template v-if="spec.formData.type === 'out'">
						<!-- 固定调出门店 -->
						<view class="form-item">
							<text class="label">调出门店</text>
							<view class="fixed-store-display">
								<text>{{ spec.current_product.store_name }}（库存：{{ spec.current_product.stock }}）</text>
							</view>
						</view>
						
						<!-- 可选调入门店 -->
						<view class="form-item">
							<text class="label">调入门店 <text class="required">*</text></text>
							<picker 
								@change="onSpecStoreChange(index, $event)" 
								:value="spec.storeIndex" 
								:range="spec.storeOptions" 
								range-key="displayText"
								:disabled="spec.submitted || spec.submitting">
								<view class="picker-display" :class="{ 'picker-disabled': spec.submitted || spec.submitting }">
									<text>{{ (spec.storeOptions[spec.storeIndex] && spec.storeOptions[spec.storeIndex].displayText) || '请选择调入门店' }}</text>
									<text class="arrow">></text>
								</view>
							</picker>
						</view>
					</template>
					
					<!-- 调入模式：从选择门店调入到当前门店 -->
					<template v-if="spec.formData.type === 'in'">
						<!-- 固定调入门店 -->
						<view class="form-item">
							<text class="label">调入门店</text>
							<view class="fixed-store-display">
								<text>{{ spec.current_product.store_name }}（库存：{{ spec.current_product.stock }}）</text>
							</view>
						</view>
						
						<!-- 可选调出门店 -->
						<view class="form-item">
							<text class="label">调出门店 <text class="required">*</text></text>
							<picker 
								@change="onSpecStoreChange(index, $event)" 
								:value="spec.storeIndex" 
								:range="spec.storeOptions" 
								range-key="displayText"
								:disabled="spec.submitted || spec.submitting">
								<view class="picker-display" :class="{ 'picker-disabled': spec.submitted || spec.submitting }">
									<text>{{ (spec.storeOptions[spec.storeIndex] && spec.storeOptions[spec.storeIndex].displayText) || '请选择调出门店' }}</text>
									<text class="arrow">></text>
								</view>
							</picker>
						</view>
					</template>

					<!-- 调拨数量 -->
					<view class="form-item">
						<text class="label">调拨数量 <text class="required">*</text></text>
						<view class="quantity-input">
							<button 
								class="quantity-btn" 
								@click="decreaseSpecQuantity(index)" 
								:disabled="spec.formData.quantity <= 1 || spec.submitted || spec.submitting">-</button>
							<input 
								type="number" 
								v-model.number="spec.formData.quantity" 
								class="quantity-field" 
								@input="onSpecQuantityInput(index, $event)"
								:disabled="spec.submitted || spec.submitting" />
							<button 
								class="quantity-btn" 
								@click="increaseSpecQuantity(index)"
								:disabled="spec.submitted || spec.submitting">+</button>
						</view>
						<text class="hint">最大可调拨: {{ getMaxQuantity(spec) }}</text>
					</view>

					<!-- 申请原因 -->
					<view class="form-item">
						<text class="label">申请原因 <text class="required">*</text></text>
						<textarea 
							v-model="spec.formData.reason" 
							placeholder="请填写调拨申请原因" 
							class="reason-textarea" 
							maxlength="200"
							:disabled="spec.submitted || spec.submitting" />
						<text class="char-count">{{ spec.formData.reason.length }}/200</text>
					</view>
					
					<!-- 单个规格提交按钮 -->
					<view class="form-item">
						<button 
							class="spec-submit-btn" 
							:class="{ 
								'spec-submit-btn-disabled': !canSubmitSpec(spec) || spec.submitting,
								'spec-submit-btn-submitted': spec.submitted
							}"
							:disabled="!canSubmitSpec(spec) || spec.submitting"
							@click.stop="submitSingleSpec(index)">
							<text v-if="spec.submitted">已提交此规格</text>
							<text v-else-if="spec.submitting">提交中...</text>
							<text v-else>提交此规格</text>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-container">
			<button 
				class="submit-btn" 
				@click="submitTransfer" 
				:disabled="!canSubmit || loading || isAnySubmitting">
				<text v-if="loading">批量提交中...</text>
				<text v-else-if="isAnySubmitting">等待提交完成...</text>
				<text v-else>批量提交所有展开的规格</text>
			</button>
		</view>

		<!-- 加载提示 -->
		<view v-if="loading" class="loading-container">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import { submitTransferApply, getProductInfo } from '@/api/admin.js'

export default {
	name: 'TransferApply',
	data() {
		return {
			// 商品基础信息
			baseInfo: {
				id: null,
				store_name: '',
				image: '',
				price: '0.00',
				unit_name: '',
				spec_type: 0,
				total_specs: 0
			},
			// 规格列表
			specList: [],
			loading: false
		}
	},
	computed: {
		// 是否可以提交
		canSubmit() {
			// 至少有一个规格填写完整且展开且未提交且未正在提交
			return this.specList.some(spec => {
				if (!spec.expanded || spec.submitted || spec.submitting) return false
				const { type, target_store_id, quantity, reason } = spec.formData
				const hasTargetStore = target_store_id
				const maxQuantity = this.getMaxQuantity(spec)
				return quantity > 0 && quantity <= maxQuantity && reason.trim() && hasTargetStore
			})
		},
		// 是否有任何规格正在提交
		isAnySubmitting() {
			return this.specList.some(spec => spec.submitting)
		}
	},
	onLoad(options) {
		if (options.product_id) {
			console.log('开始初始化调拨申请页面，商品ID:', options.product_id)
			this.loadProductInfo(options.product_id)
		} else {
			console.error('缺少商品ID参数')
			this.showToast('缺少商品信息')
			this.goBack()
		}
	},
	methods: {
		// 返回
		goBack() {
			uni.navigateBack()
		},
		
		// 显示提示
		showToast(title, icon = 'none') {
			uni.showToast({ title, icon })
		},
		
		// 加载商品信息
		async loadProductInfo(productId) {
			try {
				console.log('开始加载商品信息...', productId)
				const res = await getProductInfo(productId)
				console.log('商品信息API响应:', res)
				
				if (res.status === 200 || res.code === 200) {
					const data = res.data || {}
					
					// 设置商品基础信息
					this.baseInfo = {
						id: data.base_info?.id || productId,
						store_name: data.base_info?.store_name || '未知商品',
						image: data.base_info?.image || '/static/images/default-product.png',
						price: data.base_info?.price || '0.00',
						unit_name: data.base_info?.unit_name || '',
						spec_type: data.base_info?.spec_type || 0,
						total_specs: data.base_info?.total_specs || 0
					}
					
					// 处理规格列表
					this.specList = (data.spec_list || []).map((spec, index) => ({
						...spec,
						expanded: index === 0, // 默认展开第一个规格
						submitting: false, // 该规格是否正在提交
						submitted: false, // 该规格是否已提交成功
						formData: {
							type: 'out', // 默认调出
							target_store_id: '',
							quantity: 1,
							reason: ''
						},
						storeOptions: [],
						storeIndex: 0
					}))
					
					// 为每个规格生成门店选项
					this.specList.forEach((spec, index) => {
						this.updateSpecStoreOptions(index)
						// 自动选择第一个门店作为默认值
						if (spec.storeOptions.length > 0) {
							spec.formData.target_store_id = spec.storeOptions[0].id
							spec.storeIndex = 0
						}
					})
					
					console.log('商品信息处理完成:', {
						baseInfo: this.baseInfo,
						specCount: this.specList.length
					})
				} else {
					console.error('获取商品信息失败:', res)
					this.showToast(res.msg || res.message || '获取商品信息失败')
				}
			} catch (error) {
				console.error('加载商品信息失败:', error)
				this.showToast('加载商品信息失败')
			}
		},
		
		// 切换规格展开/折叠
		toggleSpec(index) {
			this.specList[index].expanded = !this.specList[index].expanded
		},
		
		// 选择规格调拨类型
		selectSpecType(index, type) {
			this.specList[index].formData.type = type
			this.specList[index].formData.target_store_id = ''
			this.specList[index].storeIndex = 0
			// 重新生成门店选项
			this.updateSpecStoreOptions(index)
			
			// 自动选择第一个门店作为默认值
			const spec = this.specList[index]
			if (spec.storeOptions.length > 0) {
				spec.formData.target_store_id = spec.storeOptions[0].id
				spec.storeIndex = 0
			}
		},
		
		// 更新规格的门店选项
		updateSpecStoreOptions(index) {
			const spec = this.specList[index]
			const { type } = spec.formData
			
			// 通过product_id匹配找到当前门店
			const currentStoreId = this.getCurrentStoreId(spec)
			
			if (type === 'out') {
				// 调出模式：显示所有其他门店（基于store_stocks，不检查库存）
				spec.storeOptions = spec.store_stocks
					.filter(store => store.store_id !== currentStoreId)
					.map(store => ({
						id: store.store_id,
						name: store.store_name,
						stock: store.stock,
						displayText: store.store_name,
						unique: store.unique,
						cost: store.cost,
						price: store.price
					}))
			} else {
				// 调入模式：显示有此规格库存的其他门店
				spec.storeOptions = spec.store_stocks
					.filter(store => store.stock > 0 && store.store_id !== currentStoreId)
					.map(store => ({
						id: store.store_id,
						name: store.store_name,
						stock: store.stock,
						displayText: `${store.store_name}（库存：${store.stock}）`,
						unique: store.unique,
						cost: store.cost,
						price: store.price
					}))
			}
		},
		
		// 获取当前门店ID（通过product_id与base_info.id匹配）
		getCurrentStoreId(spec) {
			// 在store_stocks中找到product_id等于base_info.id的门店
			const currentStore = spec.store_stocks.find(store => 
				store.product_id === parseInt(this.baseInfo.id)
			)
			
			console.log('当前门店识别：', {
				baseInfoId: this.baseInfo.id,
				currentStore: currentStore,
				fallbackStoreId: spec.current_product.store_id
			})
			
			return currentStore ? currentStore.store_id : spec.current_product.store_id
		},
		
		// 规格门店选择变更
		onSpecStoreChange(index, e) {
			this.specList[index].storeIndex = e.detail.value
			const selectedStore = this.specList[index].storeOptions[e.detail.value]
			this.specList[index].formData.target_store_id = selectedStore ? selectedStore.id : ''
		},
		
		// 规格数量输入处理
		onSpecQuantityInput(index, e) {
			let value = parseInt(e.detail.value) || 1
			if (value < 1) value = 1
			const maxQuantity = this.getMaxQuantity(this.specList[index])
			if (value > maxQuantity) value = maxQuantity
			this.specList[index].formData.quantity = value
		},
		
		// 减少规格数量
		decreaseSpecQuantity(index) {
			if (this.specList[index].formData.quantity > 1) {
				this.specList[index].formData.quantity--
			}
		},
		
		// 增加规格数量
		increaseSpecQuantity(index) {
			const maxQuantity = this.getMaxQuantity(this.specList[index])
			if (this.specList[index].formData.quantity < maxQuantity) {
				this.specList[index].formData.quantity++
			}
		},
		
		// 获取规格最大可调拨数量
		getMaxQuantity(spec) {
			const { type } = spec.formData
			if (type === 'out') {
				// 调出模式：当前门店的库存
				return spec.current_product.stock
			} else {
				// 调入模式：选中门店的库存
				const selectedStore = spec.storeOptions[spec.storeIndex]
				return selectedStore ? selectedStore.stock : 0
			}
		},
		
		// 检查规格是否可以提交
		canSubmitSpec(spec) {
			if (spec.submitted || spec.submitting) return false
			const { type, target_store_id, quantity, reason } = spec.formData
			const hasTargetStore = target_store_id
			const maxQuantity = this.getMaxQuantity(spec)
			return quantity > 0 && quantity <= maxQuantity && reason.trim() && hasTargetStore
		},
		
		// 提交单个规格
		async submitSingleSpec(index) {
			const spec = this.specList[index]
			
			if (!this.canSubmitSpec(spec)) {
				this.showToast('请完善申请信息')
				return
			}
			
			if (spec.submitting) {
				this.showToast('正在提交中，请稍候')
				return
			}
			
			// 设置提交状态
			this.specList[index].submitting = true
			
			try {
				const { type, target_store_id, quantity, reason } = spec.formData
				let fromStoreId, toStoreId
				
				if (type === 'out') {
					// 调出：从当前门店到目标门店
					fromStoreId = spec.current_product.store_id
					toStoreId = target_store_id
				} else {
					// 调入：从目标门店到当前门店
					fromStoreId = target_store_id
					toStoreId = spec.current_product.store_id
				}
				
				const submitData = {
					from_store_id: fromStoreId,
					to_store_id: toStoreId,
					remark: reason.trim(),
					details: [{
						product_id: parseInt(this.baseInfo.id),
						unique: spec.spec_summary.unique,
						transfer_num: parseInt(quantity)
					}]
				}
				
				console.log('提交单个规格调拨申请数据：', submitData)
				const result = await submitTransferApply(submitData)
				
				if (result.status === 200 || result.code === 200) {
					this.specList[index].submitted = true
					this.showToast(`规格"${spec.spec_summary.spec_name}"申请提交成功`, 'success')
				} else {
					this.showToast(result.msg || result.message || '提交失败')
				}
			} catch (error) {
				console.error('提交规格调拨申请失败:', error)
				this.showToast('提交失败，请重试')
			} finally {
				this.specList[index].submitting = false
			}
		},
		
		// 批量提交调拨申请
		async submitTransfer() {
			if (!this.canSubmit || this.isAnySubmitting) {
				this.showToast('请完善申请信息或等待当前提交完成')
				return
			}
			
			// 收集所有可提交的规格
			const validSpecs = this.specList.filter((spec, index) => {
				return spec.expanded && this.canSubmitSpec(spec)
			})
			
			if (validSpecs.length === 0) {
				this.showToast('请至少完善一个规格的申请信息')
				return
			}
			
			this.loading = true
			
			try {
				// 按调拨方向分组提交
				const transferGroups = new Map()
				
				validSpecs.forEach(spec => {
					const { type, target_store_id, quantity, reason } = spec.formData
					let fromStoreId, toStoreId, transferKey
					
					if (type === 'out') {
						// 调出：从当前门店到目标门店
						fromStoreId = spec.current_product.store_id
						toStoreId = target_store_id
					} else {
						// 调入：从目标门店到当前门店
						fromStoreId = target_store_id
						toStoreId = spec.current_product.store_id
					}
					
					transferKey = `${fromStoreId}-${toStoreId}-${reason.trim()}`
					
					if (!transferGroups.has(transferKey)) {
						transferGroups.set(transferKey, {
							from_store_id: fromStoreId,
							to_store_id: toStoreId,
							remark: reason.trim(),
							details: [],
							specs: [] // 记录相关的规格，用于状态更新
						})
					}
					
					transferGroups.get(transferKey).details.push({
						product_id: parseInt(this.baseInfo.id),
						unique: spec.spec_summary.unique,
						transfer_num: parseInt(quantity)
					})
					transferGroups.get(transferKey).specs.push(spec)
				})
				
				// 设置所有相关规格为提交中状态
				validSpecs.forEach(spec => {
					const specIndex = this.specList.findIndex(s => s.spec_summary.unique === spec.spec_summary.unique)
					if (specIndex !== -1) {
						this.specList[specIndex].submitting = true
					}
				})
				
				// 逐个提交每个调拨组
				const submitPromises = Array.from(transferGroups.entries()).map(([key, groupData]) => {
					console.log('提交调拨申请数据：', groupData)
					return submitTransferApply({
						from_store_id: groupData.from_store_id,
						to_store_id: groupData.to_store_id,
						remark: groupData.remark,
						details: groupData.details
					}).then(result => ({ result, specs: groupData.specs }))
				})
				
				const results = await Promise.all(submitPromises)
				let successCount = 0
				let failedCount = 0
				
				// 更新每个规格的状态
				results.forEach(({ result, specs }) => {
					if (result.status === 200 || result.code === 200) {
						successCount++
						specs.forEach(spec => {
							const specIndex = this.specList.findIndex(s => s.spec_summary.unique === spec.spec_summary.unique)
							if (specIndex !== -1) {
								this.specList[specIndex].submitted = true
								this.specList[specIndex].submitting = false
							}
						})
					} else {
						failedCount++
						specs.forEach(spec => {
							const specIndex = this.specList.findIndex(s => s.spec_summary.unique === spec.spec_summary.unique)
							if (specIndex !== -1) {
								this.specList[specIndex].submitting = false
							}
						})
					}
				})
				
				if (failedCount === 0) {
					this.showToast('所有申请提交成功', 'success')
					// 不再自动返回，让用户可以继续操作其他规格
				} else if (successCount > 0) {
					this.showToast(`${successCount}个申请成功，${failedCount}个申请失败`)
				} else {
					this.showToast('提交失败，请重试')
				}
			} catch (error) {
				console.error('批量提交调拨申请失败:', error)
				this.showToast('提交失败，请重试')
				// 重置所有提交中状态
				validSpecs.forEach(spec => {
					const specIndex = this.specList.findIndex(s => s.spec_summary.unique === spec.spec_summary.unique)
					if (specIndex !== -1) {
						this.specList[specIndex].submitting = false
					}
				})
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.transfer-apply-container {
	background: #f2f2f7;
	min-height: 100vh;
	padding-bottom: 200rpx; /* 为固定按钮预留空间 */
}

.nav-bar {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	position: relative;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.nav-left {
	position: absolute;
	left: 20rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	transition: background-color 0.2s ease;
	
	&:active {
		background-color: rgba(0, 0, 0, 0.05);
	}
}

.nav-left .iconfont {
	font-size: 38rpx;
	color: #007aff;
	font-weight: 600;
}

.nav-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #1d1d1f;
	letter-spacing: -0.5rpx;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

.product-card {
	background: rgba(255, 255, 255, 0.95);
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.5);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}

.product-header {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #1d1d1f;
	letter-spacing: -0.5rpx;
}

.product-info {
	display: flex;
	align-items: center;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 16rpx;
	margin-right: 24rpx;
}

.product-details {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.product-name {
	font-size: 32rpx;
	color: #1d1d1f;
	margin-bottom: 8rpx;
	font-weight: 600;
	letter-spacing: -0.3rpx;
	line-height: 1.3;
}

.product-price {
	font-size: 30rpx;
	color: #ff3b30;
	margin-bottom: 8rpx;
	font-weight: 700;
	letter-spacing: -0.2rpx;
}

.spec-count {
	font-size: 28rpx;
	color: #8e8e93;
	letter-spacing: -0.2rpx;
}

.spec-list {
	margin: 20rpx;
}

.spec-item {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.5);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
	overflow: hidden;
}

.spec-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	cursor: pointer;
	transition: background-color 0.2s ease;
	
	&:active {
		background-color: rgba(0, 0, 0, 0.02);
	}
}

.spec-info {
	flex: 1;
}

.spec-name-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.spec-name {
	font-size: 32rpx;
	color: #1d1d1f;
	font-weight: 600;
	letter-spacing: -0.3rpx;
	flex: 1;
}

.status-indicator {
	padding: 6rpx 16rpx;
	border-radius: 12rpx;
	margin-left: 16rpx;
}

.status-indicator.submitted {
	background: rgba(52, 199, 89, 0.1);
	border: 1rpx solid #34c759;
}

.status-indicator.submitting {
	background: rgba(255, 149, 0, 0.1);
	border: 1rpx solid #ff9500;
}

.status-text {
	font-size: 24rpx;
	font-weight: 600;
	letter-spacing: -0.2rpx;
}

.status-indicator.submitted .status-text {
	color: #34c759;
}

.status-indicator.submitting .status-text {
	color: #ff9500;
}

.spec-stock {
	font-size: 28rpx;
	color: #8e8e93;
	letter-spacing: -0.2rpx;
}

.expand-icon {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: transform 0.3s ease;
	
	&.expanded {
		transform: rotate(90deg);
	}
}

.expand-icon .iconfont {
	font-size: 24rpx;
	color: #8e8e93;
}

.spec-form {
	padding: 0 30rpx 30rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.form-item {
	margin-bottom: 32rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.label {
	display: block;
	font-size: 32rpx;
	color: #1d1d1f;
	margin-bottom: 16rpx;
	font-weight: 600;
	letter-spacing: -0.3rpx;
}

.required {
	color: #ff4757;
}

.radio-group {
	display: flex;
	gap: 24rpx;
	padding: 20rpx 0;
	justify-content: center;
}

.radio-item {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 18rpx 36rpx;
	border-radius: 28rpx;
	background: #f8f8f8;
	border: 2rpx solid rgba(0, 0, 0, 0.08);
	transition: all 0.2s ease;
	cursor: pointer;
	min-width: 160rpx;
	
	&:active {
		transform: scale(0.98);
	}
}

.radio-item-checked {
	background: #007aff;
	border-color: #007aff;
}

.radio-text {
	margin-left: 8rpx;
	font-size: 32rpx;
	color: #1d1d1f;
	font-weight: 600;
	letter-spacing: -0.3rpx;
	transition: all 0.2s ease;
}

.radio-item-checked .radio-text {
	color: white;
}

// 隐藏原生radio按钮
radio {
	position: absolute;
	opacity: 0;
	width: 0;
	height: 0;
}

.picker-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	border: 2rpx solid rgba(0, 0, 0, 0.08);
	border-radius: 16rpx;
	background: #f8f8f8;
	transition: all 0.2s ease;
	
	&:active {
		border-color: #007aff;
		background: #ffffff;
	}
}

.picker-disabled {
	opacity: 0.6;
	background: #e8e8e8 !important;
	
	&:active {
		border-color: rgba(0, 0, 0, 0.08) !important;
		background: #e8e8e8 !important;
	}
}

.fixed-store-display {
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx;
	border: 2rpx solid #007aff;
	border-radius: 16rpx;
	background: rgba(0, 122, 255, 0.05);
	
	text {
		color: #007aff;
		font-weight: 600;
		font-size: 32rpx;
		letter-spacing: -0.3rpx;
	}
}

.arrow {
	color: #999;
	font-size: 28rpx;
}

.quantity-input {
	display: flex;
	align-items: center;
	width: 240rpx;
	height: 64rpx; /* 确保容器高度固定 */
}

.quantity-btn {
	width: 64rpx;
	height: 64rpx;
	background: #f8f8f8;
	border: none;
	border-radius: 32rpx;
	font-size: 32rpx;
	color: #007aff;
	font-weight: 600;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 1; /* 确保文字垂直居中 */
	
	&:active {
		background: #e0e0e0;
		transform: scale(0.95);
	}
	
	&:disabled {
		opacity: 0.3;
		color: #8e8e93;
	}
}

.quantity-field {
	flex: 1;
	text-align: center;
	border: 2rpx solid rgba(0, 0, 0, 0.08);
	border-radius: 16rpx;
	height: 64rpx;
	margin: 0 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	background: #f8f8f8;
	transition: all 0.2s ease;
	
	&:focus {
		border-color: #007aff;
		background: #ffffff;
	}
	
	&:disabled {
		opacity: 0.6;
		background: #e8e8e8;
		color: #8e8e93;
	}
}

.hint {
	font-size: 26rpx;
	color: #8e8e93;
	margin-top: 12rpx;
	display: block;
	letter-spacing: -0.2rpx;
}

.reason-textarea {
	width: 100%;
	height: 160rpx;
	border: 2rpx solid rgba(0, 0, 0, 0.08);
	border-radius: 16rpx;
	padding: 24rpx;
	font-size: 30rpx;
	box-sizing: border-box;
	resize: none;
	background: #f8f8f8;
	transition: all 0.2s ease;
	line-height: 1.4;
	
	&:focus {
		border-color: #007aff;
		background: #ffffff;
	}
	
	&:disabled {
		opacity: 0.6;
		background: #e8e8e8;
		color: #8e8e93;
	}
}

.char-count {
	display: block;
	text-align: right;
	font-size: 26rpx;
	color: #8e8e93;
	margin-top: 12rpx;
	letter-spacing: -0.2rpx;
}

.spec-submit-btn {
	width: 100%;
	height: 72rpx;
	background: #007aff;
	border: none;
	border-radius: 16rpx;
	color: white;
	font-size: 30rpx;
	font-weight: 600;
	letter-spacing: -0.3rpx;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 20rpx;
	
	&:active {
		transform: scale(0.98);
		background: #0051d0;
	}
}

.spec-submit-btn-disabled {
	background: #d1d1d6;
	color: #8e8e93;
	
	&:active {
		transform: none;
		background: #d1d1d6;
	}
}

.spec-submit-btn-submitted {
	background: #34c759;
	color: white;
	
	&:active {
		transform: none;
		background: #34c759;
	}
}

.submit-container {
	padding: 40rpx 30rpx 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.submit-btn {
	width: 600rpx;
	height: 96rpx;
	background: #d1d1d6; /* 默认灰色状态 */
	border: none;
	border-radius: 48rpx;
	color: #8e8e93; /* 默认灰色文字 */
	font-size: 34rpx;
	font-weight: 700;
	letter-spacing: -0.3rpx;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: not-allowed; /* 默认不可点击 */
	
	/* 启用状态 */
	&:not(:disabled) {
		background: #007aff;
		color: white;
		cursor: pointer;
		
		&:active {
			transform: scale(0.98);
			background: #0051d0;
		}
	}
	
	/* 明确的禁用状态 */
	&:disabled {
		background: #d1d1d6;
		color: #8e8e93;
		transform: none;
		cursor: not-allowed;
		
		&:active {
			transform: none;
			background: #d1d1d6;
		}
	}
}
</style> 