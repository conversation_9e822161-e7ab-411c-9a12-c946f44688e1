{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue?vue&type=template&id=4ead133c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Form',{ref:\"orderData\",staticClass:\"tabform\",attrs:{\"inline\":\"\",\"model\":_vm.orderData,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Row',[_c('Col',[_c('FormItem',{attrs:{\"label\":\"订单类型：\"}},[_c('RadioGroup',{attrs:{\"type\":\"button\"},on:{\"on-change\":_vm.onClickTab},model:{value:(_vm.currentTab),callback:function ($$v) {_vm.currentTab=$$v},expression:\"currentTab\"}},[_c('Radio',{attrs:{\"label\":\"-1\"}},[_vm._v(\"所有订单\")]),_c('Radio',{attrs:{\"label\":\"0\"}},[_vm._v(\"平台订单\")]),_c('Radio',{attrs:{\"label\":\"1\"}},[_vm._v(\"门店订单\")]),_c('Radio',{attrs:{\"label\":\"2\"}},[_vm._v(\"供应商订单\")])],1)],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"支付方式：\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"clearable\":\"\",\"placeholder\":\"全部\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.orderData.pay_type),callback:function ($$v) {_vm.$set(_vm.orderData, \"pay_type\", $$v)},expression:\"orderData.pay_type\"}},_vm._l((_vm.payList),function(item){return _c('Option',{key:item.id,attrs:{\"value\":item.val}},[_vm._v(_vm._s(item.label))])}),1)],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"创建时间：\"}},[_c('DatePicker',{staticClass:\"input-add mr20\",attrs:{\"editable\":false,\"clearable\":true,\"value\":_vm.timeVal,\"format\":\"yyyy/MM/dd HH:mm:ss\",\"type\":\"datetimerange\",\"placement\":\"bottom-start\",\"placeholder\":\"自定义时间\",\"options\":_vm.options},on:{\"on-change\":_vm.onchangeTime}})],1)],1)],1),_c('Row',[_c('Col',[_c('FormItem',{attrs:{\"label\":\"活动类型：\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"clearable\":\"\",\"placeholder\":\"全部\"},on:{\"on-change\":_vm.typeChange},model:{value:(_vm.orderData.type),callback:function ($$v) {_vm.$set(_vm.orderData, \"type\", $$v)},expression:\"orderData.type\"}},[_c('Option',{attrs:{\"value\":\"0\"}},[_vm._v(\"普通订单\")]),_c('Option',{attrs:{\"value\":\"1\"}},[_vm._v(\"秒杀订单\")]),_c('Option',{attrs:{\"value\":\"2\"}},[_vm._v(\"砍价订单\")]),_c('Option',{attrs:{\"value\":\"3\"}},[_vm._v(\"拼团订单\")]),_c('Option',{attrs:{\"value\":\"4\"}},[_vm._v(\"积分订单\")]),_c('Option',{attrs:{\"value\":\"5\"}},[_vm._v(\"套餐订单\")]),_c('Option',{attrs:{\"value\":\"6\"}},[_vm._v(\"预售订单\")]),_c('Option',{attrs:{\"value\":\"7\"}},[_vm._v(\"新人订单\")]),_c('Option',{attrs:{\"value\":\"8\"}},[_vm._v(\"抽奖订单\")]),_c('Option',{attrs:{\"value\":\"9\"}},[_vm._v(\"拼单订单\")]),_c('Option',{attrs:{\"value\":\"10\"}},[_vm._v(\"桌码订单\")])],1)],1)],1),(_vm.orderType == '1')?_c('Col',[_c('FormItem',{attrs:{\"label\":\"选择门店：\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"clearable\":\"\",\"filterable\":\"\"},on:{\"on-change\":_vm.storeChange},model:{value:(_vm.orderData.store_id),callback:function ($$v) {_vm.$set(_vm.orderData, \"store_id\", $$v)},expression:\"orderData.store_id\"}},_vm._l((_vm.staffData),function(item){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.name)+\"\\n            \")])}),1)],1)],1):_vm._e(),(_vm.orderType == '2')?_c('Col',[_c('FormItem',{attrs:{\"label\":\"供应商：\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"clearable\":\"\",\"filterable\":\"\"},on:{\"on-change\":_vm.supplierChange},model:{value:(_vm.orderData.supplier_id),callback:function ($$v) {_vm.$set(_vm.orderData, \"supplier_id\", $$v)},expression:\"orderData.supplier_id\"}},_vm._l((_vm.supplierName),function(item){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.supplier_name))])}),1)],1)],1):_vm._e(),_c('Col',[_c('FormItem',{attrs:{\"label\":\"订单搜索：\",\"prop\":\"real_name\",\"label-for\":\"real_name\"}},[_c('Input',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请输入\",\"element-id\":\"name\",\"clearable\":\"\",\"maxlength\":\"20\"},on:{\"on-change\":_vm.clearTap},model:{value:(_vm.orderData.real_name),callback:function ($$v) {_vm.$set(_vm.orderData, \"real_name\", $$v)},expression:\"orderData.real_name\"}},[_c('Select',{staticStyle:{\"width\":\"80px\"},attrs:{\"slot\":\"prepend\",\"default-label\":\"全部\"},slot:\"prepend\",model:{value:(_vm.orderData.field_key),callback:function ($$v) {_vm.$set(_vm.orderData, \"field_key\", $$v)},expression:\"orderData.field_key\"}},[_c('Option',{attrs:{\"value\":\"all\"}},[_vm._v(\"全部\")]),_c('Option',{attrs:{\"value\":\"order_id\"}},[_vm._v(\"订单号\")]),_c('Option',{attrs:{\"value\":\"uid\"}},[_vm._v(\"用户UID\")]),_c('Option',{attrs:{\"value\":\"real_name\"}},[_vm._v(\"用户姓名\")]),_c('Option',{attrs:{\"value\":\"user_phone\"}},[_vm._v(\"用户电话\")]),_c('Option',{attrs:{\"value\":\"title\"}},[_vm._v(\"商品名称\")]),_c('Option',{attrs:{\"value\":\"total_num\"}},[_vm._v(\"商品件数\")])],1)],1)],1),_c('FormItem',[_c('Button',{staticStyle:{\"margin-left\":\"-90px\"},attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.orderSearch(_vm.orderData.real_name)}}},[_vm._v(\"查询\")])],1)],1)],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}