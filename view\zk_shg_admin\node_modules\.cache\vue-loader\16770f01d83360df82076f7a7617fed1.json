{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue?vue&type=template&id=db491212&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue", "mtime": 1717062347000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"c_product\" v-if=\"configData\">\n        <div class=\"title\">{{configData.title}}</div>\n        <div class=\"list-box\">\n            <draggable\n                    class=\"dragArea list-group\"\n                    :list=\"configData.list\"\n                    group=\"peoples\"\n                    handle=\".move-icon\"\n            >\n                <div class=\"item\" v-for=\"(item,index) in configData.list\" :key=\"index\">\n                    <div class=\"move-icon\">\n                        <span class=\"iconfont-diy iconxingzhuangjiehe\"></span>\n                    </div>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<div class=\"con-item\">\n\t\t\t\t\t\t    <span>{{item.text.title}}</span>\n\t\t\t\t\t\t    <div>\n\t\t\t\t\t\t        <Input v-model=\"item.text.val\" :placeholder=\"item.text.pla\" :maxlength=\"item.text.max\" show-word-limit/>\n\t\t\t\t\t\t    </div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"con-item\">\n\t\t\t\t\t\t\t<span>{{item.dataType.title}}</span>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<RadioGroup v-model=\"item.dataType.tabVal\">\n\t\t\t\t\t\t\t\t    <Radio :label=\"key\" v-for=\"(radio,key) in item.dataType.tabList\" :key=\"key\">\n\t\t\t\t\t\t\t\t        <span>{{radio.name}}</span>\n\t\t\t\t\t\t\t\t    </Radio>\n\t\t\t\t\t\t\t\t</RadioGroup>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"con-item\">\n\t\t\t\t\t\t\t<span>{{item.dataType.tabList[item.dataType.tabVal].name}}</span>\n\t\t\t\t\t\t\t<div @click=\"getLink(index)\">\n\t\t\t\t\t\t\t    <Input icon='ios-arrow-forward' v-model=\"item.dataType.tabVal?item.classPage.name:item.microPage.name\" :placeholder=\"item.dataType.tabVal?'选择分类':'选择页面'\" readonly/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n                    <div class=\"delete\" @click.stop=\"bindDelete(index)\">\n                        <Icon type=\"ios-close-circle\" size=\"24\"/>\n                    </div>\n                </div>\n            </draggable>\n        </div>\n        <div v-if=\"configData.list\">\n            <div class=\"add-btn\" @click=\"addHotTxt\" v-if=\"configData.list.length < configData.max\">\n                <Button class=\"btn\" type=\"primary\" ghost>\n\t\t\t\t\t<span class=\"iconfont iconjiahao\"></span>添加\n\t\t\t\t</Button>\n            </div>\n        </div>\n        <linkaddress ref=\"linkaddres\" :linkType='1' :fromType='\"diyPage\"' :isCateTree=\"!['homeComb', 'tabNav'].includes(defaults.name)\" @linkUrl=\"linkUrl\"></linkaddress>\n    </div>\n", null]}