{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\label\\cate.vue?vue&type=template&id=0bc7bcab&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\label\\cate.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n  <Row class=\"ivu-mt box-wrapper\">\n    <Col span=\"3\" class=\"left-wrapper\" v-if=\"labelSort.length > 0\">\n      <Menu :theme=\"theme3\" :active-name=\"sortName\" width=\"auto\">\n        <MenuGroup>\n          <MenuItem\n            :name=\"item.id\"\n            class=\"menu-item\"\n            v-for=\"(item, index) in labelSort\"\n            :key=\"index\"\n            @click.native=\"bindMenuItem(item)\"\n          >\n            {{ item.name }}\n            <div class=\"icon-box\" v-if=\"index != 0\">\n              <Icon type=\"ios-more\" size=\"24\" @click.stop=\"showMenu(item)\" />\n            </div>\n            <div\n              class=\"right-menu ivu-poptip-inner\"\n              v-show=\"item.status\"\n              v-if=\"index != 0\"\n            >\n              <div class=\"ivu-poptip-body\" @click=\"labelEdit(item)\">\n                <div class=\"ivu-poptip-body-content\">\n                  <div class=\"ivu-poptip-body-content-inner\">编辑</div>\n                </div>\n              </div>\n              <div\n                class=\"ivu-poptip-body\"\n                @click=\"deleteSort(item, '删除分类', index)\"\n              >\n                <div class=\"ivu-poptip-body-content\">\n                  <div class=\"ivu-poptip-body-content-inner\">删除</div>\n                </div>\n              </div>\n            </div>\n          </MenuItem>\n        </MenuGroup>\n      </Menu>\n    </Col>\n    <Col span=\"21\" ref=\"rightBox\">\n      <Card :bordered=\"false\" dis-hover>\n        <!-- 相关操作 -->\n        <Row type=\"flex\">\n          <Col v-bind=\"grid\">\n            <Button\n              v-auth=\"['admin-user-label_add']\"\n              type=\"primary\"\n              @click=\"add\"\n              >添加标签</Button\n            >\n            <Button\n              v-auth=\"['admin-user-label_add']\"\n              type=\"success\"\n              @click=\"addSort\"\n              class=\"ml10\"\n              >添加分类</Button\n            >\n            <Button\n              ghost\n              type=\"primary\"\n              class=\"ml10\"\n              @click=\"userLabeSync()\"\n              >同步企业微信标签</Button>\n          </Col>\n        </Row>\n        <!-- 用户标签表格 -->\n        <Table\n          :columns=\"columns1\"\n          :data=\"labelLists\"\n          ref=\"table\"\n          class=\"mt25\"\n          :loading=\"loading\"\n          highlight-row\n          no-userFrom-text=\"暂无数据\"\n          no-filtered-userFrom-text=\"暂无筛选结果\"\n        >\n          <template slot-scope=\"{ row, index }\" slot=\"icons\">\n            <viewer>\n              <div class=\"tabBox_img\">\n                <img v-lazy=\"row.icon\" />\n              </div>\n            </viewer>\n          </template>\n          <template slot-scope=\"{ row, index }\" slot=\"action\">\n            <a @click=\"edit(row.id)\">修改</a>\n            <Divider type=\"vertical\" />\n            <a @click=\"del(row, '删除标签', index)\">删除</a>\n          </template>\n        </Table>\n        <div class=\"acea-row row-right page\">\n          <Page\n            :total=\"total\"\n            show-elevator\n            show-total\n            @on-change=\"pageChange\"\n            :page-size=\"labelFrom.limit\"\n          />\n        </div>\n      </Card>\n    </Col>\n  </Row>\n</div>\n", null]}