{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_img.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    export default {\n        name: 'c_button_img',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            }\n        },\n        data () {\n            return {\n                defaults: {},\n                configData: {},\n\t\t\t\tcurrent:0,\n\t\t\t\tlist:[]\n            }\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n\t\t\t\t\tthis.getBnt(nVal);\n                },\n                deep: true\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj;\n                this.configData = this.configObj[this.configNme];\n\t\t\t\tthis.getBnt(this.defaults);\n            })\n        },\n        methods: {\n\t\t\ttap(index){\n\t\t\t\tthis.current = index;\n\t\t\t\tthis.configData.tabVal = index;\n\t\t\t},\n\t\t\tgetBnt(nVal){\n\t\t\t\tlet obj = [\n\t\t\t\t\t{\n\t\t\t\t\t\turl: require('@/assets/images/cart2.png'),\n\t\t\t\t\t\twidth: 24,\n\t\t\t\t\t\theight: 24\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\turl: require('@/assets/images/cart3.png'),\n\t\t\t\t\t\twidth: 24,\n\t\t\t\t\t\theight: 24\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t\tif(nVal.bntStyleConfig.typeFrom == 'bnt'){\n\t\t\t\t\tthis.list = obj\n\t\t\t\t}else{\n\t\t\t\t\tif(nVal.styleConfig.tabVal == 0 || nVal.styleConfig.tabVal == 4){\n\t\t\t\t\t\tthis.list = [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\turl: require('@/assets/images/cart1.png'),\n\t\t\t\t\t\t\t\twidth: 42,\n\t\t\t\t\t\t\t\theight: 24\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\turl: require('@/assets/images/cart2.png'),\n\t\t\t\t\t\t\t\twidth: 24,\n\t\t\t\t\t\t\t\theight: 24\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\turl: require('@/assets/images/cart3.png'),\n\t\t\t\t\t\t\t\twidth: 24,\n\t\t\t\t\t\t\t\theight: 24\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.current = this.current == 2?1:this.current\n\t\t\t\t\t\tthis.list = obj\n\t\t\t\t\t}\n\t\t\t\t\tnVal.bntStyleConfig.tabVal = this.current;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n    }\n", null]}