{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=template&id=040cd542&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751012477533}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"sales-stats-container\" },\n    [\n      _c(\"div\", { staticClass: \"page-header\" }, [\n        _vm._m(0),\n        _c(\n          \"div\",\n          { staticClass: \"header-right\" },\n          [\n            _c(\n              \"Button\",\n              {\n                attrs: { type: \"success\", icon: \"ios-download\" },\n                on: { click: _vm.exportSales }\n              },\n              [_vm._v(\"导出统计\")]\n            )\n          ],\n          1\n        )\n      ]),\n      _c(\n        \"div\",\n        { staticClass: \"filter-container\" },\n        [\n          _c(\n            \"Form\",\n            { attrs: { model: _vm.filterForm, inline: \"\" } },\n            [\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"门店：\" } },\n                [\n                  _c(\n                    \"Select\",\n                    {\n                      staticStyle: { width: \"200px\" },\n                      attrs: {\n                        placeholder: \"选择门店\",\n                        filterable: \"\",\n                        clearable: \"\"\n                      },\n                      model: {\n                        value: _vm.filterForm.store_id,\n                        callback: function($$v) {\n                          _vm.$set(_vm.filterForm, \"store_id\", $$v)\n                        },\n                        expression: \"filterForm.store_id\"\n                      }\n                    },\n                    _vm._l(_vm.storeList, function(store) {\n                      return _c(\n                        \"Option\",\n                        {\n                          key: store.value,\n                          attrs: { value: store.value, label: store.label }\n                        },\n                        [\n                          _vm._v(\n                            \"\\n            \" +\n                              _vm._s(store.label) +\n                              \"\\n          \"\n                          )\n                        ]\n                      )\n                    }),\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"日期范围：\" } },\n                [\n                  _c(\"DatePicker\", {\n                    staticStyle: { width: \"300px\" },\n                    attrs: {\n                      type: \"daterange\",\n                      \"split-panels\": \"\",\n                      placeholder: \"选择日期范围\"\n                    },\n                    on: { \"on-change\": _vm.handleDateChange },\n                    model: {\n                      value: _vm.dateRange,\n                      callback: function($$v) {\n                        _vm.dateRange = $$v\n                      },\n                      expression: \"dateRange\"\n                    }\n                  })\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                { attrs: { label: \"商品名称：\" } },\n                [\n                  _c(\"Input\", {\n                    staticStyle: { width: \"200px\" },\n                    attrs: {\n                      placeholder: \"输入商品名称\",\n                      search: \"\",\n                      \"enter-button\": \"\"\n                    },\n                    on: { \"on-search\": _vm.getSalesStatistics },\n                    model: {\n                      value: _vm.filterForm.keyword,\n                      callback: function($$v) {\n                        _vm.$set(_vm.filterForm, \"keyword\", $$v)\n                      },\n                      expression: \"filterForm.keyword\"\n                    }\n                  })\n                ],\n                1\n              ),\n              _c(\n                \"FormItem\",\n                [\n                  _c(\n                    \"Button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: { click: _vm.getSalesStatistics }\n                    },\n                    [_vm._v(\"查询\")]\n                  ),\n                  _c(\n                    \"Button\",\n                    {\n                      staticStyle: { \"margin-left\": \"8px\" },\n                      on: { click: _vm.resetFilter }\n                    },\n                    [_vm._v(\"重置\")]\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\"Table\", {\n        attrs: {\n          loading: _vm.tableLoading,\n          columns: _vm.columns,\n          data: _vm.tableData,\n          border: \"\",\n          \"highlight-row\": \"\"\n        }\n      }),\n      _c(\n        \"div\",\n        { staticClass: \"pagination-container\" },\n        [\n          _c(\"Page\", {\n            attrs: {\n              total: _vm.total,\n              current: _vm.currentPage,\n              \"page-size\": _vm.pageSize,\n              \"page-size-opts\": [20, 50, 100, 200],\n              \"show-total\": \"\",\n              \"show-sizer\": \"\",\n              \"show-elevator\": \"\"\n            },\n            on: {\n              \"update:current\": function($event) {\n                _vm.currentPage = $event\n              },\n              \"on-change\": _vm.handleCurrentChange,\n              \"on-page-size-change\": _vm.handleSizeChange\n            }\n          })\n        ],\n        1\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"header-left\" }, [\n      _c(\"h2\", { staticClass: \"page-title\" }, [_vm._v(\"商品销售统计\")]),\n      _c(\"p\", { staticClass: \"page-desc\" }, [\n        _vm._v(\"查看各门店商品销售量、销售金额和当前库存统计\")\n      ])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}