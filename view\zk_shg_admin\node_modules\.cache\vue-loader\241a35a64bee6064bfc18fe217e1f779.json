{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\offline\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\offline\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport { orderScanList, orderOfflineScan } from \"@/api/order\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  data() {\n    return {\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      thead: [\n        {\n          title: \"订单号\",\n          key: \"order_id\",\n        },\n        {\n          title: \"用户信息\",\n          key: \"nickname\",\n        },\n        {\n          title: \"实际支付\",\n          key: \"pay_price\",\n        },\n        {\n          title: \"优惠价格\",\n          key: \"true_price\",\n        },\n        {\n          title: \"支付时间\",\n          key: \"pay_time\",\n        },\n      ],\n      tbody: [],\n      loading: false,\n      total: 0,\n      animal: 1,\n      pagination: {\n        page: 1,\n        limit: 30,\n        order_id: \"\",\n        add_time: \"\",\n      },\n      options: timeOptions,\n      timeVal: [],\n      modal: false,\n      qrcode: null,\n      name: \"\",\n      spin: false,\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getOrderList();\n  },\n  methods: {\n    onchangeCode(e) {\n      this.animal = e;\n      this.qrcodeShow();\n    },\n    // 具体日期搜索()；\n    onchangeTime(e) {\n      this.pagination.page = 1;\n      this.timeVal = e;\n      this.pagination.add_time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n\t  this.getOrderList();\n    },\n    // 订单列表\n    getOrderList() {\n      this.loading = true;\n      orderScanList(this.pagination)\n        .then((res) => {\n          this.loading = false;\n          const { count, list } = res.data;\n          this.total = count;\n          this.tbody = list;\n        })\n        .catch((err) => {\n          this.loading = false;\n          this.$Message.error(err.msg);\n        });\n    },\n    // 分页\n    pageChange(index) {\n      this.pagination.page = index;\n      this.getOrderList();\n    },\n    search() {\n      this.pagination.page = 1;\n      this.getOrderList();\n    },\n    // 查看二维码\n    qrcodeShow() {\n      this.spin = true;\n      orderOfflineScan({ type: this.animal })\n        .then((res) => {\n          this.spin = false;\n          this.qrcode = res.data;\n          this.modal = true;\n        })\n        .catch((err) => {\n          this.spin = false;\n          this.$Message.error(err.msg);\n        });\n    },\n  },\n};\n", null]}