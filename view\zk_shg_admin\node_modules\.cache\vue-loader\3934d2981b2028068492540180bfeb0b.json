{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_classify.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_classify.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\timport { cascaderListApi } from \"@/api/product\";\n    export default {\n        name: 'c_classify',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            },\n            number: {\n                type: null\n            },\n        },\n        data () {\n            return {\n                defaults: {},\n                configData: {},\n\t\t\t\tprops: { emitPath: false, multiple: true },\n\t\t\t\ttreeSelect: []\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj\n                this.configData = this.configObj[this.configNme]\n\t\t\t\tthis.goodsCategory();\n            })\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n                },\n                deep: true\n            }\n        },\n        methods: {\n\t\t\tsliderChange(){\n\t\t\t\tthis.$emit('getConfig',{ name: 'classlfy'})\n\t\t\t},\n\t\t\tgoodsCategory() {\n\t\t\t  cascaderListApi(1)\n\t\t\t    .then((res) => {\n\t\t\t      this.treeSelect = res.data;\n\t\t\t    })\n\t\t\t    .catch((res) => {\n\t\t\t      this.$Message.error(res.msg);\n\t\t\t    });\n\t\t\t}\n        }\n    }\n", null]}