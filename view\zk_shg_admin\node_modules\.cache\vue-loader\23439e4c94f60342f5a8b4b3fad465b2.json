{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue?vue&type=template&id=65e1da4a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue", "mtime": 1690191972000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 优惠券列表 -->\n    <div>\n        <Modal v-model=\"isTemplate\"\n               title=\"优惠券列表\"\n               width=\"60%\"\n               @on-ok=\"ok\"\n               @on-cancel=\"cancel\">\n            <Table :columns=\"columns\" :data=\"couponList\" ref=\"table\" class=\"mt25\"\n                   :loading=\"loading\" highlight-row\n                   @on-select=\"handleSelectRow\" @on-select-cancel=\"handleCancelRow\"\n                   @on-select-all=\"handleSelectAll\" @on-select-all-cancel=\"handleSelectAll\"\n\t\t\t\t\t\t\t\t\t @on-selection-change=\"changeCheckbox\"\n                   no-userFrom-text=\"暂无数据\"\n                   no-filtered-userFrom-text=\"暂无筛选结果\">\n\t\t\t\t\t\t\t\t<template slot-scope=\"{ row }\" slot=\"coupon_price\">\n\t\t\t\t\t\t\t\t   <span v-if=\"row.coupon_type==1\">{{row.coupon_price}}元</span>\n\t\t\t\t\t\t\t\t\t <span v-if=\"row.coupon_type==2\">{{parseFloat(row.coupon_price)/10}}折（{{row.coupon_price.toString().split(\".\")[0]}}%）</span>\n\t\t\t\t\t\t\t\t</template>\t \n                <template slot-scope=\"{ row, index }\" slot=\"count\">\n                    <span v-if=\"row.is_permanent\">不限量</span>\n                    <div v-else>\n                        <span class=\"fa\">发布：{{row.total_count}}</span>\n                        <span class=\"sheng\">剩余：{{row.remain_count}}</span>\n                    </div>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"start_time\">\n                    <div v-if=\"row.start_time\">\n                        {{row.start_time | formatDate}} - {{row.end_time | formatDate}}\n                    </div>\n                    <span v-else>不限时</span>\n                </template>\n                <template slot-scope=\"{ row }\" slot=\"type\">\n                    <span v-if=\"row.type === 1\">品类券</span>\n                    <span v-else-if=\"row.type === 2\">商品券</span>\n                    <span v-else-if=\"row.type === 3\">会员券</span>\n                    <span v-else>通用券</span>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"status\">\n                    <Tag color=\"blue\" v-show=\"row.status===1\">正常</Tag>\n                    <Tag color=\"gold\" v-show=\"row.status===0\">未开启</Tag>\n                    <Tag color=\"red\" v-show=\"row.status=== -1\">已失效</Tag>\n                </template>\n            </Table>\n            <div class=\"acea-row row-right page\">\n                <Page :total=\"total\" show-elevator show-total @on-change=\"receivePageChange\"\n                      :page-size=\"tableFrom.limit\"/>\n            </div>\n        </Modal>\n    </div>\n", null]}