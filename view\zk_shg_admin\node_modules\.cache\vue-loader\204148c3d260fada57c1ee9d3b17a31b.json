{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\goodPool\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\goodPool\\index.vue", "mtime": 1712136182752}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport { AlibabaImportGoods,getAlibabaGoodList,getAlibabaCategoryList } from \"@/api/vop\";\nimport {\ncascaderListApi,\n} from '@/api/product';\nexport default {\n  name: \"goodPool\",\n  data() {\n    return {\n      skuId:'',\n      cate_id:'',\n      data2: [],\n      batchModal: false, // 分类modal\n\n      data1: [], // 分类数组\n      props: { emitPath: false, multiple: false, checkStrictly: true },\n      total: 0,\n      loading: false,\n      columns: [\n        {\n          title: \"ID\",\n          key: \"openOfferId\",\n          width: 80,\n        },\n        {\n          title: \"名称\",\n          slot: \"name\",\n          minWidth: 180,\n        },\n        {\n          title: \"公司相关信息\",\n          slot: \"companyInfo\",\n          minWidth: 60,\n        },\n        {\n          title: \"价格\",\n          slot: \"price\",\n          minWidth: 60,\n        },\n        {\n          title: \"商品评价分\",\n          slot: \"qualityEvaluation\",\n          minWidth: 60,\n        },\n        {\n          title: \"商品图片\",\n          slot: \"image\",\n          minWidth: 50,\n        },\n        {\n          title: \"操作\",\n          slot: \"createModalFrame\",\n          fixed: \"right\",\n          width: 120,\n        },\n      ],\n      tabList: [],\n      treeData: {\n        withdrawal: [\n          {\n            title: \"48小时发货\",\n            value: 'shipIn48Hours',\n          },\n          {\n            title: \"7天包换\",\n            value: 'freeExchange7days',\n          },\n          {\n            title: \"实力商家\",\n            value: 'powerMerchant',\n          },\n          {\n            title: \"跨境潜力商品\",\n            value: 'crossPotential',\n          },\n          {\n            title: \"批发团商品\",\n            value: 'ttpft',\n          },\n          {\n            title: \"精选货源商品\",\n            value: 'jxhy',\n          }\n        ]\n      },\n      formValidate: {\n        filter: \"\",\n        categoryIds: \"\",\n        keywords: \"食品\",\n        priceStart:0,\n        priceEnd:0,\n        quantityBegin:1,\n        pageSize: 20,\n        pageNum: 1\n      }\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  mounted() {\n    // 初始化\n    this.getInit();\n  },\n  methods: {\n    // 导入商品库\n    dao(skuId) {\n      this.skuId = skuId\n      this.batchModal = true\n    },\n    saveBatch() {\n      this.loading = true;\n      AlibabaImportGoods({\n        skuId:this.skuId,\n        cateId:this.cate_id\n      })\n      .then(async () => {\n        this.loading = false;\n        this.batchModal = false;\n        this.cate_id = [];\n        this.$Message.success('同步成功');\n      })\n      .catch((res) => {\n        this.loading = false;\n        this.$Message.error(res.msg);\n      });\n    },\n    getInit() {\n      // 设置分页\n      this.formValidate.pageNum = 1;\n      // 获取商品分类\n      getAlibabaCategoryList()\n      .then(async (res) => {\n          this.data1 = res.data\n      })\n      .catch((res) => {\n        this.loading = false;\n        this.$Message.error(res.msg);\n      });\n      // 获取列表\n      this.getList();\n      // 获取商品分类2\n      cascaderListApi(1)\n        .then((res) => {\n          this.data2 = res.data;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 选择\n    selChange() {\n      this.formValidate.pageNum = 1;\n      this.getList();\n    },\n    reset(){\n      this.formValidate = {\n        filter: \"\",\n        categoryIds: \"\",\n        keywords: \"食品\",\n        quantityBegin:1,\n        priceStart:0,\n        priceEnd:0,\n        pageSize: 20,\n        pageNum: 1\n      };\n      this.getList();\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      getAlibabaGoodList(this.formValidate)\n        .then(async (res) => {\n          let data = res.data;\n          if(data.success) {\n            this.tabList = data.result ? data.result : [];\n            this.total = data.pageInfo.totalRecords;\n          } else {\n            this.tabList = [];\n            this.total = 0;\n          }\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.formValidate.pageNum = index;\n      this.getList();\n    }\n  },\n};\n", null]}