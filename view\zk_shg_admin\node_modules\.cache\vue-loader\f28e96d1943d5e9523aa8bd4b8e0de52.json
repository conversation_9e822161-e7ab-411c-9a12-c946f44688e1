{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue", "mtime": 1716618646000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport uploadPictures from \"@/components/uploadPictures\";\nimport OperationFloorModal from \"@/components/hotpotModal\";\nexport default {\n  name: \"c_one_pictrue\",\n  components: {\n    uploadPictures,\n    OperationFloorModal,\n  },\n  props: {\n    configObj: {\n      type: Object,\n    },\n    configNme: {\n      type: String,\n    },\n  },\n  data() {\n    return {\n      defaults: {},\n      configData: {},\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      modalPic: false,\n      isChoice: \"单选\",\n      imgAreaData: [], //热区数据\n    };\n  },\n  watch: {\n    configObj: {\n      handler(nVal, oVal) {\n        this.defaults = nVal;\n        // this.configData = nVal[this.configNme];\n        this.$set(this, 'imgAreaData', nVal[this.configNme].list)\n      },\n      deep: true,\n    },\n  },\n  mounted() {\n    this.$nextTick(() => {\n      console.log(this.configObj, \"this.defaults\");\n      this.defaults = this.configObj;\n      this.configData = this.configObj[this.configNme];\n      this.imgAreaData = this.configObj.picStyle.list;\n    });\n  },\n  methods: {\n    bindDelete() {\n      this.configData.url = \"\";\n    },\n    // 点击图文封面\n    modalPicTap(title) {\n      this.modalPic = true;\n    },\n    // 获取图片信息\n    getPic(pc) {\n      this.$nextTick(() => {\n        this.configData.url = pc.att_dir;\n        this.modalPic = false;\n      });\n    },\n    openFloorModal() {\n      // 如果配置数据中有url，则显示热点图对话框\n      if (this.configData.url) this.$refs.hotpot.dialogVisible = true;\n    },\n    /**\n     * 处理区域数据\n     * @param {Object} areaData - 区域数据对象\n     */\n    handleAreaData(areaData) {\n      // 打印保存的数据\n      this.configData.list = areaData;\n      console.log(\"保存的数据\", areaData);\n    },\n  },\n};\n", null]}