{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue", "mtime": 1717551943000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport vuedraggable from 'vuedraggable';\nimport storeLabelList from \"@/components/storeLabelList\";\nimport goodsList from '@/components/goodsList';\nimport uploadPictures from '@/components/uploadPictures';\nimport { brandList, cascaderListApi } from \"@/api/product\";\nexport default {\n  name: 'c_promotion',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    },\n    index: {\n      type: null\n    }\n  },\n  components: {\n    draggable: vuedraggable,\n    storeLabelList: storeLabelList,\n    goodsList: goodsList,\n    uploadPictures: uploadPictures\n  },\n  data: function data() {\n    return {\n      props: {\n        emitPath: false,\n        multiple: true\n      },\n      defaults: {},\n      configData: {},\n      itemObj: {},\n      storeLabelShow: false,\n      modals: false,\n      modalPic: false,\n      isChoice: '单选',\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      typeList: [{\n        activeValue: 1,\n        title: '指定商品'\n      }, {\n        activeValue: 2,\n        title: '指定品牌'\n      }, {\n        activeValue: 3,\n        title: '指定分类'\n      }, {\n        activeValue: 4,\n        title: '商品标签'\n      }],\n      brandData: [],\n      treeSelect: [],\n      tabIndex: 1\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.defaults = _this.configObj;\n      _this.configData = _this.configObj[_this.configNme];\n\n      _this.getBrandList();\n\n      _this.goodsCategory();\n    });\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n        this.tabIndex = nVal.styleConfig.tabVal;\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 点击图文封面\n    modalPicTap: function modalPicTap(title) {\n      this.modalPic = true;\n    },\n    bindPicDelete: function bindPicDelete() {\n      this.configData.list[this.configData.tabCur].image = '';\n    },\n    // 获取图片信息\n    getPic: function getPic(pc) {\n      var _this2 = this;\n\n      this.$nextTick(function () {\n        _this2.configData.list[_this2.configData.tabCur].image = pc.att_dir;\n        _this2.modalPic = false;\n      });\n    },\n    getBrandList: function getBrandList() {\n      var _this3 = this;\n\n      brandList().then(function (res) {\n        _this3.brandData = res.data;\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    goodsCategory: function goodsCategory() {\n      var _this4 = this;\n\n      cascaderListApi(1).then(function (res) {\n        _this4.treeSelect = res.data;\n      }).catch(function (res) {\n        _this4.$Message.error(res.msg);\n      });\n    },\n    openGoods: function openGoods() {\n      this.modals = true;\n    },\n    //对象数组去重；\n    unique: function unique(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.id) && res.set(arr.id, 1);\n      });\n    },\n    getProductId: function getProductId(data) {\n      this.modals = false;\n      var list = this.configData.list[this.configData.tabCur].goodsList.list.concat(data);\n      this.configData.list[this.configData.tabCur].goodsList.list = this.unique(list);\n    },\n    cancel: function cancel() {\n      this.modals = false;\n    },\n    bindGoodDelete: function bindGoodDelete(index) {\n      this.configData.list[this.configData.tabCur].goodsList.list.splice(index, 1);\n    },\n    openStoreLabel: function openStoreLabel(row, index) {\n      this.storeLabelShow = true;\n      this.$refs.storeLabel.storeLabel(JSON.parse(JSON.stringify(row)));\n    },\n    closeStoreLabel: function closeStoreLabel(label) {\n      var list = this.configData.list[this.configData.tabCur].goodsLabel.list;\n      var index = list.indexOf(list.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      list.splice(index, 1);\n      this.getLabelId(list);\n    },\n    activeStoreData: function activeStoreData(storeDataLabel) {\n      this.storeLabelShow = false;\n      this.configData.list[this.configData.tabCur].goodsLabel.list = storeDataLabel;\n      this.getLabelId(storeDataLabel);\n    },\n    getLabelId: function getLabelId(storeDataLabel) {\n      var storeActiveIds = [];\n      storeDataLabel.forEach(function (item) {\n        storeActiveIds.push(item.id);\n      });\n      this.configData.list[this.configData.tabCur].goodsLabel.activeValue = storeActiveIds;\n      this.$emit('getConfig', {\n        name: 'goodsLabel'\n      });\n    },\n    // 标签弹窗关闭\n    storeLabelClose: function storeLabelClose() {\n      this.storeLabelShow = false;\n    },\n    addHotTxt: function addHotTxt() {\n      if (this.configData.list.length == 0) {\n        var storage = window.localStorage;\n        this.itemObj = JSON.parse(storage.getItem('itemObj'));\n\n        if (this.itemObj.link) {\n          this.itemObj.link.activeVal = 0;\n        }\n\n        this.itemObj.chiild[0].val = '首发新品';\n        this.itemObj.chiild[1].val = '最新出炉';\n        this.itemObj.tabVal = 0;\n        this.itemObj.selectConfig.activeValue = [];\n        this.itemObj.goodsLabel.activeValue = [];\n        this.itemObj.goodsLabel.list = [];\n        this.itemObj.goodsSort = 0;\n        this.itemObj.numConfig.val = 6;\n        this.itemObj.goodsList.list = [];\n        this.itemObj.productList.list = [];\n        this.configData.list.push(this.itemObj);\n      } else {\n        var obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]));\n\n        if (obj.chiild[0].empty) {\n          obj.chiild[0].val = '';\n          obj.chiild[1].val = '';\n        }\n\n        obj.tabVal = 0;\n        obj.selectConfig.activeValue = [];\n        obj.goodsLabel.activeValue = [];\n        obj.goodsLabel.list = [];\n        obj.goodsSort = 0;\n        obj.numConfig.val = 6;\n        obj.goodsList.list = [];\n        obj.productList.list = [];\n        this.configData.list.push(obj);\n      }\n    },\n    // 删除数组\n    bindDelete: function bindDelete(index) {\n      if (this.configData.list.length == 1) {\n        var itemObj = this.configData.list[0];\n        this.itemObj = itemObj;\n        var storage = window.localStorage;\n        storage.setItem('itemObj', JSON.stringify(itemObj));\n      }\n\n      this.configData.list.splice(index, 1);\n      this.configData.tabCur = 0;\n      this.$emit('getConfig', {\n        name: 'delete',\n        indexs: 0\n      });\n    },\n    activeBtn: function activeBtn(index) {\n      this.configData.tabCur = index; // this.$emit('getConfig', { name: 'product', indexs: index })\n    },\n    radioChange: function radioChange(e) {\n      this.$emit('getConfig', {\n        name: 'promotion',\n        values: e\n      });\n    },\n    // 品牌\n    brandChange: function brandChange() {\n      this.$emit('getConfig', {\n        name: 'brands'\n      });\n    },\n    //商品分类\n    sliderChange: function sliderChange(e) {\n      this.configData.list[this.configData.tabCur].selectConfig.activeValue = e;\n      this.$emit('getConfig', {\n        name: 'cascader',\n        values: e\n      });\n    },\n    tabChange: function tabChange(e) {\n      this.$emit('getConfig', e);\n    }\n  }\n};", null]}