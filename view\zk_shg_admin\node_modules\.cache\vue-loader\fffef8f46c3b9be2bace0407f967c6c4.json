{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\staffList\\menuChild.vue?vue&type=template&id=c20cf27e&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\staffList\\menuChild.vue", "mtime": 1650783676000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Submenu',{attrs:{\"name\":_vm.parentItem.department_id}},[_c('template',{slot:\"title\"},[_c('Icon',{attrs:{\"type\":\"ios-folder\",\"size\":\"15\",\"color\":\"#FFCA28\"}}),_c('span',[_vm._v(_vm._s(_vm.parentItem.name))])],1),_vm._l((_vm.parentItem.children),function(item){return [(item.children&&item.children.length!==0)?_c('side-menu-item',{key:'menu-'+item.name,attrs:{\"parent-item\":item}}):_c('menu-item',{key:'menu-'+item.name,attrs:{\"name\":item.department_id}},[_c('Icon',{attrs:{\"type\":\"ios-folder\",\"size\":\"15\",\"color\":\"#FFCA28\"}}),_c('span',[_vm._v(_vm._s(item.name)+\" (\"+_vm._s(item.count)+\")\")])],1)]})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}