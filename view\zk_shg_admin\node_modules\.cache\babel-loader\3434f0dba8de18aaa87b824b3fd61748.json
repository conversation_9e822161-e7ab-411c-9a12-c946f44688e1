{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\lottery\\addGoods.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\lottery\\addGoods.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport couponList from \"@/components/couponList\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport goodsList from \"@/components/goodsList/index\";\nimport freightTemplate from \"@/components/freightTemplate\";\nimport { changeListApi } from \"@/api/product\";\nexport default {\n  components: {\n    uploadPictures: uploadPictures,\n    goodsList: goodsList,\n    freightTemplate: freightTemplate,\n    couponList: couponList\n  },\n  data: function data() {\n    var _this = this;\n\n    return {\n      modalPic: false,\n      modals: false,\n      isChoice: \"单选\",\n      updateIds: [],\n      updateName: [],\n      goodsData: {\n        pic: \"\",\n        product_id: \"\",\n        img: \"\",\n        coverImg: \"\"\n      },\n      formValidate: {\n        type: 5,\n        //类型 1：未中奖2：积分  3:余额  4：红包 5:优惠券 6：站内商品\n        name: \"\",\n        //活动名称\n        num: 0,\n        //奖品数量\n        image: \"\",\n        //奖品图片\n        chance: 0,\n        //中奖权重\n        product_id: 0,\n        //商品id\n        coupon_id: 0,\n        //优惠券id\n        total: 0,\n        //奖品数量\n        prompt: \"\",\n        //提示语\n        goods_image: \"\",\n        //自用商品图\n        unique: \"\",\n        //商品规格\n        coupon_title: '' //优惠券名称\n\n      },\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: \"奖品名称\",\n          trigger: \"blur\"\n        }],\n        goods_image: [{\n          required: true,\n          message: \"请添加商品\",\n          trigger: \"blur\"\n        }],\n        // attr_image: [\n        //   {\n        //     required: true,\n        //     message: \"请添加商品规格\",\n        //     trigger: \"blur\",\n        //   },\n        // ],\n        num: [{\n          required: true,\n          type: \"number\",\n          message: \"请输入金额数量\",\n          trigger: \"blur\"\n        }],\n        chance: [{\n          required: true,\n          type: \"number\",\n          message: \"请输入奖品权重\",\n          trigger: \"blur\"\n        }],\n        image: [{\n          required: true,\n          message: \"请选择奖品图片\",\n          trigger: \"blur\"\n        }],\n        prompt: [{\n          required: true,\n          message: \"请输入提示语\",\n          trigger: \"blur\"\n        }]\n      },\n      couponName: [],\n      attrColumns: [{\n        width: 60,\n        align: 'center',\n        render: function render(h, _ref) {\n          var row = _ref.row;\n          return h('Radio', {\n            props: {\n              value: row.unique === _this.formValidate.unique\n            },\n            on: {\n              'on-change': function onChange() {\n                _this.attrModal = false;\n                _this.attrImage = row.image;\n                _this.formValidate.unique = row.unique;\n              }\n            }\n          });\n        }\n      }, {\n        title: \"图片\",\n        slot: \"image\",\n        width: 120,\n        align: \"center\"\n      }, {\n        title: \"规格\",\n        key: \"suk\",\n        align: \"center\",\n        minWidth: 120\n      }],\n      attrData: [],\n      attrModal: false,\n      attrImage: \"\" //自用商品规格图\n\n    };\n  },\n  props: {\n    editData: {\n      type: Object,\n      default: function _default() {}\n    }\n  },\n  watch: {\n    editData: function editData(data) {}\n  },\n  mounted: function mounted() {\n    var _this2 = this;\n\n    var keys = Object.keys(this.editData);\n    keys.forEach(function (item) {\n      _this2.formValidate[item] = _this2.editData[item];\n\n      if (item === 'coupon_title' && _this2.editData[item]) {\n        _this2.couponName.push({\n          title: _this2.editData[item],\n          id: _this2.editData.coupon_id\n        });\n      }\n    }); // this.getList();\n  },\n  methods: {\n    getCouponId: function getCouponId(e) {\n      this.formValidate.coupon_id = e.id;\n      this.formValidate.coupon_title = e.coupon_title;\n      var couponName = [];\n      couponName.push(e);\n      this.couponName = couponName;\n    },\n    handleSubmit: function handleSubmit(name) {\n      var _this3 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this3.$emit(\"addGoodsData\", _this3.formValidate);\n\n          _this3.$Message.success(\"添加成功\");\n        } else {\n          _this3.$Message.warning(\"请完善数据\");\n        }\n      });\n    },\n    // 获取单张图片信息\n    getPic: function getPic(pc) {\n      this.formValidate.image = pc.att_dir;\n      this.modalPic = false;\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap() {\n      this.modalPic = true;\n    },\n    cancel: function cancel() {\n      this.modals = false;\n    },\n    // 选择的商品\n    getProductId: function getProductId(productList) {\n      // if (productList.length > 1) {\n      //   this.$Message.warning(\"最多添加一个商品\");\n      //   return;\n      // }\n      this.formValidate.product_id = productList.id;\n      this.formValidate.goods_image = productList.image;\n      this.modals = false; // productList.forEach((value) => {\n      //   this.formValidate.product_id = value.product_id;\n      //   this.formValidate.goods_image = value.image;\n      // });\n\n      this.attrData = productList.attrValue;\n    },\n    removeGoods: function removeGoods() {\n      this.formValidate.product_id = \"\";\n      this.formValidate.goods_image = \"\";\n      this.removeAttr();\n    },\n    remove: function remove() {\n      this.formValidate.image = \"\";\n    },\n    // 添加优惠券\n    addCoupon: function addCoupon() {\n      this.$refs.couponTemplates.isTemplate = true;\n      this.$refs.couponTemplates.tableList();\n    },\n    handleClose: function handleClose(name) {\n      this.couponName.splice(0, 1);\n      this.formValidate.coupon_id = 0; // let index = this.couponName.indexOf(name);\n      // this.couponName.splice(index, 1);\n      //\n      // let couponIds = this.formValidate.coupon_id;\n      // couponIds.splice(index, 1);\n      // this.updateIds = couponIds;\n      // this.updateName = this.couponName;\n    },\n    // nameId(id, names) {\n    //   this.formValidate.coupon_id = id[0];\n    //   this.couponName = this.unique(names);\n    // },\n    //对象数组去重；\n    unique: function unique(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.id) && res.set(arr.id, 1);\n      });\n    },\n    removeAttr: function removeAttr() {\n      this.attrImage = '';\n      this.formValidate.unique = '';\n    },\n    callAttr: function callAttr() {\n      this.attrModal = true;\n    },\n    getList: function getList() {\n      var _this4 = this;\n\n      changeListApi().then(function (_ref2) {\n        var data = _ref2.data;\n        var list = data.list;\n\n        for (var i = 0; i < list.length; i++) {\n          if (list[i].id === _this4.formValidate.product_id) {\n            _this4.attrData = list[i].attrValue;\n\n            for (var j = 0; j < _this4.attrData.length; j++) {\n              if (_this4.attrData[j].unique === _this4.formValidate.unique) {\n                _this4.attrImage = _this4.attrData[j].image;\n                break;\n              }\n            }\n\n            break;\n          }\n        }\n      });\n    }\n  }\n};", null]}