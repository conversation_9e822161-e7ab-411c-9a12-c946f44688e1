{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue?vue&type=template&id=3ebb3761&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n  <Modal v-model=\"modals\" width=\"700\" scrollable footer-hide closable :title=\"titleFrom\" :z-index=\"1\"\n    @on-cancel=\"handleReset\" class-name=\"vertical-center-modal\">\n    <Form ref=\"formValidate\" :model=\"formValidate\" :label-width=\"110\" :rules=\"ruleValidate\" @submit.native.prevent>\n      <Col v-bind=\"grid\">\n      <FormItem label=\"上级品牌：\">\n        <Cascader :data=\"FromData\" placeholder=\"请选择上级品牌\" change-on-select v-model=\"formValidate.fid\"></Cascader>\n      </FormItem>\n      </Col>\n      <Col v-bind=\"grid\">\n      <FormItem label=\"品牌名称：\" prop=\"brand_name\">\n        <Input v-model=\"formValidate.brand_name\" maxlength='7' placeholder=\"请输入品牌名称\" prop=\"\"></Input>\n      </FormItem>\n      </Col>\n      <Col v-bind=\"grid\">\n      <FormItem label=\"品牌排序：\">\n        <!-- <Input\n                  v-model=\"formValidate.sort\"\n                  placeholder=\"请输入分类排序\"\n                  prop=\"\"\n          ></Input> -->\n        <InputNumber v-model=\"formValidate.sort\" :step=\"1\" placeholder=\"请输入品牌排序\" style=\"width: 100%;\"></InputNumber>\n      </FormItem>\n      </Col>\n      <Col v-bind=\"grid\">\n      <FormItem label=\"是否显示：\">\n        <i-switch v-model=\"formValidate.is_show\" size=\"large\" :true-value=\"1\" :false-value=\"0\">\n          <span slot=\"open\">开启</span>\n          <span slot=\"close\">关闭</span>\n        </i-switch>\n      </FormItem>\n      </Col>\n      <Col span=\"24\">\n      <div class=\"style-add\">\n      <Button class=\"mr14\" type=\"default\" @click=\"cancle\">取消</Button>\n      <Button type=\"primary\" @click=\"handleSubmit('formValidate')\">确认</Button>\n      </div>\n      </Col>\n    </Form>\n  </Modal>\n\n</div>\n", null]}