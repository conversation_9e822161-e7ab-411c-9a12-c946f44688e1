{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\record\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\record\\index.vue", "mtime": 1745374843360}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { userMemberCard, memberRecord, memberTab } from \"@/api/user\";\nimport { mapState } from \"vuex\";\nexport default {\n  name: \"card\",\n  data: function data() {\n    return {\n      treeSelect: [],\n      payList: [{\n        val: \"free\",\n        label: \"免费\"\n      }, {\n        val: \"weixin\",\n        label: \"微信\"\n      }, {\n        val: \"alipay\",\n        label: \"支付宝\"\n      }],\n      thead: [{\n        title: \"订单号\",\n        key: \"order_id\",\n        minWidth: 100\n      }, {\n        title: \"用户名\",\n        minWidth: 50,\n        ellipsis: true,\n        render: function render(h, params) {\n          return h(\"span\", params.row.user.nickname);\n        }\n      }, {\n        title: \"推广人\",\n        key: \"spread_name\",\n        minWidth: 40\n      }, {\n        title: \"手机号码\",\n        minWidth: 80,\n        render: function render(h, params) {\n          return h(\"span\", params.row.user.phone || \"--\");\n        }\n      }, {\n        title: \"会员类型\",\n        key: \"member_type\",\n        minWidth: 40\n      }, {\n        title: \"有效期限（天）\",\n        key: \"vip_day\",\n        minWidth: 50\n      }, {\n        title: \"支付金额（元）\",\n        key: \"pay_price\",\n        minWidth: 50\n      }, {\n        title: \"支付方式\",\n        key: \"pay_type\",\n        minWidth: 30\n      }, {\n        title: \"购买时间\",\n        key: \"pay_time\",\n        minWidth: 90\n      }, {\n        title: \"到期时间\",\n        minWidth: 90,\n        render: function render(h, params) {\n          return h(\"span\", params.row.overdue_time);\n        }\n      }],\n      tbody: [],\n      loading: false,\n      total: 0,\n      formValidate: {\n        name: \"\",\n        member_type: \"\",\n        pay_type: \"\",\n        add_time: \"\"\n      },\n      options: {\n        shortcuts: [{\n          text: \"今天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()));\n            return [start, end];\n          }\n        }, {\n          text: \"昨天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            return [start, end];\n          }\n        }, {\n          text: \"最近7天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 6)));\n            return [start, end];\n          }\n        }, {\n          text: \"最近30天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 29)));\n            return [start, end];\n          }\n        }, {\n          text: \"上月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            var day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, day)));\n            return [start, end];\n          }\n        }, {\n          text: \"本月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));\n            return [start, end];\n          }\n        }, {\n          text: \"本年\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)));\n            return [start, end];\n          }\n        }]\n      },\n      timeVal: [],\n      tablePage: {\n        page: 1,\n        limit: 15\n      }\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.sletab();\n    this.getMemberRecord();\n  },\n  methods: {\n    // 获取会员类型\n    sletab: function sletab() {\n      var _this = this;\n\n      memberTab().then(function (res) {\n        _this.treeSelect = res.data;\n      });\n    },\n    // 用户名搜索；\n    selChange: function selChange() {\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    //用户类型搜索；\n    userSearchs: function userSearchs() {\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    //支付方式搜索；\n    paySearchs: function paySearchs() {\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.add_time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    getMemberRecord: function getMemberRecord() {\n      var _this2 = this;\n\n      this.loading = true;\n      var data = {\n        page: this.tablePage.page,\n        limit: this.tablePage.limit,\n        member_type: this.formValidate.member_type,\n        pay_type: this.formValidate.pay_type,\n        add_time: this.formValidate.add_time,\n        name: this.formValidate.name\n      };\n      memberRecord(data).then(function (res) {\n        _this2.loading = false;\n        var _res$data = res.data,\n            list = _res$data.list,\n            count = _res$data.count;\n        _this2.tbody = list;\n        _this2.total = count;\n      }).catch(function (err) {\n        _this2.loading = false;\n\n        _this2.$Message.error(err.msg);\n      });\n    },\n    // 分页\n    pageChange: function pageChange(index) {\n      this.tablePage.page = index;\n      this.getMemberRecord();\n    }\n  }\n};", null]}