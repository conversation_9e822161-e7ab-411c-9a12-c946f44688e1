{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_comb_data.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_comb_data.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'c_comb_data',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    }\n  },\n  data: function data() {\n    return {\n      defaults: {},\n      configData: {}\n    };\n  },\n  created: function created() {\n    this.defaults = this.configObj;\n    this.configData = this.configObj[this.configNme];\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    getDaterange: function getDaterange(e) {\n      this.$emit('getConfig', {\n        type: 2,\n        val: e\n      });\n    },\n    radioChange: function radioChange(e) {\n      this.$emit('getConfig', {\n        type: 0,\n        val: e\n      });\n    },\n    radioDataChange: function radioDataChange(e) {\n      this.$emit('getConfig', {\n        type: 1,\n        val: e\n      });\n    }\n  }\n};", null]}