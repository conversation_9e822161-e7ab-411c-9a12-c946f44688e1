{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: \"c_button_style\",\n  props: {\n    configObj: {\n      type: Object,\n    },\n    configNme: {\n      type: String,\n    },\n  },\n  data() {\n    return {\n      defaults: {},\n      configData: {},\n      modals: false,\n      current: 0,\n      navBar: [\n        {\n          url: require(\"@/assets/images/tab02.png\"),\n          width: 220,\n          height: 24,\n        },\n        {\n          url: require(\"@/assets/images/tab03.png\"),\n          width: 220,\n          height: 24,\n        },\n        {\n          url: require(\"@/assets/images/tab01.png\"),\n          width: 220,\n          height: 24,\n        },\n      ],\n      signIn: [\n        {\n          url: require(\"@/assets/images/signIn01.png\"),\n          width: 220,\n          height: 64,\n        },\n        {\n          url: require(\"@/assets/images/signIn02.png\"),\n          width: 220,\n          height: 59,\n        },\n      ],\n      ranking: [\n        {\n          url: require(\"@/assets/images/ranking01.png\"),\n          width: 200,\n          height: 172,\n        },\n        {\n          url: require(\"@/assets/images/ranking02.png\"),\n          width: 200,\n          height: 167,\n        },\n      ],\n      coupon: [\n        {\n          url: require(\"@/assets/images/coupon01.png\"),\n          width: 220,\n          height: 69,\n        },\n        {\n          url: require(\"@/assets/images/coupon02.png\"),\n          width: 220,\n          height: 87,\n        },\n        {\n          url: require(\"@/assets/images/coupon03.png\"),\n          width: 220,\n          height: 62,\n        },\n        {\n          url: require(\"@/assets/images/coupon04.png\"),\n          width: 220,\n          height: 69,\n        },\n        {\n          url: require(\"@/assets/images/coupon05.png\"),\n          width: 220,\n          height: 49,\n        },\n      ],\n      pictureCube: [\n        {\n          url: require(\"@/assets/images/cube2.png\"),\n          width: 130,\n          height: 129,\n          count: 2,\n        },\n        {\n          url: require(\"@/assets/images/cube3.png\"),\n          width: 130,\n          height: 129,\n          count: 2,\n        },\n        {\n          url: require(\"@/assets/images/cube4.png\"),\n          width: 130,\n          height: 129,\n          count: 3,\n        },\n        {\n          url: require(\"@/assets/images/cube5.png\"),\n          width: 130,\n          height: 129,\n          count: 3,\n        },\n        {\n          url: require(\"@/assets/images/cube6.png\"),\n          width: 130,\n          height: 129,\n          count: 3,\n        },\n        {\n          url: require(\"@/assets/images/cube7.png\"),\n          width: 130,\n          height: 129,\n          count: 3,\n        },\n        {\n          url: require(\"@/assets/images/cube8.png\"),\n          width: 130,\n          height: 129,\n          count: 3,\n        },\n        {\n          url: require(\"@/assets/images/cube9.png\"),\n          width: 130,\n          height: 129,\n          count: 4,\n        },\n        {\n          url: require(\"@/assets/images/cube10.png\"),\n          width: 130,\n          height: 129,\n          count: 5,\n        },\n        {\n          url: require(\"@/assets/images/cube11.png\"),\n          width: 130,\n          height: 129,\n          count: 4,\n        },\n        {\n          url: require(\"@/assets/images/cube12.png\"),\n          width: 130,\n          height: 129,\n          count: 1,\n        },\n        {\n          url: require(\"@/assets/images/cube1.png\"),\n          width: 130,\n          height: 130,\n          count: 16,\n        },\n      ],\n      list: [],\n    };\n  },\n  watch: {\n    configObj: {\n      handler(nVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme];\n      },\n      deep: true,\n    },\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.defaults = this.configObj;\n      this.configData = this.configObj[this.configNme];\n      this.current = this.configData.tabVal;\n      switch (this.configData.type) {\n        case \"navBar\":\n          this.list = this.navBar;\n          break;\n        case \"signIn\":\n          this.list = this.signIn;\n          break;\n        case \"ranking\":\n          this.list = this.ranking;\n          break;\n        case \"coupon\":\n          this.list = this.coupon;\n          break;\n        case \"pictureCube\":\n          this.list = this.pictureCube;\n          break;\n      }\n    });\n  },\n  methods: {\n    tap(index) {\n      this.current = index;\n    },\n    styleTap() {\n      this.modals = true;\n    },\n    cancel() {\n      this.modals = false;\n    },\n    ok() {\n      this.modals = false;\n      this.configData.tabVal = this.current;\n      this.configData.count = this.list[this.current].count;\n      if (this.defaults.picStyle) {\n        this.defaults.picStyle.tabVal = 0;\n      }\n    },\n  },\n};\n", null]}