{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue?vue&type=template&id=045af22c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 城市配送区域 -->\n    <div>\n        <Modal\n                v-model=\"addressModal\"\n                title=\"选择可配送区域\"\n                width=\"50%\"\n                class=\"modal\"\n                :mask=\"true\"\n        >\n            <Row :gutter=\"24\" type=\"flex\">\n                <Col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\" class=\"item\">\n                    <div class=\"acea-row row-right row-middle\">\n                        <Checkbox v-model=\"iSselect\" @on-change=\"allCheckbox\">全选</Checkbox>\n                        <div class=\"empty\" @click=\"empty\">清空</div>\n                    </div>\n                </Col>\n            </Row>\n                <Row :gutter=\"24\" type=\"flex\" :loading=\"loading\">\n                    <Col :xl=\"6\" :lg=\"6\" :md=\"6\" :sm=\"8\" :xs=\"6\" class=\"item\" v-for=\"(item,index) in cityList\" :key=\"index\" v-if=\"item.isShow\">\n                        <div @mouseenter=\"enter(index)\" @mouseleave=\"leave()\" >\n                            <Checkbox v-model=\"item.checked\" :label=\"item.name\" @on-change=\"checkedClick(index)\">{{item.name}}</Checkbox><span class=\"red\">({{(item.count || 0) + '/' + item.childNum}})</span>\n                            <div class=\"city\" v-show=\"activeCity===index\">\n                                <div class=\"checkBox\">\n                                    <div class=\"arrow\"></div>\n                                        <div>\n                                            <Checkbox v-model=\"city.checked\" :label=\"city.name\" @on-change=\"primary(index,indexn)\" class=\"itemn\" v-for=\"(city,indexn) in item.children\" :key=\"indexn\" v-show=\"city.isShow\">{{city.name}}</Checkbox>\n                                        </div>\n                                </div>\n                            </div>\n                        </div>\n                    </Col>\n                </Row>\n            <div slot=\"footer\">\n                <Button @click=\"close\">取消</Button>\n                <Button type=\"primary\" @click=\"confirm\">确定</Button>\n            </div>\n            <Spin size=\"large\" fix v-if=\"loading\"></Spin>\n        </Modal>\n    </div>\n", null]}