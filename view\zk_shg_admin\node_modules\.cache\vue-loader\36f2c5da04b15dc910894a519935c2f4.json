{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue?vue&type=template&id=accaaf10&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue", "mtime": 1718158266000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 用户-付费会员-会员权益 -->\n    <div>\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n            <!-- 会员权益表格 -->\n            <Table\n                :columns=\"thead\"\n                :data=\"tbody\"\n                :loading=\"loading\"\n                highlight-row\n                no-userFrom-text=\"暂无数据\"\n                no-filtered-userFrom-text=\"暂无筛选结果\"\n            >\n                <template slot-scope=\"{ row }\" slot=\"image\">\n                    <div class=\"image-wrap\" v-viewer>\n                        <img v-lazy=\"row.image\" />\n                    </div>\n                </template>\n                <template slot-scope=\"{ row }\" slot=\"status\">\n                    <i-switch\n                        v-model=\"row.status\"\n                        :value=\"row.status\"\n                        :true-value=\"1\"\n                        :false-value=\"0\"\n                        size=\"large\"\n                        @on-change=\"statusChange(row)\"\n                    >\n                        <span slot=\"open\">启用</span>\n                        <span slot=\"close\">禁用</span>\n                    </i-switch>\n                </template>\n                <template slot-scope=\"{ row }\" slot=\"action\">\n                    <a @click=\"edit(row)\">编辑</a>\n                    <Divider type=\"vertical\" />\n                    <a @click=\"editRight(row)\">权益介绍</a>\n                </template>\n            </Table>\n            <div class=\"acea-row row-right page\">\n                <Page\n                    :total=\"total\"\n                    :current=\"page\"\n                    :page-size=\"limit\"\n                    show-elevator\n                    show-total\n                    @on-change=\"pageChange\"\n                />\n            </div>\n        </Card>\n        <Modal v-model=\"modal3\" title=\"权益介绍\" @on-ok=\"saveMemberContent\">\n          <WangEditor\n            style=\"width: 100%\"\n            :content=\"content\"\n            @editorContent=\"getEditorContent\"\n          ></WangEditor>\n        </Modal>\n        <!-- 编辑会员权益模态框 -->\n        <Modal v-model=\"modal1\" title=\"编辑会员权益\" footer-hide>\n            <Form ref=\"form\" :model=\"form\" :rules=\"rules\" :label-width=\"80\">\n                <Input v-model=\"form.id\" class=\"display-add\"></Input>\n                <Input v-model=\"form.status\" class=\"display-add\"></Input>\n                <Input v-model=\"form.right_type\" class=\"display-add\"></Input>\n                <FormItem label=\"权益名称\" prop=\"title\">\n                    <Input v-model=\"form.title\" placeholder=\"请输入权益名称\" disabled></Input>\n                </FormItem>\n                <FormItem label=\"展示名称\" prop=\"show_title\">\n                    <Input v-model=\"form.show_title\" maxlength=\"8\" placeholder=\"请输入展示名称\"></Input>\n                </FormItem>\n                <FormItem label=\"权益图标\" prop=\"image\">\n                    <div class=\"image-group\" @click=\"callImage\">\n                        <img v-if=\"form.image\" v-lazy=\"form.image\" />\n                        <Icon v-else type=\"ios-camera-outline\" size=\"26\" />\n                    </div>\n                    <Input v-model=\"form.image\" class=\"display-add\"></Input>\n                </FormItem>\n                <FormItem label=\"权益简介\" prop=\"show_title\">\n                    <Input\n                        v-model=\"form.explain\"\n                        type=\"textarea\"\n                        maxlength=\"8\"\n                        :autosize=\"{ minRows: 2, maxRows: 10 }\"\n                        placeholder=\"请输入权益简介\"\n                    ></Input>\n                </FormItem>\n                <FormItem\n                    v-show=\"form.right_type !== 'coupon' && form.right_type !== 'vip_price'\"\n                    :label=\"\n                        form.right_type === 'offline' ||\n                        form.right_type === 'express' ||\n                        form.right_type === 'vip_price'\n                            ? '折扣数(%)'\n                            : '积分倍数'\n                    \"\n                    prop=\"number\"\n                >\n                    <InputNumber v-model=\"form.number\" :min=\"1\" :max=\"form.right_type === 'offline'?100:10000\"></InputNumber>\n                </FormItem>\n                <FormItem>\n                    <Button type=\"primary\" @click=\"formSubmit('form')\">提交</Button>\n                </FormItem>\n            </Form>\n        </Modal>\n        <Modal v-model=\"modal2\" width=\"960px\" scrollable footer-hide closable title=\"选择权益图标\">\n            <uploadPictures\n                v-show=\"modal2\"\n                isChoice=\"单选\"\n                :gridBtn=\"gridBtn\"\n                :gridPic=\"gridPic\"\n                @getPic=\"getPic\"\n            ></uploadPictures>\n        </Modal>\n    </div>\n", null]}