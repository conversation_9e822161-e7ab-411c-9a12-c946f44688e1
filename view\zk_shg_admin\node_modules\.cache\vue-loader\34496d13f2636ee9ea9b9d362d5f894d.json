{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\record\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\record\\index.vue", "mtime": 1745374843360}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userMemberCard, memberRecord, memberTab } from \"@/api/user\";\nimport { mapState } from \"vuex\";\n\nexport default {\n  name: \"card\",\n  data() {\n    return {\n      treeSelect: [],\n      payList: [\n        {\n          val: \"free\",\n          label: \"免费\",\n        },\n        {\n          val: \"weixin\",\n          label: \"微信\",\n        },\n        {\n          val: \"alipay\",\n          label: \"支付宝\",\n        },\n      ],\n      thead: [\n        {\n          title: \"订单号\",\n          key: \"order_id\",\n          minWidth: 100,\n        },\n        {\n          title: \"用户名\",\n          minWidth: 50,\n          ellipsis: true,\n          render: (h, params) => {\n            return h(\"span\", params.row.user.nickname);\n          },\n        },\n        {\n          title: \"推广人\",\n          key: \"spread_name\",\n          minWidth: 40,\n        },\n        {\n          title: \"手机号码\",\n          minWidth: 80,\n          render: (h, params) => {\n            return h(\"span\", params.row.user.phone || \"--\");\n          },\n        },\n        {\n          title: \"会员类型\",\n          key: \"member_type\",\n          minWidth: 40,\n        },\n        {\n          title: \"有效期限（天）\",\n          key: \"vip_day\",\n          minWidth: 50,\n        },\n        {\n          title: \"支付金额（元）\",\n          key: \"pay_price\",\n          minWidth: 50,\n        },\n        {\n          title: \"支付方式\",\n          key: \"pay_type\",\n          minWidth: 30,\n        },\n        {\n          title: \"购买时间\",\n          key: \"pay_time\",\n          minWidth: 90,\n        },\n        {\n          title: \"到期时间\",\n          minWidth: 90,\n          render: (h, params) => {\n            return h(\"span\", params.row.overdue_time);\n          },\n        },\n      ],\n      tbody: [],\n      loading: false,\n      total: 0,\n      formValidate: {\n        name: \"\",\n        member_type: \"\",\n        pay_type: \"\",\n        add_time: \"\",\n      },\n      options: {\n        shortcuts: [\n          {\n            text: \"今天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                new Date(\n                  new Date().getFullYear(),\n                  new Date().getMonth(),\n                  new Date().getDate()\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"昨天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 1\n                  )\n                )\n              );\n              end.setTime(\n                end.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 1\n                  )\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"最近7天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 6\n                  )\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"最近30天\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(\n                    new Date().getFullYear(),\n                    new Date().getMonth(),\n                    new Date().getDate() - 29\n                  )\n                )\n              );\n              return [start, end];\n            },\n          },\n\t\t  {\n\t\t    text: \"上月\",\n\t\t    value() {\n\t\t      const end = new Date();\n\t\t      const start = new Date();\n\t\t  \tconst day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n\t\t      start.setTime(\n\t\t        start.setTime(\n\t\t          new Date(new Date().getFullYear(), new Date().getMonth()-1, 1)\n\t\t        )\n\t\t      );\n\t\t  \tend.setTime(\n\t\t  \t  end.setTime(\n\t\t  \t    new Date(new Date().getFullYear(), new Date().getMonth()-1, day)\n\t\t  \t  )\n\t\t  \t);\n\t\t      return [start, end];\n\t\t    },\n\t\t  },\n          {\n            text: \"本月\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(\n                  new Date(new Date().getFullYear(), new Date().getMonth(), 1)\n                )\n              );\n              return [start, end];\n            },\n          },\n          {\n            text: \"本年\",\n            value() {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(\n                start.setTime(new Date(new Date().getFullYear(), 0, 1))\n              );\n              return [start, end];\n            },\n          },\n        ],\n      },\n      timeVal: [],\n      tablePage: {\n        page: 1,\n        limit: 15,\n      },\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n\tthis.sletab()\n    this.getMemberRecord();\n  },\n  methods: {\n\t// 获取会员类型\n\tsletab(){\n\t\tmemberTab().then(res=>{\n\t\t\tthis.treeSelect = res.data\n\t\t})\n\t},\n    // 用户名搜索；\n    selChange() {\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    //用户类型搜索；\n    userSearchs() {\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    //支付方式搜索；\n    paySearchs() {\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.add_time = this.timeVal[0]\n        ? this.timeVal.join(\"-\")\n        : \"\";\n      this.tablePage.page = 1;\n      this.getMemberRecord();\n    },\n    getMemberRecord() {\n      this.loading = true;\n      let data = {\n        page: this.tablePage.page,\n        limit: this.tablePage.limit,\n        member_type: this.formValidate.member_type,\n        pay_type: this.formValidate.pay_type,\n        add_time: this.formValidate.add_time,\n        name: this.formValidate.name,\n      };\n      memberRecord(data)\n        .then((res) => {\n          this.loading = false;\n          const { list, count } = res.data;\n          this.tbody = list;\n          this.total = count;\n        })\n        .catch((err) => {\n          this.loading = false;\n          this.$Message.error(err.msg);\n        });\n    },\n    // 分页\n    pageChange(index) {\n      this.tablePage.page = index;\n      this.getMemberRecord();\n    },\n  },\n};\n", null]}