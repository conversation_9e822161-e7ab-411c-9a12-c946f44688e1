{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue?vue&type=style&index=0&id=3ebb3761&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productBrand\\components\\menusFrom.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n .style-add {\n display:flex;\n justify-content: flex-end;\n }\n .mr14 {\n  margin-right:14px\n }\n  /deep/.ivu-cascader-menu{\n    height: auto;\n    max-height 165px !important\n  }\n\n  /deep/.ivu-cascader-menu-item{\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n    /deep/.ivu-select-item{\n      max-width 294px\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n.trees-coadd {\n  width: 100%;\n  height: 500px;\n  border-radius: 4px;\n  overflow: hidden;\n}\n.scollhide {\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n  margin-left: 18px;\n  padding: 10px 0 10px 0;\n  box-sizing: border-box;\n}\n.content {\n  font-size: 12px;\n}\n\n.time {\n  font-size: 12px;\n  color: #2d8cf0;\n}\n\n.icons-item {\n  float: left;\n  margin: 6px 6px 6px 0;\n  width: 53px;\n  text-align: center;\n  list-style: none;\n  cursor: pointer;\n  height: 50px;\n  color: #5c6b77;\n  transition: all 0.2s ease;\n  position: relative;\n  padding-top: 10px;\n\tfont-size: 20px;\n}\n.search-rule {\n  display: flex;\n  align-items: center;\n  padding: 10px;\n  background-color: #f2f2f2;\n}\n.rule {\n  display: flex;\n  flex-wrap: wrap;\n  max-height: 700px;\n  overflow: scroll;\n}\n/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/\n.rule::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n  background-color: #f5f5f5;\n}\n\n/*定义滚动条轨道 内阴影+圆角*/\n.rule::-webkit-scrollbar-track {\n  border-radius: 4px;\n  background-color: #f5f5f5;\n}\n\n/*定义滑块 内阴影+圆角*/\n.rule::-webkit-scrollbar-thumb {\n  border-radius: 4px;\n  background-color: #555;\n}\n.rule-list {\n  background-color: #f2f2f2;\n  width: 32%;\n  margin: 5px;\n  border-radius: 3px;\n  padding: 10px;\n  color: #333;\n  cursor: pointer;\n  transition: all 0.1s;\n  overflow: hidden;\n}\n.rule-list:hover {\n  background-color: #c5d1dd;\n}\n.rule-list div {\n  white-space: nowrap;\n}\n.select-rule {\n  background-color: #c5d1dd;\n}\n.add {\n  display: flex;\n  align-items: center;\n}\n", null]}