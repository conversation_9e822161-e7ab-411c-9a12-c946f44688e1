{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\queueList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\queueList.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { queueIndex, deliveryLog, queueAgain as _queueAgain, queueDel as _queueDel, batchOrderDelivery, stopWrongQueue } from '@/api/order';\nimport { mapState } from 'vuex';\nexport default {\n  data: function data() {\n    return {\n      modal: false,\n      columns1: [{\n        title: 'ID',\n        key: 'id'\n      }, {\n        title: '操作时间',\n        key: 'add_time'\n      }, {\n        title: '发货单数',\n        key: 'total_num'\n      }, {\n        title: '成功发货单数',\n        key: 'success_num'\n      }, {\n        title: '发货类型',\n        key: 'title'\n      }, {\n        title: '状态',\n        key: 'status_cn'\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        width: 150,\n        align: 'center'\n      }],\n      data1: [],\n      page1: {\n        total: 0,\n        // 总条数\n        pageNum: 1,\n        // 当前页\n        pageSize: 10 // 每页显示条数\n\n      },\n      formValidate: {\n        type: '',\n        status: '',\n        data: ''\n      },\n      options: {\n        shortcuts: [{\n          text: '今天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()));\n            return [start, end];\n          }\n        }, {\n          text: '昨天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            return [start, end];\n          }\n        }, {\n          text: '最近7天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 6)));\n            return [start, end];\n          }\n        }, {\n          text: '最近30天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 29)));\n            return [start, end];\n          }\n        }, {\n          text: \"上月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            var day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, day)));\n            return [start, end];\n          }\n        }, {\n          text: '本月',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));\n            return [start, end];\n          }\n        }, {\n          text: '本年',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)));\n            return [start, end];\n          }\n        }]\n      },\n      timeVal: [],\n      typeList: [// {\n      //     label: '批量发放用户优惠券',\n      //     value: '1'\n      // },\n      // {\n      //     label: '批量设置用户分组',\n      //     value: '2'\n      // },\n      // {\n      //     label: '批量设置用户标签',\n      //     value: '3'\n      // },\n      // {\n      //     label: '批量下架商品',\n      //     value: '4'\n      // },\n      // {\n      //     label: '批量删除商品规格',\n      //     value: '5'\n      // },\n      {\n        label: '批量删除订单',\n        value: '6'\n      }, {\n        label: '批量手动发货',\n        value: '7'\n      }, {\n        label: '批量打印电子面单',\n        value: '8'\n      }, {\n        label: '批量配送',\n        value: '9'\n      }, {\n        label: '批量虚拟发货',\n        value: '10'\n      }],\n      statusList: [{\n        label: '未处理',\n        value: '0'\n      }, {\n        label: '处理中',\n        value: '1'\n      }, {\n        label: '已完成',\n        value: '2'\n      }, {\n        label: '处理失败',\n        value: '3'\n      }],\n      columns2: [{\n        title: '订单ID',\n        key: 'order_id'\n      }, {\n        title: '物流公司',\n        key: 'delivery_name'\n      }, {\n        title: '物流单号',\n        key: 'delivery_id'\n      }, {\n        title: '处理状态',\n        key: 'status_cn'\n      }, {\n        title: '异常原因',\n        key: 'error'\n      }],\n      columns3: [{\n        title: '订单ID',\n        key: 'order_id'\n      }, {\n        title: '备注',\n        key: 'fictitious_content'\n      }, {\n        title: '处理状态',\n        key: 'status_cn'\n      }, {\n        title: '异常原因',\n        key: 'error'\n      }],\n      columns5: [{\n        title: '订单ID',\n        key: 'order_id'\n      }, {\n        title: '配送员',\n        key: 'delivery_name'\n      }, {\n        title: '配送员电话',\n        key: 'delivery_id'\n      }, {\n        title: '处理状态',\n        key: 'status_cn'\n      }, {\n        title: '异常原因',\n        key: 'error'\n      }],\n      columns4: [],\n      data2: [],\n      page2: {\n        total: 0,\n        // 总条数\n        pageNum: 1,\n        // 当前页\n        pageSize: 12 // 每页显示条数\n\n      },\n      modal1: false,\n      deliveryLog: null,\n      deliveryLogId: 0,\n      deliveryLogType: '',\n      loading: false,\n      loading2: false\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 75;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  created: function created() {\n    this.getQueue();\n  },\n  methods: {\n    getQueue: function getQueue() {\n      var _this = this;\n\n      var data = {\n        page: this.page1.pageNum,\n        limit: this.page1.pageSize\n      };\n\n      if (this.formValidate.status) {\n        data.status = this.formValidate.status;\n      }\n\n      if (this.formValidate.type) {\n        data.type = this.formValidate.type;\n      }\n\n      if (this.formValidate.data) {\n        data.data = this.formValidate.data;\n      }\n\n      this.loading = true;\n      queueIndex(data).then(function (res) {\n        _this.loading = false;\n        _this.data1 = res.data.list;\n        _this.page1.total = res.data.count;\n      }).catch(function (err) {\n        _this.loading = false;\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.page1.pageNum = index;\n      this.getQueue();\n    },\n    // 查看-分页\n    pageChange2: function pageChange2(index) {\n      this.page2.pageNum = index;\n      this.getDeliveryLog();\n    },\n    limitChange: function limitChange(limit) {\n      this.page1.pageSize = limit;\n      this.getQueue();\n    },\n    limitChange2: function limitChange2(limit) {\n      this.page2.pageSize = limit;\n      this.getDeliveryLog();\n    },\n    // 搜索-操作时间\n    onchangeTime: function onchangeTime(time) {\n      this.timeVal = time;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join('-') : '';\n      this.page1.pageNum = 1;\n      this.getQueue();\n    },\n    // 搜索-类型\n    typeSearchs: function typeSearchs() {\n      this.page1.pageNum = 1;\n      this.getQueue();\n    },\n    // 搜索-状态\n    statusSearchs: function statusSearchs() {\n      this.page1.pageNum = 1;\n      this.getQueue();\n    },\n    // 查看-获取数据\n    getDeliveryLog: function getDeliveryLog() {\n      var _this2 = this;\n\n      this.loading2 = true;\n      deliveryLog(this.deliveryLogId, this.deliveryLogType, {\n        page: this.page2.pageNum,\n        limit: this.page2.pageSize\n      }).then(function (res) {\n        _this2.loading2 = false;\n        _this2.data2 = res.data.list;\n        _this2.page2.total = res.data.count;\n      }).catch(function (err) {\n        _this2.loading2 = false;\n      });\n    },\n    // 查看\n    deliveryLook: function deliveryLook(row) {\n      this.modal1 = true;\n      this.deliveryLogId = row.id;\n      this.deliveryLogType = row.cache_type;\n      this.deliveryLog = row;\n\n      switch (row.type) {\n        case 7:\n        case 8:\n          this.columns4 = this.columns2;\n          break;\n\n        case 9:\n          this.columns4 = this.columns5;\n          break;\n\n        case 10:\n          this.columns4 = this.columns3;\n          break;\n      }\n\n      this.getDeliveryLog();\n    },\n    // 更多\n    changeMenu: function changeMenu(row, $event) {\n      var _this3 = this;\n\n      switch ($event) {\n        // 下载\n        case '1':\n          batchOrderDelivery(row.id, row.type, row.cache_type).then(function (res) {\n            window.open(res.data[0]);\n          }).catch(function (err) {\n            _this3.$Message.error(err.msg);\n          });\n          break;\n        // 重新执行\n\n        case '2':\n          this.queueAgain(row.id, row.type);\n          break;\n        // 停止任务\n\n        case '3':\n          this.$Modal.confirm({\n            title: '谨慎操作',\n            content: '<p>确认停止该任务？</p>',\n            onOk: function onOk() {\n              _this3.stopQueue(row.id);\n            }\n          });\n          break;\n        // 清除异常任务\n\n        case '4':\n          this.queueDel(row.id, row.type);\n          break;\n      }\n    },\n    // 重新执行\n    queueAgain: function queueAgain(id, type) {\n      var _this4 = this;\n\n      _queueAgain(id, type).then(function (res) {\n        _this4.$Message.success(res.msg);\n\n        _this4.getQueue();\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    },\n    // 清除异常任务\n    queueDel: function queueDel(id, type) {\n      var _this5 = this;\n\n      _queueDel(id, type).then(function (res) {\n        _this5.$Message.success(res.msg);\n\n        _this5.getQueue();\n      }).catch(function (err) {\n        _this5.$Message.error(err.msg);\n      });\n    },\n    // 停止任务\n    stopQueue: function stopQueue(id) {\n      var _this6 = this;\n\n      stopWrongQueue(id).then(function (res) {\n        _this6.$Message.success(res.msg);\n\n        _this6.getQueue();\n      }).catch(function (err) {\n        _this6.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}