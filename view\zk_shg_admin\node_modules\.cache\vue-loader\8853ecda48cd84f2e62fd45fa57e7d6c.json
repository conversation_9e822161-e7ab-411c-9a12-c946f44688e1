{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue?vue&type=template&id=455dedc3&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"Box\"},[_c('Card',[_c('div',[_vm._v(\"\\n        生成的商品默认是没有上架的，请手动上架商品！\\n        \"),(_vm.copyConfig.copy_type == 2)?_c('a',{attrs:{\"href\":\"http://help.crmeb.net/crmeb-v4/1863579\",\"target\":\"_blank\"}},[_vm._v(\"如何配置密钥\")]):_c('span',[_vm._v(\"您当前剩余\"+_vm._s(_vm.copyConfig.copy_num)+\"条采集次数，\"),_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.mealPay('copy')}}},[_vm._v(\"增加采集次数\")])])]),_c('div',[_vm._v(\"商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置\")])]),_c('Form',{ref:\"formValidate\",staticClass:\"formValidate mt20\",attrs:{\"model\":_vm.formValidate,\"rules\":_vm.ruleInline,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{attrs:{\"span\":\"15\"}},[_c('FormItem',{attrs:{\"label\":\"链接地址：\"}},[_c('Input',{staticClass:\"numPut\",attrs:{\"search\":\"\",\"enter-button\":\"确定\",\"placeholder\":\"请输入链接地址\"},on:{\"on-search\":_vm.add},model:{value:(_vm.soure_link),callback:function ($$v) {_vm.soure_link=$$v},expression:\"soure_link\"}})],1)],1),_c('div',[(_vm.isData)?_c('div',[_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品名称：\",\"prop\":\"store_name\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入商品名称\"},model:{value:(_vm.formValidate.store_name),callback:function ($$v) {_vm.$set(_vm.formValidate, \"store_name\", $$v)},expression:\"formValidate.store_name\"}})],1)],1),_c('Col',{attrs:{\"span\":\"22\"}},[_c('FormItem',{attrs:{\"label\":\"商品简介：\",\"prop\":\"store_info\",\"label-for\":\"store_info\"}},[_c('Input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入商品简介\"},model:{value:(_vm.formValidate.store_info),callback:function ($$v) {_vm.$set(_vm.formValidate, \"store_info\", $$v)},expression:\"formValidate.store_info\"}})],1)],1),_c('Col',{attrs:{\"span\":\"22\"}},[_c('FormItem',{attrs:{\"label\":\"商品分类：\",\"prop\":\"cate_id\"}},[_c('Select',{attrs:{\"multiple\":\"\"},model:{value:(_vm.formValidate.cate_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"cate_id\", $$v)},expression:\"formValidate.cate_id\"}},_vm._l((_vm.treeSelect),function(item){return _c('Option',{key:item.id,attrs:{\"disabled\":item.pid === 0,\"value\":item.id}},[_vm._v(_vm._s(item.html + item.cate_name))])}),1)],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"商品关键字：\",\"prop\":\"keyword\",\"label-for\":\"keyword\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入商品关键字\"},model:{value:(_vm.formValidate.keyword),callback:function ($$v) {_vm.$set(_vm.formValidate, \"keyword\", $$v)},expression:\"formValidate.keyword\"}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"单位：\",\"prop\":\"unit_name\",\"label-for\":\"unit_name\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入单位\"},model:{value:(_vm.formValidate.unit_name),callback:function ($$v) {_vm.$set(_vm.formValidate, \"unit_name\", $$v)},expression:\"formValidate.unit_name\"}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"虚拟销量：\",\"label-for\":\"ficti\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('100%'),expression:\"'100%'\"}],attrs:{\"placeholder\":\"请输入虚拟销量\"},model:{value:(_vm.formValidate.ficti),callback:function ($$v) {_vm.$set(_vm.formValidate, \"ficti\", $$v)},expression:\"formValidate.ficti\"}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"积分：\",\"label-for\":\"give_integral\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('100%'),expression:\"'100%'\"}],attrs:{\"placeholder\":\"请输入积分\"},model:{value:(_vm.formValidate.give_integral),callback:function ($$v) {_vm.$set(_vm.formValidate, \"give_integral\", $$v)},expression:\"formValidate.give_integral\"}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"运费模板：\",\"prop\":\"temp_id\"}},[_c('Select',{attrs:{\"clearable\":\"\"},model:{value:(_vm.formValidate.temp_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"temp_id\", $$v)},expression:\"formValidate.temp_id\"}},_vm._l((_vm.templateList),function(item,index){return _c('Option',{key:index,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.name))])}),1)],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品图：\"}},[_c('div',{staticClass:\"pictrueBox\"},[(_vm.formValidate.image)?_c('div',{staticClass:\"pictrue\"},[_c('viewer',[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.formValidate.image),expression:\"formValidate.image\"}]})])],1):_vm._e()])])],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品轮播图：\"}},[_c('viewer',[_c('div',{staticClass:\"acea-row\"},_vm._l((_vm.formValidate.slider_image),function(item,index){return _c('div',{key:index,staticClass:\"lunBox mr15\",attrs:{\"draggable\":\"true\"},on:{\"dragstart\":function($event){return _vm.handleDragStart($event, item)},\"dragover\":function($event){$event.preventDefault();return _vm.handleDragOver($event, item)},\"dragenter\":function($event){return _vm.handleDragEnter($event, item)},\"dragend\":function($event){return _vm.handleDragEnd($event, item)}}},[_c('div',{staticClass:\"pictrue\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(item),expression:\"item\"}]})]),_c('ButtonGroup',{attrs:{\"size\":\"small\"}},[_c('Button',{nativeOn:{\"click\":function($event){return _vm.checked(item, index)}}},[_vm._v(\"主图\")]),_c('Button',{nativeOn:{\"click\":function($event){return _vm.handleRemove(index)}}},[_vm._v(\"移除\")])],1)],1)}),0)])],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[(_vm.formValidate.attrs)?_c('FormItem',{staticClass:\"labeltop\",attrs:{\"label\":\"批量设置：\"}},[_c('Table',{attrs:{\"data\":_vm.oneFormBatch,\"columns\":_vm.columnsBatch,\"border\":\"\"},scopedSlots:_vm._u([{key:\"pic\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',{staticClass:\"acea-row row-middle row-center-wrapper\",on:{\"click\":function($event){return _vm.modalPicTap('dan', 'duopi', index)}}},[(_vm.oneFormBatch[0].pic)?_c('div',{staticClass:\"pictrue pictrueTab\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.oneFormBatch[0].pic),expression:\"oneFormBatch[0].pic\"}]})]):_c('div',{staticClass:\"upLoad pictrueTab acea-row row-center-wrapper\"},[_c('Icon',{staticClass:\"iconfont\",attrs:{\"type\":\"ios-camera-outline\",\"size\":\"21\"}})],1)])]}},{key:\"price\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].price),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"price\", $$v)},expression:\"oneFormBatch[0].price\"}})]}},{key:\"cost\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].cost),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"cost\", $$v)},expression:\"oneFormBatch[0].cost\"}})]}},{key:\"ot_price\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].ot_price),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"ot_price\", $$v)},expression:\"oneFormBatch[0].ot_price\"}})]}},{key:\"stock\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].stock),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"stock\", $$v)},expression:\"oneFormBatch[0].stock\"}})]}},{key:\"bar_code\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('Input',{model:{value:(_vm.oneFormBatch[0].bar_code),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"bar_code\", $$v)},expression:\"oneFormBatch[0].bar_code\"}})]}},{key:\"weight\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].weight),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"weight\", $$v)},expression:\"oneFormBatch[0].weight\"}})]}},{key:\"volume\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].volume),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"volume\", $$v)},expression:\"oneFormBatch[0].volume\"}})]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":_vm.batchAdd}},[_vm._v(\"添加\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":_vm.batchDel}},[_vm._v(\"清空\")])]}}],null,false,180024394)})],1):_vm._e()],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品规格：\",\"props\":\"spec_type\",\"label-for\":\"spec_type\"}},[_c('Col',{attrs:{\"xl\":23,\"lg\":24,\"md\":24,\"sm\":24,\"xs\":24}},[_c('FormItem',[_c('Table',{attrs:{\"data\":_vm.items,\"columns\":_vm.columns,\"border\":\"\"},scopedSlots:_vm._u([{key:\"pic\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',{staticClass:\"acea-row row-middle row-center-wrapper\",on:{\"click\":function($event){return _vm.modalPicTap('dan', index)}}},[(_vm.formValidate.attrs[index].pic)?_c('div',{staticClass:\"pictrue pictrueTab\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.formValidate.attrs[index].pic),expression:\"formValidate.attrs[index].pic\"}]})]):_c('div',{staticClass:\"upLoad upLoadTab acea-row row-center-wrapper\"},[_c('Icon',{staticClass:\"iconfont\",attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1)])]}},{key:\"price\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",model:{value:(_vm.formValidate.attrs[index].price),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"price\", $$v)},expression:\"formValidate.attrs[index].price\"}})]}},{key:\"cost\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",model:{value:(_vm.formValidate.attrs[index].cost),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"cost\", $$v)},expression:\"formValidate.attrs[index].cost\"}})]}},{key:\"ot_price\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",model:{value:(_vm.formValidate.attrs[index].ot_price),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"ot_price\", $$v)},expression:\"formValidate.attrs[index].ot_price\"}})]}},{key:\"vip_price\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",model:{value:(_vm.formValidate.attrs[index].vip_price),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"vip_price\", $$v)},expression:\"formValidate.attrs[index].vip_price\"}})]}},{key:\"stock\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",model:{value:(_vm.formValidate.attrs[index].stock),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"stock\", $$v)},expression:\"formValidate.attrs[index].stock\"}})]}},{key:\"bar_code\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('Input',{model:{value:(_vm.formValidate.attrs[index].bar_code),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"bar_code\", $$v)},expression:\"formValidate.attrs[index].bar_code\"}})]}},{key:\"weight\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.formValidate.attrs[index].weight),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"weight\", $$v)},expression:\"formValidate.attrs[index].weight\"}})]}},{key:\"volume\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.formValidate.attrs[index].volume),callback:function ($$v) {_vm.$set(_vm.formValidate.attrs[index], \"volume\", $$v)},expression:\"formValidate.attrs[index].volume\"}})]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.delAttrTable(index)}}},[_vm._v(\"删除\")])]}}],null,false,854518418)})],1)],1)],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品详情：\"}},[_c('WangEditor',{staticStyle:{\"width\":\"100%\"},attrs:{\"content\":_vm.formValidate.description},on:{\"editorContent\":_vm.getEditorContent}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',[_c('Button',{staticClass:\"submission\",attrs:{\"type\":\"primary\",\"loading\":_vm.modal_loading},on:{\"click\":function($event){return _vm.handleSubmit('formValidate')}}},[_vm._v(\"提交\")])],1)],1)],1):_vm._e(),(_vm.spinShow)?_c('Spin',{attrs:{\"size\":\"large\",\"fix\":\"\"}}):_vm._e()],1)],1)],1),_c('Modal',{staticClass:\"aaaa\",attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"上传商品图\",\"mask-closable\":false},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}