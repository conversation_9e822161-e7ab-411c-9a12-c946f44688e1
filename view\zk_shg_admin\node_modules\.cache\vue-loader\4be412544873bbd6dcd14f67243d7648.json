{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import { mapState, mapMutations } from 'vuex';\n    import { levelListApi, setShowApi, createApi } from '@/api/user';\n    import taskList from './handle/task';\n    import editFrom from '@/components/from/from';\n    export default {\n        name: 'user_level',\n        components: { editFrom, taskList },\n        data () {\n            return {\n                grid: {\n                    xl: 7,\n                    lg: 7,\n                    md: 12,\n                    sm: 24,\n                    xs: 24\n                },\n                loading: false,\n                columns1: [\n\t\t\t\t\t{\n\t\t\t\t\t    title: '等级',\n\t\t\t\t\t    key: 'grade',\n\t\t\t\t\t    minWidth: 100\n\t\t\t\t\t},\n                    {\n                        title: '等级图标',\n                        slot: 'icons',\n                        minWidth: 100\n                    },\n                    {\n                        title: '等级名称',\n                        key: 'name',\n                        minWidth: 120\n                    },\n                    {\n                        title: '享受折扣(%)',\n                        key: 'discount',\n                        minWidth: 100\n                    },\n\t\t\t\t\t{\n\t\t\t\t\t    title: '解锁经验值',\n\t\t\t\t\t    key: 'exp_num',\n\t\t\t\t\t    minWidth: 100\n\t\t\t\t\t},\n                    // {\n                    //     title: '有效时间',\n                    //     key: 'valid_date',\n                    //     minWidth: 120\n                    // },\n                    // {\n                    //     title: '是否永久',\n                    //     slot: 'is_forevers',\n                    //     minWidth: 130\n                    // },\n                    // {\n                    //     title: '是否付费',\n                    //     slot: 'is_pays',\n                    //     minWidth: 120\n                    // },\n                    {\n                        title: '是否显示',\n                        slot: 'is_shows',\n                        minWidth: 120\n                    },\n                    {\n                        title: '等级说明',\n                        key: 'explain',\n                        minWidth: 120\n                    },\n                    {\n                        title: '操作',\n                        slot: 'action',\n                        fixed: 'right',\n                        width: 120\n                    }\n                ],\n                levelFrom: {\n                    is_show: '',\n                    title: '',\n                    page: 1,\n                    limit: 10\n                },\n                levelLists: [],\n                total: 0,\n                FromData: null,\n                imgName: '',\n                visible: false,\n                levelId: 0,\n                modalTitleSs: '',\n                titleType: 'level',\n                modelTask: false,\n                num: 0\n            }\n        },\n        created () {\n            this.getList();\n        },\n        computed: {\n            ...mapState('admin/layout', [\n                'isMobile'\n            ]),\n            labelWidth () {\n                return this.isMobile ? undefined : 96;\n            },\n            labelPosition () {\n                return this.isMobile ? 'top' : 'right';\n            }\n        },\n        methods: {\n            ...mapMutations('admin/userLevel', [\n                'getlevelId'\n            ]),\n            // 操作\n            changeMenu (row, name, num) {\n                this.levelId = row.id;\n                switch (name) {\n                case '1':\n                    this.getlevelId(this.levelId);\n                    this.$refs.tasks.modals = true;\n                    this.$refs.tasks.getList();\n                    break;\n                default:\n                    this.del(row, '删除等级', num);\n                }\n            },\n            // 删除\n            del (row, tit, num) {\n                let delfromData = {\n                    title: tit,\n                    num: num,\n                    url: `user/user_level/delete/${row.id}`,\n                    method: 'put',\n                    ids: ''\n                }\n                this.$modalSure(delfromData).then((res) => {\n                    this.$Message.success(res.msg);\n                    this.levelLists.splice(num, 1);\n                    if (!this.levelLists.length) {\n                      this.levelFrom.page =\n                          this.levelFrom.page == 1 ? 1 : this.levelFrom.page - 1;\n                    }\n                    this.getList();\n                }).catch(res => {\n                    this.$Message.error(res.msg);\n                });\n            },\n            // 删除成功\n            // submitModel () {\n            //     this.levelLists.splice(this.delfromData.num, 1)\n            // },\n            // 修改是否显示\n            onchangeIsShow (row) {\n                let data = {\n                    id: row.id,\n                    is_show: row.is_show\n                }\n                setShowApi(data).then(async res => {\n                    this.$Message.success(res.msg);\n                }).catch(res => {\n                    this.$Message.error(res.msg);\n                })\n            },\n            // 等级列表\n            getList () {\n                this.loading = true;\n                this.levelFrom.is_show = this.levelFrom.is_show || '';\n                levelListApi(this.levelFrom).then(async res => {\n                    let data = res.data\n                    this.levelLists = data.list;\n                    this.total = res.data.count;\n                    this.loading = false;\n                }).catch(res => {\n                    this.loading = false;\n                    this.$Message.error(res.msg);\n                })\n            },\n            pageChange (index) {\n                this.levelFrom.page = index;\n                this.getList();\n            },\n            // 添加\n            add () {\n                this.levelId = 0;\n                this.$modalForm(createApi({ id: this.levelId })).then(() => this.getList());\n            },\n            // 编辑\n            edit (row) {\n                this.levelId = row.id;\n                this.$modalForm(createApi({ id: this.levelId })).then(() => this.getList());\n                this.getlevelId(this.levelId);\n            },\n            // 表格搜索\n            userSearchs () {\n                this.levelFrom.page = 1;\n                this.getList();\n            },\n            // 修改成功\n            submitFail () {\n                this.getList();\n            }\n        }\n    }\n", null]}