{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\list.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userMemberCard, memberRecord, memberCardStatus } from \"@/api/user\";\nimport { mapState } from \"vuex\";\nimport Setting from \"@/setting\";\nexport default {\n    name: \"card\",\n    data() {\n        return {\n          roterPre: Setting.roterPre,\n            columns1: [\n                {\n                    title: \"编号\",\n                    key: \"id\",\n                    minWidth: 100\n                },\n                {\n                    title: \"卡号\",\n                    key: \"card_number\",\n                    minWidth: 100\n                },\n                {\n                    title: \"密码\",\n                    key: \"card_password\",\n                    minWidth: 100\n                },\n                {\n                    title: \"领取人名称\",\n                    key: \"username\",\n                    minWidth: 100\n                },\n                {\n                    title: \"领取人电话\",\n                    key: \"phone\",\n                    minWidth: 100\n                },\n                {\n                    title: \"领取时间\",\n                    key: \"use_time\",\n                    minWidth: 100\n                },\n                {\n                    title: \"是否激活\",\n                    slot: \"status\",\n                    minWidth: 100\n                }\n            ],\n            data1: [],\n            loading: false,\n            total: 0,\n            table: {\n                page: 1,\n                limit: 15,\n                card_number: \"\",\n                phone: \"\",\n                is_use: \"\"\n            }\n        };\n    },\n    computed: {\n        ...mapState(\"media\", [\"isMobile\"]),\n        labelWidth() {\n            return this.isMobile ? undefined : 75;\n        },\n        labelPosition() {\n            return this.isMobile ? \"top\" : \"right\";\n        }\n    },\n    created() {\n        this.getMemberCard();\n    },\n    methods: {\n        onchangeIsShow(row){\n            let data = {\n                card_id: row.id,\n                status: row.status\n            };\n            memberCardStatus(data).then(res=>{\n                this.$Message.success(res.msg);\n                this.getMemberCard();\n            }).catch(err=>{\n                this.$Message.error(err.msg);\n            })\n        },\n        getMemberCard() {\n            this.loading = true;\n            userMemberCard(this.$route.params.id, this.table)\n                .then(res => {\n                    this.loading = false;\n                    this.data1 = res.data.list;\n                    this.total = res.data.count;\n                })\n                .catch(err => {\n                    this.loading = false;\n                    this.$Message.error(err.msg);\n                });\n        },\n        // 搜索\n        formSubmit() {\n            this.table.page = 1;\n            this.getMemberCard();\n        },\n        // 分页\n        pageChange(index) {\n            this.table.page = index;\n            this.getMemberCard();\n        }\n    }\n};\n", null]}