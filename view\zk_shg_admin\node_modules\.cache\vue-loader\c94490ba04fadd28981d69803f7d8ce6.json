{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAdd\\taoBao.vue?vue&type=template&id=0633a2e6&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAdd\\taoBao.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"Box\">\n    <Card>\n        <div>生成的商品默认是没有上架的，请手动上架商品！\n            <a href=\"http://help.crmeb.net/crmeb-v4/1863579\" v-if=\"copyConfig.copy_type == 2\"  target=\"_blank\">如何配置密钥</a>\n            <span v-else>您当前剩余{{copyConfig.copy_num}}条采集次数，<a href=\"#\" @click=\"mealPay('copy')\">增加采集次数</a></span>\n        </div>\n        <div>商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置</div>\n    </Card>\n    <Form class=\"formValidate mt20\" ref=\"formValidate\" :label-width=\"120\" label-position=\"right\" @submit.native.prevent>\n        <Row :gutter=\"24\" type=\"flex\">\n            <Col span=\"18\">\n                <FormItem label=\"链接地址：\">\n                    <Input search enter-button=\"确定\"  v-model=\"soure_link\" placeholder=\"请输入链接地址\" class=\"numPut\" @on-search=\"add\"/>\n                </FormItem>\n            </Col>\n        </Row>\n    </Form>\n    <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\n</div>\n", null]}