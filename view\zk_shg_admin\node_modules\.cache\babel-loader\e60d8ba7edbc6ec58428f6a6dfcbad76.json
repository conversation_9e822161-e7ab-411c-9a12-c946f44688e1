{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\orderDetall.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\orderDetall.vue", "mtime": 1694567788000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { orderInvoiceInfo } from '@/api/order';\nexport default {\n  name: \"order_detail\",\n  props: {\n    orderId: {\n      type: String | Number,\n      default: ''\n    }\n  },\n  data: function data() {\n    return {\n      orderDetail: {},\n      orderList: [],\n      columns1: [{\n        title: '商品ID',\n        slot: 'id',\n        maxWidth: 80\n      }, {\n        title: '商品名称',\n        slot: 'name',\n        minWidth: 160\n      }, {\n        title: '商品分类',\n        slot: 'className'\n      }, {\n        title: '商品售价',\n        slot: 'price'\n      }, {\n        title: '商品数量',\n        slot: 'total_num'\n      }],\n      spinShow: false\n    };\n  },\n  mounted: function mounted() {\n    this.getOrderInfo();\n  },\n  methods: {\n    getOrderInfo: function getOrderInfo() {\n      var _this = this;\n\n      this.spinShow = true;\n      orderInvoiceInfo(this.orderId).then(function (res) {\n        _this.spinShow = false;\n        _this.orderDetail = res.data;\n        _this.orderList = res.data.orderInfo.cartInfo;\n      }).catch(function (err) {\n        _this.spinShow = false;\n\n        _this.$Message.error(err.msg);\n\n        _this.$emit('detall', false);\n      });\n    }\n  }\n};", null]}