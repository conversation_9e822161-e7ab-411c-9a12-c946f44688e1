{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_radio.vue?vue&type=template&id=b9b85b00&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_radio.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"c_radio\",class:_vm.configData.type=='form'?'mb15':'on mb5'},[_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"c_label\",class:_vm.configData.type=='form'?'on':'',attrs:{\"span\":\"4\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.configData.title)+\"\\n        \")]),_c('Col',{staticClass:\"color-box\",attrs:{\"span\":_vm.configData.type=='form'?'19':'18'}},[_c('RadioGroup',{on:{\"on-change\":function($event){return _vm.radioChange($event)}},model:{value:(_vm.configData.tabVal),callback:function ($$v) {_vm.$set(_vm.configData, \"tabVal\", $$v)},expression:\"configData.tabVal\"}},_vm._l((_vm.configData.tabList),function(radio,key){return _c('Radio',{key:key,attrs:{\"label\":key}},[_c('span',[_vm._v(_vm._s(radio.name))])])}),1)],1)],1)]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}