{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue?vue&type=template&id=08b87947&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"areaBox\",style:({\n    width: _vm.areaInit.areaWidth + 'px',\n    height: _vm.areaInit.areaHeight + 'px',\n    left: _vm.areaInit.starX + 'px',\n    top: _vm.areaInit.starY + 'px',\n  }),on:{\"dblclick\":function($event){_vm.editBoxShow = true},\"mousedown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"left\",37,$event.key,[\"Left\",\"ArrowLeft\"])){ return null; }if('button' in $event && $event.button !== 0){ return null; }$event.stopPropagation();return _vm.mouseDownLint($event)},\"mouseup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"left\",37,$event.key,[\"Left\",\"ArrowLeft\"])){ return null; }if('button' in $event && $event.button !== 0){ return null; }$event.stopPropagation();return _vm.mouseUp($event)}}},[_c('div',{staticClass:\"prompt-text\"},[_c('div',{staticClass:\"prompt-item num\"},[_vm._v(\"热区 \"+_vm._s(_vm.areaInit.number))]),_c('div',{staticClass:\"prompt-item\",style:({ color: _vm.isSet ? '#2d8cf0' : '#f00' })},[_vm._v(\"\\n      \"+_vm._s(_vm.isSet ? \"(已设置)\" : \"(未设置)\")+\"\\n    \")])]),_c('div',{staticClass:\"del\",on:{\"click\":function($event){$event.stopPropagation();return _vm.del()}}},[_c('Icon',{attrs:{\"type\":\"ios-close\",\"size\":\"16\"}})],1),_c('div',{staticClass:\"shape\",on:{\"mousedown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"left\",37,$event.key,[\"Left\",\"ArrowLeft\"])){ return null; }if('button' in $event && $event.button !== 0){ return null; }$event.stopPropagation();return _vm.shapeDown($event)},\"mouseup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"left\",37,$event.key,[\"Left\",\"ArrowLeft\"])){ return null; }if('button' in $event && $event.button !== 0){ return null; }$event.stopPropagation();return _vm.mouseUp($event)}}}),_c('div',{on:{\"mousedown\":function($event){$event.stopPropagation();}}},[_c('Modal',{attrs:{\"title\":\"设置热区\",\"width\":\"560px\"},model:{value:(_vm.editBoxShow),callback:function ($$v) {_vm.editBoxShow=$$v},expression:\"editBoxShow\"}},[_c('div',{staticClass:\"area-set\"},[_c('div',{staticClass:\"area-label\"},[_vm._v(\"热区跳转链接：\")]),_c('div',{staticClass:\"area-content\",on:{\"click\":function($event){return _vm.getLink()}}},[_c('Input',{staticStyle:{\"width\":\"100%\"},attrs:{\"icon\":\"ios-arrow-forward\",\"readonly\":\"\",\"placeholder\":\"选择跳转链接\"},model:{value:(_vm.url),callback:function ($$v) {_vm.url=$$v},expression:\"url\"}})],1)]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{on:{\"click\":function($event){$event.stopPropagation();_vm.editBoxShow = false}}},[_vm._v(\"取 消\")]),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.addURL($event)}}},[_vm._v(\"确 定\")])],1)]),_c('linkaddress',{ref:\"linkaddres\",on:{\"linkUrl\":_vm.linkUrl}})],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}