{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\searchFrom\\searchFrom.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\searchFrom\\searchFrom.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nexport default {\n    name: 'searchFrom',\n    props: {\n        typeName: Array\n    },\n    data () {\n        return {\n            currentTab: '',\n            grid: {\n                xl: 8,\n                lg: 8,\n                md: 8,\n                sm: 24,\n                xs: 24\n            },\n            // collapse: false,\n            // 搜索条件\n            DataList: {\n                status: '',\n                data: '',\n                real_name: ''\n            },\n            rules: {\n\n            },\n            statusType: '',\n            time: '',\n            value2: []\n        }\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        ...mapState('admin/order', [\n            'orderChartType'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 80;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'right';\n        }\n    },\n    methods: {\n        // 订单选择状态\n        selectChange (status) {\n            this.$emit('getTypeNum', status);\n        },\n        // 时间状态\n        timeChange (time) {\n            this.$emit('getSeachTime', time);\n        },\n        // 订单号搜索\n        orderSearch (num) {\n            this.getOrderNum(num);\n            this.$emit('getList');\n        },\n        // 点击订单类型\n        onClickTab () {\n            this.$emit('onChangeType', this.currentTab);\n        },\n        handleSubmit () {\n            this.$emit('on-submit', this.data);\n        },\n        // 刷新\n        Refresh () {\n            this.$emit('getList');\n        },\n        handleReset () {\n            this.$refs.form.resetFields();\n            this.$emit('on-reset');\n        }\n    }\n}\n", null]}