{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderSend.vue?vue&type=template&id=7a3a1fb6&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderSend.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Modal',{staticClass:\"order_box\",attrs:{\"scrollable\":\"\",\"title\":\"订单发送货\",\"closable\":false},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('Form',{ref:\"formItem\",attrs:{\"model\":_vm.formItem,\"label-width\":100},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"选择类型：\"}},[_c('RadioGroup',{on:{\"on-change\":_vm.changeRadio},model:{value:(_vm.formItem.type),callback:function ($$v) {_vm.$set(_vm.formItem, \"type\", $$v)},expression:\"formItem.type\"}},[_c('Radio',{attrs:{\"label\":\"1\"}},[_vm._v(\"发货\")]),_c('Radio',{attrs:{\"label\":\"2\"}},[_vm._v(\"送货\")]),_c('Radio',{attrs:{\"label\":\"3\"}},[_vm._v(\"虚拟\")])],1)],1),_c('FormItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.formItem.type === '1' && _vm.export_open),expression:\"formItem.type === '1' && export_open\"}],attrs:{\"label\":\"发货类型：\"}},[_c('RadioGroup',{on:{\"on-change\":_vm.changeExpress},model:{value:(_vm.formItem.express_record_type),callback:function ($$v) {_vm.$set(_vm.formItem, \"express_record_type\", $$v)},expression:\"formItem.express_record_type\"}},[_c('Radio',{attrs:{\"label\":\"1\"}},[_vm._v(\"手动填写\")]),_c('Radio',{attrs:{\"label\":\"2\"}},[_vm._v(\"电子面单打印\")])],1)],1),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.formItem.type === '1'),expression:\"formItem.type === '1'\"}]},[_c('FormItem',{attrs:{\"label\":\"快递公司：\"}},[_c('Select',{staticStyle:{\"width\":\"80%\"},attrs:{\"filterable\":\"\",\"placeholder\":\"请选择快递公司\"},on:{\"on-change\":_vm.expressChange},model:{value:(_vm.formItem.delivery_name),callback:function ($$v) {_vm.$set(_vm.formItem, \"delivery_name\", $$v)},expression:\"formItem.delivery_name\"}},_vm._l((_vm.express),function(item,i){return _c('Option',{key:item.value,attrs:{\"value\":item.value}},[_vm._v(_vm._s(item.value))])}),1)],1),(_vm.formItem.express_record_type === '1')?_c('FormItem',{attrs:{\"label\":\"快递单号：\"}},[_c('Input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入快递单号\"},model:{value:(_vm.formItem.delivery_id),callback:function ($$v) {_vm.$set(_vm.formItem, \"delivery_id\", $$v)},expression:\"formItem.delivery_id\"}}),(_vm.formItem.delivery_name == '顺丰速运')?_c('div',{staticClass:\"trips\"},[_c('p',[_vm._v(\"顺丰请输入单号 :收件人或寄件人手机号后四位\")]),_c('p',[_vm._v(\"例如：SF000000000000:3941\")])]):_vm._e()],1):_vm._e(),(_vm.formItem.express_record_type === '2')?[_c('FormItem',{staticClass:\"express_temp_id\",attrs:{\"label\":\"电子面单：\"}},[_c('Select',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请选择电子面单\"},on:{\"on-change\":_vm.expressTempChange},model:{value:(_vm.formItem.express_temp_id),callback:function ($$v) {_vm.$set(_vm.formItem, \"express_temp_id\", $$v)},expression:\"formItem.express_temp_id\"}},_vm._l((_vm.expressTemp),function(item,i){return _c('Option',{key:i,attrs:{\"value\":item.temp_id}},[_vm._v(_vm._s(item.title))])}),1),(_vm.formItem.express_temp_id)?_c('Button',{attrs:{\"type\":\"text\"},on:{\"click\":_vm.preview}},[_vm._v(\"预览\")]):_vm._e()],1),_c('FormItem',{attrs:{\"label\":\"寄件人姓名：\"}},[_c('Input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入寄件人姓名\"},model:{value:(_vm.formItem.to_name),callback:function ($$v) {_vm.$set(_vm.formItem, \"to_name\", $$v)},expression:\"formItem.to_name\"}})],1),_c('FormItem',{attrs:{\"label\":\"寄件人电话：\"}},[_c('Input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入寄件人电话\"},model:{value:(_vm.formItem.to_tel),callback:function ($$v) {_vm.$set(_vm.formItem, \"to_tel\", $$v)},expression:\"formItem.to_tel\"}})],1),_c('FormItem',{attrs:{\"label\":\"寄件人地址：\"}},[_c('Input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入寄件人地址\"},model:{value:(_vm.formItem.to_addr),callback:function ($$v) {_vm.$set(_vm.formItem, \"to_addr\", $$v)},expression:\"formItem.to_addr\"}})],1)]:_vm._e()],2),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.formItem.type === '2'),expression:\"formItem.type === '2'\"}]},[_c('FormItem',{attrs:{\"label\":\"送货人：\"}},[_c('Select',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请选择送货人\"},on:{\"on-change\":_vm.shDeliveryChange},model:{value:(_vm.formItem.sh_delivery),callback:function ($$v) {_vm.$set(_vm.formItem, \"sh_delivery\", $$v)},expression:\"formItem.sh_delivery\"}},_vm._l((_vm.deliveryList),function(item,i){return _c('Option',{key:i,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.wx_name)+\"（\"+_vm._s(item.phone)+\"）\")])}),1)],1)],1),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.formItem.type === '3'),expression:\"formItem.type === '3'\"}]},[_c('FormItem',{attrs:{\"label\":\"备注：\"}},[_c('Input',{staticStyle:{\"width\":\"80%\"},attrs:{\"type\":\"textarea\",\"autosize\":{ minRows: 2, maxRows: 5 },\"placeholder\":\"备注\"},model:{value:(_vm.formItem.fictitious_content),callback:function ($$v) {_vm.$set(_vm.formItem, \"fictitious_content\", $$v)},expression:\"formItem.fictitious_content\"}})],1)],1)],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{on:{\"click\":_vm.cancel}},[_vm._v(\"取消\")]),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.putSend}},[_vm._v(\"提交\")])],1),_c('div',{directives:[{name:\"viewer\",rawName:\"v-viewer\"},{name:\"show\",rawName:\"v-show\",value:(_vm.temp),expression:\"temp\"}],ref:\"viewer\"},[_c('img',{staticStyle:{\"display\":\"none\"},attrs:{\"src\":_vm.temp.pic}})])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}