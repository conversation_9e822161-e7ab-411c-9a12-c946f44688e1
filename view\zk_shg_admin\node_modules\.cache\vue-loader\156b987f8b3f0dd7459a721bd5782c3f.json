{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderSend.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderSend.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  getExpressData,\n  orderExpressTemp,\n  orderDeliveryList,\n  orderSheetInfo,\n  integralOrderPutDelivery,\n} from \"@/api/marketing\";\n// import {integralOrderPutDelivery} from \"@/api/marketing\";\nexport default {\n  name: \"orderSend\",\n  props: {\n    orderId: Number,\n  },\n  data() {\n    return {\n      formItem: {\n        type: \"1\",\n        express_record_type: \"1\",\n        delivery_name: \"\",\n        delivery_id: \"\",\n        express_temp_id: \"\",\n        to_name: \"\",\n        to_tel: \"\",\n        to_addr: \"\",\n        sh_delivery: \"\",\n        fictitious_content: \"\",\n      },\n      modals: false,\n      express: [],\n      expressTemp: [],\n      deliveryList: [],\n      // ruleValidate: {\n      //     delivery_name: [\n      //         { required: true, message: '请选择快递公司', trigger: 'change' }\n      //     ],\n      //     delivery_id: [\n      //         { required: true, message: '请输入快递单号', trigger: 'blur' }\n      //     ],\n      //     express_temp_id: [\n      //         { required: true, message: '请选择电子面单', trigger: 'change' }\n      //     ],\n      //     sh_delivery: [\n      //         { required: true, message: '请选择送货人', trigger: 'change', type: 'number' }\n      //     ]\n      // },\n      temp: {},\n      export_open: true,\n    };\n  },\n  methods: {\n    changeRadio(o) {\n      this.$refs.formItem.resetFields();\n      switch (o) {\n        case \"1\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.delivery_id = \"\";\n          this.formItem.express_temp_id = \"\";\n          this.formItem.express_record_type = \"1\";\n          this.expressTemp = [];\n          break;\n        case \"2\":\n          this.formItem.sh_delivery = \"\";\n          break;\n        case \"3\":\n          this.formItem.fictitious_content = \"\";\n          break;\n        default:\n          // this.formItem = {\n          //     type: '3',\n          //     express_record_type: '1',\n          //     delivery_name: '',\n          //     delivery_id: '',\n          //     express_temp_id: '',\n          //     to_name: '',\n          //     to_tel: '',\n          //     to_addr: '',\n          //     sh_delivery: ''\n          // };\n          break;\n      }\n    },\n    changeExpress(j) {\n      switch (j) {\n        case \"2\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.express_temp_id = \"\";\n          this.expressTemp = [];\n          this.getList(2);\n          break;\n        case \"1\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.delivery_id = \"\";\n          this.getList(1);\n          break;\n        default:\n          break;\n      }\n    },\n    reset() {\n      this.formItem = {\n        type: \"1\",\n        express_record_type: \"1\",\n        delivery_name: \"\",\n        delivery_id: \"\",\n        express_temp_id: \"\",\n        expressTemp: [],\n        to_name: \"\",\n        to_tel: \"\",\n        to_addr: \"\",\n        sh_delivery: \"\",\n        fictitious_content: \"\",\n      };\n    },\n    // 物流公司列表\n    getList(type) {\n      let status = type === 2 ? 1 : \"\";\n      getExpressData(status)\n        .then(async (res) => {\n          this.express = res.data;\n          this.getSheetInfo();\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 提交\n    putSend(name) {\n      let data = {\n        id: this.orderId,\n        datas: this.formItem,\n      };\n      if (\n        this.formItem.type === \"1\" &&\n        this.formItem.express_record_type === \"2\"\n      ) {\n        if (this.formItem.delivery_name === \"\") {\n          return this.$Message.error(\"快递公司不能为空\");\n        } else if (this.formItem.express_temp_id === \"\") {\n          return this.$Message.error(\"电子面单不能为空\");\n        } else if (this.formItem.to_name === \"\") {\n          return this.$Message.error(\"寄件人姓名不能为空\");\n        } else if (this.formItem.to_tel === \"\") {\n          return this.$Message.error(\"寄件人电话不能为空\");\n        } else if (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(this.formItem.to_tel)) {\n          return this.$Message.error(\"请输入正确的手机号码\");\n        } else if (this.formItem.to_addr === \"\") {\n          return this.$Message.error(\"寄件人地址不能为空\");\n        }\n      }\n      if (\n        this.formItem.type === \"1\" &&\n        this.formItem.express_record_type === \"1\"\n      ) {\n        if (this.formItem.delivery_name === \"\") {\n          return this.$Message.error(\"快递公司不能为空\");\n        } else if (this.formItem.delivery_id === \"\") {\n          return this.$Message.error(\"快递单号不能为空\");\n        }\n      }\n      if (this.formItem.type === \"2\") {\n        if (this.formItem.sh_delivery === \"\") {\n          return this.$Message.error(\"送货人不能为空\");\n        }\n      }\n      integralOrderPutDelivery(data)\n        .then(async (res) => {\n          this.modals = false;\n          this.$Message.success(res.msg);\n          this.$emit(\"submitFail\");\n          this.reset();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n      // if (this.formItem.type == 3) {\n      //     putDelivery(data).then(async res => {\n      //         this.modals = false;\n      //         this.$Message.success(res.msg);\n      //         this.$refs[name].resetFields();\n      //         this.$emit('submitFail')\n      //     }).catch(res => {\n      //         this.$Message.error(res.msg);\n      //     })\n      // } else {\n      //     this.$refs[name].validate((valid) => {\n      //         if (valid) {\n      //             putDelivery(data).then(async res => {\n      //                 this.modals = false;\n      //                 this.$Message.success(res.msg);\n      //                 this.$refs[name].resetFields();\n      //                 this.$emit('submitFail')\n      //             }).catch(res => {\n      //                 this.$Message.error(res.msg);\n      //             })\n      //         } else {\n      //             this.$Message.error('请填写信息');\n      //         }\n      //     })\n      // }\n    },\n    cancel(name) {\n      this.modals = false;\n      this.reset();\n      // this.$refs[name].resetFields();\n      // this.formItem.type = '1';\n    },\n    // 电子面单列表\n    expressChange(value) {\n      let expressItem = this.express.find((item) => {\n        return item.value === value;\n      });\n      if (expressItem === undefined) {\n        return;\n      }\n      this.formItem.delivery_code = expressItem.code;\n      if (this.formItem.express_record_type === \"2\") {\n        this.expressTemp = [];\n        this.formItem.express_temp_id = \"\";\n        orderExpressTemp({\n          com: this.formItem.delivery_code,\n        })\n          .then((res) => {\n            this.expressTemp = res.data;\n            if (!res.data.length) {\n              this.$Message.error(\"请配置你所选快递公司的电子面单\");\n            }\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n      }\n    },\n    getDeliveryList() {\n      orderDeliveryList()\n        .then((res) => {\n          this.deliveryList = res.data.list;\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    getSheetInfo() {\n      orderSheetInfo()\n        .then((res) => {\n          const data = res.data;\n          for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n              this.formItem[key] = data[key];\n            }\n          }\n          this.export_open =\n            data.export_open === undefined ? true : data.export_open;\n          if (!this.export_open) {\n            this.formItem.express_record_type = \"1\";\n          }\n          this.formItem.to_addr = data.to_add;\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    shDeliveryChange(value) {\n      let deliveryItem = this.deliveryList.find((item) => {\n        return item.id === value;\n      });\n      this.formItem.sh_delivery_name = deliveryItem.wx_name;\n      this.formItem.sh_delivery_id = deliveryItem.phone;\n      this.formItem.sh_delivery_uid = deliveryItem.uid;\n    },\n    expressTempChange(tempId) {\n      this.temp = this.expressTemp.find((item) => {\n        return tempId === item.temp_id;\n      });\n      if (this.temp === undefined) {\n        this.temp = {};\n      }\n    },\n    // inited (viewer) {\n    //     this.$viewer = viewer;\n    // },\n    preview() {\n      this.$refs.viewer.$viewer.show();\n      // this.$viewer.show();\n    },\n  },\n};\n", null]}