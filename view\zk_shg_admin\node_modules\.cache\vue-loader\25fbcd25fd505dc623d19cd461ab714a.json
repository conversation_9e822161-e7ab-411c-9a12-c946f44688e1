{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue?vue&type=style&index=0&id=5642b922&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.btn {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 16px 0;\n}\n\n.dialog-footer {\n  text-align: right;\n  margin-top: 20px;\n  margin-right: 20px;\n}\n\n.operationFloor {\n  display: flex;\n  position: relative;\n  max-height: 85vh;\n\n  .header {\n    .titleBox {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      height: 100px;\n\n      .name {\n        font-size: 13px;\n        font-weight: bold;\n      }\n    }\n\n    .textBox {\n      font-size: 12px;\n      color: #777;\n      margin-bottom: 10px;\n    }\n  }\n\n  .imgBox::-webkit-scrollbar {\n    display: none; /* Chrome Safari */\n  }\n\n  .imgBox {\n    display: flex;\n    justify-content: center;\n    width: 65%;\n    overflow-y: scroll;\n\n    .container {\n      position: relative;\n    }\n\n    img {\n      cursor: crosshair;\n      display: block;\n      width: 750px;\n    }\n\n    .area {\n      position: absolute;\n      width: 200px;\n      height: 200px;\n      left: 200px;\n      top: 300px;\n      background: rgba(#2980b9, 0.3);\n      border: 1px dashed #34495e;\n    }\n  }\n}\n\n.form {\n  font-size: 12px;\n  width: 30%;\n\n  .form-row {\n    display: flex;\n    margin: 12px 0;\n    align-items: center;\n\n    .form-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      white-space: nowrap;\n      margin: 0 5px;\n      font-size: 12px;\n\n      .num {\n        width: 69px;\n        color: #999;\n        font-size: 12px;\n      }\n\n      .label {\n        color: #C7C7C7;\n      }\n    }\n  }\n\n  .el-icon-delete {\n    font-size: 16px;\n    cursor: pointer;\n  }\n}\n", null]}