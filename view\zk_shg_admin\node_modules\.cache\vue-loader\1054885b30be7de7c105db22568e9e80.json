{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_seckill.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_seckill.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    export default {\n        name: 'c_home_seckill',\n        componentsName: 'home_seckill',\n        cname: '秒杀',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ],\n\t\t\t\toneContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'styleConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContentImg:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_upload_img,\n\t\t\t\t\t  configNme: 'imgBgConfig'\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttwoContent:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'titleConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContentImg:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_upload_img,\n\t\t\t\t\t  configNme: 'imgConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContentColorImg:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_upload_img,\n\t\t\t\t\t  configNme: 'imgColorConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContentText:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'titleTxtConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeContent:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'rightBntConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleGoodsList'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'goodStyleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleGoods'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'numberConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_checkbox,\n\t\t\t\t\t    configNme: 'checkboxInfo'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourContent:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'seckillConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'headerBgColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'titleText'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'titleColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'titleNumber'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'headerBntColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourStyle2:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'headerBntColor2'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourBntStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bntNumber'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourColorStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'tipsColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'numberBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'numberColor'\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tfourColorStyle2:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'tipsColor2'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'numberBgColor2'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'numberColor2'\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tfourGoodsStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleGoodsStyle'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'filletImg'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'goodsName'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'goodsNameColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'goodsPriceColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfiveStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'seckillPriceColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfiveStyle2:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'seckillPriceColor2'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tprogressStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'progressColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'progressTxtColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tbntStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'goodsBntColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsixStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'goodsBntTxtColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsetUp:0,\n\t\t\t\ttype:0,\n\t\t\t\ttype2:0,\n\t\t\t\ttype3:0,\n\t\t\t\ttype4:0,\n\t\t\t\ttype5:0\n            }\n        },\n        watch: {\n            num (nVal) {\n                // debugger;\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n\t\t\t\t\tthis.setUp = nVal;\n                    var arr = [this.rCom[0]];\n                    if (nVal == 0) {\n\t\t\t\t\t\tthis.getRComContent(arr,this.type,this.type2,this.type3)\n                    } else {\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type2,this.type3,this.type5,this.type4)\n                    }\n                },\n                deep: true\n            },\n\t\t\t'configObj.styleConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tthis.getRComContent(arr,nVal,this.type2,this.type3)\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.getRComStyle(arr,nVal,this.type2,this.type3,this.type5,this.type4)\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.titleConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tthis.getRComContent(arr,this.type,nVal,this.type3)\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,nVal,this.type3,this.type5,this.type4)\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.goodStyleConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type3 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tthis.getRComContent(arr,this.type,this.type2,nVal)\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.getRComStyle(arr,this.type,this.type2,nVal,this.type5,this.type4)\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.seckillConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type5 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t   this.getRComStyle(arr,this.type,this.type2,this.type3,nVal,this.type4)\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.toneConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type4 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t   this.getRComStyle(arr,this.type,this.type2,this.type3,this.type5,nVal)\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n\t\t\tgetRComContent(arr,type,type2,type3){\n\t\t\t\tif(type == 0){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 2){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.twoContent,...this.twoContentColorImg,...this.threeContent]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.twoContent,...this.twoContentColorImg,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 2){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.twoContent,...this.twoContentText,...this.threeContent]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.twoContent,...this.twoContentText,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 2){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentImg,...this.twoContent,...this.twoContentImg,...this.threeContent]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentImg,...this.twoContent,...this.twoContentImg,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 2){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentImg,...this.twoContent,...this.twoContentText,...this.threeContent]\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneContent,...this.oneContentImg,...this.twoContent,...this.twoContentText,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetRComStyle(arr,type,type2,type3,type5,type4){\n\t\t\t\tlet obj = [...arr,...this.oneStyle,...this.twoStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.currencyStyle];\n\t\t\t\tlet obj2 = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.currencyStyle];\n\t\t\t\tlet obj3 = [...arr,...this.oneStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.currencyStyle];\n\t\t\t\tlet obj4 = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.currencyStyle];\n\t\t\t\tif(type == 0){\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(type3 == 2){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle2,...this.bntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(type3 == 2){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle2,...this.bntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj2\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle2,...this.fourBntStyle,...this.fourColorStyle2,...this.fourGoodsStyle,...this.fiveStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tif(type2 == 0){\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj3\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj3\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(type3 == 2){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj3\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle2,...this.bntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj3\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj3\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(type3 == 0){\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj4\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj4\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.progressStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(type3 == 2){\n\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\tthis.rCom = obj4\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle2,...this.bntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(type5 == 0){\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj4\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.bntStyle,...this.sixStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(type4 == 0){\n\t\t\t\t\t\t\t\t\tthis.rCom = obj4\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.fourBntStyle,...this.fourColorStyle,...this.fourGoodsStyle,...this.fiveStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n            // 获取组件参数\n            getConfig (data) {}\n        }\n    }\n", null]}