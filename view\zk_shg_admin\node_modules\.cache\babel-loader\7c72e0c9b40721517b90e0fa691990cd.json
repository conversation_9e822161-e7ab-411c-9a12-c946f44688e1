{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\modal\\modal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\modal\\modal.vue", "mtime": 1725352513362}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport Icon from '../icon';\nimport iButton from '../button/button.vue';\nimport TransferDom from '../../directives/transfer-dom';\nimport Locale from '../../mixins/locale';\nimport Emitter from '../../mixins/emitter';\nimport ScrollbarMixins from './mixins-scrollbar';\nimport { on, off } from '../../utils/dom';\nimport { findComponentsDownward } from '../../utils/assist';\nimport { transferIndex as modalIndex, transferIncrease as modalIncrease } from '../../utils/transfer-queue';\nvar prefixCls = 'ivu-modal';\nexport default {\n  name: 'Modal',\n  mixins: [Locale, Emitter, ScrollbarMixins],\n  components: {\n    Icon: Icon,\n    iButton: iButton\n  },\n  directives: {\n    TransferDom: TransferDom\n  },\n  props: {\n    value: {\n      type: Boolean,\n      default: false\n    },\n    closable: {\n      type: Boolean,\n      default: true\n    },\n    maskClosable: {\n      type: Boolean,\n      default: function _default() {\n        return !this.$IVIEW || this.$IVIEW.modal.maskClosable === '' ? true : this.$IVIEW.modal.maskClosable;\n      }\n    },\n    title: {\n      type: String\n    },\n    width: {\n      type: [Number, String],\n      default: 520\n    },\n    okText: {\n      type: String\n    },\n    cancelText: {\n      type: String\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    styles: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    className: {\n      type: String\n    },\n    // for instance\n    footerHide: {\n      type: Boolean,\n      default: false\n    },\n    scrollable: {\n      type: Boolean,\n      default: false\n    },\n    transitionNames: {\n      type: Array,\n      default: function _default() {\n        return ['ease', 'fade'];\n      }\n    },\n    transfer: {\n      type: Boolean,\n      default: function _default() {\n        return !this.$IVIEW || this.$IVIEW.transfer === '' ? true : this.$IVIEW.transfer;\n      }\n    },\n    fullscreen: {\n      type: Boolean,\n      default: false\n    },\n    mask: {\n      type: Boolean,\n      default: true\n    },\n    draggable: {\n      type: Boolean,\n      default: false\n    },\n    zIndex: {\n      type: Number,\n      default: 1000\n    }\n  },\n  data: function data() {\n    return {\n      prefixCls: prefixCls,\n      wrapShow: false,\n      showHead: true,\n      buttonLoading: false,\n      visible: this.value,\n      dragData: {\n        x: null,\n        y: null,\n        dragX: null,\n        dragY: null,\n        dragging: false\n      },\n      modalIndex: this.handleGetModalIndex(),\n      // for Esc close the top modal\n      isMouseTriggerIn: false // #5800\n\n    };\n  },\n  computed: {\n    wrapClasses: function wrapClasses() {\n      var _ref;\n\n      return [\"\".concat(prefixCls, \"-wrap\"), (_ref = {}, _defineProperty(_ref, \"\".concat(prefixCls, \"-hidden\"), !this.wrapShow), _defineProperty(_ref, \"\".concat(this.className), !!this.className), _defineProperty(_ref, \"\".concat(prefixCls, \"-no-mask\"), !this.showMask), _ref)];\n    },\n    wrapStyles: function wrapStyles() {\n      return {\n        zIndex: this.modalIndex + this.zIndex\n      };\n    },\n    maskClasses: function maskClasses() {\n      return \"\".concat(prefixCls, \"-mask\");\n    },\n    classes: function classes() {\n      var _ref2;\n\n      return [\"\".concat(prefixCls), (_ref2 = {}, _defineProperty(_ref2, \"\".concat(prefixCls, \"-fullscreen\"), this.fullscreen), _defineProperty(_ref2, \"\".concat(prefixCls, \"-fullscreen-no-header\"), this.fullscreen && !this.showHead), _defineProperty(_ref2, \"\".concat(prefixCls, \"-fullscreen-no-footer\"), this.fullscreen && this.footerHide), _ref2)];\n    },\n    contentClasses: function contentClasses() {\n      var _ref3;\n\n      return [\"\".concat(prefixCls, \"-content\"), (_ref3 = {}, _defineProperty(_ref3, \"\".concat(prefixCls, \"-content-no-mask\"), !this.showMask), _defineProperty(_ref3, \"\".concat(prefixCls, \"-content-drag\"), this.draggable), _defineProperty(_ref3, \"\".concat(prefixCls, \"-content-dragging\"), this.draggable && this.dragData.dragging), _ref3)];\n    },\n    mainStyles: function mainStyles() {\n      var style = {};\n      var width = parseInt(this.width);\n      var styleWidth = this.dragData.x !== null ? {\n        top: 0\n      } : {\n        width: width <= 100 ? \"\".concat(width, \"%\") : \"\".concat(width, \"px\")\n      };\n      var customStyle = this.styles ? this.styles : {};\n      Object.assign(style, styleWidth, customStyle);\n      return style;\n    },\n    contentStyles: function contentStyles() {\n      var style = {};\n\n      if (this.draggable) {\n        var customTop = this.styles.top ? parseFloat(this.styles.top) : 0;\n        var customLeft = this.styles.left ? parseFloat(this.styles.left) : 0;\n        if (this.dragData.x !== null) style.left = \"\".concat(this.dragData.x - customLeft, \"px\");\n        if (this.dragData.y !== null) style.top = \"\".concat(this.dragData.y - customTop, \"px\");\n        var width = parseInt(this.width);\n        var styleWidth = {\n          width: width <= 100 ? \"\".concat(width, \"%\") : \"\".concat(width, \"px\")\n        };\n        Object.assign(style, styleWidth);\n      }\n\n      return style;\n    },\n    localeOkText: function localeOkText() {\n      if (this.okText === undefined) {\n        return this.t('i.modal.okText');\n      } else {\n        return this.okText;\n      }\n    },\n    localeCancelText: function localeCancelText() {\n      if (this.cancelText === undefined) {\n        return this.t('i.modal.cancelText');\n      } else {\n        return this.cancelText;\n      }\n    },\n    showMask: function showMask() {\n      return this.draggable ? false : this.mask;\n    }\n  },\n  methods: {\n    close: function close() {\n      this.visible = false;\n      this.$emit('input', false);\n      this.$emit('on-cancel');\n    },\n    handleMask: function handleMask() {\n      if (this.maskClosable && this.showMask) {\n        this.close();\n      }\n    },\n    handleWrapClick: function handleWrapClick(event) {\n      if (this.isMouseTriggerIn) {\n        this.isMouseTriggerIn = false;\n        return;\n      } // use indexOf,do not use === ,because ivu-modal-wrap can have other custom className\n\n\n      var className = event.target.getAttribute('class');\n      if (className && className.indexOf(\"\".concat(prefixCls, \"-wrap\")) > -1) this.handleMask();\n    },\n    handleMousedown: function handleMousedown() {\n      this.isMouseTriggerIn = true;\n    },\n    cancel: function cancel() {\n      this.close();\n    },\n    ok: function ok() {\n      if (this.loading) {\n        this.buttonLoading = true;\n      } else {\n        this.visible = false;\n        this.$emit('input', false);\n      }\n\n      this.$emit('on-ok');\n    },\n    EscClose: function EscClose(e) {\n      if (this.visible && this.closable) {\n        if (e.keyCode === 27) {\n          var $Modals = findComponentsDownward(this.$root, 'Modal').filter(function (item) {\n            return item.$data.visible && item.$props.closable;\n          });\n          var $TopModal = $Modals.sort(function (a, b) {\n            return a.$data.modalIndex < b.$data.modalIndex ? 1 : -1;\n          })[0];\n          setTimeout(function () {\n            $TopModal.close();\n          }, 0);\n        }\n      }\n    },\n    animationFinish: function animationFinish() {\n      this.$emit('on-hidden');\n    },\n    handleMoveStart: function handleMoveStart(event) {\n      if (!this.draggable) return false;\n      var $content = this.$refs.content;\n      var rect = $content.getBoundingClientRect();\n      this.dragData.x = rect.x || rect.left;\n      this.dragData.y = rect.y || rect.top;\n      var distance = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      this.dragData.dragX = distance.x;\n      this.dragData.dragY = distance.y;\n      this.dragData.dragging = true;\n      on(window, 'mousemove', this.handleMoveMove);\n      on(window, 'mouseup', this.handleMoveEnd);\n    },\n    handleMoveMove: function handleMoveMove(event) {\n      if (!this.dragData.dragging) return false;\n      var distance = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var diff_distance = {\n        x: distance.x - this.dragData.dragX,\n        y: distance.y - this.dragData.dragY\n      };\n      this.dragData.x += diff_distance.x;\n      this.dragData.y += diff_distance.y;\n      this.dragData.dragX = distance.x;\n      this.dragData.dragY = distance.y;\n    },\n    handleMoveEnd: function handleMoveEnd() {\n      this.dragData.dragging = false;\n      off(window, 'mousemove', this.handleMoveMove);\n      off(window, 'mouseup', this.handleMoveEnd);\n    },\n    handleGetModalIndex: function handleGetModalIndex() {\n      modalIncrease();\n      return modalIndex;\n    },\n    handleClickModal: function handleClickModal() {\n      if (this.draggable) {\n        this.modalIndex = this.handleGetModalIndex();\n      }\n    }\n  },\n  mounted: function mounted() {\n    if (this.visible) {\n      this.wrapShow = true;\n    }\n\n    var showHead = true;\n\n    if (this.$slots.header === undefined && !this.title) {\n      showHead = false;\n    }\n\n    this.showHead = showHead; // ESC close\n\n    document.addEventListener('keydown', this.EscClose);\n  },\n  beforeDestroy: function beforeDestroy() {\n    document.removeEventListener('keydown', this.EscClose);\n    this.removeScrollEffect();\n  },\n  watch: {\n    value: function value(val) {\n      this.visible = val;\n    },\n    visible: function visible(val) {\n      var _this = this;\n\n      if (val === false) {\n        this.buttonLoading = false;\n        this.timer = setTimeout(function () {\n          _this.wrapShow = false;\n\n          _this.removeScrollEffect();\n        }, 300);\n      } else {\n        this.modalIndex = this.handleGetModalIndex();\n        if (this.timer) clearTimeout(this.timer);\n        this.wrapShow = true;\n\n        if (!this.scrollable) {\n          this.addScrollEffect();\n        }\n      }\n\n      this.broadcast('Table', 'on-visible-change', val);\n      this.broadcast('Slider', 'on-visible-change', val); // #2852\n\n      this.$emit('on-visible-change', val);\n    },\n    loading: function loading(val) {\n      if (!val) {\n        this.buttonLoading = false;\n      }\n    },\n    scrollable: function scrollable(val) {\n      if (!val) {\n        this.addScrollEffect();\n      } else {\n        this.removeScrollEffect();\n      }\n    },\n    title: function title(val) {\n      if (this.$slots.header === undefined) {\n        this.showHead = !!val;\n      }\n    }\n  }\n};", null]}