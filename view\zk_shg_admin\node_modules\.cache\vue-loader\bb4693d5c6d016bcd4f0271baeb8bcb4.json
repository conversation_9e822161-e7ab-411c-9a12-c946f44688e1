{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\shortVideo\\components\\videoDetails.vue?vue&type=style&index=0&id=7db753c6&scoped=true&lang=less&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\shortVideo\\components\\videoDetails.vue", "mtime": 1661486458000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1725352507996}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.order_box{\n    box-sizing: border-box;\n    .header{\n        margin: 25px 24px;\n        .pictrue{\n            width: 60px;\n            height: 60px;\n            img{\n                width: 100%;\n                height: 100%;\n                display: block;\n            }\n        }\n        .text{\n            margin-left: 12px;\n            .name{\n                font-size: 16px;\n                font-weight: 500;\n                color: rgba(0,0,0,0.85);\n            }\n            .info{\n                font-size: 13px;\n                color: #606266;\n                margin-top: 3px;\n            }\n        }\n    }\n    .pictrueBox {\n        display: inline-block;\n        .upLoad {\n            width: 58px;\n            height: 58px;\n            line-height: 58px;\n            border: 1px dotted rgba(0, 0, 0, 0.1);\n            border-radius: 4px;\n            background: rgba(0, 0, 0, 0.02);\n            cursor: pointer;\n            .input-display {\n                display: none\n            }\n        }\n        .pictrue {\n            width: 60px;\n            height: 60px;\n            border: 1px dotted rgba(0, 0, 0, 0.1);\n            margin-right: 15px;\n            margin-bottom: 10px;\n            display: inline-block;\n            position: relative;\n            cursor: pointer;\n\n            img {\n                width: 100%;\n                height: 100%;\n            }\n\n            .btndel {\n                position: absolute;\n                z-index: 1;\n                width: 20px !important;\n                height: 20px !important;\n                left: 46px;\n                top: -4px;\n            }\n        }\n    }\n    .iview-video-style {\n        width: 40%;\n        height: 180px;\n        border-radius: 10px;\n        background-color: #707070;\n        margin-top: 10px;\n        position: relative;\n        overflow: hidden;\n        .video-style {\n            width: 100%;\n            height: 100% !important;\n            border-radius: 10px;\n        }\n        .iconv{\n            color: #fff;\n            line-height: 180px;\n            width: 50px;\n            height: 50px;\n            display: inherit;\n            font-size: 26px;\n            position: absolute;\n            top: -74px;\n            left: 50%;\n            margin-left: -25px;\n        }\n        .mark{\n            position: absolute;\n            width: 100%;\n            height: 30px;\n            top: 0;\n            background-color: rgba(0, 0, 0, 0.5);\n            text-align: center;\n        }\n    }\n    .imgPic{\n        .info{\n            width: 60%;\n            margin-left: 10px;\n        }\n        .pictrue{\n            height: 36px;\n            margin: 7px 3px 0 3px;\n            img{\n                height: 100%;\n                display: block;\n            }\n        }\n    }\n    .margins{\n        margin: 0 35px;\n    }\n}\n/deep/.ivu-tabs-ink-bar{\n    display: none;\n}\n/deep/.ivu-tabs-bar{\n    background: #F5F7FA;\n    border-bottom: 0;\n    margin-bottom: 0;\n}\n/deep/.ivu-tabs-nav-wrap {\n    margin-bottom: 0;\n}\n/deep/.ivu-tabs-nav{\n    height: 40px;\n    line-height: 40px;\n}\n/deep/.ivu-tabs-nav .ivu-tabs-tab-active{\n    color: rgba(0,0,0,0.85);\n    font-weight: 400;\n    background-color: #fff;\n    &::before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 2px;\n        background-color: #1890FF;\n    }\n}\n/deep/.ivu-tabs-tabpane {\n    padding: 15px;\n\n    &:first-child {\n        padding: 0 25px;\n    }\n}\n/deep/.ivu-tabs-nav .ivu-tabs-tab{\n    padding: 7px 19px !important;\n    margin-right: 0;\n    line-height: 26px;\n}\n/deep/.ivu-tabs-nav-container {\n    font-size: 13px;\n}\n", null]}