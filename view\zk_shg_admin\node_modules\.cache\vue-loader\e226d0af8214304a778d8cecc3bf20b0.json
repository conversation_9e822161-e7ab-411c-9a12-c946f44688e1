{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue?vue&type=template&id=accaaf10&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue", "mtime": 1718158266000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Table',{attrs:{\"columns\":_vm.thead,\"data\":_vm.tbody,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"image\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',{directives:[{name:\"viewer\",rawName:\"v-viewer\"}],staticClass:\"image-wrap\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.image),expression:\"row.image\"}]})])]}},{key:\"status\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('i-switch',{attrs:{\"value\":row.status,\"true-value\":1,\"false-value\":0,\"size\":\"large\"},on:{\"on-change\":function($event){return _vm.statusChange(row)}},model:{value:(row.status),callback:function ($$v) {_vm.$set(row, \"status\", $$v)},expression:\"row.status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"启用\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"禁用\")])])]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.edit(row)}}},[_vm._v(\"编辑\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":function($event){return _vm.editRight(row)}}},[_vm._v(\"权益介绍\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"current\":_vm.page,\"page-size\":_vm.limit,\"show-elevator\":\"\",\"show-total\":\"\"},on:{\"on-change\":_vm.pageChange}})],1)],1),_c('Modal',{attrs:{\"title\":\"权益介绍\"},on:{\"on-ok\":_vm.saveMemberContent},model:{value:(_vm.modal3),callback:function ($$v) {_vm.modal3=$$v},expression:\"modal3\"}},[_c('WangEditor',{staticStyle:{\"width\":\"100%\"},attrs:{\"content\":_vm.content},on:{\"editorContent\":_vm.getEditorContent}})],1),_c('Modal',{attrs:{\"title\":\"编辑会员权益\",\"footer-hide\":\"\"},model:{value:(_vm.modal1),callback:function ($$v) {_vm.modal1=$$v},expression:\"modal1\"}},[_c('Form',{ref:\"form\",attrs:{\"model\":_vm.form,\"rules\":_vm.rules,\"label-width\":80}},[_c('Input',{staticClass:\"display-add\",model:{value:(_vm.form.id),callback:function ($$v) {_vm.$set(_vm.form, \"id\", $$v)},expression:\"form.id\"}}),_c('Input',{staticClass:\"display-add\",model:{value:(_vm.form.status),callback:function ($$v) {_vm.$set(_vm.form, \"status\", $$v)},expression:\"form.status\"}}),_c('Input',{staticClass:\"display-add\",model:{value:(_vm.form.right_type),callback:function ($$v) {_vm.$set(_vm.form, \"right_type\", $$v)},expression:\"form.right_type\"}}),_c('FormItem',{attrs:{\"label\":\"权益名称\",\"prop\":\"title\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入权益名称\",\"disabled\":\"\"},model:{value:(_vm.form.title),callback:function ($$v) {_vm.$set(_vm.form, \"title\", $$v)},expression:\"form.title\"}})],1),_c('FormItem',{attrs:{\"label\":\"展示名称\",\"prop\":\"show_title\"}},[_c('Input',{attrs:{\"maxlength\":\"8\",\"placeholder\":\"请输入展示名称\"},model:{value:(_vm.form.show_title),callback:function ($$v) {_vm.$set(_vm.form, \"show_title\", $$v)},expression:\"form.show_title\"}})],1),_c('FormItem',{attrs:{\"label\":\"权益图标\",\"prop\":\"image\"}},[_c('div',{staticClass:\"image-group\",on:{\"click\":_vm.callImage}},[(_vm.form.image)?_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.form.image),expression:\"form.image\"}]}):_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1),_c('Input',{staticClass:\"display-add\",model:{value:(_vm.form.image),callback:function ($$v) {_vm.$set(_vm.form, \"image\", $$v)},expression:\"form.image\"}})],1),_c('FormItem',{attrs:{\"label\":\"权益简介\",\"prop\":\"show_title\"}},[_c('Input',{attrs:{\"type\":\"textarea\",\"maxlength\":\"8\",\"autosize\":{ minRows: 2, maxRows: 10 },\"placeholder\":\"请输入权益简介\"},model:{value:(_vm.form.explain),callback:function ($$v) {_vm.$set(_vm.form, \"explain\", $$v)},expression:\"form.explain\"}})],1),_c('FormItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.form.right_type !== 'coupon' && _vm.form.right_type !== 'vip_price'),expression:\"form.right_type !== 'coupon' && form.right_type !== 'vip_price'\"}],attrs:{\"label\":_vm.form.right_type === 'offline' ||\n                        _vm.form.right_type === 'express' ||\n                        _vm.form.right_type === 'vip_price'\n                            ? '折扣数(%)'\n                            : '积分倍数',\"prop\":\"number\"}},[_c('InputNumber',{attrs:{\"min\":1,\"max\":_vm.form.right_type === 'offline'?100:10000},model:{value:(_vm.form.number),callback:function ($$v) {_vm.$set(_vm.form, \"number\", $$v)},expression:\"form.number\"}})],1),_c('FormItem',[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.formSubmit('form')}}},[_vm._v(\"提交\")])],1)],1)],1),_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"选择权益图标\"},model:{value:(_vm.modal2),callback:function ($$v) {_vm.modal2=$$v},expression:\"modal2\"}},[_c('uploadPictures',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.modal2),expression:\"modal2\"}],attrs:{\"isChoice\":\"单选\",\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}