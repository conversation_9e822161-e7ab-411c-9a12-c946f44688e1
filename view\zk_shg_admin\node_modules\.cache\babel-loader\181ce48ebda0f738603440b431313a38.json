{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\goodPool\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\goodPool\\index.vue", "mtime": 1712136182752}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { AlibabaImportGoods, getAlibabaGoodList, getAlibabaCategoryList } from \"@/api/vop\";\nimport { cascaderListApi } from '@/api/product';\nexport default {\n  name: \"goodPool\",\n  data: function data() {\n    return {\n      skuId: '',\n      cate_id: '',\n      data2: [],\n      batchModal: false,\n      // 分类modal\n      data1: [],\n      // 分类数组\n      props: {\n        emitPath: false,\n        multiple: false,\n        checkStrictly: true\n      },\n      total: 0,\n      loading: false,\n      columns: [{\n        title: \"ID\",\n        key: \"openOfferId\",\n        width: 80\n      }, {\n        title: \"名称\",\n        slot: \"name\",\n        minWidth: 180\n      }, {\n        title: \"公司相关信息\",\n        slot: \"companyInfo\",\n        minWidth: 60\n      }, {\n        title: \"价格\",\n        slot: \"price\",\n        minWidth: 60\n      }, {\n        title: \"商品评价分\",\n        slot: \"qualityEvaluation\",\n        minWidth: 60\n      }, {\n        title: \"商品图片\",\n        slot: \"image\",\n        minWidth: 50\n      }, {\n        title: \"操作\",\n        slot: \"createModalFrame\",\n        fixed: \"right\",\n        width: 120\n      }],\n      tabList: [],\n      treeData: {\n        withdrawal: [{\n          title: \"48小时发货\",\n          value: 'shipIn48Hours'\n        }, {\n          title: \"7天包换\",\n          value: 'freeExchange7days'\n        }, {\n          title: \"实力商家\",\n          value: 'powerMerchant'\n        }, {\n          title: \"跨境潜力商品\",\n          value: 'crossPotential'\n        }, {\n          title: \"批发团商品\",\n          value: 'ttpft'\n        }, {\n          title: \"精选货源商品\",\n          value: 'jxhy'\n        }]\n      },\n      formValidate: {\n        filter: \"\",\n        categoryIds: \"\",\n        keywords: \"食品\",\n        priceStart: 0,\n        priceEnd: 0,\n        quantityBegin: 1,\n        pageSize: 20,\n        pageNum: 1\n      }\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  mounted: function mounted() {\n    // 初始化\n    this.getInit();\n  },\n  methods: {\n    // 导入商品库\n    dao: function dao(skuId) {\n      this.skuId = skuId;\n      this.batchModal = true;\n    },\n    saveBatch: function saveBatch() {\n      var _this = this;\n\n      this.loading = true;\n      AlibabaImportGoods({\n        skuId: this.skuId,\n        cateId: this.cate_id\n      }).then(\n      /*#__PURE__*/\n      _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _this.loading = false;\n                _this.batchModal = false;\n                _this.cate_id = [];\n\n                _this.$Message.success('同步成功');\n\n              case 4:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee);\n      }))).catch(function (res) {\n        _this.loading = false;\n\n        _this.$Message.error(res.msg);\n      });\n    },\n    getInit: function getInit() {\n      var _this2 = this;\n\n      // 设置分页\n      this.formValidate.pageNum = 1; // 获取商品分类\n\n      getAlibabaCategoryList().then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  _this2.data1 = res.data;\n\n                case 1:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      }); // 获取列表\n\n      this.getList(); // 获取商品分类2\n\n      cascaderListApi(1).then(function (res) {\n        _this2.data2 = res.data;\n      }).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    // 选择\n    selChange: function selChange() {\n      this.formValidate.pageNum = 1;\n      this.getList();\n    },\n    reset: function reset() {\n      this.formValidate = {\n        filter: \"\",\n        categoryIds: \"\",\n        keywords: \"食品\",\n        quantityBegin: 1,\n        priceStart: 0,\n        priceEnd: 0,\n        pageSize: 20,\n        pageNum: 1\n      };\n      this.getList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this3 = this;\n\n      this.loading = true;\n      getAlibabaGoodList(this.formValidate).then(\n      /*#__PURE__*/\n      function () {\n        var _ref3 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee3(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n            while (1) {\n              switch (_context3.prev = _context3.next) {\n                case 0:\n                  data = res.data;\n\n                  if (data.success) {\n                    _this3.tabList = data.result ? data.result : [];\n                    _this3.total = data.pageInfo.totalRecords;\n                  } else {\n                    _this3.tabList = [];\n                    _this3.total = 0;\n                  }\n\n                  _this3.loading = false;\n\n                case 3:\n                case \"end\":\n                  return _context3.stop();\n              }\n            }\n          }, _callee3);\n        }));\n\n        return function (_x2) {\n          return _ref3.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this3.loading = false;\n\n        _this3.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.formValidate.pageNum = index;\n      this.getList();\n    }\n  }\n};", null]}