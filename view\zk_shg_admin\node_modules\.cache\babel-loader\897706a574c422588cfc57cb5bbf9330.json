{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\couponList\\index.vue", "mtime": 1690191972000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { releasedListApi } from '@/api/marketing';\nimport { formatDate as _formatDate } from '@/utils/validate';\nexport default {\n  name: 'index',\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  props: {\n    couponids: {\n      type: Array\n    },\n    updateIds: {\n      type: Array\n    },\n    updateName: {\n      type: Array\n    },\n    luckDraw: {\n      type: Boolean,\n      default: false\n    },\n    discount: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      currentid: 0,\n      productRow: {},\n      isTemplate: false,\n      loading: false,\n      tableFrom: {\n        receive_type: 3,\n        type: 'send',\n        page: 1,\n        limit: 10\n      },\n      total: 0,\n      ids: [],\n      texts: [],\n      columns: [{\n        title: 'ID',\n        key: 'id',\n        width: 60\n      }, {\n        title: '优惠券名称',\n        key: 'title',\n        minWidth: 150\n      }, {\n        title: '适用类型',\n        slot: 'type',\n        minWidth: 80\n      }, {\n        title: '面值',\n        slot: 'coupon_price',\n        minWidth: 100\n      }, {\n        title: '最低消费额',\n        key: 'use_min_price',\n        minWidth: 100\n      }, {\n        title: '发布数量',\n        slot: 'count',\n        minWidth: 120\n      }, {\n        title: '有效期限',\n        slot: 'start_time',\n        minWidth: 120\n      }, {\n        title: '状态',\n        slot: 'status',\n        minWidth: 80\n      }],\n      couponList: [],\n      selectedIds: new Set(),\n      selectedNames: new Set(),\n      couponVal: []\n    };\n  },\n  mounted: function mounted() {},\n  watch: {\n    'updateIds': function updateIds(newVal) {\n      this.selectedIds = new Set(newVal);\n    },\n    'updateName': function updateName(newVal) {\n      this.selectedNames = new Set(newVal);\n    }\n  },\n  created: function created() {\n    var _this = this;\n\n    var radio = {\n      width: 60,\n      align: \"center\",\n      render: function render(h, params) {\n        var id = params.row.id;\n        var flag = false;\n\n        if (_this.currentid === id) {\n          flag = true;\n        } else {\n          flag = false;\n        }\n\n        var self = _this;\n        return h(\"div\", [h(\"Radio\", {\n          props: {\n            value: flag\n          },\n          on: {\n            \"on-change\": function onChange() {\n              self.currentid = id;\n              _this.productRow = params.row;\n            }\n          }\n        })]);\n      }\n    };\n    var checkbox = {\n      type: \"selection\",\n      width: 60,\n      align: \"center\"\n    };\n\n    if (this.luckDraw) {\n      this.columns.unshift(radio);\n    } else {\n      this.columns.unshift(checkbox);\n    }\n  },\n  methods: {\n    //对象数组去重；\n    unique: function unique(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.id) && res.set(arr.id, 1);\n      });\n    },\n    changeCheckbox: function changeCheckbox(selection) {\n      this.couponVal = selection;\n    },\n    handleSelectAll: function handleSelectAll(selection) {\n      var _this2 = this;\n\n      if (this.discount) return; // 取消全选 数组为空\n\n      if (selection.length === 0) {\n        // cy 若取消全选，删除保存在selectedIds里和当前table数据的id一致的数据，达到，当前页取消全选的效果\n        // 当前页的table数据\n        var that = this;\n        var data = that.$refs.table.data;\n        data.forEach(function (item) {\n          if (that.selectedIds.has(item.id)) {\n            that.selectedIds.delete(item.id);\n            var nameList = that.unique(Array.from(that.selectedNames));\n            that.unique(Array.from(that.selectedNames)).forEach(function (j, index) {\n              if (j.id === item.id) {\n                nameList.splice(index, 1);\n              }\n            });\n            that.selectedNames = new Set(nameList); // this.selectedNames.clear();\n          }\n        });\n      } else {\n        selection.forEach(function (item) {\n          _this2.selectedIds.add(item.id);\n\n          _this2.selectedNames.add({\n            id: item.id,\n            title: item.title\n          });\n        });\n      }\n\n      this.$nextTick(function () {\n        //确保dom加载完毕\n        _this2.setChecked();\n      });\n    },\n    //  选中某一行\n    handleSelectRow: function handleSelectRow(selection, row) {\n      var _this3 = this;\n\n      if (this.discount) return;\n      this.selectedIds.add(row.id);\n      this.selectedNames.add({\n        id: row.id,\n        title: row.title\n      });\n      this.$nextTick(function () {\n        //确保dom加载完毕\n        _this3.setChecked();\n      });\n    },\n    //  取消某一行\n    handleCancelRow: function handleCancelRow(selection, row) {\n      var _this4 = this;\n\n      if (this.discount) return;\n      var that = this;\n      that.selectedIds.delete(row.id);\n      var nameList = Array.from(that.selectedNames);\n      Array.from(that.selectedNames).forEach(function (item, index) {\n        if (item.id === row.id) {\n          nameList.splice(index, 1);\n        }\n      });\n      that.selectedNames = new Set(nameList);\n      this.$nextTick(function () {\n        //确保dom加载完毕\n        _this4.setChecked();\n      });\n    },\n    setChecked: function setChecked() {\n      this.ids = _toConsumableArray(this.selectedIds);\n      this.texts = _toConsumableArray(this.selectedNames); // 找到绑定的table的ref对应的dom，找到table的objData对象，objData保存的是当前页的数据\n\n      var objData = this.$refs.table.objData;\n\n      for (var index in objData) {\n        if (this.selectedIds.has(objData[index].id)) {\n          // cy 弊端 每次切换select都会触发table的on-select事件\n          // this.$refs.purchaseTable.toggleSelect(index) // 在保存选中的ids的set合集里找与当前页数据id一样的行，使用toggleSelect（index），将这一行选中\n          // cy 改进\n          objData[index]._isChecked = true;\n        }\n      }\n    },\n    cancel: function cancel() {\n      this.isTemplate = false;\n\n      if (this.luckDraw) {\n        this.currentid = 0;\n      }\n    },\n    tableList: function tableList() {\n      var _this5 = this;\n\n      this.loading = true;\n      releasedListApi(this.tableFrom).then(function (res) {\n        var data = res.data;\n        _this5.couponList = data.list;\n\n        if (!_this5.discount) {\n          _this5.$nextTick(function () {\n            //确保dom加载完毕\n            _this5.setChecked();\n          });\n        }\n\n        _this5.total = data.count;\n        _this5.loading = false;\n      });\n    },\n    ok: function ok() {\n      if (this.luckDraw) {\n        this.$emit(\"getCouponId\", this.productRow);\n        this.currentid = 0;\n      } else if (this.discount) {\n        this.$emit(\"getCouponList\", this.couponVal);\n      } else {\n        this.$emit('nameId', this.ids, this.texts);\n      }\n    },\n    receivePageChange: function receivePageChange(index) {\n      this.tableFrom.page = index;\n      this.tableList();\n    }\n  }\n};", null]}