{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\directives\\transfer-dom.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\directives\\transfer-dom.js", "mtime": 1725352513757}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["// Thanks to: https://github.com/airyland/vux/blob/v2/src/directives/transfer-dom/index.js\n// Thanks to: https://github.com/calebroseland/vue-dom-portal\n\n/**\n * Get target DOM Node\n * @param {(Node|string|Boolean)} [node=document.body] DOM Node, CSS selector, or Boolean\n * @return {Node} The target that the el will be appended to\n */\nfunction getTarget(node) {\n  if (node === void 0) {\n    node = document.body;\n  }\n\n  if (node === true) {\n    return document.body;\n  }\n\n  return node instanceof window.Node ? node : document.querySelector(node);\n}\n\nvar directive = {\n  inserted: function inserted(el, _ref, vnode) {\n    var value = _ref.value;\n    if (el.dataset && el.dataset.transfer !== 'true') return false;\n    el.className = el.className ? el.className + ' v-transfer-dom' : 'v-transfer-dom';\n    var parentNode = el.parentNode;\n    if (!parentNode) return;\n    var home = document.createComment('');\n    var hasMovedOut = false;\n\n    if (value !== false) {\n      parentNode.replaceChild(home, el); // moving out, el is no longer in the document\n\n      getTarget(value).appendChild(el); // moving into new place\n\n      hasMovedOut = true;\n    }\n\n    if (!el.__transferDomData) {\n      el.__transferDomData = {\n        parentNode: parentNode,\n        home: home,\n        target: getTarget(value),\n        hasMovedOut: hasMovedOut\n      };\n    }\n  },\n  componentUpdated: function componentUpdated(el, _ref2) {\n    var value = _ref2.value;\n    if (el.dataset && el.dataset.transfer !== 'true') return false; // need to make sure children are done updating (vs. `update`)\n\n    var ref$1 = el.__transferDomData;\n    if (!ref$1) return; // homes.get(el)\n\n    var parentNode = ref$1.parentNode;\n    var home = ref$1.home;\n    var hasMovedOut = ref$1.hasMovedOut; // recall where home is\n\n    if (!hasMovedOut && value) {\n      // remove from document and leave placeholder\n      parentNode.replaceChild(home, el); // append to target\n\n      getTarget(value).appendChild(el);\n      el.__transferDomData = Object.assign({}, el.__transferDomData, {\n        hasMovedOut: true,\n        target: getTarget(value)\n      });\n    } else if (hasMovedOut && value === false) {\n      // previously moved, coming back home\n      parentNode.replaceChild(el, home);\n      el.__transferDomData = Object.assign({}, el.__transferDomData, {\n        hasMovedOut: false,\n        target: getTarget(value)\n      });\n    } else if (value) {\n      // already moved, going somewhere else\n      getTarget(value).appendChild(el);\n    }\n  },\n  unbind: function unbind(el) {\n    if (el.dataset && el.dataset.transfer !== 'true') return false;\n    el.className = el.className.replace('v-transfer-dom', '');\n    var ref$1 = el.__transferDomData;\n    if (!ref$1) return;\n\n    if (el.__transferDomData.hasMovedOut === true) {\n      el.__transferDomData.parentNode && el.__transferDomData.parentNode.appendChild(el);\n    }\n\n    el.__transferDomData = null;\n  }\n};\nexport default directive;", null]}