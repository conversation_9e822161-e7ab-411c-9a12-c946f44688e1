{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue?vue&type=template&id=4c2bb400&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"mobile-page\"},[(_vm.isUpdate)?_c('div',[_c('div',{staticClass:\"advert\"},[_vm._l((_vm.configData.picList),function(item,index){return (_vm.style === 0)?_c('div',{key:index,staticClass:\"advertItem07\",class:_vm.currentIndex === index ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(index, _vm.configData)}}},[(item.image)?_c('img',{attrs:{\"src\":item.image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"750*375\")])]):_vm._e()}),(_vm.style === 1)?_c('div',{staticClass:\"advertItem02 acea-row\"},_vm._l((_vm.configData.picList),function(item,index){return _c('div',{key:index,staticClass:\"item\",class:_vm.currentIndex === index ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(index, _vm.configData)}}},[(item.image)?_c('img',{attrs:{\"src\":item.image}}):_c('div',{staticClass:\"empty-box\"},[_vm._m(0,true)])])}),0):_vm._e(),(_vm.style === 2)?_c('div',{staticClass:\"advertItem03 acea-row\"},_vm._l((_vm.configData.picList),function(item,index){return _c('div',{key:index,staticClass:\"item\",class:_vm.currentIndex === index ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(index, _vm.configData)}}},[(item.image)?_c('img',{attrs:{\"src\":item.image}}):_c('div',{staticClass:\"empty-box\"},[_vm._m(1,true)])])}),0):_vm._e(),(_vm.style === 3)?_c('div',{staticClass:\"advertItem08\"},[_c('div',{staticClass:\"item acea-row\"},[_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 0 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(0, _vm.configData)}}},[(_vm.configData.picList[0].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[0].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])]),_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 1 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(1, _vm.configData)}}},[(_vm.configData.picList[1].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[1].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])])]),_c('div',{staticClass:\"item\",class:_vm.currentIndex === 2 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(2, _vm.configData)}}},[(_vm.configData.picList[2].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[2].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"750*375\")])])]):_vm._e(),(_vm.style === 4)?_c('div',{staticClass:\"advertItem08\"},[_c('div',{staticClass:\"item\",class:_vm.currentIndex === 0 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(0, _vm.configData)}}},[(_vm.configData.picList[0].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[0].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"750*375\")])]),_c('div',{staticClass:\"item acea-row\"},[_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 1 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(1, _vm.configData)}}},[(_vm.configData.picList[1].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[1].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])]),_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 2 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(2, _vm.configData)}}},[(_vm.configData.picList[2].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[2].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])])])]):_vm._e(),(_vm.style === 5)?_c('div',{staticClass:\"advertItem04 acea-row\"},[_c('div',{staticClass:\"item\",class:_vm.currentIndex === 0 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(0, _vm.configData)}}},[(_vm.configData.picList[0].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[0].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*750\")])]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 1 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(1, _vm.configData)}}},[(_vm.configData.picList[1].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[1].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])]),_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 2 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(2, _vm.configData)}}},[(_vm.configData.picList[2].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[2].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])])])]):_vm._e(),(_vm.style === 6)?_c('div',{staticClass:\"advertItem04 acea-row\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 0 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(0, _vm.configData)}}},[(_vm.configData.picList[0].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[0].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])]),_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 1 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(1, _vm.configData)}}},[(_vm.configData.picList[1].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[1].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])])]),_c('div',{staticClass:\"item\",class:_vm.currentIndex === 2 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(2, _vm.configData)}}},[(_vm.configData.picList[2].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[2].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*750\")])])]):_vm._e(),(_vm.style === 7)?_c('div',{staticClass:\"advertItem06 acea-row\"},_vm._l((_vm.configData.picList),function(item,index){return _c('div',{key:index,staticClass:\"item\",class:_vm.currentIndex === index ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(index, _vm.configData)}}},[(item.image)?_c('img',{attrs:{\"src\":item.image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])])}),0):_vm._e(),(_vm.style === 8)?_c('div',{staticClass:\"advertItem08\"},[_c('div',{staticClass:\"item acea-row\"},[_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 0 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(0, _vm.configData)}}},[(_vm.configData.picList[0].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[0].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])]),_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 1 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(1, _vm.configData)}}},[(_vm.configData.picList[1].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[1].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])])]),_c('div',{staticClass:\"items acea-row\"},[_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 2 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(2, _vm.configData)}}},[(_vm.configData.picList[2].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[2].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"250*375\")])]),_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 3 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(3, _vm.configData)}}},[(_vm.configData.picList[3].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[3].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"250*375\")])]),_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 4 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(4, _vm.configData)}}},[(_vm.configData.picList[4].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[4].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"250*375\")])])])]):_vm._e(),(_vm.style === 9)?_c('div',{staticClass:\"advertItem04 acea-row\"},[_c('div',{staticClass:\"item\",class:_vm.currentIndex === 0 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(0, _vm.configData)}}},[(_vm.configData.picList[0].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[0].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*750\")])]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"pic\",class:_vm.currentIndex === 1 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(1, _vm.configData)}}},[(_vm.configData.picList[1].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[1].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*375\")])]),_c('div',{staticClass:\"pic acea-row\"},[_c('div',{staticClass:\"picItem\",class:_vm.currentIndex === 2 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(2, _vm.configData)}}},[(_vm.configData.picList[2].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[2].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*250\")])]),_c('div',{staticClass:\"picItem\",class:_vm.currentIndex === 3 ? 'on' : '',on:{\"click\":function($event){return _vm.currentTab(3, _vm.configData)}}},[(_vm.configData.picList[3].image)?_c('img',{attrs:{\"src\":_vm.configData.picList[3].image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"375*250\")])])])])]):_vm._e(),_vm._l((_vm.configData.picList),function(item,index){return (_vm.style === 10)?_c('div',{key:index,staticClass:\"advertItem01 acea-row\"},[(item.image)?_c('img',{attrs:{\"src\":item.image}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"尺寸不限\")])]):_vm._e()}),(_vm.style === 11)?[_c('div',{staticClass:\"pic-box\",on:{\"mousemove\":function($event){$event.stopPropagation();return _vm.move($event)}}},[_c('div',{staticClass:\"advertItem11 acea-row\",attrs:{\"id\":\"lay1\"},on:{\"click\":_vm.clickBox}},_vm._l((_vm.configData.picList),function(item,index){return _c('div',{key:index + 'aaa',staticClass:\"lay-item\",class:_vm.currentIndex === index ? 'on' : ''},[(item.img)?_c('img',{attrs:{\"src\":item.img}}):_c('div',{staticClass:\"empty-box\"},[_vm._v(\"+\")])])}),0),_vm._l((_vm.selBoxList),function(item,index){return _c('div',{key:index,staticClass:\"areaBox\",class:{ active: _vm.selPicBox == index },style:({\n              width: item.doc.w + 'px',\n              height: item.doc.h + 'px',\n              left: item.doc.startX + 'px',\n              top: item.doc.startY + 'px',\n            }),on:{\"mouseover\":_vm.initRect,\"click\":function($event){return _vm.currentTab(index, _vm.configData)}}},[(item.img)?_c('img',{attrs:{\"src\":item.img}}):_c('div',{staticClass:\"prompt-text\"},[_vm._v(\"\\n              \"+_vm._s(item.doc.w)+\"x\"+_vm._s(item.doc.h)+\"\\n            \")]),(_vm.selPicBox == index)?_c('div',{staticClass:\"del\",on:{\"click\":function($event){$event.stopPropagation();return _vm.delAreaBox(index)}}},[_c('Icon',{attrs:{\"type\":\"ios-close\",\"size\":\"16\"}})],1):_vm._e()])})],2)]:_vm._e()],2)]):_vm._e()])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('div',[_vm._v(\"375*750\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('div',[_vm._v(\"250*750\")])])}]\n\nexport { render, staticRenderFns }"]}