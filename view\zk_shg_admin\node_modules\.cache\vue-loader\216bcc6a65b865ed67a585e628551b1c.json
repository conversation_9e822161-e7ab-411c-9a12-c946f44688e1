{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\setupUser\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\setupUser\\index.vue", "mtime": 1689129842000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport uploadPictures from '@/components/uploadPictures'\nimport couponList from '@/components/couponList'\nimport goodsList from '@/components/goodsList'\nimport information from '@/components/information'\nimport WangEditor from '@/components/wangEditor/index.vue'\nimport { settingUser, setUser } from '@/api/user.js'\nexport default {\n  name: 'setupUser',\n  components: {\n    uploadPictures,\n    couponList,\n    goodsList,\n    WangEditor,\n    information,\n  },\n  props: {},\n  data() {\n    const validatorSingle = (rule, value, callback)=>{\n      if(value.length<2){\n        callback(new Error('单选项最少输入2个'));\n      }else{\n        callback();\n      }\n    };\n    const validatorActive = (rule, value, callback)=>{\n      if(value===\"\"||value == null || value<0){\n        callback(new Error('活动价不能为空'));\n      }else{\n        callback();\n      }\n    };\n    return {\n      tabVal: 'basic',\n      paySwitch: 1,\n      phoneSwitch: 1,\n      indexCoupon: 0,\n      val: '',\n      formActive:{\n        activeInput: 0\n      },\n      basicsForm: {},\n      selectArr: [],\n      value: '',\n      formItem: {\n        info: '',\n        format: '',\n        tip: '',\n        single: '',\n        singlearr: [],\n      },\n      activityShow: false,\n      isChoice: '单选',\n      modalPic: false,\n      loading: false,\n      addModel: false,\n      inputShow: false,\n      modals: false, // 添加商品弹窗\n      authorizedPicture: '', // 图片\n      //选中的数组\n      ids: [],\n      // 基础信息表单\n      // basicsForm: {\n      //   h5_avatar: 'jjj',\n      //   format: '',\n      // },\n      avatar: {},\n\n      member_card_status:1,\n      svip_price_status: 0,\n      // 登录注册表单\n      loginForm: {\n\t\tstore_user_agreement: 0, //自动登录\n        newcomer_status: '1',\n        store_user_mobile: '', // 手机号强制开启\n        newcomer_limit_status: '', // 是否限时\n        newcomer_limit_time: '', // 限时时间\n        register_integral_status: '', // 赠送积分开启或者关闭 1开启0关闭\n        register_give_integral: '', // 赠送积分数量\n        register_money_status: '', // 赠送余额开启\n        register_give_money: '', // 赠送余额数量\n        register_coupon_status: '', // 赠送优惠券开启\n        register_give_coupon: [], // 赠送优惠券数量\n        first_order_status: '', // 首单优惠开启\n        first_order_discount: '', // 首单优惠折扣\n        first_order_discount_limit: '', // 首单优惠折扣上限\n        register_price_status: '', // 新人专享价开启\n        product: [],\n        newcomer_agreement: '',\n        register_notice: ''\n      },\n      newcomer_agreement: '',\n      product_list: [],\n      // 会员等级表单\n      vipForm: {\n        member_func_status: 0, // 等级启用\n        sign_give_exp: '', //签到赠送\n        order_give_exp: '', // 下单赠送\n        invite_user_exp: '', // 邀请新用户\n        level_activate_status: 1, // 会员卡激活开启 1开启 0 关闭\n        level_extend_info: [], // 会员卡信息\n        level_integral_status: 1, // 赠送积分开启\n        level_give_integral: 8, // 赠送积分数量\n        level_money_status: 1, // 赠送余额开启\n        level_give_money: 15, // 赠送余额数量\n        level_coupon_status: 1, // 赠送优惠券开启\n        level_give_coupon: [], // 赠送优惠券数量\n      },\n      isShow: false,\n\n      formatList: [\n        {\n          value: 'text',\n          label: '文本',\n        },\n        {\n          value: 'num',\n          label: '数字',\n        },\n        {\n          value: 'date',\n          label: '日期',\n        },\n        {\n          value: 'radio',\n          label: '单选项',\n        },\n        {\n          value: 'id',\n          label: '身份证',\n        },\n        {\n          value: 'mail',\n          label: '邮件',\n        },\n        {\n          value: 'phone',\n          label: '手机号',\n        },\n        {\n          value: 'address',\n          label: '地址',\n        },\n      ],\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      columns: [\n        {\n          title: '',\n          slot: 'drag',\n          width: 50,\n        },\n        {\n          title: '信息',\n          key: 'info',\n          width: 120,\n        },\n        {\n          title: '使用',\n          slot: 'use',\n          width: 70,\n        },\n        {\n          title: '必填',\n          slot: 'required',\n          width: 70,\n        },\n        {\n          title: '用户端展示',\n          slot: 'user_show',\n          minWidth: 70,\n        },\n        {\n          title: '信息格式',\n          key: 'label',\n          minWidth: 120,\n        },\n        {\n          title: '提示信息',\n          key: 'tip',\n          minWidth: 120,\n        },\n        {\n          title: '操作',\n          slot: 'action',\n          minWidth: 80,\n        },\n      ],\n      columns3: [\n        {\n          title: '信息',\n          key: 'info',\n          width: 120,\n        },\n\n        {\n          title: '必填',\n          slot: 'required',\n          width: 70,\n        },\n\n        {\n          title: '信息格式',\n          key: 'label',\n          minWidth: 120,\n        },\n        {\n          title: '提示信息',\n          key: 'tip',\n          minWidth: 120,\n        },\n        {\n          title: '操作',\n          slot: 'action',\n          minWidth: 80,\n        },\n      ],\n      listOne: [],\n      listVip: [],\n      promotionsData: [\n        {\n          threshold: 0,\n          give_integral: 0,\n          checkIntegral: false,\n          checkCoupon: false,\n          checkGoods: false,\n          giveProducts: [],\n          giveCoupon: [],\n        },\n      ],\n      tableData: [],\n      columns1: [\n        {\n          title: '优惠券名称',\n          key: 'title',\n          minWidth: 150,\n        },\n        {\n          title: '类型',\n          slot: 'coupon_type',\n          minWidth: 80,\n        },\n        {\n          title: '面值',\n          slot: 'coupon_price',\n          minWidth: 100,\n        },\n        {\n          title: '最低消费额',\n          key: 'use_min_price',\n          minWidth: 100,\n        },\n        // {\n        //   title: '限量',\n        //   key: 'limit_num',\n        //   width: 120,\n        //   render: (h, params) => {\n        //     return h('div', [\n        //       h('InputNumber', {\n        //         props: {\n        //           value: params.row.limit_num,\n        //           placeholder: '请输入',\n        //           precision: 0,\n        //           min: 0,\n        //         },\n        //         on: {\n        //           'on-change': (e) => {\n        //             params.row.limit_num = e\n        //             this.promotionsData[params.row.indexCoupon].giveCoupon[\n        //               params.index\n        //             ].limit_num = e\n        //           },\n        //         },\n        //       }),\n        //     ])\n        //   },\n        // },\n        {\n          title: '操作',\n          slot: 'status',\n          align: 'center',\n          minWidth: 80,\n        },\n      ],\n      ruleValidate: {\n        info: [\n          {\n            required: true,\n            message: '信息名称不能为空',\n            trigger: 'blur',\n          },\n        ],\n        format: [\n          {\n            required: true,\n            message: '信息格式不能为空',\n            trigger: 'blur',\n          },\n        ],\n        tip: [\n          {\n            required: true,\n            message: '信息文案不能为空',\n            trigger: 'blur',\n          },\n        ],\n        singlearr: [\n          { required: true, validator:validatorSingle,type: 'array', trigger: 'blur' },\n        ],\n      },\n      ruleActive:{\n        activeInput: [\n          {\n            required: true,\n            validator:validatorActive,\n            trigger: 'blur'\n          },\n        ]\n      },\n      couponType: 0,\n      vipCopon: [],\n    }\n  },\n  computed: {},\n  created() {\n    if(this.$route.query.type=='register'){\n      this.tabVal = 'register';\n    }else if(this.$route.query.type=='level'){\n      this.tabVal = 'level';\n    }else{\n      this.tabVal = 'basic';\n    }\n    this.settingUser()\n  },\n  mounted() {},\n\n  methods: {\n    tapCheckbox(e){\n      console.log('sdsdsd',e)\n    },\n    informationTap(){\n      this.$refs.information.isShow = true;\n    },\n    onDragDrop(first, end) {\n      //转成int型，方便后续使用\n      first = parseInt(first)\n      end = parseInt(end)\n\n      let tmp = this.listOne[first]\n\n      if(first < end) {\n        for(var i=first+1; i<=end; i++) {\n          this.listOne.splice(i-1,1,this.listOne[i])\n        }\n        this.listOne.splice(end,1,tmp)\n      }\n\n      if(first > end) {\n        for(var i=first; i>end; i--) {\n          this.listOne.splice(i, 1, this.listOne[i-1])\n        }\n        this.listOne.splice(end, 1, tmp)\n      }\n\n      //重置排序值\n      let index = 1\n      this.listOne.forEach(e => {\n        e.sort = index\n        index++\n      })\n      //排序值重置后，向后台发送请求，更新数据库中数据的排序，这里就不写了\n      //axios\n\n      console.log(JSON.stringify(this.listOne))\n    },\n    // 获取用户配置\n    settingUser() {\n      settingUser(this.tabVal).then((res) => {\n        if (this.tabVal === 'basic') {\n          this.authorizedPicture = res.data.h5_avatar\n          this.listOne = res.data.user_extend_info\n        }\n        if (this.tabVal === 'register') {\n          this.loginForm = res.data\n          this.promotionsData[0].giveCoupon = res.data.register_give_coupon\n\n          // 添加活动价格\n          const addKey = (uni) =>\n            uni.map((item) => ({\n              ...item,\n              ativity_price: item.price,\n              id: item.product_id,\n              attrValue: item.attrValue ? addKey(item.attrValue) : [],\n            }))\n          this.tableData = addKey(res.data.product)\n        }\n        if (this.tabVal === 'level') {\n          this.vipForm = res.data\n          this.vipCopon = res.data.level_give_coupon\n          res.data.level_extend_info.forEach(item=>{\n            if(item.required==1 || item.required==true){\n              item.required = true\n            }else{\n              item.required = false\n            }\n          });\n          this.listVip = res.data.level_extend_info\n        }\n        if (this.tabVal === 'svip') {\n          this.member_card_status = res.data.member_card_status;\n          this.svip_price_status = res.data.svip_price_status\n        }\n      })\n    },\n\n    //全选\n    selectAll(row) {\n      this.selectArr = row.records\n    },\n\n    // 批量设置活动价\n    activityShowFn() {\n      if (this.selectArr.length === 0) {\n        this.$Message.error('请先选择设置活动价的商品！')\n      } else {\n        this.activityShow = true\n      }\n    },\n\n    cancel(name){\n      this.activityShow = false\n      this.$refs[name].resetFields();\n    },\n\n    ok(name) {\n      this.$refs[name].validate((valid)=>{\n        if(valid){\n          this.selectArr.forEach((item) => {\n            item.ativity_price = this.formActive.activeInput\n          });\n          this.activityShow = false\n          this.$refs[name].resetFields();\n        }\n      });\n    },\n\n    // 批量删除商品\n    delAll() {\n      if (this.selectArr.length === 0) {\n        this.$Message.error('请先选择删除的商品！')\n      } else {\n        this.$Modal.confirm({\n          title: '删除确认',\n          content: '您确认要删除这些商品？',\n          onOk: () => {\n            this.selectArr.forEach((row) => {\n              this.tableData.forEach((i, index) => {\n                if (row.id == i.id) {\n                  this.tableData.splice(index, 1)\n                } else {\n                  i.attrValue.forEach((j, indexn) => {\n                    if (row.id == j.id) {\n                      if (i.attrValue.length == 1) {\n                        this.tableData.splice(index, 1)\n                      } else {\n                        i.attrValue.splice(indexn, 1)\n                      }\n                    }\n                  })\n                }\n              })\n            })\n          },\n        })\n      }\n    },\n\n    // 切换tabs\n    tabChange() {\n      this.settingUser()\n    },\n\n    // 选中图片\n    getPic(pc) {\n      this.authorizedPicture = pc.att_dir\n      this.modalPic = false\n    },\n\n    // 选择图片\n    modalPicTap() {\n      this.modalPic = true\n    },\n\n    // 取消新增信息\n    cancelSubmit() {\n      this.formItem = {\n        info: '',\n        format: '',\n        tip: '',\n        single: '',\n        singlearr: [],\n      }\n      this.addModel = false\n      this.$refs.formValidate.resetFields()\n    },\n    // 提交信息\n    addSubmit() {\n      this.$refs.formValidate.validate((valid) => {\n        let obj = {\n          ...this.formItem,\n          required: 0,\n          use: 0,\n          user_show: 0,\n          label: ''\n        };\n        switch (obj.format) {\n          case 'text':\n            obj.label = '文本';\n            break;\n          case 'num':\n            obj.label = '数字';\n            break;\n          case 'date':\n            obj.label = '日期';\n            break;\n          case 'radio':\n            obj.label = '单选项';\n            break;\n          case 'id':\n            obj.label = '身份证';\n            break;\n          case 'mail':\n            obj.label = '邮件';\n            break;\n          case 'phone':\n            obj.label = '手机号';\n            break;\n          case 'address':\n            obj.label = '地址';\n            break;\n        }\n        let labelName = [];\n        this.listOne.forEach(item=>{\n          labelName.push(item.info)\n        });\n        if (valid) {\n          if(labelName.indexOf(obj.info) == -1){\n            this.listOne.push(obj)\n            this.cancelSubmit();\n          }else{\n            this.$Message.error('该信息已经添加过')\n          }\n        }\n      })\n    },\n    // 信息删除\n    delInfo(index) {\n      this.listOne.splice(index, 1)\n    },\n    delVip(row, index) {\n      this.listVip.splice(index, 1)\n    },\n\n    // 输入后回车\n    addlabel() {\n      if (!this.formItem.single) {\n        return\n      }\n      let count = this.formItem.singlearr.indexOf(this.formItem.single)\n      if (count === -1) {\n        this.formItem.singlearr.push(this.formItem.single)\n      }\n      this.formItem.single = ''\n    },\n\n    // 表单提交\n    handleSubmit(val) {\n      switch (val) {\n        case 'basic':\n          let data = {\n            h5_avatar: this.authorizedPicture,\n            user_extend_info: this.listOne,\n          }\n          setUser(val, data).then((res) => {\n            this.$Message.success(res.msg)\n          })\n          break\n        case 'register':\n          this.product_list = []\n          this.tableData.forEach((item) => {\n            let obj = {\n              product_id: item.id,\n              price: item.ativity_price,\n              attr: [],\n            }\n\n            if (item.attrValue.length) {\n              item.attrValue.forEach((j) => {\n                let newAttr = { unique: j.unique, price: j.ativity_price }\n                obj.attr.push(newAttr)\n              })\n            }\n            this.product_list.push(obj)\n          })\n          let ids = this.promotionsData[0].giveCoupon.map((item) => item.id)\n          this.loginForm.register_give_coupon = Array.from(new Set(ids))\n          this.loginForm.product = this.product_list\n          this.loginForm.newcomer_agreement = this.newcomer_agreement\n          setUser(val, this.loginForm).then((res) => {\n            this.$Message.success(res.msg)\n          }).catch(err=>{\n            this.$Message.error(err.msg)\n          })\n          break\n        case 'level':\n          let arrIds = this.vipCopon.map((item) => item.id)\n          this.vipForm.level_give_coupon = Array.from(new Set(arrIds))\n          this.vipForm.level_extend_info = this.listVip\n          setUser(val, this.vipForm).then((res) => {\n            this.$Message.success(res.msg)\n          }).catch(err=>{\n            this.$Message.error(err.msg)\n          });\n          break\n        case 'svip':\n          let vipData = {\n            member_card_status: this.member_card_status,\n            svip_price_status: this.svip_price_status,\n          }\n          setUser(val, vipData).then((res) => {\n            this.$Message.success(res.msg)\n          })\n          break\n      }\n    },\n\n    // 添加优惠券\n    addCoupon(index) {\n      this.indexCoupon = index\n      this.$refs.couponTemplates.isTemplate = true\n      this.$refs.couponTemplates.tableList()\n    },\n    handleClose(event, name) {\n      const index = this.formItem.singlearr.indexOf(name)\n      this.formItem.singlearr.splice(index, 1)\n    },\n\n    // 优惠卷表格\n    getCouponList(data) {\n      let indexCoupon = this.indexCoupon\n      this.$refs.couponTemplates.isTemplate = false\n      data.forEach((j) => {\n        j.limit_num = 0\n        j.indexCoupon = indexCoupon\n      })\n      let list = this.promotionsData[indexCoupon].giveCoupon.concat(data)\n      let uni = this.unique(list)\n      if (this.tabVal === 'register') {\n        this.promotionsData[indexCoupon].giveCoupon = uni\n      } else {\n        this.vipCopon = uni\n      }\n    },\n\n    // 删除优惠券\n    delCoupon(index, indexw) {\n      if (this.tabVal === 'level') {\n        this.vipCopon.splice(index, 1)\n      }\n      this.promotionsData[indexw].giveCoupon.splice(index, 1)\n    },\n\n    // 添加商品\n    addGoods(index) {\n      this.modals = true\n    },\n\n    // 设置活动价格\n    inputChange(row) {\n      if (row.attrValue.length > 0) {\n        row.attrValue.forEach((item) => {\n          item.ativity_price = row.ativity_price\n        })\n      }\n    },\n\n    // 删除商品\n    del(row) {\n      this.tableData.forEach((i, index) => {\n        if (row.id == i.id) {\n          return this.tableData.splice(index, 1)\n        } else {\n          i.attrValue.forEach((j, indexn) => {\n            if (row.id == j.id) {\n              if (i.attrValue.length == 1) {\n                return this.tableData.splice(index, 1)\n              } else {\n                return i.attrValue.splice(indexn, 1)\n              }\n            }\n          })\n        }\n      })\n    },\n\n    // 对象数组去重\n    unique(arr) {\n      const res = new Map()\n      return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1))\n    },\n\n    // 添加商品\n    getProductId(data) {\n      this.modals = false\n      let list = this.tableData.concat(data)\n      let uni = this.unique(list)\n      uni.forEach((i) => {\n        i.attrValue.forEach((j) => {\n          j.cate_name = i.cate_name\n          j.store_label = i.store_label\n        })\n      })\n\n      // 添加活动价格\n      const addKey = (uni) =>\n        uni.map((item) => ({\n          ...item,\n          ativity_price: '',\n          attrValue: item.attrValue ? addKey(item.attrValue) : [], // 这里要判断原数据有没有子级如果没有判断会报错\n        }))\n      this.tableData = addKey(uni)\n    },\n\n    // 选择信息\n    getInfoList(data) {\n      let list = this.listVip.concat(data)\n      let uni = this.uniqueVip(list)\n      uni.forEach(item=>{\n        if(item.required==1 || item.required==true){\n          item.required = true\n        }else{\n          item.required = false\n        }\n      });\n      this.listVip = uni\n      this.$refs.information.isShow = false\n    },\n\n    // 对象数组去重；\n    uniqueVip(arr) {\n      const res = new Map()\n      return arr.filter((arr) => !res.has(arr.info) && res.set(arr.info, 1))\n    },\n\n    // 规则详情\n    getEditorContent(data) {\n      this.newcomer_agreement = data;\n    }\n  },\n}\n", null]}