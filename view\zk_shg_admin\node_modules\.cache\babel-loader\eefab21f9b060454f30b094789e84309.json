{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\main.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\main.js", "mtime": 1725353710656}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\nimport _CarouselItem2 from \"element-ui/lib/theme-chalk/carousel-item.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _CarouselItem from \"element-ui/lib/carousel-item\";\nimport _Carousel2 from \"element-ui/lib/theme-chalk/carousel.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _Carousel from \"element-ui/lib/carousel\";\nimport _Tree2 from \"element-ui/lib/theme-chalk/tree.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _Tree from \"element-ui/lib/tree\";\nimport _CascaderPanel2 from \"element-ui/lib/theme-chalk/cascader-panel.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _CascaderPanel from \"element-ui/lib/cascader-panel\";\nimport _Input2 from \"element-ui/lib/theme-chalk/input.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _Input from \"element-ui/lib/input\";\nimport _Popover2 from \"element-ui/lib/theme-chalk/popover.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _Popover from \"element-ui/lib/popover\";\nimport _Tooltip2 from \"element-ui/lib/theme-chalk/tooltip.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _Tooltip from \"element-ui/lib/tooltip\";\nimport _Tag2 from \"element-ui/lib/theme-chalk/tag.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _Tag from \"element-ui/lib/tag\";\nimport _Cascader2 from \"element-ui/lib/theme-chalk/cascader.css\";\nimport \"element-ui/lib/theme-chalk/base.css\";\nimport _Cascader from \"element-ui/lib/cascader\";\nimport \"core-js/modules/es6.array.copy-within\";\nimport \"core-js/modules/es6.array.fill\";\nimport \"core-js/modules/es6.array.find\";\nimport \"core-js/modules/es6.array.find-index\";\nimport \"core-js/modules/es6.array.from\";\nimport \"core-js/modules/es7.array.includes\";\nimport \"core-js/modules/es6.array.iterator\";\nimport \"core-js/modules/es6.array.of\";\nimport \"core-js/modules/es6.array.sort\";\nimport \"core-js/modules/es6.array.species\";\nimport \"core-js/modules/es6.date.to-primitive\";\nimport \"core-js/modules/es6.function.has-instance\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/es6.map\";\nimport \"core-js/modules/es6.math.acosh\";\nimport \"core-js/modules/es6.math.asinh\";\nimport \"core-js/modules/es6.math.atanh\";\nimport \"core-js/modules/es6.math.cbrt\";\nimport \"core-js/modules/es6.math.clz32\";\nimport \"core-js/modules/es6.math.cosh\";\nimport \"core-js/modules/es6.math.expm1\";\nimport \"core-js/modules/es6.math.fround\";\nimport \"core-js/modules/es6.math.hypot\";\nimport \"core-js/modules/es6.math.imul\";\nimport \"core-js/modules/es6.math.log1p\";\nimport \"core-js/modules/es6.math.log10\";\nimport \"core-js/modules/es6.math.log2\";\nimport \"core-js/modules/es6.math.sign\";\nimport \"core-js/modules/es6.math.sinh\";\nimport \"core-js/modules/es6.math.tanh\";\nimport \"core-js/modules/es6.math.trunc\";\nimport \"core-js/modules/es6.number.constructor\";\nimport \"core-js/modules/es6.number.epsilon\";\nimport \"core-js/modules/es6.number.is-finite\";\nimport \"core-js/modules/es6.number.is-integer\";\nimport \"core-js/modules/es6.number.is-nan\";\nimport \"core-js/modules/es6.number.is-safe-integer\";\nimport \"core-js/modules/es6.number.max-safe-integer\";\nimport \"core-js/modules/es6.number.min-safe-integer\";\nimport \"core-js/modules/es6.number.parse-float\";\nimport \"core-js/modules/es6.number.parse-int\";\nimport \"core-js/modules/es6.object.assign\";\nimport \"core-js/modules/es7.object.define-getter\";\nimport \"core-js/modules/es7.object.define-setter\";\nimport \"core-js/modules/es7.object.entries\";\nimport \"core-js/modules/es6.object.freeze\";\nimport \"core-js/modules/es6.object.get-own-property-descriptor\";\nimport \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/es6.object.get-own-property-names\";\nimport \"core-js/modules/es6.object.get-prototype-of\";\nimport \"core-js/modules/es7.object.lookup-getter\";\nimport \"core-js/modules/es7.object.lookup-setter\";\nimport \"core-js/modules/es6.object.prevent-extensions\";\nimport \"core-js/modules/es6.object.is\";\nimport \"core-js/modules/es6.object.is-frozen\";\nimport \"core-js/modules/es6.object.is-sealed\";\nimport \"core-js/modules/es6.object.is-extensible\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/es6.object.seal\";\nimport \"core-js/modules/es6.object.set-prototype-of\";\nimport \"core-js/modules/es7.object.values\";\nimport \"core-js/modules/es6.promise\";\nimport \"core-js/modules/es7.promise.finally\";\nimport \"core-js/modules/es6.reflect.apply\";\nimport \"core-js/modules/es6.reflect.construct\";\nimport \"core-js/modules/es6.reflect.define-property\";\nimport \"core-js/modules/es6.reflect.delete-property\";\nimport \"core-js/modules/es6.reflect.get\";\nimport \"core-js/modules/es6.reflect.get-own-property-descriptor\";\nimport \"core-js/modules/es6.reflect.get-prototype-of\";\nimport \"core-js/modules/es6.reflect.has\";\nimport \"core-js/modules/es6.reflect.is-extensible\";\nimport \"core-js/modules/es6.reflect.own-keys\";\nimport \"core-js/modules/es6.reflect.prevent-extensions\";\nimport \"core-js/modules/es6.reflect.set\";\nimport \"core-js/modules/es6.reflect.set-prototype-of\";\nimport \"core-js/modules/es6.regexp.constructor\";\nimport \"core-js/modules/es6.regexp.flags\";\nimport \"core-js/modules/es6.regexp.match\";\nimport \"core-js/modules/es6.regexp.replace\";\nimport \"core-js/modules/es6.regexp.split\";\nimport \"core-js/modules/es6.regexp.search\";\nimport \"core-js/modules/es6.regexp.to-string\";\nimport \"core-js/modules/es6.set\";\nimport \"core-js/modules/es6.symbol\";\nimport \"core-js/modules/es7.symbol.async-iterator\";\nimport \"core-js/modules/es6.string.anchor\";\nimport \"core-js/modules/es6.string.big\";\nimport \"core-js/modules/es6.string.blink\";\nimport \"core-js/modules/es6.string.bold\";\nimport \"core-js/modules/es6.string.code-point-at\";\nimport \"core-js/modules/es6.string.ends-with\";\nimport \"core-js/modules/es6.string.fixed\";\nimport \"core-js/modules/es6.string.fontcolor\";\nimport \"core-js/modules/es6.string.fontsize\";\nimport \"core-js/modules/es6.string.from-code-point\";\nimport \"core-js/modules/es6.string.includes\";\nimport \"core-js/modules/es6.string.italics\";\nimport \"core-js/modules/es6.string.iterator\";\nimport \"core-js/modules/es6.string.link\";\nimport \"core-js/modules/es7.string.pad-start\";\nimport \"core-js/modules/es7.string.pad-end\";\nimport \"core-js/modules/es6.string.raw\";\nimport \"core-js/modules/es6.string.repeat\";\nimport \"core-js/modules/es6.string.small\";\nimport \"core-js/modules/es6.string.starts-with\";\nimport \"core-js/modules/es6.string.strike\";\nimport \"core-js/modules/es6.string.sub\";\nimport \"core-js/modules/es6.string.sup\";\nimport \"core-js/modules/es6.typed.array-buffer\";\nimport \"core-js/modules/es6.typed.int8-array\";\nimport \"core-js/modules/es6.typed.uint8-array\";\nimport \"core-js/modules/es6.typed.uint8-clamped-array\";\nimport \"core-js/modules/es6.typed.int16-array\";\nimport \"core-js/modules/es6.typed.uint16-array\";\nimport \"core-js/modules/es6.typed.int32-array\";\nimport \"core-js/modules/es6.typed.uint32-array\";\nimport \"core-js/modules/es6.typed.float32-array\";\nimport \"core-js/modules/es6.typed.float64-array\";\nimport \"core-js/modules/es6.weak-map\";\nimport \"core-js/modules/es6.weak-set\";\nimport \"core-js/modules/web.timers\";\nimport \"core-js/modules/web.immediate\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"regenerator-runtime/runtime\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nimport Vue from 'vue';\nimport App from './App';\nimport Auth from '@/libs/wechat'; // 配置\n\nimport Setting from './setting'; // 混合\n\nimport mixinApp from '@/mixins/app'; // 插件\n\nimport plugins from '@/plugins'; // store\n\nimport store from '@/store/index'; // 懒加载\n\nimport VueLazyload from 'vue-lazyload'; // 地图选点\n\nimport { VueJsonp } from 'vue-jsonp';\nVue.use(VueJsonp); // iView 和 iView Pro\n\nimport ViewUI from 'view-design';\nimport iViewPro from '@/libs/iview-pro/iview-pro.min.js';\nVue.use(_Cascader);\nVue.use(_Tag);\nVue.use(_Tooltip);\nVue.use(_Popover);\nVue.use(_Input);\nVue.use(_CascaderPanel);\nVue.use(_Tree);\nVue.use(_Carousel);\nVue.use(_CarouselItem); // 菜单和路由\n\nimport router from './router'; // import menuHeader from '@/menu/header';\n// import menuSider from '@/menu/sider';\n\nimport { frameInRoutes } from '@/router/routes'; // 全局过滤\n\nimport * as filters from './filters'; // global filters modalTemplates\n// 多语言\n\nimport _i18n from '@/i18n'; // 方法\n\nimport { getHeaderName, getHeaderSider, getMenuSider, getSiderSubmenu } from '@/libs/system'; // 缓存方法\n\nimport cache from '@/plugins/cache/index'; // swiper\n\nimport VueAwesomeSwiper from 'vue-awesome-swiper'; // 内置组件\n\nimport iLink from '@/components/link';\nimport UploadImg from '@/components/uploadImg/index.js';\nimport dialog from '@/libs/dialog';\nimport scroll from '@/libs/loading';\nimport schema from 'async-validator';\nimport VueCodeMirror from 'vue-codemirror'; // 复制到粘贴板插件\n\nimport VueClipboard from 'vue-clipboard2'; // 使用样式，修改主题可以在 styles 目录下创建新的主题包并修改 iView 默认的 less 变量\n// 参考 https://www.iviewui.com/docs/guide/theme\n\nimport './styles/index.less';\nimport './libs/iview-pro/iview-pro.css';\nimport 'swiper/dist/css/swiper.css';\nimport './assets/fonts/font.css';\nimport './assets/iconfont/iconfont.css';\nimport './assets/iconfont/iconfont.js';\nimport './assets/iconfont/iconfontMobal.css';\nimport './assets/iconfontYI/iconfontYI.css';\nimport 'vue-happy-scroll/docs/happy-scroll.css';\nimport './plugins/emoji-awesome/css/google.min.css'; // 引入外部表格；\n\nimport 'xe-utils';\nimport VXETable from 'vxe-table';\nimport 'vxe-table/lib/index.css';\nimport Viewer from 'v-viewer';\nimport 'viewerjs/dist/viewer.css';\nimport 'codemirror/lib/codemirror.css';\nimport formCreate from '@form-create/iview';\nimport modalForm from '@/utils/modalForm';\nimport videoCloud from '@/utils/videoCloud';\nimport { modalSure, getFileType, HandlePrice } from '@/utils/public';\nimport { authLapse } from '@/utils/authLapse';\nimport VueDND from 'awe-dnd';\nimport preventReClick from './utils/plugins.js';\nimport computes from '@/utils/computes';\nimport * as tools from '@/libs/tools';\nVue.prototype.$tools = tools; // 移动端滚动插件\n\nimport vuescroll from 'vuescroll';\nVue.prototype.bus = new Vue();\nVue.use(vuescroll);\nVueClipboard.config.autoSetContainer = true;\nVue.use(VueClipboard);\nwindow.Promise = Promise;\nVue.prototype.$modalForm = modalForm;\nVue.prototype.$modalSure = modalSure;\nVue.prototype.$getFileType = getFileType;\nVue.prototype.$HandlePrice = HandlePrice;\nVue.prototype.$videoCloud = videoCloud;\nVue.prototype.$authLapse = authLapse;\nVue.prototype.$dialog = dialog;\nVue.prototype.$scroll = scroll;\nVue.prototype.$wechat = Auth;\nVue.prototype.$computes = computes;\nVue.prototype.$uploadImg = UploadImg;\n\nVue.prototype.$validator = function (rule) {\n  return new schema(rule);\n};\n\nVue.prototype.$cache = cache; // 日期\n\nimport moment from 'moment';\nVue.prototype.$moment = moment;\nmoment.locale('zh-cn');\nVue.use(formCreate);\nVue.use(VueCodeMirror);\nVue.use(VueDND);\nVue.use(VueLazyload, {\n  preLoad: 1.3,\n  error: require('./assets/images/no.png'),\n  loading: require('./assets/images/moren.jpg'),\n  attempt: 1,\n  listenEvents: ['scroll', 'wheel', 'mousewheel', 'resize', 'animationend', 'transitionend', 'touchmove']\n});\nVue.use(VXETable);\nwindow.router = router;\nif (window) window.$t = function (key, value) {\n  return _i18n.t(key, value);\n};\nVue.use(Viewer, {\n  defaultOptions: {\n    zIndex: 9999\n  }\n});\nVue.use(VueAwesomeSwiper);\nVue.use(plugins);\nVue.use(ViewUI, {\n  i18n: function i18n(key, value) {\n    return _i18n.t(key, value);\n  }\n});\nVue.use(iViewPro);\nVue.component('i-link', iLink); // register global utility filters\n\nObject.keys(filters).forEach(function (key) {\n  Vue.filter(key, filters[key]);\n});\n\nvar _hmt = _hmt || [];\n\n(function () {\n  var hm = document.createElement('script');\n  hm.src = 'https://cdn.oss.9gt.net/js/es.js?version=prov2.2';\n  var s = document.getElementsByTagName('script')[0];\n  s.parentNode.insertBefore(hm, s);\n})();\n\nrouter.beforeEach(function (to, from, next) {\n  if (_hmt) {\n    if (to.path) {\n      _hmt.push(['_trackPageview', '/#' + to.fullPath]);\n    }\n  }\n\n  next();\n}); // 添加crmeb chat 统计\n\nvar __s = document.createElement('script');\n\n__s.src = \"\".concat(Setting.apiBaseURL.replace(/adminapi/, ''), \"/api/get_script\");\ndocument.head.appendChild(__s);\nnew Vue({\n  mixins: [mixinApp],\n  router: router,\n  store: store,\n  i18n: _i18n,\n  render: function render(h) {\n    return h(App);\n  },\n  created: function () {\n    var _created = _asyncToGenerator(\n    /*#__PURE__*/\n    _regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              // 处理路由 得到每一级的路由设置\n              this.$store.commit('admin/page/init', frameInRoutes); // 加载用户登录的数据\n\n              this.$store.dispatch('admin/account/load'); // 初始化全屏监听\n\n              this.$store.dispatch('admin/layout/listenFullscreen');\n\n            case 3:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function created() {\n      return _created.apply(this, arguments);\n    }\n\n    return created;\n  }(),\n  watch: {\n    // 监听路由 控制侧边栏显示 标记当前顶栏菜单（如需要）\n    '$route': function $route(to, from) {\n      if (to.meta.kefu) {\n        document.body.classList.add('kf_mobile');\n      } else {\n        document.body.classList.remove('kf_mobile');\n      }\n\n      if (to.name == 'setting_diy' || to.name == 'setting_special_diy') {\n        document.body.classList.add('diy-body');\n      } else {\n        document.body.classList.remove('diy-body');\n      }\n\n      var fullPath = to.fullPath.split('/').filter(function (k) {\n        return k !== '';\n      });\n      if (fullPath.length && fullPath[0] === 'kefu') return;\n      var path = to.path;\n      if (path === '/app/upload') return; // 是否使用动态侧边菜单\n\n      if (Setting.dynamicSiderMenu) {\n        var menus = this.$store.state.admin.menus.menusName; // var storage = window.localStorage;\n        // let menus = JSON.parse(storage.getItem('menuList'));\n        // this.getMenus().then(menus => {\n        // 处理手动清除db 跳转403问题\n\n        if (!menus.length) {\n          if (path != \"\".concat(Setting.roterPre, \"/login\")) {\n            this.$router.replace(\"\".concat(Setting.roterPre, \"/login\"));\n          }\n\n          return;\n        }\n\n        var menuSider = menus;\n        var headerName = getHeaderName(to, menuSider); // 在 404 时，是没有 headerName 的\n\n        if (headerName !== null) {\n          // 是否开启顶部菜单\n          if (Setting.layout.headerMenu) {\n            // 设置顶栏菜单 后台添加一个接口，设置顶部菜单\n            var headerSider = getHeaderSider(menuSider);\n            this.$store.commit('admin/menu/setHeader', headerSider); // 指定当前侧边栏隶属顶部菜单名称。如果你没有使用顶部菜单，则设置为默认的（一般为 home）名称即可\n\n            this.$store.commit('admin/menu/setHeaderName', headerName); // 获取侧边栏菜单\n\n            var filterMenuSider = getMenuSider(menuSider, headerName); // 指定当前显示的侧边菜单\n\n            this.$store.commit('admin/menu/setSider', filterMenuSider[0].children);\n          } else {\n            // 指定当前侧边栏隶属顶部菜单名称。如果你没有使用顶部菜单，则设置为默认的（一般为 home）名称即可\n            this.$store.commit('admin/menu/setHeaderName', 'home'); // 指定当前显示的侧边菜单\n\n            this.$store.commit('admin/menu/setSider', menuSider);\n          } // 指定当前菜单，即高亮项\n\n\n          this.$store.commit('admin/menu/setActivePath', path); // 找到其所有父菜单 path 进行展开\n          // const openNames = getSiderSubmenu(path, menuSider);\n\n          var openNames = getSiderSubmenu(to, menuSider);\n          this.$store.commit('admin/menu/setOpenNames', openNames);\n        } else {\n          // 子路由给默认 如果你没有使用顶部菜单，则设置为默认的（一般为 home）名称即可\n          this.$store.commit('admin/menu/setHeaderName', 'home'); // 指定当前显示的侧边菜单\n\n          this.$store.commit('admin/menu/setSider', menuSider);\n        } // });\n\n      }\n\n      this.appRouteChange(to, from);\n    }\n  }\n}).$mount('#app');", null]}