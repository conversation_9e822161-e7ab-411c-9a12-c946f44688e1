{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\tableExpand.vue?vue&type=template&id=2c2e31b8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\tableExpand.vue", "mtime": 1662341778000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div>\n        <Row class=\"expand-row\">\n            <Col span=\"8\">\n                <span class=\"expand-key\">商品分类：</span>\n                <span class=\"expand-value\">{{ row.cate_name }}</span>\n            </Col>\n            <Col span=\"8\">\n                <span class=\"expand-key\">商品市场价格：</span>\n                <span class=\"expand-value\">{{ row.ot_price }}</span>\n            </Col>\n            <Col span=\"8\">\n                <span class=\"expand-key\">成本价：</span>\n                <span class=\"expand-value\">{{ row.cost }}</span>\n            </Col>\n        </Row>\n        <Row class=\"expand-row\">\n            <Col span=\"8\">\n                <span class=\"expand-key\">收藏：</span>\n                <span class=\"expand-value\">{{ row.collect }}</span>\n            </Col>\n            <Col span=\"8\">\n                <span class=\"expand-key\">虚拟销量：</span>\n                <span class=\"expand-value\">{{ row.ficti }}</span>\n            </Col>\n\t\t\t<Col span=\"8\" v-show=\"row.is_verify === -1\">\n\t\t\t    <span class=\"expand-key\">审核未通过原因：</span>\n\t\t\t    <span class=\"expand-value\">{{ row.refusal }}</span>\n\t\t\t</Col>\n\t\t\t<Col span=\"8\" v-show=\"row.is_verify === -2\">\n\t\t\t    <span class=\"expand-key\">强制下架原因：</span>\n\t\t\t    <span class=\"expand-value\">{{ row.refusal }}</span>\n\t\t\t</Col>\n        </Row>\n    </div>\n", null]}