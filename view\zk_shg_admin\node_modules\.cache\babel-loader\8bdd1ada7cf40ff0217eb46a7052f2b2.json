{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nimport { memberAgreement as _memberAgreement, memberAgreementSave as _memberAgreementSave } from \"@/api/user\";\nimport { mapState, mapMutations } from \"vuex\";\nexport default {\n  components: {\n    WangEditor: WangEditor\n  },\n  data: function data() {\n    return {\n      id: 0,\n      agreement: {\n        title: \"\",\n        content: \"\",\n        status: 1\n      },\n      content: '',\n      spinShow: false\n    };\n  },\n  created: function created() {\n    this.memberAgreement();\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\", \"menuCollapse\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n    labelBottom: function labelBottom() {\n      return this.isMobile ? undefined : 15;\n    }\n  }),\n  methods: {\n    getEditorContent: function getEditorContent(data) {\n      this.agreement.content = data;\n    },\n    memberAgreement: function memberAgreement() {\n      var _this = this;\n\n      this.spinShow = true;\n\n      _memberAgreement().then(function (res) {\n        _this.spinShow = false;\n        var _res$data = res.data,\n            title = _res$data.title,\n            content = _res$data.content,\n            status = _res$data.status,\n            id = _res$data.id;\n        _this.agreement.title = title;\n        _this.agreement.content = content;\n        _this.content = content;\n        _this.agreement.status = status;\n        _this.id = id;\n      }).catch(function (err) {\n        _this.$Message.error(err);\n\n        _this.spinShow = false;\n      });\n    },\n    // 保存\n    memberAgreementSave: function memberAgreementSave() {\n      var _this2 = this;\n\n      this.$Spin.show();\n\n      _memberAgreementSave(this.id, this.agreement).then(function (res) {\n        _this2.$Spin.hide();\n\n        _this2.$Message.success(\"保存成功\");\n\n        _this2.memberAgreement();\n      }).catch(function (err) {\n        _this2.$Spin.hide();\n\n        _this2.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}