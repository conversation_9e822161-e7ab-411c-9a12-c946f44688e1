{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue?vue&type=template&id=621824e8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 财务-发票列表 -->\n    <div>\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding= \"0\">\n            <div class=\"new_card_pd\">\n                <!-- 查询条件 -->\n                <Form ref=\"orderData\" :model=\"orderData\" \n                :label-width=\"labelWidth\" \n                inline \n                :label-position=\"labelPosition\"\n                class=\"tabform\" \n                @submit.native.prevent>\n                    <FormItem label=\"创建时间：\">\n                        <DatePicker :editable=\"false\" \n                        @on-change=\"onchangeTime\" \n                        :value=\"timeVal\" \n                        format=\"yyyy/MM/dd HH:mm\"\n                        type=\"datetimerange\" \n                        placement=\"bottom-start\" \n                        placeholder=\"自定义时间\"\n                        class=\"input-add\"\n                        :options=\"options\"></DatePicker>\n                    </FormItem>\n                    <FormItem label=\"搜索：\" prop=\"real_name\" label-for=\"real_name\">\n                        <Input v-model=\"orderData.real_name\" placeholder=\"请输入\" \n                        class=\"input-add\"\n                        element-id=\"name\"  >\n                        <Select v-model=\"orderData.field_key\" slot=\"prepend\" style=\"width: 80px\">\n                            <Option value=\"all\">全部</Option>\n                            <Option value=\"order_id\">订单号</Option>\n                            <Option value=\"uid\">UID</Option>\n                            <Option value=\"real_name\">用户姓名</Option>\n                            <Option value=\"user_phone\">用户电话</Option>\n                        </Select>\n                        </Input>\n                    </FormItem>\n                    <FormItem>\n                        <Button type=\"primary\" @click=\"orderSearch()\" class=\"btn-add\">查询</Button>\n                        <Button @click=\"exports\">导出</Button>\n                    </FormItem>\n                </Form>        \n            </div>\n        </Card>\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n            <!-- 表格 -->\n            <Table :columns=\"columns\" :data=\"orderList\" ref=\"table\" class=\"mt25\"\n                   :loading=\"loading\" highlight-row\n                   no-userFrom-text=\"暂无数据\"\n                   no-filtered-userFrom-text=\"暂无筛选结果\">\n                <template slot-scope=\"{ row, index }\" slot=\"pay_price\">\n                    <div>¥ {{row.pay_price}}</div>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"type\">\n                    <div v-if=\"row.type===1\">电子普通发票</div>\n                    <div v-else>纸质专用发票</div>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"is_invoice\">\n                    <Tag color=\"orange\" size=\"medium\" v-if=\"row.is_invoice===1\">已开票</Tag>\n                    <Tag color=\"green\" size=\"medium\" v-else>未开票</Tag>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"status\">\n                    <template v-if=\"row.refund_status\">\n                        <div v-if=\"row.refund_status===1\">退款中</div>\n                        <div v-else-if=\"row.refund_status===2\">已退款</div>\n                    </template>\n                    <template v-else>\n                        <Tag color=\"green\" size=\"medium\" v-if=\"row.status===0\">未发货</Tag>\n                        <Tag color=\"blue\" size=\"medium\" v-else-if=\"row.status===1\">待收货</Tag>\n                        <Tag color=\"orange\" size=\"medium\" v-else-if=\"row.status===2\">待评价</Tag>\n                        <Tag color=\"volcano\" size=\"medium\" v-else-if=\"row.status===3\">已完成</Tag>\n                    </template>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"header_type\">\n                    <div v-if=\"row.header_type===1\">个人</div>\n                    <div v-else>企业</div>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"action\">\n                    <a :disabled=\"row.refund_status !== 0\" @click=\"edit(row)\">编辑</a>\n                    <Divider type=\"vertical\"/>\n                    <a @click=\"orderInfo(row.id)\">订单信息</a>\n                </template>\n            </Table>\n            <div class=\"acea-row row-right page\">\n                <Page :total=\"total\" :current=\"orderData.page\" show-elevator show-total @on-change=\"pageChange\"\n                      :page-size=\"orderData.limit\"/>\n            </div>\n        </Card>\n        <Modal v-model=\"invoiceShow\" scrollable title=\"发票详情\" class=\"order_box\" width=\"700\" @on-cancel=\"cancel\" footer-hide>\n            <Form ref=\"formInline\" :model=\"formInline\" :label-width=\"100\" @submit.native.prevent>\n                <div v-if=\"invoiceDetails.header_type===1 && invoiceDetails.type===1\">\n                    <div class=\"list\">\n                        <div class=\"title\">发票信息</div>\n                        <Row class=\"row\">\n                            <Col span=\"12\">发票抬头: <span class=\"info\">{{invoiceDetails.name}}</span></Col>\n                            <Col span=\"12\">发票类型: <span class=\"info\">电子普通发票</span></Col>\n                        </Row>\n                        <Row class=\"row\">\n                            <Col span=\"12\">发票抬头类型: 个人</Col>\n                            <Col span=\"12\" v-if=\"invoiceDetails.is_invoice === 1\">发票金额: ￥{{ invoiceDetails.pay_price }}</Col>\n                        </Row>\n                    </div>\n                    <div class=\"list\">\n                        <div class=\"title row\">联系信息</div>\n                        <Row  class=\"row\">\n                            <Col span=\"12\">真实姓名: {{invoiceDetails.name}}</Col>\n                            <Col span=\"12\">联系电话: {{invoiceDetails.drawer_phone}}</Col>\n                        </Row>\n                        <Row  class=\"row\">\n                            <Col span=\"12\">联系邮箱: {{invoiceDetails.email}}</Col>\n                        </Row>\n                    </div>\n                </div>\n                <div v-if=\"invoiceDetails.header_type===2 && invoiceDetails.type===1\">\n                    <div class=\"list\">\n                        <div class=\"title\">发票信息</div>\n                        <Row class=\"row\">\n                            <Col span=\"12\">发票抬头: <span class=\"info\">{{invoiceDetails.name}}</span></Col>\n                            <Col span=\"12\">企业税号: <span class=\"info\">{{invoiceDetails.duty_number}}</span></Col>\n                        </Row>\n                        <Row class=\"row\">\n                            <Col span=\"12\">发票类型: 电子普通发票</Col>\n                            <Col span=\"12\">发票抬头类型: 企业</Col>\n                        </Row>\n                    </div>\n                    <div class=\"list\">\n                        <div class=\"title row\">联系信息</div>\n                        <Row  class=\"row\">\n                            <Col span=\"12\">真实姓名: {{invoiceDetails.name}}</Col>\n                            <Col span=\"12\">联系电话: {{invoiceDetails.drawer_phone}}</Col>\n                        </Row>\n                        <Row  class=\"row\">\n                            <Col span=\"12\">联系邮箱: {{invoiceDetails.email}}</Col>\n                        </Row>\n                    </div>\n                </div>\n                <div v-if=\"invoiceDetails.header_type===2 && invoiceDetails.type===2\">\n                    <div class=\"list\">\n                        <div class=\"title\">发票信息</div>\n                        <Row class=\"row\">\n                            <Col span=\"12\">发票抬头: <span class=\"info\">{{invoiceDetails.name}}</span></Col>\n                            <Col span=\"12\">企业税号: <span class=\"info\">{{invoiceDetails.duty_number}}</span></Col>\n                        </Row>\n                        <Row class=\"row\">\n                            <Col span=\"12\">发票类型: 纸质专用发票</Col>\n                            <Col span=\"12\">发票抬头类型: 企业</Col>\n                        </Row>\n                        <Row class=\"row\">\n                            <Col span=\"12\">开户银行: <span class=\"info\">{{invoiceDetails.bank}}</span></Col>\n                            <Col span=\"12\">银行账号: <span class=\"info\">{{invoiceDetails.card_number}}</span></Col>\n                        </Row>\n                        <Row class=\"row\">\n                            <Col span=\"12\">企业地址: {{invoiceDetails.address}}</Col>\n                            <Col span=\"12\">企业电话: {{invoiceDetails.tell}}</Col>\n                        </Row>\n                    </div>\n                    <div class=\"list\">\n                        <div class=\"title row\">联系信息</div>\n                        <Row  class=\"row\">\n                            <Col span=\"12\">真实姓名: {{invoiceDetails.name}}</Col>\n                            <Col span=\"12\">联系电话: {{invoiceDetails.drawer_phone}}</Col>\n                        </Row>\n                        <Row  class=\"row\">\n                            <Col span=\"12\">联系邮箱: {{invoiceDetails.email}}</Col>\n                        </Row>\n                    </div>\n                </div>\n                <FormItem label=\"开票状态：\" style=\"margin-top: 14px;\">\n                    <RadioGroup v-model=\"formInline.is_invoice\" @on-change=\"kaiInvoice(formInline.is_invoice)\">\n                        <Radio :label=\"1\">已开票</Radio>\n                        <Radio :label=\"0\">未开票</Radio>\n                        <Radio :label=\"-1\">已拒绝</Radio>\n                    </RadioGroup>\n                </FormItem>\n                <FormItem label=\"发票编号：\" v-if=\"formInline.is_invoice===1\">\n                    <Input  v-model=\"formInline.invoice_number\" placeholder=\"请输入发票编号\"></Input>\n                </FormItem>\n                <!-- @keyup.native=\"keyUp($event,'pay_price',true)\" -->\n                <FormItem label=\"发票金额：\" v-if=\"formInline.is_invoice===1\">\n                    <Input @input=\"inputEnter\"\n                    @blur=\"inputBlur\"  v-model=\"formInline.invoice_amount\" placeholder=\"请输入发票金额\"></Input>\n                    <!-- <InputNumber @on-change=\"keyUp($event,'pay_price',true)\" v-model=\"formInline.pay_price\" placeholder=\"请输入发票金额\"></InputNumber> -->\n                </FormItem>\n                <FormItem label=\"发票备注：\" v-if=\"formInline.is_invoice\">\n                    <Input v-model=\"formInline.remark\" value=\"备注\" type=\"textarea\" :autosize=\"{minRows: 2,maxRows: 5}\" placeholder=\"请输入发票备注\"></Input>\n                </FormItem>\n                <Button type=\"primary\" @click=\"handleSubmit()\" class=\"ok-btn\">确定</Button>\n            </Form>\n        </Modal>\n        <Modal v-model=\"orderShow\" scrollable title=\"订单详情\" footer-hide class=\"order_box\" width=\"700\">\n            <orderDetall :orderId=\"orderId\" @detall=\"detall\" v-if=\"orderShow\"></orderDetall>\n        </Modal>\n    </div>\n", null]}