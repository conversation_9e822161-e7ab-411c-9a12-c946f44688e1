{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue?vue&type=style&index=0&id=6b24a4ca&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/deep/.ivu-input-icon {\n  color: #BBBBBB;\n}\n\n/deep/.ivu-input-word-count {\n  color: #BBBBBB;\n}\n\n.hot_imgs {\n  margin: 0 15px 20px 15px;\n\n  .title {\n    padding-bottom: 21px;\n    color: #999;\n    font-size: 12px;\n  }\n\n  .list-box {\n    .item {\n      position: relative;\n      display: flex;\n      background: #F9F9F9;\n      align-items: center;\n      padding: 16px 20px 16px 0;\n      margin-bottom: 16px;\n      border-radius: 3px;\n\n      .delect-btn {\n        position: absolute;\n        right: -13px;\n        top: -16px;\n\n        .iconfont-diy {\n          font-size: 25px;\n          color: #ccc;\n        }\n      }\n\n      .move-icon {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 30px;\n        cursor: move;\n      }\n\n      .img-box {\n        position: relative;\n        width: 64px;\n        height: 64px;\n\n        img {\n          width: 100%;\n          height: 100%;\n          border-radius: 3px;\n        }\n      }\n\n      .info {\n        flex: 1;\n        margin-left: 22px;\n\n        .info-item {\n          display: flex;\n          align-items: center;\n          margin-bottom: 10px;\n\n          &:nth-last-child(1) {\n            margin-bottom: 0;\n          }\n\n          .span {\n            width: 40px;\n            font-size: 12px;\n            color: #999;\n          }\n\n          .input-box {\n            flex: 1;\n          }\n        }\n      }\n    }\n  }\n\n  .add-btn {\n    margin-top: 10px;\n\n    .btn {\n      width: 100%;\n      height: 36px;\n      border-color: #EEEEEE;\n      color: #666666;\n\n      .iconfont {\n        font-size: 11px;\n        margin-right: 5px;\n      }\n    }\n  }\n}\n\n.upload-box {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background: #fff;\n  border-radius: 4px;\n  border: 1px solid #EEEEEE;\n  color: #ccc\n}\n\n.iconfont-diy {\n  color: #DDDDDD;\n  font-size: 16px;\n}\n", null]}