-- 插入测试调拨数据
-- 首先确保有门店数据
INSERT IGNORE INTO `zk_system_store` (`id`, `name`, `phone`, `address`, `detailed_address`, `is_show`, `is_del`, `add_time`) VALUES
(1, '总店', '13800138001', '北京市朝阳区', '朝阳区建国路1号', 1, 0, UNIX_TIMESTAMP()),
(2, '分店A', '13800138002', '北京市海淀区', '海淀区中关村大街1号', 1, 0, UNIX_TIMESTAMP()),
(3, '分店B', '13800138003', '北京市西城区', '西城区西单大街1号', 1, 0, UNIX_TIMESTAMP());

-- 插入测试调拨单数据
INSERT INTO `zk_transfer_order` (`id`, `order_no`, `from_store_id`, `to_store_id`, `applicant_id`, `status`, `total_amount`, `remark`, `apply_time`, `approve_time`, `approver_id`, `approve_remark`, `execute_time`, `create_time`, `update_time`) VALUES
(1, 'TF20241216001', 1, 2, 1, 0, 100.00, '测试调拨单1', UNIX_TIMESTAMP(), 0, 0, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'TF20241216002', 2, 3, 1, 1, 200.00, '测试调拨单2', UNIX_TIMESTAMP() - 3600, UNIX_TIMESTAMP() - 1800, 1, '审核通过', 0, UNIX_TIMESTAMP() - 3600, UNIX_TIMESTAMP() - 1800),
(3, 'TF20241216003', 1, 3, 1, 2, 150.00, '测试调拨单3', UNIX_TIMESTAMP() - 7200, UNIX_TIMESTAMP() - 5400, 1, '审核通过', UNIX_TIMESTAMP() - 3600, UNIX_TIMESTAMP() - 7200, UNIX_TIMESTAMP() - 3600),
(4, 'TF20241216004', 3, 1, 1, 3, 80.00, '测试调拨单4', UNIX_TIMESTAMP() - 10800, UNIX_TIMESTAMP() - 9000, 1, '库存不足，拒绝调拨', 0, UNIX_TIMESTAMP() - 10800, UNIX_TIMESTAMP() - 9000);

-- 插入测试调拨单明细数据
INSERT INTO `zk_transfer_order_detail` (`id`, `transfer_id`, `product_id`, `attr_value_id`, `transfer_qty`, `cost_price`, `amount`, `create_time`) VALUES
(1, 1, 1, 1, 10, 10.00, 100.00, UNIX_TIMESTAMP()),
(2, 2, 2, 2, 20, 10.00, 200.00, UNIX_TIMESTAMP() - 3600),
(3, 3, 3, 3, 15, 10.00, 150.00, UNIX_TIMESTAMP() - 7200),
(4, 4, 4, 4, 8, 10.00, 80.00, UNIX_TIMESTAMP() - 10800);
