{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue?vue&type=template&id=20b14438&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n  <div class=\"button-style acea-row row-middle\">\n    <div class=\"title-tips\" v-if=\"configData\">\n      <span>{{ configData.title }}</span>\n    </div>\n    <div class=\"style-box acea-row row-middle\">\n      <div class=\"bnt\" @click=\"styleTap\">修改风格</div>\n      <div class=\"name\">当前：样式{{ configData.tabVal + 1 }}</div>\n    </div>\n  </div>\n  <Modal\n    v-model=\"modals\"\n    title=\"风格选择器\"\n    scrollable\n    height=\"500\"\n    :width=\"\n      configData.type == 'signIn' || configData.type == 'ranking' ? 616 : 900\n    \"\n    @on-cancel=\"cancel\"\n    @on-ok=\"ok\"\n  >\n    <div class=\"list acea-row row-middle\">\n      <div\n        class=\"item\"\n        :class=\"current == index ? 'on' : ''\"\n        v-for=\"(item, index) in list\"\n        :key=\"index\"\n        @click=\"tap(index)\"\n      >\n        <div class=\"pictrue acea-row row-center-wrapper\">\n          <img\n            :src=\"item.url\"\n            :style=\"{\n              width: item.width + 'px',\n              height: item.height + 'px',\n            }\"\n          />\n          <span\n            class=\"iconfont icona-zu80222\"\n            v-if=\"current == index\"\n          ></span>\n        </div>\n        <div class=\"name\">风格{{ index + 1 }}</div>\n      </div>\n    </div>\n  </Modal>\n</div>\n", null]}