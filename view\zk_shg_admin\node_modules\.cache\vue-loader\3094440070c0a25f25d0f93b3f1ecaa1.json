{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue?vue&type=style&index=0&id=19f859da&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.upload_img{\n\tpadding: 0 15px;\n\t&.on{\n\t\t.title{\n\t\t\tmargin-bottom: 0;\n\t\t\tpadding-bottom 0;\n\t\t}\n\t\t.list{\n\t\t\tpadding: 0;\n\t\t\theight: auto;\n\t\t\tbackground: #fff;\n\t\t\tpadding-bottom:20px;\n\t\t\t.box{\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t\t.name{\n\t\t\t\twidth: 75px;\n\t\t\t}\n\t\t}\n\t}\n\t.list{\n\t\twidth: 370px;\n\t\theight: 137px;\n\t\tbackground: #F9F9F9;\n\t\tborder-radius: 3px;\n\t\tpadding: 16px 20px;\n\t}\n\t.item{\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\t.name{\n\t\t\tfont-size: 12px;\n\t\t\tcolor: #999999;\n\t\t\tmargin-right: 16px;\n\t\t}\n\t\t/deep/.ivu-input-icon{\n\t\t\tcolor: #BBBBBB;\n\t\t}\n\t\t.picTxt{\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\t.tip{\n\t\t\t\tcolor: #BBBBBB;\n\t\t\t\tfont-size: 12px;\n\t\t\t\tmargin-left: 20px;\n\t\t\t}\n\t\t}\n\t}\n}\n.header{\n    font-size 14px\n    color #000\n}\n.title{\n    margin-bottom: 10px;\n    padding-bottom 10px;\n    font-size:12px;\n    color:#999;\n}\n.box{\n\twidth 64px\n\theight 64px\n\tmargin-bottom 10px\n\tposition relative\n\tbackground url(../../assets/images/transparents.jpg) no-repeat\n\tbackground-size 100% 100%;\n\tborder-radius: 3px;\n\t.pictrue{\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tvideo{\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t}\n\t\t.iconfont{\n\t\t\tposition: absolute;\n\t\t\tright:-12px;\n\t\t\ttop:-19px;\n\t\t\tfont-size: 24px;\n\t\t\tcolor: #CCCCCC;\n\t\t}\n\t}\n\timg{\n\t\twidth 64px\n\t\tborder-radius: 3px;\n\t\tmax-height: 64px;\n\t\tobject-fit: cover;\n\t}\n}\n\n.upload-box{\n\tdisplay flex\n\talign-items center\n\tjustify-content center\n\twidth 64px\n\theight 64px\n\tbackground #fff\n\tborder-radius: 4px;\n\tborder: 1px solid #EEEEEE;\n\t.ivu-icon{\n\t\tcolor: #ccc\n\t}\n}\n", null]}