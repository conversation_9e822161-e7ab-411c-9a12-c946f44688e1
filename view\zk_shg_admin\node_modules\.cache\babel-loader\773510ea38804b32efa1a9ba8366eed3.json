{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: \"c_pictrue\",\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    }\n  },\n  data: function data() {\n    return {\n      defaults: {},\n      configData: {},\n      style: 0,\n      isUpdate: false,\n      // 重新渲染\n      currentIndex: 0,\n      arrayObj: {\n        image: \"\",\n        link: \"\"\n      },\n      list: undefined,\n      select: false,\n      lis: undefined,\n      rect: null,\n      // 定义移动元素div\n      // 记录鼠标按下时的坐标\n      downX: 0,\n      downY: 0,\n      // 记录鼠标抬起时候的坐标\n      mouseX2: 0,\n      mouseY2: 0,\n      imgNum: 0,\n      selPicBox: 0 // 当前选中的图片盒子\n\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.defaults = _this.configObj;\n\n      if (_this.configObj.hasOwnProperty(\"timestamp\")) {\n        _this.isUpdate = true;\n      } else {\n        _this.isUpdate = false;\n      }\n\n      _this.$set(_this, \"configData\", _this.configObj[_this.configNme]);\n\n      _this.style = _this.configObj.styleConfig.tabVal;\n      _this.count = _this.defaults.styleConfig.count;\n\n      _this.picArrayConcat(_this.count);\n\n      if (_this.style == 11) {\n        _this.lis = document.getElementsByClassName(\"lay-item\");\n      }\n\n      _this.currentTab(0, _this.configData);\n    });\n  },\n  computed: {\n    selBoxList: function selBoxList() {\n      return this.configObj.picStyle.docPicList;\n    }\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal) {\n        this.defaults = nVal;\n        this.$set(this, \"configData\", nVal[this.configNme]);\n        this.style = nVal.styleConfig.tabVal;\n        this.isUpdate = true;\n        this.$set(this, \"isUpdate\", true);\n      },\n      deep: true\n    },\n    \"configObj.styleConfig.tabVal\": {\n      handler: function handler() {\n        this.count = this.defaults.styleConfig.count;\n        this.picArrayConcat(this.count);\n        this.configData.picList.splice(this.count);\n        this.currentIndex = 0;\n        var list = this.defaults.menuConfig.list[0];\n\n        if (this.configData.picList[0]) {\n          list.img = this.configData.picList[0].image;\n          list.info[0].value = this.configData.picList[0].link;\n        }\n\n        this.lis = document.getElementsByClassName(\"lay-item\");\n      },\n      deep: true,\n      immediate: true\n    },\n    \"configObj.picStyle.docPicList\": {\n      handler: function handler() {\n        var _this2 = this;\n\n        if (this.configObj.styleConfig.tabVal == 11) {\n          this.configObj.picStyle.docPicList.map(function (e, i) {\n            _this2.configObj.picStyle.docPicList[i].img = _this2.configObj.picStyle.picList[i].image;\n            _this2.configObj.picStyle.docPicList[i].link = _this2.configObj.picStyle.picList[i].link;\n          });\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  methods: {\n    currentTab: function currentTab(e, data) {\n      this.selPicBox = e;\n      this.currentIndex = e;\n      this.configData.tabVal = e;\n\n      if (this.defaults.menuConfig.isCube) {\n        if (this.configData.tabVal !== 11) {\n          var list = this.defaults.menuConfig.list[0];\n\n          if (data.picList[e] && data.picList[e].image) {\n            list.img = data.picList[e].image;\n            list.info[0].value = data.picList[e].link;\n          } else {\n            list.img = \"\";\n            list.info[0].value = \"\";\n          }\n        } else {\n          this.selPicBox = e;\n          var _list = this.defaults.docPicList;\n\n          if (data.menuConfig.picStyle.picList[e].image) {\n            _list[e].img = data.menuConfig.picStyle.picList[e].image;\n            _list[e].info[0].value = data.menuConfig.picStyle.docPicList[e].value;\n          } else {\n            _list[0].img = \"\";\n            _list[0].info[0].value = \"\";\n          }\n        }\n      }\n    },\n    picArrayConcat: function picArrayConcat(count) {\n      for (var i = this.configData.picList.length; i < count; i++) {\n        this.configData.picList.push(this.arrayObj);\n      }\n    },\n    // 删除指定热区\n    delAreaBox: function delAreaBox(index) {\n      /* 删除某个热区 */\n      this.selBoxList.splice(index, 1);\n      this.configObj.picStyle.picList.splice(index, 1);\n      this.configObj.picStyle.picList.push({\n        image: \"\",\n        link: \"\"\n      });\n      if (this.selBoxList.length) this.currentTab(this.selBoxList.length - 1, this.configData); //   /* 删除后 重新编号 */\n      //   if (this.selBoxList) {\n      //     const arr = this.selBoxList.filter((i) => i.number > index);\n      //     if (!arr) return;\n      //     arr.forEach((i) => i.number--);\n      //     if (this.selBoxList[this.selBoxList.length - 1]) {\n      //       this.imgNum = this.selBoxList[this.selBoxList.length - 1].number + 1;\n      //     } else {\n      //       this.imgNum = 1;\n      //     }\n      //   }\n    },\n    initRect: function initRect() {\n      if (this.rect) {\n        document.getElementById(\"lay1\").removeChild(this.rect);\n      }\n    },\n    //处理鼠标按下事件\n    clickBox: function clickBox(event) {\n      if (this.select) {\n        var boxData = this.up();\n\n        try {\n          if (this.selBoxList.length && this.selBoxList.length == 1 && this.selBoxList[0].doc.w === 0) {\n            this.selBoxList[0].doc = boxData;\n          } else {\n            this.selBoxList.push({\n              img: \"\",\n              link: \"\",\n              doc: boxData\n            });\n          }\n\n          this.currentTab(this.selBoxList.length - 1, this.configData);\n        } catch (error) {\n          console.log(error);\n        }\n\n        this.selPicBox = this.selBoxList.length ? this.selBoxList.length - 1 : 0;\n        return;\n      } // 鼠标按下时才允许处理鼠标的移动事件\n\n\n      this.select = true;\n      this.rect = document.createElement(\"div\"); // 框选div 样式\n\n      this.rect.style.cssText = \"position:absolute;width:0px;height:0px;font-size:0px;margin:0px;padding:0px;border:1px dashed #0099FF;background-color:#C3D5ED;z-index:1000;filter:alpha(opacity:60);opacity:0.6;display:none;\";\n      this.rect.id = \"selectDiv\"; // 添加到lay1下\n\n      document.getElementById(\"lay1\").appendChild(this.rect); // 取得鼠标按下时的坐标位置\n\n      this.downX = event.layerX;\n      this.downY = event.layerY;\n      this.rect.style.left = this.downX + \"px\";\n      this.rect.style.top = this.downY + \"px\"; //设置你要画的矩形框的起点位置\n\n      this.rect.style.left = this.downX + \"px\";\n      this.rect.style.top = this.downY + \"px\";\n    },\n    //鼠标抬起事件\n    up: function up() {\n      var topList = [];\n      var leftList = [];\n\n      for (var i = 0; i < this.lis.length; i++) {\n        //将移动的div的四个点和和div元素的四个点进行比较\n        if ( //判断div元素 右边框的位置大于移动div的左起始点\n        this.rect.offsetLeft < this.lis[i].offsetLeft + this.lis[i].offsetWidth && //判断div元素 下边框的位置大于移动div的上起始点\n        this.lis[i].offsetTop + this.lis[i].offsetHeight > this.rect.offsetTop && // 判断div元素左边框的位置小于移动div的右起始点\n        this.rect.offsetLeft + this.rect.offsetWidth > this.lis[i].offsetLeft && // 判断div元素上边框的位置小于移动div的下起始点\n        this.rect.offsetTop + this.rect.offsetHeight > this.lis[i].offsetTop) {\n          //将已选中的样式改变\n          if (this.lis[i].className.indexOf(\"seled\") == -1) {\n            topList.push(this.lis[i].offsetTop);\n            leftList.push(this.lis[i].offsetLeft);\n          }\n        } else {\n          //如果没有选中则清除样式\n          if (this.lis[i].className.indexOf(\"seled\") != -1) {\n            this.lis[i].className = \"lay-item\";\n          }\n        } //鼠标抬起,就不允许在处理鼠标移动事件\n\n\n        this.select = false;\n      } //隐藏图层\n\n\n      if (this.rect) {\n        document.getElementById(\"lay1\").removeChild(this.rect);\n      }\n\n      return {\n        startX: this.getMin(leftList),\n        startY: this.getMin(topList),\n        w: this.getMax(leftList) - this.getMin(leftList) + 93.75,\n        h: this.getMax(topList) - this.getMin(topList) + 93.75\n      };\n    },\n    // 删除\n    del: function del() {\n      this.$emit(\"delAreaBox\", this.areaDataIndex);\n    },\n    getMin: function getMin(arr) {\n      var min = arr[0];\n\n      for (var i = 1; i < arr.length; i++) {\n        if (arr[i] < min) {\n          min = arr[i];\n        }\n      }\n\n      return min;\n    },\n    getMax: function getMax(arr) {\n      var max = arr[0];\n\n      for (var i = 1; i < arr.length; i++) {\n        if (arr[i] > max) {\n          max = arr[i];\n        }\n      }\n\n      return max;\n    },\n    out: function out() {\n      if (this.rect) {\n        this.select = false;\n        document.getElementById(\"lay1\").removeChild(this.rect);\n      }\n    },\n    //鼠标移动事件,最主要的事件\n    move: function move(event) {\n      var _this3 = this;\n\n      event.preventDefault();\n      if (!this.select) return;\n      /*\n            这个部分,根据你鼠标按下的位置,和你拉框时鼠标松开的位置关系,可以把区域分为四个部分,根据四个部分的不同,\n            我们可以分别来画框,否则的话,就只能向一个方向画框,也就是点的右下方画框.\n           \n            */\n\n      if (this.select) {\n        console.log(event.layerX, event.layerY, event);\n        window.requestAnimationFrame(function () {\n          // 取得鼠标移动时的坐标位置\n          _this3.mouseX2 = event.layerX - 5;\n          _this3.mouseY2 = event.layerY - 5; // 显示框选元素\n\n          if (_this3.rect.style.display == \"none\") {\n            _this3.rect.style.display = \"\";\n          }\n\n          _this3.rect.style.left = Math.min(_this3.mouseX2, _this3.downX) + \"px\";\n          _this3.rect.style.top = Math.min(_this3.mouseY2, _this3.downY) + \"px\";\n          _this3.rect.style.width = _this3.mouseX2 - _this3.downX + \"px\";\n          _this3.rect.style.height = _this3.mouseY2 - _this3.downY + \"px\"; // // A part\n          // if (this.mouseX2 < this.downX && this.mouseY2 < this.downY) {\n          //   this.rect.style.left = this.mouseX2;\n          //   this.rect.style.top = this.mouseY2;\n          // }\n          // // B part\n          // if (this.mouseX2 > this.downX && this.mouseY2 < this.downY) {\n          //   this.rect.style.left = this.downX;\n          //   this.rect.style.top = this.mouseY2;\n          // }\n          // // C part\n          // if (this.mouseX2 < this.downX && this.mouseY2 > this.downY) {\n          //   this.rect.style.left = this.mouseX2;\n          //   this.rect.style.top = this.downY;\n          // }\n          // // D part\n          // if (this.mouseX2 > this.downX && this.mouseY2 > this.downY) {\n          //   this.rect.style.left = this.downX;\n          //   this.rect.style.top = this.downY;\n          // }\n          //   this.rect.style.left = this.downX;\n          //   this.rect.style.top = this.downY;\n        });\n      } // 阻止事件上传\n\n\n      window.event.cancelBubble = true; // 阻止默认事件\n\n      window.event.returnValue = false;\n    }\n  }\n};", null]}