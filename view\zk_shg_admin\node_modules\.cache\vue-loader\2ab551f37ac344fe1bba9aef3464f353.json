{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue?vue&type=template&id=4f15628a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue", "mtime": 1718268362000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <Drawer :value=\"visible\" :closable=\"true\" :styles=\"{ padding: '0 0 60px' }\" width=\"1000\"\n          @on-visible-change=\"drawerChange\">\n    <div class=\"header\">\n      <Icon custom=\"iconfont iconmanjianmanzhe\" size=\"60\" />\n      <div>\n        <div class=\"title\">{{ formValidate.store_name }}</div>\n        <div>商品ID：{{ productId }}</div>\n      </div>\n    </div>\n    <Tabs v-model=\"currentTab\">\n      <TabPane v-for=\"(item, index) in headTab\" v-if=\"index !=6 || (index==6 && merchantType==0)\" :key=\"item.name\" :label=\"item.title\" :name=\"item.name\">\n      </TabPane>\n    </Tabs>\n    <Form class=\"formValidate\" ref=\"formValidate\" :model=\"formValidate\" :label-width=\"labelWidth\"\n          :label-position=\"labelPosition\" @submit.native.prevent>\n      <Row type=\"flex\" v-show=\"currentTab === '1'\">\n        <!-- 商品信息-->\n        <Col span=\"24\">\n          <FormItem label=\"商品类型：\" props=\"is_virtual\">\n            <RadioGroup v-model=\"formValidate.product_type\">\n              <Radio v-for=\"item in productType\" :key=\"item.id\" :label=\"item.id\" disabled>{{ item.name }}</Radio>\n            </RadioGroup>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品分类：\" prop=\"cate_id\">\n            <el-cascader placeholder=\"请选择商品分类\" v-width=\"'50%'\" size=\"mini\" v-model=\"formValidate.cate_id\"\n                         :options=\"treeSelect\" :props=\"props\" filterable clearable>\n            </el-cascader>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品名称：\" prop=\"store_name\">\n            <Input v-model=\"formValidate.store_name\" placeholder=\"请输入商品名称\" v-width=\"'50%'\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\" class=\"brandName\">\n          <FormItem label=\"商品品牌：\" prop=\"\">\n            <Cascader :data=\"brandData\" placeholder=\"请选择商品品牌\" change-on-select v-model=\"formValidate.brand_id\" filterable\n                      v-width=\"'50%'\"></Cascader>\n            <!-- <span class=\"addClass\" @click=\"addBrand\">新增品牌</span> -->\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"单位：\" prop=\"unit_name\">\n            <Select v-model=\"formValidate.unit_name\" clearable filterable v-width=\"'50%'\" placeholder=\"请输入单位\">\n              <Option v-for=\"(item, index) in unitNameList\" :value=\"item.name\" :key=\"item.id\">{{ item.name }}</Option>\n            </Select>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品编码：\" prop=\"\">\n            <Input v-model=\"formValidate.code\" placeholder=\"请输入商品编码\" v-width=\"'50%'\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品轮播图：\" prop=\"slider_image\">\n            <div class=\"acea-row\">\n              <div class=\"pictrue\" v-for=\"(item, index) in formValidate.slider_image\" :key=\"index+'img'\" draggable=\"true\"\n                   @dragstart=\"handleDragStart($event, item)\" @dragover.prevent=\"handleDragOver($event, item)\"\n                   @dragenter=\"handleDragEnter($event, item)\" @dragend=\"handleDragEnd($event, item)\">\n                <img v-lazy=\"item\" />\n                <Button shape=\"circle\" icon=\"md-close\" @click.native=\"handleRemove(index)\" class=\"btndel\"></Button>\n              </div>\n              <div v-if=\"formValidate.slider_image.length < 10\" class=\"upLoad acea-row row-center-wrapper\"\n                   @click=\"modalPicTap('duo')\">\n                <Icon type=\"ios-camera-outline\" size=\"26\" />\n              </div>\n              <Input v-model=\"formValidate.slider_image[0]\" class=\"input-display\"></Input>\n            </div>\n            <div class=\"tips\">\n              建议尺寸：800\n              *800px，可拖拽改变图片顺序，默认首张图为主图，最多上传10张\n            </div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品标签：\" prop=\"store_label_id\" class=\"labelClass\">\n            <div class=\"acea-row row-middle\">\n              <div class=\"labelInput acea-row row-between-wrapper\" @click=\"openStoreLabel\">\n                <div v-if=\"storeDataLabel.length\">\n                  <Tag closable v-for=\"(item, index) in storeDataLabel\" :key=\"item.id\" @on-close=\"closeStoreLabel(item)\">{{\n                      item.label_name\n                    }}</Tag>\n                </div>\n                <span class=\"span\" v-else>选择商品标签</span>\n              </div>\n            </div>\n          </FormItem>\n        </Col>\n        <!-- <Col span=\"24\">\n          <FormItem label=\"供应商：\" prop=\"supplier_id\" v-if=\"formValidate.product_type == 0 && merchantType!=1\">\n            <Select v-model=\"formValidate.supplier_id\" clearable filterable v-width=\"'50%'\" placeholder=\"请选择供应商\">\n              <Option v-for=\"(item, index) in supplierList\" :value=\"item.id\" :key=\"item.id\">{{ item.supplier_name }}\n              <Option v-for=\"(item, index) in supplierList\" :value=\"item.id\" :key=\"item.id\">{{ item.supplier_name }}\n              </Option>\n            </Select>\n          </FormItem>\n        </Col> -->\n        <Col span=\"24\">\n          <FormItem label=\"添加视频：\">\n            <i-switch v-model=\"formValidate.video_open\" size=\"large\">\n              <span slot=\"open\">开启</span>\n              <span slot=\"close\">关闭</span>\n            </i-switch>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.video_open\">\n          <FormItem label=\"视频类型：\">\n            <RadioGroup v-model=\"seletVideo\" @on-change=\"changeVideo\">\n              <Radio :label=\"0\" class=\"radio\">本地视频</Radio>\n              <Radio :label=\"1\">视频链接</Radio>\n            </RadioGroup>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.video_open\">\n          <FormItem label=\"\" prop=\"video_link\">\n            <Input v-if=\"seletVideo == 1 && !formValidate.video_link\" v-width=\"'50%'\" v-model=\"videoLink\"\n                   placeholder=\"请输入视频链接\" />\n            <input type=\"file\" ref=\"refid\" @change=\"zh_uploadFile_change\" class=\"input-display\" />\n            <div v-if=\"\n            seletVideo == 0 &&\n            (upload_type !== '1' || videoLink) &&\n            !formValidate.video_link\n          \" class=\"ml10 videbox\" @click=\"zh_uploadFile\">\n              +\n            </div>\n            <Button v-if=\"\n            seletVideo == 1 &&\n            (upload_type !== '1' || videoLink) &&\n            !formValidate.video_link\n          \" type=\"primary\" icon=\"ios-cloud-upload-outline\" class=\"uploadVideo\" @click=\"zh_uploadFile\">确认添加</Button>\n            <Upload v-if=\"upload_type === '1' && !formValidate.video_link\" :show-upload-list=\"false\" :action=\"fileUrl2\"\n                    :before-upload=\"videoSaveToUrl\" :data=\"uploadData\" :headers=\"header\" :multiple=\"true\"\n                    style=\"display: inline-block\">\n              <div v-if=\"seletVideo === 0 && !formValidate.video_link\" class=\"videbox\">\n                +\n              </div>\n            </Upload>\n            <div class=\"iview-video-style\" v-if=\"formValidate.video_link\">\n              <video class=\"video-style\" :src=\"formValidate.video_link\" controls=\"controls\">\n                您的浏览器不支持 video 标签。\n              </video>\n              <div class=\"mark\"></div>\n              <Icon type=\"ios-trash-outline\" class=\"iconv\" @click=\"delVideo\" />\n            </div>\n            <Progress class=\"progress\" :percent=\"progress\" :stroke-width=\"5\" v-if=\"upload.videoIng || videoIng\" />\n            <div class=\"tips\">建议时长：9～30秒，视频宽高比16:9</div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" class=\"goodsShow\">\n          <FormItem label=\"上架时间：\">\n            <RadioGroup v-model=\"formValidate.is_show\" @on-change=\"goodsOn\">\n              <Radio :label=\"1\">\n                <Icon type=\"social-apple\"></Icon>\n                <span>立即上架</span>\n              </Radio>\n              <Radio :label=\"2\">\n                <Icon type=\"social-android\"></Icon>\n                <span>定时上架</span>\n              </Radio>\n              <Radio :label=\"0\">\n                <Icon type=\"social-windows\"></Icon>\n                <span>放入仓库</span>\n              </Radio>\n            </RadioGroup>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.is_show == 2\">\n          <FormItem label=\"\">\n            <DatePicker type=\"datetime\" @on-change=\"onchangeShow\" :options=\"startPickOptions\"\n                        :value=\"formValidate.auto_on_time\" v-model=\"formValidate.auto_on_time\" placeholder=\"请选择上架时间\"\n                        format=\"yyyy-MM-dd HH:mm\" style=\"width: 260px\"></DatePicker>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" class=\"goodsShow\">\n          <FormItem label=\"定时下架：\">\n            <i-switch v-model=\"off_show\" :true-value=\"1\" :false-value=\"0\" size=\"large\" @on-change=\"goodsOff\">\n              <span slot=\"open\">开启</span>\n              <span slot=\"close\">关闭</span>\n            </i-switch>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"off_show == 1\">\n          <FormItem label=\"\">\n            <DatePicker type=\"datetime\" @on-change=\"onchangeOff\" :options=\"endPickOptions\"\n                        :value=\"formValidate.auto_off_time\" v-model=\"formValidate.auto_off_time\" placeholder=\"请选择下架时间\"\n                        format=\"yyyy-MM-dd HH:mm\" style=\"width: 260px\"></DatePicker>\n            <div class=\"tips\">\n              开启定时下架后，系统会在设置时间下架该商品。下架时间需晚于开售时间，商品才能定时开售。\n            </div>\n          </FormItem>\n        </Col>\n      </Row>\n      <Row :gutter=\"24\" type=\"flex\" v-show=\"currentTab === '2'\">\n        <Col span=\"24\" v-if=\"formValidate.product_type != 4\">\n          <FormItem label=\"商品规格：\" props=\"spec_type\">\n            <RadioGroup v-model=\"formValidate.spec_type\" @on-change=\"changeSpec\">\n              <Radio :label=\"0\" class=\"radio\">单规格</Radio>\n              <Radio :label=\"1\">多规格</Radio>\n            </RadioGroup>\n          </FormItem>\n        </Col>\n        <!-- 多规格添加-->\n        <Col span=\"24\" v-if=\"formValidate.spec_type === 1  && formValidate.product_type != 4\" class=\"noForm\">\n          <Col span=\"24\">\n            <FormItem label=\"选择规格：\" prop=\"\">\n              <div class=\"acea-row row-middle\">\n                <Select v-model=\"formValidate.selectRule\" style=\"width: 23%\">\n                  <Option v-for=\"(item, index) in ruleList\" :value=\"item.rule_name\" :key=\"item.id\">{{ item.rule_name }}\n                  </Option>\n                </Select>\n                <Button type=\"primary\" class=\"mr20\" @click=\"confirm\">确认</Button>\n                <Button @click=\"addRule\">添加规格模板</Button>\n              </div>\n            </FormItem>\n          </Col>\n          <Col span=\"24\">\n            <FormItem v-if=\"attrs.length !== 0\">\n              <draggable class=\"dragArea list-group\" :list=\"attrs\" group=\"peoples\" handle=\".move-icon\" :move=\"checkMove\"\n                         @end=\"end\">\n                <div v-for=\"(item, index) in attrs\" :key=\"index+'attrs'\" class=\"acea-row row-middle mb10\">\n                  <div class=\"move-icon\">\n                    <span class=\"iconfont icondrag2\"></span>\n                  </div>\n                  <div style=\"width: 90%\" :class=\"moveIndex === index ? 'borderStyle' : ''\">\n                    <div class=\"acea-row row-middle\">\n                      <span class=\"mr5\">{{ item.value }}</span>\n                      <Icon type=\"ios-close-circle\" size=\"14\" class=\"curs\" @click=\"handleRemoveRole(index)\" />\n                    </div>\n                    <div class=\"rulesBox\">\n                      <draggable :list=\"item.detail\" handle=\".drag\">\n                        <Tag type=\"dot\" closable color=\"primary\" v-for=\"(j, indexn) in item.detail\" :key=\"indexn\" :name=\"j\"\n                             class=\"mr20 drag\" @on-close=\"handleRemove2(item.detail, indexn)\">{{ j }}</Tag>\n                      </draggable>\n                      <Input search enter-button=\"添加\" placeholder=\"请输入属性名称\" v-model=\"item.detail.attrsVal\"\n                             @on-search=\"createAttr(item.detail.attrsVal, index)\" class=\"width-add\" />\n                    </div>\n                  </div>\n                </div>\n              </draggable>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"createBnt\">\n            <FormItem>\n              <Button type=\"primary\" icon=\"md-add\" @click=\"addBtn\" class=\"mr15\">添加新规格</Button>\n              <Button type=\"success\" @click=\"generate(1)\">立即生成</Button>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"showIput\">\n            <Col :xl=\"6\" :lg=\"9\" :md=\"10\" :sm=\"24\" :xs=\"24\">\n              <FormItem label=\"规格：\">\n                <Input placeholder=\"请输入规格\" v-model=\"formDynamic.attrsName\" />\n              </FormItem>\n            </Col>\n            <Col :xl=\"6\" :lg=\"9\" :md=\"10\" :sm=\"24\" :xs=\"24\">\n              <FormItem label=\"规格值：\">\n                <Input v-model=\"formDynamic.attrsVal\" placeholder=\"请输入规格值\" />\n              </FormItem>\n            </Col>\n            <Col :xl=\"6\" :lg=\"5\" :md=\"10\" :sm=\"24\" :xs=\"24\">\n              <FormItem>\n                <Button type=\"primary\" class=\"mr15\" @click=\"createAttrName\">确定</Button>\n                <Button @click=\"offAttrName\">取消</Button>\n              </FormItem>\n            </Col>\n          </Col>\n          <!-- 多规格设置-->\n          <Col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\" v-if=\"\n          manyFormValidate.length &&\n          formValidate.header.length !== 0 &&\n          attrs.length !== 0\n        \">\n            <!-- 批量设置-->\n            <Col span=\"24\">\n              <FormItem label=\"批量设置：\" class=\"labeltop\">\n                <Table :data=\"oneFormBatch\" :columns=\"\n            formValidate.product_type == 1\n              ? columnsCarMy\n              : formValidate.product_type == 3\n                ? columnsFictitious\n                : columns2\n          \" border>\n                  <template slot-scope=\"{ row, index }\" slot=\"attr\">\n                    <div @click=\"batchAttr\" class=\"acea-row row-between-wrapper\" style=\"cursor: pointer\">\n                      <div style=\"width: 41px\">{{ oneFormBatch[0].attr }}</div>\n                      <span class=\"iconfont icondrop-down\"></span>\n                    </div>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"pic\">\n                    <div class=\"acea-row row-middle row-center-wrapper\" @click=\"modalPicTap('dan', 'duopi', index)\">\n                      <div class=\"pictrue pictrueTab\" v-if=\"oneFormBatch[0].pic\">\n                        <img v-lazy=\"oneFormBatch[0].pic\" />\n                      </div>\n                      <div class=\"upLoad pictrueTab acea-row row-center-wrapper\" v-else>\n                        <Icon type=\"ios-camera-outline\" size=\"26\" class=\"iosfont\" />\n                      </div>\n                    </div>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"price\">\n                    <InputNumber v-model=\"oneFormBatch[0].price\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n\t\t\t\t  <template slot-scope=\"{ row, index }\" slot=\"settle_price\" v-if='merchantType == 2'>\n\t\t\t\t    <InputNumber\n\t\t\t\t        v-model=\"oneFormBatch[0].settle_price\"\n\t\t\t\t        :min=\"0\"\n\t\t\t\t        class=\"priceBox\"\n\t\t\t\t    ></InputNumber>\n\t\t\t\t  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"cost\">\n                    <InputNumber v-model=\"oneFormBatch[0].cost\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"ot_price\">\n                    <InputNumber v-model=\"oneFormBatch[0].ot_price\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"stock\">\n                    <InputNumber v-model=\"oneFormBatch[0].stock\" :min=\"0\"\n                                 :disabled=\"formValidate.product_type == 1 || openErp\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"bar_code\">\n                    <Input v-model=\"oneFormBatch[0].bar_code\"></Input>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"code\">\n                    <Input v-model=\"oneFormBatch[0].code\"></Input>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"weight\">\n                    <InputNumber v-model=\"oneFormBatch[0].weight\" :step=\"0.1\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"volume\">\n                    <InputNumber v-model=\"oneFormBatch[0].volume\" :step=\"0.1\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"fictitious\" v-if=\"formValidate.product_type == 1\">\n                    <Button v-if=\"!row.virtual_list && !row.stock\" @click=\"addVirtual(0, 'oneFormBatch')\">添加卡密</Button>\n                    <span v-else class=\"seeCatMy\" @click=\"seeVirtual(oneFormBatch[0], 'oneFormBatch', 0)\">已设置</span>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"action\">\n                    <a @click=\"batchAdd\">批量设置</a>\n                    <Divider type=\"vertical\" />\n                    <a @click=\"batchDel\">清空</a>\n                  </template>\n                </Table>\n              </FormItem>\n            </Col>\n            <!-- 多规格表格-->\n            <Col span=\"24\">\n              <FormItem label=\"商品属性：\" class=\"labeltop\">\n                <Table :data=\"manyFormValidate\" :columns=\"formValidate.header\" border>\n                  <template slot-scope=\"{ row, index }\" v-for=\"(item, i) in attrData.length\" :slot=\"'value' + (i + 1)\">\n                    <div :class=\"manyFormValidate[index].select ? 'selectOn' : ''\">\n                      {{ manyFormValidate[index]['value' + (i + 1)] }}\n                    </div>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"pic\">\n                    <div class=\"acea-row row-middle row-center-wrapper\" @click=\"modalPicTap('dan', 'duoTable', index)\">\n                      <div class=\"pictrue pictrueTab\" v-if=\"manyFormValidate[index].pic\">\n                        <img v-lazy=\"manyFormValidate[index].pic\" />\n                      </div>\n                      <div class=\"upLoad pictrueTab acea-row row-center-wrapper\" v-else>\n                        <Icon type=\"ios-camera-outline\" size=\"21\" />\n                      </div>\n                    </div>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"price\">\n                    <InputNumber v-model=\"manyFormValidate[index].price\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n\t\t\t\t  <template slot-scope=\"{ row, index }\" slot=\"settle_price\" v-if=\"merchantType == 2\">\n\t\t\t\t    <InputNumber\n\t\t\t\t        v-model=\"manyFormValidate[index].settle_price\"\n\t\t\t\t        :min=\"0\"\n\t\t\t\t        class=\"priceBox\"\n\t\t\t\t    ></InputNumber>\n\t\t\t\t  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"cost\">\n                    <InputNumber v-model=\"manyFormValidate[index].cost\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"ot_price\">\n                    <InputNumber v-model=\"manyFormValidate[index].ot_price\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"stock\">\n                    <InputNumber v-model=\"manyFormValidate[index].stock\" :min=\"0\" :precision=\"0\"\n                                 :disabled=\"formValidate.product_type == 1 || openErp\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"fictitious\" v-if=\"formValidate.product_type == 1\">\n                    <Button v-if=\"\n                (!row.virtual_list || !row.virtual_list.length) &&\n                !row.stock\n              \" @click=\"addVirtual(index, 'manyFormValidate')\">添加卡密</Button>\n                    <span v-else class=\"seeCatMy\" @click=\"seeVirtual(row, 'manyFormValidate', index)\">已设置</span>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"bar_code\">\n                    <Input v-model=\"manyFormValidate[index].bar_code\"></Input>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"code\">\n                    <Input v-model=\"manyFormValidate[index].code\"></Input>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"weight\">\n                    <InputNumber v-model=\"manyFormValidate[index].weight\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"volume\">\n                    <InputNumber v-model=\"manyFormValidate[index].volume\" :min=\"0\" class=\"priceBox\"></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"action\">\n                    <a @click=\"delAttrTable(index)\">删除</a>\n                  </template>\n                </Table>\n              </FormItem>\n            </Col>\n          </Col>\n        </Col>\n        <!-- 单规格表格-->\n        <div v-if=\"formValidate.spec_type === 0 || formValidate.product_type == 4\" style=\"width: 100%\">\n          <Col span=\"24\" v-if=\"formValidate.product_type != 4\">\n            <FormItem label=\"图片：\" prop=\"image\">\n              <div class=\"pictrueBox\" @click=\"modalPicTap('dan', 'danTable', 0)\">\n                <div class=\"pictrue\" v-if=\"oneFormValidate[0].pic\">\n                  <img v-lazy=\"oneFormValidate[0].pic\" />\n                </div>\n                <div class=\"upLoad acea-row row-center-wrapper\" v-else>\n                  <Input v-model=\"oneFormValidate[0].pic\" class=\"input-display\"></Input>\n                  <Icon type=\"ios-camera-outline\" size=\"26\" />\n                </div>\n              </div>\n            </FormItem>\n          </Col>\n          <Col class=\"asterisk\" span=\"24\" v-if=\"formValidate.product_type == 4\">\n            <div class=\"asteriskInfo on2\">*</div>\n            <FormItem label=\"核销次数：\">\n              <InputNumber\n                  v-model=\"oneFormValidate[0].write_times\"\n                  :min=\"1\"\n                  :max=\"99999999\"\n                  :precision=\"0\"\n                  v-width=\"'50%'\"\n                  placeholder=\"请输入核销次数\"\n              ></InputNumber>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"formValidate.product_type == 4\">\n            <FormItem required label=\"核销时效：\">\n              <RadioGroup v-model=\"oneFormValidate[0].write_valid\">\n                <Radio :label=\"1\">永久有效</Radio>\n                <Radio :label=\"2\">购买后几天有效</Radio>\n                <Radio :label=\"3\">固定有效期</Radio>\n              </RadioGroup>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"oneFormValidate[0].write_valid==2\">\n            <FormItem\n                label=\"\"\n                prop=\"freight\"\n            >\n              <div class=\"acea-row row-middle\">\n                <InputNumber\n                    :min=\"1\"\n                    v-model=\"oneFormValidate[0].days\"\n                    placeholder=\"请输入有效天数\"\n                    v-width=\"'50%'\"\n                />\n                <span class=\"ml10\">天</span>\n              </div>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"oneFormValidate[0].write_valid==3\">\n            <FormItem\n                label=\"\"\n                prop=\"freight\"\n            >\n              <div class=\"acea-row row-middle\">\n                <DatePicker :editable=\"false\" type=\"daterange\" format=\"yyyy-MM-dd\" placeholder=\"请选择固定有效期\"\n                            @on-change=\"onchangeTime\" v-width=\"'50%'\" :value=\"oneFormValidate[0].section_time\" v-model=\"oneFormValidate[0].section_time\"></DatePicker>\n              </div>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" class=\"asterisk\">\n            <!-- <div class=\"asteriskInfo\">*</div> -->\n            <FormItem label=\"售价：\">\n              <InputNumber v-model=\"oneFormValidate[0].price\" :min=\"0\" :max=\"99999999\" v-width=\"'50%'\"></InputNumber>\n            </FormItem>\n          </Col>\n\t\t  <Col span=\"24\" class=\"asterisk\" v-if=\"merchantType == 2\">\n\t\t    <!-- <div class=\"asteriskInfo on\">*</div> -->\n\t\t    <FormItem label=\"结算价：\">\n\t\t      <InputNumber\n\t\t          v-model=\"oneFormValidate[0].settle_price\"\n\t\t          :min=\"0\"\n\t\t          :max=\"99999999\"\n\t\t          v-width=\"'50%'\"\n\t\t      ></InputNumber>\n\t\t    </FormItem>\n\t\t  </Col>\n          <Col span=\"24\" class=\"asterisk\">\n            <!-- <div class=\"asteriskInfo on\">*</div> -->\n            <FormItem label=\"成本价：\">\n              <InputNumber v-model=\"oneFormValidate[0].cost\" :min=\"0\" :max=\"99999999\" v-width=\"'50%'\"></InputNumber>\n            </FormItem>\n          </Col>\n          <Col span=\"24\">\n            <FormItem label=\"原价：\">\n              <InputNumber v-model=\"oneFormValidate[0].ot_price\" :min=\"0\" :max=\"99999999\" v-width=\"'50%'\"></InputNumber>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" class=\"asterisk\">\n            <!-- <div class=\"asteriskInfo\">*</div> -->\n            <FormItem label=\"库存：\">\n              <InputNumber v-model=\"oneFormValidate[0].stock\" :min=\"0\" :max=\"99999999\"\n                           :disabled=\"formValidate.product_type == 1 || openErp\" :precision=\"0\" v-width=\"'50%'\"></InputNumber>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"formValidate.product_type != 4\">\n            <FormItem label=\"商品条形码：\">\n              <Input v-model.trim=\"oneFormValidate[0].bar_code\" v-width=\"'50%'\" placeholder=\"请输入商品条形码\"></Input>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"formValidate.product_type != 4\">\n            <FormItem label=\"商品编号：\">\n              <Input v-model.trim=\"oneFormValidate[0].code\" v-width=\"'50%'\" placeholder=\"请输入商品编码\"></Input>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"formValidate.product_type == 0\">\n            <FormItem label=\"重量（KG）：\">\n              <InputNumber v-model=\"oneFormValidate[0].weight\" :min=\"0\" :max=\"99999999\" v-width=\"'50%'\"></InputNumber>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"formValidate.product_type == 0\">\n            <FormItem label=\"体积(m³)：\">\n              <InputNumber v-model=\"oneFormValidate[0].volume\" :min=\"0\" :max=\"99999999\" v-width=\"'50%'\"></InputNumber>\n            </FormItem>\n          </Col>\n          <Col span=\"24\" v-if=\"formValidate.product_type == 1\">\n            <FormItem label=\"卡密设置：\">\n              <Button v-if=\"\n              !oneFormValidate[0].virtual_list.length &&\n              !oneFormValidate[0].stock\n            \" @click=\"addVirtual(0, 'oneFormValidate')\">添加卡密</Button>\n              <span v-else class=\"seeCatMy\" @click=\"seeVirtual(oneFormValidate[0], 'oneFormValidate', 0)\">已设置</span>\n            </FormItem>\n          </Col>\n        </div>\n      </Row>\n      <!-- 商品详情 -->\n      <Row v-if=\"currentTab === '3'\">\n        <Col span=\"20\">\n          <wangeditor style=\"width: 100%\" :content=\"contents\" @editorContent=\"getEditorContent\"></wangeditor>\n        </Col>\n        <!-- <Col span=\"6\">\n        <div class=\"ifam\">\n          <div class=\"content\" v-html=\"content\"></div>\n        </div>\n        </Col> -->\n      </Row>\n      <!-- 物流设置 -->\n      <Row v-show=\"currentTab === '4'\">\n        <Col v-bind=\"grid3\">\n          <FormItem label=\"配送方式：\" prop=\"\" required>\n            <CheckboxGroup v-model=\"formValidate.delivery_type\">\n              <Checkbox label=\"1\" v-if=\"merchantType!=1\">快递</Checkbox>\n              <Checkbox label=\"3\">门店配送</Checkbox>\n              <Checkbox label=\"2\">到店自提</Checkbox>\n            </CheckboxGroup>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"运费设置：\">\n            <RadioGroup v-model=\"formValidate.freight\">\n              <Radio :label=\"1\">包邮</Radio>\n              <Radio :label=\"2\">固定邮费</Radio>\n              <Radio :label=\"3\">运费模板</Radio>\n            </RadioGroup>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.freight == 2\">\n          <FormItem label=\"\" prop=\"freight\">\n            <div class=\"acea-row row-middle\">\n              <InputNumber :min=\"0\" v-model=\"formValidate.postage\" placeholder=\"请输入金额\" class=\"perW20 maxW\" />\n              <span class=\"ml10\">元</span>\n            </div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.freight == 3\">\n          <FormItem label=\"\" prop=\"\">\n            <div class=\"acea-row\">\n              <Select v-model=\"formValidate.temp_id\" clearable class=\"perW20 maxW\">\n                <Option v-for=\"(item, index) in templateList\" :value=\"item.id\" :key=\"item.id\">{{ item.name }}</Option>\n              </Select>\n              <!-- <Button @click=\"addTemp\" class=\"ml15\">添加运费模板</Button> -->\n            </div>\n          </FormItem>\n        </Col>\n      </Row>\n      <!-- 营销设置 -->\n      <Row v-show=\"currentTab === '5'\">\n        <Col span=\"24\">\n          <FormItem label=\"虚拟销量：\">\n            <InputNumber v-width=\"'50%'\" :min=\"0\" :max=\"999999\" v-model=\"formValidate.ficti\" placeholder=\"请输入虚拟销量\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"排序：\">\n            <InputNumber :min=\"0\" :max=\"999999\" v-width=\"'50%'\" v-model=\"formValidate.sort\" placeholder=\"请输入排序\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <div class=\"lines\"></div>\n        </Col>\n        <Col span=\"24\" v-if=\"merchantType==0\">\n          <FormItem label=\"赠送积分：\" prop=\"give_integral\">\n            <InputNumber v-width=\"'50%'\" v-model=\"formValidate.give_integral\" :min=\"0\" :max=\"999999\"\n                         placeholder=\"请输入积分\" />\n          </FormItem>\n        </Col>\n        <Col v-bind=\"grid3\" v-if=\"merchantType==0\">\n          <FormItem label=\"赠送优惠券：\">\n            <div v-if=\"couponName.length\" class=\"mb20\">\n              <Tag closable v-for=\"(item, index) in couponName\" :key=\"index+'coupon'\" @on-close=\"handleClose(item)\">{{ item.title\n                }}</Tag>\n            </div>\n            <Button type=\"primary\" @click=\"addCoupon\">添加优惠券</Button>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"merchantType==0\">\n          <FormItem label=\"关联用户标签：\" prop=\"label_id\" class=\"labelClass\">\n            <div class=\"labelInput acea-row row-between-wrapper\" @click=\"openLabel\">\n              <div>\n                <div v-if=\"dataLabel.length\">\n                  <Tag closable v-for=\"(item, index) in dataLabel\" :key=\"item.id\" @on-close=\"closeLabel(item)\">{{ item.label_name }}\n                  </Tag>\n                </div>\n                <span class=\"span\" v-else>选择用户关联标签</span>\n              </div>\n              <Icon type=\"ios-arrow-down\" />\n            </div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"merchantType==0\">\n          <FormItem label=\"仅会员可见：\">\n            <i-switch v-model=\"formValidate.is_vip_product\" :true-value=\"1\" :false-value=\"0\" size=\"large\">\n              <span slot=\"open\">开启</span>\n              <span slot=\"close\">关闭</span>\n            </i-switch>\n            <div class=\"tips\">开启后仅付费会员可以看见并购买此商品</div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"merchantType==0\">\n          <FormItem label=\"单独设置：\">\n            <CheckboxGroup class=\"checkAlls\" v-model=\"formValidate.is_sub\" @on-change=\"checkAllGroupChange\">\n              <Checkbox :label=\"1\">佣金设置（数字即返佣金额）</Checkbox>\n              <Checkbox :label=\"0\">付费会员价</Checkbox>\n            </CheckboxGroup>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.is_sub.length\">\n          <!--单规格返佣-->\n          <FormItem label=\"商品属性：\" v-if=\"formValidate.spec_type === 0\">\n            <Table :data=\"oneFormValidate\" :columns=\"columnsInstall\" border>\n              <template slot-scope=\"{ row, index }\" slot=\"pic\">\n                <div class=\"pictrue pictrueTab\">\n                  <img v-lazy=\"oneFormValidate[0].pic\" />\n                </div>\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"price\">{{\n                  oneFormValidate[0].price\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"cost\">{{\n                  oneFormValidate[0].cost\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"ot_price\">{{\n                  oneFormValidate[0].ot_price\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"stock\">{{\n                  oneFormValidate[0].stock\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"bar_code\">{{\n                  oneFormValidate[0].bar_code\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"code\">{{\n                  oneFormValidate[0].code\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"weight\">{{\n                  oneFormValidate[0].weight\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"volume\">{{\n                  oneFormValidate[0].volume\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"brokerage\">\n                <InputNumber v-model=\"oneFormValidate[0].brokerage\" :min=\"0\" class=\"priceBox\"></InputNumber>\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"brokerage_two\">\n                <InputNumber v-model=\"oneFormValidate[0].brokerage_two\" :min=\"0\" class=\"priceBox\"></InputNumber>\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"vip_price\">\n                <InputNumber v-model=\"oneFormValidate[0].vip_price\" :min=\"0\" class=\"priceBox\"></InputNumber>\n              </template>\n            </Table>\n          </FormItem>\n          <!--多规格返佣-->\n          <FormItem label=\"批量设置：\" v-if=\"formValidate.spec_type === 1\">\n          <span v-if=\"formValidate.is_sub.indexOf(1) > -1\">\n            一级返佣：<InputNumber placeholder=\"请输入一级返佣\" :min=\"0\" class=\"columnsBox perW20\" v-model=\"manyBrokerage\">\n            </InputNumber>\n            二级返佣：<InputNumber placeholder=\"请输入二级返佣\" :min=\"0\" class=\"columnsBox perW20\" v-model=\"manyBrokerageTwo\">\n            </InputNumber>\n          </span>\n            <span v-if=\"formValidate.is_sub.indexOf(0) > -1\">\n            会员价：<InputNumber placeholder=\"请输入会员价\" :min=\"0\" class=\"columnsBox perW20\" v-model=\"manyVipPrice\">\n            </InputNumber>\n          </span>\n            <Button type=\"primary\" @click=\"brokerageSetUp\">批量设置</Button>\n          </FormItem>\n          <FormItem label=\"商品属性：\" v-if=\"formValidate.spec_type === 1 && manyFormValidate.length\">\n            <Table v-if=\"formValidate.is_sub && visible\" :data=\"manyFormValidate\" :columns=\"columnsInstal2\" border>\n              <template slot-scope=\"{ row, index }\" v-for=\"(item, i) in attrData.length\" :slot=\"'value' + (i + 1)\">\n                <div>{{ manyFormValidate[index]['value' + (i + 1)] }}</div>\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"pic\">\n                <div class=\"pictrue pictrueTab\">\n                  <img v-lazy=\"manyFormValidate[index].pic\" />\n                </div>\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"price\">{{\n                  manyFormValidate[index].price\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"cost\">{{\n                  manyFormValidate[index].cost\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"ot_price\">{{\n                  manyFormValidate[index].ot_price\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"stock\">{{\n                  manyFormValidate[index].stock\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"bar_code\">{{\n                  manyFormValidate[index].bar_code\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"code\">{{\n                  manyFormValidate[index].code\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"weight\">{{\n                  manyFormValidate[index].weight\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"volume\">{{\n                  manyFormValidate[index].volume\n                }}</template>\n              <template slot-scope=\"{ row, index }\" slot=\"brokerage\">\n                <InputNumber v-model=\"manyFormValidate[index].brokerage\" :min=\"0\" class=\"priceBox\"></InputNumber>\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"brokerage_two\">\n                <InputNumber v-model=\"manyFormValidate[index].brokerage_two\" :min=\"0\" class=\"priceBox\"></InputNumber>\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"vip_price\">\n                <InputNumber v-model=\"manyFormValidate[index].vip_price\" :min=\"0\" class=\"priceBox\"></InputNumber>\n              </template>\n            </Table>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"merchantType==0\">\n          <div class=\"lines\"></div>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"是否限购：\">\n            <i-switch v-model=\"formValidate.is_limit\" :true-value=\"1\" :false-value=\"0\" size=\"large\" @on-change=\"limitTap\">\n              <span slot=\"open\">开启</span>\n              <span slot=\"close\">关闭</span>\n            </i-switch>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.is_limit\">\n          <FormItem label=\"限购类型：\">\n            <RadioGroup v-model=\"formValidate.limit_type\">\n              <Radio :label=\"1\">单次限购</Radio>\n              <Radio :label=\"2\">长期限购</Radio>\n            </RadioGroup>\n            <div class=\"tips\">\n              单次限购是限制每次下单最多购买的数量，长期限购是限制一个用户总共可以购买的数量\n            </div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.is_limit\">\n          <FormItem label=\"限购数量：\">\n            <InputNumber :min=\"1\" v-model=\"formValidate.limit_num\" placeholder=\"请输入限购数量\" class=\"perW20 maxW\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.product_type == 0 && merchantType==0\">\n          <FormItem label=\"预售商品：\">\n            <i-switch v-model=\"formValidate.is_presale_product\" :true-value=\"1\" :false-value=\"0\" size=\"large\">\n              <span slot=\"open\">开启</span>\n              <span slot=\"close\">关闭</span>\n            </i-switch>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"\n          formValidate.product_type == 0 && formValidate.is_presale_product\n        \">\n          <FormItem label=\"预售活动时间：\" prop=\"presale_time\">\n            <div class=\"acea-row row-middle\">\n              <DatePicker :editable=\"false\" :options=\"datePickerOptions\" type=\"datetimerange\" format=\"yyyy-MM-dd HH:mm\"\n                          placeholder=\"请选择活动时间\" @on-change=\"onchangeTime\" :value=\"formValidate.presale_time\"\n                          v-model=\"formValidate.presale_time\" v-width=\"'50%'\"></DatePicker>\n            </div>\n            <div class=\"tips\">\n              设置活动开启结束时间，用户可以在设置时间内发起参与预售\n            </div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"\n          formValidate.product_type == 0 && formValidate.is_presale_product\n        \">\n          <FormItem label=\"发货时间：\" prop=\"presale_day\">\n            <div class=\"acea-row row-middle\">\n              <span class=\"mr10\">预售活动结束后</span>\n              <InputNumber placeholder=\"请输入发货时间\" :precision=\"0\" :min=\"1\" style=\"width: 100px\"\n                           v-model=\"formValidate.presale_day\" />\n              <span class=\"ml10\">天之内</span>\n            </div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"merchantType==0\">\n\t\t\t<FormItem label=\"优品推荐：\">\n\t\t\t  <i-switch v-model=\"formValidate.is_good\" :true-value=\"1\" :false-value=\"0\" size=\"large\">\n\t\t\t    <span slot=\"open\">开启</span>\n\t\t\t    <span slot=\"close\">关闭</span>\n\t\t\t  </i-switch>\n\t\t\t</FormItem>\n          <!-- <FormItem label=\"商品推荐：\">\n            <CheckboxGroup v-model=\"formValidate.recommend\" class=\"checkAlls\">\n              <Checkbox label=\"is_hot\">热卖单品</Checkbox>\n              <Checkbox label=\"is_benefit\">促销单品</Checkbox>\n              <Checkbox label=\"is_best\">精品推荐</Checkbox>\n              <Checkbox label=\"is_new\">首发新品</Checkbox>\n              <Checkbox label=\"is_good\">优品推荐</Checkbox>\n            </CheckboxGroup>\n          </FormItem> -->\n        </Col>\n        <Col v-bind=\"grid3\" v-if=\"merchantType==0\">\n          <FormItem label=\"活动优先级：\">\n            <div class=\"color-list acea-row row-middle\">\n              <div class=\"color-item acea-row row-center-wrapper\" :class=\"activity[color]\"\n                   v-for=\"(color, index) in formValidate.activity\" v-dragging=\"{\n                item: color,\n                list: formValidate.activity,\n                group: 'color',\n              }\" :key=\"color\">\n                <div class=\"num\">{{ index + 1 }}</div>\n                <div>{{ color }}</div>\n              </div>\n              <div class=\"tips\">可拖动按钮调整活动的优先展示顺序</div>\n            </div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"merchantType==0\">\n          <FormItem label=\"选择优品推荐商品：\">\n            <div class=\"acea-row\">\n              <div class=\"pictrue\" v-for=\"(item, index) in goodsData\" :key=\"index+'goods'\">\n                <img v-lazy=\"item.image\" />\n                <Button shape=\"circle\" icon=\"md-close\" @click.native=\"bindDelete(index)\" class=\"btndel\"></Button>\n              </div>\n              <div v-if=\"goodsData.length < 12\" class=\"upLoad acea-row row-center-wrapper\" @click=\"goodsTap\">\n                <Icon type=\"ios-camera-outline\" size=\"26\" />\n              </div>\n            </div>\n          </FormItem>\n        </Col>\n      </Row>\n      <!-- 其他设置-->\n      <Row v-show=\"currentTab === '6'\">\n        <Col span=\"24\">\n          <FormItem label=\"商品关键字：\" prop=\"\">\n            <Input v-model=\"formValidate.keyword\" placeholder=\"请输入商品关键字\" v-width=\"'50%'\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品简介：\" prop=\"\">\n            <Input v-model=\"formValidate.store_info\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入商品简介\" v-width=\"'50%'\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品口令：\">\n            <Input v-model=\"formValidate.command_word\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入商品口令\" v-width=\"'50%'\" />\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品推荐图：\">\n            <div class=\"pictrueBox\" @click=\"modalPicTap('dan', 'recommend_image')\">\n              <div class=\"pictrue\" v-if=\"formValidate.recommend_image\">\n                <img v-lazy=\"formValidate.recommend_image\" />\n                <Input v-model=\"formValidate.recommend_image\" class=\"input-display\"></Input>\n              </div>\n              <div class=\"upLoad acea-row row-center-wrapper\" v-else>\n                <Input v-model=\"formValidate.recommend_image\" class=\"input-display\"></Input>\n                <Icon type=\"ios-camera-outline\" size=\"26\" />\n              </div>\n            </div>\n            <div class=\"tips\">(建议图片比例5:2)</div>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"服务保障：\">\n            <CheckboxGroup v-model=\"formValidate.ensure_id\" class=\"checkAlls\">\n              <Checkbox :label=\"item.id\" v-for=\"(item, index) in ensureData\" :key=\"item.id\">{{\n                  item.name\n                }}</Checkbox>\n            </CheckboxGroup>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"商品参数：\" prop=\"\">\n            <Select v-model=\"formValidate.specs_id\" clearable filterable v-width=\"'50%'\" placeholder=\"请输入商品参数\"\n                    @on-change=\"specsInfo\">\n              <Option v-for=\"(item, index) in specsData\" :value=\"item.id\" :key=\"item.id\">{{ item.name }}</Option>\n            </Select>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.specs_id\">\n          <FormItem label=\"\" props=\"\">\n            <Table border :columns=\"specsColumns\" :data=\"specsList\" ref=\"table\" class=\"specsList\" width=\"700\">\n              <template slot-scope=\"{ row, index }\" slot=\"action\">\n                <a @click=\"delSpecs(index)\" v-if=\"index > 0\">删除</a>\n              </template>\n            </Table>\n            <Button class=\"mt20\" @click=\"addSpecs\">添加参数</Button>\n          </FormItem>\n        </Col>\n        <Col span=\"24\" v-if=\"formValidate.product_type\">\n          <FormItem label=\"支持退款：\" props=\"status\" label-for=\"status\">\n            <i-switch v-model=\"formValidate.is_support_refund\" :true-value=\"1\" :false-value=\"0\" size=\"large\">\n              <span slot=\"open\">开启</span>\n              <span slot=\"close\">关闭</span>\n            </i-switch>\n          </FormItem>\n        </Col>\n        <Col span=\"24\">\n          <FormItem label=\"自定义留言：\">\n            <i-switch\n                v-model=\"customBtn\"\n                @on-change=\"customMessBtn\"\n                size=\"large\"\n            >\n              <span slot=\"open\">开启</span>\n              <span slot=\"close\">关闭</span>\n            </i-switch>\n            <div class=\"mt10\" v-if=\"customBtn\">\n              <Select\n                  v-model=\"formValidate.system_form_id\"\n                  clearable\n                  filterable\n                  v-width=\"'50%'\"\n                  placeholder=\"请选择\"\n                  @on-change=\"changeForm\"\n              >\n                <Option\n                    v-for=\"(item, index) in formList\"\n                    :value=\"item.id\"\n                    :key=\"item.id\"\n                >{{ item.name }}</Option\n                >\n              </Select>\n            </div>\n          </FormItem>\n<!--          <FormItem label=\"自定义留言：\">-->\n<!--            <i-switch v-model=\"customBtn\" @on-change=\"customMessBtn\" size=\"large\">-->\n<!--              <span slot=\"open\">开启</span>-->\n<!--              <span slot=\"close\">关闭</span>-->\n<!--            </i-switch>-->\n<!--            <div class=\"addCustom_content\" v-if=\"customBtn\">-->\n<!--              <div v-for=\"(item, index) in formValidate.custom_form\" type=\"flex\" :key=\"index+'custom'\" class=\"custom_box\">-->\n<!--                <Input v-model.trim=\"item.title\" :placeholder=\"'留言标题' + (index + 1)\" class=\"custom-input\" />-->\n<!--                <Select v-model=\"item.label\" class=\"select-add\">-->\n<!--                  <Option v-for=\"items in customList\" :value=\"items.value\" :key=\"items.value\">{{ items.label }}</Option>-->\n<!--                </Select>-->\n<!--                <Checkbox v-model=\"item.status\" :true-value=\"1\" :false-value=\"0\">必填</Checkbox>-->\n<!--                <div class=\"addfont\" @click=\"delcustom(index)\">删除</div>-->\n<!--              </div>-->\n<!--            </div>-->\n<!--            <div class=\"addCustomBox\" v-show=\"customBtn\">-->\n<!--              <div class=\"btn\" @click=\"addcustom\">+ 添加表单</div>-->\n<!--              <div class=\"tips\">用户下单时需填写的信息，最多可设置10条</div>-->\n<!--            </div>-->\n<!--          </FormItem>-->\n        </Col>\n        <Col span=\"24\" v-if=\"customBtn && formValidate.system_form_id\">\n          <FormItem label=\"\" props=\"\">\n            <Table border :columns=\"formColumns\" :data=\"formTypeList\" ref=\"table\" class=\"specsList on\">\n              <template slot-scope=\"{ row }\" slot=\"require\">\n                <span>{{row.require?'必填':'不必填'}}</span>\n              </template>\n            </Table>\n          </FormItem>\n        </Col>\n      </Row>\n      <Row v-show=\"currentTab === '8' && merchantType==0\" class=\"storeModule\">\n        <Col span=\"24\">\n          <RadioGroup v-model=\"formValidate.applicable_type\" class=\"radioGroup\">\n            <Radio :label=\"1\">全部门店</Radio>\n            <Radio :label=\"2\">部分门店</Radio>\n            <Radio :label=\"0\">仅平台适用</Radio>\n          </RadioGroup>\n          <div class=\"tips on\">可选择将商品同步到哪些门店使用，选择“仅平台适用“则商品不同步任何门店</div>\n        </Col>\n        <Col span=\"24\" class=\"mt20 mb20\" v-if=\"formValidate.applicable_type == 2\">\n          <Button type=\"primary\" @click=\"addStore\">添加门店</Button>\n        </Col>\n        <Col span=\"24\">\n          <div class=\"storeTable\" v-if=\"formValidate.applicable_type == 2\">\n            <Table\n                :columns=\"storeColumns\"\n                :data=\"storesList\"\n                ref=\"table\"\n                class=\"ivu-mt\"\n                highlight-row\n                no-userFrom-text=\"暂无数据\"\n                no-filtered-userFrom-text=\"暂无筛选结果\"\n            >\n              <template slot-scope=\"{ row }\" slot=\"image\">\n                <img :src=\"row.image\" />\n              </template>\n              <template slot-scope=\"{ row, index }\" slot=\"action\">\n                <a @click=\"delte(index)\">删除</a>\n              </template>\n            </Table>\n          </div>\n        </Col>\n      </Row>\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\n    </Form>\n    <!-- 商品评论表格 -->\n    <Table width=\"940\" v-if=\"currentTab === '7'\" ref=\"table\" :columns=\"replyColumns\" :data=\"replyData\" class=\"ivu-mt\"\n           :loading=\"replyLoading\" no-data-text=\"暂无数据\" no-filtered-data-text=\"暂无筛选结果\">\n      <template slot-scope=\"{ row }\" slot=\"info\">\n        <div class=\"imgPic acea-row row-middle\">\n          <viewer>\n            <div class=\"pictrue\"><img v-lazy=\"row.image\" /></div>\n          </viewer>\n          <div class=\"info line2\">{{ row.store_name }}</div>\n        </div>\n      </template>\n      <template slot-scope=\"{ row }\" slot=\"content\">\n        <div>用户：{{ row.nickname }}</div>\n        <div>评分：{{ row.score }}</div>\n        <div>\n          <div class=\"mb5 content_font\">{{ row.comment }}</div>\n          <viewer>\n            <div class=\"pictrue mr10\" v-for=\"(item, index) in row.pics || []\" :key=\"index+'pics'\">\n              <img v-lazy=\"item\" />\n            </div>\n          </viewer>\n        </div>\n      </template>\n      <!-- <template slot-scope=\"{ row }\" slot=\"reply\">\n        <Tooltip max-width=\"200\" placement=\"bottom\">\n          <span class=\"line2\">{{\n              row.replyComment ? row.replyComment.content : ''\n          }}</span>\n          <p slot=\"content\">\n            {{ row.replyComment ? row.replyComment.content : '' }}\n          </p>\n        </Tooltip>\n      </template> -->\n      <template slot-scope=\"{ row, index }\" slot=\"action\">\n        <a @click=\"seeReply(row)\">查看</a>\n        <Divider type=\"vertical\" />\n        <a @click=\"reply(row)\">回复</a>\n        <Divider type=\"vertical\" />\n        <a @click=\"delReply(row, '删除评论', index)\">删除</a>\n      </template>\n    </Table>\n    <div class=\"drawer-footer\">\n      <Button type=\"primary\" @click=\"handleSubmit('formValidate')\">保存</Button>\n    </div>\n    <Modal v-model=\"modalPic\" width=\"960px\" scrollable footer-hide closable title=\"上传商品图\" :mask-closable=\"false\">\n      <uploadPictures :isChoice=\"isChoice\" @getPic=\"getPic\" @getPicD=\"getPicD\" :gridBtn=\"gridBtn\" :gridPic=\"gridPic\"\n                      v-if=\"modalPic\"></uploadPictures>\n    </Modal>\n    <coupon-list ref=\"couponTemplates\" @nameId=\"nameId\" :couponids=\"formValidate.coupon_ids\" :updateIds=\"updateIds\"\n                 :updateName=\"updateName\"></coupon-list>\n    <!-- 商品列表 -->\n    <Modal v-model=\"goodsModals\" title=\"商品列表\" footerHide scrollable width=\"900\" @on-cancel=\"goodCancel\">\n      <goods-list v-if=\"goodsModals\" ref=\"goodslist\" @getProductId=\"getProductId\" :ischeckbox=\"true\"></goods-list>\n    </Modal>\n    <!-- 用户标签 -->\n    <Modal v-model=\"labelShow\" scrollable title=\"选择用户标签\" :closable=\"true\" width=\"540\" :footer-hide=\"true\"\n           :mask-closable=\"false\">\n      <userLabel ref=\"userLabel\" @activeData=\"activeData\" @close=\"labelClose\"></userLabel>\n    </Modal>\n    <!-- 商品标签 -->\n    <Modal v-model=\"storeLabelShow\" scrollable title=\"请选择商品标签\" :closable=\"true\" width=\"540\" :footer-hide=\"true\"\n           :mask-closable=\"false\">\n      <storeLabelList ref=\"storeLabel\" @activeData=\"activeStoreData\" @close=\"storeLabelClose\"></storeLabelList>\n    </Modal>\n    <replyList ref=\"replyList\"></replyList>\n    <Modal v-model=\"replyModal\" scrollable title=\"回复内容\" closable>\n      <Form ref=\"replyForm\" :model=\"replyForm\" :rules=\"ruleInline\" :label-position=\"labelPosition\"\n            @submit.native.prevent>\n        <FormItem prop=\"content\">\n          <Input v-model=\"replyForm.content\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入回复内容\" />\n        </FormItem>\n      </Form>\n      <div slot=\"footer\">\n        <Button type=\"primary\" @click=\"oks\">确定</Button>\n        <Button @click=\"cancels\">取消</Button>\n      </div>\n    </Modal>\n    <Modal v-model=\"storeModals\" title=\"门店列表\" footerHide  scrollable width=\"900\" @on-cancel=\"cancelStore\">\n      <store-list ref=\"storelist\" @getStoreId=\"getStoreId\" v-if=\"storeModals\"></store-list>\n    </Modal>\n  </Drawer>\n", null]}