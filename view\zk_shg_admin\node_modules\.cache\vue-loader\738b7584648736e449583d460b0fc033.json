{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue?vue&type=template&id=0d2b2f52&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue", "mtime": 1734512585792}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"mobile-page\",style:({\n  background: _vm.bottomBgColor,\n  marginTop: _vm.mTop + 'px',\n  paddingTop: _vm.topConfig + 'px',\n  paddingBottom: _vm.bottomConfig + 'px',\n  paddingLeft: _vm.prConfig + 'px',\n  paddingRight: _vm.prConfig + 'px'\n})},[_c('div',{staticClass:\"home_product\"},[(_vm.styleConfig == 0)?_c('div',{staticClass:\"hd_nav\"},_vm._l((_vm.navlist),function(item,index){return _c('div',{key:index,staticClass:\"item\",class:index == _vm.tabCur ? 'active' : ''},[_c('p',{staticClass:\"title\",style:({ color: index == _vm.tabCur ? (_vm.toneConfig ? _vm.textColor2 : '#E93323') : '#282828' })},[_vm._v(\"\\n          \"+_vm._s(item.chiild[0].val || '标题'))]),(item.chiild[1].val)?_c('span',{staticClass:\"label\",style:({ background: index == _vm.tabCur ? (_vm.toneConfig ? _vm.decorateColor : _vm.themeColor) : '' })},[_vm._v(_vm._s(item.chiild[1].val || '标题简介'))]):_vm._e()])}),0):_c('div',{staticClass:\"menus\",class:_vm.styleConfig == 2 ? 'on' : ''},[_vm._l((_vm.navlist),function(item,index){return (_vm.styleConfig == 1)?_c('div',{key:index,staticClass:\"item\",class:index == _vm.tabCur ? 'on' : '',style:({\n          color: index == _vm.tabCur ? (_vm.toneConfig ? _vm.textColor : '#333') : '#282828'\n        })},[_vm._v(_vm._s(item.chiild[0].val || '标题')),_c('span',{style:({\n      background: _vm.toneConfig ? _vm.decorateColor : _vm.themeColor\n    })})]):_vm._e()}),_vm._l((_vm.navlist),function(item,index){return (_vm.styleConfig == 2)?_c('div',{key:index,staticClass:\"item\",class:index == _vm.tabCur ? 'on3' : '',style:({\n          color: index == _vm.tabCur ? (_vm.toneConfig ? _vm.textColor2 : '#E93323') : '#282828'\n        })},[_vm._v(_vm._s(item.chiild[0].val || '标题')),_c('span',{style:({\n        borderColor: _vm.toneConfig ? _vm.decorateColor2 : _vm.colorStyle.theme\n      })})]):_vm._e()}),_vm._l((_vm.navlist),function(item,index){return (_vm.styleConfig == 3)?_c('div',{key:index,staticClass:\"item\",class:index == _vm.tabCur ? 'on2' : '',style:({\n          color: index == _vm.tabCur ? (_vm.toneConfig ? _vm.textColor3 : '#fff') : '#282828',\n          background: index == _vm.tabCur ? (_vm.toneConfig ? _vm.decorateColor : _vm.themeColor) : ''\n        })},[_vm._v(_vm._s(item.chiild[0].val || '标题'))]):_vm._e()}),_vm._l((_vm.navlist),function(item,index){return (_vm.styleConfig == 4)?_c('div',{key:index,staticClass:\"item pic\"},[_c('div',{staticClass:\"pictrue acea-row row-center-wrapper\",style:({ borderColor: index == _vm.tabCur ? (_vm.toneConfig ? _vm.decorateColorLeft : '#E93323') : '#EEEEEE' })},[(item.image)?_c('img',{staticClass:\"img\",attrs:{\"src\":item.image}}):_c('img',{attrs:{\"src\":require(\"../../assets/images/shan.png\")}})]),_c('div',{staticClass:\"title\",style:({\n          color: index == _vm.tabCur ? (_vm.toneConfig ? _vm.textColor3 : '#fff') : '#282828',\n          background: index == _vm.tabCur ? (_vm.toneConfig ? _vm.decorateColor : _vm.themeColor) : ''\n        })},[_vm._v(_vm._s(item.chiild[0].val || '标题'))])]):_vm._e()})],2),_c('div',{staticClass:\"list-wrapper\"},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"item\"},[_c('div',{staticClass:\"img-box\"},[(item.image)?_c('img',{staticClass:\"img\",style:({\n            borderRadius: _vm.bgRadius\n          }),attrs:{\"src\":item.image,\"alt\":\"\"}}):_c('div',{staticClass:\"empty-box\",style:({\n            borderRadius: _vm.bgRadius\n          })},[_c('img',{attrs:{\"src\":require(\"../../assets/images/shan.png\")}})])]),_c('div',{staticClass:\"info\",style:({ borderRadius: _vm.bgRadius2 })},[_c('div',{staticClass:\"title line1\"},[_vm._v(_vm._s(item.store_name || '海南梅陇蜜瓜 2.5kg海梅陇蜜瓜'))]),_vm._m(0,true),_c('div',{staticClass:\"price\"},[_c('div',{staticClass:\"num\"},[_c('span',[_vm._v(\"￥\")]),_vm._v(_vm._s(item.price ? _vm.$HandlePrice(item.price, 0) : 77)),_c('span',[_vm._v(_vm._s(item.price ? _vm.$HandlePrice(item.price, 1) : ''))])]),_c('img',{attrs:{\"src\":require(\"../../assets/images/goods02.png\")}})]),_c('div',{staticClass:\"sales\"},[_vm._v(\"已售\"+_vm._s(item.sales || 0)+\"件\")])]),(!_vm.cartConfig)?_c('div',{staticClass:\"jia\",style:({\n          background: _vm.toneCartConfig ? _vm.bntBgColor : _vm.themeColor\n        })},[_c('div',{staticClass:\"jiaCon\"},[(_vm.bntStyleConfig == 0)?_c('span',{staticClass:\"iconfont iconjiahao1\"}):_c('span',{staticClass:\"iconfont icongouwuche1\"})])]):_vm._e()])}),0)])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"pictrue\"},[_c('img',{attrs:{\"src\":require(\"../../assets/images/goods01.png\")}})])}]\n\nexport { render, staticRenderFns }"]}