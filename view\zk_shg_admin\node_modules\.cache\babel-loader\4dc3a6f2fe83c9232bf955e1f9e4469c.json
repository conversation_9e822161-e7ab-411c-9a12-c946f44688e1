{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue", "mtime": 1728874543955}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { formatDate as _formatDate } from '@/utils/validate';\nimport userLabel from \"../../../components/userLabel\";\nimport labelList from \"@/components/labelList\";\nimport { mapState } from \"vuex\";\nimport expandRow from \"./tableExpand.vue\";\nimport { userList, getUserData, isShowApi, editOtherApi, giveLevelApi, userSetGroup, userGroupApi, levelListApi, userSetLabelApi, userLabelApi, userSynchro, getUserSaveForm, giveLevelTimeApi, extendInfo as _extendInfo, batchProcess } from \"@/api/user\";\nimport { agentSpreadApi } from \"@/api/agent\";\nimport editFrom from \"../../../components/from/from\";\nimport sendFrom from \"@/components/sendCoupons/index\";\nimport userDetails from \"./handle/userDetails\";\nimport newsCategory from \"@/components/newsCategory/index\";\nimport city from \"@/utils/city\";\nimport customerInfo from \"@/components/customerInfo\";\nexport default {\n  name: \"user_list\",\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  components: {\n    expandRow: expandRow,\n    editFrom: editFrom,\n    sendFrom: sendFrom,\n    userDetails: userDetails,\n    newsCategory: newsCategory,\n    customerInfo: customerInfo,\n    userLabel: userLabel,\n    labelList: labelList\n  },\n  data: function data() {\n    return {\n      dataLabel: [],\n      labelListShow: false,\n      labelShow: false,\n      customerShow: false,\n      promoterShow: false,\n      labelActive: {\n        uid: 0\n      },\n      formInline: {\n        uid: 0,\n        spread_uid: 0,\n        image: \"\"\n      },\n      options: {\n        shortcuts: [{\n          text: \"今天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()));\n            return [start, end];\n          }\n        }, {\n          text: \"昨天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            return [start, end];\n          }\n        }, {\n          text: \"最近7天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 6)));\n            return [start, end];\n          }\n        }, {\n          text: \"最近30天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 29)));\n            return [start, end];\n          }\n        }, {\n          text: \"上月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            var day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, day)));\n            return [start, end];\n          }\n        }, {\n          text: \"本月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));\n            return [start, end];\n          }\n        }, {\n          text: \"本年\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)));\n            return [start, end];\n          }\n        }]\n      },\n      collapse: false,\n      headeType: \"-1\",\n      headeNum: [{\n        type: \"-1\",\n        name: \"全部\"\n      }, {\n        type: \"wechat\",\n        name: \"微信公众号\"\n      }, {\n        type: \"routine\",\n        name: \"微信小程序\"\n      }, {\n        type: \"h5\",\n        name: \"H5\"\n      }, {\n        type: \"pc\",\n        name: \"PC\"\n      }, {\n        type: \"app\",\n        name: \"APP\"\n      }],\n      address: [],\n      addresData: city,\n      isShowSend: true,\n      modal13: false,\n      maxCols: 4,\n      scrollerHeight: \"600\",\n      contentTop: \"130\",\n      contentWidth: \"98%\",\n      // grid: {\n      //   xl: 8,\n      //   lg: 8,\n      //   md: 12,\n      //   sm: 24,\n      //   xs: 24,\n      // },\n      grid2: {\n        xl: 18,\n        lg: 16,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      total: 0,\n      userFrom: {\n        label_id: \"\",\n        user_type: \"\",\n        status: \"\",\n        sex: \"\",\n        is_promoter: \"\",\n        country: \"\",\n        isMember: \"\",\n        pay_count: \"\",\n        user_time_type: \"\",\n        user_time: \"\",\n        nickname: \"\",\n        province: \"\",\n        city: \"\",\n        page: 1,\n        limit: 15,\n        level: \"\",\n        group_id: \"\",\n        field_key: \"\"\n      },\n      field_key: \"\",\n      level: \"\",\n      group_id: \"\",\n      label_id: \"\",\n      user_time_type: \"\",\n      pay_count: \"\",\n      userLists: [],\n      FromData: null,\n      selectionList: [],\n      user_ids: \"\",\n      selectedData: [],\n      timeVal: [],\n      array_ids: [],\n      groupList: [],\n      levelList: [],\n      labelFrom: {\n        page: 1,\n        limit: \"\"\n      },\n      labelLists: [],\n      display: \"none\",\n      checkBox: false,\n      selectionCopy: [],\n      isCheckBox: false,\n      isAll: 0,\n      userId: 0,\n      checkUidList: [],\n      batchModal: false,\n      menuActive: 1,\n      batchLabel: [],\n      batchData: {\n        group_id: 0,\n        label_id: [],\n        level_id: 0,\n        money_status: 0,\n        money: 0,\n        integration_status: 0,\n        integration: 0,\n        days_status: 1,\n        day: 0,\n        spread_uid: ''\n      },\n      spread_name: ''\n    };\n  },\n  watch: {\n    selectionList: function selectionList(value) {\n      var arr = value.map(function (item) {\n        return item.uid;\n      });\n      this.array_ids = arr;\n      this.user_ids = arr.join();\n    },\n    userLists: {\n      deep: true,\n      handler: function handler(value) {\n        var _this = this;\n\n        value.forEach(function (item) {\n          _this.selectionList.forEach(function (itm) {\n            if (itm.uid === item.uid) {\n              item.checkBox = true;\n            }\n          });\n        });\n        var arr = this.userLists.filter(function (item) {\n          return item.checkBox;\n        });\n\n        if (this.userLists.length) {\n          this.checkBox = this.userLists.length === arr.length;\n        } else {\n          this.checkBox = false;\n        }\n      }\n    }\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 100;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getList();\n  },\n  mounted: function mounted() {\n    this.userGroup();\n    this.levelLists();\n    this.groupLists();\n  },\n  methods: {\n    checkboxItem: function checkboxItem(e) {\n      var uid = parseInt(e.rowid);\n      var index = this.checkUidList.indexOf(uid);\n\n      if (index !== -1) {\n        this.checkUidList = this.checkUidList.filter(function (item) {\n          return item !== uid;\n        });\n      } else {\n        this.checkUidList.push(uid);\n      }\n    },\n    checkboxAll: function checkboxAll() {\n      // 获取选中当前值\n      var obj2 = this.$refs.xTable.getCheckboxRecords(true); // 获取之前选中值\n\n      var obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\n      if (this.isAll == 0 && this.checkUidList.length <= obj.length && !this.isCheckBox) {\n        obj = [];\n      }\n\n      obj = obj.concat(obj2);\n      var uids = [];\n      obj.forEach(function (item) {\n        uids.push(parseInt(item.uid));\n      });\n      this.checkUidList = uids;\n\n      if (!obj2.length) {\n        this.isCheckBox = false;\n      }\n    },\n    allPages: function allPages(e) {\n      this.isAll = e;\n\n      if (e == 0) {\n        this.$refs.xTable.toggleAllCheckboxRow(); // this.checkboxAll();\n      } else {\n        if (!this.isCheckBox) {\n          this.$refs.xTable.setAllCheckboxRow(true);\n          this.isCheckBox = true;\n          this.isAll = 1;\n        } else {\n          this.$refs.xTable.setAllCheckboxRow(false);\n          this.isCheckBox = false;\n          this.isAll = 0;\n        }\n\n        this.checkUidList = [];\n      }\n    },\n    closeLabel: function closeLabel(label) {\n      var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.dataLabel.splice(index, 1);\n    },\n    activeData: function activeData(dataLabel) {\n      this.labelListShow = false;\n\n      if (this.batchModal && this.menuActive === 2) {\n        this.batchLabel = dataLabel;\n        this.batchData.label_id = dataLabel.map(function (item) {\n          return item.id;\n        });\n      } else {\n        this.dataLabel = dataLabel;\n      }\n    },\n    openLabelList: function openLabelList(row) {\n      this.labelListShow = true;\n      var data = JSON.parse(JSON.stringify(this.dataLabel));\n\n      if (this.batchModal && this.menuActive === 2) {\n        data = JSON.parse(JSON.stringify(this.batchLabel));\n      }\n\n      this.$refs.labelList.userLabel(data);\n    },\n    // 标签弹窗关闭\n    labelListClose: function labelListClose() {\n      this.labelListShow = false;\n    },\n    // 标签弹窗关闭\n    labelClose: function labelClose(e) {\n      if (!e) {\n        this.getList();\n      }\n\n      this.labelShow = false;\n      this.labelActive.uid = 0;\n    },\n    // 提交\n    putSend: function putSend(name) {\n      var _this2 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (!_this2.formInline.spread_uid) {\n            return _this2.$Message.error(\"请上传用户\");\n          }\n\n          agentSpreadApi(_this2.formInline).then(function (res) {\n            _this2.promoterShow = false;\n\n            _this2.$Message.success(res.msg);\n\n            _this2.getList();\n\n            _this2.$refs[name].resetFields();\n          }).catch(function (res) {\n            _this2.$Message.error(res.msg);\n          });\n        }\n      });\n    },\n    save: function save() {\n      var _this3 = this;\n\n      this.$modalForm(getUserSaveForm()).then(function () {\n        return _this3.getList();\n      }); // getUserSaveForm().then(async (res) => {\n      // \tif(res.data.status === false){\n      // \t\treturn this.$authLapse(res.data);\n      // \t}\n      // \tthis.FromData = res.data;\n      // \tthis.$refs.edits.modals = true;\n      // }).catch(err=>{\n      // \tthis.$Message.error(err.msg);\n      // })\n    },\n    synchro: function synchro() {\n      var _this4 = this;\n\n      userSynchro().then(function (res) {\n        _this4.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    },\n    // 分组列表\n    groupLists: function groupLists() {\n      var _this5 = this;\n\n      this.loading = true;\n      userLabelApi(this.labelFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this5.labelLists = data.list;\n\n                case 2:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this5.loading = false;\n\n        _this5.$Message.error(res.msg);\n      });\n    },\n    onClickTab: function onClickTab(type) {\n      this.isAll = 0;\n      this.isCheckBox = false;\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = [];\n      this.userFrom.page = 1;\n      this.userFrom.user_type = type == -1 ? '' : type;\n      this.getList();\n    },\n    userGroup: function userGroup() {\n      var _this6 = this;\n\n      var data = {\n        page: 1,\n        limit: \"\"\n      };\n      userGroupApi(data).then(function (res) {\n        _this6.groupList = res.data.list;\n      });\n    },\n    levelLists: function levelLists() {\n      var _this7 = this;\n\n      var data = {\n        page: 1,\n        limit: \"\",\n        title: \"\",\n        is_show: 1\n      };\n      levelListApi(data).then(function (res) {\n        _this7.levelList = res.data.list;\n      });\n    },\n    // 批量设置分组；\n    setGroup: function setGroup() {\n      var _this8 = this;\n\n      if (this.selectionList.length === 0) {\n        this.$Message.warning(\"请选择要设置分组的用户\");\n      } else {\n        var uids = {\n          all: this.isAll,\n          uids: this.array_ids\n        };\n\n        if (this.isAll == 1) {\n          uids.where = this.userFrom;\n          uids.where = {\n            city: this.userFrom.city,\n            country: this.userFrom.country,\n            field_key: this.userFrom.field_key,\n            group_id: this.userFrom.group_id,\n            isMember: this.userFrom.isMember,\n            is_promoter: this.userFrom.is_promoter,\n            label_id: this.userFrom.label_id,\n            level: this.userFrom.level,\n            nickname: this.userFrom.nickname,\n            pay_count: this.userFrom.pay_count,\n            province: this.userFrom.province,\n            sex: this.userFrom.sex,\n            status: this.userFrom.status,\n            user_time: this.userFrom.user_time,\n            user_time_type: this.userFrom.user_time_type,\n            user_type: this.userFrom.user_type\n          };\n        }\n\n        this.$modalForm(userSetGroup(uids)).then(function () {\n          return _this8.getList();\n        });\n      }\n    },\n    // 批量设置标签；\n    setLabel: function setLabel() {\n      if (this.selectionList.length === 0) {\n        this.$Message.warning(\"请选择要设置标签的用户\");\n      } else {\n        var uids = {\n          all: this.isAll,\n          uids: this.array_ids\n        };\n\n        if (this.isAll == 1) {\n          uids.where = {\n            city: this.userFrom.city,\n            country: this.userFrom.country,\n            field_key: this.userFrom.field_key,\n            group_id: this.userFrom.group_id,\n            isMember: this.userFrom.isMember,\n            is_promoter: this.userFrom.is_promoter,\n            label_id: this.userFrom.label_id,\n            level: this.userFrom.level,\n            nickname: this.userFrom.nickname,\n            pay_count: this.userFrom.pay_count,\n            province: this.userFrom.province,\n            sex: this.userFrom.sex,\n            status: this.userFrom.status,\n            user_time: this.userFrom.user_time,\n            user_time_type: this.userFrom.user_time_type,\n            user_type: this.userFrom.user_type\n          };\n        }\n\n        this.labelShow = true;\n        this.labelActive.uid = uids; // this.$modalForm(userSetLabelApi(uids)).then(() => this.getList());\n      }\n    },\n    // 是否为付费会员；\n    changeMember: function changeMember() {\n      this.userFrom.page = 1;\n      this.getList();\n    },\n    // 选择国家\n    changeCountry: function changeCountry() {\n      if (this.userFrom.country === \"abroad\" || !this.userFrom.country) {\n        this.selectedData = [];\n        this.userFrom.province = \"\";\n        this.userFrom.city = \"\";\n        this.address = [];\n      }\n\n      this.userSearchs();\n    },\n    // 选择地址\n    handleChange: function handleChange(value, selectedData) {\n      this.selectedData = selectedData.map(function (o) {\n        return o.label;\n      });\n      this.userFrom.province = this.selectedData[0];\n      this.userFrom.city = this.selectedData[1];\n      this.userSearchs();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.userFrom.user_time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.userSearchs();\n    },\n    // 操作\n    changeMenu: function changeMenu(row, name, index) {\n      var _this9 = this;\n\n      this.userId = row.uid;\n      var uid = [];\n      uid.push(row.uid);\n      var uids = {\n        uids: uid\n      };\n\n      switch (name) {\n        case \"1\":\n          this.$refs.userDetails.modals = true;\n          this.$refs.userDetails.activeName = \"info\";\n          this.$refs.userDetails.getDetails(row.uid);\n          break;\n\n        case \"2\":\n          this.getOtherFrom(row.uid);\n          break;\n\n        case \"3\":\n          // this.giveLevel(row.uid);\n          this.giveLevelTime(row.uid);\n          break;\n\n        case \"4\":\n          this.del(row, \"清除 【 \" + row.nickname + \" 】的会员等级\", index, \"user\");\n          break;\n\n        case \"5\":\n          this.$modalForm(userSetGroup(uids)).then(function () {\n            return _this9.$refs.sends.getList();\n          });\n          break;\n\n        case \"6\":\n          this.openLabel(row); // this.$modalForm(userSetLabelApi(uids)).then(() => this.$refs.sends.getList());\n\n          break;\n\n        case \"7\":\n          this.editS(row);\n          break;\n\n        default:\n          this.del(row, \"解除【 \" + row.nickname + \" 】的上级推广人\", index, \"tuiguang\");\n          break;\n        // this.del(row, '清除 【 ' + row.nickname + ' 】的会员等级', index)\n      }\n    },\n    openLabel: function openLabel(row) {\n      this.labelShow = true;\n      this.labelActive.uid = row.uid;\n    },\n    editS: function editS(row) {\n      this.promoterShow = true;\n      this.formInline.uid = row.uid;\n    },\n    customer: function customer() {\n      this.customerShow = true;\n    },\n    imageObject: function imageObject(e) {\n      this.customerShow = false;\n\n      if (this.batchModal && this.menuActive === 6) {\n        this.batchData.spread_uid = e.uid;\n        this.spread_name = e.name;\n      } else {\n        this.formInline.spread_uid = e.uid;\n        this.formInline.image = e.image;\n      }\n    },\n    cancel: function cancel(name) {\n      this.promoterShow = false;\n      this.$refs[name].resetFields();\n    },\n    // 赠送会员等级\n    giveLevel: function giveLevel(id) {\n      var _this10 = this;\n\n      giveLevelApi(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context2.next = 2;\n                    break;\n                  }\n\n                  return _context2.abrupt(\"return\", _this10.$authLapse(res.data));\n\n                case 2:\n                  _this10.FromData = res.data;\n                  _this10.$refs.edits.modals = true;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this10.$Message.error(res.msg);\n      });\n    },\n    // 赠送会员等级\n    giveLevelTime: function giveLevelTime(id) {\n      var _this11 = this;\n\n      giveLevelTimeApi(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref3 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee3(res) {\n          return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n            while (1) {\n              switch (_context3.prev = _context3.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context3.next = 2;\n                    break;\n                  }\n\n                  return _context3.abrupt(\"return\", _this11.$authLapse(res.data));\n\n                case 2:\n                  _this11.FromData = res.data;\n                  _this11.$refs.edits.modals = true;\n\n                case 4:\n                case \"end\":\n                  return _context3.stop();\n              }\n            }\n          }, _callee3);\n        }));\n\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this11.$Message.error(res.msg);\n      });\n    },\n    // 删除\n    del: function del(row, tit, num, name) {\n      var _this12 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: name === \"user\" ? \"user/del_level/\".concat(row.uid) : \"agent/stair/delete_spread/\".concat(row.uid),\n        method: name === \"user\" ? \"DELETE\" : \"PUT\",\n        // url: `user/del_level/${row.uid}`,\n        // method: 'DELETE',\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this12.$Message.success(res.msg);\n\n        _this12.getList();\n      }).catch(function (res) {\n        _this12.$Message.error(res.msg);\n      });\n    },\n    // 清除会员删除成功\n    submitModel: function submitModel() {\n      this.getList();\n    },\n    // 会员列表\n    getList: function getList() {\n      var _this13 = this;\n\n      this.loading = true;\n      var activeIds = [];\n      this.dataLabel.forEach(function (item) {\n        activeIds.push(item.id);\n      });\n      this.userFrom.label_id = activeIds.join(\",\") || \"\";\n      this.userFrom.user_type = this.userFrom.user_type || \"\";\n      this.userFrom.status = this.userFrom.status || \"\";\n      this.userFrom.sex = this.userFrom.sex || \"\";\n      this.userFrom.is_promoter = this.userFrom.is_promoter || \"\";\n      this.userFrom.country = this.userFrom.country || \"\";\n      this.userFrom.user_time_type = this.userFrom.user_time_type || \"\";\n      this.userFrom.pay_count = this.userFrom.pay_count || \"\"; // this.userFrom.label_id = this.userFrom.label_id || \"\";\n\n      this.userFrom.field_key = this.field_key === \"all\" ? \"\" : this.field_key;\n      this.userFrom.level = this.userFrom.level === \"all\" ? \"\" : this.userFrom.level;\n      this.userFrom.group_id = this.userFrom.group_id === \"all\" ? \"\" : this.userFrom.group_id;\n      userList(this.userFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref4 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee4(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n            while (1) {\n              switch (_context4.prev = _context4.next) {\n                case 0:\n                  data = res.data;\n                  data.list.forEach(function (item) {\n                    item.checkBox = false;\n                  });\n                  _this13.userLists = data.list;\n                  _this13.total = data.count;\n                  _this13.loading = false;\n\n                  _this13.$nextTick(function () {\n                    if (this.isAll == 1) {\n                      this.selectionList = this.userLists;\n\n                      if (this.isCheckBox) {\n                        this.$refs.xTable.setAllCheckboxRow(true);\n                      } else {\n                        this.$refs.xTable.setAllCheckboxRow(false);\n                      }\n                    } else {\n                      var obj = this.$refs.xTable.getCheckboxReserveRecords(true);\n\n                      if (!this.checkUidList.length || this.checkUidList.length <= obj.length) {\n                        this.$refs.xTable.setAllCheckboxRow(false);\n                      }\n                    }\n                  });\n\n                case 6:\n                case \"end\":\n                  return _context4.stop();\n              }\n            }\n          }, _callee4);\n        }));\n\n        return function (_x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this13.loading = false;\n\n        _this13.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(_ref5) {\n      var currentPage = _ref5.currentPage,\n          pageSize = _ref5.pageSize;\n      this.userFrom.page = currentPage;\n      this.userFrom.limit = pageSize;\n      this.getList();\n    },\n    // pageChange(index) {\n    //   this.userFrom.page = index;\n    //   this.getList();\n    // },\n    // 搜索\n    userSearchs: function userSearchs() {\n      if (this.userFrom.user_time_type && !this.timeVal.length) {\n        return this.$Message.error(\"请选择访问时间\");\n      }\n\n      if (this.timeVal.length && !this.userFrom.user_time_type) {\n        return this.$Message.error(\"请选择访问情况\");\n      }\n\n      this.isAll = 0;\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = [];\n      this.userFrom.page = 1;\n      this.selectionList = [];\n      this.getList();\n    },\n    // 重置\n    reset: function reset(name) {\n      this.$refs.xTable.setAllCheckboxRow(false);\n      this.checkUidList = [];\n      this.headeType = \"-1\";\n      this.userFrom = {\n        user_type: \"\",\n        status: \"\",\n        sex: \"\",\n        is_promoter: \"\",\n        country: \"\",\n        pay_count: \"\",\n        user_time_type: \"\",\n        user_time: \"\",\n        nickname: \"\",\n        field_key: \"\",\n        level: \"\",\n        group_id: \"\",\n        label_id: \"\",\n        page: 1,\n        // 当前页\n        limit: 20 // 每页显示条数\n\n      };\n      this.field_key = \"\";\n      this.level = \"\";\n      this.group_id = \"\";\n      this.label_id = \"\";\n      this.user_time_type = \"\";\n      this.pay_count = \"\";\n      this.timeVal = [];\n      this.selectionList = [];\n      this.dataLabel = [];\n      this.getList();\n    },\n    // 获取编辑表单数据\n    getUserFrom: function getUserFrom(id) {\n      var _this14 = this;\n\n      this.$modalForm(getUserData(id)).then(function () {\n        return _this14.getList();\n      }); // getUserData(id)\n      //   .then(async (res) => {\n      //     if (res.data.status === false) {\n      //       return this.$authLapse(res.data);\n      //     }\n      //     this.FromData = res.data;\n      //     this.$refs.edits.modals = true;\n      //   })\n      //   .catch((res) => {\n      //     this.$Message.error(res.msg);\n      //   });\n    },\n    // 获取积分余额表单\n    getOtherFrom: function getOtherFrom(id) {\n      var _this15 = this;\n\n      editOtherApi(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref6 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee5(res) {\n          return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n            while (1) {\n              switch (_context5.prev = _context5.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context5.next = 2;\n                    break;\n                  }\n\n                  return _context5.abrupt(\"return\", _this15.$authLapse(res.data));\n\n                case 2:\n                  res.data.rules[1].props.max = 999999;\n                  _this15.FromData = res.data;\n                  _this15.$refs.edits.modals = true;\n\n                case 5:\n                case \"end\":\n                  return _context5.stop();\n              }\n            }\n          }, _callee5);\n        }));\n\n        return function (_x5) {\n          return _ref6.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this15.$Message.error(res.msg);\n      });\n    },\n    // 修改状态\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this16 = this;\n\n      var data = {\n        id: row.uid,\n        status: row.status\n      };\n      isShowApi(data).then(\n      /*#__PURE__*/\n      function () {\n        var _ref7 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee6(res) {\n          return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n            while (1) {\n              switch (_context6.prev = _context6.next) {\n                case 0:\n                  _this16.$Message.success(res.msg);\n\n                case 1:\n                case \"end\":\n                  return _context6.stop();\n              }\n            }\n          }, _callee6);\n        }));\n\n        return function (_x6) {\n          return _ref7.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this16.$Message.error(res.msg);\n      });\n    },\n    // 点击发送优惠券\n    onSend: function onSend() {\n      if (this.checkUidList.length === 0 && this.isAll == 0) {\n        return this.$Message.warning(\"请选择要发送优惠券的用户\");\n      }\n\n      this.$refs.sends.modals = true;\n      this.$refs.sends.getList();\n    },\n    // 发送图文消息\n    onSendPic: function onSendPic() {\n      if (this.checkUidList.length === 0 && this.isAll == 0) {\n        this.$Message.warning(\"请选择要发送图文消息的用户\");\n      } else {\n        this.modal13 = true;\n      }\n    },\n    // 编辑\n    edit: function edit(row) {\n      this.getUserFrom(row.uid); // this.$modalForm(getUserSaveForm(row.uid)).then(() => this.getList());\n    },\n    //信息补充\n    extendInfo: function extendInfo(row) {\n      var _this17 = this;\n\n      // this.$modalForm(extendInfo(row.uid)).then(() => this.getList());\n      _extendInfo(row.uid).then(\n      /*#__PURE__*/\n      function () {\n        var _ref8 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee7(res) {\n          return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n            while (1) {\n              switch (_context7.prev = _context7.next) {\n                case 0:\n                  if (!(res.data.status === false)) {\n                    _context7.next = 2;\n                    break;\n                  }\n\n                  return _context7.abrupt(\"return\", _this17.$authLapse(res.data));\n\n                case 2:\n                  _this17.FromData = res.data;\n                  _this17.$refs.edits.modals = true; // this.getList()\n\n                case 4:\n                case \"end\":\n                  return _context7.stop();\n              }\n            }\n          }, _callee7);\n        }));\n\n        return function (_x7) {\n          return _ref8.apply(this, arguments);\n        };\n      }()).catch(function (err) {\n        _this17.$Message.error(err.msg);\n      });\n    },\n    // 修改成功\n    submitFail: function submitFail(p) {\n      // this.getList();\n      if (this.$refs.userDetails.modals) {\n        this.$refs.userDetails.getDetails(this.userId);\n      }\n    },\n    // 排序\n    // sortChanged(e) {\n    //   this.userFrom[e.key] = e.order;\n    //   this.getList();\n    // },\n    // onSelectCancel(selection, row) {},\n    menuSelect: function menuSelect(name) {\n      this.menuActive = name;\n    },\n    setBatch: function setBatch() {\n      this.batchModal = true;\n    },\n    tagClose: function tagClose(id) {\n      var index = this.batchLabel.findIndex(function (item) {\n        return item.id === id;\n      });\n      this.batchLabel.splice(index, 1);\n    },\n    cancelBatch: function cancelBatch() {\n      this.batchModal = false;\n    },\n    // 保存批量操作\n    saveBatch: function saveBatch() {\n      var _this18 = this;\n\n      batchProcess({\n        type: this.menuActive,\n        uids: this.checkUidList,\n        all: this.isAll,\n        where: this.userFrom,\n        data: this.batchData\n      }).then(function (res) {\n        _this18.$Message.success(res.msg);\n\n        _this18.batchModal = false;\n      }).catch(function (res) {\n        _this18.$Message.error(res.msg);\n      });\n    },\n    batchVisibleChange: function batchVisibleChange() {\n      this.batchData = {\n        group_id: 0,\n        label_id: [],\n        level_id: 0,\n        money_status: 0,\n        money: 0,\n        integration_status: 0,\n        integration: 0,\n        days_status: 1,\n        day: 0,\n        spread_uid: ''\n      };\n      this.batchLabel = [];\n      this.spread_name = '';\n      this.menuActive = 1;\n    }\n  }\n};", null]}