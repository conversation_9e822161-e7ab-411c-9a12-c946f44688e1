{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\list.vue?vue&type=template&id=b0eb2eda&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\list.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n    <div class=\"i-layout-page-header\">\n        <!-- <PageHeader class=\"product_tabs\" :title=\"$route.meta.title\" hidden-breadcrumb></PageHeader> -->\n        <div class=\"i-layout-page-header\">\n            <PageHeader class=\"product_tabs\" hidden-breadcrumb>\n                <div slot=\"title\">\n                    <router-link :to=\"{path:`${roterPre}/vipuser/grade/card`}\">\n                        <!-- <Button icon=\"ios-arrow-back\" size=\"small\"  class=\"mr20\">返回</Button> -->\n                        <div class=\"font-sm after-line\">\n                            <span class=\"iconfont iconfanhui\"></span>\n                            <span class=\"pl10\">返回</span>\n                        </div>\n                    </router-link>\n                    <span v-text=\"$route.meta.title\" class=\"mr20 ml16\"></span>\n                </div>\n            </PageHeader>\n        </div>\n    </div>\n    \n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n        <div class=\"new_card_pd\">\n        <Form\n            ref=\"formData\"\n            :model=\"table\"\n            :label-width=\"labelWidth\"\n            :label-position=\"labelPosition\"\n            inline\n            @submit.native.prevent\n        >\n            <FormItem label=\"是否领取：\" style=\"width:200px;\">\n                <Select clearable v-model=\"table.is_use\">\n                    <Option value=\"1\">已领取</Option>\n                    <Option value=\"0\">未领取</Option>\n                </Select>\n            </FormItem>\n            <FormItem label=\"卡号：\" >\n                <Input v-model=\"table.card_number\" placeholder=\"请输入卡号\"  class=\"input-add\"/>\n            </FormItem>\n            <FormItem label=\"手机号：\">\n                <Input v-model=\"table.phone\" placeholder=\"请输入手机号\"  class=\"input-add mr14\"/>\n                <Button type=\"primary\" @click=\"formSubmit\">搜索</Button>\n            </FormItem>\n        </Form>\n        </div>\n    </Card>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n        <Table\n            :columns=\"columns1\"\n            :data=\"data1\"\n            ref=\"table\"\n            class=\"mt25\"\n            :loading=\"loading\"\n            highlight-row\n            no-userFrom-text=\"暂无数据\"\n            no-filtered-userFrom-text=\"暂无筛选结果\"\n        >\n            <template slot-scope=\"{ row, index }\" slot=\"status\">\n                <i-switch\n                    v-model=\"row.status\"\n                    :value=\"row.status\"\n                    :true-value=\"1\"\n                    :false-value=\"0\"\n                    @on-change=\"onchangeIsShow(row)\"\n                    size=\"large\"\n                >\n                    <span slot=\"open\">激活</span>\n                    <span slot=\"close\">冻结</span>\n                </i-switch>\n            </template>\n        </Table>\n        <div class=\"acea-row row-right page\">\n            <Page\n                :total=\"total\"\n                :current=\"table.page\"\n                :page-size=\"table.limit\"\n                show-elevator\n                show-total\n                @on-change=\"pageChange\"\n            />\n        </div>\n    </Card>\n</div>\n", null]}