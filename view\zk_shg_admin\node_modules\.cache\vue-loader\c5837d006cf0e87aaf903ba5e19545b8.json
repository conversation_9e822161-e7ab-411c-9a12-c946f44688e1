{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addReply.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addReply.vue", "mtime": 1690874758000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { saveFictitiousReply } from '@/api/product';\nexport default {\n  props: {\n    visible: {\n      type: Boolean,\n      default: false,\n    },\n    goods: {\n      type: Object,\n      default() {\n        return {};\n      },\n    },\n    attr: {\n      type: Object,\n      default() {\n        return {};\n      },\n    },\n    avatar: {\n      type: Object,\n      default() {\n        return {};\n      },\n    },\n    picture: {\n      type: Array,\n      default() {\n        return [];\n      },\n    },\n  },\n  data() {\n    return {\n      formData: {\n        avatar: '',\n        nickname: '',\n        comment: '',\n      },\n      product_score: 0,\n      service_score: 0,\n      pics: [],\n      add_time: '',\n    };\n  },\n  watch: {\n    picture(value) {\n      this.pics = value.map((item) => {\n        return item.att_dir;\n      });\n    },\n    visible(value) {\n      if (!value) {\n        this.formData.nickname = '';\n        this.formData.comment = '';\n        this.product_score = 0;\n        this.service_score = 0;\n        this.add_time = '';\n      }\n    },\n  },\n  methods: {\n    removeUser() {\n      this.avatar.att_dir = '';\n    },\n    removePicture(att_id) {\n      this.$emit('removePicture', att_id);\n    },\n    onChange(date) {\n      this.add_time = date;\n    },\n    callGoods() {\n      this.$emit('callGoods');\n    },\n    callAttr() {\n      this.$emit('callAttr');\n    },\n    callPicture(type) {\n      this.$emit('callPicture', type);\n    },\n    onOk() {\n      if (!this.goods.id) {\n        return this.$Message.error('请选择商品');\n      }\n      if (!this.attr.unique) {\n        return this.$Message.error('请选择商品规格');\n      }\n      if (!this.avatar.att_dir) {\n        return this.$Message.error('请选择用户头像');\n      }\n      if (!this.formData.nickname) {\n        return this.$Message.error('请填写用户昵称');\n      }\n      if (!this.formData.comment) {\n        return this.$Message.error('请填写评论内容');\n      }\n      if (!this.product_score) {\n        return this.$Message.error('商品分数必须是1-5之间的整数');\n      }\n      if (!this.service_score) {\n        return this.$Message.error('服务分数必须是1-5之间的整数');\n      }\n      let data = {\n        image: {\n          image: this.goods.image,\n          product_id: this.goods.id,\n        },\n        unique: this.attr.unique,\n        avatar: this.avatar.att_dir,\n        nickname: this.formData.nickname,\n        comment: this.formData.comment,\n        product_score: this.product_score,\n        service_score: this.service_score,\n        pics: this.pics,\n        add_time: this.add_time,\n      };\n      saveFictitiousReply(data)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.$emit('update:visible', false);\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    onCancel() {\n      this.$emit('update:visible', false);\n    },\n  },\n};\n", null]}