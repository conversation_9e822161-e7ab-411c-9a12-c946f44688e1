{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\lottery\\addGoods.vue?vue&type=style&index=0&id=5b37cc49&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\lottery\\addGoods.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.pictrueBox {\n  display: inline-block;\n}\n\n.pictrue {\n  width: 60px;\n  height: 60px;\n  border: 1px dotted rgba(0, 0, 0, 0.1);\n  margin-right: 15px;\n  display: inline-block;\n  position: relative;\n  cursor: pointer;\n\n  img {\n    width: 100%;\n    height: 100%;\n  }\n\n  .btndel {\n    position: absolute;\n    z-index: 1;\n    width: 20px !important;\n    height: 20px !important;\n    left: 46px;\n    top: -4px;\n  }\n}\n\n.upload-list {\n  width: 58px;\n  height: 58px;\n  line-height: 58px;\n  border: 1px dotted rgba(0, 0, 0, 0.1);\n  border-radius: 4px;\n  background: rgba(0, 0, 0, 0.02);\n  cursor: pointer;\n  position: relative;\n}\n\n.upload-list img {\n  display: block;\n  width: 100%;\n  height: 100%;\n}\n\n.upLoad {\n  width: 58px;\n  height: 58px;\n  line-height: 58px;\n  border: 1px dotted rgba(0, 0, 0, 0.1);\n  border-radius: 4px;\n  background: rgba(0, 0, 0, 0.02);\n  cursor: pointer;\n}\n\n.ivu-icon-ios-close-circle {\n  position: absolute;\n  top: 0;\n  right: 0;\n  transform: translate(50%, -50%);\n}\n\n.grey {\n  color: #999;\n}\n\n.product-data {\n  display: flex;\n  align-items: center;\n\n  .image {\n    width: 50px !important;\n    height: 50px !important;\n    margin-right: 10px;\n  }\n}\n", null]}