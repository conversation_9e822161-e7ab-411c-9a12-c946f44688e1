{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { releasedListApi, releasedissueLogApi, releaseStatusApi, delCouponReleased, couponStatusApi } from \"@/api/marketing\";\nimport { formatDate as _formatDate } from \"@/utils/validate\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"storeCouponIssue\",\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    }\n  },\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      modals2: false,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns1: [{\n        title: \"ID\",\n        key: \"id\",\n        width: 80\n      }, {\n        title: \"优惠券名称\",\n        slot: \"coupon_title\",\n        minWidth: 150 // render (h, data) {\n        //     let row = data.row, content = '';\n        //     if (row.is_give_subscribe) {\n        //         content = '关注';\n        //     } else if (row.is_full_give) {\n        //         content = '满赠';\n        //     } else {\n        //         content = '普通'\n        //     }\n        //     return h('div', [\n        //         h('Tag', { attrs: {\n        //             color: 'blue'\n        //         } }, content),\n        //         h('span', data.row.coupon_title)\n        //     ]);\n        // }\n\n      }, {\n        title: \"优惠券类型\",\n        slot: \"coupon_type\",\n        minWidth: 80\n      }, {\n        title: \"适用类型\",\n        slot: \"type\",\n        minWidth: 80\n      }, {\n        title: \"面值\",\n        slot: \"coupon_price\",\n        minWidth: 100\n      }, {\n        title: \"领取方式\",\n        slot: \"receive_type\",\n        minWidth: 100\n      }, {\n        title: \"优惠券种类\",\n        key: \"category\",\n        minWidth: 100,\n        render: function render(h, params) {\n          return h('div', params.row.category == 1 ? '普通券' : '会员券');\n        }\n      }, {\n        title: \"领取时间\",\n        slot: \"start_time\",\n        minWidth: 250\n      }, {\n        title: \"使用时间\",\n        slot: \"start_use_time\",\n        minWidth: 250\n      }, {\n        title: \"发布数量\",\n        slot: \"count\",\n        minWidth: 90\n      }, {\n        title: \"是否开启\",\n        slot: \"status\",\n        minWidth: 90\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        fixed: \"right\",\n        minWidth: 200\n      }],\n      tableFrom: {\n        status: \"\",\n        coupon_title: \"\",\n        receive_type: \"\",\n        coupon_type: \"\",\n        page: 1,\n        limit: 15\n      },\n      receive_type: \"\",\n      tableList: [],\n      total: 0,\n      FromData: null,\n      receiveList: [],\n      loading2: false,\n      columns2: [{\n        title: \"ID\",\n        key: \"uid\",\n        minWidth: 80\n      }, {\n        title: \"用户名\",\n        key: \"nickname\",\n        minWidth: 150\n      }, {\n        title: \"用户头像\",\n        slot: \"avatar\",\n        minWidth: 100\n      }, {\n        title: \"领取时间\",\n        key: \"add_time\",\n        minWidth: 140\n      }],\n      total2: 0,\n      receiveFrom: {\n        page: 1,\n        limit: 15\n      },\n      rows: {}\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  methods: {\n    // 失效\n    couponInvalid: function couponInvalid(row, tit, num) {\n      this.delfromData = {\n        title: tit,\n        num: num,\n        url: \"marketing/coupon/status/\".concat(row.id),\n        method: \"PUT\",\n        ids: \"\"\n      };\n      this.$refs.modelSure.modals = true;\n    },\n    // 领取记录\n    receive: function receive(row) {\n      this.modals2 = true;\n      this.rows = row;\n      this.getReceivelist(row);\n    },\n    getReceivelist: function getReceivelist(row) {\n      var _this = this;\n\n      this.loading2 = true;\n      releasedissueLogApi(row.id, this.receiveFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this.receiveList = data.list;\n                  _this.total2 = res.data.count;\n                  _this.loading2 = false;\n\n                case 4:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.loading2 = false;\n\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 领取记录改变分页\n    receivePageChange: function receivePageChange(index) {\n      this.receiveFrom.page = index;\n      this.getReceivelist(this.rows);\n    },\n    // 删除\n    couponDel: function couponDel(row, tit, num) {\n      var _this2 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"marketing/coupon/released/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this2.$Message.success(res.msg);\n\n        _this2.tableList.splice(num, 1);\n\n        if (!_this2.tableList.length) {\n          _this2.tableFrom.page = _this2.tableFrom.page == 1 ? 1 : _this2.tableFrom.page - 1;\n        }\n\n        _this2.getList();\n      }).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this3 = this;\n\n      this.loading = true;\n      this.tableFrom.receive_type = this.receive_type === \"all\" ? \"\" : this.receive_type;\n      this.tableFrom.status = this.tableFrom.status || \"\";\n      releasedListApi(this.tableFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  data = res.data;\n                  _this3.tableList = data.list;\n                  _this3.total = res.data.count;\n                  _this3.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this3.loading = false;\n\n        _this3.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 搜索()\n    orderSearch: function orderSearch() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 添加优惠券\n    add: function add() {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_coupon_issue/create\"\n      });\n    },\n    // 复制\n    copy: function copy(data) {\n      this.$router.push({\n        path: \"\".concat(this.roterPre, \"/marketing/store_coupon_issue/create/\").concat(data.id)\n      });\n    },\n    // 是否开启\n    openChange: function openChange(data) {\n      var _this4 = this;\n\n      couponStatusApi(data).then(function () {\n        return _this4.getList();\n      });\n    }\n  }\n};", null]}