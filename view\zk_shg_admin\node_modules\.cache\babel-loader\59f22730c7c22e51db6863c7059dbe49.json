{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue", "mtime": 1734512585792}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex'; // import theme from \"@/mixins/theme\";\n\nexport default {\n  name: 'home_card_2',\n  cname: '卡片二',\n  configName: 'c_home_card_2',\n  icon: '#iconzujian-shangpinxuanxiangka',\n  type: 0,\n  // 0 基础组件 1 营销组件 2工具组件\n  defaultName: 'productCard2',\n  // 外面匹配名称\n  props: {\n    index: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    colorStyle: {\n      type: null\n    }\n  },\n  computed: _objectSpread({}, mapState('admin/mobildConfig', ['defaultArray'])),\n  watch: {\n    pageData: {\n      handler: function handler(nVal, oVal) {\n        this.setConfig(nVal);\n      },\n      deep: true\n    },\n    num: {\n      handler: function handler(nVal, oVal) {\n        var data = this.$store.state.admin.mobildConfig.defaultArray[nVal];\n        this.setConfig(data);\n      },\n      deep: true\n    },\n    'defaultArray': {\n      handler: function handler(nVal, oVal) {\n        var data = this.$store.state.admin.mobildConfig.defaultArray[this.num];\n        this.setConfig(data);\n      },\n      deep: true\n    }\n  },\n  // mixins: [theme],\n  data: function data() {\n    return {\n      // 默认初始化数据禁止修改\n      defaultConfig: {\n        cname: '测试',\n        name: 'productCard2',\n        timestamp: this.num,\n        isHide: false,\n        setUp: {\n          tabVal: 0\n        },\n        titleLeft: '展示设置',\n        titleTab: '选项卡设置',\n        titleRight: '选项卡样式',\n        titleCurrency: '通用样式',\n        titleCart: '购物车按钮',\n        styleConfig: {\n          title: '选择风格',\n          tabVal: 1,\n          tabList: [{\n            name: '样式一'\n          }, {\n            name: '样式二'\n          }, {\n            name: '样式三'\n          }, {\n            name: '样式四'\n          }, {\n            name: '样式五'\n          }]\n        },\n        slideConfig: {\n          title: '滑动置顶',\n          tabVal: 1,\n          tabList: [{\n            name: '启用'\n          }, {\n            name: '不启用'\n          }]\n        },\n        tabConfig: {\n          title: '点击下方选项卡可进行编辑；鼠标拖拽版块可调整顺序',\n          max: '',\n          tabCur: 0,\n          classList: [],\n          list: [{\n            chiild: [{\n              title: '标题',\n              val: '首发新品',\n              max: 4,\n              pla: '选填，不超过四个字'\n            }, {\n              title: '简介',\n              val: '最新出炉',\n              max: 4,\n              pla: '选填，不超过四个字'\n            }],\n            image: '',\n            tabVal: 1,\n            brandConfig: {\n              brandVal: []\n            },\n            selectConfig: {\n              activeValue: []\n            },\n            goodsLabel: {\n              activeValue: [],\n              list: []\n            },\n            goodsSort: 0,\n            numConfig: {\n              val: 6\n            },\n            goodsList: {\n              max: 20,\n              list: []\n            },\n            productList: {\n              list: []\n            }\n          }]\n        },\n        cartConfig: {\n          title: '是否显示',\n          tabVal: 0,\n          tabList: [{\n            name: '显示'\n          }, {\n            name: '隐藏'\n          }]\n        },\n        bntConfig: {\n          title: '按钮效果',\n          tabVal: 1,\n          tabList: [{\n            name: '进入商品详情页'\n          }, {\n            name: '商品加购'\n          }]\n        },\n        bntStyleConfig: {\n          typeFrom: 'bnt',\n          title: '按钮样式',\n          tabVal: 0\n        },\n        toneConfig: {\n          title: '色调',\n          tabVal: 0,\n          tabList: [{\n            name: '跟随主题风格'\n          }, {\n            name: '自定义'\n          }]\n        },\n        decorateColor: {\n          title: \"装饰元素\",\n          default: [{\n            item: \"#E93323\"\n          }, {\n            item: \"#FF7931\"\n          }],\n          color: [{\n            item: \"#E93323\"\n          }, {\n            item: \"#FF7931\"\n          }]\n        },\n        decorateColor2: {\n          title: \"装饰元素\",\n          default: [{\n            item: \"#E93323\"\n          }],\n          color: [{\n            item: \"#E93323\"\n          }]\n        },\n        textColor: {\n          title: \"选中文字\",\n          default: [{\n            item: \"#333333\"\n          }],\n          color: [{\n            item: \"#333333\"\n          }]\n        },\n        textColor2: {\n          title: \"选中文字\",\n          default: [{\n            item: \"#E93323\"\n          }],\n          color: [{\n            item: \"#E93323\"\n          }]\n        },\n        textColor3: {\n          title: \"选中文字\",\n          default: [{\n            item: \"#FFFFFF\"\n          }],\n          color: [{\n            item: \"#FFFFFF\"\n          }]\n        },\n        toneCartConfig: {\n          title: '色调',\n          tabVal: 0,\n          tabList: [{\n            name: '跟随主题风格'\n          }, {\n            name: '自定义'\n          }]\n        },\n        bntBgColor: {\n          title: '按钮颜色',\n          name: 'bntBgColor',\n          default: [{\n            item: '#E93323'\n          }, {\n            item: '#FF7931'\n          }],\n          color: [{\n            item: '#E93323'\n          }, {\n            item: '#FF7931'\n          }]\n        },\n        bottomBgColor: {\n          title: '底部背景',\n          default: [{\n            item: '#f5f5f5'\n          }],\n          color: [{\n            item: '#f5f5f5'\n          }]\n        },\n        topConfig: {\n          title: '上边距',\n          val: 0,\n          min: 0\n        },\n        bottomConfig: {\n          title: '下边距',\n          val: 0,\n          min: 0\n        },\n        prConfig: {\n          title: '左右边距',\n          val: 10,\n          min: 0\n        },\n        mbConfig: {\n          title: '页面间距',\n          val: 0,\n          min: 0\n        },\n        fillet: {\n          title: '背景圆角',\n          type: 0,\n          list: [{\n            val: \"全部\",\n            icon: \"iconcaozuo-zhengti\"\n          }, {\n            val: \"单个\",\n            icon: \"iconcaozuo-bianjiao\"\n          }],\n          valName: '圆角值',\n          val: 0,\n          min: 0,\n          valList: [{\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }]\n        }\n      },\n      navlist: [],\n      imgStyle: '',\n      tabCur: 0,\n      list: [],\n      pageData: {},\n      styleConfig: 0,\n      toneConfig: 0,\n      textColor: '',\n      textColor2: '',\n      textColor3: '',\n      decorateColor: '',\n      decorateColor2: '',\n      decorateColorLeft: '',\n      // bgColor:'',\n      bottomBgColor: '',\n      mTop: 0,\n      topConfig: 0,\n      bottomConfig: 0,\n      prConfig: 0,\n      bgRadius: 0,\n      bgRadius2: 0,\n      themeColor: '',\n      cartConfig: 0,\n      toneCartConfig: 0,\n      bntBgColor: '',\n      bntStyleConfig: 0\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.pageData = _this.$store.state.admin.mobildConfig.defaultArray[_this.num];\n\n      _this.setConfig(_this.pageData);\n    });\n  },\n  methods: {\n    setConfig: function setConfig(data) {\n      if (!data) return;\n\n      if (data.mbConfig) {\n        this.styleConfig = data.styleConfig.tabVal;\n        this.cartConfig = data.cartConfig.tabVal;\n        this.bntStyleConfig = data.bntStyleConfig.tabVal;\n        this.toneCartConfig = data.toneCartConfig.tabVal;\n        var bntBgColorLeft = data.bntBgColor.color[0].item;\n        var bntBgColorRight = data.bntBgColor.color[1].item;\n        this.bntBgColor = \"linear-gradient(90deg,\".concat(bntBgColorLeft, \" 0%,\").concat(bntBgColorRight, \" 100%)\");\n        this.toneConfig = data.toneConfig.tabVal;\n        this.textColor = data.textColor.color[0].item;\n        this.textColor2 = data.textColor2.color[0].item;\n        this.textColor3 = data.textColor3.color[0].item;\n        var decorateColorLeft = data.decorateColor.color[0].item;\n        var decorateColorRight = data.decorateColor.color[1].item;\n        this.decorateColorLeft = decorateColorLeft;\n        this.decorateColor = \"linear-gradient(90deg,\".concat(decorateColorLeft, \" 0%,\").concat(decorateColorRight, \" 100%)\");\n        this.decorateColor2 = data.decorateColor2.color[0].item;\n        this.themeColor = \"linear-gradient(90deg,\".concat(this.colorStyle.theme, \" 0%,\").concat(this.colorStyle.gradient, \" 100%)\"); // let bgColorLeft =  data.moduleColor.color[0].item;\n        // let bgColorRight =  data.moduleColor.color[1].item;\n        // this.bgColor = `linear-gradient(90deg,${bgColorLeft} 0%,${bgColorRight} 100%)`;\n\n        this.bottomBgColor = data.bottomBgColor.color[0].item;\n        this.mTop = data.mbConfig.val;\n        this.topConfig = data.topConfig.val;\n        this.bottomConfig = data.bottomConfig.val;\n        this.prConfig = data.prConfig.val;\n        var fillet = data.fillet.type;\n        var filletVal = data.fillet.val;\n        var valList = data.fillet.valList;\n        this.bgRadius = fillet ? valList[0].val + 'px ' + valList[1].val + 'px 0 0' : filletVal + 'px ' + filletVal + 'px 0 0';\n        this.bgRadius2 = fillet ? '0 0 ' + valList[3].val + 'px ' + valList[2].val + 'px' : '0 0 ' + filletVal + 'px ' + filletVal + 'px';\n        this.navlist = data.tabConfig.list;\n        this.tabCur = data.tabConfig.tabCur || 0;\n        var goods = data.tabConfig.list[this.tabCur];\n\n        if (goods.tabVal == 1) {\n          this.list = goods.goodsList.list.length ? goods.goodsList.list : 2;\n        } else {\n          this.list = goods.productList.list.length ? goods.productList.list : 2;\n        }\n      }\n    }\n  }\n};", null]}