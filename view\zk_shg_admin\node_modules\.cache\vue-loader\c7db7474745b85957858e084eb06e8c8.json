{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue?vue&type=template&id=d172c52a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"line-box\"},[_c('div',{staticClass:\"input-box\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.configData.list,\"group\":\"peoples\",\"handle\":\".icon\"}},_vm._l((_vm.configData.list),function(item,index){return _c('div',{key:index,staticClass:\"input-item\"},[_c('div',{staticClass:\"icon\"},[_c('span',{staticClass:\"iconfont-diy iconxingzhuangjiehe\"})]),_c('Select',{model:{value:(item.val),callback:function ($$v) {_vm.$set(item, \"val\", $$v)},expression:\"item.val\"}},_vm._l((_vm.wordList),function(val,index){return _c('Option',{key:index,attrs:{\"value\":val.name}},[_vm._v(_vm._s(val.name))])}),1),_c('div',{staticClass:\"delete\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete(index)}}},[_c('span',{staticClass:\"iconfont icondel_2\"})])],1)}),0),(_vm.configData.list.length < 20)?_c('div',{staticClass:\"add-btn\",on:{\"click\":_vm.addHotTxt}},[_c('Button',{staticClass:\"btn\",attrs:{\"type\":\"primary\",\"ghost\":\"\"}},[_c('span',{staticClass:\"iconfont iconjiahao\"}),_vm._v(\"添加\\n\\t\\t\\t\\t\")])],1):_vm._e()],1)]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}