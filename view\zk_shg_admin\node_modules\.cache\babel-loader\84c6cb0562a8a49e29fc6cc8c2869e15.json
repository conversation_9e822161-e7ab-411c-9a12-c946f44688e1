{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\index.vue", "mtime": 1685091240000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport timeOptions from \"@/utils//timeOptions\";\nimport { getGroupTemplateChatList, workGroupTemplateSendMsg } from \"@/api/work\";\nimport { log } from 'util'; // import { apiGoods } from \"@/mock/mock\";\n\nimport Setting from \"@/setting\";\nexport default {\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      options: timeOptions,\n      timeVal: [],\n      tableFrom: {\n        name: \"\",\n        create_time: \"\",\n        client_type: \"\",\n        page: 1,\n        type: '1',\n        limit: 15\n      },\n      total: 0,\n      tableList: [],\n      loading: false,\n      tableData: [],\n      columns1: [{\n        title: '群发名称',\n        key: 'name',\n        minWidth: 150\n      }, {\n        title: '已发送群主',\n        key: 'user_count',\n        minWidth: 120\n      }, {\n        title: '送达群聊',\n        key: 'external_user_count',\n        minWidth: 120\n      }, {\n        title: '未发送群主',\n        key: 'unuser_count',\n        minWidth: 120\n      }, {\n        title: '未送达群聊',\n        key: 'external_unuser_count',\n        minWidth: 120\n      }, {\n        title: '是否发送',\n        slot: 'send_type',\n        minWidth: 120\n      }, {\n        title: '群发类型',\n        slot: 'template_type',\n        minWidth: 120\n      }, {\n        title: '发送时间',\n        key: 'update_time',\n        minWidth: 150\n      }, {\n        title: '创建时间',\n        key: 'create_time',\n        minWidth: 150\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        minWidth: 170\n      }]\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n\n      this.loading = true;\n      getGroupTemplateChatList(this.tableFrom).then(function (res) {\n        _this.tableData = res.data;\n        _this.loading = false;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n\n        _this.loading = false;\n      });\n    },\n    search: function search() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.time = this.timeVal.join(\"-\");\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    // 删除\n    delItem: function delItem(row, index) {\n      var _this2 = this;\n\n      var delfromData = {\n        title: '删除该客户群发',\n        num: index,\n        url: \"work/group_template_chat/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this2.$Message.success(res.msg);\n\n        _this2.tableData.list.splice(index, 1);\n\n        if (!_this2.tableData.list.length) {\n          _this2.tableFrom.page = _this2.tableFrom.page == 1 ? 1 : _this2.tableFrom.page - 1;\n        }\n\n        _this2.getList();\n      }).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    // 详情\n    detailsItem: function detailsItem(row, index) {\n      this.$router.push(this.roterPre + \"/work/group/template_info/\" + row.id);\n    },\n    // 提醒发送\n    sendMessage: function sendMessage(row, index) {\n      var _this3 = this;\n\n      workGroupTemplateSendMsg({\n        userid: \"\",\n        time: row.update_time,\n        id: row.id\n      }).then(function (res) {\n        _this3.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}