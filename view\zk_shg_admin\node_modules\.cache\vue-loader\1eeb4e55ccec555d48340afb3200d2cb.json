{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue?vue&type=template&id=52ac2395&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 用户-付费会员-会员协议 -->\n    <div>\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n            <Form :label-width=\"80\" @submit.native.prevent>\n                <FormItem label=\"协议名称：\">\n                    <Input v-model=\"agreement.title\"></Input>\n                </FormItem>\n                <FormItem label=\"协议内容：\">\n\t\t\t\t\t<WangEditor\n\t\t\t\t\t  :content=\"content\"\n\t\t\t\t\t  @editorContent=\"getEditorContent\"\n\t\t\t\t\t></WangEditor>\n                </FormItem>\n                <FormItem label=\"开启状态：\">\n                    <i-switch\n                        v-model=\"agreement.status\"\n                        size=\"large\"\n                        :true-value=\"1\"\n                        :false-value=\"0\"\n                    >\n                        <span slot=\"open\">开启</span>\n                        <span slot=\"close\">关闭</span>\n                    </i-switch>\n                </FormItem>\n            </Form>\n            <Spin fix v-if=\"spinShow\"></Spin>\n        </Card>\n\t\t<!-- <Button type=\"primary\" @click=\"memberAgreementSave\">保存</Button> -->\n\t\t<Card :bordered=\"false\" dis-hover class=\"fixed-card\" :style=\"{left: `${!menuCollapse?'200px':isMobile?'0':'80px'}`}\">\n\t\t\t<div class=\"acea-row row-center\">\n\t\t\t\t<Button type=\"primary\" @click=\"memberAgreementSave\">保存</Button>\n\t\t\t</div>\n\t\t</Card>\n    </div>\n", null]}