{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_input_number.vue?vue&type=template&id=95a8396a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_input_number.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"numbox\" :class=\"configData.type=='form'?'on':configData.type=='words'?'on2':''\" v-if=\"configData\">\n        <div class=\"c_row-item\">\n\t\t\t<Col class=\"label\" span=\"4\">\n\t\t\t\t<span>{{configData.title ||'商品数量'}}</span>\n\t\t\t</Col>\n\t\t\t<Col :span=\"configData.type=='form'?19:18\" class=\"slider-box\">\n\t\t\t\t<!--<Input v-model=\"configData.val\" type=\"number\" placeholder=\"请输入数量\" @on-change=\"bindChange\" style=\"text-align: right;\"/>-->\n\t\t\t\t<div class=\"acea-row row-middle\">\n\t\t\t\t\t<InputNumber v-model=\"configData.val\" :placeholder=\"configData.placeholder\" :step=\"1\" :max=\"100\" :min=\"1\" @on-change=\"bindChange\" style=\"text-align: right;\"></InputNumber>\n\t\t\t\t\t<div class=\"unit\" v-if=\"configData.type=='words'\">秒</div>\n\t\t\t\t</div>\n\t\t\t</Col>\n        </div>\n    </div>\n", null]}