{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_bg_color.vue?vue&type=template&id=5e9e6f92&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_bg_color.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[(_vm.configData)?_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"c_label\"},[_vm._v(_vm._s(_vm.configData.title))]),_c('Col',{staticClass:\"color-box\"},[_vm._l((_vm.configData.color),function(color,key){return _c('div',{key:key,staticClass:\"color-item\"},[_c('ColorPicker',{attrs:{\"alpha\":\"\"},on:{\"on-change\":function($event){return _vm.changeColor($event,color)}},model:{value:(color.item),callback:function ($$v) {_vm.$set(color, \"item\", $$v)},expression:\"color.item\"}}),_c('Input',{staticClass:\"input\",model:{value:(color.item),callback:function ($$v) {_vm.$set(color, \"item\", $$v)},expression:\"color.item\"}}),_c('span',{on:{\"click\":function($event){return _vm.resetBgA(color,_vm.index,key)}}},[_vm._v(\"重置\")])],1)}),(_vm.configData.color.length>1)?_c('div',{staticClass:\"iconfont iconlianjie\"}):_vm._e()],2)],1):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}