{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue", "mtime": 1750985338988}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 注意：实际项目中需要引入echarts\n// import * as echarts from 'echarts'\nexport default {\n  name: 'ERPReport',\n  data: function data() {\n    return {\n      // 日期选择\n      dateRange: [],\n      // 概览数据\n      overview: {\n        inventory_value: 1250000,\n        inventory_change: 12.5,\n        transfer_count: 156,\n        transfer_change: 8.3,\n        warning_count: 23,\n        efficiency: 92\n      },\n      // 图表控制\n      inventoryPeriod: 'month',\n      transferType: 'status',\n      // 报表数据\n      storeReport: [],\n      transferReport: [],\n      storeLoading: false,\n      transferLoading: false,\n      // 商品分析\n      hotProducts: [],\n      warningStats: {\n        severe: 5,\n        low: 18,\n        normal: 234,\n        abundant: 89\n      }\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  methods: {\n    // 初始化数据\n    initData: function () {\n      var _initData = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        var _this = this;\n\n        var now, start;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                // 设置默认日期范围（最近3个月）\n                now = new Date();\n                start = new Date(now.getFullYear(), now.getMonth() - 2, 1);\n                this.dateRange = [start, now];\n                _context.next = 5;\n                return Promise.all([this.loadStoreReport(), this.loadTransferReport(), this.loadProductAnalysis()]);\n\n              case 5:\n                this.$nextTick(function () {\n                  _this.initCharts();\n                });\n\n              case 6:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function initData() {\n        return _initData.apply(this, arguments);\n      }\n\n      return initData;\n    }(),\n    // 加载门店报表\n    loadStoreReport: function () {\n      var _loadStoreReport = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee2() {\n        var _this2 = this;\n\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                this.storeLoading = true; // 模拟数据\n\n                setTimeout(function () {\n                  _this2.storeReport = [{\n                    store_id: 1,\n                    store_name: '上海总店',\n                    total_products: 156,\n                    total_stock: 2340,\n                    total_value: 450000,\n                    warning_count: 8,\n                    out_stock_count: 2\n                  }, {\n                    store_id: 2,\n                    store_name: '北京分店',\n                    total_products: 134,\n                    total_stock: 1890,\n                    total_value: 380000,\n                    warning_count: 12,\n                    out_stock_count: 1\n                  }, {\n                    store_id: 3,\n                    store_name: '深圳分店',\n                    total_products: 142,\n                    total_stock: 2100,\n                    total_value: 420000,\n                    warning_count: 3,\n                    out_stock_count: 2\n                  }];\n                  _this2.storeLoading = false;\n                }, 1000);\n\n              case 2:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function loadStoreReport() {\n        return _loadStoreReport.apply(this, arguments);\n      }\n\n      return loadStoreReport;\n    }(),\n    // 加载调拨报表\n    loadTransferReport: function () {\n      var _loadTransferReport = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee3() {\n        var _this3 = this;\n\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.transferLoading = true; // 模拟数据\n\n                setTimeout(function () {\n                  _this3.transferReport = [{\n                    date: '2024-12-16',\n                    total_orders: 12,\n                    approved_orders: 10,\n                    completed_orders: 8,\n                    avg_process_time: 2.5,\n                    total_quantity: 245\n                  }, {\n                    date: '2024-12-15',\n                    total_orders: 8,\n                    approved_orders: 8,\n                    completed_orders: 7,\n                    avg_process_time: 1.8,\n                    total_quantity: 156\n                  }, {\n                    date: '2024-12-14',\n                    total_orders: 15,\n                    approved_orders: 13,\n                    completed_orders: 12,\n                    avg_process_time: 3.2,\n                    total_quantity: 389\n                  }];\n                  _this3.transferLoading = false;\n                }, 1000);\n\n              case 2:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this);\n      }));\n\n      function loadTransferReport() {\n        return _loadTransferReport.apply(this, arguments);\n      }\n\n      return loadTransferReport;\n    }(),\n    // 加载商品分析\n    loadProductAnalysis: function () {\n      var _loadProductAnalysis = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee4() {\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                // 模拟数据\n                this.hotProducts = [{\n                  id: 1,\n                  name: '苹果iPhone 14',\n                  transfer_count: 45,\n                  trend: 'up'\n                }, {\n                  id: 2,\n                  name: '华为Mate50',\n                  transfer_count: 38,\n                  trend: 'up'\n                }, {\n                  id: 3,\n                  name: '小米13',\n                  transfer_count: 32,\n                  trend: 'down'\n                }, {\n                  id: 4,\n                  name: 'OPPO Find X5',\n                  transfer_count: 28,\n                  trend: 'up'\n                }, {\n                  id: 5,\n                  name: 'vivo X90',\n                  transfer_count: 25,\n                  trend: 'down'\n                }];\n\n              case 1:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this);\n      }));\n\n      function loadProductAnalysis() {\n        return _loadProductAnalysis.apply(this, arguments);\n      }\n\n      return loadProductAnalysis;\n    }(),\n    // 初始化图表\n    initCharts: function initCharts() {\n      this.loadInventoryTrend();\n      this.loadTransferStats();\n    },\n    // 加载库存趋势图\n    loadInventoryTrend: function loadInventoryTrend() {\n      // 模拟图表初始化\n      console.log('加载库存趋势图:', this.inventoryPeriod); // 在实际项目中，这里会使用echarts初始化图表\n    },\n    // 加载调拨统计图\n    loadTransferStats: function loadTransferStats() {\n      // 模拟图表初始化\n      console.log('加载调拨统计图:', this.transferType); // 在实际项目中，这里会使用echarts初始化图表\n    },\n    // 工具方法\n    formatMoney: function formatMoney(amount) {\n      return (amount / 10000).toFixed(1) + '万';\n    },\n    getStockHealth: function getStockHealth(row) {\n      var total = row.total_products;\n      var warning = row.warning_count;\n      var outStock = row.out_stock_count;\n      return Math.round((total - warning - outStock) / total * 100);\n    },\n    getProgressColor: function getProgressColor(row) {\n      var health = this.getStockHealth(row);\n      if (health >= 80) return '#67c23a';\n      if (health >= 60) return '#e6a23c';\n      return '#f56c6c';\n    },\n    getCompletionRate: function getCompletionRate(row) {\n      return Math.round(row.completed_orders / row.total_orders * 100);\n    },\n    getEfficiencyClass: function getEfficiencyClass(row) {\n      var rate = this.getCompletionRate(row);\n      if (rate >= 80) return 'success-text';\n      if (rate >= 60) return 'warning-text';\n      return 'danger-text';\n    },\n    getEfficiencyTagType: function getEfficiencyTagType(row) {\n      var rate = this.getCompletionRate(row);\n      if (rate >= 80) return 'success';\n      if (rate >= 60) return 'warning';\n      return 'danger';\n    },\n    getEfficiencyText: function getEfficiencyText(row) {\n      var rate = this.getCompletionRate(row);\n      if (rate >= 80) return '优秀';\n      if (rate >= 60) return '良好';\n      return '待改进';\n    },\n    // 事件处理\n    handleDateChange: function handleDateChange() {\n      this.refreshData();\n    },\n    refreshData: function refreshData() {\n      this.loadStoreReport();\n      this.loadTransferReport();\n      this.loadProductAnalysis();\n      this.initCharts();\n    },\n    refreshProductAnalysis: function refreshProductAnalysis() {\n      this.loadProductAnalysis();\n    },\n    // 查看详情\n    viewStoreDetail: function viewStoreDetail(row) {\n      this.$message.info(\"\\u67E5\\u770B\".concat(row.store_name, \"\\u8BE6\\u60C5\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\"));\n    },\n    // 导出功能\n    exportReport: function exportReport() {\n      this.$message.info('导出报表功能开发中...');\n    },\n    exportStoreReport: function exportStoreReport() {\n      this.$message.info('导出门店报表功能开发中...');\n    },\n    exportTransferReport: function exportTransferReport() {\n      this.$message.info('导出调拨报表功能开发中...');\n    }\n  }\n};", null]}