{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\mixins\\emitter.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\mixins\\emitter.js", "mtime": 1725352513847}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["function _broadcast(componentName, eventName, params) {\n  this.$children.forEach(function (child) {\n    var name = child.$options.name;\n\n    if (name === componentName) {\n      child.$emit.apply(child, [eventName].concat(params));\n    } else {\n      // todo 如果 params 是空数组，接收到的会是 undefined\n      _broadcast.apply(child, [componentName, eventName].concat([params]));\n    }\n  });\n}\n\nexport default {\n  methods: {\n    dispatch: function dispatch(componentName, eventName, params) {\n      var parent = this.$parent || this.$root;\n      var name = parent.$options.name;\n\n      while (parent && (!name || name !== componentName)) {\n        parent = parent.$parent;\n\n        if (parent) {\n          name = parent.$options.name;\n        }\n      }\n\n      if (parent) {\n        parent.$emit.apply(parent, [eventName].concat(params));\n      }\n    },\n    broadcast: function broadcast(componentName, eventName, params) {\n      _broadcast.call(this, componentName, eventName, params);\n    }\n  }\n};", null]}