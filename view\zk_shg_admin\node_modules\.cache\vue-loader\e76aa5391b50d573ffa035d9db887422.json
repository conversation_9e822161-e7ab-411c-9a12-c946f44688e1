{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {\n  releasedListApi,\n  releasedissueLogApi,\n  releaseStatus<PERSON><PERSON>,\n  delCouponReleased,\n  couponStatus<PERSON><PERSON>\n} from \"@/api/marketing\";\nimport { formatDate } from \"@/utils/validate\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"storeCouponIssue\",\n  filters: {\n    formatDate(time) {\n      if (time !== 0) {\n        let date = new Date(time * 1000);\n        return formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    }\n  },\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      modals2: false,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns1: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          width: 80\n        },\n        {\n          title: \"优惠券名称\",\n          slot: \"coupon_title\",\n          minWidth: 150\n          // render (h, data) {\n          //     let row = data.row, content = '';\n          //     if (row.is_give_subscribe) {\n          //         content = '关注';\n          //     } else if (row.is_full_give) {\n          //         content = '满赠';\n          //     } else {\n          //         content = '普通'\n          //     }\n          //     return h('div', [\n          //         h('Tag', { attrs: {\n          //             color: 'blue'\n          //         } }, content),\n          //         h('span', data.row.coupon_title)\n          //     ]);\n          // }\n        },\n        {\n          title: \"优惠券类型\",\n          slot: \"coupon_type\",\n          minWidth: 80\n        },\n        {\n          title: \"适用类型\",\n          slot: \"type\",\n          minWidth: 80\n        },\n        {\n          title: \"面值\",\n          slot: \"coupon_price\",\n          minWidth: 100\n        },\n        {\n          title: \"领取方式\",\n          slot: \"receive_type\",\n          minWidth: 100\n        },\n        {\n          title: \"优惠券种类\",\n          key: \"category\",\n          minWidth: 100,\n          render: (h, params) => {\n            return h('div', params.row.category == 1 ? '普通券' : '会员券');\n          }\n        },\n        {\n          title: \"领取时间\",\n          slot: \"start_time\",\n          minWidth: 250\n        },\n        {\n          title: \"使用时间\",\n          slot: \"start_use_time\",\n          minWidth: 250\n        },\n        {\n          title: \"发布数量\",\n          slot: \"count\",\n          minWidth: 90\n        },\n        {\n          title: \"是否开启\",\n          slot: \"status\",\n          minWidth: 90\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n          minWidth: 200\n        }\n      ],\n      tableFrom: {\n        status: \"\",\n        coupon_title: \"\",\n        receive_type: \"\",\n        coupon_type: \"\",\n        page: 1,\n        limit: 15\n      },\n      receive_type: \"\",\n      tableList: [],\n      total: 0,\n      FromData: null,\n      receiveList: [],\n      loading2: false,\n      columns2: [\n        {\n          title: \"ID\",\n          key: \"uid\",\n          minWidth: 80\n        },\n        {\n          title: \"用户名\",\n          key: \"nickname\",\n          minWidth: 150\n        },\n        {\n          title: \"用户头像\",\n          slot: \"avatar\",\n          minWidth: 100\n        },\n        {\n          title: \"领取时间\",\n          key: \"add_time\",\n          minWidth: 140\n        }\n      ],\n      total2: 0,\n      receiveFrom: {\n        page: 1,\n        limit: 15\n      },\n      rows: {}\n    };\n  },\n  created() {\n    this.getList();\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  },\n  methods: {\n    // 失效\n    couponInvalid(row, tit, num) {\n      this.delfromData = {\n        title: tit,\n        num: num,\n        url: `marketing/coupon/status/${row.id}`,\n        method: \"PUT\",\n        ids: \"\"\n      };\n      this.$refs.modelSure.modals = true;\n    },\n    // 领取记录\n    receive(row) {\n      this.modals2 = true;\n      this.rows = row;\n      this.getReceivelist(row);\n    },\n    getReceivelist(row) {\n      this.loading2 = true;\n      releasedissueLogApi(row.id, this.receiveFrom)\n        .then(async res => {\n          let data = res.data;\n          this.receiveList = data.list;\n          this.total2 = res.data.count;\n          this.loading2 = false;\n        })\n        .catch(res => {\n          this.loading2 = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 领取记录改变分页\n    receivePageChange(index) {\n      this.receiveFrom.page = index;\n      this.getReceivelist(this.rows);\n    },\n    // 删除\n    couponDel(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `marketing/coupon/released/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then(res => {\n          this.$Message.success(res.msg);\n          this.tableList.splice(num, 1);\n          if (!this.tableList.length) {\n            this.tableFrom.page =\n                this.tableFrom.page == 1 ? 1 : this.tableFrom.page - 1;\n          }\n          this.getList();\n        })\n        .catch(res => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      this.tableFrom.receive_type =\n        this.receive_type === \"all\" ? \"\" : this.receive_type;\n      this.tableFrom.status = this.tableFrom.status || \"\";\n      releasedListApi(this.tableFrom)\n        .then(async res => {\n          let data = res.data;\n          this.tableList = data.list;\n          this.total = res.data.count;\n          this.loading = false;\n        })\n        .catch(res => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 搜索()\n    orderSearch () {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 添加优惠券\n    add() {\n      this.$router.push({ path: this.roterPre + \"/marketing/store_coupon_issue/create\" });\n    },\n    // 复制\n    copy(data) {\n      this.$router.push({\n        path: `${this.roterPre}/marketing/store_coupon_issue/create/${data.id}`\n      });\n    },\n    // 是否开启\n    openChange(data) {\n      couponStatusApi(data).then(() => this.getList());\n    }\n  }\n};\n", null]}