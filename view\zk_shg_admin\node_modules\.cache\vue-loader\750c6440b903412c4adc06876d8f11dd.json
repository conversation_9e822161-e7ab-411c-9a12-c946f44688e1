{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\index.vue", "mtime": 1689737622000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport { getWorkMomenttList } from \"@api/work\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"\",\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      timeVal: [],\n      options: timeOptions,\n      columns1: [\n        {\n          title: \"任务名称\",\n          key: \"name\",\n          minWidth: 80,\n        },\n\t\t{\n          title: \"已发送成员\",\n          key: \"user_count\",\n          minWidth: 100,\n        },\n        {\n          title: \"未发送成员\",\n          key: \"unuser_count\",\n          minWidth: 100,\n        },\n        {\n          title: \"成员类型\",\n          slot: \"type\",\n          minWidth: 120,\n        },\n\t\t{\n          title: \"发布方式\",\n          slot: \"send_type\",\n          minWidth: 120,\n        },\n        {\n          title: \"发送人\",\n          slot: \"user_list\",\n          minWidth: 150,\n        },\n        {\n          title: \"创建时间\",\n          key: \"create_time\",\n          minWidth: 150,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          minWidth: 100,\n        },\n      ],\n      tableData: [],\n      tableFrom: {\n        time: \"\",\n        name: \"\",\n        page: 1,\n        limit: 15,\n      },\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    },\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      getWorkMomenttList(this.tableFrom)\n        .then((res) => {\n          this.tableData = res.data;\n          this.loading = false;\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n          this.loading = false;\n        });\n    },\n    search() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n    },\n    pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    detailsItem(row,index){\n      this.$router.push(this.roterPre + \"/work/client/moment_info/\" + row.id)\n    },\n    delItem(row,index){\n      let delfromData = {\n\t\t\t\t\ttitle: '删除该朋友圈',\n\t\t\t\t\tnum:index,\n\t\t\t\t\turl: `work/moment/${row.id}`,\n\t\t\t\t\tmethod: \"DELETE\",\n\t\t\t\t\tids: \"\",\n\t\t\t\t};\n\t\t\t\tthis.$modalSure(delfromData)\n\t\t\t\t\t.then((res) => {\n\t\t\t\t\tthis.$Message.success(res.msg);\n          this.tableData.list.splice(index, 1);\n          if (!this.tableData.list.length) {\n            this.tableFrom.page =\n                this.tableFrom.page == 1 ? 1 : this.tableFrom.page - 1;\n          }\n          this.getList();\n\t\t\t\t\t})\n\t\t\t\t\t.catch((res) => {\n\t\t\t\t\tthis.$Message.error(res.msg);\n\t\t\t\t});\n    },\n  },\n};\n", null]}