{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\queueList.vue?vue&type=template&id=7a2c1447&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\queueList.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Modal',{attrs:{\"title\":\"任务列表\",\"width\":\"1000\",\"footer-hide\":\"\",\"class-name\":\"vertical-center-modal\"},model:{value:(_vm.modal),callback:function ($$v) {_vm.modal=$$v},expression:\"modal\"}},[(_vm.modal)?_c('Card',{attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Form',{ref:\"formValidate\",staticClass:\"tabform\",attrs:{\"inline\":\"\",\"model\":_vm.formValidate,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"操作时间：\"}},[_c('DatePicker',{staticClass:\"width20\",attrs:{\"editable\":false,\"value\":_vm.timeVal,\"format\":\"yyyy/MM/dd\",\"type\":\"datetimerange\",\"placement\":\"bottom-start\",\"placeholder\":\"自定义时间\",\"options\":_vm.options},on:{\"on-change\":_vm.onchangeTime}})],1),_c('FormItem',{attrs:{\"label\":\"类型：\"}},[_c('Select',{staticClass:\"width20\",attrs:{\"clearable\":\"\"},on:{\"on-change\":_vm.typeSearchs},model:{value:(_vm.formValidate.type),callback:function ($$v) {_vm.$set(_vm.formValidate, \"type\", $$v)},expression:\"formValidate.type\"}},_vm._l((_vm.typeList),function(item){return _c('Option',{key:item.value,attrs:{\"value\":item.value}},[_vm._v(_vm._s(item.label))])}),1)],1),_c('FormItem',{attrs:{\"label\":\"状态：\"}},[_c('Select',{staticClass:\"width20\",attrs:{\"clearable\":\"\"},on:{\"on-change\":_vm.statusSearchs},model:{value:(_vm.formValidate.status),callback:function ($$v) {_vm.$set(_vm.formValidate, \"status\", $$v)},expression:\"formValidate.status\"}},_vm._l((_vm.statusList),function(item){return _c('Option',{key:item.value,attrs:{\"value\":item.value}},[_vm._v(_vm._s(item.label))])}),1)],1)],1),_c('Table',{staticClass:\"mt25\",attrs:{\"height\":\"500\",\"columns\":_vm.columns1,\"data\":_vm.data1,\"loading\":_vm.loading},scopedSlots:_vm._u([{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.is_show_log)?[_c('a',{on:{\"click\":function($event){return _vm.deliveryLook(row)}}},[_vm._v(\"查看\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}})]:_vm._e(),[_c('Dropdown',{on:{\"on-click\":function($event){return _vm.changeMenu(row, $event)}}},[_c('a',[_vm._v(\"更多\"),_c('Icon',{attrs:{\"type\":\"ios-arrow-down\"}})],1),_c('DropdownMenu',{attrs:{\"slot\":\"list\"},slot:\"list\"},[([7, 8, 9, 10].includes(row.type))?_c('DropdownItem',{attrs:{\"name\":\"1\"}},[_vm._v(\"下载\")]):_vm._e(),(row.status == 1)?_c('DropdownItem',{attrs:{\"name\":\"2\"}},[_vm._v(\"重新执行\")]):_vm._e(),(row.is_stop_button)?_c('DropdownItem',{attrs:{\"name\":\"3\"}},[_vm._v(\"停止任务\")]):_vm._e(),_c('DropdownItem',{attrs:{\"name\":\"4\"}},[_vm._v(\"清除任务\")])],1)],1)]]}}],null,false,2501981875)}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.page1.total,\"current\":_vm.page1.pageNum,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.page1.pageSize,\"show-sizer\":\"\"},on:{\"on-change\":_vm.pageChange,\"on-page-size-change\":_vm.limitChange}})],1)],1):_vm._e(),_c('Modal',{attrs:{\"width\":\"900\",\"footer-hide\":\"\"},model:{value:(_vm.modal1),callback:function ($$v) {_vm.modal1=$$v},expression:\"modal1\"}},[_c('Table',{staticClass:\"mt25\",attrs:{\"height\":\"500\",\"columns\":_vm.columns4,\"data\":_vm.data2,\"loading\":_vm.loading2}}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.page2.total,\"current\":_vm.page2.pageNum,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.page2.pageSize,\"show-sizer\":\"\"},on:{\"on-change\":_vm.pageChange2,\"on-page-size-change\":_vm.limitChange2}})],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}