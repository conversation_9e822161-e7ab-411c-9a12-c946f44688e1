{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue?vue&type=template&id=1d6e0732&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue", "mtime": 1750985338988}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"report-container\" }, [\n    _c(\"div\", { staticClass: \"page-header\" }, [\n      _vm._m(0),\n      _c(\n        \"div\",\n        { staticClass: \"header-right\" },\n        [\n          _c(\"el-date-picker\", {\n            staticStyle: { \"margin-right\": \"12px\" },\n            attrs: {\n              type: \"monthrange\",\n              \"range-separator\": \"至\",\n              \"start-placeholder\": \"开始月份\",\n              \"end-placeholder\": \"结束月份\"\n            },\n            on: { change: _vm.handleDateChange },\n            model: {\n              value: _vm.dateRange,\n              callback: function($$v) {\n                _vm.dateRange = $$v\n              },\n              expression: \"dateRange\"\n            }\n          }),\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"primary\", icon: \"el-icon-refresh\" },\n              on: { click: _vm.refreshData }\n            },\n            [_vm._v(\"刷新数据\")]\n          ),\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"success\", icon: \"el-icon-download\" },\n              on: { click: _vm.exportReport }\n            },\n            [_vm._v(\"导出报表\")]\n          )\n        ],\n        1\n      )\n    ]),\n    _c(\"div\", { staticClass: \"overview-cards\" }, [\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _vm._m(1),\n        _c(\"div\", { staticClass: \"card-content\" }, [\n          _c(\"div\", { staticClass: \"card-title\" }, [_vm._v(\"库存总值\")]),\n          _c(\"div\", { staticClass: \"card-number\" }, [\n            _vm._v(\"¥\" + _vm._s(_vm.formatMoney(_vm.overview.inventory_value)))\n          ]),\n          _c(\n            \"div\",\n            {\n              staticClass: \"card-change\",\n              class:\n                _vm.overview.inventory_change >= 0 ? \"positive\" : \"negative\"\n            },\n            [\n              _c(\"i\", {\n                class:\n                  _vm.overview.inventory_change >= 0\n                    ? \"el-icon-arrow-up\"\n                    : \"el-icon-arrow-down\"\n              }),\n              _vm._v(\n                \"\\n          \" +\n                  _vm._s(Math.abs(_vm.overview.inventory_change)) +\n                  \"%\\n        \"\n              )\n            ]\n          )\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _vm._m(2),\n        _c(\"div\", { staticClass: \"card-content\" }, [\n          _c(\"div\", { staticClass: \"card-title\" }, [_vm._v(\"调拨单数\")]),\n          _c(\"div\", { staticClass: \"card-number\" }, [\n            _vm._v(_vm._s(_vm.overview.transfer_count))\n          ]),\n          _c(\n            \"div\",\n            {\n              staticClass: \"card-change\",\n              class: _vm.overview.transfer_change >= 0 ? \"positive\" : \"negative\"\n            },\n            [\n              _c(\"i\", {\n                class:\n                  _vm.overview.transfer_change >= 0\n                    ? \"el-icon-arrow-up\"\n                    : \"el-icon-arrow-down\"\n              }),\n              _vm._v(\n                \"\\n          \" +\n                  _vm._s(Math.abs(_vm.overview.transfer_change)) +\n                  \"%\\n        \"\n              )\n            ]\n          )\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _vm._m(3),\n        _c(\"div\", { staticClass: \"card-content\" }, [\n          _c(\"div\", { staticClass: \"card-title\" }, [_vm._v(\"预警商品\")]),\n          _c(\"div\", { staticClass: \"card-number\" }, [\n            _vm._v(_vm._s(_vm.overview.warning_count))\n          ]),\n          _c(\"div\", { staticClass: \"card-desc\" }, [_vm._v(\"需要关注\")])\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _vm._m(4),\n        _c(\"div\", { staticClass: \"card-content\" }, [\n          _c(\"div\", { staticClass: \"card-title\" }, [_vm._v(\"调拨效率\")]),\n          _c(\"div\", { staticClass: \"card-number\" }, [\n            _vm._v(_vm._s(_vm.overview.efficiency) + \"%\")\n          ]),\n          _c(\"div\", { staticClass: \"card-desc\" }, [_vm._v(\"平均处理时间\")])\n        ])\n      ])\n    ]),\n    _c(\"div\", { staticClass: \"charts-container\" }, [\n      _c(\"div\", { staticClass: \"chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-header\" }, [\n          _c(\"h3\", [_vm._v(\"库存变动趋势\")]),\n          _c(\n            \"div\",\n            { staticClass: \"chart-controls\" },\n            [\n              _c(\n                \"el-radio-group\",\n                {\n                  attrs: { size: \"small\" },\n                  on: { change: _vm.loadInventoryTrend },\n                  model: {\n                    value: _vm.inventoryPeriod,\n                    callback: function($$v) {\n                      _vm.inventoryPeriod = $$v\n                    },\n                    expression: \"inventoryPeriod\"\n                  }\n                },\n                [\n                  _c(\"el-radio-button\", { attrs: { label: \"week\" } }, [\n                    _vm._v(\"近7天\")\n                  ]),\n                  _c(\"el-radio-button\", { attrs: { label: \"month\" } }, [\n                    _vm._v(\"近30天\")\n                  ]),\n                  _c(\"el-radio-button\", { attrs: { label: \"quarter\" } }, [\n                    _vm._v(\"近3个月\")\n                  ])\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ]),\n        _c(\"div\", { staticClass: \"chart-content\" }, [\n          _c(\"div\", {\n            ref: \"inventoryChart\",\n            staticStyle: { width: \"100%\", height: \"300px\" }\n          })\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-header\" }, [\n          _c(\"h3\", [_vm._v(\"调拨统计分析\")]),\n          _c(\n            \"div\",\n            { staticClass: \"chart-controls\" },\n            [\n              _c(\n                \"el-select\",\n                {\n                  staticStyle: { width: \"120px\" },\n                  attrs: { size: \"small\" },\n                  on: { change: _vm.loadTransferStats },\n                  model: {\n                    value: _vm.transferType,\n                    callback: function($$v) {\n                      _vm.transferType = $$v\n                    },\n                    expression: \"transferType\"\n                  }\n                },\n                [\n                  _c(\"el-option\", {\n                    attrs: { label: \"按状态\", value: \"status\" }\n                  }),\n                  _c(\"el-option\", {\n                    attrs: { label: \"按门店\", value: \"store\" }\n                  }),\n                  _c(\"el-option\", { attrs: { label: \"按时间\", value: \"time\" } })\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ]),\n        _c(\"div\", { staticClass: \"chart-content\" }, [\n          _c(\"div\", {\n            ref: \"transferChart\",\n            staticStyle: { width: \"100%\", height: \"300px\" }\n          })\n        ])\n      ])\n    ]),\n    _c(\"div\", { staticClass: \"report-tables\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"table-card\" },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"table-header\" },\n            [\n              _c(\"h3\", [_vm._v(\"门店库存报表\")]),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"text\", size: \"small\" },\n                  on: { click: _vm.exportStoreReport }\n                },\n                [_vm._v(\"导出\")]\n              )\n            ],\n            1\n          ),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.storeLoading,\n                  expression: \"storeLoading\"\n                }\n              ],\n              attrs: { data: _vm.storeReport, stripe: \"\" }\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { prop: \"store_name\", label: \"门店名称\", width: \"150\" }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"total_products\",\n                  label: \"商品总数\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"total_stock\",\n                  label: \"库存总量\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"total_value\",\n                  label: \"库存总值\",\n                  width: \"120\",\n                  align: \"center\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\"span\", { staticClass: \"money\" }, [\n                          _vm._v(\n                            \"¥\" + _vm._s(_vm.formatMoney(scope.row.total_value))\n                          )\n                        ])\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"warning_count\",\n                  label: \"预警商品\",\n                  width: \"100\",\n                  align: \"center\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"span\",\n                          {\n                            class:\n                              scope.row.warning_count > 0 ? \"warning-text\" : \"\"\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(scope.row.warning_count) +\n                                \"\\n            \"\n                            )\n                          ]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"out_stock_count\",\n                  label: \"缺货商品\",\n                  width: \"100\",\n                  align: \"center\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"span\",\n                          {\n                            class:\n                              scope.row.out_stock_count > 0 ? \"danger-text\" : \"\"\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(scope.row.out_stock_count) +\n                                \"\\n            \"\n                            )\n                          ]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"库存状态\", width: \"120\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\"el-progress\", {\n                          attrs: {\n                            percentage: _vm.getStockHealth(scope.row),\n                            color: _vm.getProgressColor(scope.row),\n                            \"stroke-width\": 8,\n                            \"show-text\": false\n                          }\n                        }),\n                        _c(\"span\", { staticClass: \"progress-text\" }, [\n                          _vm._v(_vm._s(_vm.getStockHealth(scope.row)) + \"%\")\n                        ])\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  width: \"120\",\n                  align: \"center\",\n                  fixed: \"right\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function($event) {\n                                return _vm.viewStoreDetail(scope.row)\n                              }\n                            }\n                          },\n                          [_vm._v(\"查看详情\")]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              })\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"table-card\" },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"table-header\" },\n            [\n              _c(\"h3\", [_vm._v(\"调拨效率报表\")]),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"text\", size: \"small\" },\n                  on: { click: _vm.exportTransferReport }\n                },\n                [_vm._v(\"导出\")]\n              )\n            ],\n            1\n          ),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.transferLoading,\n                  expression: \"transferLoading\"\n                }\n              ],\n              attrs: { data: _vm.transferReport, stripe: \"\" }\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"date\",\n                  label: \"日期\",\n                  width: \"120\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"total_orders\",\n                  label: \"调拨单数\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"approved_orders\",\n                  label: \"已审核\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"completed_orders\",\n                  label: \"已完成\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"完成率\", width: \"100\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"span\",\n                          { class: _vm.getEfficiencyClass(scope.row) },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.getCompletionRate(scope.row)) +\n                                \"%\\n            \"\n                            )\n                          ]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"avg_process_time\",\n                  label: \"平均处理时间\",\n                  width: \"120\",\n                  align: \"center\"\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _vm._v(\n                          \"\\n            \" +\n                            _vm._s(scope.row.avg_process_time) +\n                            \"小时\\n          \"\n                        )\n                      ]\n                    }\n                  }\n                ])\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"total_quantity\",\n                  label: \"调拨总量\",\n                  width: \"100\",\n                  align: \"center\"\n                }\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"状态\", width: \"100\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function(scope) {\n                      return [\n                        _c(\n                          \"el-tag\",\n                          {\n                            attrs: {\n                              type: _vm.getEfficiencyTagType(scope.row),\n                              size: \"small\"\n                            }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.getEfficiencyText(scope.row)) +\n                                \"\\n            \"\n                            )\n                          ]\n                        )\n                      ]\n                    }\n                  }\n                ])\n              })\n            ],\n            1\n          )\n        ],\n        1\n      )\n    ]),\n    _c(\"div\", { staticClass: \"analysis-section\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"section-header\" },\n        [\n          _c(\"h3\", [_vm._v(\"商品分析\")]),\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"text\", size: \"small\" },\n              on: { click: _vm.refreshProductAnalysis }\n            },\n            [_vm._v(\"刷新\")]\n          )\n        ],\n        1\n      ),\n      _c(\"div\", { staticClass: \"analysis-cards\" }, [\n        _c(\"div\", { staticClass: \"analysis-card\" }, [\n          _vm._m(5),\n          _c(\n            \"div\",\n            { staticClass: \"product-list\" },\n            _vm._l(_vm.hotProducts, function(product, index) {\n              return _c(\n                \"div\",\n                { key: product.id, staticClass: \"product-item\" },\n                [\n                  _c(\"div\", { staticClass: \"product-rank\" }, [\n                    _vm._v(_vm._s(index + 1))\n                  ]),\n                  _c(\"div\", { staticClass: \"product-info\" }, [\n                    _c(\"div\", { staticClass: \"product-name\" }, [\n                      _vm._v(_vm._s(product.name))\n                    ]),\n                    _c(\"div\", { staticClass: \"product-stats\" }, [\n                      _vm._v(\"调拨次数: \" + _vm._s(product.transfer_count))\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"product-trend\" }, [\n                    _c(\"i\", {\n                      class:\n                        product.trend === \"up\"\n                          ? \"el-icon-arrow-up trend-up\"\n                          : \"el-icon-arrow-down trend-down\"\n                    })\n                  ])\n                ]\n              )\n            }),\n            0\n          )\n        ]),\n        _c(\"div\", { staticClass: \"analysis-card\" }, [\n          _vm._m(6),\n          _c(\"div\", { staticClass: \"warning-stats\" }, [\n            _c(\"div\", { staticClass: \"warning-item\" }, [\n              _c(\"div\", { staticClass: \"warning-label\" }, [_vm._v(\"严重缺货\")]),\n              _c(\"div\", { staticClass: \"warning-value danger\" }, [\n                _vm._v(_vm._s(_vm.warningStats.severe))\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"warning-item\" }, [\n              _c(\"div\", { staticClass: \"warning-label\" }, [_vm._v(\"库存偏低\")]),\n              _c(\"div\", { staticClass: \"warning-value warning\" }, [\n                _vm._v(_vm._s(_vm.warningStats.low))\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"warning-item\" }, [\n              _c(\"div\", { staticClass: \"warning-label\" }, [_vm._v(\"库存正常\")]),\n              _c(\"div\", { staticClass: \"warning-value success\" }, [\n                _vm._v(_vm._s(_vm.warningStats.normal))\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"warning-item\" }, [\n              _c(\"div\", { staticClass: \"warning-label\" }, [_vm._v(\"库存充足\")]),\n              _c(\"div\", { staticClass: \"warning-value info\" }, [\n                _vm._v(_vm._s(_vm.warningStats.abundant))\n              ])\n            ])\n          ])\n        ])\n      ])\n    ])\n  ])\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"header-left\" }, [\n      _c(\"h2\", { staticClass: \"page-title\" }, [_vm._v(\"数据报表\")]),\n      _c(\"p\", { staticClass: \"page-desc\" }, [\n        _vm._v(\"ERP系统数据分析与报表展示\")\n      ])\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"card-icon inventory\" }, [\n      _c(\"i\", { staticClass: \"el-icon-goods\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"card-icon transfer\" }, [\n      _c(\"i\", { staticClass: \"el-icon-sort\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"card-icon warning\" }, [\n      _c(\"i\", { staticClass: \"el-icon-warning\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"card-icon efficiency\" }, [\n      _c(\"i\", { staticClass: \"el-icon-data-analysis\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"card-header\" }, [\n      _c(\"h4\", [_vm._v(\"热门调拨商品 TOP10\")])\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"card-header\" }, [\n      _c(\"h4\", [_vm._v(\"库存预警分析\")])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}