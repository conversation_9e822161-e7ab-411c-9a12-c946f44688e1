{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\statistics.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\statistics.vue", "mtime": 1694049038000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport cardsData from '@/components/cards/cards';\nimport { getbargainStatistics, getbargainStatisticsPeople, getbargainStatisticsOrder, bargainUserInfoApi } from '@/api/marketing';\nimport Setting from \"@/setting\";\nimport { mapState } from \"vuex\";\nexport default {\n  name: 'index',\n  components: {\n    cardsData: cardsData\n  },\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      modals: false,\n      tabList3: [],\n      loading2: false,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      id: 0,\n      tbody: [],\n      total: 0,\n      tabs: [{\n        type: '',\n        label: '活动参与人'\n      }, {\n        type: '',\n        label: '活动订单'\n      }],\n      currentTab: 0,\n      loading: false,\n      thead: [{\n        title: '头像',\n        slot: 'avatar'\n      }, {\n        title: '发起用户',\n        key: 'nickname'\n      }, {\n        title: '发起时间',\n        key: 'add_time'\n      }, {\n        title: '帮砍人数',\n        slot: 'num'\n      }, {\n        title: '结束时间',\n        key: 'datatime'\n      }, {\n        title: '砍价状态',\n        slot: 'status'\n      }, {\n        title: '操作',\n        slot: 'action'\n      }],\n      thead2: [{\n        title: '订单号',\n        key: 'order_id'\n      }, {\n        title: '用户',\n        key: 'real_name'\n      }, {\n        title: '订单状态',\n        key: 'status'\n      }, {\n        title: '订单支付金额',\n        key: 'pay_price'\n      }, {\n        title: '订单商品数',\n        key: 'total_num'\n      }, {\n        title: '下单时间',\n        key: 'add_time'\n      }, {\n        title: '支付时间',\n        key: 'pay_time'\n      }],\n      cardLists: [{\n        col: 4,\n        count: 0,\n        name: '活动参与人数（人）',\n        className: 'iconcanyurenshu',\n        type: true\n      }, {\n        col: 4,\n        count: 0,\n        name: '推广人数（人）',\n        className: 'icontuiguangrenshu',\n        type: true\n      }, {\n        col: 4,\n        count: 0,\n        name: '发起砍价数',\n        className: 'iconfaqirenshu',\n        type: true\n      }, {\n        col: 4,\n        count: 0,\n        name: '砍价成功数',\n        className: 'iconkanjiachenggong',\n        type: true\n      }, {\n        col: 4,\n        count: 0,\n        name: '支付订单额（元）',\n        className: 'iconzhifudingdan',\n        type: true\n      }, {\n        col: 4,\n        count: 0,\n        name: '支付人数（人）',\n        className: 'iconzhifurenshu',\n        type: true\n      }],\n      pagination: {\n        page: 1,\n        limit: 15,\n        real_name: '',\n        status: ''\n      },\n      type: 0,\n      columns2: [{\n        title: '用户ID',\n        key: 'uid',\n        width: 80\n      }, {\n        title: '用户头像',\n        slot: 'avatar'\n      }, {\n        title: '用户名称',\n        slot: 'nickname',\n        minWidth: 100\n      }, {\n        title: '砍价金额',\n        key: 'price'\n      }, {\n        title: '砍价时间',\n        key: 'add_time',\n        minWidth: 100\n      }]\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.id = this.$route.params.id;\n    this.getStatistics(this.id);\n    this.getList(this.id);\n  },\n  methods: {\n    // 统计\n    getStatistics: function getStatistics(id) {\n      var _this = this;\n\n      console.log(id);\n      getbargainStatistics(id).then(function (res) {\n        var arr = ['people_count', 'spread_count', 'start_count', 'success_count', 'pay_price', 'pay_count', 'pay_rate'];\n\n        _this.cardLists.map(function (i, index) {\n          i.count = res.data[arr[index]];\n        });\n      });\n    },\n    // 列表\n    getList: function getList(id) {\n      var _this2 = this;\n\n      this.loading = true;\n\n      if (this.type == 0) {\n        getbargainStatisticsPeople(this.id, this.pagination).then(function (res) {\n          _this2.loading = false;\n          var _res$data = res.data,\n              count = _res$data.count,\n              list = _res$data.list;\n          _this2.total = count;\n          _this2.tbody = list;\n        });\n      } else {\n        getbargainStatisticsOrder(this.id, this.pagination).then(function (res) {\n          _this2.loading = false;\n          var _res$data2 = res.data,\n              count = _res$data2.count,\n              list = _res$data2.list;\n          _this2.total = count;\n          _this2.tbody = list;\n        });\n      }\n    },\n    // 标签切换\n    onClickTab: function onClickTab() {\n      this.type = this.currentTab;\n      this.getList(this.id);\n    },\n    // 搜索\n    searchList: function searchList() {\n      this.pagination.page = 1;\n      this.getList(this.id);\n    },\n    // 分页\n    pageChange: function pageChange(index) {\n      this.pagination.page = index;\n      this.getList(this.id);\n    },\n    // 查看详情\n    Info: function Info(row) {\n      var _this3 = this;\n\n      this.modals = true;\n      this.rows = row;\n      bargainUserInfoApi(row.id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this3.tabList3 = data.list;\n                  _this3.loading = false;\n\n                case 3:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this3.loading = false;\n\n        _this3.$Message.error(res.msg);\n      });\n    }\n  }\n};", null]}