{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\utils\\assist.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\utils\\assist.js", "mtime": 1725352514139}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["import Vue from 'vue';\nvar isServer = Vue.prototype.$isServer; // 判断参数是否是其中之一\n\nexport function oneOf(value, validList) {\n  for (var i = 0; i < validList.length; i++) {\n    if (value === validList[i]) {\n      return true;\n    }\n  }\n\n  return false;\n}\nexport function camelcaseToHyphen(str) {\n  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n} // For Modal scrollBar hidden\n\nvar cached;\nexport function getScrollBarSize(fresh) {\n  if (isServer) return 0;\n\n  if (fresh || cached === undefined) {\n    var inner = document.createElement('div');\n    inner.style.width = '100%';\n    inner.style.height = '200px';\n    var outer = document.createElement('div');\n    var outerStyle = outer.style;\n    outerStyle.position = 'absolute';\n    outerStyle.top = 0;\n    outerStyle.left = 0;\n    outerStyle.pointerEvents = 'none';\n    outerStyle.visibility = 'hidden';\n    outerStyle.width = '200px';\n    outerStyle.height = '150px';\n    outerStyle.overflow = 'hidden';\n    outer.appendChild(inner);\n    document.body.appendChild(outer);\n    var widthContained = inner.offsetWidth;\n    outer.style.overflow = 'scroll';\n    var widthScroll = inner.offsetWidth;\n\n    if (widthContained === widthScroll) {\n      widthScroll = outer.clientWidth;\n    }\n\n    document.body.removeChild(outer);\n    cached = widthContained - widthScroll;\n  }\n\n  return cached;\n} // watch DOM change\n\nexport var MutationObserver = isServer ? false : window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver || false;\nvar SPECIAL_CHARS_REGEXP = /([\\:\\-\\_]+(.))/g;\nvar MOZ_HACK_REGEXP = /^moz([A-Z])/;\n\nfunction camelCase(name) {\n  return name.replace(SPECIAL_CHARS_REGEXP, function (_, separator, letter, offset) {\n    return offset ? letter.toUpperCase() : letter;\n  }).replace(MOZ_HACK_REGEXP, 'Moz$1');\n} // getStyle\n\n\nexport function getStyle(element, styleName) {\n  if (!element || !styleName) return null;\n  styleName = camelCase(styleName);\n\n  if (styleName === 'float') {\n    styleName = 'cssFloat';\n  }\n\n  try {\n    var computed = document.defaultView.getComputedStyle(element, '');\n    return element.style[styleName] || computed ? computed[styleName] : null;\n  } catch (e) {\n    return element.style[styleName];\n  }\n} // firstUpperCase\n\nfunction firstUpperCase(str) {\n  return str.toString()[0].toUpperCase() + str.toString().slice(1);\n}\n\nexport { firstUpperCase }; // Warn\n\nexport function warnProp(component, prop, correctType, wrongType) {\n  correctType = firstUpperCase(correctType);\n  wrongType = firstUpperCase(wrongType);\n  console.error(\"[iView warn]: Invalid prop: type check failed for prop \".concat(prop, \". Expected \").concat(correctType, \", got \").concat(wrongType, \". (found in component: \").concat(component, \")\")); // eslint-disable-line\n}\n\nfunction typeOf(obj) {\n  var toString = Object.prototype.toString;\n  var map = {\n    '[object Boolean]': 'boolean',\n    '[object Number]': 'number',\n    '[object String]': 'string',\n    '[object Function]': 'function',\n    '[object Array]': 'array',\n    '[object Date]': 'date',\n    '[object RegExp]': 'regExp',\n    '[object Undefined]': 'undefined',\n    '[object Null]': 'null',\n    '[object Object]': 'object'\n  };\n  return map[toString.call(obj)];\n} // deepCopy\n\n\nfunction deepCopy(data) {\n  var t = typeOf(data);\n  var o;\n\n  if (t === 'array') {\n    o = [];\n  } else if (t === 'object') {\n    o = {};\n  } else {\n    return data;\n  }\n\n  if (t === 'array') {\n    for (var i = 0; i < data.length; i++) {\n      o.push(deepCopy(data[i]));\n    }\n  } else if (t === 'object') {\n    for (var _i in data) {\n      o[_i] = deepCopy(data[_i]);\n    }\n  }\n\n  return o;\n}\n\nexport { deepCopy }; // scrollTop animation\n\nexport function scrollTop(el) {\n  var from = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var to = arguments.length > 2 ? arguments[2] : undefined;\n  var duration = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 500;\n  var endCallback = arguments.length > 4 ? arguments[4] : undefined;\n\n  if (!window.requestAnimationFrame) {\n    window.requestAnimationFrame = window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.msRequestAnimationFrame || function (callback) {\n      return window.setTimeout(callback, 1000 / 60);\n    };\n  }\n\n  var difference = Math.abs(from - to);\n  var step = Math.ceil(difference / duration * 50);\n\n  function scroll(start, end, step) {\n    if (start === end) {\n      endCallback && endCallback();\n      return;\n    }\n\n    var d = start + step > end ? end : start + step;\n\n    if (start > end) {\n      d = start - step < end ? end : start - step;\n    }\n\n    if (el === window) {\n      window.scrollTo(d, d);\n    } else {\n      el.scrollTop = d;\n    }\n\n    window.requestAnimationFrame(function () {\n      return scroll(d, end, step);\n    });\n  }\n\n  scroll(from, to, step);\n} // Find components upward\n\nfunction findComponentUpward(context, componentName, componentNames) {\n  if (typeof componentName === 'string') {\n    componentNames = [componentName];\n  } else {\n    componentNames = componentName;\n  }\n\n  var parent = context.$parent;\n  var name = parent.$options.name;\n\n  while (parent && (!name || componentNames.indexOf(name) < 0)) {\n    parent = parent.$parent;\n    if (parent) name = parent.$options.name;\n  }\n\n  return parent;\n}\n\nexport { findComponentUpward }; // Find component downward\n\nexport function findComponentDownward(context, componentName) {\n  var childrens = context.$children;\n  var children = null;\n\n  if (childrens.length) {\n    var _iteratorNormalCompletion = true;\n    var _didIteratorError = false;\n    var _iteratorError = undefined;\n\n    try {\n      for (var _iterator = childrens[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n        var child = _step.value;\n        var name = child.$options.name;\n\n        if (name === componentName) {\n          children = child;\n          break;\n        } else {\n          children = findComponentDownward(child, componentName);\n          if (children) break;\n        }\n      }\n    } catch (err) {\n      _didIteratorError = true;\n      _iteratorError = err;\n    } finally {\n      try {\n        if (!_iteratorNormalCompletion && _iterator.return != null) {\n          _iterator.return();\n        }\n      } finally {\n        if (_didIteratorError) {\n          throw _iteratorError;\n        }\n      }\n    }\n  }\n\n  return children;\n} // Find components downward\n\nexport function findComponentsDownward(context, componentName) {\n  var ignoreComponentNames = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n\n  if (!Array.isArray(ignoreComponentNames)) {\n    ignoreComponentNames = [ignoreComponentNames];\n  }\n\n  return context.$children.reduce(function (components, child) {\n    if (child.$options.name === componentName) components.push(child);\n\n    if (ignoreComponentNames.indexOf(child.$options.name) < 0) {\n      var foundChilds = findComponentsDownward(child, componentName);\n      return components.concat(foundChilds);\n    } else {\n      return components;\n    }\n  }, []);\n} // Find components upward\n\nexport function findComponentsUpward(context, componentName) {\n  var parents = [];\n  var parent = context.$parent;\n\n  if (parent) {\n    if (parent.$options.name === componentName) parents.push(parent);\n    return parents.concat(findComponentsUpward(parent, componentName));\n  } else {\n    return [];\n  }\n} // Find brothers components\n\nexport function findBrothersComponents(context, componentName) {\n  var exceptMe = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  var res = context.$parent.$children.filter(function (item) {\n    return item.$options.name === componentName;\n  });\n  var index = res.findIndex(function (item) {\n    return item._uid === context._uid;\n  });\n  if (exceptMe) res.splice(index, 1);\n  return res;\n}\n/* istanbul ignore next */\n\nvar trim = function trim(string) {\n  return (string || '').replace(/^[\\s\\uFEFF]+|[\\s\\uFEFF]+$/g, '');\n};\n/* istanbul ignore next */\n\n\nexport function hasClass(el, cls) {\n  if (!el || !cls) return false;\n  if (cls.indexOf(' ') !== -1) throw new Error('className should not contain space.');\n\n  if (el.classList) {\n    return el.classList.contains(cls);\n  } else {\n    return (' ' + el.className + ' ').indexOf(' ' + cls + ' ') > -1;\n  }\n}\n/* istanbul ignore next */\n\nexport function addClass(el, cls) {\n  if (!el) return;\n  var curClass = el.className;\n  var classes = (cls || '').split(' ');\n\n  for (var i = 0, j = classes.length; i < j; i++) {\n    var clsName = classes[i];\n    if (!clsName) continue;\n\n    if (el.classList) {\n      el.classList.add(clsName);\n    } else {\n      if (!hasClass(el, clsName)) {\n        curClass += ' ' + clsName;\n      }\n    }\n  }\n\n  if (!el.classList) {\n    el.className = curClass;\n  }\n}\n/* istanbul ignore next */\n\nexport function removeClass(el, cls) {\n  if (!el || !cls) return;\n  var classes = cls.split(' ');\n  var curClass = ' ' + el.className + ' ';\n\n  for (var i = 0, j = classes.length; i < j; i++) {\n    var clsName = classes[i];\n    if (!clsName) continue;\n\n    if (el.classList) {\n      el.classList.remove(clsName);\n    } else {\n      if (hasClass(el, clsName)) {\n        curClass = curClass.replace(' ' + clsName + ' ', ' ');\n      }\n    }\n  }\n\n  if (!el.classList) {\n    el.className = trim(curClass);\n  }\n}\nexport var dimensionMap = {\n  xs: '480px',\n  sm: '576px',\n  md: '768px',\n  lg: '992px',\n  xl: '1200px',\n  xxl: '1600px'\n};\nexport function setMatchMedia() {\n  if (typeof window !== 'undefined') {\n    var matchMediaPolyfill = function matchMediaPolyfill(mediaQuery) {\n      return {\n        media: mediaQuery,\n        matches: false,\n        on: function on() {},\n        off: function off() {}\n      };\n    };\n\n    window.matchMedia = window.matchMedia || matchMediaPolyfill;\n  }\n}\nexport var sharpMatcherRegx = /#([^#]+)$/;", null]}