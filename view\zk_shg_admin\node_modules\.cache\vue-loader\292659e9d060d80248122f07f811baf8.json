{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_news_roll.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_news_roll.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { mapMutations } from 'vuex'\n    export default {\n        name: 'c_news_roll',\n        componentsName: 'home_news_roll',\n        cname: '新闻公告',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        data () {\n            return {\n                hotIndex: 1,\n                configObj: {}, // 配置对象\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ], // 当前页面组件\n\t\t\t\trComContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'styleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleStyle'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'titleConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsixContentImg:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_upload_img,\n\t\t\t\t\t  configNme: 'imgConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsixContentTxt:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'titleTxtConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContent:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'rollConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleButton'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'buttonConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeContent:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'linkConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourContent:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleContent'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_product,\n\t\t\t\t\t    configNme: 'listConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfiveContent:[\n\t\t\t\t\t{\n\t\t\t\t\t  components: toolCom.c_input_item,\n\t\t\t\t\t  configNme: 'textConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'newsColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneBntStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bntColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'titleBgColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'titleColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'moduleColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsetUp:0,\n\t\t\t\ttype:0,\n\t\t\t\ttype2:0,\n\t\t\t\ttype3:0,\n\t\t\t\ttype4:0\n            }\n        },\n        watch: {\n            num (nVal) {\n                // debugger;\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n\t\t\t\t\tthis.setUp = nVal;\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n\t\t\t\t\t\tif(this.type==0){\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n                    } else {\n\t\t\t\t\t\tif(this.type==0){\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n                    }\n                },\n                deep: true\n            },\n\t\t\t'configObj.styleConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.titleConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type4 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tif(this.type==0){\n\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(this.type==0){\n\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.buttonConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tif(this.type==0){\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.oneContent,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentImg,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.threeContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.rComContent,...this.sixContentTxt,...this.twoContent,...this.fourContent]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(this.type==0){\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tif(this.type3){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type2==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.toneConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type3 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tif(this.type==0){\n\t\t\t\t\t\t\tif(this.type4==0){\n\t\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(nVal==0){\n\t\t\t\t\t\t\t\t\tif(nVal){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.oneBntStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tif(nVal){\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.twoStyle,...this.threeStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.fourStyle,...this.currencyStyle]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n            // 获取组件参数\n            getConfig (data) {\n\n            }\n        }\n    }\n", null]}