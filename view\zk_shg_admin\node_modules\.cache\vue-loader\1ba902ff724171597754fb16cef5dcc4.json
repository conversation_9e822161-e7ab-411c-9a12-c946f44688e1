{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue?vue&type=template&id=ed720464&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"库存管理\",\"width\":\"800\",\"footer-hide\":\"\",\"class-name\":\"vertical-center-modal\"},on:{\"on-cancel\":_vm.cancel},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('Card',{staticClass:\"cards\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[(_vm.specType)?_c('div',{staticClass:\"batch\"},[_c('div',{staticClass:\"name\",on:{\"click\":_vm.batchTap}},[_vm._v(\"批量\"),_c('span',{staticClass:\"iconfont iconxiayi\"})]),(_vm.batchShow)?_c('div',{staticClass:\"input acea-row row-center-wrapper\"},[_c('Input',{staticClass:\"width15\",attrs:{\"type\":\"number\"},on:{\"on-change\":_vm.inputTap},model:{value:(_vm.batchStock),callback:function ($$v) {_vm.batchStock=$$v},expression:\"batchStock\"}},[_c('Select',{staticStyle:{\"width\":\"60px\"},attrs:{\"slot\":\"append\"},on:{\"on-change\":_vm.batchStockTap},slot:\"append\",model:{value:(_vm.batchPm),callback:function ($$v) {_vm.batchPm=$$v},expression:\"batchPm\"}},[_c('Option',{attrs:{\"value\":1}},[_vm._v(\"入库\")]),_c('Option',{attrs:{\"value\":0}},[_vm._v(\"出库\")])],1)],1)],1):_vm._e()]):_vm._e(),_c('Table',{ref:\"selection\",attrs:{\"columns\":_vm.columns,\"border\":\"\",\"data\":_vm.stockData,\"loading\":_vm.loading,\"no-data-text\":\"暂无数据\",\"highlight-row\":\"\",\"no-filtered-data-text\":\"暂无筛选结果\",\"max-height\":\"450\"},scopedSlots:_vm._u([{key:\"image\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',{staticClass:\"product-data\"},[_c('img',{staticClass:\"image\",attrs:{\"src\":row.image}})])]}},{key:\"num\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',{staticClass:\"acea-row row-middle\"},[_c('Input',{staticClass:\"width15\",attrs:{\"type\":\"number\"},on:{\"on-change\":function($event){return _vm.changeTap(row)}},model:{value:(row.changeNum),callback:function ($$v) {_vm.$set(row, \"changeNum\", $$v)},expression:\"row.changeNum\"}},[_c('Select',{staticStyle:{\"width\":\"60px\"},attrs:{\"slot\":\"append\"},on:{\"on-change\":function($event){return _vm.stockTap(row)}},slot:\"append\",model:{value:(row.pm),callback:function ($$v) {_vm.$set(row, \"pm\", $$v)},expression:\"row.pm\"}},[_c('Option',{attrs:{\"value\":1}},[_vm._v(\"入库\")]),_c('Option',{attrs:{\"value\":0}},[_vm._v(\"出库\")])],1)],1),_c('span',{staticClass:\"ml20\"},[_vm._v(\"=\"+_vm._s(row.resultNum))])],1)]}}])}),_c('div',{staticClass:\"footer acea-row row-right\"},[_c('Button',{staticClass:\"mr\",on:{\"click\":_vm.cancel}},[_vm._v(\"取消\")]),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.productSaveStocks}},[_vm._v(\"提交\")])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}