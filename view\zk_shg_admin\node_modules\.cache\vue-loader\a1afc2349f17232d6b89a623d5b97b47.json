{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addPiecesDiscount\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addPiecesDiscount\\index.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState,mapMutations } from \"vuex\";\nimport {\n  saveDiscount,\n  discountInfo\n} from \"@/api/marketing\";\nimport {\n  userLabelAddApi\n} from \"@/api/user\";\nimport { brandList } from \"@/api/product\";\nimport storeList from \"@/components/storeList\";\nimport userLabel from \"@/components/labelList\";\nimport goodsList from '@/components/goodsList'\nimport storeLabelList from \"@/components/storeLabelList\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"addPiecesDiscount\",\n  components: {\n    userLabel,\n    goodsList,\n    storeLabelList,\n    storeList\n  },\n  data(){\n    return {\n      storesList:[],\n      storeModals: false,\n      roterPre: Setting.roterPre,\n      storeDataLabel: [],\n      storeLabelShow: false,\n      props: { emitPath: false, multiple: true },\n      brandData: [],\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      modals: false,\n      tableData: [],\n      headTab: [\n        {\n          name: \"基础设置\",\n          type: '1',\n        },\n        {\n          name: \"添加商品\",\n          type: '2',\n        },\n        {\n          name: \"适用门店\",\n          type: '3',\n        }\n      ],\n      discountType: [\n        { name: \"第2件半价\", title: \"例：甜筒第2件半价\", id: 1 },\n        { name: \"买1送1\", title: \"例：T恤买1送1\", id: 2 },\n        { name: \"自定义优惠\", title: \"例：第3件打5折\", id: 3 }\n      ],\n      activityType:[\n        {\n          name:'优惠券',\n          type:'5'\n        },\n        {\n          name:'满减满折',\n          type:'3'\n        },\n        {\n          name:'限时折扣',\n          type:'1'\n        },\n        // {\n        // \tname:'满送活动',\n        // \ttype:'4'\n        // }\n      ],\n      currentTab: '1',\n      dataLabel:[],\n      labelShow:false,\n      formValidate: {\n        applicable_type: 1,\n        name:'',\n        section_time:[],\n        discount: 50,\n        threshold: 2,\n        is_label:0,\n        label_id: [],\n        is_overlay:'0',\n        overlay:[],\n        product_partake_type:1,\n        product_id:[],\n        n_piece_n_discount:1,\n        brand_id: [],\n        store_label_id: []\n      },\n      ruleValidate: {\n        name: [\n          { required: true, message: '请输入活动名称', trigger: 'blur' }\n        ],\n        section_time: [\n          { required: true, type: 'array', message: '请选择活动时间', trigger: 'change' }\n        ],\n        is_overlay: [\n          { required: true, message: '请设置优惠叠加', trigger: 'change' }\n        ],\n        threshold: [\n          { type: 'number', required: true, message: '请填写优惠设置', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n    labelBottom() {\n      return this.isMobile ? undefined : 15;\n    },\n  },\n  mounted(){\n    this.setCopyrightShow({ value: false });\n    if(this.$route.params.id != 0){\n      this.getDiscountInfo();\n    }\n  },\n  destroyed () {\n    this.setCopyrightShow({ value: true });\n  },\n  methods: {\n    ...mapMutations('admin/layout', [\n      'setCopyrightShow'\n    ]),\n    //删除门店\n    delte(row){\n      this.storesList.forEach((item,index)=>{\n        if(row.id == item.id){\n          this.storesList.splice(index, 1)\n        }\n      })\n    },\n    //添加门店\n    addStore(){\n      this.storeModals = true;\n    },\n    //关闭门店弹窗\n    cancelStore(){\n      this.storeModals = false;\n    },\n    getStoreId (data) {\n      this.storeModals = false;\n      let list = this.storesList.concat(data);\n      let uni = this.unique(list);\n      this.storesList = uni;\n    },\n    closeStoreLabel(label){\n      let index = this.storeDataLabel.indexOf(this.storeDataLabel.filter(d=>d.id == label.id)[0]);\n      this.storeDataLabel.splice(index,1);\n    },\n    openStoreLabel() {\n      this.storeLabelShow = true;\n      this.$refs.storeLabel.storeLabel(\n          JSON.parse(JSON.stringify(this.storeDataLabel))\n      );\n    },\n    activeStoreData(storeDataLabel) {\n      this.storeLabelShow = false;\n      this.storeDataLabel = storeDataLabel;\n    },\n    // 标签弹窗关闭\n    storeLabelClose() {\n      this.storeLabelShow = false;\n    },\n    goodTap(e){\n      if(e==4){\n        this.getBrandList();\n      }\n    },\n    // 品牌列表\n    getBrandList(){\n      brandList().then(res=>{\n        this.brandData = res.data\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n      })\n    },\n    discountTypeTap(item){\n      this.formValidate.n_piece_n_discount = item.id;\n      if(item.id == 1){\n        this.formValidate.threshold = 2;\n        this.formValidate.discount = 50;\n      }else if(item.id == 2){\n        this.formValidate.threshold = 2;\n        this.formValidate.discount = 0;\n      }else{\n        this.formValidate.threshold = 1;\n        this.formValidate.discount = 100;\n      }\n    },\n    getDiscountInfo(){\n      this.formValidate.n_piece_n_discount = 0\n      discountInfo(this.$route.params.id).then(res=>{\n        this.formValidate = res.data.info;\n        this.storesList = res.data.info.stores || [];\n        this.formValidate.discount = parseFloat(res.data.info.discount)\n        this.formValidate.threshold = parseFloat(res.data.info.threshold)\n        this.formValidate.is_overlay = this.formValidate.is_overlay.toString();\n        this.tableData = res.data.info.products\n        this.dataLabel = res.data.info.label_id;\n        this.storeDataLabel = res.data.info.store_label_id;\n        if(res.data.info.product_partake_type == 4){\n          this.getBrandList();\n        }\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n      })\n    },\n    del(row){\n      this.tableData.forEach((i,index)=>{\n        if(row.id == i.id){\n          return this.tableData.splice(index, 1)\n        }else{\n          i.attrValue.forEach((j,indexn)=>{\n            if(row.id == j.id){\n              if(i.attrValue.length == 1){\n                return this.tableData.splice(index, 1)\n              }else{\n                return i.attrValue.splice(indexn, 1)\n              }\n            }\n          })\n        }\n      })\n    },\n    addLabel(){\n      this.$modalForm(userLabelAddApi(0)).then(() => {});\n    },\n    addGoods(){\n      this.modals = true;\n    },\n    cancel () {\n      this.modals = false;\n    },\n    //对象数组去重；\n    unique(arr) {\n      const res = new Map();\n      return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1))\n    },\n    getProductId (data) {\n      this.modals = false;\n      let list = this.tableData.concat(data);\n      let uni = this.unique(list);\n      uni.forEach((i)=>{\n        i.attrValue.forEach(j=>{\n          j.cate_name = i.cate_name;\n          j.store_label = i.store_label;\n        })\n      })\n      this.tableData = uni;\n    },\n    // 具体日期\n    onchangeTime (e) {\n      this.formValidate.section_time = e;\n    },\n    closeLabel(label){\n      let index = this.dataLabel.indexOf(this.dataLabel.filter(d=>d.id == label.id)[0]);\n      this.dataLabel.splice(index,1);\n    },\n    activeData(dataLabel){\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    openLabel(row) {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    // 标签弹窗关闭\n    labelClose() {\n      this.labelShow = false;\n    },\n    // 上一页：\n    upTab() {\n      if(this.currentTab==1&&this.formValidate.product_type!=0){\n        this.currentTab = (Number(this.currentTab) - 2).toString();\n      }else{\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    // 下一页；\n    downTab(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if(!this.formValidate.threshold){\n            return this.$Message.warning(\"请填写优惠设置的件数\");\n          }\n          if(this.formValidate.discount == null){\n            return this.$Message.warning(\"请填写优惠设置的折扣\");\n          }\n          if(this.formValidate.is_label && !this.dataLabel.length){\n            return this.$Message.warning(\"请选择用户关联标签\");\n          }\n          if(parseInt(this.formValidate.is_overlay) && !this.formValidate.overlay.length){\n            return this.$Message.warning(\"请选择叠加的营销活动\");\n          }\n          if(this.formValidate.section_time[0] == ''){\n            return this.$Message.warning(\"请选择活动时间\");\n          }\n          this.currentTab = (Number(this.currentTab) + 1).toString();\n        }else{\n          this.$Message.warning(\"请完善数据\");\n        }\n      })\n    },\n    handleSubmit(name){\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if(!this.formValidate.threshold){\n            return this.$Message.warning(\"请填写优惠设置\");\n          }\n          if(this.formValidate.is_label && !this.dataLabel.length){\n            return this.$Message.warning(\"请选择用户关联标签\");\n          }\n          if(parseInt(this.formValidate.is_overlay) && !this.formValidate.overlay.length){\n            return this.$Message.warning(\"请选择叠加的营销活动\");\n          }\n          if(this.formValidate.section_time[0] == ''){\n            return this.$Message.warning(\"请选择活动时间\");\n          }\n          if(this.formValidate.product_partake_type == 4 && !this.formValidate.brand_id.length){\n            return this.$Message.error('请添加指定品牌');\n          }\n          if(this.formValidate.product_partake_type == 5){\n            let labelIds = [];\n            this.storeDataLabel.forEach((item)=>{\n              labelIds.push(item.id)\n            });\n            if(!labelIds.length){\n              return this.$Message.error('请添加指定标签');\n            }\n            this.formValidate.store_label_id = labelIds\n          }\n          // 用户标签\n          let activeIds = [];\n          this.dataLabel.forEach((item)=>{\n            activeIds.push(item.id)\n          });\n          if(this.formValidate.is_label==0){\n            this.formValidate.label_id = [];\n          }else{\n            this.formValidate.label_id = activeIds\n          }\n          let product_id = [];\n          this.tableData.forEach((item)=>{\n            let obj = {\n              product_id: item.id,\n              unique:[]\n            }\n            if(item.attrValue.length){\n              item.attrValue.forEach((j)=>{\n                obj.unique.push(j.unique)\n              })\n            }\n            product_id.push(obj)\n          })\n          if(this.formValidate.product_partake_type == 2 && !product_id.length){\n            return this.$Message.error('请添加商品');\n          }\n          this.formValidate.product_id = product_id\n          let storeId = []\n          this.storesList.forEach(item=>{\n            storeId.push(item.id)\n          })\n          if(this.formValidate.applicable_type==2 && !storeId.length){\n            return this.$Message.warning('请添加适用门店');\n          }\n          this.formValidate.applicable_store_id = storeId;\n          saveDiscount(2,this.$route.params.id,this.formValidate).then(res=>{\n            this.$router.push({ path: `${this.roterPre}/marketing/discount/pieces_discount` });\n            this.$Message.success(res.msg)\n          }).catch(err=>{\n            this.$Message.error(err.msg);\n          })\n        }else{\n          this.$Message.warning(\"请完善数据\");\n        }\n      })\n    }\n  }\n}\n", null]}