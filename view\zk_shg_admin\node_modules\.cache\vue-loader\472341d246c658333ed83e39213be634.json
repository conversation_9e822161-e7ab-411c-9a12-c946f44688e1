{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\splitList.vue?vue&type=template&id=3ce73bd8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\splitList.vue", "mtime": 1733822761343}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('div',{staticClass:\"i-layout-page-header\"},[_c('PageHeader',{staticClass:\"product_tabs\",attrs:{\"title\":\"子订单列表\",\"hidden-breadcrumb\":\"\"}})],1),_c('Table',{ref:\"table\",staticClass:\"orderData mt25\",attrs:{\"columns\":_vm.columns,\"data\":_vm.orderList,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-data-text\":\"暂无数据\",\"no-filtered-data-text\":\"暂无筛选结果\"},on:{\"on-selection-change\":_vm.onSelectTab,\"on-select-all\":_vm.selectAll,\"on-select-all-cancel\":_vm.selectAll,\"on-select-cancel\":_vm.onSelectCancel},scopedSlots:_vm._u([{key:\"order_id\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('span',{staticStyle:{\"display\":\"block\"},domProps:{\"textContent\":_vm._s(row.order_id)}}),_c('span',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.is_del === 1),expression:\"row.is_del === 1\"}],staticClass:\"span-del\"},[_vm._v(\"用户已删除\")])]}},{key:\"nickname\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.showUserInfo(row)}}},[_vm._v(_vm._s(row.nickname))])]}},{key:\"info\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn _vm._l((row._info),function(val,i){return _c('div',{key:i,staticClass:\"tabBox\"},[_c('div',{directives:[{name:\"viewer\",rawName:\"v-viewer\"}],staticClass:\"tabBox_img\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(\n                val.cart_info.productInfo.attrInfo\n                  ? val.cart_info.productInfo.attrInfo.image\n                  : val.cart_info.productInfo.image\n              ),expression:\"\\n                val.cart_info.productInfo.attrInfo\\n                  ? val.cart_info.productInfo.attrInfo.image\\n                  : val.cart_info.productInfo.image\\n              \"}]})]),_c('span',{staticClass:\"tabBox_tit\"},[_vm._v(\"\\n            \"+_vm._s(val.cart_info.productInfo.store_name + \" | \")+_vm._s(val.cart_info.productInfo.attrInfo\n                ? val.cart_info.productInfo.attrInfo.suk\n                : \"\")+\"\\n          \")]),_c('span',{staticClass:\"tabBox_pice\"},[_vm._v(_vm._s(\"￥\" + val.cart_info.truePrice + \" x \" + val.cart_info.cart_num))])])})}},{key:\"statusName\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('div',{staticClass:\"pt5\",domProps:{\"innerHTML\":_vm._s(row.status_name.status_name)}}),_c('div',{staticClass:\"pictrue-box\"},_vm._l((row.status_name.pics || []),function(item,index){return (row.status_name.pics)?_c('div',{directives:[{name:\"viewer\",rawName:\"v-viewer\"}],key:index},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(item),expression:\"item\"}],staticClass:\"pictrue mr10\",attrs:{\"src\":item}})]):_vm._e()}),0)]}},{key:\"action\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [(row._status === 1)?_c('a',{on:{\"click\":function($event){return _vm.edit(row)}}},[_vm._v(\"编辑\")]):_vm._e(),(\n            (row._status === 2 || row._status === 8) &&\n            row.shipping_type === 1 &&\n            (row.pinkStatus === null || row.pinkStatus === 2)\n          )?_c('a',{on:{\"click\":function($event){return _vm.sendOrder(row)}}},[_vm._v(\"发送货\")]):_vm._e(),(row._status === 4)?_c('a',{on:{\"click\":function($event){return _vm.delivery(row)}}},[_vm._v(\"配送信息\")]):_vm._e(),(\n            row.shipping_type == 2 &&\n            row.status == 0 &&\n            row.paid == 1 &&\n            row.refund_status === 0\n          )?_c('a',{on:{\"click\":function($event){return _vm.bindWrite(row)}}},[_vm._v(\"立即核销\")]):_vm._e(),(row._status === 2 || row._status === 8)?_c('Divider',{attrs:{\"type\":\"vertical\"}}):_vm._e(),(row._status === 8)?_c('a',{on:{\"click\":function($event){return _vm.splitOrderDetail(row)}}},[_vm._v(\"查看子订单\")]):_vm._e(),(\n            row._status === 1 ||\n            ((row._status === 2 || row._status === 8) &&\n              (row.pinkStatus === null || row.pinkStatus === 2)) ||\n            row._status === 4 ||\n            (row.shipping_type == 2 &&\n              row.status == 0 &&\n              row.paid == 1 &&\n              row.refund_status === 0)\n          )?_c('Divider',{attrs:{\"type\":\"vertical\"}}):_vm._e(),[_c('Dropdown',{on:{\"on-click\":function($event){return _vm.changeMenu(row, $event)}}},[_c('a',{attrs:{\"href\":\"javascript:void(0)\"}},[_vm._v(\"\\n              更多\\n              \"),_c('Icon',{attrs:{\"type\":\"ios-arrow-down\"}})],1),_c('DropdownMenu',{attrs:{\"slot\":\"list\"},slot:\"list\"},[_c('DropdownItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(\n                  row._status === 1 &&\n                  row.paid === 0 &&\n                  row.pay_type === 'offline'\n                ),expression:\"\\n                  row._status === 1 &&\\n                  row.paid === 0 &&\\n                  row.pay_type === 'offline'\\n                \"}],ref:\"ones\",attrs:{\"name\":\"1\"}},[_vm._v(\"立即支付\")]),_c('DropdownItem',{attrs:{\"name\":\"2\"}},[_vm._v(\"订单详情\")]),_c('DropdownItem',{attrs:{\"name\":\"3\"}},[_vm._v(\"订单记录\")]),_c('DropdownItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(row._status >= 3 && row.express_dump),expression:\"row._status >= 3 && row.express_dump\"}],attrs:{\"name\":\"11\"}},[_vm._v(\"电子面单打印\")]),_c('DropdownItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(row._status >= 2),expression:\"row._status >= 2\"}],attrs:{\"name\":\"10\"}},[_vm._v(\"小票打印\")]),_c('DropdownItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(\n                  row._status !== 1 ||\n                  (row._status === 3 &&\n                    row.use_integral > 0 &&\n                    row.use_integral >= row.back_integral)\n                ),expression:\"\\n                  row._status !== 1 ||\\n                  (row._status === 3 &&\\n                    row.use_integral > 0 &&\\n                    row.use_integral >= row.back_integral)\\n                \"}],attrs:{\"name\":\"4\"}},[_vm._v(\"订单备注\")]),_c('DropdownItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.refund_type != 2 && row.refund_type != 4 && row.refund_type != 6 && row.paid==1 && row.refund_status !==2 && parseFloat(row.pay_price) > 0),expression:\"row.refund_type != 2 && row.refund_type != 4 && row.refund_type != 6 && row.paid==1 && row.refund_status !==2 && parseFloat(row.pay_price) > 0\"}],attrs:{\"name\":\"5\"}},[_vm._v(\"立即退款\")]),_c('DropdownItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.refund_type == 2),expression:\"row.refund_type == 2\"}],attrs:{\"name\":\"55\"}},[_vm._v(\"同意退货\")]),_c('DropdownItem',{directives:[{name:\"show\",rawName:\"v-show\",value:(row._status === 4),expression:\"row._status === 4\"}],attrs:{\"name\":\"8\"}},[_vm._v(\"已收货\")]),(row.is_del == 1)?_c('DropdownItem',{attrs:{\"name\":\"9\"}},[_vm._v(\"删除订单\")]):_vm._e()],1)],1)]]}}])}),_c('div',{staticClass:\"acea-row row-right page\"}),_c('edit-from',{ref:\"edits\",attrs:{\"FromData\":_vm.FromData},on:{\"submitFail\":_vm.submitFail}}),_c('user-details',{ref:\"userDetails\"}),_c('details-from',{ref:\"detailss\",attrs:{\"orderDatalist\":_vm.orderDatalist,\"orderId\":_vm.orderId}}),_c('order-remark',{ref:\"remarks\",attrs:{\"orderId\":_vm.orderId},on:{\"submitFail\":_vm.submitFail}}),_c('order-record',{ref:\"record\"}),_c('order-send',{ref:\"send\",attrs:{\"orderId\":_vm.orderId,\"status\":_vm.status},on:{\"submitFail\":_vm.submitFail}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}