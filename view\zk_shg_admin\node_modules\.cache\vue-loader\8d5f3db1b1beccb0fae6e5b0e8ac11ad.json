{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableFrom.vue?vue&type=template&id=0f6a6c14&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableFrom.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"table_box\">\n  <Form\n    ref=\"orderData\"\n    inline\n    :model=\"orderData\"\n    :label-width=\"labelWidth\"\n    :label-position=\"labelPosition\"\n    class=\"tabform\"\n    @submit.native.prevent\n  >\n    <FormItem label=\"订单状态：\">\n      <Select\n        v-model=\"orderData.status\"\n        placeholder=\"请选择\"\n        clearable\n        @on-change=\"selectChange2(orderData.status)\"\n       class=\"width25\"\n      >\n        <Option value=\"\">全部</Option>\n        <Option value=\"1\">未发货</Option>\n        <Option value=\"2\">待收货</Option>\n        <Option value=\"3\">交易完成</Option>\n      </Select>\n    </FormItem>\n    <FormItem label=\"创建时间：\">\n      <DatePicker\n        :editable=\"false\"\n        @on-change=\"onchangeTime\"\n        :value=\"timeVal\"\n        format=\"yyyy/MM/dd HH:mm:ss\"\n        type=\"datetimerange\"\n        placement=\"bottom-start\"\n        placeholder=\"自定义时间\"\n        class=\"mr20 width30\"\n        :options=\"options\"\n      ></DatePicker>\n    </FormItem>\n    <FormItem label=\"搜索：\" prop=\"real_name\" label-for=\"real_name\">\n      <Input\n        v-model=\"orderData.real_name\"\n        placeholder=\"请输入\"\n        element-id=\"name\"\n       class=\"width25\"\n      >\n        <!--@on-search=\"orderSearch(orderData.real_name)\"-->\n        <Select\n          v-model=\"orderData.field_key\"\n          slot=\"prepend\"\n          style=\"width: 80px\"\n        >\n          <Option value=\"all\">全部</Option>\n          <Option value=\"order_id\">订单号</Option>\n          <Option value=\"uid\">UID</Option>\n          <Option value=\"real_name\">用户姓名</Option>\n          <Option value=\"user_phone\">用户电话</Option>\n          <Option value=\"store_name\">商品名称(模糊)</Option>\n        </Select>\n      </Input>\n    </FormItem>\n    <FormItem>\n      <Button type=\"primary\" @click=\"orderSearch\" class=\"mr14\">查询</Button>\n      <Button class=\"export\" @click=\"exports\">导出</Button>\n    </FormItem>\n  </Form>\n</div>\n", null]}