{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\clientGroupInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\clientGroupInfo.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport cardsData from \"@/components/cards/cards\";\nimport { workGroupTemplateInfo, groupTemplateMemberList, groupTemplateClientList, workGroupTemplateSendMsg } from \"@/api/work\";\nimport { formatDate as _formatDate } from '@/utils/validate';\nimport Setting from \"@/setting\";\nexport default {\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      cardLists: [],\n      optionData: {},\n      style: {\n        height: \"400px\"\n      },\n      spinShow: false,\n      tableColumn: [{\n        title: \"成员\",\n        key: \"name\",\n        minWidth: 100\n      }, {\n        title: \"发送状态\",\n        slot: \"status\",\n        minWidth: 100\n      }, {\n        title: \"发送数目\",\n        slot: \"sendResult\",\n        minWidth: 100\n      }, {\n        title: \"发送时间\",\n        slot: \"send_time\",\n        minWidth: 100\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        minWidth: 100\n      }],\n      tableColumn1: [{\n        title: \"客户\",\n        key: \"name\",\n        minWidth: 100\n      }, {\n        title: \"发送状态\",\n        slot: \"status1\",\n        minWidth: 100\n      }, {\n        title: \"发送时间\",\n        slot: \"send_time\",\n        minWidth: 100\n      }],\n      tabIndex: 0,\n      tableData: [],\n      userLoading: false,\n      timeVal: [],\n      tableForm: {\n        page: 1,\n        limit: 15,\n        client_name: \"\",\n        //客户名称\n        user_name: \"\",\n        //成员名称\n        status: \"\",\n        status1: \"\",\n        id: 0\n      }\n    };\n  },\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  components: {\n    cardsData: cardsData\n  },\n  mounted: function mounted() {\n    this.tableForm.id = this.$route.params.id;\n    this.getData();\n    this.getMemberList(); // this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n\n      this.userLoading = true;\n      workGroupStatisticsList(this.tableForm).then(function (res) {\n        _this.tableData = res.data;\n        _this.userLoading = false;\n      }).catch(function (err) {\n        _this.userLoading = false;\n\n        _this.$Message.error(err.msg);\n      });\n    },\n    getData: function getData() {\n      var _this2 = this;\n\n      workGroupTemplateInfo(this.$route.params.id).then(function (res) {\n        _this2.cardLists = [{\n          col: 6,\n          count: res.data.user_count,\n          name: \"已发送成员\",\n          type: 1,\n          className: \"iconjinrixinzeng\"\n        }, {\n          col: 6,\n          count: res.data.external_user_count,\n          name: \"送达客户\",\n          type: 1,\n          className: \"iconjinrituiqun\"\n        }, {\n          col: 6,\n          count: res.data.unuser_count,\n          name: \"未发送成员\",\n          type: 1,\n          className: \"icondangqianqunchengyuan\"\n        }, {\n          col: 6,\n          count: res.data.external_unuser_count,\n          name: \"未送达客户\",\n          type: 1,\n          className: \"iconleijituiqun\"\n        }];\n      });\n    },\n    getMemberList: function getMemberList() {\n      var _this3 = this;\n\n      groupTemplateMemberList(this.tableForm.id, {\n        user_name: this.tableForm.user_name,\n        status: this.tableForm.status,\n        page: this.tableForm.page,\n        limit: this.tableForm.limit\n      }).then(function (res) {\n        _this3.tableData = res.data;\n      });\n    },\n    getClientList: function getClientList() {\n      var _this4 = this;\n\n      groupTemplateClientList(this.tableForm.id, {\n        client_name: this.tableForm.user_name,\n        status: this.tableForm.status1,\n        page: this.tableForm.page,\n        limit: this.tableForm.limit\n      }).then(function (res) {\n        _this4.tableData = res.data;\n      });\n    },\n    sendMsg: function sendMsg(row, index) {\n      var _this5 = this;\n\n      workGroupTemplateSendMsg({\n        userid: row.userid,\n        time: row.create_time,\n        id: \"\"\n      }).then(function (res) {\n        _this5.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this5.$Message.error(err.msg);\n      });\n    },\n    onChangeType: function onChangeType() {\n      if (this.tabIndex == 0) {\n        this.getMemberList();\n      } else {\n        this.getClientList();\n      }\n    },\n    search: function search() {\n      if (this.tabIndex == 0) {\n        this.getMemberList();\n      } else {\n        this.getClientList();\n      }\n    },\n    pageChange: function pageChange(index) {\n      this.tableForm.page = index;\n\n      if (this.tabIndex == 0) {\n        this.getMemberList();\n      } else {\n        this.getClientList();\n      }\n    }\n  }\n};", null]}