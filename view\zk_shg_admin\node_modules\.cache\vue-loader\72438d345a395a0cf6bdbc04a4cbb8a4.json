{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue?vue&type=template&id=1cf002c8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue", "mtime": 1716618646000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"onePictrue\"},[_c('div',{staticClass:\"info\"},[_vm._v(\"建议：请先选择图片，图片宽度750px，高度不限\")]),_c('div',{staticClass:\"pictrues\"},[(_vm.configData.url)?_c('img',{attrs:{\"src\":_vm.configData.url}}):_c('div',{staticClass:\"emptyBox\"},[_vm._v(\"750*高度不限\")])]),_c('div',{staticClass:\"uploadImg\"},[_c('div',{staticClass:\"name\"},[_vm._v(\"图片\")]),_c('div',{staticClass:\"picTxt\"},[_c('div',{staticClass:\"box\",on:{\"click\":function($event){return _vm.modalPicTap('单选')}}},[(_vm.configData.url)?_c('div',{staticClass:\"pictrue acea-row row-center-wrapper\"},[_c('img',{attrs:{\"src\":_vm.configData.url,\"alt\":\"\"}}),_c('div',{staticClass:\"iconfont icondel_1\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete($event)}}})]):_c('div',{staticClass:\"upload-box\"},[_c('Icon',{attrs:{\"type\":\"ios-add\",\"size\":\"50\"}})],1)]),_c('div',{staticClass:\"tip\"},[_vm._v(_vm._s(_vm.configData.info))])])]),_c('div',{staticClass:\"bnt\",on:{\"click\":_vm.openFloorModal}},[_vm._v(\"+ 编辑热区\")]),_c('div',[_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":'上传图片',\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1),_c('OperationFloorModal',{ref:\"hotpot\",attrs:{\"imgs\":_vm.configData.url,\"img-area-data\":_vm.imgAreaData},on:{\"delAreaData\":_vm.handleAreaData,\"saveAreaData\":_vm.handleAreaData}})],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}