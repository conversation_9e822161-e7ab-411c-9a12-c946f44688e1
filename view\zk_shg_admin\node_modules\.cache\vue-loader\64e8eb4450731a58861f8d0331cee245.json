{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\group\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\group\\index.vue", "mtime": 1683594784000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nimport { userGroupApi, groupAddApi } from '@/api/user';\nexport default {\n    name: \"user_group\",\n    data() {\n        return {\n            grid: {\n                xl: 7,\n                lg: 7,\n                md: 12,\n                sm: 24,\n                xs: 24\n            },\n            loading: false,\n            columns1: [\n                {\n                    title: 'ID',\n                    key: 'id',\n                    width: 80\n                },\n                {\n                    title: '分组名称',\n                    key: 'group_name',\n                    minWidth: 600\n                },\n                {\n                    title: '操作',\n                    slot: 'action',\n                    fixed: 'right',\n                    minWidth: 120,\n                    maxWidth: 140\n                }\n            ],\n            groupFrom: {\n                page: 1,\n                limit: 10\n            },\n            groupLists: [],\n            total:0\n        }\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 75;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'left';\n        }\n    },\n    created () {\n        this.getList();\n    },\n    methods:{\n        // 添加\n        add () {\n            this.$modalForm(groupAddApi(0)).then(() => this.getList());\n        },\n        // 分组列表\n        getList () {\n            this.loading = true;\n            userGroupApi(this.groupFrom).then(async res => {\n                let data = res.data;\n                this.groupLists = data.list;\n                this.total = data.count;\n                this.loading = false;\n            }).catch(res => {\n                this.loading = false;\n                this.$Message.error(res.msg);\n            })\n        },\n        pageChange (index) {\n            this.groupFrom.page = index;\n            this.getList();\n        },\n        //修改\n        edit(id){\n            this.$modalForm(groupAddApi(id)).then(() => this.getList());\n        },\n        // 删除\n        del (row, tit, num) {\n            let delfromData = {\n                title: tit,\n                num: num,\n                url: `user/user_group/del/${row.id}`,\n                method: 'DELETE',\n                ids: ''\n            };\n            this.$modalSure(delfromData).then((res) => {\n                this.$Message.success(res.msg);\n                this.groupLists.splice(num, 1);\n                if (!this.groupLists.length) {\n                  this.groupFrom.page =\n                      this.groupFrom.page == 1 ? 1 : this.groupFrom.page - 1;\n                }\n                this.getList();\n            }).catch(res => {\n                this.$Message.error(res.msg);\n            });\n        },\n    }\n}\n", null]}