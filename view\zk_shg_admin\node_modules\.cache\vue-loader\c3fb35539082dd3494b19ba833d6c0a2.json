{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue", "mtime": 1717062347000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import vuedraggable from 'vuedraggable'\n    import linkaddress from '@/components/linkaddress';\n\n    export default {\n        name: 'c_tab_list',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            linkaddress,\n            draggable: vuedraggable\n        },\n        data () {\n            return {\n                defaults: {},\n                configData: {},\n                itemObj: {},\n                activeIndex: 0\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj\n                this.configData = this.configObj[this.configNme]\n            })\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n                },\n                deep: true\n            }\n        },\n        methods: {\n            linkUrl(e){\n\t\t\t\tif(this.configData.list[this.activeIndex].dataType.tabVal){\n\t\t\t\t\tlet obj = e.split('?')[1];\n\t\t\t\t\tlet obj2 = obj.split('&');\n\t\t\t\t\tthis.configData.list[this.activeIndex].classPage.name = obj2[1].split('=')[1];\n\t\t\t\t\tthis.configData.list[this.activeIndex].classPage.id = obj2[0].split('=')[1]\n\t\t\t\t}else{\n\t\t\t\t\tlet obj = e.split('?')[1];\n\t\t\t\t\tlet obj2 = obj.split('&')\n\t\t\t\t\tthis.configData.list[this.activeIndex].microPage.name = obj2[1].split('=')[1];\n\t\t\t\t\tthis.configData.list[this.activeIndex].microPage.id = obj2[0].split('=')[1];\n\t\t\t\t}\n            },\n            getLink (index){\n                this.activeIndex = index;\n\t\t\t\tlet obj = {}\n\t\t\t\tif(this.configData.list[this.activeIndex].dataType.tabVal){\n\t\t\t\t\tobj = {\n\t\t\t\t\t\tid: 8,\n\t\t\t\t\t\tpid: 2,\n\t\t\t\t\t\ttype: \"product_category\"\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tobj = {\n\t\t\t\t\t\tid: 7,\n\t\t\t\t\t\tpid: 1,\n\t\t\t\t\t\ttype: \"special\"\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$refs.linkaddres.handleCheckChange('',obj)\n                this.$refs.linkaddres.modals = true\n            },\n            addHotTxt () {\n                if (this.configData.list.length == 0) {\n                    let storage = window.localStorage;\n                    this.itemObj = JSON.parse(storage.getItem('itemObj'));\n\t\t\t\t\tthis.itemObj.dataType.tabVal = 0;\n                    this.itemObj.microPage.name='';\n                    this.itemObj.classPage.name='';\n                    this.configData.list.push(this.itemObj)\n                } else {\n                    let obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]));\n                    obj.dataType.tabVal = 0;\n                    obj.microPage.name='';\n                    obj.classPage.name='';\n                    this.configData.list.push(obj)\n                }\n            },\n            // 删除数组\n            bindDelete (index) {\n                if (this.configData.list.length == 1) {\n                    let itemObj = this.configData.list[0];\n                    this.itemObj = itemObj;\n                    let storage = window.localStorage;\n                    storage.setItem('itemObj', JSON.stringify(itemObj));\n                }\n                this.configData.list.splice(index, 1)\n            }\n        }\n    }\n", null]}