{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormPage\\home_city.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormPage\\home_city.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nexport default {\n  name: 'home_city',\n  cname: '城市',\n  icon: 'iconbiaoda<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>',\n  configName: 'c_home_city',\n  type: 0, // 0 基础组件 1 营销组件 2工具组件\n  defaultName: 'citys', // 外面匹配名称\n  props: {\n    index: {\n      type: null\n    },\n    num: {\n      type: null\n    }\n  },\n  computed: {\n    ...mapState('admin/mobildConfig', ['defaultArray'])\n  },\n  watch: {\n    pageData: {\n      handler (nVal, oVal) {\n        this.setConfig(nVal)\n      },\n      deep: true\n    },\n    num: {\n      handler (nVal, oVal) {\n        let data = this.$store.state.admin.mobildConfig.defaultArray[nVal]\n        this.setConfig(data)\n      },\n      deep: true\n    },\n    'defaultArray': {\n      handler (nVal, oVal) {\n        let data = this.$store.state.admin.mobildConfig.defaultArray[this.num]\n        this.setConfig(data);\n      },\n      deep: true\n    }\n  },\n  data () {\n    return {\n      defaultConfig: {\n        name: 'citys',\n        timestamp: this.num,\n        titleConfig: {\n          title: '标题',\n          value: '城市',\n          place: '请输入标题',\n          max: 10,\n          type:'form'\n        },\n        valConfig: {\n          title:'默认值',\n          tabVal: 1,\n          type:'form',\n          tabList: [\n            {\n              name: '省市'\n            },\n            {\n              name: '省市区'\n            },\n            {\n              name: '省市区街道'\n            }\n          ]\n        },\n        tipConfig: {\n          title: '提示语',\n          value: '请选择',\n          place: '请输入提示语',\n          max: 10,\n          type:'form'\n        },\n        titleShow: {\n          title: '是否必填',\n          val: true,\n          type:'form'\n        },\n      },\n      titleTxt: '',\n      tipVal: '',\n      pageData: {}\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.pageData = this.$store.state.admin.mobildConfig.defaultArray[this.num]\n      this.setConfig(this.pageData)\n    })\n  },\n  methods: {\n    setConfig (data) {\n      if(!data) return\n      if(data.titleConfig){\n        this.titleTxt = data.titleConfig.value\n        this.tipVal = data.tipConfig.value\n      }\n    }\n  }\n}\n", null]}