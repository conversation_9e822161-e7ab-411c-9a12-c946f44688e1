{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue", "mtime": 1683692372000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { cascaderListApi, changeListApi, allLabelApi } from \"@/api/product\";\nexport default {\n  name: \"index\",\n  props: {\n    goodsType: {\n      type: Number,\n      default: 0\n    },\n    is_new: {\n      type: String,\n      default: \"\"\n    },\n    diy: {\n      type: Boolean,\n      default: false\n    },\n    isdiy: {\n      type: Boolean,\n      default: false\n    },\n    ischeckbox: {\n      type: Boolean,\n      default: false\n    },\n    datas: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      labelSelect: [],\n      cateIds: [],\n      treeSelect: [],\n      formValidate: {\n        page: 1,\n        limit: 10,\n        cate_id: \"\",\n        store_name: \"\",\n        is_new: this.is_new,\n        store_label_id: \"\"\n      },\n      total: 0,\n      loading: false,\n      grid: {\n        xl: 10,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      tableList: [],\n      currentid: 0,\n      productRow: {},\n      images: []\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {},\n  mounted: function mounted() {\n    this.goodsCategory();\n    this.getList();\n    this.getAllLabelApi();\n  },\n  methods: {\n    getAllLabelApi: function getAllLabelApi() {\n      var _this = this;\n\n      allLabelApi().then(function (res) {\n        _this.labelSelect = res.data;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n      });\n    },\n    // 商品分类；\n    goodsCategory: function goodsCategory() {\n      var _this2 = this;\n\n      cascaderListApi(1).then(function (res) {\n        _this2.treeSelect = res.data;\n      }).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(_ref) {\n      var currentPage = _ref.currentPage,\n          pageSize = _ref.pageSize;\n      this.formValidate.page = currentPage;\n      this.formValidate.limit = pageSize;\n      this.getList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this3 = this;\n\n      this.loading = true;\n\n      if (this.goodsType) {\n        this.formValidate.is_presale_product = 0;\n        this.formValidate.is_vip_product = 0;\n      }\n\n      this.formValidate.cate_id = this.cateIds[this.cateIds.length - 1];\n      changeListApi(this.formValidate).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var list;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  list = res.data.list;\n                  list.forEach(function (item) {\n                    item.attrValue.forEach(function (j) {\n                      j.store_name = j.suk, j.cate_name = item.cate_name, j.store_label = item.store_label;\n                    });\n                  });\n                  _this3.tableList = list;\n                  _this3.total = res.data.count;\n                  _this3.loading = false;\n\n                case 5:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this3.loading = false;\n\n        _this3.$Message.error(res.msg);\n      });\n    },\n    ok: function ok() {\n      var selectRecords = this.$refs.xTree.getCheckboxRecords();\n      var goodsattr = [];\n      selectRecords.forEach(function (item) {\n        if (item.hasOwnProperty('product_id')) {\n          goodsattr.push(item);\n        }\n      });\n\n      if (goodsattr.length > 0) {\n        this.$emit(\"getProductId\", goodsattr);\n      } else {\n        this.$Message.warning(\"请先选择商品\");\n      }\n    },\n    treeSearchs: function treeSearchs(value) {\n      this.cateIds = value;\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    clear: function clear() {\n      this.productRow.id = \"\";\n      this.currentid = \"\";\n    }\n  }\n};", null]}