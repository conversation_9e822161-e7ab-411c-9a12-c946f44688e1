{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableFrom.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableFrom.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from \"vuex\";\nimport { integralGetOrdes } from \"@/api/marketing\";\nimport { putWrite, storeOrderApi, handBatchDelivery, otherBatchDelivery, exportExpressList, storeIntegralOrder } from \"@/api/order\";\nimport autoSend from \"../handle/autoSend\";\nimport queueList from \"../handle/queueList\";\nimport Setting from \"@/setting\";\nimport util from \"@/libs/util\";\nimport QueueList from \"../handle/queueList.vue\";\nimport exportExcel from \"@/utils/newToExcel.js\"; // import XLSX from 'xlsx';\n// const make_cols = refstr => Array(XLSX.utils.decode_range(refstr).e.c + 1).fill(0).map((x,i) => ({name:XLSX.utils.encode_col(i), key:i}));\n\nexport default {\n  name: \"table_from\",\n  components: {\n    autoSend: autoSend,\n    queueList: queueList\n  },\n  props: [\"formSelection\", \"autoDisabled\", \"isAll\"],\n  data: function data() {\n    var codeNum = function codeNum(rule, value, callback) {\n      if (!value) {\n        return callback(new Error(\"请填写核销码\"));\n      } // 模拟异步验证效果\n\n\n      if (!Number.isInteger(value)) {\n        callback(new Error(\"请填写12位数字\"));\n      } else {\n        // const reg = /[0-9]{12}/;\n        var reg = /\\b\\d{12}\\b/;\n\n        if (!reg.test(value)) {\n          callback(new Error(\"请填写12位数字\"));\n        } else {\n          callback();\n        }\n      }\n    };\n\n    return {\n      fromList: {\n        title: \"选择时间\",\n        custom: true,\n        fromTxt: [{\n          text: \"全部\",\n          val: \"\"\n        }, {\n          text: \"今天\",\n          val: \"today\"\n        }, {\n          text: \"昨天\",\n          val: \"yesterday\"\n        }, {\n          text: \"最近7天\",\n          val: \"lately7\"\n        }, {\n          text: \"最近30天\",\n          val: \"lately30\"\n        }, {\n          text: \"本月\",\n          val: \"month\"\n        }, {\n          text: \"本年\",\n          val: \"year\"\n        }]\n      },\n      currentTab: \"\",\n      // 搜索条件\n      orderData: {\n        status: \"\",\n        data: \"\",\n        real_name: \"\",\n        field_key: \"all\",\n        pay_type: \"\"\n      },\n      modalTitleSs: \"\",\n      statusType: \"\",\n      time: \"\",\n      value2: [],\n      isDelIdList: [],\n      modals2: false,\n      timeVal: [],\n      options: {\n        shortcuts: [{\n          text: \"今天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()));\n            return [start, end];\n          }\n        }, {\n          text: \"昨天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            return [start, end];\n          }\n        }, {\n          text: \"最近7天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 6)));\n            return [start, end];\n          }\n        }, {\n          text: \"最近30天\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 29)));\n            return [start, end];\n          }\n        }, {\n          text: \"上月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            var day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, day)));\n            return [start, end];\n          }\n        }, {\n          text: \"本月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));\n            return [start, end];\n          }\n        }, {\n          text: \"本年\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)));\n            return [start, end];\n          }\n        }]\n      },\n      payList: [{\n        label: \"全部\",\n        val: \"\"\n      }, {\n        label: \"微信支付\",\n        val: \"1\"\n      }, {\n        label: \"支付宝支付\",\n        val: \"4\"\n      }, {\n        label: \"余额支付\",\n        val: \"2\"\n      }, {\n        label: \"线下支付\",\n        val: \"3\"\n      }],\n      manualModal: false,\n      uploadAction: \"\".concat(Setting.apiBaseURL, \"/file/upload/1\"),\n      uploadHeaders: {},\n      file: \"\",\n      autoModal: false,\n      isShow: false,\n      recordModal: false,\n      sendOutValue: \"\",\n      exportList: [{\n        name: \"1\",\n        label: \"导出发货单\"\n      }, {\n        name: \"0\",\n        label: \"导出订单\"\n      }],\n      exportListOn: 0,\n      fileList: [] //orderChartType: {},\n      // modal5: false,\n      // data5: [],\n      // cols5: []\n      // orderStatus: false,\n      // orderInfo:''\n\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {}, mapState(\"admin/integralOrder\", [\"orderChartType\", \"isDels\", \"delIdList\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n    today: function today() {\n      var end = new Date();\n      var start = new Date();\n      var datetimeStart = start.getFullYear() + \"/\" + (start.getMonth() + 1) + \"/\" + start.getDate();\n      var datetimeEnd = end.getFullYear() + \"/\" + (end.getMonth() + 1) + \"/\" + end.getDate();\n      return [datetimeStart, datetimeEnd];\n    }\n  }),\n  watch: {\n    $route: function $route() {\n      if (this.$route.fullPath === \"/order/list?status=1\") {\n        this.getPath();\n      }\n    }\n  },\n  created: function created() {\n    // this.timeVal = this.today;\n    // this.orderData.data = this.timeVal.join('-');\n    if (this.$route.fullPath === \"/order/list?status=1\") {\n      this.getPath();\n    }\n\n    this.getToken();\n    this.$parent.$emit(\"add\"); // let searchData = {\n    //   status: this.orderData.status,\n    //   product_id: this.$route.query.product_id || \"\",\n    // };\n    // integralGetOrdes(searchData)\n    //   .then((res) => {\n    //     this.orderChartType = res.data;\n    //   })\n    //   .catch((res) => {});\n  },\n  methods: _objectSpread({}, mapMutations(\"admin/integralOrder\", [\"getOrderStatus\", \"getOrderType\", \"getOrderTime\", \"getOrderNum\", \"getfieldKey\", \"getOrderRealName\"]), {\n    getPath: function getPath() {\n      this.orderData.status = this.$route.query.status.toString();\n      this.getOrderStatus(this.orderData.status);\n      this.$emit(\"getList\", 1);\n      this.$emit(\"order-data\", this.orderData);\n    },\n    // 导出\n    // exports(value) {\n    //   this.exportListOn = this.exportList.findIndex(\n    //     (item) => item.name === value\n    //   );\n    //   let formValidate = this.orderData;\n    //   let data = {\n    //     status: formValidate.status,\n    //     data: formValidate.data,\n    //     real_name: formValidate.real_name,\n    //     type: value,\n    //   };\n    //   storeOrderApi(data)\n    //     .then((res) => {\n    //       location.href = res.data[0];\n    //     })\n    //     .catch((res) => {\n    //       this.$Message.error(res.msg);\n    //     });\n    // },\n    // 数据导出；\n    exports: function () {\n      var _exports = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        var th, filekey, data, fileName, excelData, i, lebData;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                th = [], filekey = [], data = [], fileName = \"\";\n                excelData = JSON.parse(JSON.stringify(this.orderData));\n                excelData.page = 1;\n                excelData.product_id = this.$route.query.product_id || \"\";\n                i = 0;\n\n              case 5:\n                if (!(i < excelData.page + 1)) {\n                  _context.next = 16;\n                  break;\n                }\n\n                _context.next = 8;\n                return this.getExcelData(excelData);\n\n              case 8:\n                lebData = _context.sent;\n                if (!fileName) fileName = lebData.filename;\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) th = lebData.header;\n\n                if (lebData.export.length) {\n                  data = data.concat(lebData.export);\n                  excelData.page++;\n                }\n\n              case 13:\n                i++;\n                _context.next = 5;\n                break;\n\n              case 16:\n                exportExcel(th, filekey, fileName, data);\n\n              case 17:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function exports() {\n        return _exports.apply(this, arguments);\n      }\n\n      return exports;\n    }(),\n    getExcelData: function getExcelData(excelData) {\n      return new Promise(function (resolve, reject) {\n        storeIntegralOrder(excelData).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.orderData.data = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.$store.dispatch(\"admin/integralOrder/getOrderTabs\", {\n        data: this.orderData.data\n      });\n      this.getOrderTime(this.orderData.data);\n      this.$emit(\"getList\", 1);\n      this.$emit(\"order-data\", this.orderData);\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.$store.dispatch(\"admin/integralOrder/getOrderTabs\", {\n        data: tab\n      });\n      this.orderData.data = tab;\n      this.getOrderTime(this.orderData.data);\n      this.timeVal = [];\n      this.$emit(\"getList\");\n      this.$emit(\"order-data\", this.orderData);\n    },\n    // 订单选择状态\n    selectChange2: function selectChange2(tab) {\n      this.getOrderStatus(tab);\n      this.$emit(\"getList\", 1);\n    },\n    userSearchs: function userSearchs(type) {\n      this.getOrderType(type);\n      this.$emit(\"getList\", 1);\n    },\n    // 时间状态\n    timeChange: function timeChange(time) {\n      this.getOrderTime(time);\n      this.$emit(\"getList\");\n    },\n    // 订单号搜索\n    orderSearch: function orderSearch() {\n      this.getOrderRealName(this.orderData.real_name);\n      this.getfieldKey(this.orderData.field_key);\n      this.$emit(\"getList\", 1);\n    },\n    // 点击订单类型\n    onClickTab: function onClickTab() {\n      this.$emit(\"onChangeType\", this.currentTab);\n    },\n    // 批量删除\n    delAll: function delAll() {\n      var _this = this;\n\n      if (this.delIdList.length === 0) {\n        this.$Message.error(\"请先选择删除的订单！\");\n      } else {\n        if (this.isDels) {\n          this.delIdList.filter(function (item) {\n            _this.isDelIdList.push(item.id);\n          });\n          var idss = {\n            ids: this.isDelIdList,\n            all: this.isAll,\n            where: this.orderData\n          };\n          var delfromData = {\n            title: \"删除订单\",\n            url: \"/order/dels\",\n            method: \"post\",\n            ids: idss\n          };\n          this.$modalSure(delfromData).then(function (res) {\n            _this.$Message.success(res.msg);\n\n            _this.tabList();\n          }).catch(function (res) {\n            _this.$Message.error(res.msg);\n          });\n        } else {\n          var title = \"错误！\";\n          var content = \"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>\";\n          this.$Modal.error({\n            title: title,\n            content: content\n          });\n        }\n      }\n    },\n    del: function del(name) {\n      // this.orderInfo = ''\n      this.modals2 = false;\n      this.writeOffFrom.confirm = 0;\n      this.$refs[name].resetFields();\n    },\n    handleSubmit: function handleSubmit() {\n      this.$emit(\"on-submit\", this.data);\n    },\n    // 刷新\n    Refresh: function Refresh() {\n      this.$emit(\"getList\");\n    },\n    //\n    handleReset: function handleReset() {\n      this.$refs.form.resetFields();\n      this.$emit(\"on-reset\");\n    },\n    // 上传头部token\n    getToken: function getToken() {\n      this.uploadHeaders[\"Authori-zation\"] = \"Bearer \" + util.cookies.get(\"token\");\n    },\n    // beforeUpload(file){\n    //     /* Boilerplate to set up FileReader */\n    // \tconst reader = new FileReader();\n    // \treader.onload = (e) => {\n    // \t\t/* Parse data */\n    // \t\tconst bstr = e.target.result;\n    // \t\tconst wb = XLSX.read(bstr, {type:'binary'});\n    // \t\t/* Get first worksheet */\n    // \t\tconst wsname = wb.SheetNames[0];\n    // \t\tconst ws = wb.Sheets[wsname];\n    // \t\t/* Convert array of arrays */\n    // \t\tconst data = XLSX.utils.sheet_to_json(ws, {header:1});\n    // \t\t/* Update state */\n    // \t\tthis.data5 = data;\n    //         this.cols5 = make_cols(ws['!ref']);\n    //         this.modal5 = true;\n    // \t};\n    // \treader.readAsBinaryString(file);\n    // },\n    // 上传成功\n    uploadSuccess: function uploadSuccess(res, file, fileList) {\n      if (res.status === 200) {\n        this.$Message.success(res.msg);\n        this.file = res.data.src;\n        this.fileList = fileList;\n      } else {\n        this.$Message.error(res.msg);\n      }\n    },\n    //移除文件\n    removeFile: function removeFile(file, fileList) {\n      this.file = \"\";\n      this.fileList = fileList;\n    },\n    // 手动批量发货-确定\n    manualModalOk: function manualModalOk() {\n      var _this2 = this;\n\n      this.$refs.upload.clearFiles();\n      handBatchDelivery({\n        file: this.file\n      }).then(function (res) {\n        _this2.$Message.success(res.msg);\n\n        _this2.fileList = [];\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n\n        _this2.fileList = [];\n      });\n    },\n    // 手动批量发货-取消\n    manualModalCancel: function manualModalCancel() {\n      this.fileList = [];\n      this.$refs.upload.clearFiles();\n    },\n    // 自动批量发货-取消\n    autoModalOk: function autoModalOk() {\n      if (this.isAll == \"全部\" || this.formSelection.length) {\n        this.$refs.send.modals = true;\n        this.$refs.send.getList();\n        this.$refs.send.getDeliveryList();\n      } else {\n        this.$Message.error(\"请选择本页订单\");\n      }\n    },\n    // 自动批量发货-取消\n    autolModalCancel: function autolModalCancel() {},\n    submitFail: function submitFail() {\n      otherBatchDelivery();\n    },\n    queuemModal: function queuemModal() {\n      // this.$router.push({ path: 'queue/list' });\n      this.$refs.queue.modal = true;\n    },\n    onAuto: function onAuto() {\n      this.$refs.sends.modals = true;\n      this.$refs.sends.getList();\n      this.$refs.sends.getDeliveryList();\n    },\n    // 下载物流公司对照表\n    getExpressList: function getExpressList() {\n      var _this3 = this;\n\n      exportExpressList().then(function (res) {\n        window.open(res.data[0]);\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    }\n  })\n};", null]}