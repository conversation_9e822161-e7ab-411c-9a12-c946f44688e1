{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue?vue&type=template&id=163c60bb&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\pageTitle.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"box\">\n    <!-- <div class=\"c_row-item\" v-if=\"this.$route.query.type !==2\">\n        <Col class=\"label\" span=\"4\">\n            模板名称\n        </Col>\n        <Col span=\"19\" class=\"slider-box\">\n            <Input v-model=\"name\" placeholder=\"选填不超过15个字\" maxlength=\"15\" @on-change=\"changName\" />\n        </Col>\n    </div> -->\n    <div class=\"c_row-item\">\n        <Col class=\"label\" span=\"4\">\n            页面标题\n        </Col>\n        <Col span=\"19\" class=\"slider-box\">\n            <Input v-model=\"value\" placeholder=\"选填不超过30个字\" maxlength=\"30\" @on-change=\"changVal\" />\n        </Col>\n    </div>\n    <div class=\"c_row-item\">\n        <Col class=\"label\" span=\"4\">\n            页面状态\n        </Col>\n        <Col span=\"19\" class=\"slider-box\">\n            <i-switch v-model=\"isShow\" @on-change=\"changeState\" />\n        </Col>\n    </div>\n    <div class=\"c_row-item acea-row row-top\">\n        <Col class=\"label\" span=\"4\">\n            背景设置\n        </Col>\n        <Col span=\"19\" class=\"slider-box\">\n            <div class=\"acea-row row-between row-top color\">\n                <Checkbox v-model=\"bgColor\" @on-change=\"bgColorTap\">背景色</Checkbox>\n                <ColorPicker v-model=\"colorPicker\" @on-change=\"colorPickerTap(colorPicker)\" />\n            </div>\n            <div class=\"acea-row row-between row-top color\">\n                <Checkbox v-model=\"bgPic\" @on-change=\"bgPicTap\">背景图</Checkbox>\n                <RadioGroup v-model=\"tabVal\" type=\"button\" @on-change=\"radioTap\">\n                    <Radio :label=\"index\" v-for=\"(item,index) in picList\" :key=\"index\">\n                        <span class=\"iconfont-diy\" :class=\"item\"></span>\n                    </Radio>\n                </RadioGroup>\n            </div>\n            <div v-if=\"bgPic\">\n                <div class=\"title\">建议尺寸：690 * 240px</div>\n                <div class=\"boxs\" @click=\"modalPicTap('单选')\">\n                    <img :src=\"bgPicUrl\" alt=\"\" v-if=\"bgPicUrl\">\n                    <div class=\"upload-box\" v-else><Icon type=\"ios-camera-outline\" size=\"36\" /></div>\n                    <div class=\"replace\" v-if=\"bgPicUrl\">更换图片</div>\n                    <!--<span class=\"iconfont-diy icondel_1\" @click.stop=\"bindDelete\" v-if=\"bgPicUrl\"></span>-->\n                </div>\n            </div>\n        </Col>\n    </div>\n    <div>\n        <Modal v-model=\"modalPic\" width=\"960px\" scrollable  footer-hide closable title='上传背景图' :mask-closable=\"false\" :z-index=\"1\">\n            <uploadPictures :isChoice=\"isChoice\" @getPic=\"getPic\" :gridBtn=\"gridBtn\" :gridPic=\"gridPic\" v-if=\"modalPic\"></uploadPictures>\n        </Modal>\n    </div>\n</div>\n", null]}