{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\type\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\type\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  userMemberShip,\n  memberShipSave,\n  memberCard,\n  deleteCard,\n} from \"@/api/user\";\n\nexport default {\n  name: \"list\",\n  data() {\n    return {\n      thead: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          maxWidth: 60\n        },\n        {\n          title: \"会员名\",\n          key: \"title\",\n        },\n        {\n          title: \"有限期（天）\",\n          key: \"vip_day\",\n          render: (h, params) => {\n            return h(\n              \"span\",\n              params.row.vip_day === -1 ? \"永久\" : params.row.vip_day\n            );\n          },\n        },\n        {\n          title: \"原价\",\n          key: \"price\",\n        },\n        {\n          title: \"优惠价\",\n          key: \"pre_price\",\n        },\n        {\n          title: \"排序\",\n          key: \"sort\",\n        },\n        {\n          title: \"是否开启\",\n          slot: \"is_del\",\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n        },\n      ],\n      tbody: [],\n      loading: false,\n      modal: false,\n      rowEdit: {},\n      rowModelType: \"编辑\",\n      rule: [\n        {\n          type: \"hidden\",\n          field: \"id\",\n          value: \"\",\n        },\n        {\n          type: \"hidden\",\n          field: \"type\",\n          value: \"\",\n        },\n        {\n          type: \"input\",\n          field: \"title\",\n          title: \"会员名\",\n          value: \"\",\n          props: {\n            disabled: false,\n            required: true\n          },\n          validate: [\n            {\n              type: \"string\",\n              max: 5,\n              min: 1,\n              message: \"请输入长度为1-5的名称\",\n              requred: true,\n            },\n          ],\n        },\n        {\n          type: \"InputNumber\",\n          field: \"vip_day\",\n          title: \"有限期（天）\",\n          value: null,\n          props: {\n            precision: 0,\n            disabled: false,\n            type: \"text\",\n            required: true\n          },\n          validate: [\n            {\n              type: \"number\",\n              max: 1000000,\n              min: 0,\n              message: \"最大只能输入1000000,最小为0\",\n              requred: true,\n            },\n          ],\n        },\n        {\n          type: \"InputNumber\",\n          field: \"price\",\n          title: \"原价\",\n          value: null,\n          props: {\n            min: 0,\n            disabled: false,\n            required: true\n          },\n          validate: [\n            {\n              type: \"number\",\n              max: 1000000,\n              min: 0,\n              message: \"最大只能输入1000000,最小为0\",\n              requred: true,\n            },\n          ],\n        },\n        {\n          type: \"InputNumber\",\n          field: \"pre_price\",\n          title: \"优惠价\",\n          value: null,\n          props: {\n            min: 0,\n            disabled: false,\n            required: true\n          },\n          validate: [\n            {\n              type: \"number\",\n              max: 1000000,\n              min: 0.01,\n              message: \"最大只能输入1000000,最小为0.01\",\n              requred: true,\n            },\n          ],\n        },\n        {\n          type: \"InputNumber\",\n          field: \"sort\",\n          title: \"排序\",\n          value: 0,\n          props: {\n            precision: 0,\n            min: 1,\n            max: 1000000,\n            disabled: false,\n          },\n          validate: [\n            {\n              type: \"number\",\n              max: 1000000,\n              min: 0,\n              message: \"最大只能输入1000000,最小为0\",\n              requred: true,\n            },\n          ],\n        },\n        {\n          type: \"radio\",\n          field: \"is_label\",\n          title: \"特惠标签\",\n          value: 0,\n          options:[\n              {value:1,label:\"是\"},\n              {value:0,label:\"否\"},\n          ],\n        },\n      ],\n      fapi: {\n        id: \"\",\n        pre_price: null,\n        price: null,\n        sort: null,\n        title: \"\",\n        type: \"owner\",\n        vip_day: null,\n      },\n    };\n  },\n  created() {\n    this.getMemberShip();\n  },\n  mounted() {},\n  methods: {\n    onchangeIsShow(row) {\n      let data = {\n        id: row.id,\n        is_del: row.is_del,\n      };\n      memberCard(data)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.getMemberShip();\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    cancel() {\n      this.fapi.resetFields();\n    },\n    getMemberShip() {\n      this.loading = true;\n      userMemberShip()\n        .then((res) => {\n          this.loading = false;\n          const { count, list } = res.data;\n          this.total = count;\n          this.tbody = list;\n        })\n        .catch((err) => {\n          this.loading = false;\n          this.$Message.error(err.msg);\n        });\n    },\n    addType() {\n      this.rowEdit.id = 0;\n      this.rowModelType = \"新增\";\n      this.rule[1].value = \"owner\";\n      this.rule[3].props.disabled = false;\n      this.rule[5].props.disabled = false;\n      this.rowEdit.title = \"\";\n      // this.cancel();\n      this.modal = true;\n    },\n    del(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `user/member_ship/delete/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.getMemberShip();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    editType(row) {\n      this.rule.forEach((item) => {\n        for (const key in row) {\n          if (row.hasOwnProperty(key)) {\n            if (item.field === key) {\n              if (key === \"vip_day\") {\n                if (row[key] === -1 || row[key] == \"永久\") {\n                  item.type = \"Input\";\n                  item.props.disabled = true;\n    \t\t\t\t\t\t\titem.validate = [\n    \t\t\t\t\t\t\t  { type: \"string\", message: \"\", requred: true },\n    \t\t\t\t\t\t\t];\n\t\t\t\t\t\t\t\t\trow[key] = \"永久\";\n                } else {\n\t\t\t\t\t\t\t\t\titem.type = \"InputNumber\";\n                  item.props.disabled = false;\n                  item.props.min = 1;\n                  item.validate = [\n                    {\n                      type: \"number\",\n                      max: 1000000,\n                      min: 0,\n                      message: \"最大只能输入1000000,最小为0\",\n                      requred: true,\n                    },\n                  ];\n                }\n              }\n             \n    \t\t\t\t\tif ([\"price\"].includes(key)) {\n                row[key] = parseFloat(row[key]);\n              }\n              if ([\"pre_price\"].includes(key)) {\n                row[key] = parseFloat(row[key]);\n                if (row.type == 'free') {\n                  item.props.disabled = true;\n                  item.validate = [];\n                } else {\n                  item.props.disabled = false;\n                  item.validate = [\n                    {\n                      type: \"number\",\n                      max: 1000000,\n                      min: 0.01,\n                      message: \"最大只能输入1000000,最小为0.01\",\n                      requred: true,\n                    },\n                  ];\n                }\n              }\n\t\t\t\t\t\t\titem.value = row[key];\n            }\n          }\n        }\n      });\n      this.rowModelType = \"编辑\";\n      this.rowEdit = JSON.parse(JSON.stringify(row));\n      this.modal = true;\n    },\n    onSubmit(formData) {\n      memberShipSave(this.rowEdit.id, formData)\n        .then((res) => {\n          this.modal = false;\n          this.$Message.success(res.msg);\n          this.getMemberShip();\n          this.cancel();\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n  },\n};\n", null]}