{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue?vue&type=style&index=0&id=0d2b2f52&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue", "mtime": 1734512585792}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.menus {\ndisplay: flex;\nalign-items: center;\nwidth: 10000%;\ncursor pointer;\npadding-left: 12px;\npadding: 9px 0;\n&.on{\n  padding-bottom: 14px;\n}\n\n.title{\n  color: #333;\n  font-size: 12px;\n  margin-top: 4px;\n  height: 20px;\n  border-radius: 20px;\n  padding: 0 5px;\n  line-height: 20px;\n  text-align: center\n}\n\n.item {\n  position: relative;\n  color: #333;\nfont-size: 14px;\nmargin-right: 28px;\nz-index: 9;\n\n&.pic{\n  margin-right: 15px;\n}\n\n.pictrue{\n  width: 46px;\n  height: 46px;\n  border: 1px solid #EEEEEE;\n  border-radius: 50%;\n  background: #f3f9ff;\n  margin: 0 auto;\n  .img {\n    width: 43px;\n    height: 43px;\n    border-radius: 50%;\n  }\n  img {\n    width: 27px;\n  }\n}\n\n&.on{\n  font-size: 16px;\n  font-weight: 600;\n  \n  span {\n    display: block;\n    position: absolute;\n    left: 50%;\n    bottom: 4px;\n    width: 100%;\n    height: 4px;\n    border-radius: 100px;\n    transform: translateX(-50%);\n    background: #fff;\n    background: linear-gradient(90deg, #E93323 0%, #FF7931 100%);\n    z-index: -1;\n  }\n}\n\n&.on2{\n  height: 24px;\n  text-align: center;\n  line-height: 24px;\n  color: #fff;\n  background: linear-gradient(90deg, #E93323 0%, #FF7931 100%);\n  border-radius: 50px;\n  padding: 0 6px;\n}\n\n&.on3{\n  font-size: 16px;\n  font-weight: 600;\n  color: #E93323;\n  \n  span{\n    position: absolute;\n    width: 30px;\n    height: 30px;\n    border:3px solid #E93323\n    border-left: 3px solid transparent !important;\n    border-top: 3px solid transparent !important;\n    border-right: 3px solid transparent !important;\n    border-radius: 50%\n    bottom: -4px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n}\n}\n}\n.home_product{\noverflow: hidden;\n.hd_nav{\n  display flex\n  height 65px\n  overflow: hidden;\n  padding: 10px 0;\n  .item{\n    display flex\n    flex-direction column\n    justify-content center\n    margin-right: 37px\n    .title{\n      font-size 15px\n      color #282828\n      width 65px\n      text-align center\n    }\n    .label{\n      width:56px;\n      height:19px;\n      line-height 19px\n      text-align center\n      background:transparent;\n      border-radius:10px;\n      color #999999\n      font-size 11px\n    }\n    &.active{\n      .title{\n        color #FF4444\n      }\n      .label{\n        color #fff\n        background:linear-gradient(270deg,rgba(255,84,0,1) 0%,rgba(255,0,0,1) 100%);\n      }\n    }\n  }\n}\n.list-wrapper{\n  display flex\n  flex-wrap wrap\n  justify-content space-between\n  .item{\n    width: 48.5%\n    margin-bottom: 10px\n    position: relative;\n    .jia{\n      width: 22px;\n      height: 22px;\n      background-color: #E93323;\n      border-radius: 50%;\n      position: absolute;\n      right: 10px;\n      bottom: 8px;\n      .jiaCon{\n        width: 100%;\n        height: 100%;\n        text-align: center;\n        line-height: 22px;\n        .iconfont{\n          color: #fff;\n          font-size: 13px;\n        }\n      }\n    }\n    .img-box{\n      width: 100%\n      height: 173px\n      .img{\n        width: 100%;\n        height: 100%;\n      }\n      img,.box{\n        width: 65px\n        height: 50px\n      }\n      .empty-box{\n        background:  #F3F9FF\n      }\n      .box{\n        background #D8D8D8\n      }\n      .label{\n        position absolute\n        left 0\n        top 0\n        width:46px;\n        height:22px;\n        border-radius:10px 0px 10px 0px;\n        color #fff\n        font-size 13px\n        text-align center\n        line-height 22px\n      }\n    }\n    .info{\n      padding 7px 10px\n      background #fff\n      border-radius: 0px 0px 10px 10px;\n      .pictrue{\n        width: 99px;\n        height: 14px;\n        margin-top: 4px;\n        img {\n          width: 100%;\n          height: 100%;\n        }\n      }\n      .title{\n        font-size 14px\n        color #282828\n      }\n      .sales{\n        color: #999;\n        font-size: 11px;\n      }\n      .price{\n        display flex\n        align-items center\n        margin-top: 6px;\n        img {\n          width: 70px;\n          height: 15px;\n        }\n        .num{\n          font-size 20px\n          margin-right: 4px\n          font-family: SemiBold;\n          \n          span{\n            font-size 12px\n          }\n        }\n        .label{\n          width:16px;\n          height:18px;\n          margin-left 5px\n          text-align center\n          line-height 18px\n          font-size 11px\n          &.on{\n            margin-left 0;\n          }\n        }\n      }\n    }\n  }\n}\n}\n", null]}