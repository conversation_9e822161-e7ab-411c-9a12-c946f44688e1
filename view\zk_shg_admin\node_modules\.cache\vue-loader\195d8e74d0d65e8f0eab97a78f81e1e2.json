{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue?vue&type=template&id=1cf002c8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_one_pictrue.vue", "mtime": 1716618646000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"onePictrue\">\n  <div class=\"info\">建议：请先选择图片，图片宽度750px，高度不限</div>\n  <div class=\"pictrues\">\n    <img :src=\"configData.url\" v-if=\"configData.url\" />\n    <div class=\"emptyBox\" v-else>750*高度不限</div>\n  </div>\n  <div class=\"uploadImg\">\n    <div class=\"name\">图片</div>\n    <div class=\"picTxt\">\n      <div class=\"box\" @click=\"modalPicTap('单选')\">\n        <div\n          class=\"pictrue acea-row row-center-wrapper\"\n          v-if=\"configData.url\"\n        >\n          <img :src=\"configData.url\" alt=\"\" />\n          <div class=\"iconfont icondel_1\" @click.stop=\"bindDelete\"></div>\n        </div>\n        <div class=\"upload-box\" v-else><Icon type=\"ios-add\" size=\"50\" /></div>\n      </div>\n      <div class=\"tip\">{{ configData.info }}</div>\n    </div>\n  </div>\n  <div class=\"bnt\" @click=\"openFloorModal\">+ 编辑热区</div>\n  <div>\n    <Modal\n      v-model=\"modalPic\"\n      width=\"960px\"\n      scrollable\n      footer-hide\n      closable\n      :title=\"'上传图片'\"\n      :mask-closable=\"false\"\n      :z-index=\"1\"\n    >\n      <uploadPictures\n        :isChoice=\"isChoice\"\n        @getPic=\"getPic\"\n        :gridBtn=\"gridBtn\"\n        :gridPic=\"gridPic\"\n        v-if=\"modalPic\"\n      ></uploadPictures>\n    </Modal>\n    <OperationFloorModal\n      ref=\"hotpot\"\n      :imgs=\"configData.url\"\n      :img-area-data=\"imgAreaData\"\n      @delAreaData=\"handleAreaData\"\n      @saveAreaData=\"handleAreaData\"\n    ></OperationFloorModal>\n  </div>\n</div>\n", null]}