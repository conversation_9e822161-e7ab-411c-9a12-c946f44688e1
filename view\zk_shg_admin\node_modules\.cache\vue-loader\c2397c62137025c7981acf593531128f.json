{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\index.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n\nimport productlistDetails from './orderlistDetails';\nimport { mapMutations } from 'vuex';\nexport default {\n    name: 'list',\n    components: {\n        productlistDetails\n    },\n    data () {\n        return {\n            spinShow: false,\n            currentTab: '',\n            data: [],\n            tablists: null\n        }\n    },\n    created () {\n        this.getOrderType('');\n        this.getOrderStatus('');\n        this.getOrderTime('');\n        this.getOrderNum('');\n        this.getfieldKey('');\n        this.onChangeTabs('');\n        this.getSupplier_id('')\n        this.getStore_id('')\n        this.getType_id('')\n\n    },\n    beforeDestroy () {\n        this.getOrderType('');\n        this.getOrderStatus('');\n        this.getOrderTime('');\n        this.getOrderNum('');\n        this.getfieldKey('');\n        this.onChangeTabs('');\n          this.getSupplier_id('')\n        this.getStore_id('')\n        this.getType_id('')\n    },\n    mounted () {\n        \n    },\n    methods: {\n        ...mapMutations('admin/order', [\n            'getOrderStatus',\n            'getOrderTime',\n            'getOrderNum',\n            'getfieldKey',\n            'onChangeTabs',\n            'getOrderType',\n            'getSupplier_id',\n            'getStore_id',\n            'getType_id'\n            // 'onChangeChart'\n        ]),\n    }\n}\n", null]}