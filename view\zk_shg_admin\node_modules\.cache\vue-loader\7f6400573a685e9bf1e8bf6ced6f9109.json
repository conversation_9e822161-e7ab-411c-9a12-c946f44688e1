{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue?vue&type=template&id=52ac2395&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\agreement\\index.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Form',{attrs:{\"label-width\":80},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"协议名称：\"}},[_c('Input',{model:{value:(_vm.agreement.title),callback:function ($$v) {_vm.$set(_vm.agreement, \"title\", $$v)},expression:\"agreement.title\"}})],1),_c('FormItem',{attrs:{\"label\":\"协议内容：\"}},[_c('WangEditor',{attrs:{\"content\":_vm.content},on:{\"editorContent\":_vm.getEditorContent}})],1),_c('FormItem',{attrs:{\"label\":\"开启状态：\"}},[_c('i-switch',{attrs:{\"size\":\"large\",\"true-value\":1,\"false-value\":0},model:{value:(_vm.agreement.status),callback:function ($$v) {_vm.$set(_vm.agreement, \"status\", $$v)},expression:\"agreement.status\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1),(_vm.spinShow)?_c('Spin',{attrs:{\"fix\":\"\"}}):_vm._e()],1),_c('Card',{staticClass:\"fixed-card\",style:({left: (\"\" + (!_vm.menuCollapse?'200px':_vm.isMobile?'0':'80px'))}),attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('div',{staticClass:\"acea-row row-center\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.memberAgreementSave}},[_vm._v(\"保存\")])],1)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}