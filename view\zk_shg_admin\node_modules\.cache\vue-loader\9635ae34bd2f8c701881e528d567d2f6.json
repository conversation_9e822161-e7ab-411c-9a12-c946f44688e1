{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\offline\\index.vue?vue&type=template&id=dc60c1d4&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\offline\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 订单-收款订单 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n      <div class=\"new_card_pd\">\n        <!-- 筛选条件 -->\n        <Form\n          ref=\"pagination\"\n          :model=\"pagination\"\n          :label-width=\"labelWidth\"\n          inline\n          :label-position=\"labelPosition\"\n          @submit.native.prevent\n        >\n          <FormItem label=\"订单号：\" label-for=\"title\">\n            <Input\n              v-model=\"pagination.order_id\"\n              placeholder=\"请输入订单号\"\n              class=\"input-add\"\n            />\n          </FormItem>\n          <FormItem label=\"用户名：\" label-for=\"title\">\n            <Input\n              v-model=\"pagination.name\"\n              placeholder=\"请输入用户名\"\n             class=\"input-add\"\n            />\n          </FormItem>\n          <FormItem label=\"创建时间：\">\n            <DatePicker\n              @on-change=\"onchangeTime\"\n              :value=\"timeVal\"\n              format=\"yyyy/MM/dd\"\n              type=\"datetimerange\"\n              placement=\"bottom-start\"\n              placeholder=\"自定义时间\"\n              style=\"width: 250px;margin-right:14px\"\n              class=\"mr20\"\n              :options=\"options\"\n            ></DatePicker>\n            <Button type=\"primary\" @click=\"search()\">查询</Button>\n          </FormItem>\n        </Form>\n      </div>\n    </Card>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n      <!-- 操作 -->\n      <Button type=\"primary\" @click=\"qrcodeShow\">查看二维码</Button>\n      <!-- 收款订单表格 -->\n      <Table\n        :columns=\"thead\"\n        :data=\"tbody\"\n        ref=\"table\"\n        :loading=\"loading\"\n        highlight-row\n        class=\"ivu-mt\"\n        no-userFrom-text=\"暂无数据\"\n        no-filtered-userFrom-text=\"暂无筛选结果\"\n      >\n      </Table>\n      <div class=\"acea-row row-right page\">\n        <Page\n          :total=\"total\"\n          :current=\"pagination.page\"\n          show-elevator\n          show-total\n          @on-change=\"pageChange\"\n          :page-size=\"pagination.limit\"\n        />\n      </div>\n    </Card>\n    <Modal v-model=\"modal\" title=\"收款码\" footer-hide class-name=\"vertical-center-modal\">\n      <div>\n        <div v-viewer class=\"acea-row row-around code\">\n          <Spin fix v-if=\"spin\"></Spin>\n          <div class=\"acea-row row-column-around row-between-wrapper\">\n            <div class=\"QRpic\">\n              <img v-lazy=\"qrcode && qrcode.wechat\" />\n            </div>\n            <span class=\"mt10\">{{\n              animal ? \"公众号收款码\" : \"公众号二维码\"\n            }}</span>\n          </div>\n          <div class=\"acea-row row-column-around row-between-wrapper\">\n            <div class=\"QRpic\">\n              <img v-lazy=\"qrcode && qrcode.routine\" />\n            </div>\n            <span class=\"mt10\">{{\n              animal ? \"小程序收款码\" : \"小程序二维码\"\n            }}</span>\n          </div>\n        </div>\n      </div>\n    </Modal>\n  </div>\n", null]}