{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormConfig\\c_home_date.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormConfig\\c_home_date.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { formatDate } from '@/utils/validate';\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nexport default {\n  name: 'c_home_date',\n  componentsName: 'home_date',\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_input_item,\n        configNme: 'titleConfig'\n      }, {\n        components: toolCom.c_comb_data,\n        configNme: 'valConfig'\n      }, {\n        components: toolCom.c_input_item,\n        configNme: 'tipConfig'\n      }, {\n        components: toolCom.c_is_show,\n        configNme: 'titleShow'\n      }]\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.getChange(value);\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.getChange(nVal);\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n\n      _this.getChange(value);\n\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    getChange: function getChange(value) {\n      if (value.valConfig.specifyDate) {\n        value.valConfig.specifyDate = formatDate(new Date(value.valConfig.specifyDate), 'yyyy-MM-dd');\n      }\n    },\n    // 获取组件参数\n    getConfig: function getConfig(data) {}\n  }\n};", null]}