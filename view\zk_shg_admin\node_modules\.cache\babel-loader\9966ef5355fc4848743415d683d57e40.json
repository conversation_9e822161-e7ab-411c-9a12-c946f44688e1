{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from 'vuex';\nimport { taskListApi, setTaskShowApi, setTaskMustApi, createTaskApi } from '@/api/user';\nimport editFrom from '@/components/from/from';\nexport default {\n  name: 'task',\n  components: {\n    editFrom: editFrom\n  },\n  data: function data() {\n    return {\n      // levelIds: this.levelId,\n      grid: {\n        xl: 10,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      modals: false,\n      levelFrom: {\n        is_show: '',\n        name: '',\n        page: 1,\n        limit: 20\n      },\n      total: 0,\n      levelLists: [],\n      loading: false,\n      columns1: [{\n        title: 'ID',\n        key: 'id',\n        sortable: true,\n        width: 80\n      }, {\n        title: '等级名称',\n        key: 'level_name',\n        minWidth: 100\n      }, {\n        title: '任务名称',\n        key: 'name',\n        minWidth: 120\n      }, {\n        title: '排序',\n        sort: 'grade',\n        sortable: true,\n        minWidth: 100\n      }, {\n        title: '是否显示',\n        slot: 'is_shows',\n        minWidth: 110\n      }, {\n        title: '务必达成',\n        slot: 'is_musts',\n        minWidth: 135\n      }, {\n        title: '任务说明',\n        key: 'illustrate',\n        minWidth: 120\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        minWidth: 120\n      }],\n      FromData: null,\n      ids: 0,\n      modalTitleSs: '',\n      titleType: 'task'\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {}, mapState('admin/userLevel', ['levelId']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 75;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  methods: _objectSpread({}, mapMutations('admin/userLevel', ['getTaskId', 'getlevelId']), {\n    // 添加\n    add: function add() {\n      this.ids = '';\n      this.getFrom();\n    },\n    // 新建 编辑表单\n    getFrom: function getFrom() {\n      var _this = this;\n\n      var data = {\n        id: this.ids,\n        level_id: this.levelId\n      };\n      this.$modalForm(createTaskApi(data)).then(function () {\n        return _this.getList();\n      });\n    },\n    // 编辑\n    edit: function edit(row) {\n      this.ids = row.id;\n      this.getFrom();\n    },\n    // 关闭模态框\n    handleReset: function handleReset() {\n      this.modals = false;\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.getList();\n    },\n    // 任务列表\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      this.levelFrom.is_show = this.levelFrom.is_show || '';\n      taskListApi(this.levelId, this.levelFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this2.levelLists = data.list;\n                  _this2.total = res.data.count;\n                  _this2.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.levelFrom.page = index;\n      this.getList();\n    },\n    // 修改显示隐藏\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this3 = this;\n\n      var data = {\n        id: row.id,\n        is_show: row.is_show\n      };\n      setTaskShowApi(data).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  _this3.$Message.success(res.msg);\n\n                case 1:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this3.$Message.error(res.msg);\n      });\n    },\n    // 设置任务是否达成\n    onchangeIsMust: function onchangeIsMust(row) {\n      var _this4 = this;\n\n      var data = {\n        id: row.id,\n        is_must: row.is_must\n      };\n      setTaskMustApi(data).then(\n      /*#__PURE__*/\n      function () {\n        var _ref3 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee3(res) {\n          return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n            while (1) {\n              switch (_context3.prev = _context3.next) {\n                case 0:\n                  _this4.$Message.success(res.msg);\n\n                case 1:\n                case \"end\":\n                  return _context3.stop();\n              }\n            }\n          }, _callee3);\n        }));\n\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this4.$Message.error(res.msg);\n      });\n    },\n    // 新建编辑提交成功\n    submitFail: function submitFail() {\n      this.getList();\n    },\n    // 删除任务\n    del: function del(row, tit, num) {\n      var _this5 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"user/user_level/delete_task/\".concat(row.id),\n        method: 'DELETE',\n        ids: ''\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this5.$Message.success(res.msg);\n\n        _this5.levelLists.splice(num, 1);\n      }).catch(function (res) {\n        _this5.$Message.error(res.msg);\n      });\n    }\n  })\n};", null]}