{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\recharge\\index.vue?vue&type=template&id=67d15c14&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\recharge\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 财务-充值记录 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding= \"0\">\n      <div class=\"new_card_pd\">\n        <!-- 查询条件 -->\n      <Form\n        ref=\"formValidate\"\n        :model=\"formValidate\"\n        :label-width=\"labelWidth\" \n        inline \n        :label-position=\"labelPosition\"\n        class=\"tabform\"\n        @submit.native.prevent\n      >\n            <FormItem label=\"时间选择：\">\n              <DatePicker\n                :editable=\"false\"\n                @on-change=\"onchangeTime\"\n                :value=\"timeVal\"\n                format=\"yyyy/MM/dd HH:mm\"\n                type=\"datetimerange\" \n                placement=\"bottom-start\" \n                placeholder=\"自定义时间\"\n                class=\"input-width\"\n                :options=\"options\"\n              ></DatePicker>\n            </FormItem>\n            \n            <FormItem label=\"支付类型：\">\n              <Select v-model=\"formValidate.paid\" class=\"input-add\" @on-change=\"orderSearch\">\n                <Option value=\"\">全部</Option>\n                <Option value=\"1\">已支付</Option>\n                <Option value=\"0\">未支付</Option>\n              </Select>\n            </FormItem>\n            <FormItem label=\"搜索：\">\n              <Input\n                @on-search=\"selChange\"\n                placeholder=\"请输入用户昵称、订单号\"\n                element-id=\"name\"\n                v-model=\"formValidate.nickname\"\n                class=\"mr input-add\"\n              />\n              <Button type=\"primary\" @click=\"orderSearch()\" class=\"mr\">查询</Button>\n              <Button\n              v-auth=\"['export-userRecharge']\"\n              @click=\"exports\"\n              >导出</Button>\n            </FormItem>\n      </Form>\n      </div>\n    </Card>\n    \n    <cards-data\n      :cardLists=\"cardLists\"\n      v-if=\"cardLists.length >= 0\"\n    ></cards-data>\n    <Card :bordered=\"false\" dis-hover>\n<!--      <div>充值记录列表</div>-->\n      <!-- 表格 -->\n      <Table\n        ref=\"table\"\n        :columns=\"columns\"\n        :data=\"tabList\"\n        class=\"ivu-mt\"\n        :loading=\"loading\"\n        no-data-text=\"暂无数据\"\n        no-filtered-data-text=\"暂无筛选结果\"\n      >\n\t\t\t  <template slot-scope=\"{ row }\" slot=\"nickname\">\n\t\t\t\t\t<span>{{ row.nickname }} </span>\n\t\t\t\t\t<span style=\"color: #ed4014;\" v-if=\"row.delete_time != null\"> (已注销)</span>\n\t\t\t  </template>\n        <template slot-scope=\"{ row }\" slot=\"paid_type\">\n          <Tag color=\"orange\" size=\"large\" v-show=\"row.paid_type === '未支付'\">{{row.paid_type}}</Tag>\n          <Tag color=\"green\" size=\"large\" v-show=\"row.paid_type === '已支付'\">{{row.paid_type}}</Tag>\n          <Tag color=\"default\" size=\"large\" v-show=\"row.paid_type === '全部'\">{{row.paid_type}}</Tag>\n        </template>\n\t\t\t\t<template slot-scope=\"{ row, index }\" slot=\"right\">\n\t\t\t\t  <a\n\t\t\t\t    href=\"javascript:void(0);\"\n\t\t\t\t    v-if=\"row.refund_price <= 0 && row.paid && row.delete_time == null\"\n\t\t\t\t    @click=\"refund(row)\"\n\t\t\t\t    >退款</a\n\t\t\t\t  >\n\t\t\t\t  <!--                    <Divider type=\"vertical\"  v-if=\"row.paid\"/>-->\n\t\t\t\t  <a\n\t\t\t\t    href=\"javascript:void(0);\"\n\t\t\t\t    v-if=\"row.paid === 0\"\n\t\t\t\t    @click=\"del(row, '删除此条充值记录', index)\"\n\t\t\t\t    >删除</a\n\t\t\t\t  >\n\t\t\t\t</template>\n      </Table>\n      <div class=\"acea-row row-right page\">\n        <Page\n          :total=\"total\"\n          :current=\"formValidate.page\"\n          show-elevator\n          show-total\n          @on-change=\"pageChange\"\n          :page-size=\"formValidate.limit\"\n        />\n      </div>\n    </Card>\n    <!-- 退款表单-->\n    <edit-from\n      ref=\"edits\"\n      :FromData=\"FromData\"\n      @submitFail=\"submitFail\"\n    ></edit-from>\n  </div>\n", null]}