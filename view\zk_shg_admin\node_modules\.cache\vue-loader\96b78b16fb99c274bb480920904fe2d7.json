{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue?vue&type=template&id=4c2bb400&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_pictrue.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"mobile-page\">\n  <div v-if=\"isUpdate\">\n    <!-- <div class=\"title\">布局</div>\n          <div class=\"tip\">选定布局区域，在下方添加图片，建议添加比例一致的图片</div> -->\n    <div class=\"advert\">\n      <div\n        class=\"advertItem07\"\n        :class=\"currentIndex === index ? 'on' : ''\"\n        @click=\"currentTab(index, configData)\"\n        v-if=\"style === 0\"\n        v-for=\"(item, index) in configData.picList\"\n        :key=\"index\"\n      >\n        <img :src=\"item.image\" v-if=\"item.image\" />\n        <div class=\"empty-box\" v-else>750*375</div>\n      </div>\n      <div class=\"advertItem02 acea-row\" v-if=\"style === 1\">\n        <div\n          class=\"item\"\n          :class=\"currentIndex === index ? 'on' : ''\"\n          @click=\"currentTab(index, configData)\"\n          v-for=\"(item, index) in configData.picList\"\n          :key=\"index\"\n        >\n          <img :src=\"item.image\" v-if=\"item.image\" />\n          <div class=\"empty-box\" v-else>\n            <div>\n              <div>375*750</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"advertItem03 acea-row\" v-if=\"style === 2\">\n        <div\n          class=\"item\"\n          :class=\"currentIndex === index ? 'on' : ''\"\n          @click=\"currentTab(index, configData)\"\n          v-for=\"(item, index) in configData.picList\"\n          :key=\"index\"\n        >\n          <img :src=\"item.image\" v-if=\"item.image\" />\n          <div class=\"empty-box\" v-else>\n            <div>\n              <div>250*750</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"advertItem08\" v-if=\"style === 3\">\n        <div class=\"item acea-row\">\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 0 ? 'on' : ''\"\n            @click=\"currentTab(0, configData)\"\n          >\n            <img\n              :src=\"configData.picList[0].image\"\n              v-if=\"configData.picList[0].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 1 ? 'on' : ''\"\n            @click=\"currentTab(1, configData)\"\n          >\n            <img\n              :src=\"configData.picList[1].image\"\n              v-if=\"configData.picList[1].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n        </div>\n        <div\n          class=\"item\"\n          :class=\"currentIndex === 2 ? 'on' : ''\"\n          @click=\"currentTab(2, configData)\"\n        >\n          <img\n            :src=\"configData.picList[2].image\"\n            v-if=\"configData.picList[2].image\"\n          />\n          <div class=\"empty-box\" v-else>750*375</div>\n        </div>\n      </div>\n      <div class=\"advertItem08\" v-if=\"style === 4\">\n        <div\n          class=\"item\"\n          :class=\"currentIndex === 0 ? 'on' : ''\"\n          @click=\"currentTab(0, configData)\"\n        >\n          <img\n            :src=\"configData.picList[0].image\"\n            v-if=\"configData.picList[0].image\"\n          />\n          <div class=\"empty-box\" v-else>750*375</div>\n        </div>\n        <div class=\"item acea-row\">\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 1 ? 'on' : ''\"\n            @click=\"currentTab(1, configData)\"\n          >\n            <img\n              :src=\"configData.picList[1].image\"\n              v-if=\"configData.picList[1].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 2 ? 'on' : ''\"\n            @click=\"currentTab(2, configData)\"\n          >\n            <img\n              :src=\"configData.picList[2].image\"\n              v-if=\"configData.picList[2].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"advertItem04 acea-row\" v-if=\"style === 5\">\n        <div\n          class=\"item\"\n          :class=\"currentIndex === 0 ? 'on' : ''\"\n          @click=\"currentTab(0, configData)\"\n        >\n          <img\n            :src=\"configData.picList[0].image\"\n            v-if=\"configData.picList[0].image\"\n          />\n          <div class=\"empty-box\" v-else>375*750</div>\n        </div>\n        <div class=\"item\">\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 1 ? 'on' : ''\"\n            @click=\"currentTab(1, configData)\"\n          >\n            <img\n              :src=\"configData.picList[1].image\"\n              v-if=\"configData.picList[1].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 2 ? 'on' : ''\"\n            @click=\"currentTab(2, configData)\"\n          >\n            <img\n              :src=\"configData.picList[2].image\"\n              v-if=\"configData.picList[2].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"advertItem04 acea-row\" v-if=\"style === 6\">\n        <div class=\"item\">\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 0 ? 'on' : ''\"\n            @click=\"currentTab(0, configData)\"\n          >\n            <img\n              :src=\"configData.picList[0].image\"\n              v-if=\"configData.picList[0].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 1 ? 'on' : ''\"\n            @click=\"currentTab(1, configData)\"\n          >\n            <img\n              :src=\"configData.picList[1].image\"\n              v-if=\"configData.picList[1].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n        </div>\n        <div\n          class=\"item\"\n          :class=\"currentIndex === 2 ? 'on' : ''\"\n          @click=\"currentTab(2, configData)\"\n        >\n          <img\n            :src=\"configData.picList[2].image\"\n            v-if=\"configData.picList[2].image\"\n          />\n          <div class=\"empty-box\" v-else>375*750</div>\n        </div>\n      </div>\n      <div class=\"advertItem06 acea-row\" v-if=\"style === 7\">\n        <div\n          class=\"item\"\n          :class=\"currentIndex === index ? 'on' : ''\"\n          @click=\"currentTab(index, configData)\"\n          v-for=\"(item, index) in configData.picList\"\n          :key=\"index\"\n        >\n          <img :src=\"item.image\" v-if=\"item.image\" />\n          <div class=\"empty-box\" v-else>375*375</div>\n        </div>\n      </div>\n      <div class=\"advertItem08\" v-if=\"style === 8\">\n        <div class=\"item acea-row\">\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 0 ? 'on' : ''\"\n            @click=\"currentTab(0, configData)\"\n          >\n            <img\n              :src=\"configData.picList[0].image\"\n              v-if=\"configData.picList[0].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 1 ? 'on' : ''\"\n            @click=\"currentTab(1, configData)\"\n          >\n            <img\n              :src=\"configData.picList[1].image\"\n              v-if=\"configData.picList[1].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n        </div>\n        <div class=\"items acea-row\">\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 2 ? 'on' : ''\"\n            @click=\"currentTab(2, configData)\"\n          >\n            <img\n              :src=\"configData.picList[2].image\"\n              v-if=\"configData.picList[2].image\"\n            />\n            <div class=\"empty-box\" v-else>250*375</div>\n          </div>\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 3 ? 'on' : ''\"\n            @click=\"currentTab(3, configData)\"\n          >\n            <img\n              :src=\"configData.picList[3].image\"\n              v-if=\"configData.picList[3].image\"\n            />\n            <div class=\"empty-box\" v-else>250*375</div>\n          </div>\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 4 ? 'on' : ''\"\n            @click=\"currentTab(4, configData)\"\n          >\n            <img\n              :src=\"configData.picList[4].image\"\n              v-if=\"configData.picList[4].image\"\n            />\n            <div class=\"empty-box\" v-else>250*375</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"advertItem04 acea-row\" v-if=\"style === 9\">\n        <div\n          class=\"item\"\n          :class=\"currentIndex === 0 ? 'on' : ''\"\n          @click=\"currentTab(0, configData)\"\n        >\n          <img\n            :src=\"configData.picList[0].image\"\n            v-if=\"configData.picList[0].image\"\n          />\n          <div class=\"empty-box\" v-else>375*750</div>\n        </div>\n        <div class=\"item\">\n          <div\n            class=\"pic\"\n            :class=\"currentIndex === 1 ? 'on' : ''\"\n            @click=\"currentTab(1, configData)\"\n          >\n            <img\n              :src=\"configData.picList[1].image\"\n              v-if=\"configData.picList[1].image\"\n            />\n            <div class=\"empty-box\" v-else>375*375</div>\n          </div>\n          <div class=\"pic acea-row\">\n            <div\n              class=\"picItem\"\n              :class=\"currentIndex === 2 ? 'on' : ''\"\n              @click=\"currentTab(2, configData)\"\n            >\n              <img\n                :src=\"configData.picList[2].image\"\n                v-if=\"configData.picList[2].image\"\n              />\n              <div class=\"empty-box\" v-else>375*250</div>\n            </div>\n            <div\n              class=\"picItem\"\n              :class=\"currentIndex === 3 ? 'on' : ''\"\n              @click=\"currentTab(3, configData)\"\n            >\n              <img\n                :src=\"configData.picList[3].image\"\n                v-if=\"configData.picList[3].image\"\n              />\n              <div class=\"empty-box\" v-else>375*250</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div\n        class=\"advertItem01 acea-row\"\n        v-if=\"style === 10\"\n        v-for=\"(item, index) in configData.picList\"\n        :key=\"index\"\n      >\n        <img :src=\"item.image\" v-if=\"item.image\" />\n        <div class=\"empty-box\" v-else>尺寸不限</div>\n      </div>\n      <template v-if=\"style === 11\">\n        <div class=\"pic-box\" @mousemove.stop=\"move\">\n          <div class=\"advertItem11 acea-row\" id=\"lay1\" @click=\"clickBox\">\n            <div\n              class=\"lay-item\"\n              :class=\"currentIndex === index ? 'on' : ''\"\n              v-for=\"(item, index) in configData.picList\"\n              :key=\"index + 'aaa'\"\n            >\n              <img :src=\"item.img\" v-if=\"item.img\" />\n              <div class=\"empty-box\" v-else>+</div>\n            </div>\n          </div>\n          <div\n            v-for=\"(item, index) in selBoxList\"\n            :key=\"index\"\n            :style=\"{\n              width: item.doc.w + 'px',\n              height: item.doc.h + 'px',\n              left: item.doc.startX + 'px',\n              top: item.doc.startY + 'px',\n            }\"\n            class=\"areaBox\"\n            :class=\"{ active: selPicBox == index }\"\n            @mouseover=\"initRect\"\n            @click=\"currentTab(index, configData)\"\n          >\n            <img :src=\"item.img\" v-if=\"item.img\" />\n            <div class=\"prompt-text\" v-else>\n              {{ item.doc.w }}x{{ item.doc.h }}\n            </div>\n            <div\n              v-if=\"selPicBox == index\"\n              class=\"del\"\n              @click.stop=\"delAreaBox(index)\"\n            >\n              <Icon type=\"ios-close\" size=\"16\" />\n            </div>\n          </div>\n        </div>\n      </template>\n    </div>\n  </div>\n</div>\n", null]}