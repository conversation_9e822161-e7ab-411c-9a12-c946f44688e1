{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormPage\\home_city.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileFormPage\\home_city.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nexport default {\n  name: 'home_city',\n  cname: '城市',\n  icon: 'iconbiaodanzujian-chengshi',\n  configName: 'c_home_city',\n  type: 0,\n  // 0 基础组件 1 营销组件 2工具组件\n  defaultName: 'citys',\n  // 外面匹配名称\n  props: {\n    index: {\n      type: null\n    },\n    num: {\n      type: null\n    }\n  },\n  computed: _objectSpread({}, mapState('admin/mobildConfig', ['defaultArray'])),\n  watch: {\n    pageData: {\n      handler: function handler(nVal, oVal) {\n        this.setConfig(nVal);\n      },\n      deep: true\n    },\n    num: {\n      handler: function handler(nVal, oVal) {\n        var data = this.$store.state.admin.mobildConfig.defaultArray[nVal];\n        this.setConfig(data);\n      },\n      deep: true\n    },\n    'defaultArray': {\n      handler: function handler(nVal, oVal) {\n        var data = this.$store.state.admin.mobildConfig.defaultArray[this.num];\n        this.setConfig(data);\n      },\n      deep: true\n    }\n  },\n  data: function data() {\n    return {\n      defaultConfig: {\n        name: 'citys',\n        timestamp: this.num,\n        titleConfig: {\n          title: '标题',\n          value: '城市',\n          place: '请输入标题',\n          max: 10,\n          type: 'form'\n        },\n        valConfig: {\n          title: '默认值',\n          tabVal: 1,\n          type: 'form',\n          tabList: [{\n            name: '省市'\n          }, {\n            name: '省市区'\n          }, {\n            name: '省市区街道'\n          }]\n        },\n        tipConfig: {\n          title: '提示语',\n          value: '请选择',\n          place: '请输入提示语',\n          max: 10,\n          type: 'form'\n        },\n        titleShow: {\n          title: '是否必填',\n          val: true,\n          type: 'form'\n        }\n      },\n      titleTxt: '',\n      tipVal: '',\n      pageData: {}\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.pageData = _this.$store.state.admin.mobildConfig.defaultArray[_this.num];\n\n      _this.setConfig(_this.pageData);\n    });\n  },\n  methods: {\n    setConfig: function setConfig(data) {\n      if (!data) return;\n\n      if (data.titleConfig) {\n        this.titleTxt = data.titleConfig.value;\n        this.tipVal = data.tipConfig.value;\n      }\n    }\n  }\n};", null]}