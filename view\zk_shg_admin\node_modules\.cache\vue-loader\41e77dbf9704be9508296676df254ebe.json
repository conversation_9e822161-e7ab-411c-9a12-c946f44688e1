{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue?vue&type=style&index=0&id=ed720464&lang=stylus&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*定义滑块 内阴影+圆角*/\n/deep/::-webkit-scrollbar-thumb{\n  -webkit-box-shadow: inset 0 0 6px #999;\n}\n/deep/::-webkit-scrollbar {\n    width: 4px!important; /*对垂直流动条有效*/\n}\n.footer{\n\tmargin-top 20px;\n}\n.product-data .image {\n\twidth: 50px !important;\n\theight: 50px !important;\n}\n.cards{\n\tposition relative;\n}\n.batch{\n\tposition absolute;\n\tright -20px;\n\tz-index 9;\n\ttop:18px;\n\twidth: 176px;\n\t.input{\n\t\twidth: 176px;\n\t\theight: 56px;\n\t\tbackground: #FFFFFF;\n\t\tbox-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);\n\t\tborder-radius: 4px;\n\t\tmargin-left -80px;\n\t}\n\t.name{\n\t\tfont-size 13px;\n\t\tcolor: #1890FF;\n\t\tmargin-bottom 10px;\n\t\tcursor pointer;\n\t\t.iconfont{\n\t\t\tfont-size 12px;\n\t\t\tmargin-left 4px;\n\t\t}\n\t}\n}\n/deep/.ivu-select-single .ivu-select-selection .ivu-select-placeholder,\n/deep/.ivu-select-single .ivu-select-selection .ivu-select-selected-value{\n\tfont-size 13px!important;\n}\n/deep/.ivu-card-body{\n\tpadding 0!important;\n}\n/deep/.ivu-input:focus{\n\tborder-color #dcdee2!important;\n\tbox-shadow unset!important;\n}\n/deep/.ivu-input:hover{\n\tborder-color #dcdee2!important;\n}\n/deep/.ivu-input{\n\tborder-right 0 !important;\n\ttransition:unset!important;\n}\n/deep/.ivu-input-group-append{\n\tbackground-color #fff !important;\n}\n/deep/.ivu-table{\n\toverflow unset!important;\n}\n/deep/.ivu-table-cell{\n\toverflow unset!important;\n\tfont-size 13px!important;\n}\n/deep/.ivu-table-border:after{\n\tbackground-color #fff;\n}\n/deep/.ivu-table-header table{\n\tborder-top: 0!important;\n}\n/deep/.ivu-modal-body{\n\tpadding-top 0!important;\n}\n.ivu-table-wrapper-with-border{\n\tborder 0!important;\n}\n/deep/.ivu-table-border th, /deep/.ivu-table-border td{\n\tborder-right 0!important;\n}\n", null]}