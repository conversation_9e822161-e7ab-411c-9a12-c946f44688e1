{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\agent\\handle\\promotersList.vue?vue&type=template&id=1a72cadc&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\agent\\handle\\promotersList.vue", "mtime": 1682663004000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n    <Modal v-model=\"modals\"  scrollable  footer-hide closable :title=\"listTitle === 'man'?'统计推广人列表':'推广订单'\" :mask-closable=\"false\"  width=\"900\" @on-cancel=\"onCancel\">\n        <div class=\"table_box\">\n            <Form ref=\"formValidate\" \n            inline\n            :model=\"formValidate\" \n            :label-width=\"labelWidth\" \n            :label-position=\"labelPosition\" \n            class=\"tabform\" @submit.native.prevent>\n                <FormItem label=\"时间选择：\">\n                    <DatePicker \n                    :editable=\"false\"\n                    @on-change=\"onchangeTime\" \n                    :value=\"timeVal\"  \n                    format=\"yyyy/MM/dd\"\n                    type=\"datetimerange\"\n                    placement=\"bottom-start\"\n                    placeholder=\"自定义时间\"\n                    class=\"input-add\"\n                    :options=\"options\"></DatePicker>\n                </FormItem>\n                <FormItem label=\"用户类型：\">\n                    <Select v-model=\"formValidate.type\"  class=\"input-add\">\n                        <Option v-for=\"(item,i) in fromList.fromTxt2\" :key=\"i\" :value=\"item.val\">{{ item.text }}</Option>\n                    </Select>\n                </FormItem>\n                <FormItem label=\"搜索：\">\n                    <Input placeholder=\"请输入请姓名、电话、UID\" v-model=\"formValidate.nickname\"  class=\"input-add\"></Input>\n                </FormItem>\n                <FormItem label=\"订单号：\" v-if=\"listTitle !== 'man'\">\n                    <Input placeholder=\"请输入请订单号\" v-model=\"formValidate.order_id\"  class=\"input-add mr14\"></Input>\n                </FormItem>\n                <Button type=\"primary\" @click=\"userSearchs()\">查询</Button>\n            </Form>\n        </div>\n        <Table ref=\"selection\" :columns=\"columns4\" :data=\"tabList\" :loading=\"loading\"\n               no-data-text=\"暂无数据\" highlight-row max-height=\"400\"\n               no-filtered-data-text=\"暂无筛选结果\">\n        </Table>\n        <div class=\"acea-row row-right page\">\n            <Page :total=\"total\" show-elevator show-total @on-change=\"pageChange\"\n                  :page-size=\"formValidate.limit\"/>\n        </div>\n    </Modal>\n</div>\n", null]}