{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue?vue&type=template&id=5889e2f3&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue", "mtime": 1709543824155}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 供应商-供应商流水 -->\n<div>\n  <Card :bordered=\"false\" dis-hover class=\"ivu-mt box\">\n    <Form\n      ref=\"formValidate\"\n      inline\n      :model=\"formValidate\"\n      :label-width=\"labelWidth\"\n      :label-position=\"labelPosition\"\n      @submit.native.prevent\n    >\n      <FormItem label=\"创建时间：\">\n        <DatePicker\n          :editable=\"false\"\n          @on-change=\"onchangeTime\"\n          :value=\"timeVal\"\n          format=\"yyyy/MM/dd\"\n          type=\"datetimerange\"\n          placement=\"bottom-start\"\n          placeholder=\"自定义时间\"\n          class=\"input-add\"\n          :options=\"options\"\n        ></DatePicker>\n      </FormItem>\n      <FormItem>\n        <Button\n          type=\"primary\"\n          @click=\"search()\"\n          style=\"margin-left: -75px; margin-right: 14px\"\n          >查询</Button\n        >\n      </FormItem>\n    </Form>\n  </Card>\n  \n  <cards-data :cardLists=\"cardLists\"></cards-data>\n\n  <Card :bordered=\"false\" dis-hover class=\"ive-mt tablebox\" :padding=\"20\">\n    <div class=\"table\">\n      <Table\n        :columns=\"columns\"\n        :data=\"orderList\"\n        ref=\"table\"\n        :loading=\"loading\"\n        highlight-row\n        no-userFrom-text=\"暂无数据\"\n        no-filtered-userFrom-text=\"暂无筛选结果\"\n      >\n        <template slot-scope=\"{ row, index }\" slot=\"number\">\n          <span v-if=\"row.amount < 0\" class=\"colorgreen\"\n            >{{ row.amount }}</span\n          >\n          <span v-if=\"row.amount >= 0\" class=\"colorred\">+ {{ row.amount }}</span>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"type\">\n          <span v-if=\"row.tradeType == 457\" class=\"colorred\">\n            备查款账户返余(充值) \n          </span>\n          <span v-if=\"row.tradeType == 758\" class=\"colorred\">\n            余额支付(取消返回) \n          </span>\n          <span v-if=\"row.tradeType == 1209\" class=\"colorgreen\">\n            实物或礼品卡余额支付(下单扣减)\n          </span>\n        </template>\n      </Table>\n    </div>\n    <div class=\"acea-row row-right page\">\n      <Page\n        :total=\"total\"\n        :current=\"formValidate.page\"\n        show-elevator\n        show-total\n        @on-change=\"pageChange\"\n        :page-size=\"formValidate.limit\"\n      />\n    </div>\n  </Card>\n</div>\n", null]}