{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_label.vue?vue&type=template&id=55287365&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_label.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"slider-box\"},[_c('div',{staticClass:\"c_row-item\"},[(_vm.configData.title)?_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"\\n                \"+_vm._s(_vm.configData.title)+\"\\n            \")]):_vm._e(),_c('Col',{attrs:{\"span\":\"18\"}},[_c('div',{staticClass:\"labelInput acea-row row-between-wrapper\",on:{\"click\":_vm.openStoreLabel}},[_c('div',{staticStyle:{\"width\":\"90%\"}},[(_vm.configData.list.length)?_c('div',_vm._l((_vm.configData.list),function(item,index){return _c('Tag',{key:index,attrs:{\"closable\":\"\"},on:{\"on-close\":function($event){return _vm.closeStoreLabel(item)}}},[_vm._v(_vm._s(item.label_name))])}),1):_c('span',{staticClass:\"span\"},[_vm._v(\"选择商品标签\")])]),_c('div',{staticClass:\"iconfont iconxiayi\"})])])],1),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"选择商品标签\",\"closable\":true,\"width\":\"540\",\"footer-hide\":true,\"mask-closable\":false},model:{value:(_vm.storeLabelShow),callback:function ($$v) {_vm.storeLabelShow=$$v},expression:\"storeLabelShow\"}},[_c('storeLabelList',{ref:\"storeLabel\",on:{\"activeData\":_vm.activeStoreData,\"close\":_vm.storeLabelClose}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}