{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\uploadPictures\\widgetImg.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\uploadPictures\\widgetImg.vue", "mtime": 1730707874008}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\nimport uploadFrom from './index';\nexport default {\n  name: 'widgetImg',\n  components: {\n    uploadFrom: uploadFrom\n  },\n  data: function data() {\n    return {\n      isChoice: '单选',\n      isChoiceD: '多选',\n      gridPic: {\n        xl: 4,\n        lg: 4,\n        md: 8,\n        sm: 12,\n        xs: 12\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 4,\n        md: 4,\n        sm: 8,\n        xs: 8\n      }\n    };\n  },\n  mounted: function mounted() {},\n  methods: {\n    getPicD: function getPicD(pc) {\n      if (this.$route.query.fodder === 'dialog') {// let str = ''\n        // for (let i = 0; i < pc.length; i++) {\n        //     nowEditor.editor.execCommand('insertimage', { src: pc[i].att_dir })\n        // }\n        // nowEditor.dialog.close(true)\n      } else {\n        var fodder = this.$route.query.fodder;\n\n        if (typeof form_create_helper !== 'undefined') {\n          var pcs = window.form_create_helper.get(this.$route.query.fodder) || [];\n          pc = pc.map(function (item) {\n            return item.att_dir;\n          });\n          var concatPc = pcs.concat(pc);\n          var pcList = Array.from(new Set(concatPc));\n          form_create_helper.set(fodder, pcList);\n          form_create_helper.close(fodder);\n        } else {\n          var _pcs = window.parent.$fromSubmit[fodder].getValue() || [];\n\n          pc = pc.map(function (item) {\n            return item.att_dir;\n          });\n\n          var _concatPc = _pcs.concat(pc);\n\n          var _pcList = Array.from(new Set(_concatPc));\n\n          window.parent.$fromSubmit[fodder].setValue(_pcList);\n          window.parent.$fromSubmit[fodder].closeUploadFrame();\n        }\n      }\n    },\n    getPic: function getPic(pc) {\n      var fodder = this.$route.query.fodder;\n\n      if (typeof form_create_helper !== 'undefined') {\n        form_create_helper.set(fodder, pc.att_dir);\n        form_create_helper.close(fodder);\n      } else {\n        window.parent.$fromSubmit[fodder].setValue(pc);\n        window.parent.$fromSubmit[fodder].closeUploadFrame();\n      }\n    } // getPic (pc) {\n    //     if (this.$route.query.fodder === 'dialog') {\n    //         /* eslint-disable */\n    //         nowEditor.dialog.close(true);\n    //         nowEditor.editor.setContent('<img src=\"'+pc.att_dir+'\">',true);\n    //     }\n    //     else {\n    //         form_create_helper.set(this.$route.query.fodder, pc.satt_dir)\n    //         form_create_helper.close(this.$route.query.fodder);\n    //     }\n    // }\n\n  }\n};", null]}