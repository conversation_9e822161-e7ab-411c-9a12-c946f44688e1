{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue?vue&type=template&id=230761d4&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"carMywrapper\"},[_c('div',{staticClass:\"type-radio\"},[_c('Form',{attrs:{\"label-width\":80}},[_c('FormItem',{attrs:{\"label\":\"卡密类型：\"}},[_c('RadioGroup',{attrs:{\"size\":\"large\"},model:{value:(_vm.cartMyType),callback:function ($$v) {_vm.cartMyType=$$v},expression:\"cartMyType\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"固定卡密\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"一次性卡密\")])],1),(_vm.cartMyType == 1)?_c('div',[_c('div',{staticClass:\"stock-disk\"},[_c('Input',{attrs:{\"size\":\"large\",\"type\":\"textarea\",\"rows\":4,\"placeholder\":\"填写卡密信息\"},model:{value:(_vm.fixedCar.disk_info),callback:function ($$v) {_vm.$set(_vm.fixedCar, \"disk_info\", $$v)},expression:\"fixedCar.disk_info\"}})],1),_c('div',{staticClass:\"stock-input\"},[_c('Input',{attrs:{\"type\":\"number\",\"size\":\"large\",\"placeholder\":\"填写库存数量\"},model:{value:(_vm.fixedCar.stock),callback:function ($$v) {_vm.$set(_vm.fixedCar, \"stock\", $$v)},expression:\"fixedCar.stock\"}},[_c('span',{attrs:{\"slot\":\"append\"},slot:\"append\"},[_vm._v(\"件\")])])],1)]):_vm._e(),(_vm.cartMyType == 2)?_c('div',{staticClass:\"scroll-virtual\"},_vm._l((_vm.virtualList),function(item,index){return _c('div',{key:index,staticClass:\"acea-row row-middle mb10\"},[_c('span',{staticClass:\"mr10 virtual-title\"},[_vm._v(\"卡号\"+_vm._s(index + 1)+\"：\")]),_c('Input',{staticClass:\"mr10 width15\",attrs:{\"type\":\"text\",\"placeholder\":\"请输入卡号(非必填)\"},model:{value:(item.key),callback:function ($$v) {_vm.$set(item, \"key\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"item.key\"}}),_c('span',{staticClass:\"mr10 virtual-title\"},[_vm._v(\"卡密\"+_vm._s(index + 1)+\"：\")]),_c('Input',{staticClass:\"mr10 width15\",attrs:{\"type\":\"text\",\"placeholder\":\"请输入卡密\"},model:{value:(item.value),callback:function ($$v) {_vm.$set(item, \"value\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"item.value\"}}),_c('span',{staticClass:\"deteal-btn\",on:{\"click\":function($event){return _vm.removeVirtual(index)}}},[_vm._v(\"删除\")])],1)}),0):_vm._e(),(_vm.cartMyType == 2)?_c('div',{staticClass:\"add-more\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleAdd}},[_vm._v(\"添加卡密\")]),_c('Upload',{ref:\"upload\",staticClass:\"ml10\",attrs:{\"action\":_vm.cardUrl,\"before-upload\":_vm.beforeUpload,\"headers\":_vm.header,\"on-success\":_vm.upFile,\"format\":['xlsx'],\"on-format-error\":_vm.handleFormatError}},[_c('Button',{attrs:{\"type\":\"success\"}},[_vm._v(\"导入卡密\")])],1),_c('Button',{staticClass:\"download\",attrs:{\"type\":\"default\",\"icon\":\"ios-download-outline\"},on:{\"click\":_vm.getCarMyList}},[_vm._v(\"下载卡密模板\")])],1):_vm._e()],1)],1)],1),_c('div',{staticClass:\"footer\"},[_c('Button',{staticClass:\"btns\",attrs:{\"type\":\"primary\",\"ghost\":\"\"},on:{\"click\":_vm.cancel}},[_vm._v(\"取消\")]),_c('Button',{staticClass:\"btns\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.subBtn}},[_vm._v(\"确定\")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}