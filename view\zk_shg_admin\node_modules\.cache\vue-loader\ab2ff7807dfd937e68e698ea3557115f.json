{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\goodPool\\index.vue?vue&type=template&id=412b460a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\goodPool\\index.vue", "mtime": 1712136182752}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n  <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n    <div class=\"card_pd\">\n      <!-- 查询条件 -->\n      <Form\n      ref=\"formValidate\"\n      inline\n      :model=\"formValidate\"\n      :label-width=\"labelWidth\"\n      :label-position=\"labelPosition\"\n      @submit.native.prevent\n    >\n      <FormItem label=\"商品分类：\" prop=\"categoryIds\">\n        <el-cascader\n            placeholder=\"请选择商品分类\"\n            class=\"input-add\"\n            size=\"mini\"\n            v-model=\"formValidate.categoryIds\"\n            :options=\"data1\"\n            :props=\"props\"\n            @change=\"selChange\"\n            filterable\n            clearable\n        >\n        </el-cascader>\n      </FormItem>\n      <FormItem label=\"商品源头：\">\n        <Select v-model=\"formValidate.filter\" multiple  class=\"input-add\">\n          <Option\n            v-for=\"(itemn, indexn) in treeData.withdrawal\"\n            :value=\"itemn.value\"\n            :key=\"indexn\"\n            >{{ itemn.title }}</Option\n          >\n        </Select>\n      </FormItem>\n      <FormItem label=\"价格区间：\">\n        <InputNumber\n        v-model=\"formValidate.priceStart\"\n        ></InputNumber>\n        --\n        <InputNumber\n        v-model=\"formValidate.priceEnd\"\n        ></InputNumber>\n      </FormItem>\n      <FormItem label=\"起批量：\">\n        <InputNumber\n        v-model=\"formValidate.quantityBegin\"\n        ></InputNumber>\n      </FormItem>\n      <FormItem label=\"搜索：\">\n        <div class=\"acea-row row-middle\">\n          <Input\n            placeholder=\"商品关键字\"\n            element-id=\"keywords\"\n            v-model=\"formValidate.keywords\"\n             class=\"input-width\"\n          />\n        </div>\n      </FormItem>\n      <FormItem>\n        <Button type=\"primary\" @click=\"selChange\" class=\"btn-add\">查询</Button>\n        <Button @click=\"reset\">重置</Button>\n      </FormItem>\n    </Form>\n    </div>\n  </Card>\n  <Card :bordered=\"false\" dis-hover>\n    <!-- 表格 -->\n    <Table\n      ref=\"table\"\n      :columns=\"columns\"\n      :data=\"tabList\"\n      class=\"ivu-mt\"\n      :loading=\"loading\"\n      no-data-text=\"暂无数据\"\n      no-filtered-data-text=\"暂无筛选结果\"\n    >\n      <template slot-scope=\"{ row }\" slot=\"name\">\n        <div>\n          {{ row.subject }}\n        </div>\n      </template>\n      <template slot-scope=\"{ row }\" slot=\"companyInfo\">\n        <div>\n          公司名称：{{ row.companyInfo.companyName }}<br />\n          省：{{ row.companyInfo.province }}<br />\n          城市：{{ row.companyInfo.city }}\n        </div>\n      </template>\n      <template slot-scope=\"{ row }\" slot=\"price\">\n        <div>\n          <div style=\"color: red;font-weight:bold\">结算价：¥{{ row.offerPrice.consignPrice }}</div>\n          划线价(仅供参考)：{{ row.offerPrice.priceUnderLine }}<br />\n          正常价格：{{ row.offerPrice.price }}\n        </div>\n      </template>\n      <template slot-scope=\"{ row, index }\" slot=\"qualityEvaluation\">\n        \t综合得分：{{ row.qualityEvaluation.compositeScore }}<br />\n          采购咨询分：{{ row.qualityEvaluation.consultationScore }}<br />\n          纠纷解决分：{{ row.qualityEvaluation.disputeScore }}<br />\n          品质体验分：{{ row.qualityEvaluation.logisticsScore }}<br />\n          物流时效分：{{ row.qualityEvaluation.goodsScore }}<br />\n          退换体验分：{{ row.qualityEvaluation.returnScore }}\n      </template>\n      <template slot-scope=\"{ row, index }\" slot=\"image\">\n        <viewer>\n          <div class=\"tabBox_img\">\n            <img v-lazy=\"row.offerImage.imageUrl\" />\n          </div>\n        </viewer>\n      </template>\n\n      <template\n        slot-scope=\"{ row }\"\n        slot=\"createModalFrame\"\n      >\n        <a @click=\"dao(row.openOfferId)\">导入商品库</a>\n      </template>\n    </Table>\n    <div class=\"acea-row row-right page\">\n      <Page\n        :total=\"total\"\n        :current=\"formValidate.pageNum\"\n        show-elevator\n        show-total\n        @on-change=\"pageChange\"\n        :page-size=\"formValidate.pageSize\"\n      />\n    </div>\n  </Card>\n  <!-- 同步分类设置 -->\n  <Modal v-model=\"batchModal\" title=\"选择分类\" width=\"400\" class-name=\"batch-modal\">\n    <el-cascader\n        v-model=\"cate_id\"\n        :options=\"data2\"\n        :props=\"{ emitPath: false, multiple: true, checkStrictly: true }\"\n        size=\"small\"\n        filterable\n        clearable\n        :class=\"{ single: !cate_id.length }\"\n    >\n    </el-cascader>\n    <div slot=\"footer\">\n      <Button type=\"primary\" @click=\"saveBatch\">确认</Button>\n    </div>\n  </Modal>\n</div>\n", null]}