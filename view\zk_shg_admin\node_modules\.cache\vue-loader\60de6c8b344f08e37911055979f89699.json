{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\queueList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\queueList.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { queueIndex, deliveryLog, queueAgain, queueDel, batchOrderDelivery, stopWrongQueue } from '@/api/order';\nimport { mapState } from 'vuex';\n\nexport default {\n    data() {\n        return {\n            modal: false,\n            columns1: [\n                {\n                    title: 'ID',\n                    key: 'id'\n                },\n                {\n                    title: '操作时间',\n                    key: 'add_time'\n                },\n                {\n                    title: '发货单数',\n                    key: 'total_num'\n                },\n                {\n                    title: '成功发货单数',\n                    key: 'success_num'\n                },\n                {\n                    title: '发货类型',\n                    key: 'title'\n                },\n                {\n                    title: '状态',\n                    key: 'status_cn'\n                },\n                {\n                    title: '操作',\n                    slot: 'action',\n                    fixed: 'right',\n                    width: 150,\n                    align: 'center'\n                }\n            ],\n            data1: [],\n            page1: {\n                total: 0, // 总条数\n                pageNum: 1, // 当前页\n                pageSize: 10 // 每页显示条数\n            },\n            formValidate: {\n                type: '',\n                status: '',\n                data: ''\n            },\n            options: {\n                shortcuts: [\n                    {\n                        text: '今天',\n                        value() {\n                            const end = new Date();\n                            const start = new Date();\n                            start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()));\n                            return [start, end];\n                        }\n                    },\n                    {\n                        text: '昨天',\n                        value() {\n                            const end = new Date();\n                            const start = new Date();\n                            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n                            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n                            return [start, end];\n                        }\n                    },\n                    {\n                        text: '最近7天',\n                        value() {\n                            const end = new Date();\n                            const start = new Date();\n                            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 6)));\n                            return [start, end];\n                        }\n                    },\n                    {\n                        text: '最近30天',\n                        value() {\n                            const end = new Date();\n                            const start = new Date();\n                            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 29)));\n                            return [start, end];\n                        }\n                    },\n\t\t\t\t\t{\n\t\t\t\t\t  text: \"上月\",\n\t\t\t\t\t  value() {\n\t\t\t\t\t    const end = new Date();\n\t\t\t\t\t    const start = new Date();\n\t\t\t\t\t\tconst day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n\t\t\t\t\t    start.setTime(\n\t\t\t\t\t      start.setTime(\n\t\t\t\t\t        new Date(new Date().getFullYear(), new Date().getMonth()-1, 1)\n\t\t\t\t\t      )\n\t\t\t\t\t    );\n\t\t\t\t\t\tend.setTime(\n\t\t\t\t\t\t  end.setTime(\n\t\t\t\t\t\t    new Date(new Date().getFullYear(), new Date().getMonth()-1, day)\n\t\t\t\t\t\t  )\n\t\t\t\t\t\t);\n\t\t\t\t\t    return [start, end];\n\t\t\t\t\t  },\n\t\t\t\t\t},\n                    {\n                        text: '本月',\n                        value() {\n                            const end = new Date();\n                            const start = new Date();\n                            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));\n                            return [start, end];\n                        }\n                    },\n                    {\n                        text: '本年',\n                        value() {\n                            const end = new Date();\n                            const start = new Date();\n                            start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)));\n                            return [start, end];\n                        }\n                    }\n                ]\n            },\n            timeVal: [],\n            typeList: [\n                // {\n                //     label: '批量发放用户优惠券',\n                //     value: '1'\n                // },\n                // {\n                //     label: '批量设置用户分组',\n                //     value: '2'\n                // },\n                // {\n                //     label: '批量设置用户标签',\n                //     value: '3'\n                // },\n                // {\n                //     label: '批量下架商品',\n                //     value: '4'\n                // },\n                // {\n                //     label: '批量删除商品规格',\n                //     value: '5'\n                // },\n                {\n                    label: '批量删除订单',\n                    value: '6'\n                },\n                {\n                    label: '批量手动发货',\n                    value: '7'\n                },\n                {\n                    label: '批量打印电子面单',\n                    value: '8'\n                },\n                {\n                    label: '批量配送',\n                    value: '9'\n                },\n                {\n                    label: '批量虚拟发货',\n                    value: '10'\n                }\n            ],\n            statusList: [\n                {\n                    label: '未处理',\n                    value: '0'\n                },\n                {\n                    label: '处理中',\n                    value: '1'\n                },\n                {\n                    label: '已完成',\n                    value: '2'\n                },\n                {\n                    label: '处理失败',\n                    value: '3'\n                }\n            ],\n            columns2: [\n                {\n                    title: '订单ID',\n                    key: 'order_id'\n                },\n                {\n                    title: '物流公司',\n                    key: 'delivery_name'\n                },\n                {\n                    title: '物流单号',\n                    key: 'delivery_id'\n                },\n                {\n                    title: '处理状态',\n                    key: 'status_cn'\n                },\n                {\n                    title: '异常原因',\n                    key: 'error'\n                }\n            ],\n            columns3: [\n                {\n                    title: '订单ID',\n                    key: 'order_id'\n                },\n                {\n                    title: '备注',\n                    key: 'fictitious_content'\n                },\n                {\n                    title: '处理状态',\n                    key: 'status_cn'\n                },\n                {\n                    title: '异常原因',\n                    key: 'error'\n                }\n            ],\n            columns5: [\n                {\n                    title: '订单ID',\n                    key: 'order_id'\n                },\n                {\n                    title: '配送员',\n                    key: 'delivery_name'\n                },\n                {\n                    title: '配送员电话',\n                    key: 'delivery_id'\n                },\n                {\n                    title: '处理状态',\n                    key: 'status_cn'\n                },\n                {\n                    title: '异常原因',\n                    key: 'error'\n                }\n            ],\n            columns4: [],\n            data2: [],\n            page2: {\n                total: 0, // 总条数\n                pageNum: 1, // 当前页\n                pageSize: 12 // 每页显示条数\n            },\n            modal1: false,\n            deliveryLog: null,\n            deliveryLogId: 0,\n            deliveryLogType: '',\n            loading: false,\n            loading2: false\n        };\n    },\n    computed: {\n        ...mapState('admin/layout', ['isMobile']),\n        labelWidth() {\n            return this.isMobile ? undefined : 75;\n        },\n        labelPosition() {\n            return this.isMobile ? 'top' : 'right';\n        }\n    },\n    created() {\n        this.getQueue();\n    },\n    methods: {\n        getQueue() {\n            let data = {\n                page: this.page1.pageNum,\n                limit: this.page1.pageSize\n            };\n            if (this.formValidate.status) {\n                data.status = this.formValidate.status;\n            }\n            if (this.formValidate.type) {\n                data.type = this.formValidate.type;\n            }\n            if (this.formValidate.data) {\n                data.data = this.formValidate.data;\n            }\n            this.loading = true;\n            queueIndex(data)\n                .then(res => {\n                    this.loading = false;\n                    this.data1 = res.data.list;\n                    this.page1.total = res.data.count;\n                })\n                .catch(err => {\n                    this.loading = false;\n                });\n        },\n        pageChange(index) {\n            this.page1.pageNum = index;\n            this.getQueue();\n        },\n        // 查看-分页\n        pageChange2(index) {\n            this.page2.pageNum = index;\n            this.getDeliveryLog();\n        },\n        limitChange(limit) {\n            this.page1.pageSize = limit;\n            this.getQueue();\n        },\n        limitChange2(limit) {\n            this.page2.pageSize = limit;\n            this.getDeliveryLog();\n        },\n        // 搜索-操作时间\n        onchangeTime(time) {\n            this.timeVal = time;\n            this.formValidate.data = this.timeVal[0] ? this.timeVal.join('-') : '';\n            this.page1.pageNum = 1;\n            this.getQueue();\n        },\n        // 搜索-类型\n        typeSearchs() {\n            this.page1.pageNum = 1;\n            this.getQueue();\n        },\n        // 搜索-状态\n        statusSearchs() {\n            this.page1.pageNum = 1;\n            this.getQueue();\n        },\n        // 查看-获取数据\n        getDeliveryLog() {\n            this.loading2 = true;\n            deliveryLog(this.deliveryLogId, this.deliveryLogType, {\n                page: this.page2.pageNum,\n                limit: this.page2.pageSize\n            })\n                .then(res => {\n                    this.loading2 = false;\n                    this.data2 = res.data.list;\n                    this.page2.total = res.data.count;\n                })\n                .catch(err => {\n                    this.loading2 = false;\n                });\n        },\n        // 查看\n        deliveryLook(row) {\n            this.modal1 = true;\n            this.deliveryLogId = row.id;\n            this.deliveryLogType = row.cache_type;\n            this.deliveryLog = row;\n            switch (row.type) {\n                case 7:\n                case 8:\n                    this.columns4 = this.columns2;\n                    break;\n                case 9:\n                    this.columns4 = this.columns5;\n                    break;\n                case 10:\n                    this.columns4 = this.columns3;\n                    break;\n            }\n            this.getDeliveryLog();\n        },\n        // 更多\n        changeMenu(row, $event) {\n            switch ($event) {\n                // 下载\n                case '1':\n                    batchOrderDelivery(row.id, row.type, row.cache_type)\n                        .then(res => {\n                            window.open(res.data[0]);\n                        })\n                        .catch(err => {\n                            this.$Message.error(err.msg);\n                        });\n                    break;\n                // 重新执行\n                case '2':\n                    this.queueAgain(row.id, row.type);\n                    break;\n                // 停止任务\n                case '3':\n                    this.$Modal.confirm({\n                        title: '谨慎操作',\n                        content: '<p>确认停止该任务？</p>',\n                        onOk: () => {\n                            this.stopQueue(row.id);\n                        }\n                    });\n                    break;\n                // 清除异常任务\n                case '4':\n                    this.queueDel(row.id, row.type);\n                    break;\n            }\n        },\n        // 重新执行\n        queueAgain(id, type) {\n            queueAgain(id, type)\n                .then(res => {\n                    this.$Message.success(res.msg);\n                    this.getQueue();\n                })\n                .catch(err => {\n                    this.$Message.error(err.msg);\n                });\n        },\n        // 清除异常任务\n        queueDel(id, type) {\n            queueDel(id, type)\n                .then(res => {\n                    this.$Message.success(res.msg);\n                    this.getQueue();\n                })\n                .catch(err => {\n                    this.$Message.error(err.msg);\n                });\n        },\n        // 停止任务\n        stopQueue(id) {\n            stopWrongQueue(id)\n                .then(res => {\n                    this.$Message.success(res.msg);\n                    this.getQueue();\n                })\n                .catch(err => {\n                    this.$Message.error(err.msg);\n                });\n        }\n    }\n};\n", null]}