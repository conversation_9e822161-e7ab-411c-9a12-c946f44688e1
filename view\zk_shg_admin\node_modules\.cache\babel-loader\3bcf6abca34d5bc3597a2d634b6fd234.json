{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\type\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\type\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { userMemberShip, memberShipSave, memberCard, deleteCard } from \"@/api/user\";\nexport default {\n  name: \"list\",\n  data: function data() {\n    return {\n      thead: [{\n        title: \"ID\",\n        key: \"id\",\n        maxWidth: 60\n      }, {\n        title: \"会员名\",\n        key: \"title\"\n      }, {\n        title: \"有限期（天）\",\n        key: \"vip_day\",\n        render: function render(h, params) {\n          return h(\"span\", params.row.vip_day === -1 ? \"永久\" : params.row.vip_day);\n        }\n      }, {\n        title: \"原价\",\n        key: \"price\"\n      }, {\n        title: \"优惠价\",\n        key: \"pre_price\"\n      }, {\n        title: \"排序\",\n        key: \"sort\"\n      }, {\n        title: \"是否开启\",\n        slot: \"is_del\"\n      }, {\n        title: \"操作\",\n        slot: \"action\"\n      }],\n      tbody: [],\n      loading: false,\n      modal: false,\n      rowEdit: {},\n      rowModelType: \"编辑\",\n      rule: [{\n        type: \"hidden\",\n        field: \"id\",\n        value: \"\"\n      }, {\n        type: \"hidden\",\n        field: \"type\",\n        value: \"\"\n      }, {\n        type: \"input\",\n        field: \"title\",\n        title: \"会员名\",\n        value: \"\",\n        props: {\n          disabled: false,\n          required: true\n        },\n        validate: [{\n          type: \"string\",\n          max: 5,\n          min: 1,\n          message: \"请输入长度为1-5的名称\",\n          requred: true\n        }]\n      }, {\n        type: \"InputNumber\",\n        field: \"vip_day\",\n        title: \"有限期（天）\",\n        value: null,\n        props: {\n          precision: 0,\n          disabled: false,\n          type: \"text\",\n          required: true\n        },\n        validate: [{\n          type: \"number\",\n          max: 1000000,\n          min: 0,\n          message: \"最大只能输入1000000,最小为0\",\n          requred: true\n        }]\n      }, {\n        type: \"InputNumber\",\n        field: \"price\",\n        title: \"原价\",\n        value: null,\n        props: {\n          min: 0,\n          disabled: false,\n          required: true\n        },\n        validate: [{\n          type: \"number\",\n          max: 1000000,\n          min: 0,\n          message: \"最大只能输入1000000,最小为0\",\n          requred: true\n        }]\n      }, {\n        type: \"InputNumber\",\n        field: \"pre_price\",\n        title: \"优惠价\",\n        value: null,\n        props: {\n          min: 0,\n          disabled: false,\n          required: true\n        },\n        validate: [{\n          type: \"number\",\n          max: 1000000,\n          min: 0.01,\n          message: \"最大只能输入1000000,最小为0.01\",\n          requred: true\n        }]\n      }, {\n        type: \"InputNumber\",\n        field: \"sort\",\n        title: \"排序\",\n        value: 0,\n        props: {\n          precision: 0,\n          min: 1,\n          max: 1000000,\n          disabled: false\n        },\n        validate: [{\n          type: \"number\",\n          max: 1000000,\n          min: 0,\n          message: \"最大只能输入1000000,最小为0\",\n          requred: true\n        }]\n      }, {\n        type: \"radio\",\n        field: \"is_label\",\n        title: \"特惠标签\",\n        value: 0,\n        options: [{\n          value: 1,\n          label: \"是\"\n        }, {\n          value: 0,\n          label: \"否\"\n        }]\n      }],\n      fapi: {\n        id: \"\",\n        pre_price: null,\n        price: null,\n        sort: null,\n        title: \"\",\n        type: \"owner\",\n        vip_day: null\n      }\n    };\n  },\n  created: function created() {\n    this.getMemberShip();\n  },\n  mounted: function mounted() {},\n  methods: {\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this = this;\n\n      var data = {\n        id: row.id,\n        is_del: row.is_del\n      };\n      memberCard(data).then(function (res) {\n        _this.$Message.success(res.msg);\n\n        _this.getMemberShip();\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n      });\n    },\n    cancel: function cancel() {\n      this.fapi.resetFields();\n    },\n    getMemberShip: function getMemberShip() {\n      var _this2 = this;\n\n      this.loading = true;\n      userMemberShip().then(function (res) {\n        _this2.loading = false;\n        var _res$data = res.data,\n            count = _res$data.count,\n            list = _res$data.list;\n        _this2.total = count;\n        _this2.tbody = list;\n      }).catch(function (err) {\n        _this2.loading = false;\n\n        _this2.$Message.error(err.msg);\n      });\n    },\n    addType: function addType() {\n      this.rowEdit.id = 0;\n      this.rowModelType = \"新增\";\n      this.rule[1].value = \"owner\";\n      this.rule[3].props.disabled = false;\n      this.rule[5].props.disabled = false;\n      this.rowEdit.title = \"\"; // this.cancel();\n\n      this.modal = true;\n    },\n    del: function del(row, tit, num) {\n      var _this3 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"user/member_ship/delete/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this3.$Message.success(res.msg);\n\n        _this3.getMemberShip();\n      }).catch(function (res) {\n        _this3.$Message.error(res.msg);\n      });\n    },\n    editType: function editType(row) {\n      this.rule.forEach(function (item) {\n        for (var key in row) {\n          if (row.hasOwnProperty(key)) {\n            if (item.field === key) {\n              if (key === \"vip_day\") {\n                if (row[key] === -1 || row[key] == \"永久\") {\n                  item.type = \"Input\";\n                  item.props.disabled = true;\n                  item.validate = [{\n                    type: \"string\",\n                    message: \"\",\n                    requred: true\n                  }];\n                  row[key] = \"永久\";\n                } else {\n                  item.type = \"InputNumber\";\n                  item.props.disabled = false;\n                  item.props.min = 1;\n                  item.validate = [{\n                    type: \"number\",\n                    max: 1000000,\n                    min: 0,\n                    message: \"最大只能输入1000000,最小为0\",\n                    requred: true\n                  }];\n                }\n              }\n\n              if ([\"price\"].includes(key)) {\n                row[key] = parseFloat(row[key]);\n              }\n\n              if ([\"pre_price\"].includes(key)) {\n                row[key] = parseFloat(row[key]);\n\n                if (row.type == 'free') {\n                  item.props.disabled = true;\n                  item.validate = [];\n                } else {\n                  item.props.disabled = false;\n                  item.validate = [{\n                    type: \"number\",\n                    max: 1000000,\n                    min: 0.01,\n                    message: \"最大只能输入1000000,最小为0.01\",\n                    requred: true\n                  }];\n                }\n              }\n\n              item.value = row[key];\n            }\n          }\n        }\n      });\n      this.rowModelType = \"编辑\";\n      this.rowEdit = JSON.parse(JSON.stringify(row));\n      this.modal = true;\n    },\n    onSubmit: function onSubmit(formData) {\n      var _this4 = this;\n\n      memberShipSave(this.rowEdit.id, formData).then(function (res) {\n        _this4.modal = false;\n\n        _this4.$Message.success(res.msg);\n\n        _this4.getMemberShip();\n\n        _this4.cancel();\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}