{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport linkaddress from \"@/components/linkaddress\";\nexport default {\n  name: \"AreaBox\",\n  components: {\n    linkaddress: linkaddress\n  },\n  props: {\n    areaInit: {\n      type: Object,\n      default: function _default() {}\n    },\n    areaDataIndex: {\n      type: Number,\n      default: null\n    },\n    link: {\n      type: String,\n      default: \"\"\n    },\n    title: {\n      type: String,\n      default: \"\"\n    },\n    type: {\n      type: Number,\n      default: -1\n    },\n    parentWidth: {\n      type: Number,\n      default: 0\n    },\n    parentHeight: {\n      type: Number,\n      default: 0\n    }\n  },\n  data: function data() {\n    return {\n      areaTitle: \"\",\n      url: \"\",\n      editBoxShow: false,\n      promptText: \"双击设置热区\",\n      // box操作初始点\n      move: {\n        // 拖动\n        startX: 0,\n        starY: 0,\n        // 形变\n        start1X: 0,\n        start1Y: 0\n      }\n    };\n  },\n  computed: {\n    isSet: function isSet() {\n      return !!this.link;\n    }\n  },\n  watch: {\n    title: function title(val) {\n      this.areaTitle = val;\n    },\n    link: function link(val) {\n      this.url = val;\n    }\n  },\n  mounted: function mounted() {\n    this.url = this.link;\n  },\n  methods: {\n    // 删除\n    del: function del() {\n      this.$emit(\"delAreaBox\", this.areaDataIndex);\n    },\n    // 添加网址\n    addURL: function addURL() {\n      if (!this.url) {\n        this.$Message.error(\"请输入链接\");\n      } else {\n        this.$emit(\"addURL\", this.areaDataIndex, this.url);\n        this.editBoxShow = false;\n      }\n    },\n    // 开始拖动限制范围\n    mouseDownLint: function mouseDownLint(e) {\n      var _this = this;\n\n      console.log(e);\n      e.preventDefault();\n      this.starX = e.clientX;\n      this.starY = e.clientY;\n      var childrenDiv = e.target || e; //获取子元素的宽高\n\n      var childrenWidth = childrenDiv.getBoundingClientRect().width;\n      var childrenHight = childrenDiv.getBoundingClientRect().height; // console.log(childrenWidth, childrenHight)\n\n      if (!document.onmousemove) {\n        var initX = this.areaInit.starX;\n        var initY = this.areaInit.starY;\n\n        document.onmousemove = function (ev) {\n          // 移动位置\n          var nLeft = initX + ev.clientX - _this.starX;\n          var nTop = initY + ev.clientY - _this.starY;\n          nLeft = nLeft <= 0 ? 0 : nLeft; //判断左边是否越界\n\n          nTop = nTop <= 0 ? 0 : nTop; //判断上边是否越界\n\n          var nRight = nLeft + childrenWidth;\n          var nBottom = nTop + childrenHight; // 判断右边是否越界\n\n          if (nRight >= _this.parentWidth) {\n            nLeft = _this.parentWidth - childrenWidth;\n          } // 判断下边是否越界\n\n\n          if (nBottom >= _this.parentHeight) {\n            nTop = _this.parentHeight - childrenHight;\n          }\n\n          _this.areaInit.starX = nLeft;\n          _this.areaInit.starY = nTop;\n        };\n      }\n    },\n    // 开始拖动不限制范围\n    mouseDown: function mouseDown(e) {\n      var _this2 = this;\n\n      e.preventDefault();\n      this.starX = e.clientX;\n      this.starY = e.clientY;\n\n      if (!document.onmousemove) {\n        var initX = this.areaInit.starX;\n        var initY = this.areaInit.starY;\n\n        document.onmousemove = function (ev) {\n          _this2.areaInit.starX = initX + ev.clientX - _this2.starX;\n          _this2.areaInit.starY = initY + ev.clientY - _this2.starY;\n        };\n      }\n    },\n    // 结束拖动/变形\n    mouseUp: function mouseUp() {\n      document.onmousemove = null;\n    },\n    // 形变开始\n    shapeDown: function shapeDown(e) {\n      var _this3 = this;\n\n      e.preventDefault();\n      this.star1X = e.clientX;\n      this.star1Y = e.clientY; // 获取左部和底部的偏移量\n\n      if (!document.onmousemove) {\n        var initX = this.areaInit.areaWidth;\n        var initY = this.areaInit.areaHeight;\n\n        document.onmousemove = function (ev) {\n          _this3.areaInit.areaWidth = initX + ev.clientX - _this3.star1X;\n          _this3.areaInit.areaHeight = initY + ev.clientY - _this3.star1Y;\n        };\n      }\n    },\n    getLink: function getLink() {\n      this.$refs.linkaddres.modals = true;\n    },\n    linkUrl: function linkUrl(e) {\n      this.url = e;\n    }\n  }\n};", null]}