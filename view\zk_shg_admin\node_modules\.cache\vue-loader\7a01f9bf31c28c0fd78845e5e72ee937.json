{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue?vue&type=template&id=6b24a4ca&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_menu_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"hot_imgs\"},[(_vm.configData.title)?_c('div',{staticClass:\"title\"},[_vm._v(\"\\n    \"+_vm._s(_vm.configData.title)+\"\\n  \")]):_vm._e(),_c('div',{staticClass:\"list-box\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.configData.list,\"group\":\"peoples\",\"handle\":\".move-icon\"}},_vm._l((_vm.configData.list),function(item,index){return _c('div',{key:index,staticClass:\"item\"},[(!_vm.configData.isCube)?_c('div',{staticClass:\"delect-btn\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete(item, index)}}},[_c('span',{staticClass:\"iconfont-diy icondel_1\"})]):_vm._e(),_c('div',{staticClass:\"move-icon\"},[_c('span',{staticClass:\"iconfont-diy iconxingzhuangjiehe\"})]),_c('div',{staticClass:\"img-box\",on:{\"click\":function($event){return _vm.modalPicTap('单选', index)}}},[(item.img)?_c('img',{attrs:{\"src\":item.img,\"alt\":\"\"}}):_c('div',{staticClass:\"upload-box\"},[_c('Icon',{attrs:{\"type\":\"ios-add\",\"size\":\"50\"}})],1)]),_c('div',{staticClass:\"info\"},[_vm._l((item.info),function(infos,key){return _c('div',{key:key,staticClass:\"info-item\"},[_c('span',{staticClass:\"span\"},[_vm._v(_vm._s(infos.title))]),_c('div',{staticClass:\"input-box\",on:{\"click\":function($event){return _vm.getLink(index, key, item.info)}}},[(_vm.configData.isCube)?_c('Input',{attrs:{\"icon\":key == item.info.length - 1 ? 'ios-arrow-forward' : '',\"readonly\":key == item.info.length - 1 ? true : false,\"placeholder\":infos.tips,\"maxlength\":infos.max},on:{\"on-blur\":_vm.onBlur},model:{value:(infos.value),callback:function ($$v) {_vm.$set(infos, \"value\", $$v)},expression:\"infos.value\"}}):_c('Input',{attrs:{\"icon\":key == item.info.length - 1 ? 'ios-arrow-forward' : '',\"readonly\":key == item.info.length - 1 ? true : false,\"placeholder\":infos.tips,\"maxlength\":infos.max,\"show-word-limit\":\"\"},model:{value:(infos.value),callback:function ($$v) {_vm.$set(infos, \"value\", $$v)},expression:\"infos.value\"}})],1)])}),(_vm.configData.type)?_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"span\"},[_vm._v(\"状态\")]),_c('i-switch',{model:{value:(item.show),callback:function ($$v) {_vm.$set(item, \"show\", $$v)},expression:\"item.show\"}})],1):_vm._e()],2)])}),0),_c('div',[_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"上传图片\",\"mask-closable\":false,\"z-index\":1},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic}}):_vm._e()],1)],1)],1),(_vm.configData.list)?[(_vm.configData.list.length < _vm.configData.maxList)?_c('div',{staticClass:\"add-btn\"},[_c('Button',{staticClass:\"btn\",attrs:{\"type\":\"primary\",\"ghost\":\"\"},on:{\"click\":_vm.addBox}},[_c('span',{staticClass:\"iconfont iconjiahao\"}),_vm._v(_vm._s(_vm.configData.bnt)+\"\\n      \")])],1):_vm._e()]:_vm._e(),_c('linkaddress',{ref:\"linkaddres\",on:{\"linkUrl\":_vm.linkUrl}})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}