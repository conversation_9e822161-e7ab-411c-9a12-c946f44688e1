{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue?vue&type=template&id=0d2b2f52&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_card_2.vue", "mtime": 1734512585792}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"mobile-page\" :style=\"{\n  background: bottomBgColor,\n  marginTop: mTop + 'px',\n  paddingTop: topConfig + 'px',\n  paddingBottom: bottomConfig + 'px',\n  paddingLeft: prConfig + 'px',\n  paddingRight: prConfig + 'px'\n}\">\n  <div class=\"home_product\">\n    <div class=\"hd_nav\" v-if=\"styleConfig == 0\">\n      <div class=\"item\" :class=\"index == tabCur ? 'active' : ''\" v-for=\"(item, index) in navlist\" :key=\"index\">\n        <p class=\"title\" :style=\"{ color: index == tabCur ? (toneConfig ? textColor2 : '#E93323') : '#282828' }\">\n          {{ item.chiild[0].val || '标题' }}</p>\n        <span class=\"label\" :style=\"{ background: index == tabCur ? (toneConfig ? decorateColor : themeColor) : '' }\"\n          v-if=\"item.chiild[1].val\">{{ item.chiild[1].val || '标题简介' }}</span>\n      </div>\n    </div>\n    <div class=\"menus\" :class=\"styleConfig == 2 ? 'on' : ''\" v-else>\n      <div class=\"item\" :class=\"index == tabCur ? 'on' : ''\" v-if=\"styleConfig == 1\" v-for=\"(item, index) in navlist\"\n        :key=\"index\" :style=\"{\n          color: index == tabCur ? (toneConfig ? textColor : '#333') : '#282828'\n        }\">{{ item.chiild[0].val || '标题' }}<span :style=\"{\n      background: toneConfig ? decorateColor : themeColor\n    }\"></span></div>\n      <div class=\"item\" :class=\"index == tabCur ? 'on3' : ''\" v-if=\"styleConfig == 2\" v-for=\"(item, index) in navlist\"\n        :key=\"index\" :style=\"{\n          color: index == tabCur ? (toneConfig ? textColor2 : '#E93323') : '#282828'\n        }\">{{ item.chiild[0].val || '标题' }}<span :style=\"{\n        borderColor: toneConfig ? decorateColor2 : colorStyle.theme\n      }\"></span></div>\n      <div class=\"item\" :class=\"index == tabCur ? 'on2' : ''\" v-if=\"styleConfig == 3\" v-for=\"(item, index) in navlist\"\n        :key=\"index\" :style=\"{\n          color: index == tabCur ? (toneConfig ? textColor3 : '#fff') : '#282828',\n          background: index == tabCur ? (toneConfig ? decorateColor : themeColor) : ''\n        }\">{{ item.chiild[0].val || '标题' }}</div>\n      <div class=\"item pic\" v-if=\"styleConfig == 4\" v-for=\"(item, index) in navlist\" :key=\"index\">\n        <div class=\"pictrue acea-row row-center-wrapper\"\n          :style=\"{ borderColor: index == tabCur ? (toneConfig ? decorateColorLeft : '#E93323') : '#EEEEEE' }\">\n          <img class=\"img\" :src=\"item.image\" v-if=\"item.image\" />\n          <img src=\"../../assets/images/shan.png\" v-else />\n        </div>\n        <div class=\"title\" :style=\"{\n          color: index == tabCur ? (toneConfig ? textColor3 : '#fff') : '#282828',\n          background: index == tabCur ? (toneConfig ? decorateColor : themeColor) : ''\n        }\">{{ item.chiild[0].val || '标题' }}</div>\n      </div>\n    </div>\n    <div class=\"list-wrapper\">\n      <div class=\"item\" v-for=\"(item, index) in list\" :key=\"index\">\n        <div class=\"img-box\">\n          <img class=\"img\" v-if=\"item.image\" :src=\"item.image\" alt=\"\" :style=\"{\n            borderRadius: bgRadius\n          }\">\n          <div v-else class=\"empty-box\" :style=\"{\n            borderRadius: bgRadius\n          }\">\n            <img src=\"../../assets/images/shan.png\" />\n          </div>\n        </div>\n        <div class=\"info\" :style=\"{ borderRadius: bgRadius2 }\">\n          <div class=\"title line1\">{{ item.store_name || '海南梅陇蜜瓜 2.5kg海梅陇蜜瓜' }}</div>\n          <div class=\"pictrue\">\n            <img src=\"../../assets/images/goods01.png\" />\n          </div>\n          <div class=\"price\">\n            <div class=\"num\">\n              <span>￥</span>{{ item.price ? $HandlePrice(item.price, 0) : 77 }}<span>{{ item.price ? $HandlePrice(item.price, 1) : '' }}</span>\n            </div>\n            <img src=\"../../assets/images/goods02.png\" />\n          </div>\n          <div class=\"sales\">已售{{ item.sales || 0 }}件</div>\n        </div>\n        <div class=\"jia\" v-if=\"!cartConfig\" :style=\"{\n          background: toneCartConfig ? bntBgColor : themeColor\n        }\">\n          <div class=\"jiaCon\">\n            <span class=\"iconfont iconjiahao1\" v-if=\"bntStyleConfig == 0\"></span>\n            <span class=\"iconfont icongouwuche1\" v-else></span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", null]}