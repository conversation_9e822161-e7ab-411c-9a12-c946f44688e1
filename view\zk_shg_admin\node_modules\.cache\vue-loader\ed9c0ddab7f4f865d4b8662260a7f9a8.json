{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue", "mtime": 1675991652000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport expandRow from \"./tableExpand.vue\";\nimport {\n  orderList,\n  getOrdeDatas,\n  getDataInfo,\n  getRefundFrom,\n  getnoRefund,\n  refundIntegral,\n  getDistribution,\n  writeUpdate,\n} from \"@/api/order\";\nimport {\n  getIntegralOrderDataInfo,\n  integralOrderList,\n  getIntegralOrderDistribution,\n} from \"@/api/marketing\";\nimport { erpConfig } from \"@/api/erp\";\nimport { mapState, mapMutations } from \"vuex\";\nimport editFrom from \"../../../../components/from/from\";\nimport detailsFrom from \"../handle/orderDetails\";\nimport orderRemark from \"../handle/orderRemark\";\nimport orderRecord from \"../handle/orderRecord\";\nimport orderSend from \"../handle/orderSend\";\nimport userDetails from \"@/pages/user/list/handle/userDetails\";\n\nexport default {\n  name: \"table_list\",\n  components: {\n    expandRow,\n    editFrom,\n    detailsFrom,\n    orderRemark,\n    orderRecord,\n    orderSend,\n    userDetails,\n  },\n  props: [\"where\", \"isAll\"],\n  data() {\n    return {\n\t  openErp:false,\n      delfromData: {},\n      modal: false,\n      orderList: [],\n      orderCards: [],\n      loading: false,\n      orderId: 0,\n      columns: [\n        {\n          type: \"expand\",\n          width: 30,\n          render: (h, params) => {\n            return h(expandRow, {\n              props: {\n                row: params.row,\n              },\n            });\n          },\n        },\n        {\n          title: \"订单号\",\n          align: \"center\",\n          slot: \"order_id\",\n          minWidth: 160,\n        },\n        {\n          title: \"用户信息\",\n          slot: \"nickname\",\n          minWidth: 110,\n        },\n        {\n          title: \"商品信息\",\n          slot: \"info\",\n          minWidth: 330,\n        },\n        {\n          title: \"兑换积分\",\n          key: \"total_integral\",\n          minWidth: 70,\n        },\n        {\n          title: \"兑换金额\",\n          key: \"total_price\",\n          minWidth: 70,\n        },\n        {\n          title: \"订单状态\",\n          slot: \"status_name\",\n          minWidth: 100,\n        },\n        {\n          title: \"下单时间\",\n          key: \"add_time\",\n          minWidth: 130,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n          minWidth: 150,\n          align: \"center\",\n        },\n      ],\n      page: {\n        total: 0, // 总条数\n        pageNum: 1, // 当前页\n        pageSize: 10, // 每页显示条数\n      },\n      data: [],\n      FromData: null,\n      orderDatalist: null,\n      modalTitleSs: \"\",\n      isDelIdList: [],\n      checkBox: false,\n      formSelection: [],\n      selectionCopy: [],\n      display: \"none\",\n      autoDisabled: false,\n      // isAll: -1,\n\t\t\trowActive: {}\n    };\n  },\n  computed: {\n    ...mapState(\"admin/integralOrder\", [\n      \"orderPayType\",\n      \"orderStatus\",\n      \"orderTime\",\n      \"orderNum\",\n      \"fieldKey\",\n      \"orderType\",\n      \"realName\"\n    ]),\n  },\n  mounted() {},\n  created() {\n\tthis.getErpConfig();\n    this.getList();\n  },\n  watch: {\n    orderType: function () {\n      this.page.pageNum = 1;\n      this.getList();\n    },\n    formSelection(value) {\n      this.$emit(\"order-select\", value);\n      if (value.length) {\n        this.$emit(\"auto-disabled\", 0);\n      } else {\n        this.$emit(\"auto-disabled\", 1);\n      }\n      let isDel = value.some((item) => {\n        return item.is_del === 1;\n      });\n      this.getIsDel(isDel);\n      this.getisDelIdListl(value);\n    },\n    orderList: {\n      deep: true,\n      handler(value) {\n        value.forEach((item) => {\n          this.formSelection.forEach((itm) => {\n            if (itm.id === item.id) {\n              item.checkBox = true;\n            }\n          });\n        });\n        const arr = this.orderList.filter((item) => item.checkBox);\n        if (this.orderList.length) {\n          this.checkBox = this.orderList.length === arr.length;\n        } else {\n          this.checkBox = false;\n        }\n      },\n    },\n  },\n  methods: {\n    ...mapMutations(\"admin/integralOrder\", [\"getIsDel\", \"getisDelIdListl\"]),\n\tgetErpConfig(){\n\t\terpConfig().then(res=>{\n\t\t\tthis.openErp = res.data.open_erp;\n\t\t}).catch(err=>{\n\t\t\tthis.$Message.error(err.msg);\n\t\t})\n\t},\n    selectAll(row) {\n      if (row.length) {\n        this.formSelection = row;\n        this.selectionCopy = row;\n      }\n      this.selectionCopy.forEach((item, index) => {\n        item.checkBox = this.checkBox;\n        this.$set(this.orderList, index, item);\n      });\n    },\n    showUserInfo(row) {\n      this.$refs.userDetails.modals = true;\n\t\t\tthis.$refs.userDetails.activeName = 'info';\n      this.$refs.userDetails.getDetails(row.uid);\n    },\n    // 操作\n    changeMenu(row, name) {\n      this.orderId = row.id;\n      switch (name) {\n        case \"2\":\n\t\t\t\t  this.rowActive = row;\n          this.getData(row.id);\n          break;\n        case \"3\":\n          this.$refs.record.modals = true;\n          this.$refs.record.getList(row.id);\n          break;\n        case \"4\":\n\t\t\t\t\tthis.$refs.remarks.formValidate.remark = row.remark;\n\t\t\t\t\tthis.$refs.remarks.modals = true;\n          break;\n        case \"5\":\n          this.getRefundData(row.id);\n          break;\n        case \"6\":\n          this.getRefundIntegral(row.id);\n          break;\n        case \"7\":\n          this.getNoRefundData(row.id);\n          break;\n        case \"8\":\n          this.delfromData = {\n            title: \"修改确认收货\",\n            url: `marketing/integral/order/take/${row.id}`,\n            method: \"put\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.getList();\n\t\t\t\t\t\t\tthis.$emit(\"changeGetTabs\");\n\t\t\t\t\t\t\tthis.getData(row.id,1);\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          // this.modalTitleSs = '修改确认收货';\n          break;\n        case \"10\":\n          this.delfromData = {\n            title: \"立即打印订单\",\n            info: \"您确认打印此订单吗?\",\n            url: `marketing/integral/order/print/${row.id}`,\n            method: \"get\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.$emit(\"changeGetTabs\");\n              this.getList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          break;\n        case \"11\":\n          this.delfromData = {\n            title: \"立即打印电子面单\",\n            info: \"您确认打印此电子面单吗?\",\n            url: `/order/order_dump/${row.id}`,\n            method: \"get\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.getList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          break;\n        default:\n          this.delfromData = {\n            title: \"删除订单\",\n            url: `marketing/integral/order/del/${row.id}`,\n            method: \"DELETE\",\n            ids: \"\",\n          };\n          // this.modalTitleSs = '删除订单';\n          this.delOrder(row, this.delfromData);\n      }\n    },\n    // 立即支付 /确认收货//删除单条订单\n    submitModel() {\n      this.getList();\n    },\n    pageChange(index) {\n      this.page.pageNum = index;\n      this.getList();\n    },\n    limitChange(limit) {\n      this.page.pageSize = limit;\n      this.getList();\n    },\n    // 订单列表\n    getList(res) {\n      this.page.pageNum = res === 1 ? 1 : this.page.pageNum;\n      this.loading = true;\n      integralOrderList({\n        page: this.page.pageNum,\n        limit: this.page.pageSize,\n        status: this.orderStatus,\n        pay_type: this.orderPayType,\n        data: this.orderTime,\n        real_name: this.realName,\n        field_key: this.fieldKey,\n        type: this.orderType === 0 ? \"\" : this.orderType,\n        product_id: this.$route.query.product_id,\n      })\n        .then(async (res) => {\n          let data = res.data;\n\t\t\t\t\tdata.data.forEach((item)=>{\n\t\t\t\t\t  if(item.id == this.orderId){\n\t\t\t\t\t    this.rowActive = item;\n\t\t\t\t\t  }\n\t\t\t\t\t});\n          // this.orderList = data.data;\n          this.orderList = data.data.map((item) => {\n            // item.checkBox = false;\n            if (this.isAll === 1) {\n              item.checkBox = true;\n            } else {\n              item.checkBox = false;\n            }\n            return item;\n          });\n          this.orderCards = data.stat;\n          this.page.total = data.count;\n          this.$emit(\"on-changeCards\", data.stat);\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 全选\n    onSelectTab(selection) {\n      this.formSelection = selection;\n      let isDel = selection.some((item) => {\n        return item.is_del === 1;\n      });\n      this.getIsDel(isDel);\n      this.getisDelIdListl(selection);\n    },\n    // 编辑\n    edit(row) {\n      this.getOrderData(row.id);\n    },\n    // 删除单条订单\n    delOrder(row, data) {\n      if (row.is_del === 1) {\n        this.$modalSure(data)\n          .then((res) => {\n            this.$Message.success(res.msg);\n            this.getList();\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n      } else {\n        const title = \"错误！\";\n        const content =\n          \"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>\";\n        this.$Modal.error({\n          title: title,\n          content: content,\n        });\n      }\n    },\n    // 获取编辑表单数据\n    getOrderData(id) {\n      getOrdeDatas(id)\n        .then(async (res) => {\n          if (res.data.status === false) {\n            return this.$authLapse(res.data);\n          }\n          this.$authLapse(res.data);\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 获取详情表单数据\n    getData(id,type) {\n      getIntegralOrderDataInfo(id)\n        .then(async (res) => {\n\t\t\t\t\tif(!type){\n\t\t\t\t\t\tthis.$refs.detailss.modals = true;\n\t\t\t\t\t}\n\t\t\t\t\tthis.$refs.detailss.activeName = 'detail';\n          this.orderDatalist = res.data;\n          if (this.orderDatalist.orderInfo.refund_reason_wap_img) {\n            try {\n              this.orderDatalist.orderInfo.refund_reason_wap_img = JSON.parse(\n                this.orderDatalist.orderInfo.refund_reason_wap_img\n              );\n            } catch (e) {\n              this.orderDatalist.orderInfo.refund_reason_wap_img = [];\n            }\n          }\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 修改成功\n    submitFail(type) {\n      this.getList();\n\t\t\tthis.getData(this.orderId,1);\n\t\t\tif(type){\n\t\t\t\tthis.$emit(\"changeGetTabs\");\n\t\t\t}\n    },\n    // 发送货\n    sendOrder(row) {\n      this.$refs.send.modals = true;\n      this.$refs.send.getList();\n      this.$refs.send.getDeliveryList();\n      // this.$refs.send.getSheetInfo();\n      this.orderId = row.id;\n    },\n    // 配送信息表单数据\n    delivery(row) {\n      getIntegralOrderDistribution(row.id)\n        .then(async (res) => {\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n\t\t\t\t\tthis.getData(this.orderId,1);\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    change(status) {},\n    // 数据导出；\n    exportData: function () {\n      this.$refs.table.exportCsv({\n        filename: \"商品列表\",\n      });\n    },\n    onSelectCancel(selection, row) {},\n  },\n};\n", null]}