{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_banner.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_banner.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    export default {\n        name: 'c_banner',\n        componentsName: 'home_banner',\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ],\n\t\t\t\toneStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'docConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'docPosition'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttwoStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'dotColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'dotBgColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleImg'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'filletImg'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tfourStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'imgConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneCurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoCurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tthreeCurrencyStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsetUp:0,\n\t\t\t\ttype:0,\n\t\t\t\ttype2:0\n            }\n        },\n        watch: {\n            num (nVal) {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n\t\t\t\t\tthis.setUp = nVal;\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n                        let tempArr = [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t\t\t    configNme: 'styleConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleContent'\n\t\t\t\t\t\t\t},\n                            {\n                                components: toolCom.c_swipers_list,\n                                configNme: 'swiperConfig'\n                            }\n                        ]\n                        this.rCom = arr.concat(tempArr)\n                    } else {\n\t\t\t\t\t\tif(this.type2 == 2){\n\t\t\t\t\t\t\tif(this.type){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(this.type2 == 1){\n\t\t\t\t\t\t\tif(this.type){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n                    }\n                },\n                deep: true\n            },\n\t\t\t'configObj.styleConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tif(nVal == 2){\n\t\t\t\t\t\t\tif(this.type){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(nVal == 1){\n\t\t\t\t\t\t\tif(this.type){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(this.type){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'configObj.toneConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]]\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tif(this.type2 == 2){\n\t\t\t\t\t\t\tif(nVal){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.fourStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else if(this.type2 == 1){\n\t\t\t\t\t\t\tif(nVal){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tif(nVal){\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle,...this.oneCurrencyStyle,...this.twoCurrencyStyle,...this.threeCurrencyStyle]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n            handleSubmit (name) {\n                let obj = {}\n                obj.activeIndex = this.activeIndex\n                obj.data = this.configObj\n                this.add(obj);\n            },\n            ...mapMutations({\n                add: 'admin/mobildConfig/UPDATEARR'\n            })\n        }\n    }\n", null]}