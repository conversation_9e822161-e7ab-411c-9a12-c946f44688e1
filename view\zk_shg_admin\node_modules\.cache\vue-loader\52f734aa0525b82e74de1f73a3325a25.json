{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCoupon\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCoupon\\index.vue", "mtime": 1650783676000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from 'vuex';\nimport { couponListApi, couponCreateApi, couponEditeApi, couponSendApi } from '@/api/marketing';\nimport editFrom from '@/components/from/from';\nimport { formatDate } from '@/utils/validate';\nexport default {\n    name: 'storeCoupon',\n    filters: {\n        formatDate (time) {\n            if (time !== 0) {\n                let date = new Date(time * 1000);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n    },\n    components: { editFrom },\n    data () {\n        return {\n            grid: {\n                xl: 7,\n                lg: 7,\n                md: 12,\n                sm: 24,\n                xs: 24\n            },\n            loading: false,\n            columns1: [\n                {\n                    title: 'ID',\n                    key: 'id',\n                    width: 80\n                },\n                {\n                    title: '优惠券名称',\n                    key: 'title',\n                    minWidth: 150\n                },\n                {\n                    title: '优惠券类型',\n                    key: 'type',\n                    minWidth: 80\n                },\n                {\n                    title: '面值',\n                    slot: 'coupon_price',\n                    minWidth: 100\n                },\n                {\n                    title: '最低消费额',\n                    key: 'use_min_price',\n                    minWidth: 100\n                },\n                {\n                    title: '有效期限(天)',\n                    key: 'coupon_time',\n                    minWidth: 120\n                },\n                {\n                    title: '排序',\n                    key: 'sort',\n                    minWidth: 80\n                },\n                {\n                    title: '是否有效',\n                    slot: 'status',\n                    minWidth: 90\n                },\n                {\n                    title: '添加时间',\n                    slot: 'add_time',\n                    minWidth: 150\n                },\n                {\n                    title: '操作',\n                    slot: 'action',\n                    fixed: 'right',\n                    minWidth: 170\n                }\n            ],\n            tableFrom: {\n                status: '',\n                title: '',\n                page: 1,\n                limit: 15\n            },\n            tableList: [],\n            total: 0,\n            FromData: null\n        }\n    },\n    created () {\n        this.getList();\n    },\n    computed: {\n        ...mapState('admin/layout', [\n            'isMobile'\n        ]),\n        labelWidth () {\n            return this.isMobile ? undefined : 90;\n        },\n        labelPosition () {\n            return this.isMobile ? 'top' : 'left';\n        }\n    },\n    methods: {\n        // 失效\n        couponInvalid (row, tit, num) {\n            let delfromData = {\n                title: tit,\n                num: num,\n                url: `marketing/coupon/status/${row.id}`,\n                method: 'PUT',\n                ids: ''\n            };\n            this.$modalSure(delfromData).then((res) => {\n                this.$Message.success(res.msg);\n                this.getList();\n            }).catch(res => {\n                this.$Message.error(res.msg);\n            });\n        },\n        // 发布\n        couponSend (row) {\n            this.$modalForm(couponSendApi(row.id)).then(() => this.getList());\n        },\n        // 删除\n        couponDel (row, tit, num) {\n            let delfromData = {\n                title: tit,\n                num: num,\n                url: `marketing/coupon/del/${row.id}`,\n                method: 'DELETE',\n                ids: ''\n            };\n            this.$modalSure(delfromData).then((res) => {\n                this.$Message.success(res.msg);\n                this.tableList.splice(num, 1)\n            }).catch(res => {\n                this.$Message.error(res.msg);\n            });\n        },\n        // 列表\n        getList () {\n            this.loading = true;\n            this.tableFrom.status = this.tableFrom.status || '';\n            couponListApi(this.tableFrom).then(async res => {\n                let data = res.data\n                this.tableList = data.list;\n                this.total = res.data.count;\n                this.loading = false;\n            }).catch(res => {\n                this.loading = false;\n                this.$Message.error(res.msg);\n            });\n        },\n        pageChange (index) {\n            this.tableFrom.page = index;\n            this.getList();\n        },\n        changeType (data) {\n            this.type = data;\n        },\n        // 添加\n        add () {\n            // this.$modalForm(couponCreateApi()).then(() => this.getList());\n            this.addType(0);\n        },\n        addType (type) {\n            couponCreateApi(type).then(async res => {\n                if (res.data.status === false) {\n                    return this.$authLapse(res.data);\n                }\n                this.FromData = res.data;\n                this.$refs.edits.modals = true;\n            }).catch(res => {\n                this.$Message.error(res.msg);\n            })\n        },\n        // 编辑\n        edit (row) {\n            this.$modalForm(couponEditeApi(row.id)).then(() => this.getList());\n        },\n        // 表格搜索\n        userSearchs () {\n            this.tableFrom.page = 1;\n            this.getList();\n        },\n        // 修改成功\n        submitFail () {\n            this.getList();\n        }\n    }\n}\n", null]}