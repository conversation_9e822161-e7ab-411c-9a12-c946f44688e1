{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_new_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_new_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nimport { categoryList as _categoryList } from '@/api/diy';\nimport { cmsListApi } from '@/api/cms';\nexport default {\n  name: 'c_home_bargain',\n  componentsName: 'home_bargain',\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'filletImg'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'nameConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneConfig'\n      }],\n      twoStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'likeSuccessColor'\n      }],\n      threeStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'nameColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'timeColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'browseColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'likeColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'statisticColor'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bgColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'mbConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      setUp: 0,\n      type: 0\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n      this.categoryList();\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          var tempArr = [{\n            components: toolCom.c_title,\n            configNme: 'titleLeft'\n          }, {\n            components: toolCom.c_radio,\n            configNme: 'styleConfig'\n          }, {\n            components: toolCom.c_title,\n            configNme: 'titleArticle'\n          }, {\n            components: toolCom.c_select,\n            configNme: 'selectConfig'\n          }, {\n            components: toolCom.c_input_number,\n            configNme: 'numConfig'\n          }, {\n            components: toolCom.c_title,\n            configNme: 'titleList'\n          }, {\n            components: toolCom.c_checkbox,\n            configNme: 'checkboxList'\n          }];\n          this.rCom = arr.concat(tempArr);\n        } else {\n          if (this.type) {\n            this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle));\n          } else {\n            this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.threeStyle));\n          }\n        }\n      },\n      deep: true\n    },\n    'configObj.toneConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          if (nVal) {\n            this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle));\n          } else {\n            this.rCom = [].concat(arr, _toConsumableArray(this.oneStyle), _toConsumableArray(this.threeStyle));\n          }\n        }\n      }\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n\n      _this.categoryList();\n    });\n  },\n  methods: _objectSpread({\n    categoryList: function categoryList() {\n      var _this2 = this;\n\n      _categoryList().then(function (res) {\n        var data = [];\n        res.data.map(function (item) {\n          data.push({\n            title: item.title,\n            activeValue: item.id.toString()\n          });\n        });\n        _this2.configObj.selectConfig.list = data; // this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: this.pageData })\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    // 获取组件参数\n    getConfig: function getConfig(data) {\n      var _this3 = this;\n\n      if (data.name == 'radio') {\n        return;\n      }\n\n      var val = {\n        pid: parseInt(this.configObj.selectConfig.activeValue),\n        page: 1,\n        limit: parseInt(this.configObj.numConfig.val)\n      };\n      cmsListApi(val).then(function (res) {\n        _this3.configObj.selectList.list = res.data.list;\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    handleSubmit: function handleSubmit(name) {\n      var obj = {};\n      obj.activeIndex = this.activeIndex;\n      obj.data = this.configObj;\n      this.add(obj);\n    }\n  }, mapMutations({\n    add: 'admin/mobildConfig/UPDATEARR'\n  }))\n};", null]}