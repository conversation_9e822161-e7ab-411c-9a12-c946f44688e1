{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue", "mtime": 1709630624583}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport { supplierFinanceInfo, supplierFlowingWaterMark, getSupplierList as _getSupplierList, exportTableList } from '@/api/supplier';\nimport exportExcel from '@/utils/newToExcel.js';\nimport timeOptions from '@/utils/timeOptions';\nexport default {\n  name: 'orderStatistics',\n  data: function data() {\n    return {\n      cardLists: [],\n      extractStatistics: {},\n      // 旧\n      modalmark: false,\n      remarks: {\n        mark: ''\n      },\n      supplierList: [],\n      total: 0,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns: [{\n        title: '交易单号',\n        key: 'order_id',\n        minWidth: 180\n      }, {\n        title: '关联订单',\n        key: 'link_id',\n        minWidth: 180\n      }, {\n        title: '交易时间',\n        key: 'trade_time',\n        minWidth: 150\n      }, {\n        title: '交易金额',\n        slot: 'number',\n        minWidth: 80\n      }, {\n        title: '交易人',\n        slot: 'user_nickname',\n        ellipsis: true,\n        minWidth: 80\n      }, {\n        title: '供应商',\n        key: 'supplier_name',\n        minWidth: 80\n      }, {\n        title: '交易类型',\n        key: 'type_name',\n        minWidth: 80\n      }, {\n        title: '支付方式',\n        key: 'pay_type_name',\n        minWidth: 80\n      }, {\n        title: '备注',\n        key: 'remark',\n        minWidth: 120\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        minWidth: 80,\n        align: 'center'\n      }],\n      orderList: [],\n      formValidate: {\n        supplier_id: 2,\n        keyword: '',\n        data: '',\n        page: 1,\n        limit: 20\n      },\n      timeVal: [],\n      options: timeOptions\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'left';\n    }\n  }),\n  mounted: function mounted() {\n    this.getList();\n    this.getSupplierList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n\n      this.loading = true;\n      supplierFinanceInfo(this.formValidate).then(function (res) {\n        _this.orderList = res.data.list;\n        _this.total = res.data.count;\n        _this.loading = false;\n      });\n    },\n    getSupplierList: function getSupplierList() {\n      var _this2 = this;\n\n      _getSupplierList().then(function (res) {\n        _this2.supplierList = res.data;\n      });\n    },\n    search: function search() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    reset: function reset() {\n      this.formValidate = {\n        supplier_id: '',\n        keyword: '',\n        data: '',\n        page: 1,\n        limit: 20\n      };\n      this.timeVal = [];\n      this.getList();\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.formValidate.page = 1;\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join('-') : '';\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    //分页\n    pageChange: function pageChange(status) {\n      this.formValidate.page = status;\n      this.getList();\n    },\n    remark: function remark(e) {\n      this.remarkId = e.id;\n      this.modalmark = true;\n      this.remarks.mark = e.remark;\n    },\n    //备注的提交\n    putRemark: function putRemark() {\n      var _this3 = this;\n\n      this.modalmark = false;\n      supplierFlowingWaterMark(this.remarkId, this.remarks).then(function (res) {\n        _this3.$Message.success(res.msg);\n\n        _this3.remarks = {\n          mark: ''\n        };\n        _this3.modalmark = false;\n\n        _this3.getList();\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n\n        _this3.modalmark = false;\n      });\n    },\n    // 取消备注按钮\n    cancel: function cancel() {\n      this.remarks = {\n        mark: ''\n      };\n      this.modalmark = false;\n    },\n    //导出\n    exports: function () {\n      var _exports = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        var th, filekey, data, fileName, supplier_id, keyword, date, lebData;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                th = [], filekey = [], data = [], fileName = '';\n                supplier_id = this.formValidate.supplier_id, keyword = this.formValidate.keyword, date = this.formValidate.data;\n                _context.next = 4;\n                return this.getExcelData({\n                  supplier_id: supplier_id,\n                  keyword: keyword,\n                  date: date\n                });\n\n              case 4:\n                lebData = _context.sent;\n                if (!fileName) fileName = lebData.filename;\n                filekey = lebData.filekey;\n                if (!th.length) th = lebData.header; //表头\n\n                data = data.concat(lebData.export);\n                exportExcel(th, filekey, fileName, data);\n\n              case 10:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function exports() {\n        return _exports.apply(this, arguments);\n      }\n\n      return exports;\n    }(),\n    getExcelData: function getExcelData(data) {\n      return new Promise(function (resolve) {\n        exportTableList(data).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    }\n  }\n};", null]}