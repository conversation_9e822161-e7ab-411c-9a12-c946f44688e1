{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue", "mtime": 1718158266000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState, mapMutations } from \"vuex\";\nimport { memberRight, memberRightSave, saveMemberContent } from \"@/api/user\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport WangEditor from \"@/components/wangEditor/index.vue\";\n\nexport default {\n    components: { uploadPictures, WangEditor },\n    data() {\n        return {\n            thead: [\n                {\n                    title: \"权益名称\",\n                    key: \"title\"\n                },\n                {\n                    title: \"展示名称\",\n                    key: \"show_title\"\n                },\n                {\n                    title: \"权益图标（80x80）\",\n                    slot: \"image\"\n                },\n                {\n                    title: \"权益简介\",\n                    key: \"explain\"\n                },\n                {\n                    title: \"权益状态\",\n                    slot: \"status\"\n                },\n                {\n                    title: \"操作\",\n                    slot: \"action\"\n                }\n            ],\n            tbody: [],\n            loading: false,\n            total: 0,\n            page: 1,\n            limit: 30,\n            modal1: false,\n            form: {\n                id: \"\",\n                right_type: \"\",\n                title: \"\",\n                show_title: \"\",\n                image: \"\",\n                explain: \"\",\n                number: 1,\n                status: 1\n            },\n            rules: {\n                title: [{ required: true, message: \"请输入权益名称\", trigger: \"blur\" }],\n                show_title: [{ required: true, message: \"请输入展示名称\", trigger: \"blur\" }],\n                image: [{ required: true, message: \"请上传权益图标\" }],\n                explain: [{ required: true, message: \"请输入权益简介\", trigger: \"blur\" }],\n                number: [{ required: true, type: \"integer\", message: \"请输入正整数\" }]\n            },\n            modal2: false,\n            gridPic: {\n                xl: 6,\n                lg: 8,\n                md: 12,\n                sm: 12,\n                xs: 12\n            },\n            gridBtn: {\n                xl: 4,\n                lg: 8,\n                md: 8,\n                sm: 8,\n                xs: 8\n            },\n            modal3: false,\n            content: '',\n\t\t\tagreementCon:''\n        };\n    },\n    computed: {\n        ...mapState(\"media\", [\"isMobile\"])\n    },\n    created() {\n        this.getRightList();\n    },\n    methods: {\n        getRightList() {\n            this.loading = true;\n            memberRight()\n                .then(res => {\n                    const { count, list } = res.data;\n                    this.loading = false;\n                    this.total = count;\n                    this.tbody = list;\n                })\n                .catch(err => {\n                    this.loading = false;\n                    this.$Message.error(err);\n                });\n        },\n        // 改变状态\n        statusChange(row) {\n            this.form.id = row.id;\n            this.form.right_type = row.right_type;\n            this.form.title = row.title;\n            this.form.show_title = row.show_title;\n            this.form.image = row.image;\n            this.form.explain = row.explain;\n            this.form.number = row.number;\n            this.form.status = row.status;\n            this.rightSave();\n        },\n        // 编辑\n        edit(row) {\n            this.modal1 = true;\n            this.form.id = row.id;\n            this.form.status = row.status;\n            this.form.right_type = row.right_type;\n            this.form.title = row.title;\n            this.form.show_title = row.show_title;\n            this.form.image = row.image;\n            this.form.explain = row.explain;\n            this.form.number = row.number;\n        },\n        // 分页\n        pageChange(index) {\n            this.page = index;\n            this.getRightList();\n        },\n        // 修改\n        rightSave() {\n            memberRightSave(this.form)\n                .then(res => {\n                    this.modal1 = false;\n                    this.getRightList();\n                    this.$Message.success(res.msg);\n                })\n                .catch(err => {\n                    this.$Message.error(err.msg);\n                });\n        },\n        formSubmit(name) {\n            this.$refs[name].validate(valid => {\n                if (valid) {\n                    this.rightSave();\n                }\n            });\n        },\n        callImage() {\n            this.modal2 = true;\n        },\n        getPic(image) {\n            this.form.image = image.att_dir;\n            this.modal2 = false;\n        },\n        editRight(row) {\n          this.modal3 = true;\n          this.form.id = row.id;\n          this.content = row.content;\n\t\t  this.agreementCon = row.content;\n        },\n        getEditorContent(content) {\n\t\t\tthis.agreementCon = content;\n        },\n        saveMemberContent() {\n          saveMemberContent(this.form.id, { content: this.agreementCon }).then(res => {\n            this.modal3 = false;\n            this.$Message.success(res.msg);\n            this.getRightList();\n          }).catch(err => {\n              this.$Message.error(err.msg);\n          });\n        }\n    }\n};\n", null]}