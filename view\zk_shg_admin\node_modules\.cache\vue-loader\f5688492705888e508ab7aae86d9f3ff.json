{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue?vue&type=template&id=07a10b5d&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue", "mtime": 1677832908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<Modal\n  scrollable\n  v-model=\"modal\"\n  @on-cancel=\"onCancel\"\n  :title=\"title\"\n  class-name=\"vertical-center-modal\"\n  width=\"950\"\n>\n  <Form\n    ref=\"formDynamic\"\n    :model=\"formDynamic\"\n    :rules=\"rules\"\n    class=\"attrFrom\"\n    :label-width=\"labelWidth\"\n    :label-position=\"labelPosition\"\n    @submit.native.prevent\n  >\n    <Row :gutter=\"24\">\n      <Col span=\"24\">\n        <Col span=\"8\" class=\"mb15\">\n          <FormItem label=\"分类名称：\" prop=\"rule_name\">\n            <Input\n              placeholder=\"请输入分类名称\"\n              :maxlength=\"20\"\n              v-model=\"formDynamic.rule_name\"\n            />\n          </FormItem>\n        </Col>\n      </Col>\n      <Col\n        span=\"23\"\n        class=\"noForm\"\n        v-for=\"(item, index) in formDynamic.spec\"\n        :key=\"index\"\n      >\n        <FormItem>\n          <div class=\"acea-row row-middle\"><span class=\"mr5\">{{item.value}}</span><Icon type=\"ios-close-circle\" @click=\"handleRemove(index)\"/></div>\n          <div class=\"rulesBox\">\n              <Tag type=\"dot\" class=\"\" closable color=\"primary\" v-for=\"(j, indexn) in item.detail\" :key=\"indexn\" :name=\"j\"  @on-close=\"handleRemove2(item.detail,indexn)\">{{j}}</Tag>\n              <Input maxlength=\"30\" show-word-limit search enter-button=\"添加\" placeholder=\"请输入属性名称\" \n              v-model=\"item.detail.attrsVal\" @on-search=\"createAttr(item.detail.attrsVal,index)\" class=\"width20\"/>\n          </div>\n        </FormItem>\n      </Col>\n      <Col span=\"24\" v-if=\"isBtn\" class=\"mt10\">\n        <Col span=\"8\" class=\"mt10 mr15\">\n          <FormItem label=\"规格名称：\">\n            <Input placeholder=\"请输入规格名称\" \n            maxlength=\"30\" \n            show-word-limit\n            v-model=\"attrsName\" />\n          </FormItem>\n        </Col>\n        <Col span=\"8\" class=\"mt10 mr20\">\n          <FormItem label=\"规格值：\">\n            <Input maxlength=\"30\" show-word-limit\n            v-model=\"attrsVal\" placeholder=\"请输入规格值\" />\n          </FormItem>\n        </Col>\n        <Col span=\"8\" class=\"mr20\">\n        <div class=\"sub\">\n          <Button class=\"mr20\" type=\"primary\" @click=\"createAttrName\">确定</Button>\n          <Button @click=\"offAttrName\">取消</Button>\n        </div>\n        </Col>\n      </Col>\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\n    </Row>\n    <Button\n      type=\"primary\"\n      @click=\"addBtn\"\n      v-if=\"!isBtn\"\n      class=\"ml95 mt10\"\n      >添加新规格</Button\n    >\n  </Form>\n  <div slot=\"footer\">\n    <Button @click=\"cancle\">取消</Button>\n    <Button\n      type=\"primary\"\n      :loading=\"modal_loading\"\n      @click=\"handleSubmit('formDynamic')\"\n      >确定</Button\n    >\n  </div>\n</Modal>\n", null]}