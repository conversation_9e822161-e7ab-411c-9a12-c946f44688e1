{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue?vue&type=template&id=0bdf71a4&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('div',{staticClass:\"seckill-box\",style:({\n\t\tbackground:_vm.bottomBgColor,\n\t\tmarginTop:_vm.mTop+'px',\n\t\tpaddingTop:_vm.topConfig+'px',\n\t\tpaddingBottom:_vm.bottomConfig+'px',\n\t\tpaddingLeft:_vm.prConfig+'px',\n\t\tpaddingRight:_vm.prConfig+'px'\n\t\t})},[_c('div',{staticClass:\"hd\",style:((_vm.styleConfig?\n\t\t'backgroundImage:url(' + _vm.imgBgUrl + ')':\n\t    (\"background:linear-gradient(90deg,\" + _vm.headerBgColorLeft + \" 0%,\" + _vm.headerBgColorRight + \" 100%)\"))+\n\t\t';borderRadius:'+_vm.bgRadius)},[_c('div',{staticClass:\"left acea-row row-middle\"},[(_vm.titleConfig)?_c('div',{staticClass:\"text\",style:((_vm.titleTabVal==2?'fontStyle:':'fontWeight:') + _vm.titleText+';color:'+_vm.titleColor+';fontSize:'+_vm.titleNumber+'px;')},[_vm._v(_vm._s(_vm.titleTxtConfig))]):_c('img',{attrs:{\"src\":_vm.styleConfig?_vm.imgUrl:_vm.imgColorUrl,\"alt\":\"\"}}),_c('div',{staticClass:\"line\",style:({\n\t\t\t  background:_vm.dividerColor\n\t\t  })}),_c('div',{staticClass:\"tips\",style:({\n\t\t\t  color: _vm.styleConfig?_vm.tipsColor:_vm.tipsColor2\n\t\t  })},[_vm._v(_vm._s(_vm.tipTxt))])]),_c('div',{staticClass:\"right\",style:({\n\t\t\tcolor:_vm.styleConfig?_vm.headerBntColor:_vm.headerBntColor2,\n\t\t\tfontSize:_vm.bntNumber+'px'\n\t\t})},[_vm._v(\"\\n          \"+_vm._s(_vm.rightBntTxt)+\"\\n\\t\\t  \"),_c('span',{staticClass:\"iconfont iconjinru\",style:({\n\t\t\t fontSize:_vm.bntNumber+'px' \n\t\t  })})])]),_c('div',{staticClass:\"list-wrapper\",class:_vm.goodStyleConfig == 0?'on':(_vm.goodStyleConfig == 1 || _vm.goodStyleConfig == 2)?'on2':_vm.goodStyleConfig == 3?'on3':'',style:({\n\t\t  background: _vm.bgColor,\n\t\t  borderRadius: _vm.bgRadius2\n\t  })},[_vm._l((_vm.numberConfig),function(item,index){return (_vm.goodStyleConfig == 0)?_c('div',{key:index,staticClass:\"itemOne acea-row\"},[_c('div',{staticClass:\"empty-box\",style:({\n\t\t\t\tborderRadius: _vm.imgRadius\n\t\t\t})},[_c('img',{attrs:{\"src\":require(\"../../assets/images/shan.png\")}})]),_c('div',{staticClass:\"text\"},[_c('div',{staticClass:\"top\"},[(_vm.checkboxInfo.indexOf(0) != -1)?_c('div',{staticClass:\"name line2\",style:({\n\t\t\t\t\t\tfontWeight: _vm.goodsName,\n\t\t\t\t\t\tcolor: _vm.goodsNameColor\n\t\t\t\t\t})},[_vm._v(\"熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...\")]):_vm._e(),(_vm.checkboxInfo.indexOf(1) != -1)?_c('div',{staticClass:\"num\",style:({\n\t\t\t\t\t\tcolor:_vm.toneConfig?_vm.joinNumColor:_vm.colorStyle.theme\n\t\t\t\t\t})},[_c('span',{staticClass:\"iconfont iconic_fire\"}),_vm._v(\"1223人正在参与\")]):_vm._e()]),_c('div',{staticClass:\"bottom\",class:(_vm.checkboxInfo.indexOf(2) != -1 && _vm.checkboxInfo.indexOf(3) != -1)?'':'acea-row row-bottom'},[(_vm.checkboxInfo.indexOf(2) != -1)?_c('div',{staticClass:\"price\",style:({\n\t\t\t\t\t\tcolor:_vm.toneConfig?_vm.bargainPriceColor:_vm.colorStyle.theme\n\t\t\t\t\t})},[_c('span',{staticClass:\"label\"},[_vm._v(\"¥\")]),_c('span',{staticClass:\"num\"},[_vm._v(\"3200.00\")])]):_vm._e(),(_vm.checkboxInfo.indexOf(3) != -1)?_c('div',{staticClass:\"yprice\",style:({\n\t\t\t\t\t\tcolor:_vm.goodsPriceColor\n\t\t\t\t\t})},[_vm._v(\"¥1233423.00\")]):_vm._e()]),(!_vm.bargainConfig)?_c('div',{staticClass:\"bnt\",style:({\n\t\t\t\t\tcolor:_vm.toneConfig?_vm.goodsBntTxtColor:'#fff',\n\t\t\t\t\tbackground: _vm.toneConfig?(\"linear-gradient(90deg,\" + _vm.goodsBntColorRight + \" 0%,\" + _vm.goodsBntColorLeft + \" 100%)\"):_vm.themeColor\n\t\t\t\t})},[_vm._v(\"参与砍价\")]):_vm._e()])]):_vm._e()}),_vm._l((_vm.numberConfig),function(item2,index2){return (_vm.goodStyleConfig == 1)?_c('div',{key:index2,staticClass:\"itemTwo\"},[_c('div',{staticClass:\"empty-box\",style:({\n\t\t\t\tborderRadius: _vm.imgRadius\n\t\t\t})},[_c('img',{attrs:{\"src\":require(\"../../assets/images/shan.png\")}})]),_c('div',{class:((_vm.checkboxInfo.indexOf(0) != -1 && _vm.checkboxInfo.length == 1 && !_vm.bargainConfig) || (_vm.checkboxInfo.indexOf(0) != -1 && _vm.checkboxInfo.indexOf(1) != -1 && _vm.checkboxInfo.length == 2) && !_vm.bargainConfig)?'item':(!_vm.checkboxInfo.length || (_vm.checkboxInfo.indexOf(1) != -1 && _vm.checkboxInfo.length==1)) && !_vm.bargainConfig?'item2':''},[(_vm.checkboxInfo.indexOf(0) != -1)?_c('div',{staticClass:\"title line1\",style:({\n\t\t\t\t\tfontWeight: _vm.goodsName,\n\t\t\t\t\tcolor: _vm.goodsNameColor\n\t\t\t\t})},[_vm._v(\"熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...\")]):_vm._e(),(_vm.checkboxInfo.indexOf(2) != -1)?_c('div',{staticClass:\"price\",class:_vm.checkboxInfo.indexOf(3) == -1 && !_vm.bargainConfig?'on':'',style:({\n\t\t\t\t\tcolor:_vm.toneConfig?_vm.bargainPriceColor:_vm.colorStyle.theme\n\t\t\t\t})},[_vm._v(\"¥\"),_c('span',{staticClass:\"num\"},[_vm._v(\"3200.00\")])]):_vm._e(),(_vm.checkboxInfo.indexOf(3) != -1)?_c('div',{staticClass:\"yprice\",class:_vm.checkboxInfo.indexOf(2) == -1 && !_vm.bargainConfig?'on':'',style:({\n\t\t\t\t\tcolor:_vm.goodsPriceColor\n\t\t\t\t})},[_vm._v(\"¥3699.00\")]):_vm._e(),(!_vm.bargainConfig)?_c('div',{staticClass:\"bnt\",class:_vm.checkboxInfo.indexOf(2) == -1 && !_vm.bargainConfig?'on':'',style:({\n\t\t\t\t\tcolor:_vm.toneConfig?_vm.goodsBntTxtColor:'#fff',\n\t\t\t\t\tbackground: _vm.toneConfig?(\"linear-gradient(90deg,\" + _vm.goodsBntColorRight + \" 0%,\" + _vm.goodsBntColorLeft + \" 100%)\"):_vm.themeColor\n\t\t\t\t})},[_vm._v(\"去砍价\")]):_vm._e()])]):_vm._e()}),_vm._l((_vm.numberConfig),function(item,index){return (_vm.goodStyleConfig == 2)?_c('div',{key:index,staticClass:\"list-item\"},[_c('div',{staticClass:\"img-box\"},[_c('div',{staticClass:\"empty-box\",style:({\n\t\t\t\tborderRadius: _vm.imgRadius\n\t\t\t})},[_c('img',{attrs:{\"src\":require(\"../../assets/images/shan.png\")}})])]),(_vm.checkboxInfo.indexOf(0) != -1)?_c('div',{staticClass:\"title line1\",style:({\n\t\t\t  fontWeight: _vm.goodsName,\n\t\t\t  color: _vm.goodsNameColor\n\t\t  })},[_vm._v(\"熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...\")]):_vm._e(),(_vm.checkboxInfo.indexOf(2) != -1)?_c('div',{staticClass:\"price\",style:({\n\t\t\t  color:_vm.toneConfig?_vm.bargainPriceColor:_vm.colorStyle.theme\n\t\t  })},[_vm._v(\"低至\"),_c('span',{staticClass:\"lable\"},[_vm._v(\"¥\")]),_c('span',{staticClass:\"num\"},[_vm._v(\"350.00\")])]):_vm._e(),(_vm.checkboxInfo.indexOf(3) != -1)?_c('div',{staticClass:\"yprice\",style:({\n\t\t\t  color:_vm.goodsPriceColor\n\t\t  })},[_vm._v(\"¥3699.00\")]):_vm._e()]):_vm._e()}),_vm._l((_vm.numberConfig),function(item2,index2){return (_vm.goodStyleConfig == 3)?_c('div',{key:index2,staticClass:\"itemThree\"},[_c('div',{staticClass:\"empty-box\",style:({\n\t\t\t\tborderRadius: _vm.imgRadius\n\t\t\t})},[_c('img',{attrs:{\"src\":require(\"../../assets/images/shan.png\")}})]),_c('div',[(_vm.checkboxInfo.indexOf(0) != -1)?_c('div',{staticClass:\"title line1\",style:({\n\t\t\t\t\tfontWeight: _vm.goodsName,\n\t\t\t\t\tcolor: _vm.goodsNameColor\n\t\t\t\t})},[_vm._v(\"熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...\")]):_vm._e(),(_vm.checkboxInfo.indexOf(1) != -1)?_c('div',{staticClass:\"joinNum\",style:({\n\t\t\t\t\tcolor:_vm.toneConfig?_vm.joinNumColor2:'#fff',\n\t\t\t\t\tbackground:_vm.toneConfig?(\"linear-gradient(90deg,\" + _vm.joinBgColorLeft + \" 0%,\" + _vm.joinBgColorRight + \" 100%)\"):_vm.themeColor2\n\t\t\t\t})},[_vm._v(\"175人已砍成功\")]):_vm._e(),(_vm.checkboxInfo.indexOf(2) != -1)?_c('div',{staticClass:\"price\",class:_vm.checkboxInfo.indexOf(3) == -1 && !_vm.bargainConfig?'on':'',style:({\n\t\t\t\t\tcolor:_vm.toneConfig?_vm.bargainPriceColor:_vm.colorStyle.theme\n\t\t\t\t})},[_vm._v(\"¥\"),_c('span',{staticClass:\"num\"},[_vm._v(\"3200.00\")])]):_vm._e()])]):_vm._e()})],2)])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}