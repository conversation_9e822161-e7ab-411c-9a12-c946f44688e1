{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab.vue?vue&type=template&id=16687342&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab.vue", "mtime": 1659407670000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div style=\"margin-bottom: 20px\">\n    <div class=\"title-tips\" v-if=\"configData.tabList\">\n        <span>{{configData.title}}</span>{{configData.tabList[configData.tabVal].name}}\n    </div>\n    <div class=\"radio-box\" :class=\"{on:configData.type == 1}\">\n        <RadioGroup v-model=\"configData.tabVal\" type=\"button\" size=\"large\" @on-change=\"radioChange($event)\">\n            <Radio :label=\"index\" v-for=\"(item,index) in configData.tabList\" :key=\"index\">\n                <span class=\"iconfont-diy\" :class=\"item.icon\" v-if=\"item.icon\"></span>\n                <span v-else>{{item.name}}</span>\n            </Radio>\n        </RadioGroup>\n    </div>\n</div>\n", null]}