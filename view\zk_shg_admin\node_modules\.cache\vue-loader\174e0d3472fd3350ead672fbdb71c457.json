{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue?vue&type=template&id=745b9304&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue", "mtime": 1698031562000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Drawer',{attrs:{\"closable\":false,\"width\":\"1000\",\"class-name\":\"order_box\",\"styles\":{ padding: 0 }},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[(_vm.orderDatalist)?_c('div',[_c('div',{staticClass:\"head\"},[_c('div',{staticClass:\"full\"},[_c('Icon',{attrs:{\"custom\":\"iconfont icondingdan\",\"size\":\"60\"}}),_c('div',{staticClass:\"text\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"积分订单\")]),_c('div',[_vm._v(\"订单编号：\"+_vm._s(_vm.orderDatalist.orderInfo.order_id))])]),_c('div',[(_vm.orderData.status === 1)?_c('Button',{class:_vm.openErp?'on':'',attrs:{\"disabled\":_vm.openErp},on:{\"click\":_vm.sendOrder}},[_vm._v(\"发送货\")]):_vm._e(),(_vm.orderData.status === 2)?_c('Button',{class:_vm.openErp?'on':'',attrs:{\"disabled\":_vm.openErp},on:{\"click\":_vm.delivery}},[_vm._v(\"配送信息\")]):_vm._e(),(_vm.orderData.status >= 1)?_c('Button',{on:{\"click\":function($event){return _vm.changeMenu('10')}}},[_vm._v(\"小票打印\")]):_vm._e(),(_vm.orderData.status >= 1 && _vm.orderData.express_dump)?_c('Button',{class:_vm.openErp?'on':'',attrs:{\"disabled\":_vm.openErp},on:{\"click\":function($event){return _vm.changeMenu('11')}}},[_vm._v(\"电子面单打印\")]):_vm._e(),(_vm.orderData.is_del == 1)?_c('Button',{class:_vm.openErp?'on':'',attrs:{\"disabled\":_vm.openErp},on:{\"click\":function($event){return _vm.changeMenu('9')}}},[_vm._v(\"删除订单\")]):_vm._e(),(_vm.orderData.status !== 4)?_c('Dropdown',{on:{\"on-click\":_vm.changeMenu}},[_c('Button',{attrs:{\"icon\":\"ios-more\"}}),_c('DropdownMenu',{attrs:{\"slot\":\"list\"},slot:\"list\"},[(_vm.orderData.status !== 4)?_c('DropdownItem',{attrs:{\"name\":\"4\"}},[_vm._v(\"订单备注\")]):_vm._e(),(_vm.orderData.status === 2)?_c('DropdownItem',{attrs:{\"disabled\":_vm.openErp,\"name\":\"8\"}},[_vm._v(\"已收货\")]):_vm._e()],1)],1):_vm._e()],1)],1),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"订单状态\")]),_c('div',{staticClass:\"value1\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.status_name || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"兑换积分\")]),_c('div',[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.integral || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"兑换金额\")]),_c('div',[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.price || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"兑换时间\")]),_c('div',[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.add_time || '-'))])])])]),_c('Tabs',{model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:\"activeName\"}},[_c('TabPane',{attrs:{\"label\":\"订单信息\",\"name\":\"detail\"}},[_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"用户信息\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"用户昵称：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.userInfo.uid?_vm.orderDatalist.userInfo.nickname:'游客'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"绑定电话：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.userInfo.phone || '-'))])])])]),_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"收货信息\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"收货人：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.real_name || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"收货电话：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.user_phone || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"收货地址：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.user_address || '-'))])])])]),(_vm.orderDatalist.orderInfo.fictitious_content && _vm.orderDatalist.orderInfo.product_type !=1)?_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"虚拟发货\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.fictitious_content))])])])]):_vm._e(),(_vm.orderDatalist.orderInfo.product_type ==1)?_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"卡密发货\")]),(_vm.orderDatalist.orderInfo.virtual.length)?_c('div',_vm._l((_vm.orderDatalist.orderInfo.virtual),function(item,index){return _c('div',{key:index,staticClass:\"list\"},[_c('div',{staticClass:\"item\"},[_c('div',[_vm._v(\"卡号\"+_vm._s(index+1)+\"：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(item.card_no))])]),_c('div',{staticClass:\"item\"},[_c('div',[_vm._v(\"密码\"+_vm._s(index+1)+\"：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(item.card_pwd))])])])}),0):_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.virtual_info || '-'))])])])]):_vm._e(),_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"订单信息\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"创建时间：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo._add_time || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"商品总数：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.total_num || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"商品兑换积分：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.total_integral || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"商品兑换金额：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.total_price || '-'))])])])]),(_vm.orderDatalist.orderInfo.delivery_type==='express')?_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"物流信息\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"快递公司：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.delivery_name || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"快递单号：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.delivery_id)),_c('span',{staticClass:\"logisticsLook\",on:{\"click\":_vm.openLogistics}},[_vm._v(\"查询\")])])])])]):_vm._e(),(_vm.orderDatalist.orderInfo.delivery_type==='send')?_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"配送信息\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"送货人姓名：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.delivery_name || '-'))])]),_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"送货人电话：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.delivery_id || '-'))])])])]):_vm._e(),(_vm.orderDatalist.orderInfo.custom_form.length)?_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"自定义留言\")]),_c('ul',{staticClass:\"list\"},_vm._l((_vm.orderDatalist.orderInfo.custom_form),function(item,index){return ((item.value && ['uploadPicture','dateranges'].indexOf(item.name) == -1) || (item.value.length && ['uploadPicture','dateranges'].indexOf(item.name) != -1))?_c('li',{key:index,staticClass:\"item\"},[_c('div',{staticClass:\"txtVal\"},[_vm._v(_vm._s(item.titleConfig.value)+\"：\")]),(item.name == 'dateranges')?_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(item.value[0]+'/'+item.value[1]))]):(item.name === 'uploadPicture')?_c('div',{staticClass:\"value\"},_vm._l((item.value),function(img,i){return _c('div',{directives:[{name:\"viewer\",rawName:\"v-viewer\"}],key:i,staticClass:\"image\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(img),expression:\"img\"}]})])}),0):_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(item.value || '-'))])]):_vm._e()}),0)]):_vm._e(),(_vm.orderDatalist.orderInfo.mark)?_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"买家留言\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.mark))])])])]):_vm._e(),(_vm.orderDatalist.orderInfo.remark)?_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"订单备注\")]),_c('ul',{staticClass:\"list\"},[_c('li',{staticClass:\"item\"},[_c('div',[_vm._v(\"备注：\")]),_c('div',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderDatalist.orderInfo.remark || '-'))])])])]):_vm._e()]),_c('TabPane',{attrs:{\"label\":\"商品信息\",\"name\":\"product\"}},[_c('Table',{attrs:{\"columns\":_vm.columns1,\"data\":_vm.orderDatalist.orderInfo.cart_info,\"border\":\"\",\"highlight-row\":\"\"},scopedSlots:_vm._u([{key:\"product\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',{staticClass:\"product\"},[_c('div',{directives:[{name:\"viewer\",rawName:\"v-viewer\"}],staticClass:\"image\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.attrInfo ? row.attrInfo.image : row.image),expression:\"row.attrInfo ? row.attrInfo.image : row.image\"}]})]),_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(row.title)+\" | \"+_vm._s(row.attrInfo ? row.attrInfo.suk : ''))])])]}},{key:\"price\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',[_vm._v(_vm._s(row.attrInfo?row.attrInfo.price:row.price))])]}},{key:\"integral\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',[_vm._v(_vm._s(row.attrInfo?row.attrInfo.integral:row.integral))])]}}],null,false,1385001121)})],1),_c('TabPane',{attrs:{\"label\":\"订单记录\",\"name\":\"record\"}},[_c('Table',{attrs:{\"columns\":_vm.columns2,\"data\":_vm.recordData,\"border\":\"\",\"loading\":_vm.loading,\"no-data-text\":\"暂无数据\",\"highlight-row\":\"\",\"no-filtered-data-text\":\"暂无筛选结果\"}})],1)],1)],1):_vm._e()]),_c('Modal',{staticClass:\"order_box2\",attrs:{\"scrollable\":\"\",\"title\":\"物流查询\",\"width\":\"350\"},model:{value:(_vm.modal2),callback:function ($$v) {_vm.modal2=$$v},expression:\"modal2\"}},[(_vm.orderDatalist)?_c('div',{staticClass:\"logistics acea-row row-top\"},[_c('div',{staticClass:\"logistics_img\"},[_c('img',{attrs:{\"src\":require(\"../../../../assets/images/expressi.jpg\")}})]),_c('div',{staticClass:\"logistics_cent\"},[_c('span',[_vm._v(\"物流公司：\"+_vm._s(_vm.orderDatalist.orderInfo.delivery_name))]),_c('span',[_vm._v(\"物流单号：\"+_vm._s(_vm.orderDatalist.orderInfo.delivery_id))])])]):_vm._e(),_c('div',{staticClass:\"acea-row row-column-around trees-coadd\"},[_c('div',{staticClass:\"scollhide\"},[_c('Timeline',_vm._l((_vm.result),function(item,i){return _c('TimelineItem',{key:i},[_c('p',{staticClass:\"time\",domProps:{\"textContent\":_vm._s(item.time)}}),_c('p',{staticClass:\"content\",domProps:{\"textContent\":_vm._s(item.status)}})])}),1)],1)])])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}