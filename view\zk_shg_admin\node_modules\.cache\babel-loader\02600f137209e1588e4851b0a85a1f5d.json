{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_search_box.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_search_box.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapMutations } from 'vuex';\nexport default {\n  name: 'c_search_box',\n  componentsName: 'search_box',\n  cname: '搜索框',\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  data: function data() {\n    return {\n      hotIndex: 1,\n      configObj: {},\n      // 配置对象\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      // 当前页面组件\n      oneContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleLeft'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'styleConfig'\n      }],\n      oneContentType: [{\n        components: toolCom.c_radio,\n        configNme: 'styleTypeConfig'\n      }],\n      oneContentFix: [{\n        components: toolCom.c_radio,\n        configNme: 'fixConfig'\n      }],\n      twoContent: [{\n        components: toolCom.c_upload_img,\n        configNme: 'logoConfig'\n      }],\n      threeContent: [{\n        components: toolCom.c_input_item,\n        configNme: 'titleConfig'\n      }, {\n        components: toolCom.c_input_item,\n        configNme: 'linkConfig'\n      }],\n      rComContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleSearch'\n      }, {\n        components: toolCom.c_input_item,\n        configNme: 'tipConfig'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleHotWords'\n      }, {\n        components: toolCom.c_hot_word,\n        configNme: 'hotWords'\n      }, {\n        components: toolCom.c_input_number,\n        configNme: 'numConfig'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'searchBoxColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'tipColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'hotWordsColor'\n      }],\n      twoStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleTxt'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'txtFixConfig'\n      }],\n      twoStyle1: [{\n        components: toolCom.c_radio,\n        configNme: 'txtStyleConfig'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'txtColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'txtSize'\n      }],\n      currencyStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'moduleColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      setUp: 0,\n      type: 0,\n      type2: 0\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      // debugger;\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          this.getRComContent(arr);\n        } else {\n          this.getRComStyle(arr);\n        }\n      },\n      deep: true\n    },\n    'configObj.styleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp == 0) {\n          this.getRComContent(arr);\n        } else {\n          this.getRComStyle(arr);\n        }\n      },\n      deep: true\n    },\n    'configObj.styleTypeConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type2 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp == 0) {\n          this.getRComContent(arr);\n        } else {\n          this.getRComStyle(arr);\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    getRComContent: function getRComContent(arr) {\n      if (this.type == 1) {\n        this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.threeContent));\n      } else if (this.type == 2) {\n        this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentFix));\n      } else {\n        if (this.type2 == 0) {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentType), _toConsumableArray(this.threeContent), _toConsumableArray(this.rComContent));\n        } else if (this.type2 == 1) {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentType), _toConsumableArray(this.oneContentFix), _toConsumableArray(this.rComContent));\n        } else {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentType), _toConsumableArray(this.twoContent), _toConsumableArray(this.rComContent));\n        }\n      }\n    },\n    getRComStyle: function getRComStyle(arr) {\n      if (this.type == 0) {\n        if (this.type2 == 2) {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.currencyStyle));\n        } else {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.twoStyle1), _toConsumableArray(this.currencyStyle));\n        }\n      } else {\n        this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.twoStyle), _toConsumableArray(this.twoStyle1), _toConsumableArray(this.currencyStyle));\n      }\n    }\n  }\n};", null]}