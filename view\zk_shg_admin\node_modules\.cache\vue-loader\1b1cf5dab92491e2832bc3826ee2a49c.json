{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addDelivery\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addDelivery\\index.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState,mapMutations } from \"vuex\";\nimport {\n  savePromotions,\n  discountInfo\n} from \"@/api/marketing\";\nimport {\n  userLabelAddApi\n} from \"@/api/user\";\nimport { brandList } from \"@/api/product\";\nimport storeList from \"@/components/storeList\";\nimport storeLabelList from \"@/components/storeLabelList\";\nimport userLabel from \"@/components/labelList\";\nimport goodsList from '@/components/goodsList';\nimport goodsAttr from '@/components/goodsAttr';\nimport couponList from \"@/components/couponList\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"addPiecesDiscount\",\n  components: {\n    userLabel,\n    goodsList,\n    goodsAttr,\n    couponList,\n    storeLabelList,\n    storeList\n  },\n  data(){\n    return {\n      storesList:[],\n      storeModals: false,\n      roterPre: Setting.roterPre,\n      storeDataLabel: [],\n      storeLabelShow: false,\n      props: { emitPath: false, multiple: true },\n      brandData: [],\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      sattrModals: false,\n      modals: false,\n      tableData: [],\n      headTab: [\n        {\n          name: \"基础设置\",\n          type: '1',\n        },\n        {\n          name: \"添加商品\",\n          type: '2',\n        },\n        {\n          name: \"适用门店\",\n          type: '3',\n        }\n      ],\n      discountType: [\n        { name: \"满N元\", title: \"例：满100元送小样\", id: 1 },\n        { name: \"满N件\", title: \"例：满3件送小样\", id: 2 }\n      ],\n      activityType:[\n        {\n          name:'优惠券',\n          type:'5'\n        },\n        {\n          name:'满减满折',\n          type:'3'\n        },\n        {\n          name:'第N件N折',\n          type:'2'\n        },\n        {\n          name:'限时折扣',\n          type:'1'\n        }\n      ],\n      currentTab: '1',\n      dataLabel:[],\n      labelShow:false,\n      formValidate: {\n        applicable_type: 1,\n        name:'',\n        section_time:[],\n        is_label:0,\n        label_id: [],\n        is_overlay:'0',\n        overlay:[],\n        product_partake_type:1,\n        product_id:[],\n        threshold_type:1,\n        promotions_cate:'1',\n        promotions:[],\n        brand_id: [],\n        store_label_id: []\n      },\n      promotionsData: [\n        {\n          threshold:0,\n          give_integral:0,\n          checkIntegral:false,\n          checkCoupon:false,\n          checkGoods:false,\n          giveProducts:[],\n          giveCoupon:[]\n        }\n      ],\n      indexCoupon:0,\n      indexGoods:0,\n      ruleValidate: {\n        name: [\n          { required: true, message: '请输入活动名称', trigger: 'blur' }\n        ],\n        section_time: [\n          { required: true, type: 'array', message: '请选择活动时间', trigger: 'change' }\n        ],\n        promotions_cate: [\n          { required: true, message: '请设置满送方式', trigger: 'change' }\n        ],\n        is_overlay: [\n          { required: true, message: '请设置优惠叠加', trigger: 'change' }\n        ]\n      },\n      columns: [\n        {\n          title: '优惠券名称',\n          key: 'title',\n          minWidth: 150\n        },\n        {\n          title: '类型',\n          slot: 'coupon_type',\n          minWidth: 80\n        },\n        {\n          title: '面值',\n          slot: 'coupon_price',\n          minWidth: 100\n        },\n        {\n          title: '最低消费额',\n          key: 'use_min_price',\n          minWidth: 100\n        },\n        {\n          title: '限量',\n          key: 'limit_num',\n          width: 120,\n          render: (h, params) => {\n            return h(\"div\", [\n              h(\"InputNumber\", {\n                props: {\n                  value: params.row.limit_num,\n                  placeholder:'请输入',\n                  precision:0,\n                  min:0\n                },\n                on: {\n                  \"on-change\": (e) => {\n                    params.row.limit_num = e;\n                    this.promotionsData[params.row.indexCoupon].giveCoupon[params.index].limit_num = e;\n                  },\n                },\n              }),\n            ]);\n          }\n        },\n        {\n          title: '操作',\n          slot: 'status',\n          align:'center',\n          minWidth: 80\n        }\n      ],\n      colGoods:[\n        {\n          title: '商品信息',\n          slot: 'info',\n          minWidth: 200\n        },\n        {\n          title: '售价',\n          key: 'price',\n          minWidth: 80\n        },\n        {\n          title: '库存',\n          key: 'stock',\n          minWidth: 80\n        },\n        {\n          title: '单次赠送',\n          key: 'onceNum',\n          minWidth: 80\n        },\n        {\n          title: '总数量',\n          key: 'limit_num',\n          width: 120,\n          render: (h, params) => {\n            return h(\"div\", [\n              h(\"InputNumber\", {\n                props: {\n                  value: params.row.limit_num,\n                  placeholder:'请输入',\n                  precision:0,\n                  min:0\n                },\n                on: {\n                  \"on-change\": (e) => {\n                    params.row.limit_num = e;\n                    this.promotionsData[params.row.indexGoods].giveProducts[params.index].limit_num = e;\n                  },\n                },\n              }),\n            ]);\n          }\n        },\n        {\n          title: '操作',\n          slot: 'status',\n          align:'center',\n          minWidth: 80\n        }\n      ]\n    }\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\",\"menuCollapse\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n    labelBottom() {\n      return this.isMobile ? undefined : 15;\n    },\n  },\n  created(){},\n  mounted(){\n    this.setCopyrightShow({ value: false });\n    if(this.$route.params.id != 0){\n      this.getDiscountInfo();\n    }\n  },\n  destroyed () {\n    this.setCopyrightShow({ value: true });\n  },\n  methods: {\n    ...mapMutations('admin/layout', [\n      'setCopyrightShow'\n    ]),\n    //删除门店\n    delte(row){\n      this.storesList.forEach((item,index)=>{\n        if(row.id == item.id){\n          this.storesList.splice(index, 1)\n        }\n      })\n    },\n    //添加门店\n    addStore(){\n      this.storeModals = true;\n    },\n    //关闭门店弹窗\n    cancelStore(){\n      this.storeModals = false;\n    },\n    getStoreId (data) {\n      this.storeModals = false;\n      let list = this.storesList.concat(data);\n      let uni = this.unique(list);\n      this.storesList = uni;\n    },\n    closeStoreLabel(label){\n      let index = this.storeDataLabel.indexOf(this.storeDataLabel.filter(d=>d.id == label.id)[0]);\n      this.storeDataLabel.splice(index,1);\n    },\n    openStoreLabel() {\n      this.storeLabelShow = true;\n      this.$refs.storeLabel.storeLabel(\n          JSON.parse(JSON.stringify(this.storeDataLabel))\n      );\n    },\n    activeStoreData(storeDataLabel) {\n      this.storeLabelShow = false;\n      this.storeDataLabel = storeDataLabel;\n    },\n    // 标签弹窗关闭\n    storeLabelClose() {\n      this.storeLabelShow = false;\n    },\n    goodTap(e){\n      if(e==4){\n        this.getBrandList();\n      }\n    },\n    // 品牌列表\n    getBrandList(){\n      brandList().then(res=>{\n        this.brandData = res.data\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n      })\n    },\n    changeFull(){\n      this.promotionsData = [];\n      this.addLevel()\n    },\n    addLevel(){\n      let obj = {\n        threshold:0,\n        give_integral:0,\n        checkIntegral:false,\n        checkCoupon:false,\n        checkGoods:false,\n        giveProducts:[],\n        giveCoupon:[]\n      }\n      this.promotionsData.push(obj);\n    },\n    delCoupon(index,indexw){\n      this.promotionsData[indexw].giveCoupon.splice(index,1)\n    },\n    delGoods(index,indexw){\n      this.promotionsData[indexw].giveProducts.splice(index,1)\n    },\n    getCouponList(data){\n      let indexCoupon = this.indexCoupon;\n      this.$refs.couponTemplates.isTemplate = false;\n      data.forEach(j=>{\n        j.limit_num = 0;\n        j.indexCoupon = indexCoupon;\n      })\n      let list = this.promotionsData[indexCoupon].giveCoupon.concat(data);\n      let uni = this.unique(list);\n      this.promotionsData[indexCoupon].giveCoupon = uni;\n    },\n    // 添加优惠券\n    addCoupon(index) {\n      this.indexCoupon = index;\n      this.$refs.couponTemplates.isTemplate = true;\n      this.$refs.couponTemplates.tableList();\n    },\n    discountTypeTap(item){\n      this.formValidate.threshold_type = item.id;\n      if(item.id == 2){\n        this.promotionsData.forEach(item=>{\n          item.threshold = 0\n        })\n      }\n    },\n    getDiscountInfo(){\n      discountInfo(this.$route.params.id).then(res=>{\n        this.formValidate = res.data.info;\n        this.storesList = res.data.info.stores || [];\n        this.formValidate.promotions_cate = this.formValidate.promotions_cate.toString();\n        this.formValidate.is_overlay = this.formValidate.is_overlay.toString();\n        this.tableData = res.data.info.products;\n        this.dataLabel = res.data.info.label_id;\n        this.storeDataLabel = res.data.info.store_label_id;\n        if(res.data.info.product_partake_type == 4){\n          this.getBrandList();\n        }\n        let promotionsData = res.data.info.promotions;\n        promotionsData.forEach((item,index)=>{\n          item.checkCoupon = item.giveCoupon.length?true:false;\n          item.checkGoods = item.giveProducts.length?true:false;\n          item.checkIntegral = parseFloat(item.give_integral)>0?true:false;\n          item.giveCoupon.forEach(j=>{\n            j.indexCoupon = index;\n          })\n          item.giveProducts.forEach(i=>{\n            i.indexGoods = index;\n            i.onceNum = 1;\n          })\n        })\n        this.promotionsData = promotionsData;\n      }).catch(err=>{\n        this.$Message.error(err.msg);\n      })\n    },\n    del(row){\n      this.tableData.forEach((i,index)=>{\n        if(row.id == i.id){\n          return this.tableData.splice(index, 1)\n        }else{\n          i.attrValue.forEach((j,indexn)=>{\n            if(row.id == j.id){\n              if(i.attrValue.length == 1){\n                return this.tableData.splice(index, 1)\n              }else{\n                return i.attrValue.splice(indexn, 1)\n              }\n            }\n          })\n        }\n      })\n    },\n    addLabel(){\n      this.$modalForm(userLabelAddApi(0)).then(() => {});\n    },\n    addGoods(index){\n      this.modals = true;\n    },\n    addGoodsSattr(index){\n      this.sattrModals = true;\n      this.indexGoods = index;\n    },\n    cancel () {\n      this.modals = false;\n    },\n    //对象数组去重；\n    unique(arr) {\n      const res = new Map();\n      return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1))\n    },\n    getProductId (data) {\n      this.modals = false;\n      let list = this.tableData.concat(data);\n      let uni = this.unique(list);\n      uni.forEach((i)=>{\n        i.attrValue.forEach(j=>{\n          j.cate_name = i.cate_name;\n          j.store_label = i.store_label;\n        })\n      })\n      this.tableData = uni;\n    },\n    getAtterId(data){\n      this.sattrModals = false;\n      let indexGoods = this.indexGoods;\n      data.forEach((i)=>{\n        i.limit_num = 0;\n        i.indexGoods = indexGoods;\n        i.onceNum = 1;\n      })\n      let list = this.promotionsData[indexGoods].giveProducts.concat(data);\n      let uni = this.unique(list);\n      this.promotionsData[indexGoods].giveProducts = uni;\n    },\n    // 具体日期\n    onchangeTime (e) {\n      this.formValidate.section_time = e;\n    },\n    closeLabel(label){\n      let index = this.dataLabel.indexOf(this.dataLabel.filter(d=>d.id == label.id)[0]);\n      this.dataLabel.splice(index,1);\n    },\n    activeData(dataLabel){\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    openLabel(row) {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    // 标签弹窗关闭\n    labelClose() {\n      this.labelShow = false;\n    },\n    delGrade(index) {\n      this.promotionsData.splice(index,1);\n    },\n    // 上一页：\n    upTab() {\n      if(this.currentTab==1&&this.formValidate.product_type!=0){\n        this.currentTab = (Number(this.currentTab) - 2).toString();\n      }else{\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    // 下一页；\n    downTab(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if(this.formValidate.is_label && !this.dataLabel.length){\n            return this.$Message.warning(\"请选择用户关联标签\");\n          }\n          if(parseInt(this.formValidate.is_overlay) && !this.formValidate.overlay.length){\n            return this.$Message.warning(\"请选择叠加的营销活动\");\n          }\n          if(this.formValidate.section_time[0] == ''){\n            return this.$Message.warning(\"请选择活动时间\");\n          }\n          this.currentTab = (Number(this.currentTab) + 1).toString();\n        }else{\n          this.$Message.warning(\"请完善数据\");\n        }\n      })\n    },\n    handleSubmit(name){\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if(this.formValidate.is_label && !this.dataLabel.length){\n            return this.$Message.warning(\"请选择用户关联标签\");\n          }\n          if(parseInt(this.formValidate.is_overlay) && !this.formValidate.overlay.length){\n            return this.$Message.warning(\"请选择叠加的营销活动\");\n          }\n          if(this.formValidate.section_time[0] == ''){\n            return this.$Message.warning(\"请选择活动时间\");\n          }\n          if(this.formValidate.product_partake_type == 4 && !this.formValidate.brand_id.length){\n            return this.$Message.error('请添加指定品牌');\n          }\n          if(this.formValidate.product_partake_type == 5){\n            let labelIds = [];\n            this.storeDataLabel.forEach((item)=>{\n              labelIds.push(item.id)\n            });\n            if(!labelIds.length){\n              return this.$Message.error('请添加指定标签');\n            }\n            this.formValidate.store_label_id = labelIds\n          }\n          let promotions = [];\n          //优惠内容\n          let flag = true;\n          this.promotionsData.forEach(item=>{\n            if(item.threshold == null || item.threshold == 0){\n              this.flag = false;\n              return this.$Message.warning(\"请填写优惠门槛\");\n            }\n            if(item.checkIntegral && (item.give_integral == null || item.give_integral == 0)){\n              this.flag = false;\n              return this.$Message.warning(\"请填写赠送积分\");\n            }\n            if(item.checkCoupon && !item.giveCoupon.length){\n              this.flag = false;\n              return this.$Message.warning(\"请添加优惠券\");\n            }\n            if(item.checkGoods && !item.giveProducts.length){\n              this.flag = false;\n              return this.$Message.warning(\"请添加赠品\");\n            }\n            this.flag = true;\n            let obj = {\n              threshold: item.threshold,\n              give_integral: 0,\n              give_coupon_id:[],\n              give_product_id:[]\n            }\n            if(item.checkIntegral){\n              obj.give_integral = item.give_integral\n            }\n            if(item.checkCoupon){\n              item.giveCoupon.forEach(j=>{\n                let objVal = {\n                  give_coupon_num:j.limit_num,\n                  give_coupon_id:j.id\n                }\n                obj.give_coupon_id.push(objVal)\n              })\n            }\n            if(item.checkGoods){\n              item.giveProducts.forEach(v=>{\n                let goodsVal = {\n                  give_product_num:v.limit_num,\n                  give_product_id:v.product_id,\n                  unique:v.unique\n                }\n                obj.give_product_id.push(goodsVal)\n              })\n            }\n            promotions.push(obj)\n          })\n          if(!this.flag){\n            return\n          }\n          // 用户标签\n          let activeIds = [];\n          this.dataLabel.forEach((item)=>{\n            activeIds.push(item.id)\n          });\n          if(this.formValidate.is_label==0){\n            this.formValidate.label_id = [];\n          }else{\n            this.formValidate.label_id = activeIds\n          }\n          let product_id = [];\n          this.tableData.forEach((item)=>{\n            let obj = {\n              product_id: item.id,\n              unique:[]\n            }\n            if(item.attrValue.length){\n              item.attrValue.forEach((j)=>{\n                obj.unique.push(j.unique)\n              })\n            }\n            product_id.push(obj)\n          })\n          if(this.formValidate.product_partake_type ==2 && !product_id.length){\n            return this.$Message.error('请添加商品');\n          }\n          this.formValidate.promotions = promotions;\n          this.formValidate.product_id = product_id;\n          let storeId = []\n          this.storesList.forEach(item=>{\n            storeId.push(item.id)\n          })\n          if(this.formValidate.applicable_type==2 && !storeId.length){\n            return this.$Message.warning('请添加适用门店');\n          }\n          this.formValidate.applicable_store_id = storeId;\n          savePromotions(4,this.$route.params.id,this.formValidate).then(res=>{\n            this.$router.push({ path: `${this.roterPre}/marketing/discount/give` });\n            this.$Message.success(res.msg)\n          }).catch(err=>{\n            this.$Message.error(err.msg);\n          })\n        }else{\n          this.$Message.warning(\"请完善数据\");\n        }\n      })\n    }\n  }\n}\n", null]}