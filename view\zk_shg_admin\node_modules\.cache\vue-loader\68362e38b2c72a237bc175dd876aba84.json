{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_comb.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_comb.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nexport default {\n  name: 'c_home_comb',\n  componentsName: 'home_comb',\n  components: {\n    ...toolCom,\n    rightBtn\n  },\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  data() {\n    return {\n      configObj: {},\n      rCom: [\n        {\n          components: toolCom.c_set_up,\n          configNme: 'setUp'\n        }\n      ],\n\t  oneContent: [\n\t\t{\n\t\t\tcomponents: toolCom.c_title,\n\t\t\tconfigNme: 'titleLeft'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'styleConfig'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'classConfig'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'searchConfig'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'searchBox'\n\t\t},\n\t\t{\n\t\t\tcomponents: toolCom.c_title,\n\t\t\tconfigNme: 'titleSearch'\n\t\t}\n\t  ],\n\t  fixContent: [\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'searchFix'\n\t\t}\n\t  ],\n\t  txtContent: [\n\t\t{\n\t\t    components: toolCom.c_input_item,\n\t\t    configNme: 'titleConfig'\n\t\t}\n\t  ],\n\t  logoContent: [\n\t\t{\n\t\t   components: toolCom.c_upload_img,\n\t\t   configNme: 'logoConfig'\n\t\t}\n\t  ],\n\t  twoContent: [\n\t\t{\n\t\t  components: toolCom.c_input_item,\n\t\t  configNme: 'inputConfig'\n\t\t},\n\t\t{\n\t\t\tcomponents: toolCom.c_title,\n\t\t\tconfigNme: 'titleHotWords'\n\t\t},\n\t\t{\n\t\t  components: toolCom.c_hot_word,\n\t\t  configNme: 'hotWords'\n\t\t},\n\t\t{\n\t\t  components: toolCom.c_input_number,\n\t\t  configNme: 'numConfig'\n\t\t}  \n\t  ],\n\t  threeContent: [\n\t\t{\n\t\t  components: toolCom.c_title,\n\t\t  configNme: 'titleTab'\n\t\t},\n\t\t{\n\t\t  components: toolCom.c_tab_list,\n\t\t  configNme: 'tabListConfig'\n\t\t}\n\t  ],\n\t  rComContent: [\n\t\t{\n\t\t  components: toolCom.c_title,\n\t\t  configNme: 'titleImg'\n\t\t},\n\t\t{\n\t\t  components: toolCom.c_menu_list,\n\t\t  configNme: 'swiperConfig'\n\t\t}  \n\t  ],\n\t  oneStyle: [\n\t\t{\n\t\t\tcomponents: toolCom.c_title,\n\t\t\tconfigNme: 'titleRight'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_slider,\n\t\t    configNme: 'contentConfig'\n\t\t},\n\t\t{\n\t\t  components: toolCom.c_bg_color,\n\t\t  configNme: 'classColor'\n\t\t}\n\t  ],\n\t  twoStyle:[\n\t\t{\n\t\t\tcomponents: toolCom.c_title,\n\t\t\tconfigNme: 'titlePointer'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'docConfig'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'docPosition'\n\t\t},\n\t\t{\n\t\t    components: toolCom.c_radio,\n\t\t    configNme: 'toneConfig'\n\t\t}  \n\t  ],\n\t  threeStyle:[\n\t  \t{\n\t  \t    components: toolCom.c_bg_color,\n\t  \t    configNme: 'dotColor'\n\t  \t},\n\t  \t{\n\t  \t    components: toolCom.c_bg_color,\n\t  \t    configNme: 'dotBgColor'\n\t  \t}\n\t  ],\n\t  fourStyle:[\n\t  \t{\n\t  \t\tcomponents: toolCom.c_title,\n\t  \t\tconfigNme: 'titleImg'\n\t  \t},\n\t  \t{\n\t  \t    components: toolCom.c_fillet,\n\t  \t    configNme: 'filletImg'\n\t  \t}\n\t  ],\n\t  setUp:0,\n\t  type:0,\n\t  type2:0,\n\t  type3:0\n    };\n  },\n  watch: {\n    num(nVal) {\n      const value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler(nVal, oVal) {\n\t\tthis.setUp = nVal;\n        var arr = [this.rCom[0]];\n        if (nVal == 0) {\n\t\t\tthis.getRComContent(arr);\n        } else {\n\t\t\tthis.getRComStyle(arr);\n        }\n      },\n      deep: true\n    },\n\t'configObj.classConfig.tabVal':{\n\t  handler(nVal, oVal) {\n\t  \tthis.type = nVal;\n\t\tvar arr = [this.rCom[0]];\n\t\tif(this.setUp == 0){\n\t\t  this.getRComContent(arr);\n\t\t}else{\n\t\t  this.getRComStyle(arr);\n\t\t}\n\t  },\n\t  deep: true\n\t},\n\t'configObj.searchBox.tabVal':{\n\t\thandler(nVal, oVal) {\n\t\t  this.type2 = nVal;\n\t\t  var arr = [this.rCom[0]];\n\t\t  if(this.setUp == 0){\n\t\t\tthis.getRComContent(arr);\n\t\t  }else{\n\t\t\tthis.getRComStyle(arr);  \n\t\t  }\n\t\t}\n\t},\n\t'configObj.toneConfig.tabVal':{\n\t\thandler(nVal, oVal) {\n\t\t  this.type3 = nVal;\n\t\t  var arr = [this.rCom[0]];\n\t\t  if(this.setUp == 0){\n\t\t\tthis.getRComContent(arr);\n\t\t  }else{\n\t\t\tthis.getRComStyle(arr);  \n\t\t  }\n\t\t}\n\t}\n  },\n  mounted() {\n    this.$nextTick(() => {\n      const value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]));\n      this.configObj = value;\n    });\n  },\n  methods: {\n\tgetRComContent(arr) {\n\t\tif(this.type == 0){\n\t\t\tif(this.type2 == 0){\n\t\t\t  this.rCom = [...arr,...this.oneContent,...this.fixContent,...this.twoContent,...this.threeContent,...this.rComContent]\n\t\t\t}else if(this.type2 == 1){\n\t\t\t  this.rCom = [...arr,...this.oneContent,...this.txtContent,...this.twoContent,...this.threeContent,...this.rComContent]\n\t\t\t}else{\n\t\t\t  this.rCom = [...arr,...this.oneContent,...this.logoContent,...this.twoContent,...this.threeContent,...this.rComContent]\n\t\t\t}\n\t\t}else{\n\t\t\tif(this.type2 == 0){\n\t\t\t  this.rCom = [...arr,...this.oneContent,...this.fixContent,...this.twoContent,...this.rComContent]\n\t\t\t}else if(this.type2 == 1){\n\t\t\t  this.rCom = [...arr,...this.oneContent,...this.txtContent,...this.twoContent,...this.rComContent]\n\t\t\t}else{\n\t\t\t  this.rCom = [...arr,...this.oneContent,...this.logoContent,...this.twoContent,...this.rComContent]\n\t\t\t}\n\t\t}\n\t},\n\tgetRComStyle(arr) {\n\t\tif(this.type == 0){\n\t\t\tif(this.type3 == 0){\n\t\t\t   this.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.fourStyle]\n\t\t\t}else{\n\t\t\t   this.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle,...this.fourStyle]\n\t\t\t}\n\t\t}else{\n\t\t\tif(this.type3 == 0){\n\t\t\t   this.rCom = [...arr,...this.twoStyle,...this.fourStyle]\n\t\t\t}else{\n\t\t\t   this.rCom = [...arr,...this.twoStyle,...this.threeStyle,...this.fourStyle]\n\t\t\t}\n\t\t}\n\t},\n    handleSubmit(name) {\n      const obj = {};\n      obj.activeIndex = this.activeIndex;\n      obj.data = this.configObj;\n      this.add(obj);\n    },\n    ...mapMutations({\n      add: 'admin/mobildConfig/UPDATEARR'\n    })\n  }\n};\n", null]}