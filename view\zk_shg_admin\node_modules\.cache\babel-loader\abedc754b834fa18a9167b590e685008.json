{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport vuedraggable from 'vuedraggable';\nimport uploadPictures from '@/components/uploadPictures';\nimport linkaddress from '@/components/linkaddress';\nexport default {\n  name: \"c_foot\",\n  props: {\n    configObj: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    configNme: {\n      type: String,\n      default: ''\n    }\n  },\n  components: {\n    uploadPictures: uploadPictures,\n    linkaddress: linkaddress,\n    draggable: vuedraggable\n  },\n  data: function data() {\n    return {\n      val1: '',\n      val2: '',\n      footConfig: [],\n      modalPic: false,\n      isChoice: '单选',\n      itemIndex: 0,\n      itemChildIndex: 0,\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      navStyle: 0\n    };\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.footConfig = nVal[this.configNme];\n        this.navStyle = nVal.navStyleConfig.tabVal;\n      },\n      deep: true\n    }\n  },\n  created: function created() {\n    this.footConfig = this.configObj[this.configNme];\n  },\n  methods: {\n    linkUrl: function linkUrl(e) {\n      this.footConfig[this.itemIndex].link = e;\n    },\n    getLink: function getLink(index) {\n      this.itemIndex = index;\n      this.$refs.linkaddres.modals = true;\n    },\n    // 点击图文封面\n    modalPicTap: function modalPicTap(parent, child) {\n      this.itemIndex = parent;\n      this.itemChildIndex = child;\n      this.modalPic = true;\n    },\n    // 获取图片信息\n    getPic: function getPic(pc) {\n      var _this = this;\n\n      this.$nextTick(function () {\n        _this.footConfig[_this.itemIndex].imgList[_this.itemChildIndex] = pc.att_dir;\n        _this.modalPic = false;\n\n        _this.$store.commit('admin/mobildConfig/footUpdata', _this.footConfig);\n      });\n    },\n    // 添加模块\n    addMenu: function addMenu() {\n      var obj = {\n        imgList: ['', ''],\n        name: '自定义',\n        link: ''\n      };\n      this.footConfig.push(obj);\n    },\n    deleteMenu: function deleteMenu(index) {\n      var _this2 = this;\n\n      this.$Modal.confirm({\n        title: '提示',\n        content: '是否确定删除该菜单',\n        onOk: function onOk() {\n          _this2.footConfig.splice(index, 1);\n        },\n        onCancel: function onCancel() {}\n      });\n    }\n  }\n};", null]}