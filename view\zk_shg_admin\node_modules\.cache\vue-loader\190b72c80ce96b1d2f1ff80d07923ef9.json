{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\userExtract\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\userExtract\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport cardsData from \"@/components/cards/cards\";\nimport searchFrom from \"@/components/publicSearchFrom\";\nimport { mapState } from \"vuex\";\nimport { cashListApi, cashEditApi, refuseApi } from \"@/api/finance\";\nimport { formatDate } from \"@/utils/validate\";\nimport editFrom from \"@/components/from/from\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: \"cashApply\",\n  components: { cardsData, searchFrom, editFrom },\n  filters: {\n    formatDate(time) {\n      if (time !== 0) {\n        let date = new Date(time * 1000);\n        return formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    },\n  },\n  data() {\n    return {\n      modal_loading: false,\n      options: timeOptions,\n      fail_msg: {\n        message: \"输入信息不完整或有误!\",\n      },\n      modals: false,\n      total: 0,\n      cardLists: [],\n      loading: false,\n      columns: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          width: 80,\n        },\n        {\n          title: \"用户信息\",\n          slot: \"nickname\",\n          minWidth: 180,\n        },\n        {\n          title: \"提现到账金额\",\n          slot: \"extract_price\",\n          minWidth: 90,\n        },\n        {\n          title: \"手续费\",\n          slot: \"extract_fee\",\n          minWidth: 90,\n        },\n        {\n          title: \"提现方式\",\n          slot: \"extract_type\",\n          minWidth: 150,\n        },\n        {\n          title: \"收款码\",\n          slot: \"qrcode_url\",\n          minWidth: 150,\n        },\n        {\n          title: \"添加时间\",\n          slot: \"add_time\",\n          minWidth: 100,\n        },\n        {\n          title: \"备注\",\n          key: \"mark\",\n          minWidth: 100,\n        },\n        {\n          title: \"审核状态\",\n          slot: \"status\",\n          minWidth: 180,\n        },\n        {\n          title: \"操作\",\n          slot: \"createModalFrame\",\n          fixed: \"right\",\n          width: 100,\n        },\n      ],\n      tabList: [],\n      fromList: {\n        title: \"选择时间\",\n        custom: true,\n        fromTxt: [\n          { text: \"全部\", val: \"\" },\n          { text: \"昨天\", val: \"yesterday\" },\n          { text: \"今天\", val: \"today\" },\n          { text: \"本周\", val: \"week\" },\n          { text: \"本月\", val: \"month\" },\n          { text: \"本季度\", val: \"quarter\" },\n          { text: \"本年\", val: \"year\" },\n        ],\n      },\n      treeData: {\n        withdrawal: [\n          {\n            title: \"全部\",\n            value: \"\",\n          },\n          {\n            title: \"未通过\",\n            value: -1,\n          },\n          {\n            title: \"申请中\",\n            value: 0,\n          },\n          {\n            title: \"已通过\",\n            value: 1,\n          },\n        ],\n        payment: [\n          {\n            title: \"全部\",\n            value: \"\",\n          },\n          {\n            title: \"支付宝\",\n            value: \"alipay\",\n          },\n          {\n            title: \"银行卡\",\n            value: \"bank\",\n          },\n          {\n            title: \"微信\",\n            value: \"wx\",\n          },\n          // {\n          // \ttitle: '提现到余额',\n          // \tvalue: 'balance'\n          // }\n        ],\n      },\n      formValidate: {\n        status: \"\",\n        extract_type: \"\",\n        nireid: \"\",\n        data: \"\",\n        page: 1,\n        limit: 20,\n      },\n      extractStatistics: {},\n      timeVal: [],\n      FromData: null,\n      extractId: 0,\n    };\n  },\n  watch: {\n    $route() {\n      if (\n        this.$route.fullPath === \"/finance/user_extract/index?status=0\"\n      ) {\n        this.getPath();\n      }\n    },\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  // created() {\n  //     if(this.$route.query.key == 0){\n  //         this.formValidate.status = parseInt(this.$route.query.key)\n  //     }\n  // },\n  mounted() {\n    if (this.$route.fullPath === \"/finance/user_extract/index?status=0\") {\n      this.getPath();\n    } else {\n      this.getList();\n    }\n    // this.getList();\n  },\n  methods: {\n    getPath() {\n      this.formValidate.page = 1;\n      this.formValidate.status = parseInt(this.$route.query.status);\n      this.getList();\n    },\n    // 无效\n    invalid(row) {\n      this.extractId = row.id;\n      this.modals = true;\n    },\n    // 确定\n    oks() {\n      this.modal_loading = true;\n      refuseApi(this.extractId, this.fail_msg)\n        .then(async (res) => {\n          this.$Message.success(res.msg);\n          this.modal_loading = false;\n          this.modals = false;\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 通过\n    adopt(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `finance/extract/adopt/${row.id}`,\n        method: \"put\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.formValidate.page = 1;\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 选择\n    selChange() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    reset(){\n      this.formValidate = {\n        status: \"\",\n        extract_type: \"\",\n        nireid: \"\",\n        data: \"\",\n        page: 1,\n        limit: 20\n      };\n      this.timeVal = [];\n      // this.$refs.formValidate.resetFields()\n      this.getList();\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      cashListApi(this.formValidate)\n        .then(async (res) => {\n          let data = res.data;\n          this.tabList = data.list.list;\n          this.total = data.list.count;\n          this.extractStatistics = data.extract_statistics;\n          this.cardLists = [\n            {\n              col: 6,\n              count: this.extractStatistics.price,\n              name: \"待提现金额\",\n              className: \"md-basket\",\n            },\n            {\n              col: 6,\n              count: this.extractStatistics.brokerage_count,\n              name: \"佣金总金额\",\n              className: \"md-pricetags\",\n            },\n            {\n              col: 6,\n              count: this.extractStatistics.priced,\n              name: \"已提现金额\",\n              className: \"md-cash\",\n            },\n            {\n              col: 6,\n              count: this.extractStatistics.brokerage_not,\n              name: \"未提现金额\",\n              className: \"ios-cash\",\n            },\n          ];\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 编辑\n    edit(row) {\n      cashEditApi(row.id)\n        .then(async (res) => {\n          if (res.data.status === false) {\n            return this.$authLapse(res.data);\n          }\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 编辑提交成功\n    submitFail() {\n      this.getList();\n    },\n  },\n};\n", null]}