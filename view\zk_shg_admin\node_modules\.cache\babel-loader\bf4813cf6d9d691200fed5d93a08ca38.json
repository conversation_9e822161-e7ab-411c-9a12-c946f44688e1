{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue", "mtime": 1718268362000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from 'vuex';\nimport Setting from '@/setting';\nimport util from '@/libs/util';\nimport vuedraggable from 'vuedraggable';\nimport uploadPictures from '@/components/uploadPictures';\nimport freightTemplate from '@/components/freightTemplate';\nimport wangeditor from '@/components/wangEditor/index.vue';\nimport storeList from \"@/components/storeList\";\nimport menusFrom from '../productBrand/components/menusFrom';\nimport userLabel from '@/components/labelList';\nimport storeLabelList from '@/components/storeLabelList';\nimport couponList from '@/components/couponList';\nimport goodsList from '@/components/goodsList/index';\nimport addAttr from '../productAttr/addAttr';\nimport attrList from './attrList';\nimport addCarMy from './addCarMy';\nimport taoBao from '../productAdd/taoBao';\nimport replyList from './replyList.vue';\nimport { productInfoApi, cascaderListApi, productAddApi, generateAttrApi, productGetRuleApi, productGetTemplateApi, productGetTempKeysApi, checkActivityApi, labelListApi, productCache, cacheDelete, brandList, productCreateApi, productAllUnit, productUnitCreate, uploadType as _uploadType, productAllEnsure, productLabelAdd, productAllSpecs, setReplyApi, replyListApi, allSystemForm } from '@/api/product';\nimport { systemFormInfo } from '@/api/setting';\nimport { getSupplierList as _getSupplierList } from '@/api/supplier';\nimport { erpConfig } from '@/api/erp';\nimport { uploadByPieces } from '@/utils/upload'; //引入uploadByPieces方法\n\nexport default {\n  name: 'product_productAdd',\n  components: {\n    storeList: storeList,\n    uploadPictures: uploadPictures,\n    freightTemplate: freightTemplate,\n    goodsList: goodsList,\n    addAttr: addAttr,\n    couponList: couponList,\n    taoBao: taoBao,\n    userLabel: userLabel,\n    menusFrom: menusFrom,\n    attrList: attrList,\n    addCarMy: addCarMy,\n    storeLabelList: storeLabelList,\n    wangeditor: wangeditor,\n    draggable: vuedraggable,\n    replyList: replyList\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    productId: {\n      type: Number,\n      default: 0\n    }\n  },\n  data: function data() {\n    var _this = this;\n\n    return {\n      formTypeList: [],\n      formColumns: [{\n        title: '表单标题',\n        key: 'title',\n        // align:'center',\n        minWidth: 100\n      }, {\n        title: '表单类型',\n        key: 'name',\n        // align:'center',\n        minWidth: 100\n      }, {\n        title: '是否必填',\n        slot: 'require',\n        // align:'center',\n        minWidth: 100\n      }],\n      storesList: [],\n      tableData: [],\n      storeModals: false,\n      roterPre: Setting.roterPre,\n      specsList: [],\n      supplierList: [],\n      specsColumns: [{\n        title: '参数名称',\n        key: 'name',\n        align: 'center',\n        width: 150,\n        render: function render(h, params) {\n          return h('div', [h('Input', {\n            props: {\n              value: params.row.name,\n              placeholder: '请输入参数名称'\n            },\n            on: {\n              'on-change': function onChange(e) {\n                params.row.name = e.target.value;\n                _this.specsList[params.index].name = e.target.value;\n              }\n            }\n          })]);\n        }\n      }, {\n        title: '参数值',\n        key: 'value',\n        align: 'center',\n        width: 300,\n        render: function render(h, params) {\n          return h('div', [h('Input', {\n            props: {\n              value: params.row.value,\n              placeholder: '请输入参数值'\n            },\n            on: {\n              'on-change': function onChange(e) {\n                params.row.value = e.target.value;\n                _this.specsList[params.index].value = e.target.value;\n              }\n            }\n          })]);\n        }\n      }, {\n        title: '排序',\n        key: 'sort',\n        align: 'center',\n        width: 100,\n        render: function render(h, params) {\n          return h('div', [h('InputNumber', {\n            props: {\n              value: parseInt(params.row.sort) || 0,\n              placeholder: '排序',\n              precision: 0\n            },\n            on: {\n              'on-change': function onChange(e) {\n                params.row.sort = e;\n                _this.specsList[params.index].sort = e;\n              }\n            }\n          })]);\n        }\n      }, {\n        title: '操作',\n        slot: 'action',\n        align: 'center',\n        minWidth: 120\n      }],\n      //自定义留言下拉选择\n      customList: [{\n        value: 'text',\n        label: '文本框'\n      }, {\n        value: 'number',\n        label: '数字'\n      }, {\n        value: 'email',\n        label: '邮件'\n      }, {\n        value: 'data',\n        label: '日期'\n      }, {\n        value: 'time',\n        label: '时间'\n      }, {\n        value: 'id',\n        label: '身份证'\n      }, {\n        value: 'phone',\n        label: '手机号'\n      }, {\n        value: 'img',\n        label: '图片'\n      }],\n      headTab: [{\n        title: '基础信息',\n        name: '1'\n      }, {\n        title: '规格库存',\n        name: '2'\n      }, {\n        title: '商品详情',\n        name: '3'\n      }, {\n        title: '物流设置',\n        name: '4'\n      }, {\n        title: '营销设置',\n        name: '5'\n      }, {\n        title: '其他设置',\n        name: '6'\n      }, {\n        title: \"适用门店\",\n        name: \"8\"\n      }, {\n        title: '商品评论',\n        name: '7'\n      }],\n      productType: [{\n        name: '普通商品',\n        title: '物流发货',\n        id: 0\n      }, {\n        name: '卡密/网盘',\n        title: '自动发货',\n        id: 1\n      }, {\n        name: '虚拟商品',\n        title: '虚拟发货',\n        id: 3\n      }, {\n        name: \"次卡商品\",\n        title: \"到店核销\",\n        id: 4\n      }],\n      virtualList: [],\n      carMyShow: false,\n      //是否开启卡密弹窗\n      recommend: [],\n      //商品推荐\n      customBtn: false,\n      //自定义留言开关\n      attrShow: false,\n      content: '',\n      contents: '',\n      seletVideo: 0,\n      fileUrl: Setting.apiBaseURL + '/file/upload',\n      fileUrl2: Setting.apiBaseURL + '/file/video_upload',\n      upload_type: '',\n      //视频上传类型 1 本地上传 2 3 4 OSS上传\n      uploadData: {},\n      // 上传参数\n      header: {},\n      dataLabel: [],\n      storeDataLabel: [],\n      labelShow: false,\n      storeLabelShow: false,\n      props: {\n        emitPath: false,\n        multiple: true,\n        checkStrictly: true\n      },\n      type: 0,\n      goodsModals: false,\n      off_show: 0,\n      modals: false,\n      spinShow: false,\n      openSubimit: false,\n      grid2: {\n        xl: 10,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      grid3: {\n        xl: 18,\n        lg: 18,\n        md: 20,\n        sm: 24,\n        xs: 24\n      },\n      // 批量设置表格data\n      oneFormBatch: [{\n        attr: '全部',\n        pic: '',\n        price: 0,\n        settle_price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        bar_code: '',\n        code: '',\n        weight: 0,\n        volume: 0\n      }],\n      // 规格数据\n      formDynamic: {\n        attrsName: '',\n        attrsVal: ''\n      },\n      formDynamicNameData: [],\n      isBtn: false,\n      columnsCarMy: [],\n      columns2: [{\n        title: '商品规格',\n        slot: 'attr',\n        align: 'center',\n        minWidth: 80\n      }, {\n        title: '图片',\n        slot: 'pic',\n        align: 'center',\n        minWidth: 80\n      }, {\n        title: '售价',\n        slot: 'price',\n        align: 'center',\n        minWidth: 95\n      }, {\n        title: '成本价',\n        slot: 'cost',\n        align: 'center',\n        minWidth: 95\n      }, {\n        title: '原价',\n        slot: 'ot_price',\n        align: 'center',\n        minWidth: 95\n      }, {\n        title: '库存',\n        slot: 'stock',\n        align: 'center',\n        minWidth: 95\n      }, {\n        title: '商品条形码',\n        slot: 'bar_code',\n        align: 'center',\n        minWidth: 120\n      }, {\n        title: '商品编号',\n        slot: 'code',\n        align: 'center',\n        minWidth: 120\n      }, {\n        title: '重量（KG）',\n        slot: 'weight',\n        align: 'center',\n        minWidth: 95\n      }, {\n        title: '体积(m³)',\n        slot: 'volume',\n        align: 'center',\n        minWidth: 95\n      }, {\n        title: '操作',\n        slot: 'action',\n        // fixed: \"right\",\n        align: 'center',\n        minWidth: 140\n      }],\n      columns: [],\n      columnsInstall: [],\n      columnsInstal2: [],\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      formValidate: {\n        system_form_id: 0,\n        //自定义表单\n        supplier_id: 0,\n        //供应商\n        is_presale_product: 0,\n        //预售商品开关\n        is_limit: 0,\n        //是否限购开关\n        limit_type: 1,\n        //1单次限购，2长期限购\n        limit_num: 1,\n        //限购数量\n        is_vip_product: 0,\n        //付费会员专属开关\n        is_support_refund: 0,\n        disk_info: '',\n        //卡密简介\n        presale_day: 1,\n        //预售发货时间-结束\n        presale_time: [],\n        auto_on_time: '',\n        video_open: false,\n        //视频按钮是否显示\n        store_name: '',\n        freight: 1,\n        //运费设置\n        postage: 0,\n        //设置运费金额\n        custom_form: [],\n        //自定义留言\n        cate_id: [],\n        label_id: [],\n        ensure_id: [],\n        keyword: '',\n        applicable_type: 1,\n        unit_name: '',\n        specs_id: 0,\n        store_info: '',\n        bar_code: '',\n        code: '',\n        image: '',\n        recommend_image: '',\n        slider_image: [],\n        description: '',\n        ficti: 0,\n        give_integral: 0,\n        sort: 0,\n        is_show: 1,\n        is_hot: 0,\n        is_benefit: 0,\n        is_best: 0,\n        is_new: 0,\n        is_good: 0,\n        is_postage: 0,\n        is_sub: [],\n        id: 0,\n        spec_type: 0,\n        video_link: '',\n        temp_id: '',\n        attrs: [],\n        items: [{\n          pic: '',\n          price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: '',\n          code: ''\n        }],\n        activity: ['默认', '秒杀', '砍价', '拼团'],\n        couponName: [],\n        header: [],\n        selectRule: '',\n        coupon_ids: [],\n        command_word: '',\n        delivery_type: ['1'],\n        specs: [],\n        recommend_list: [],\n        brand_id: [],\n        product_type: 0\n      },\n      ruleList: [],\n      templateList: [],\n      createBnt: false,\n      showIput: false,\n      manyFormValidate: [],\n      // 单规格表格data\n      oneFormValidate: [{\n        pic: '',\n        price: 0,\n        settle_price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        bar_code: '',\n        code: '',\n        weight: 0,\n        volume: 0,\n        brokerage: 0,\n        brokerage_two: 0,\n        vip_price: 0,\n        virtual_list: [],\n        write_times: 0,\n        //核销次数\n        write_valid: 1,\n        //核销时效\n        days: 1\n      }],\n      images: [],\n      imagesTable: '',\n      currentTab: '1',\n      isChoice: '',\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      modalPic: false,\n      template: false,\n      uploadList: [],\n      treeSelect: [],\n      labelSelect: [],\n      ensureData: [],\n      specsData: [],\n      picTit: '',\n      tableIndex: 0,\n      ruleValidate: {\n        store_name: [{\n          required: true,\n          message: '请输入商品名称',\n          trigger: 'blur'\n        }],\n        cate_id: [{\n          required: true,\n          message: '请选择商品分类',\n          trigger: 'change',\n          type: 'array'\n        }],\n        keyword: [{\n          required: true,\n          message: '请输入商品关键字',\n          trigger: 'blur'\n        }],\n        unit_name: [{\n          required: true,\n          message: '请输入单位',\n          trigger: 'change'\n        }],\n        store_info: [{\n          required: true,\n          message: '请输入商品简介',\n          trigger: 'blur'\n        }],\n        //image: [{ required: true, message: \"请上传商品图\", trigger: \"change\" }],\n        slider_image: [{\n          required: true,\n          message: '请上传商品轮播图',\n          type: 'array',\n          trigger: 'change'\n        }],\n        spec_type: [{\n          required: true,\n          message: '请选择商品规格',\n          trigger: 'change'\n        }],\n        selectRule: [{\n          required: true,\n          message: '请选择商品规格属性',\n          trigger: 'change'\n        }],\n        give_integral: [{\n          type: 'integer',\n          message: '请输入整数'\n        }] // delivery_type:[\n        //   { required: true, type: 'array', min: 1, message: '请选择配送方式', trigger: 'change' },\n        // ]\n\n      },\n      manyBrokerage: 0,\n      manyBrokerageTwo: 0,\n      manyVipPrice: 0,\n      upload: {\n        videoIng: false // 是否显示进度条；\n\n      },\n      videoIng: false,\n      // 是否显示进度条；\n      progress: 0,\n      // 进度条默认0\n      videoLink: '',\n      attrs: [],\n      activity: {\n        默认: 'colorBlue',\n        秒杀: 'colorBlue',\n        砍价: 'colorBlue',\n        拼团: 'colorBlue'\n      },\n      couponName: [],\n      updateIds: [],\n      updateName: [],\n      rakeBack: [{\n        title: '一级返佣',\n        slot: 'brokerage',\n        align: 'center',\n        width: 95\n      }, {\n        title: '二级返佣',\n        slot: 'brokerage_two',\n        align: 'center',\n        width: 95\n      }],\n      member: [{\n        title: '会员价',\n        slot: 'vip_price',\n        align: 'center',\n        width: 95\n      }],\n      headerCarMy: {\n        title: '卡密设置',\n        slot: 'fictitious',\n        align: 'center',\n        width: 95\n      },\n      columnsInstalM: [],\n      moveIndex: '',\n      goodsData: [],\n      brandData: [],\n      unitNameList: [],\n      formBrand: {},\n      attrsList: [],\n      activeAtter: [],\n      tabIndex: 0,\n      tabName: '',\n      attrData: [],\n      datePickerOptions: {\n        disabledDate: function disabledDate(date) {\n          return date && date.valueOf() < Date.now() - 86400000;\n        }\n      },\n      openErp: false,\n      replyColumns: [{\n        title: '评论ID',\n        key: 'id',\n        width: 80\n      }, {\n        title: '商品信息',\n        slot: 'info',\n        minWidth: 250\n      }, // {\n      //   title: '用户名称',\n      //   key: 'nickname',\n      //   minWidth: 150,\n      // },\n      // {\n      //   title: '评分',\n      //   key: 'score',\n      //   sortable: true,\n      //   minWidth: 70,\n      // },\n      {\n        title: '评价内容',\n        slot: 'content',\n        minWidth: 300\n      }, // {\n      //   title: '回复内容',\n      //   slot: 'reply',\n      //   minWidth: 160,\n      // },\n      {\n        title: '评价时间',\n        key: 'add_time',\n        sortable: true,\n        width: 150\n      }, {\n        title: '操作',\n        slot: 'action',\n        // fixed: 'right',\n        width: 150\n      }],\n      replyData: [],\n      replyLoading: false,\n      replyModal: false,\n      rows: {},\n      replyForm: {\n        content: ''\n      },\n      ruleInline: {\n        content: [{\n          required: true,\n          message: '请输入回复内容',\n          trigger: 'blur'\n        }]\n      },\n      replyValidate: {\n        is_reply: '',\n        data: '',\n        store_name: '',\n        account: '',\n        product_id: this.productId,\n        page: 1,\n        limit: 15\n      },\n      total: 0,\n      merchantType: 0,\n      formList: [],\n      storeColumns: [{\n        title: \"ID\",\n        key: \"id\",\n        width: 60\n      }, {\n        title: \"门店图片\",\n        slot: \"image\",\n        minWidth: 80\n      }, {\n        title: \"门店分类\",\n        key: \"cate_name\",\n        minWidth: 80\n      }, {\n        title: \"门店名称\",\n        key: \"name\",\n        minWidth: 80\n      }, {\n        title: \"联系电话\",\n        key: \"phone\",\n        minWidth: 90\n      }, {\n        title: \"门店地址\",\n        key: \"address\",\n        ellipsis: true,\n        minWidth: 150\n      }, {\n        title: \"营业时间\",\n        key: \"day_time\",\n        minWidth: 120\n      }, {\n        title: \"营业状态\",\n        key: \"status_name\",\n        minWidth: 80\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        width: 100\n      }]\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile', 'menuCollapse']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    },\n    labelBottom: function labelBottom() {\n      return this.isMobile ? undefined : 15;\n    },\n    startPickOptions: function startPickOptions() {\n      var that = this;\n      return {\n        disabledDate: function disabledDate(time) {\n          if (that.formValidate.auto_off_time) {\n            return time.getTime() > new Date(that.formValidate.auto_off_time).getTime() - 86400000;\n          }\n\n          return '';\n        }\n      };\n    },\n    endPickOptions: function endPickOptions() {\n      var that = this;\n      return {\n        disabledDate: function disabledDate(time) {\n          if (that.formValidate.is_show == '1') {\n            return time.getTime() < Date.now();\n          }\n\n          if (that.formValidate.auto_on_time) {\n            return time.getTime() < new Date(that.formValidate.auto_on_time).getTime() + 86400000;\n          }\n\n          return '';\n        }\n      };\n    }\n  }),\n  created: function created() {\n    this.getSupplierList();\n    this.columns = this.columns2.slice(1, 10);\n    var data = JSON.parse(JSON.stringify(this.columns2));\n    data.splice(8, 2, this.headerCarMy);\n    this.columnsCarMy = data;\n    var fictitious = JSON.parse(JSON.stringify(this.columns2));\n    fictitious.splice(8, 2);\n    this.columnsFictitious = fictitious;\n    this.getToken();\n    this.getErpConfig(); // this.columnsInstall = this.columns2.slice(0, 4).concat(this.columnsInstall);\n    // this.columnsInsta8 = this.columns2.slice(0, 4).concat(this.columnsInsta8);\n  },\n  mounted: function mounted() {\n    this.setCopyrightShow({\n      value: false\n    }); // if (this.productId !== '0' && this.productId) {\n    //   this.getInfo();\n    // } else if (this.productId === '0') {\n    //   productCache()\n    //       .then((res) => {\n    //         let data = res.data.info;\n    //         if (!Array.isArray(data)) {\n    //           let cate_id = data.cate_id.map(Number);\n    //           let label_id = data.label_id.map(Number);\n    //           this.attrs = data.items || [];\n    //           let ids = [];\n    //           // let names = [];\n    //           if (data.coupons) {\n    //             data.coupons.map((item) => {\n    //               ids.push(item.id);\n    //               // names.push(item.title);\n    //             });\n    //             this.couponName = data.coupons;\n    //           }\n    //           this.storesList = data.stores || [];\n    //           let brandIds = [];\n    //           data.brand_id.forEach(item=>{\n    //             brandIds.push(item.toString())\n    //           })\n    //           this.formValidate = data;\n    //           // this.couponName = data.coupons;\n    //           // that.couponName = names;\n    //           this.formValidate.brand_id = brandIds;\n    //           this.formValidate.coupon_ids = ids;\n    //           this.formValidate.is_limit = this.formValidate.is_limit ? 1 : 0;\n    //           this.formValidate.limit_type = parseInt(\n    //               this.formValidate.limit_type\n    //           );\n    //           this.formValidate.is_support_refund = parseInt(\n    //               this.formValidate.is_support_refund\n    //           );\n    //           this.updateIds = ids;\n    //           this.updateName = data.coupons;\n    //           this.formValidate.cate_id = cate_id;\n    //           this.dataLabel = data.label_id;\n    //           this.storeDataLabel = data.store_label_id;\n    //           this.specsList = data.specs;\n    //           this.oneFormValidate = data.attrs;\n    //           this.formValidate.header = [];\n    //           this.generate(0);\n    //           this.addmanyData(data.attrs);\n    //           this.productTypeTap(2);\n    //           this.columns = this.columns2.slice(1, 10);\n    //           //this.manyFormValidate = data.attrs;\n    //           this.formValidate.system_form_id = data.system_form_id || 0;\n    //           if (this.formValidate.system_form_id) {\n    //             this.customBtn = true;\n    //           }\n    //           this.spec_type = data.spec_type;\n    //           if (data.spec_type === 0) {\n    //             this.manyFormValidate = [];\n    //           } else {\n    //             this.createBnt = true;\n    //             this.oneFormValidate = [\n    //               {\n    //                 pic: data.slider_image[0],\n    //                 price: 0,\n    //                 cost: 0,\n    //                 ot_price: 0,\n    //                 stock: 0,\n    //                 bar_code: '',\n    //                 code: '',\n    //                 weight: 0,\n    //                 volume: 0,\n    //                 brokerage: 0,\n    //                 brokerage_two: 0,\n    //                 vip_price: 0,\n    //                 virtual_list: [],\n    //                 write_times: 0, //核销次数\n    //                 write_valid: 1, //核销时效\n    //                 days: 1\n    //               },\n    //             ];\n    //           }\n    //           this.spinShow = false;\n    //         }\n    //       })\n    //       .catch((err) => {\n    //         this.$Message.error(err.msg);\n    //       });\n    // }\n  },\n  destroyed: function destroyed() {\n    this.setCopyrightShow({\n      value: true\n    });\n  },\n  methods: _objectSpread({}, mapMutations('admin/layout', ['setCopyrightShow']), {\n    changeForm: function changeForm(e) {\n      this.getSystemFormInfo(e, {\n        type: 1\n      });\n    },\n    getSystemFormInfo: function getSystemFormInfo(e, data) {\n      var _this2 = this;\n\n      systemFormInfo(e, data).then(function (res) {\n        _this2.formTypeList = res.data.info;\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    //删除门店\n    delte: function delte(index) {\n      this.storesList.splice(index, 1);\n    },\n    //添加门店\n    addStore: function addStore() {\n      this.storeModals = true;\n    },\n    //关闭门店弹窗\n    cancelStore: function cancelStore() {\n      this.storeModals = false;\n    },\n    allFormList: function allFormList() {\n      var _this3 = this;\n\n      allSystemForm().then(function (res) {\n        _this3.formList = res.data;\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    limitTap: function limitTap(e) {\n      if (e) {\n        this.formValidate.limit_type = this.formValidate.is_limit && !this.formValidate.limit_type ? 1 : 0;\n        this.formValidate.limit_num = this.formValidate.is_limit && this.formValidate.limit_num == 0 ? 1 : 0;\n      } else {\n        this.formValidate.limit_type = 0;\n        this.formValidate.limit_num = 0;\n      }\n    },\n    //erp配置\n    getErpConfig: function getErpConfig() {\n      var _this4 = this;\n\n      erpConfig().then(function (res) {\n        _this4.openErp = res.data.open_erp;\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    },\n    delSpecs: function delSpecs(index) {\n      this.specsList.splice(index, 1);\n    },\n    addSpecs: function addSpecs() {\n      var obj = {\n        name: '',\n        value: '',\n        sort: 0\n      };\n      this.specsList.push(obj);\n    },\n    specsInfo: function specsInfo(e) {\n      var _this5 = this;\n\n      this.specsData.forEach(function (item) {\n        if (item.id == e) {\n          _this5.specsList = item.specs;\n        }\n      });\n    },\n    getProductAllSpecs: function getProductAllSpecs() {\n      var _this6 = this;\n\n      productAllSpecs().then(function (res) {\n        _this6.specsData = res.data;\n      }).catch(function (err) {\n        _this6.$Message.error(err.msg);\n      });\n    },\n    getProductAllEnsure: function getProductAllEnsure() {\n      var _this7 = this;\n\n      productAllEnsure().then(function (res) {\n        _this7.ensureData = res.data;\n      }).catch(function (err) {\n        _this7.$Message.error(err.msg);\n      });\n    },\n    //添加倒入卡密的值\n    changeVirtual: function changeVirtual(e) {\n      this.virtualList = this.virtualList.concat(e);\n    },\n    //添加卡密\n    addVirtual: function addVirtual(index, name) {\n      this.tabIndex = index;\n      this.tabName = name;\n      this.virtualListClear();\n      this.$refs.addCarMy.fixedCar = {\n        disk_info: '',\n        stock: 0\n      };\n      this.$refs.addCarMy.cartMyType = 1;\n      this.carMyShow = true;\n    },\n    //确认提交卡密\n    fixdBtn: function fixdBtn(e) {\n      if (e.cartMyType == 1) {\n        this.$set(this[this.tabName][this.tabIndex], 'disk_info', e.disk_info);\n        this.$set(this[this.tabName][this.tabIndex], 'stock', Number(e.stock));\n        this[this.tabName][this.tabIndex].virtual_list = [];\n      } else {\n        this.$set(this[this.tabName][this.tabIndex], 'virtual_list', e.virtualList);\n        this.$set(this[this.tabName][this.tabIndex], 'stock', e.virtualList.length);\n        this[this.tabName][this.tabIndex].disk_info = '';\n      }\n\n      this.carMyShow = false;\n    },\n    closeCarMy: function closeCarMy() {\n      this.carMyShow = false;\n    },\n    //清空卡密\n    virtualListClear: function virtualListClear() {\n      this.virtualList = [{\n        key: '',\n        value: ''\n      }];\n    },\n    seeVirtual: function seeVirtual(data, name, index) {\n      this.tabName = name;\n      this.tabIndex = index;\n      this.virtualListClear();\n      this.$refs.addCarMy.fixedCar = {\n        disk_info: '',\n        stock: 0\n      };\n\n      if (data.virtual_list && data.virtual_list.length) {\n        this.$refs.addCarMy.cartMyType = 2;\n        this.virtualList = data.virtual_list;\n      } else if (data.disk_info) {\n        this.$refs.addCarMy.cartMyType = 1;\n        this.$refs.addCarMy.fixedCar.disk_info = data.disk_info;\n        this.$refs.addCarMy.fixedCar.stock = data.stock;\n      }\n\n      this.carMyShow = true;\n    },\n    //动态添加组件\n    addAssembly: function addAssembly() {\n      this.formValidate.custom_form.push({\n        title: '',\n        label: 'text',\n        value: '',\n        status: 0\n      });\n    },\n    customMessBtn: function customMessBtn(e) {\n      if (!e) {\n        this.formValidate.system_form_id = 0;\n      }\n    },\n    addcustom: function addcustom() {\n      if (this.formValidate.custom_form.length > 9) {\n        this.$Message.warning('最多添加10条');\n      } else {\n        this.addAssembly();\n      }\n    },\n    delcustom: function delcustom(index) {\n      this.formValidate.custom_form.splice(index, 1);\n    },\n    // 预售具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.formValidate.presale_time = e;\n    },\n    //定时上架\n    onchangeShow: function onchangeShow(e) {\n      this.formValidate.auto_on_time = e;\n    },\n    //定时下架\n    onchangeOff: function onchangeOff(e) {\n      this.formValidate.auto_off_time = e;\n    },\n    // 获取供应商内容\n    getSupplierList: function getSupplierList() {\n      var _this8 = this;\n\n      _getSupplierList().then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this8.supplierList = res.data;\n\n                case 1:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this8.$Message.error(res.msg);\n      });\n    },\n    //打开属性\n    batchAttr: function batchAttr() {\n      this.attrShow = true; // if(!this.activeAtter.length){\n      //  let data = this.attrs;\n      //  data.map(el=>{\n      //  \t\t\tel.details = [];\n      //  \tel.detail.map(label=>{\n      //  \t\tel.details.push({\n      //  \t\t\tname:label,\n      //  \t\t\tselect:false\n      //  \t\t})\n      //  \t})\n      //  })\n      //  this.attrsList = data;\n      // }\n    },\n    //获取属性\n    getAttr: function getAttr() {\n      this.oneFormBatch[0].attr = '全部';\n      var data = this.attrs;\n      data.map(function (el) {\n        el.details = [];\n        el.detail.map(function (label) {\n          el.details.push({\n            name: label,\n            select: false\n          });\n        });\n      });\n      this.attrsList = data;\n    },\n    //选中属性\n    activeAttr: function activeAttr(e) {\n      this.attrsList = e;\n    },\n    //关闭属性弹窗\n    labelAttr: function labelAttr() {\n      this.attrShow = false;\n    },\n    //多属性为空\n    manyEmpty: function manyEmpty(j) {\n      j.pic = '';\n      j.price = 0;\n      j.settle_price = 0;\n      j.cost = 0;\n      j.ot_price = 0;\n      j.stock = 0;\n      j.bar_code = '';\n      j.code = '';\n      j.weight = 0;\n      j.volume = 0;\n      j.virtual_list = [];\n    },\n    doCombination: function doCombination(arr) {\n      var count = arr.length - 1; //数组长度(从0开始)\n\n      var tmp = [];\n      var totalArr = []; // 总数组\n\n      return doCombinationCallback(arr, 0); //从第一个开始\n      //js 没有静态数据，为了避免和外部数据混淆，需要使用闭包的形式\n\n      function doCombinationCallback(arr, curr_index) {\n        var _iteratorNormalCompletion = true;\n        var _didIteratorError = false;\n        var _iteratorError = undefined;\n\n        try {\n          for (var _iterator = arr[curr_index][Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n            var val = _step.value;\n            tmp[curr_index] = val; //以curr_index为索引，加入数组\n            //当前循环下标小于数组总长度，则需要继续调用方法\n\n            if (curr_index < count) {\n              doCombinationCallback(arr, curr_index + 1); //继续调用\n            } else {\n              totalArr.push(tmp.join(',')); //(直接给push进去，push进去的不是值，而是值的地址)\n            } //js  对象都是 地址引用(引用关系)，每次都需要重新初始化，否则 totalArr的数据都会是最后一次的 tmp 数据；\n\n\n            var oldTmp = tmp;\n            tmp = [];\n\n            for (var _i = 0, _oldTmp = oldTmp; _i < _oldTmp.length; _i++) {\n              var index = _oldTmp[_i];\n              tmp.push(index);\n            }\n          }\n        } catch (err) {\n          _didIteratorError = true;\n          _iteratorError = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion && _iterator.return != null) {\n              _iterator.return();\n            }\n          } finally {\n            if (_didIteratorError) {\n              throw _iteratorError;\n            }\n          }\n        }\n\n        return totalArr;\n      }\n    },\n    //提交属性值；\n    subAttrs: function subAttrs(e) {\n      var selectData = [];\n      this.attrsList.forEach(function (el, index) {\n        var obj = [];\n        el.details.forEach(function (label) {\n          if (label.select) {\n            obj.push(label.name);\n          }\n        });\n\n        if (obj.length) {\n          selectData.push(obj);\n        }\n      });\n      var newData = [];\n\n      if (selectData.length) {\n        newData = this.doCombination(selectData);\n      }\n\n      this.attrShow = false;\n      this.activeAtter = selectData;\n      this.oneFormBatch[0].attr = newData.length ? newData.join(';') : '全部';\n      this.manyFormValidate.forEach(function (j) {\n        j.select = false;\n\n        if (newData.length) {\n          newData.forEach(function (item) {\n            if (j.values.split('').length == item.split('').length) {\n              if (j.values == item) {\n                j.select = true;\n              }\n            } else {\n              if (j.values.indexOf(item) != -1) {\n                j.select = true;\n              }\n            }\n          });\n        } else {\n          j.select = true;\n        }\n      });\n      this.$set(this, 'manyFormValidate', this.manyFormValidate);\n    },\n    goodsOn: function goodsOn(e) {\n      if (e == 0 || e == 1) {\n        this.formValidate.auto_on_time = '';\n      }\n    },\n    goodsOff: function goodsOff(e) {\n      if (!e) {\n        this.formValidate.auto_off_time = '';\n      }\n    },\n    addBrand: function addBrand() {\n      this.$refs.menusFrom.modals = true;\n      this.$refs.menusFrom.titleFrom = '添加品牌分类';\n      this.formBrand = {\n        sort: 0,\n        is_show: 1\n      };\n      this.formBrand.fid = [0];\n      this.$refs.menusFrom.type = 1;\n    },\n    getAllUnit: function getAllUnit() {\n      var _this9 = this;\n\n      productAllUnit().then(function (res) {\n        _this9.unitNameList = res.data;\n      }).catch(function (err) {\n        _this9.$Message.error(err.msg);\n      });\n    },\n    addClass: function addClass() {\n      var _this10 = this;\n\n      this.$modalForm(productCreateApi()).then(function () {\n        return _this10.goodsCategory();\n      });\n    },\n    addUnit: function addUnit() {\n      var _this11 = this;\n\n      this.$modalForm(productUnitCreate()).then(function () {\n        return _this11.getAllUnit();\n      });\n    },\n    addStoreLabel: function addStoreLabel() {\n      this.$modalForm(productLabelAdd()).then(function () {});\n    },\n    productTypeTap: function productTypeTap(num, item) {\n      if (num == 1) {\n        if (this.productId) return this.$Message.error('商品类型不能切换！');\n        this.formValidate.product_type = item.id;\n      }\n\n      if (this.formValidate.product_type && this.formValidate.product_type != 4) {\n        this.headTab = [{\n          title: '基础信息',\n          name: '1'\n        }, {\n          title: '规格库存',\n          name: '2'\n        }, {\n          title: '商品详情',\n          name: '3'\n        }, {\n          title: '营销设置',\n          name: '5'\n        }, {\n          title: '其他设置',\n          name: '6'\n        }, {\n          title: '商品评论',\n          name: '7'\n        }];\n        this.formValidate.postage = 0;\n        this.formValidate.supplier_id = 0;\n      } else if (this.formValidate.product_type == 4) {\n        this.headTab = [{\n          title: \"基础信息\",\n          name: \"1\"\n        }, {\n          title: \"规格库存\",\n          name: \"2\"\n        }, {\n          title: \"商品详情\",\n          name: \"3\"\n        }, {\n          title: \"营销设置\",\n          name: \"5\"\n        }, {\n          title: \"其他设置\",\n          name: \"6\"\n        }, {\n          title: '商品评论',\n          name: '7'\n        }, {\n          title: \"适用门店\",\n          name: \"8\"\n        }];\n      } else {\n        this.headTab = [{\n          title: '基础信息',\n          name: '1'\n        }, {\n          title: '规格库存',\n          name: '2'\n        }, {\n          title: '商品详情',\n          name: '3'\n        }, {\n          title: '物流设置',\n          name: '4'\n        }, {\n          title: '营销设置',\n          name: '5'\n        }, {\n          title: '其他设置',\n          name: '6'\n        }, {\n          title: \"适用门店\",\n          name: \"8\"\n        }, {\n          title: '商品评论',\n          name: '7'\n        }];\n      }\n    },\n    closeLabel: function closeLabel(label) {\n      var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.dataLabel.splice(index, 1);\n    },\n    activeData: function activeData(dataLabel) {\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    openLabel: function openLabel(row) {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    // 标签弹窗关闭\n    labelClose: function labelClose() {\n      this.labelShow = false;\n    },\n    closeStoreLabel: function closeStoreLabel(label) {\n      var index = this.storeDataLabel.indexOf(this.storeDataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.storeDataLabel.splice(index, 1);\n    },\n    activeStoreData: function activeStoreData(storeDataLabel) {\n      this.storeLabelShow = false;\n      this.storeDataLabel = storeDataLabel;\n    },\n    openStoreLabel: function openStoreLabel(row) {\n      this.storeLabelShow = true;\n      this.$refs.storeLabel.storeLabel(JSON.parse(JSON.stringify(this.storeDataLabel)));\n    },\n    // 标签弹窗关闭\n    storeLabelClose: function storeLabelClose() {\n      this.storeLabelShow = false;\n    },\n    // 品牌列表\n    getBrandList: function getBrandList() {\n      var _this12 = this;\n\n      brandList().then(function (res) {\n        //initBran()函数作用iview中规定value必须是字符串，后台返回成了数字，用于处理这个，给了个递归；\n        _this12.initBran(res.data);\n\n        _this12.brandData = res.data;\n      }).catch(function (err) {\n        _this12.$Message.error(err.msg);\n      });\n    },\n    initBran: function initBran(data) {\n      var _this13 = this;\n\n      data.map(function (item) {\n        item.value = item.value.toString();\n\n        if (item.children && item.children.length) {\n          _this13.initBran(item.children);\n        }\n      });\n    },\n    getProductId: function getProductId(e) {\n      this.goodsModals = false;\n      var nArr = this.goodsData.concat(e).filter(function (element, index, self) {\n        return self.findIndex(function (x) {\n          return x.product_id == element.product_id;\n        }) == index;\n      });\n      this.goodsData = nArr.slice(0, 12);\n    },\n    goodCancel: function goodCancel() {\n      this.goodsModals = false;\n    },\n    goodsTap: function goodsTap() {\n      this.goodsModals = true;\n      this.$refs.goodslist.handleSelectAll();\n    },\n    bindDelete: function bindDelete(index) {\n      this.goodsData.splice(index, 1);\n    },\n    cancel: function cancel() {\n      this.$router.push({\n        path: this.roterPre + '/product/product_list'\n      });\n    },\n    videoSaveToUrl: function videoSaveToUrl(file) {\n      var _this14 = this;\n\n      var imgTypeArr = ['video/mp4'];\n      var imgType = imgTypeArr.indexOf(file.type) !== -1;\n\n      if (!imgType) {\n        return this.$Message.warning({\n          content: '文件  ' + file.name + '  格式不正确, 请选择格式正确的视频',\n          duration: 5\n        });\n      }\n\n      uploadByPieces({\n        randoms: '',\n        // 随机数，这里作为给后端处理分片的标识 根据项目看情况 是否要加\n        file: file,\n        // 视频实体\n        pieceSize: 3,\n        // 分片大小\n        success: function success(data) {\n          _this14.formValidate.video_link = data.file_path;\n          _this14.progress = 100;\n        },\n        error: function error(e) {\n          _this14.$Message.error(e.msg);\n        },\n        uploading: function uploading(chunk, allChunk) {\n          _this14.videoIng = true;\n          var st = Math.floor(chunk / allChunk * 100);\n          _this14.progress = st;\n        }\n      });\n      return false;\n    },\n    // 上传头部token\n    getToken: function getToken() {\n      this.header['Authori-zation'] = 'Bearer ' + util.cookies.get('token');\n    },\n    // beforeUpload() {\n    //   this.uploadData = {};\n    //   let promise = new Promise((resolve) => {\n    //     this.$nextTick(function () {\n    //       resolve(true);\n    //     });\n    //   });\n    //   return promise;\n    // },\n    // 上传成功\n    handleSuccess: function handleSuccess(res, file, fileList) {\n      if (res.status === 200) {\n        this.formValidate.video_link = res.data.src;\n        this.$Message.success(res.msg);\n      } else {\n        this.$Message.error(res.msg);\n      }\n    },\n    //获取视频上传类型\n    uploadType: function uploadType() {\n      var _this15 = this;\n\n      _uploadType().then(function (res) {\n        _this15.upload_type = res.data.upload_type;\n      });\n    },\n    getEditorContent: function getEditorContent(data) {\n      this.content = data;\n    },\n    infoData: function infoData(data) {\n      this.storesList = data.stores || [];\n      var cate_id = data.cate_id.map(Number);\n      this.attrs = data.items || [];\n      var ids = [];\n      data.coupons.map(function (item) {\n        ids.push(item.id);\n      });\n      this.goodsData = data.recommend_list;\n\n      if (data.auto_off_time) {\n        this.off_show = 1;\n      } else {\n        this.off_show = 0;\n      }\n\n      var brandIds = [];\n      data.brand_id.forEach(function (item) {\n        brandIds.push(item.toString());\n      });\n      this.formValidate = data;\n      this.formTypeList = data.custom_form_info;\n      this.formValidate.brand_id = brandIds;\n\n      if (data.type == 2) {\n        this.formValidate.supplier_id = data.relation_id;\n      }\n\n      this.formValidate.is_limit = this.formValidate.is_limit ? 1 : 0;\n      this.formValidate.limit_type = parseInt(data.limit_type);\n      this.formValidate.is_support_refund = parseInt(this.formValidate.is_support_refund);\n      this.contents = this.content = data.description;\n      this.couponName = data.coupons;\n      this.formValidate.coupon_ids = ids;\n      this.updateIds = ids;\n      this.updateName = data.coupons;\n      this.formValidate.cate_id = cate_id;\n      this.dataLabel = data.label_id;\n      this.storeDataLabel = data.store_label_id;\n      this.specsList = data.specs;\n\n      if (data.attr) {\n        this.oneFormValidate = [data.attr];\n      }\n\n      this.formValidate.header = [];\n      this.generate(0); //this.manyFormValidate = data.attrs;\n\n      this.addmanyData(data.attrs);\n      this.productTypeTap(2);\n      this.formValidate.system_form_id = data.system_form_id || 0;\n\n      if (this.formValidate.system_form_id) {\n        this.customBtn = true;\n      } else {\n        this.customBtn = false;\n      }\n\n      this.spec_type = data.spec_type;\n\n      if (data.spec_type === 0) {\n        this.manyFormValidate = [];\n      } else {\n        this.createBnt = true;\n        this.oneFormValidate = [{\n          pic: '',\n          price: 0,\n          settle_price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: '',\n          code: '',\n          weight: 0,\n          volume: 0,\n          brokerage: 0,\n          brokerage_two: 0,\n          vip_price: 0,\n          virtual_list: [],\n          write_times: 0,\n          //核销次数\n          write_valid: 1,\n          //核销时效\n          days: 1\n        }];\n      }\n    },\n    //关闭淘宝弹窗并生成数据；\n    onClose: function onClose(data) {\n      this.modals = false;\n      this.infoData(data);\n    },\n    checkMove: function checkMove(evt) {\n      this.moveIndex = evt.draggedContext.index;\n    },\n    end: function end() {\n      this.moveIndex = '';\n    },\n    checkAllGroupChange: function checkAllGroupChange(data) {\n      this.checkAllGroup(data);\n    },\n    checkAllGroup: function checkAllGroup(data) {\n      if (this.formValidate.spec_type === 0) {\n        if (data.indexOf(0) > -1) {\n          this.columnsInstall = this.columns2.slice(1, 5).concat(this.member);\n        } else if (data.indexOf(1) > -1) {\n          this.columnsInstall = this.columns2.slice(1, 5).concat(this.rakeBack);\n        } else {\n          this.columnsInstall = this.columns2.slice(1, 5);\n        }\n\n        if (data.length === 2) {\n          this.columnsInstall = this.columns2.slice(1, 5).concat(this.rakeBack).concat(this.member);\n        }\n      } else {\n        if (data.indexOf(0) > -1) {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, 4).concat(this.member);\n        } else if (data.indexOf(1) > -1) {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, 4).concat(this.rakeBack);\n        } else {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, 4);\n        }\n\n        if (data.length === 2) {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, 4).concat(this.rakeBack).concat(this.member);\n        }\n      }\n    },\n    // 添加优惠券\n    addCoupon: function addCoupon() {\n      this.$refs.couponTemplates.isTemplate = true;\n      this.$refs.couponTemplates.tableList();\n    },\n    //对象数组去重；\n    unique: function unique(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.id) && res.set(arr.id, 1);\n      });\n    },\n    nameId: function nameId(id, names) {\n      this.formValidate.coupon_ids = id;\n      this.couponName = this.unique(names);\n    },\n    handleClose: function handleClose(name) {\n      var index = this.couponName.indexOf(name);\n      this.couponName.splice(index, 1);\n      var couponIds = this.formValidate.coupon_ids;\n      couponIds.splice(index, 1);\n      this.updateIds = couponIds;\n      this.updateName = this.couponName;\n    },\n    getStoreId: function getStoreId(data) {\n      this.storeModals = false;\n      var list = this.storesList.concat(data);\n      var uni = this.unique(list);\n      this.storesList = uni;\n    },\n    // 运费模板\n    getList: function getList() {\n      this.productGetTemplate();\n    },\n    // 添加运费模板\n    addTemp: function addTemp() {\n      this.$refs.templates.isTemplate = true;\n    },\n    // 删除视频；\n    delVideo: function delVideo() {\n      var that = this;\n      that.$set(that.formValidate, 'video_link', '');\n      that.$set(that, 'progress', 0);\n      that.videoIng = false;\n      that.upload.videoIng = false;\n      that.$refs.refid.value = '';\n    },\n    zh_uploadFile: function zh_uploadFile() {\n      if (this.seletVideo == 1) {\n        if (this.videoLink && this.$getFileType(this.videoLink) == 'video') {\n          this.formValidate.video_link = this.videoLink;\n        } else {\n          return this.$Message.error('请输入正确的视频链接');\n        }\n      } else {\n        this.$refs.refid.click();\n      }\n    },\n    zh_uploadFile_change: function zh_uploadFile_change(evfile) {\n      var that = this;\n      var suffix = evfile.target.files[0].name.substr(evfile.target.files[0].name.indexOf('.'));\n\n      if (suffix.indexOf('.mp4') === -1) {\n        return that.$Message.error('只能上传MP4文件');\n      }\n\n      var types = {\n        key: evfile.target.files[0].name,\n        contentType: evfile.target.files[0].type\n      };\n      productGetTempKeysApi(types).then(function (res) {\n        that.$videoCloud.videoUpload({\n          type: res.data.type,\n          evfile: evfile,\n          res: res,\n          uploading: function uploading(status, progress) {\n            that.upload.videoIng = status;\n\n            if (res.status == 200) {\n              that.progress = 100;\n            }\n          }\n        }).then(function (res) {\n          that.formValidate.video_link = res.url;\n          that.$Message.success('视频上传成功');\n          that.upload.videoIng = false;\n        }).catch(function (res) {\n          that.$Message.error(res);\n        });\n      }).catch(function (res) {\n        that.$Message.error(res.msg);\n      });\n    },\n    // 上一页；\n    upTab: function upTab() {\n      if (this.currentTab == 5 && this.formValidate.product_type != 0) {\n        this.currentTab = (Number(this.currentTab) - 2).toString();\n      } else {\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    // 下一页；\n    downTab: function downTab(name) {\n      var _this16 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this16.formValidate.is_show == 2 && !_this16.formValidate.auto_on_time) {\n            return _this16.$Message.warning('请填写定时上架时间');\n          }\n\n          if (_this16.off_show == 1 && !_this16.formValidate.auto_off_time) {\n            return _this16.$Message.warning('请填写定时下架时间');\n          }\n\n          if (_this16.currentTab == 4 && !_this16.formValidate.delivery_type.length) {\n            return _this16.$Message.warning('请选择配送方式');\n          }\n\n          if (_this16.currentTab == 3 && _this16.formValidate.product_type != 0) {\n            _this16.currentTab = (Number(_this16.currentTab) + 2).toString();\n          } else {\n            _this16.currentTab = (Number(_this16.currentTab) + 1).toString();\n          }\n        } else {\n          _this16.$Message.warning('请完善数据');\n        }\n      });\n    },\n    // 属性弹窗回调函数；\n    userSearchs: function userSearchs() {\n      this.productGetRule();\n    },\n    // 添加规则；\n    addRule: function addRule() {\n      this.$refs.addattr.modal = true;\n    },\n    // 批量设置分佣；\n    brokerageSetUp: function brokerageSetUp() {\n      var that = this;\n\n      if (that.formValidate.is_sub.indexOf(1) > -1) {\n        if (that.manyBrokerage <= 0 || that.manyBrokerageTwo <= 0) {\n          return that.$Message.error('请填写返佣金额后进行批量添加');\n        }\n      } else if (that.formValidate.is_sub.indexOf(0) > -1) {\n        if (that.manyVipPrice <= 0) {\n          return that.$Message.error('请填写会员价后进行批量添加');\n        }\n      }\n\n      if (this.formValidate.is_sub.length === 2) {\n        if (that.manyBrokerage <= 0 || that.manyBrokerageTwo <= 0 || that.manyVipPrice <= 0) {\n          return that.$Message.error('请填写完金额后进行批量添加');\n        }\n      }\n\n      var _iteratorNormalCompletion2 = true;\n      var _didIteratorError2 = false;\n      var _iteratorError2 = undefined;\n\n      try {\n        for (var _iterator2 = that.manyFormValidate[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n          var val = _step2.value;\n          this.$set(val, 'brokerage', that.manyBrokerage);\n          this.$set(val, 'brokerage_two', that.manyBrokerageTwo);\n          this.$set(val, 'vip_price', that.manyVipPrice);\n        } // let that = this;\n        // if (that.manyBrokerage <= 0 || that.manyBrokerageTwo <= 0) {\n        //     return that.$Message.error('请填写返佣金额在进行批量添加');\n        // } else {\n        //     for (let val of that.manyFormValidate) {\n        //         this.$set(val, 'brokerage', that.manyBrokerage);\n        //         this.$set(val, 'brokerage_two', that.manyBrokerageTwo);\n        //     }\n        // }\n\n      } catch (err) {\n        _didIteratorError2 = true;\n        _iteratorError2 = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion2 && _iterator2.return != null) {\n            _iterator2.return();\n          }\n        } finally {\n          if (_didIteratorError2) {\n            throw _iteratorError2;\n          }\n        }\n      }\n    },\n    // 批量设置会员价\n    vipPriceSetUp: function vipPriceSetUp() {\n      var that = this;\n\n      if (that.manyVipPrice <= 0) {\n        return that.$Message.error('请填写会员价在进行批量添加');\n      } else {\n        var _iteratorNormalCompletion3 = true;\n        var _didIteratorError3 = false;\n        var _iteratorError3 = undefined;\n\n        try {\n          for (var _iterator3 = that.manyFormValidate[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n            var val = _step3.value;\n            this.$set(val, 'vip_price', that.manyVipPrice);\n          }\n        } catch (err) {\n          _didIteratorError3 = true;\n          _iteratorError3 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion3 && _iterator3.return != null) {\n              _iterator3.return();\n            }\n          } finally {\n            if (_didIteratorError3) {\n              throw _iteratorError3;\n            }\n          }\n        }\n      }\n    },\n    batchDel: function batchDel() {\n      this.oneFormBatch = [{\n        attr: '全部',\n        pic: '',\n        price: 0,\n        settle_price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        bar_code: '',\n        code: '',\n        weight: 0,\n        volume: 0,\n        virtualList: [],\n        disk_info: ''\n      }];\n      this.activeAtter = [];\n      var _iteratorNormalCompletion4 = true;\n      var _didIteratorError4 = false;\n      var _iteratorError4 = undefined;\n\n      try {\n        for (var _iterator4 = this.manyFormValidate[Symbol.iterator](), _step4; !(_iteratorNormalCompletion4 = (_step4 = _iterator4.next()).done); _iteratorNormalCompletion4 = true) {\n          var val = _step4.value;\n          val.select = true;\n        }\n      } catch (err) {\n        _didIteratorError4 = true;\n        _iteratorError4 = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion4 && _iterator4.return != null) {\n            _iterator4.return();\n          }\n        } finally {\n          if (_didIteratorError4) {\n            throw _iteratorError4;\n          }\n        }\n      }\n    },\n    confirm: function confirm() {\n      var that = this;\n      that.createBnt = true;\n\n      if (that.formValidate.selectRule.trim().length <= 0) {\n        return that.$Message.error('请选择属性');\n      }\n\n      that.ruleList.forEach(function (item, index) {\n        if (item.rule_name === that.formValidate.selectRule) {\n          that.attrs = item.rule_value;\n        }\n      });\n    },\n    // 获取商品属性模板；\n    productGetRule: function productGetRule() {\n      var _this17 = this;\n\n      productGetRuleApi().then(function (res) {\n        _this17.ruleList = res.data;\n      });\n    },\n    // 获取运费模板；\n    productGetTemplate: function productGetTemplate() {\n      var _this18 = this;\n\n      productGetTemplateApi({\n        id: this.productId\n      }).then(function (res) {\n        _this18.templateList = res.data;\n      });\n    },\n    // 删除表格中的属性\n    delAttrTable: function delAttrTable(index) {\n      var _this19 = this;\n\n      var id = this.productId;\n\n      if (id) {\n        checkActivityApi(id).then(function (res) {\n          _this19.manyFormValidate.splice(index, 1);\n\n          _this19.$Message.success(res.msg);\n        }).catch(function (res) {\n          _this19.$Message.error(res.msg);\n        });\n      } else {\n        this.manyFormValidate.splice(index, 1);\n      }\n    },\n    // 批量添加\n    batchAdd: function batchAdd() {\n      var _iteratorNormalCompletion5 = true;\n      var _didIteratorError5 = false;\n      var _iteratorError5 = undefined;\n\n      try {\n        for (var _iterator5 = this.manyFormValidate[Symbol.iterator](), _step5; !(_iteratorNormalCompletion5 = (_step5 = _iterator5.next()).done); _iteratorNormalCompletion5 = true) {\n          var val = _step5.value;\n\n          //this.manyEmpty(val);\n          if (val.select) {\n            if (this.oneFormBatch[0].pic) {\n              this.$set(val, 'pic', this.oneFormBatch[0].pic);\n            }\n\n            if (this.oneFormBatch[0].price > 0) {\n              this.$set(val, 'price', this.oneFormBatch[0].price);\n            }\n\n            if (this.oneFormBatch[0].settle_price > 0 && this.merchantType == 2) {\n              this.$set(val, \"settle_price\", this.oneFormBatch[0].settle_price);\n            }\n\n            if (this.oneFormBatch[0].cost > 0) {\n              this.$set(val, 'cost', this.oneFormBatch[0].cost);\n            }\n\n            if (this.oneFormBatch[0].ot_price > 0) {\n              this.$set(val, 'ot_price', this.oneFormBatch[0].ot_price);\n            }\n\n            if (this.oneFormBatch[0].stock > 0) {\n              this.$set(val, 'stock', this.oneFormBatch[0].stock);\n            }\n\n            if (this.oneFormBatch[0].bar_code !== '') {\n              this.$set(val, 'bar_code', this.oneFormBatch[0].bar_code);\n            }\n\n            if (this.oneFormBatch[0].code !== '') {\n              this.$set(val, 'code', this.oneFormBatch[0].code);\n            }\n\n            if (this.oneFormBatch[0].weight > 0) {\n              this.$set(val, 'weight', this.oneFormBatch[0].weight);\n            }\n\n            if (this.oneFormBatch[0].volume > 0) {\n              this.$set(val, 'volume', this.oneFormBatch[0].volume);\n            }\n\n            if (this.formValidate.product_type == 1) {\n              if (this.oneFormBatch[0].virtual_list && this.oneFormBatch[0].virtual_list.length) {\n                this.$set(val, 'virtual_list', this.oneFormBatch[0].virtual_list);\n              } else if (this.oneFormBatch[0].disk_info) {\n                this.$refs.addCarMy.cartMyType = 1;\n                this.$set(val, 'disk_info', this.oneFormBatch[0].disk_info);\n              }\n            }\n          }\n        }\n      } catch (err) {\n        _didIteratorError5 = true;\n        _iteratorError5 = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion5 && _iterator5.return != null) {\n            _iterator5.return();\n          }\n        } finally {\n          if (_didIteratorError5) {\n            throw _iteratorError5;\n          }\n        }\n      }\n    },\n    // 添加按钮\n    addBtn: function addBtn() {\n      var _this20 = this;\n\n      var id = this.productId;\n\n      if (id) {\n        checkActivityApi(id).then(function (res) {\n          _this20.clearAttr();\n\n          _this20.createBnt = false;\n          _this20.showIput = true;\n        }).catch(function (res) {\n          _this20.$Message.error(res.msg);\n        });\n      }\n    },\n    addmanyData: function addmanyData(data) {\n      data.forEach(function (item) {\n        item.select = true;\n      });\n      this.manyFormValidate = data;\n    },\n    // 立即生成\n    generate: function generate(type) {\n      var _this21 = this;\n\n      generateAttrApi({\n        attrs: this.attrs,\n        product_type: this.formValidate.product_type\n      }, this.formValidate.id, type).then(function (res) {\n        var info = res.data.info,\n            header1 = JSON.parse(JSON.stringify(info.header));\n\n        if (_this21.productId !== '0') {\n          _this21.addmanyData(info.value);\n        }\n\n        _this21.formValidate.header = header1;\n        _this21.attrData = res.data.info.attr;\n        var header = info.header;\n        header.pop();\n        _this21.columnsInstalM = info.header;\n\n        _this21.checkAllGroup(_this21.formValidate.is_sub);\n\n        if (!_this21.productId && _this21.formValidate.spec_type === 1) {\n          _this21.manyFormValidate.map(function (item) {\n            item.pic = _this21.formValidate.slider_image[0];\n          });\n\n          _this21.oneFormBatch[0].pic = _this21.formValidate.slider_image[0];\n        } else if (_this21.productId) {\n          _this21.manyFormValidate.map(function (item) {\n            if (!item.pic) {\n              item.pic = _this21.formValidate.slider_image[0];\n            }\n          });\n\n          _this21.oneFormBatch[0].pic = _this21.formValidate.slider_image[0];\n        }\n\n        _this21.getAttr();\n      }).catch(function (res) {\n        _this21.$Message.error(res.msg);\n      });\n    },\n    // 取消\n    offAttrName: function offAttrName() {\n      this.showIput = false;\n      this.createBnt = true;\n    },\n    clearAttr: function clearAttr() {\n      this.formDynamic.attrsName = '';\n      this.formDynamic.attrsVal = '';\n    },\n    // 删除规格\n    handleRemoveRole: function handleRemoveRole(index) {\n      this.attrs.splice(index, 1);\n      this.manyFormValidate.splice(index, 1);\n    },\n    // 删除属性\n    handleRemove2: function handleRemove2(item, index) {\n      item.splice(index, 1);\n    },\n    // 添加规则名称\n    createAttrName: function createAttrName() {\n      if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {\n        var data = {\n          value: this.formDynamic.attrsName,\n          detail: [this.formDynamic.attrsVal]\n        };\n        this.attrs.push(data);\n        var hash = {};\n        this.attrs = this.attrs.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next.value] ? '' : hash[next.value] = true && item.push(next);\n          return item;\n        }, []);\n        this.clearAttr();\n        this.showIput = false;\n        this.createBnt = true;\n      } else {\n        this.$Message.warning('请添加完整的规格！');\n      }\n    },\n    // 添加属性\n    createAttr: function createAttr(num, idx) {\n      if (num) {\n        this.attrs[idx].detail.push(num);\n        var hash = {};\n        this.attrs[idx].detail = this.attrs[idx].detail.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next] ? '' : hash[next] = true && item.push(next);\n          return item;\n        }, []);\n      } else {\n        this.$Message.warning('请添加属性');\n      }\n    },\n    // 商品分类；\n    goodsCategory: function goodsCategory() {\n      var _this22 = this;\n\n      cascaderListApi(1).then(function (res) {\n        _this22.treeSelect = res.data;\n      }).catch(function (res) {\n        _this22.$Message.error(res.msg);\n      });\n    },\n    //视视上传类型\n    changeVideo: function changeVideo(e) {\n      this.formValidate.video_link = '';\n      this.videoLink = '';\n    },\n    // 改变规格\n    changeSpec: function changeSpec() {\n      var _this23 = this;\n\n      this.formValidate.is_sub = [];\n      var id = this.productId;\n\n      if (id) {\n        checkActivityApi(id).then(function (res) {}).catch(function (res) {\n          _this23.formValidate.spec_type = _this23.spec_type;\n\n          _this23.$Message.error(res.msg);\n        });\n      }\n    },\n    // 详情\n    getInfo: function getInfo() {\n      var _this24 = this;\n\n      var that = this;\n      that.spinShow = true;\n      productInfoApi(that.productId).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          var data, obj, found;\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  data = res.data.productInfo;\n                  _this24.merchantType = parseInt(data.type);\n\n                  if (_this24.merchantType == 2) {\n                    obj = {\n                      title: \"结算价\",\n                      slot: \"settle_price\",\n                      align: \"center\",\n                      minWidth: 95\n                    };\n                    found = _this24.columns2.some(function (element) {\n                      return element.slot === obj.slot;\n                    });\n\n                    if (!found) {\n                      _this24.columns2.splice(3, 0, obj);\n                    }\n                  }\n\n                  _this24.infoData(data); // let cate_id = data.cate_id.map(Number);\n                  // let label_id = data.label_id.map(Number);\n                  // this.attrs = data.items || [];\n                  // let ids = [];\n                  // data.coupons.map((item) => {\n                  //   ids.push(item.id);\n                  // });\n                  // that.formValidate = data;\n                  // that.couponName = data.coupons;\n                  // that.formValidate.coupon_ids = ids;\n                  // that.updateIds = ids;\n                  // that.updateName = data.coupons;\n                  // that.formValidate.cate_id = cate_id;\n                  // that.formValidate.label_id = label_id;\n                  // that.oneFormValidate = [data.attr];\n                  // that.formValidate.header = [];\n                  // that.manyFormValidate = data.attrs;\n                  // that.generate(0);\n                  // that.spec_type = data.spec_type;\n                  // if (data.spec_type === 0) {\n                  //   that.manyFormValidate = [];\n                  // } else {\n                  //   that.createBnt = true;\n                  //   that.oneFormValidate = [\n                  //     {\n                  //       pic: data.image,\n                  //       price: 0,\n                  //       cost: 0,\n                  //       ot_price: 0,\n                  //       stock: 0,\n                  //       bar_code: \"\",\n                  //       weight: 0,\n                  //       volume: 0,\n                  //       brokerage: 0,\n                  //       brokerage_two: 0,\n                  //       vip_price: 0,\n                  //     },\n                  //   ];\n                  // }\n\n\n                  _this24.spinShow = false;\n\n                case 5:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this24.spinShow = false;\n\n        _this24.$Message.error(res.msg);\n      });\n    },\n    // tab切换\n    onhangeTab: function onhangeTab(name) {\n      this.currentTab = name;\n    },\n    handleRemove: function handleRemove(i) {\n      this.images.splice(i, 1);\n      this.formValidate.slider_image.splice(i, 1);\n      this.oneFormValidate[0].pic = this.formValidate.slider_image[0];\n    },\n    // 关闭图片上传模态框\n    changeCancel: function changeCancel(msg) {\n      this.modalPic = false;\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, picTit, index) {\n      this.modalPic = true;\n      this.isChoice = tit === 'dan' ? '单选' : '多选';\n      this.picTit = picTit;\n      this.tableIndex = index;\n    },\n    // 获取单张图片信息\n    getPic: function getPic(pc) {\n      switch (this.picTit) {\n        case 'danFrom':\n          this.formValidate.image = pc.att_dir;\n\n          if (!this.productId) {\n            if (this.formValidate.spec_type === 0) {\n              this.oneFormValidate[0].pic = pc.att_dir;\n            } else {\n              this.manyFormValidate.map(function (item) {\n                item.pic = pc.att_dir;\n              });\n              this.oneFormBatch[0].pic = pc.att_dir;\n            }\n          }\n\n          break;\n\n        case 'danTable':\n          this.oneFormValidate[this.tableIndex].pic = pc.att_dir;\n          break;\n\n        case 'duopi':\n          this.oneFormBatch[this.tableIndex].pic = pc.att_dir;\n          break;\n\n        case 'recommend_image':\n          this.formValidate.recommend_image = pc.att_dir;\n          break;\n\n        default:\n          this.manyFormValidate[this.tableIndex].pic = pc.att_dir;\n      }\n\n      this.modalPic = false;\n    },\n    // 获取多张图信息\n    getPicD: function getPicD(pc) {\n      var _this25 = this;\n\n      this.images = pc;\n      this.images.map(function (item) {\n        _this25.formValidate.slider_image.push(item.att_dir);\n\n        _this25.formValidate.slider_image = _this25.formValidate.slider_image.splice(0, 10);\n      });\n      this.oneFormValidate[0].pic = this.formValidate.slider_image[0];\n      this.modalPic = false;\n    },\n    // 提交\n    handleSubmit: function handleSubmit(name) {\n      var _this26 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (!_this26.formValidate.store_name.trim()) {\n            return _this26.$Message.warning('基础信息-商品名称不能为空');\n          }\n\n          if (_this26.formValidate.is_show == 2 && !_this26.formValidate.auto_on_time) {\n            return _this26.$Message.warning('基础信息-定时上架时间不能为空');\n          }\n\n          if (_this26.off_show == 1 && !_this26.formValidate.auto_off_time) {\n            return _this26.$Message.warning('基础信息-定时下架时间不能为空');\n          }\n\n          if (_this26.formValidate.product_type == 4 && !_this26.oneFormValidate[0].write_times) {\n            return _this26.$Message.warning(\"规格库存-核销次数必须大于0\");\n          }\n\n          if (_this26.formValidate.product_type == 4 && _this26.oneFormValidate[0].write_valid == 2 && !_this26.oneFormValidate[0].days) {\n            return _this26.$Message.warning(\"规格库存-有效天数必须大于0\");\n          }\n\n          if (_this26.formValidate.product_type == 4 && _this26.oneFormValidate[0].write_valid == 3 && !_this26.oneFormValidate[0].section_time.length) {\n            return _this26.$Message.warning(\"规格库存-请输入固定有效期\");\n          }\n\n          if (_this26.formValidate.freight == 2 && _this26.formValidate.product_type == 0 && _this26.formValidate.postage <= 0) {\n            return _this26.$Message.warning('物流设置-固定邮费不能为0');\n          }\n\n          if (_this26.formValidate.freight == 3 && _this26.formValidate.product_type == 0 && !_this26.formValidate.temp_id) {\n            return _this26.$Message.warning('物流设置-运费模板不能为空');\n          }\n\n          if (_this26.formValidate.product_type == 0 && _this26.formValidate.is_presale_product && !_this26.formValidate.presale_time[0]) {\n            return _this26.$Message.warning('营销设置-预售时间不能为空');\n          } // for (let i = 0; i < this.formValidate.custom_form.length; i++) {\n          //   const element = this.formValidate.custom_form[i];\n          //   if (!element.title) {\n          //     return this.$Message.warning('其他设置-留言标题不能为空');\n          //   }\n          // }\n\n\n          if (_this26.customBtn && _this26.formValidate.system_form_id == 0) {\n            return _this26.$Message.warning('其他设置-请选择自定义表单模板');\n          }\n\n          var storeId = [];\n\n          _this26.storesList.forEach(function (item) {\n            storeId.push(item.id);\n          });\n\n          if (_this26.formValidate.applicable_type == 2 && !storeId.length) {\n            return _this26.$Message.warning('适用门店-请选择适用门店');\n          }\n\n          _this26.formValidate.applicable_store_id = storeId;\n          _this26.formValidate.type = _this26.type;\n\n          if (_this26.formValidate.spec_type === 0) {\n            _this26.formValidate.attrs = _this26.oneFormValidate;\n            _this26.formValidate.header = [];\n            _this26.formValidate.items = [];\n          } else {\n            _this26.formValidate.items = _this26.attrs;\n            _this26.formValidate.attrs = _this26.manyFormValidate;\n          }\n\n          if (_this26.formValidate.spec_type === 1 && _this26.manyFormValidate.length === 0) {\n            return _this26.$Message.warning('规格库存-请点击生成多规格'); // return this.$Message.warning('请点击生成规格！');\n          }\n\n          var item = _this26.formValidate.attrs;\n\n          if (_this26.formValidate.is_sub.indexOf(1) != -1) {\n            for (var i = 0; i < item.length; i++) {\n              if (item[i].brokerage === null || item[i].brokerage_two === null) {\n                return _this26.$Message.warning('营销设置- 一二级返佣不能为空');\n              }\n            }\n          }\n\n          if (_this26.formValidate.is_sub.indexOf(0) != -1) {\n            for (var _i2 = 0; _i2 < item.length; _i2++) {\n              if (item[_i2].vip_price === null) {\n                return _this26.$Message.warning('营销设置-会员价不能为空');\n              }\n\n              if (item[_i2].vip_price === 0) {\n                return _this26.$Message.warning('营销设置-会员价不能为0');\n              }\n            }\n          }\n\n          if (_this26.formValidate.is_sub.length === 2) {\n            for (var _i3 = 0; _i3 < item.length; _i3++) {\n              if (item[_i3].brokerage === null || item[_i3].brokerage_two === null || item[_i3].vip_price === null) {\n                return _this26.$Message.error('营销设置- 一二级返佣和会员价不能为空');\n              }\n            }\n          }\n\n          for (var _i4 = 0; _i4 < _this26.specsList.length; _i4++) {\n            var data = _this26.specsList[_i4];\n\n            if (!data.name.trim()) {\n              return _this26.$Message.error('请输入参数名称');\n            }\n\n            if (!data.value.trim()) {\n              return _this26.$Message.error('请输入参数值');\n            }\n          }\n\n          if (!_this26.formValidate.product_type) {\n            _this26.formValidate.is_support_refund = 1;\n          }\n\n          _this26.openSubimit = false;\n          _this26.formValidate.description = _this26.formatRichText(_this26.content);\n          var goodsId = [];\n\n          _this26.goodsData.forEach(function (item) {\n            goodsId.push(item.product_id);\n          });\n\n          _this26.formValidate.recommend_list = goodsId; // 用户标签\n\n          var activeIds = [];\n\n          _this26.dataLabel.forEach(function (item) {\n            activeIds.push(item.id);\n          });\n\n          _this26.formValidate.label_id = activeIds; // 商品标签\n\n          var storeActiveIds = [];\n\n          _this26.storeDataLabel.forEach(function (item) {\n            storeActiveIds.push(item.id);\n          });\n\n          _this26.formValidate.store_label_id = storeActiveIds; // 商品参数\n\n          _this26.formValidate.specs = _this26.specsList;\n\n          if (_this26.formValidate.product_type == 4) {\n            _this26.formValidate.delivery_type = 2;\n          }\n\n          productAddApi(_this26.formValidate).then(\n          /*#__PURE__*/\n          function () {\n            var _ref3 = _asyncToGenerator(\n            /*#__PURE__*/\n            _regeneratorRuntime.mark(function _callee3(res) {\n              return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n                while (1) {\n                  switch (_context3.prev = _context3.next) {\n                    case 0:\n                      _this26.openSubimit = true;\n\n                      _this26.$Message.success(res.msg);\n\n                      if (_this26.productId === '0') {\n                        cacheDelete().catch(function (err) {\n                          _this26.$Message.error(err.msg);\n                        });\n                      } // this.$refs[name].resetFields();\n                      // setTimeout(() => {\n                      //   this.$router.push({ path: '/product/product_list' });\n                      // }, 500);\n\n\n                      _this26.$emit('saved');\n\n                    case 4:\n                    case \"end\":\n                      return _context3.stop();\n                  }\n                }\n              }, _callee3);\n            }));\n\n            return function (_x3) {\n              return _ref3.apply(this, arguments);\n            };\n          }()).catch(function (res) {\n            _this26.openSubimit = false;\n\n            _this26.$Message.error(res.msg);\n          });\n        } else {\n          if (!_this26.formValidate.store_name) {\n            return _this26.$Message.warning('基础信息-商品名称不能为空');\n          } else if (!_this26.formValidate.cate_id.length) {\n            return _this26.$Message.warning('基础信息-商品分类不能为空');\n          } else if (!_this26.formValidate.unit_name) {\n            return _this26.$Message.warning('基础信息-商品单位不能为空');\n          } else if (!_this26.formValidate.slider_image.length) {\n            return _this26.$Message.warning('基础信息-商品轮播图不能为空');\n          } //    if(!this.formValidate.store_name || !this.formValidate.cate_id || !this.formValidate.keyword\n          //    || !this.formValidate.unit_name || !this.formValidate.store_info\n          //        || !this.formValidate.image || !this.formValidate.slider_image){\n          //        this.$Message.warning(\"请填写完整商品信息！\");\n          //    }\n\n        }\n      });\n    },\n    changeTemplate: function changeTemplate(msg) {\n      this.template = msg;\n    },\n    // 表单验证\n    validate: function validate(prop, status, error) {\n      if (status === false) {\n        this.$Message.warning(error);\n      }\n    },\n    // 移动\n    handleDragStart: function handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd: function handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    handleDragOver: function handleDragOver(e) {\n      e.dataTransfer.dropEffect = 'move';\n    },\n    handleDragEnter: function handleDragEnter(e, item) {\n      e.dataTransfer.effectAllowed = 'move';\n\n      if (item === this.dragging) {\n        return;\n      }\n\n      var newItems = _toConsumableArray(this.formValidate.slider_image);\n\n      var src = newItems.indexOf(this.dragging);\n      var dst = newItems.indexOf(item);\n      newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n      this.formValidate.slider_image = newItems;\n    },\n    // 添加自定义弹窗\n    addCustomDialog: function addCustomDialog(editorId) {\n      window.UE.registerUI('test-dialog', function (editor, uiName) {\n        // 创建 dialog\n        var dialog = new window.UE.ui.Dialog({\n          iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n          editor: editor,\n          name: uiName,\n          title: '上传图片',\n          cssRules: 'width:1200px;height:500px;padding:20px;'\n        });\n        this.dialog = dialog;\n        var btn = new window.UE.ui.Button({\n          name: 'dialog-button',\n          title: '上传图片',\n          cssRules: \"background-image: url(https://cdn.oss.9gt.net/prov1.1/1/icons.png);background-position: -726px -77px;\",\n          onclick: function onclick() {\n            // 渲染dialog\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 37);\n      window.UE.registerUI('video-dialog', function (editor, uiName) {\n        var dialog = new window.UE.ui.Dialog({\n          iframeUrl: '/admin/widget.video/index.html?fodder=video',\n          editor: editor,\n          name: uiName,\n          title: '上传视频',\n          cssRules: 'width:1000px;height:500px;padding:20px;'\n        });\n        this.dialog = dialog;\n        var btn = new window.UE.ui.Button({\n          name: 'video-button',\n          title: '上传视频',\n          cssRules: \"background-image: url(../../../assets/images/icons.png);background-position: -320px -20px;\",\n          onclick: function onclick() {\n            // 渲染dialog\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 38);\n    },\n    formatRichText: function formatRichText(html) {\n      var newContent = html.replace(/<img[^>]*>/gi, function (match, capture) {\n        match = match.replace(/style=\"[^\"]+\"/gi, '').replace(/style='[^']+'/gi, '');\n        match = match.replace(/width=\"[^\"]+\"/gi, '').replace(/width='[^']+'/gi, '');\n        match = match.replace(/height=\"[^\"]+\"/gi, '').replace(/height='[^']+'/gi, '');\n        return match;\n      });\n      newContent = newContent.replace(/style=\"[^\"]+\"/gi, function (match, capture) {\n        match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi, 'max-width:100%;');\n        return match;\n      }); // newContent = newContent.replace(/<br[^>]*\\/>/gi, '');\n\n      newContent = newContent.replace(/\\<img/gi, '<img style=\"max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;\"');\n      return newContent;\n    },\n    drawerChange: function drawerChange(e) {\n      if (e) {\n        this.currentTab = '1';\n        this.type = 0;\n        this.batchDel();\n        this.getInfo();\n        this.getReplyList();\n        this.goodsCategory();\n        this.productGetRule();\n        this.productGetTemplate();\n        this.getBrandList();\n        this.getAllUnit();\n        this.uploadType();\n        this.getProductAllEnsure();\n        this.getProductAllSpecs();\n        this.allFormList();\n      } else {\n        this.$emit('update:visible', false);\n      }\n    },\n    // 查看评论列表\n    seeReply: function seeReply(row) {\n      this.$refs.replyList.modals = true;\n      this.$refs.replyList.getList(row.id);\n    },\n    // 回复评论\n    reply: function reply(row) {\n      this.replyModal = true;\n      this.rows = row;\n      this.replyForm.content = row.replyComment ? row.replyComment.content : '';\n    },\n    // 删除评论\n    delReply: function delReply(row, tit, num) {\n      var _this27 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"product/reply/\".concat(row.id),\n        method: 'DELETE',\n        ids: ''\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this27.$Message.success(res.msg);\n\n        _this27.replyData.splice(num, 1);\n      }).catch(function (res) {\n        _this27.$Message.error(res.msg);\n      });\n    },\n    cancels: function cancels() {\n      this.replyModal = false;\n      this.$refs['replyForm'].resetFields();\n    },\n    oks: function oks() {\n      var _this28 = this;\n\n      this.replyModal = true;\n      this.$refs['replyForm'].validate(function (valid) {\n        if (valid) {\n          setReplyApi(_this28.replyForm, _this28.rows.id).then(\n          /*#__PURE__*/\n          function () {\n            var _ref4 = _asyncToGenerator(\n            /*#__PURE__*/\n            _regeneratorRuntime.mark(function _callee4(res) {\n              return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n                while (1) {\n                  switch (_context4.prev = _context4.next) {\n                    case 0:\n                      _this28.$Message.success(res.msg);\n\n                      _this28.replyModal = false;\n\n                      _this28.$refs['replyForm'].resetFields();\n\n                      _this28.getReplyList();\n\n                    case 4:\n                    case \"end\":\n                      return _context4.stop();\n                  }\n                }\n              }, _callee4);\n            }));\n\n            return function (_x4) {\n              return _ref4.apply(this, arguments);\n            };\n          }()).catch(function (res) {\n            _this28.$Message.error(res.msg);\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    // 商品评论\n    getReplyList: function getReplyList() {\n      var _this29 = this;\n\n      this.replyLoading = true; // this.formValidate.is_reply = this.formValidate.is_reply || \"\";\n      // this.formValidate.store_name = this.formValidate.store_name || \"\";\n\n      this.replyValidate.product_id = this.productId;\n      replyListApi(this.replyValidate).then(\n      /*#__PURE__*/\n      function () {\n        var _ref5 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee5(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n            while (1) {\n              switch (_context5.prev = _context5.next) {\n                case 0:\n                  data = res.data;\n                  _this29.replyData = data.list;\n                  _this29.total = res.data.count;\n                  _this29.replyLoading = false;\n\n                case 4:\n                case \"end\":\n                  return _context5.stop();\n              }\n            }\n          }, _callee5);\n        }));\n\n        return function (_x5) {\n          return _ref5.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this29.replyLoading = false;\n\n        _this29.$Message.error(res.msg);\n      });\n    }\n  })\n};", null]}