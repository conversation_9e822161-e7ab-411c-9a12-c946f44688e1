{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\agent\\handle\\promotersList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\agent\\handle\\promotersList.vue", "mtime": 1682663004000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport { stairListApi } from '@/api/agent';\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: 'promotersList',\n  data: function data() {\n    return {\n      modals: false,\n      options: timeOptions,\n      fromList: {\n        title: '选择时间',\n        custom: true,\n        fromTxt: [{\n          text: '全部',\n          val: ''\n        }, {\n          text: '今天',\n          val: 'today'\n        }, {\n          text: '昨天',\n          val: 'yesterday'\n        }, {\n          text: '最近7天',\n          val: 'lately7'\n        }, {\n          text: '最近30天',\n          val: 'lately30'\n        }, {\n          text: '本月',\n          val: 'month'\n        }, {\n          text: '本年',\n          val: 'year'\n        }],\n        fromTxt2: [{\n          text: '全部',\n          val: 0\n        }, {\n          text: '一级推广人',\n          val: 1\n        }, {\n          text: '二级推广人',\n          val: 2\n        }]\n      },\n      formValidate: {\n        limit: 15,\n        page: 1,\n        nickname: '',\n        data: '',\n        type: '',\n        order_id: '',\n        uid: 0\n      },\n      loading: false,\n      tabList: [],\n      total: 0,\n      timeVal: [],\n      columns4: [],\n      listTitle: ''\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 100;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  methods: {\n    onCancel: function onCancel() {\n      this.formValidate = {\n        limit: 15,\n        page: 1,\n        nickname: '',\n        data: '',\n        type: '',\n        order_id: '',\n        uid: 0\n      };\n      this.timeVal = [];\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join('-') : \"\";\n    },\n    // 列表\n    getList: function getList(row, tit) {\n      var _this = this;\n\n      this.listTitle = tit;\n      this.rowsList = row;\n      this.loading = true;\n      var url = '';\n\n      if (this.listTitle === 'man') {\n        url = 'agent/stair';\n      } else {\n        url = 'agent/stair/order';\n      }\n\n      this.formValidate.uid = row.uid;\n      stairListApi(url, this.formValidate).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this.tabList = data.list;\n                  _this.total = data.count;\n\n                  if (_this.listTitle === 'man') {\n                    _this.columns4 = [{\n                      title: 'UID',\n                      minWidth: 80,\n                      key: 'uid'\n                    }, {\n                      title: '头像',\n                      key: 'avatar',\n                      minWidth: 80,\n                      render: function render(h, params) {\n                        return h('viewer', [h('div', {\n                          style: {\n                            width: '36px',\n                            height: '36px',\n                            borderRadius: '4px',\n                            cursor: 'pointer'\n                          }\n                        }, [h('img', {\n                          attrs: {\n                            src: params.row.avatar ? params.row.avatar : require('../../../assets/images/moren.jpg')\n                          },\n                          style: {\n                            width: '100%',\n                            height: '100%'\n                          }\n                        })])]);\n                      }\n                    }, {\n                      title: '用户信息',\n                      key: 'nickname',\n                      minWidth: 120\n                    }, {\n                      title: '是否推广员',\n                      key: 'promoter_name',\n                      minWidth: 100\n                    }, {\n                      title: '推广人数',\n                      key: 'spread_count',\n                      sortable: true,\n                      minWidth: 90\n                    }, {\n                      title: '订单数',\n                      key: 'order_count',\n                      sortable: true,\n                      minWidth: 90\n                    }, {\n                      title: '关注时间',\n                      key: 'add_time',\n                      sortable: true,\n                      minWidth: 130\n                    }];\n                  } else {\n                    _this.columns4 = [{\n                      title: '订单ID',\n                      key: 'order_id'\n                    }, {\n                      title: '用户信息',\n                      key: 'user_info'\n                    }, {\n                      title: '时间',\n                      key: '_add_time'\n                    }, {\n                      title: '返佣金额',\n                      key: 'brokerage_price',\n                      render: function render(h, params) {\n                        return h('viewer', [h('span', params.row.brokerage_price || 0)]);\n                      }\n                    }];\n                  }\n\n                  _this.loading = false;\n\n                case 5:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this.loading = false;\n        _this.tabList = [];\n\n        _this.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange() {\n      this.formValidate.page = 1;\n      this.getList(this.rowsList, this.listTitle);\n    },\n    // 搜索\n    userSearchs: function userSearchs() {\n      this.formValidate.page = 1;\n      this.getList(this.rowsList, this.listTitle);\n    }\n  }\n};", null]}