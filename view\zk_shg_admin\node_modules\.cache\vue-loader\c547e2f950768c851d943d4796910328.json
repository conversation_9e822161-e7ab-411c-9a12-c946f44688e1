{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue?vue&type=style&index=0&id=20b14438&scoped=true&lang=less&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1725352507996}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/deep/.ivu-modal-body {\n  max-height: 623px;\n  overflow: auto;\n}\n.list {\n  padding-left: 8px;\n  .item {\n    margin-right: 11px;\n    text-align: center;\n    cursor: pointer;\n\n    &.on {\n      .pictrue {\n        border-color: #1890ff;\n      }\n    }\n\n    .name {\n      color: #3d3d3d;\n      font-size: 14px;\n      margin: 12px 0;\n    }\n\n    &:nth-of-type(3n) {\n      margin-right: 0;\n    }\n    .pictrue {\n      width: 273px;\n      height: 218px;\n      background: #f5f5f5;\n      border-radius: 4px;\n      border: 1px solid #dddddd;\n      position: relative;\n\n      img {\n        display: block;\n      }\n\n      .iconfont {\n        position: absolute;\n        right: -1px;\n        bottom: -4px;\n        color: #1890ff;\n      }\n    }\n  }\n}\n.button-style {\n  padding: 0 15px;\n  margin-bottom: 20px;\n\n  .title-tips {\n    margin-right: 14px;\n    color: #999;\n    font-size: 12px;\n    width: 82px;\n  }\n\n  .style-box {\n    .bnt {\n      width: 94px;\n      height: 32px;\n      background: #1890ff;\n      border-radius: 4px;\n      text-align: center;\n      line-height: 32px;\n      color: #fff;\n      font-size: 12px;\n      cursor: pointer;\n    }\n    .name {\n      color: #999999;\n      font-size: 12px;\n      margin-left: 12px;\n    }\n  }\n}\n", null]}