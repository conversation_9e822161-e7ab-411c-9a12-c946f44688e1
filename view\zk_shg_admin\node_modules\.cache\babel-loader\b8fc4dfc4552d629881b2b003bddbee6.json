{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAdd\\taoBao.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAdd\\taoBao.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { crawlFromApi, copyConfigApi } from '@/api/product';\nimport Setting from '@/setting';\nexport default {\n  name: 'taoB<PERSON>',\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      soure_link: '',\n      spinShow: false,\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      grid2: {\n        xl: 12,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      copyConfig: {\n        'copy_type': '',\n        'copy_num': 0\n      },\n      artFrom: {\n        type: 'taobao',\n        url: ''\n      }\n    };\n  },\n  computed: {},\n  created: function created() {},\n  mounted: function mounted() {\n    this.getCopyConfig();\n  },\n  methods: {\n    mealPay: function mealPay(val) {\n      this.$router.push({\n        path: this.roterPre + '/setting/sms/sms_pay/index',\n        query: {\n          type: val\n        }\n      });\n    },\n    getCopyConfig: function getCopyConfig() {\n      var _this = this;\n\n      copyConfigApi().then(function (res) {\n        _this.copyConfig.copy_type = res.data.copy_type;\n        _this.copyConfig.copy_num = res.data.copy_num;\n      });\n    },\n    // 生成表单\n    add: function add() {\n      var _this2 = this;\n\n      if (this.soure_link) {\n        var reg = /(http|ftp|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/;\n\n        if (!reg.test(this.soure_link)) {\n          return this.$Message.warning('请输入以http开头的地址！');\n        }\n\n        this.spinShow = true;\n        this.artFrom.url = this.soure_link;\n        crawlFromApi(this.artFrom).then(function (res) {\n          var info = res.data.productInfo;\n\n          _this2.$emit('on-close', info);\n\n          _this2.spinShow = false;\n        }).catch(function (res) {\n          _this2.spinShow = false;\n\n          _this2.$Message.error(res.msg);\n        });\n      } else {\n        this.$Message.warning('请输入链接地址！');\n      }\n    }\n  }\n};", null]}