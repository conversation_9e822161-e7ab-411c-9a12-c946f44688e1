{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\welcome\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\welcome\\index.vue", "mtime": 1685002242000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { getWelcomeList } from \"@/api/work\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"\",\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      formInline: {},\n      columns1: [{\n        title: \"欢迎语内容\",\n        key: \"content\",\n        minWidth: 80\n      }, // {\n      //   title: \"使用成员\",\n      //   key: \"userids\",\n      //   minWidth: 100,\n      //   align: 'center'\n      // },\n      {\n        title: \"类型\",\n        slot: \"type\",\n        minWidth: 80\n      }, {\n        title: \"排序\",\n        key: \"sort\",\n        minWidth: 80\n      }, {\n        title: \"创建时间\",\n        key: \"create_time\",\n        minWidth: 130\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        // fixed: \"right\",\n        minWidth: 130\n      }],\n      tableData: [],\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      tableFrom: {\n        // start_status: \"\",\n        // status: \"\",\n        // store_name: \"\",\n        page: 1,\n        limit: 15\n      },\n      total: 0\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    }\n  }),\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    //编辑\n    editData: function editData(row, index) {\n      this.$router.push({\n        path: this.roterPre + \"/work/addWelcome/\" + row.id\n      });\n    },\n    //删除\n    delData: function delData(row, index) {\n      var _this = this;\n\n      var delfromData = {\n        title: '删除欢迎语',\n        num: index,\n        url: \"/work/welcome/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this.$Message.success(res.msg);\n\n        _this.tableData.list.splice(index, 1);\n\n        if (!_this.tableData.list.length) {\n          _this.tableFrom.page = _this.tableFrom.page == 1 ? 1 : _this.tableFrom.page - 1;\n        }\n\n        _this.getList();\n      }).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      getWelcomeList(this.tableFrom).then(function (res) {\n        _this2.tableData = res.data;\n        _this2.loading = false;\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n\n        _this2.loading = false;\n      });\n    },\n    //分页\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    }\n  }\n};", null]}