{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_wechat_live.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_wechat_live.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapMutations } from 'vuex';\nexport default {\n  name: 'c_wechat_live',\n  componentsName: 'wechat_live',\n  cname: '小程序直播',\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  data: function data() {\n    return {\n      // 组件参数配置\n      option: {\n        submitBtn: false\n      },\n      configObj: {},\n      // 配置对象\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }] // 当前页面组件\n\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      // debugger;\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          var tempArr = [{\n            components: toolCom.c_title,\n            configNme: 'titleLeft'\n          }, {\n            components: toolCom.c_radio,\n            configNme: 'styleConfig'\n          }, {\n            components: toolCom.c_title,\n            configNme: 'titleContent'\n          }, {\n            components: toolCom.c_slider,\n            configNme: 'numberConfig'\n          }, {\n            components: toolCom.c_checkbox,\n            configNme: 'checkboxInfo'\n          }];\n          this.rCom = arr.concat(tempArr);\n        } else {\n          var _tempArr = [{\n            components: toolCom.c_title,\n            configNme: 'titleRight'\n          }, {\n            components: toolCom.c_slider,\n            configNme: 'liveConfig'\n          }, {\n            components: toolCom.c_fillet,\n            configNme: 'filletImg'\n          }, {\n            components: toolCom.c_title,\n            configNme: 'titleCurrency'\n          }, {\n            components: toolCom.c_bg_color,\n            configNme: 'moduleColor'\n          }, {\n            components: toolCom.c_bg_color,\n            configNme: 'bottomBgColor'\n          }, {\n            components: toolCom.c_slider,\n            configNme: 'topConfig'\n          }, {\n            components: toolCom.c_slider,\n            configNme: 'bottomConfig'\n          }, {\n            components: toolCom.c_slider,\n            configNme: 'prConfig'\n          }, {\n            components: toolCom.c_slider,\n            configNme: 'mbConfig'\n          }, {\n            components: toolCom.c_fillet,\n            configNme: 'fillet'\n          }];\n          this.rCom = arr.concat(_tempArr);\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    getConfig: function getConfig(data) {}\n  }\n};", null]}