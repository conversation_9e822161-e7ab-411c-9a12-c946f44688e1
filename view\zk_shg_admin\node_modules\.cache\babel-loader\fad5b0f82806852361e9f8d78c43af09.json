{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\create.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCouponIssue\\create.vue", "mtime": 1719540491000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport storeList from \"@/components/storeList\";\nimport goodsList from \"@/components/goodsList/index\";\nimport { couponCategoryApi, couponSaveApi, couponDetailApi } from \"@/api/marketing\";\nimport { brandList } from \"@/api/product\"; // import { formatDate } from '@/utils/validate';\n\nimport Setting from \"@/setting\";\nexport default {\n  name: \"storeCouponCreate\",\n  components: {\n    goodsList: goodsList,\n    storeList: storeList\n  },\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      couponType: [{\n        name: \"满减券\",\n        title: \"满N元减N元\",\n        id: 1\n      }, {\n        name: \"折扣券\",\n        title: \"满N元打N折\",\n        id: 2\n      }],\n      disabled: false,\n      storesList: [],\n      formData: {\n        coupon_title: \"\",\n        coupon_price: 0,\n        type: 0,\n        use_min_price: 0,\n        coupon_time: 1,\n        start_use_time: 0,\n        end_use_time: 0,\n        start_time: 0,\n        end_time: 0,\n        receive_type: 1,\n        is_permanent: 1,\n        total_count: 1,\n        sort: 0,\n        status: 1,\n        product_id: \"\",\n        category_id: [],\n        brand_id: [],\n        coupon_type: 1,\n        applicable_type: 1,\n        applicable_store_id: [],\n        rule: '',\n        category: 1\n      },\n      categoryList: [],\n      brandList: [],\n      productList: [],\n      isMinPrice: 0,\n      isCouponTime: 1,\n      isReceiveTime: 0,\n      modals: false,\n      datetime1: [],\n      datetime2: [],\n      storeModals: false,\n      currentTab: '1'\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\", \"menuCollapse\"])),\n  created: function created() {\n    this.getBrandList();\n    this.getCategoryList();\n\n    if (this.$route.params.id) {\n      this.getCouponDetail();\n    }\n  },\n  methods: {\n    //删除门店\n    delte: function delte(row) {\n      var _this = this;\n\n      this.storesList.forEach(function (item, index) {\n        if (row.id == item.id) {\n          _this.storesList.splice(index, 1);\n        }\n      });\n    },\n    //添加门店\n    addStore: function addStore() {\n      this.storeModals = true;\n    },\n    //关闭门店弹窗\n    cancelStore: function cancelStore() {\n      this.storeModals = false;\n    },\n    uniqueId: function uniqueId(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.id) && res.set(arr.id, 1);\n      });\n    },\n    getStoreId: function getStoreId(data) {\n      this.storeModals = false;\n      var list = this.storesList.concat(data);\n      var uni = this.uniqueId(list);\n      this.storesList = uni;\n    },\n    couponTypeTap: function couponTypeTap(item) {\n      this.formData.coupon_price = 0;\n      this.formData.coupon_type = item.id;\n    },\n    // 品类\n    getCategoryList: function getCategoryList() {\n      var _this2 = this;\n\n      couponCategoryApi(1).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  res.data.forEach(function (val) {\n                    val.cate_name = \"\".concat(val.html).concat(val.cate_name);\n                  });\n                  _this2.categoryList = res.data;\n\n                case 2:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    //品牌\n    getBrandList: function getBrandList() {\n      var _this3 = this;\n\n      brandList().then(function (res) {\n        _this3.brandList = res.data;\n      });\n    },\n    // 优惠券\n    getCouponDetail: function getCouponDetail() {\n      var _this4 = this;\n\n      couponDetailApi(this.$route.params.id).then(function (res) {\n        var data = res.data;\n        _this4.formData.coupon_title = data.coupon_title;\n        _this4.formData.coupon_type = data.coupon_type;\n        _this4.formData.type = data.type;\n        _this4.formData.category_id = Array.isArray(data.category_id) ? data.category_id : [];\n        _this4.formData.brand_id = Array.isArray(data.brand_id) ? data.brand_id : [];\n        _this4.formData.category = data.category;\n        _this4.formData.rule = data.rule;\n        _this4.formData.coupon_price = parseFloat(data.coupon_price);\n        _this4.formData.use_min_price = parseFloat(data.use_min_price);\n        _this4.formData.coupon_time = data.coupon_time;\n        _this4.formData.receive_type = data.receive_type;\n        _this4.formData.is_permanent = data.is_permanent;\n        _this4.formData.status = data.status;\n        _this4.formData.product_id = data.product_id;\n        _this4.formData.start_time = data.start_time;\n        _this4.formData.end_time = data.end_time;\n        _this4.formData.total_count = data.total_count;\n        _this4.formData.sort = data.sort;\n        _this4.formData.applicable_type = data.applicable_type;\n        _this4.storesList = data.stores || [];\n\n        if (\"productInfo\" in data) {\n          _this4.productList = data.productInfo;\n        }\n\n        if (!data.coupon_time) {\n          _this4.isCouponTime = 0;\n          _this4.datetime1 = [data.start_use_time * 1000, data.end_use_time * 1000];\n          _this4.formData.start_use_time = _this4.makeDate(data.start_use_time * 1000);\n          _this4.formData.end_use_time = _this4.makeDate(data.end_use_time * 1000);\n        }\n\n        if (data.start_time) {\n          _this4.isReceiveTime = 1;\n          _this4.datetime2 = [data.start_time * 1000, data.end_time * 1000];\n          _this4.formData.start_time = _this4.makeDate(data.start_time * 1000);\n          _this4.formData.end_time = _this4.makeDate(data.end_time * 1000);\n        }\n\n        if (data.use_min_price !== \"0.00\") {\n          _this4.isMinPrice = 1;\n        }\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    },\n    makeDate: function makeDate(data) {\n      var date = new Date(data);\n      var YY = date.getFullYear() + \"-\";\n      var MM = (date.getMonth() + 1 < 10 ? \"0\" + (date.getMonth() + 1) : date.getMonth() + 1) + \"-\";\n      var DD = date.getDate() < 10 ? \"0\" + date.getDate() : date.getDate();\n      var hh = (date.getHours() < 10 ? \"0\" + date.getHours() : date.getHours()) + \":\";\n      var mm = (date.getMinutes() < 10 ? \"0\" + date.getMinutes() : date.getMinutes()) + \":\";\n      var ss = date.getSeconds() < 10 ? \"0\" + date.getSeconds() : date.getSeconds();\n      return YY + MM + DD + \" \" + hh + mm + ss;\n    },\n    // 上一页：\n    upTab: function upTab() {\n      if (this.currentTab == '2') {\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    downTab: function downTab() {\n      var valid = this.validate();\n\n      if (typeof valid === 'boolean' && valid) {\n        this.currentTab = '2';\n      }\n    },\n    // 创建\n    save: function save() {\n      var _this5 = this;\n\n      var valid = this.validate();\n\n      if (typeof valid !== 'boolean' || !valid) {\n        return;\n      }\n\n      this.disabled = false;\n      var storeId = [];\n      this.storesList.forEach(function (item) {\n        storeId.push(item.id);\n      });\n\n      if (this.formData.applicable_type == 2 && !storeId.length) {\n        return this.$Message.warning('请添加适用门店');\n      }\n\n      this.formData.applicable_store_id = storeId;\n      couponSaveApi(this.formData).then(function (res) {\n        _this5.disabled = true;\n\n        _this5.$Message.success(res.msg);\n\n        setTimeout(function () {\n          _this5.$router.push({\n            path: _this5.roterPre + \"/marketing/store_coupon_issue/index\"\n          });\n        }, 1000);\n      }).catch(function (err) {\n        _this5.$Message.error(err.msg);\n      });\n    },\n    // 使用有效期--时间段\n    dateChange: function dateChange(time) {\n      this.formData.start_use_time = time[0];\n      this.formData.end_use_time = time[1];\n    },\n    // 限时\n    timeChange: function timeChange(time) {\n      this.formData.start_time = time[0];\n      this.formData.end_time = time[1];\n    },\n    //对象数组去重；\n    unique: function unique(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.product_id) && res.set(arr.product_id, 1);\n      });\n    },\n    // 选择的商品\n    getProductId: function getProductId(productList) {\n      var _this6 = this;\n\n      this.modals = false;\n      this.productList = this.unique(this.productList.concat(productList));\n      this.formData.product_id = \"\";\n      this.productList.forEach(function (value) {\n        if (_this6.formData.product_id) {\n          _this6.formData.product_id += \",\".concat(value.product_id);\n        } else {\n          _this6.formData.product_id += \"\".concat(value.product_id);\n        }\n      });\n    },\n    cancel: function cancel() {\n      this.modals = false;\n    },\n    // 删除商品\n    remove: function remove(productId) {\n      var _this7 = this;\n\n      for (var index = 0; index < this.productList.length; index++) {\n        if (this.productList[index].product_id == productId) {\n          this.productList.splice(index, 1);\n        }\n      }\n\n      this.formData.product_id = \"\";\n      this.productList.forEach(function (value) {\n        if (_this7.formData.product_id) {\n          _this7.formData.product_id += \",\".concat(value.product_id);\n        } else {\n          _this7.formData.product_id += \"\".concat(value.product_id);\n        }\n      });\n    },\n    // 优惠券种类改变\n    categoryChange: function categoryChange(value) {\n      if (value == 2) {\n        this.formData.receive_type = 1;\n      }\n    },\n    // 表单验证\n    validate: function validate() {\n      if (!this.formData.coupon_title) {\n        return this.$Message.error(\"请输入优惠券名称\");\n      }\n\n      if (this.formData.type === 2) {\n        if (!this.formData.product_id) {\n          return this.$Message.error(\"请选择商品\");\n        }\n      }\n\n      if (this.formData.type === 1) {\n        if (!this.formData.category_id.length) {\n          return this.$Message.error(\"请选择品类\");\n        }\n      }\n\n      if (this.formData.type === 3) {\n        if (!this.formData.brand_id.length) {\n          return this.$Message.error(\"请选择品牌\");\n        }\n      }\n\n      if (this.formData.coupon_price <= 0) {\n        return this.$Message.error(\"优惠券面值不能小于0\");\n      }\n\n      if (!this.isMinPrice) {\n        this.formData.use_min_price = 0;\n      } else {\n        if (this.formData.use_min_price < 1) {\n          return this.$Message.error(\"优惠券最低消费不能小于0\");\n        }\n      }\n\n      if (this.isCouponTime) {\n        this.formData.start_use_time = 0;\n        this.formData.end_use_time = 0;\n\n        if (this.formData.coupon_time < 1) {\n          return this.$Message.error(\"使用有效期限不能小于1天\");\n        }\n      } else {\n        this.formData.coupon_time = 0;\n\n        if (!this.formData.start_use_time) {\n          return this.$Message.error(\"请选择使用有效期限\");\n        }\n      }\n\n      if (this.isReceiveTime) {\n        if (!this.formData.start_time) {\n          return this.$Message.error(\"请选择领取时间\");\n        }\n      } else {\n        this.formData.start_time = 0;\n        this.formData.end_time = 0;\n      }\n\n      if (this.formData.is_permanent) {\n        this.formData.total_count = 0;\n      } else {\n        if (this.formData.total_count < 1) {\n          return this.$Message.error(\"发布数量不能小于1\");\n        }\n      }\n\n      return true;\n    }\n  }\n};", null]}