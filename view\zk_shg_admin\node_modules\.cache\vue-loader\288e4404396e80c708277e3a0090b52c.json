{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_input_item.vue?vue&type=template&id=50f0d3e3&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_input_item.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"box\" :class=\"configData.type == 'form'?'':'on3'\" v-if=\"configData\">\n    <div class=\"c_row-item\" :class=\"{on:configData.type=='form',on2:configData.type=='ranges'}\">\n        <Col class=\"label\" :span=\"configData.type=='ranges'?'':4\">\n            {{configData.title}}\n        </Col>\n        <Col :span=\"configData.type=='ranges'?24:configData.type=='form'?19:18\" class=\"slider-box\">\n            <div @click=\"getLink(configData)\">\n              <Input v-if=\"configData.inputType=='text'\" :type='(defaults.valConfig.tabVal==1 || defaults.valConfig.tabVal==4)?\"number\":\"text\"' :icon=\"configData.title=='链接'?'ios-arrow-forward':''\" :readonly=\"configData.title=='链接'?true:false\" v-model=\"configData.value\" :placeholder=\"configData.place\" :maxlength=\"configData.max\" />\n              <Input v-else :icon=\"(configData.title=='链接' || configData.type=='link')?'ios-arrow-forward':''\" :readonly=\"(configData.title=='链接' || configData.type=='link')?true:false\" v-model=\"configData.value\" :placeholder=\"configData.place\" :maxlength=\"configData.max\" />\n            </div>\n        </Col>\n    </div>\n    <linkaddress ref=\"linkaddres\" @linkUrl=\"linkUrl\" v-if=\"configData.type!='form' && (configData.title=='链接' || configData.type=='link')\"></linkaddress>\n</div>\n", null]}