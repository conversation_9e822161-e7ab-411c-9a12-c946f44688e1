{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue?vue&type=template&id=5642b922&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n  <Modal\n    title=\"编辑热区\"\n    v-model=\"dialogVisible\"\n    @on-visible-change=\"openModal\"\n    :mask-closable=\"false\"\n    fullscreen\n  >\n    <div class=\"operationFloor\">\n      <div class=\"imgBox\" @mouseup.left.stop=\"changeStop()\">\n        <div ref=\"container\" id=\"img-box-container\" class=\"container\">\n          <img\n            ref=\"backgroundImg\"\n            :src=\"imgs\"\n            ondragstart=\"return false;\"\n            oncontextmenu=\"return false;\"\n            onselect=\"document.selection.empty();\"\n            alt=\"img\"\n            @mousedown.left.stop=\"mouseDown($event)\"\n          />\n          <!--draw hotpot-->\n          <div\n            v-show=\"caseShow\"\n            :style=\"{\n              width: areaWidth + 'px',\n              height: areaHeight + 'px',\n              left: starX + 'px',\n              top: starY + 'px',\n            }\"\n            class=\"area\"\n          />\n          <!--be hotpot-->\n          <AreaBox\n            v-for=\"(item, index) in areaData\"\n            :area-data-index=\"index\"\n            :key=\"'area' + index\"\n            :link=\"item.link\"\n            :title=\"item.title\"\n            :type=\"parseInt(item.type)\"\n            :area-init.sync=\"item\"\n            :parent-width=\"parentWidth\"\n            :parent-height=\"parentHeight\"\n            @delAreaBox=\"delAreaBox\"\n            @addURL=\"addURL\"\n          />\n        </div>\n      </div>\n      <!-- 热区链接配置 -->\n      <div class=\"form\">\n        <h2 class=\"mb20\">图片热区</h2>\n        <Alert class=\"mb-20 w-400\" show-icon\n          >框选热区范围，双击设置热区信息</Alert\n        >\n\n        <div v-for=\"(item, index) in areaData\" :key=\"index\" class=\"form-row\">\n          <div class=\"form-item\">\n            <span class=\"num\">热区{{ item.number }}</span>\n          </div>\n          <div class=\"form-item label\">\n            <div @click=\"getLink(index)\">\n              <Input\n                icon=\"ios-arrow-forward\"\n                v-model=\"item.link\"\n                :style=\"linkInputStyle\"\n                readonly\n                placeholder=\"选择跳转链接\"\n              />\n            </div>\n          </div>\n          <i class=\"el-icon-delete\" @click=\"delAreaBox(index)\" />\n        </div>\n      </div>\n    </div>\n    <div slot=\"footer\">\n      <Button class=\"mr20\" type=\"primary\" @click=\"saveAreaData\">\n        完成\n      </Button>\n    </div>\n  </Modal>\n  <linkaddress ref=\"linkaddres\" @linkUrl=\"linkUrl\"></linkaddress>\n</div>\n", null]}