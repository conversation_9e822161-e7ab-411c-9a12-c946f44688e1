{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\api\\erp.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\api\\erp.js", "mtime": 1751009758620}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\nimport request from '@/plugins/request';\n/**\n * @description erp设置\n * @param {Object} param data {Object} 传值参数\n */\n\nexport function erpConfig() {\n  return request({\n    url: 'erp/config',\n    method: 'get'\n  });\n}\n/**\n * @description erp门店列表\n * @param {Object} param data {Object} 传值参数\n */\n\nexport function erpShop(data) {\n  return request({\n    url: 'store/erp/shop',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 导入erp\n * @param {Object} param data {Object} 传值参数\n */\n\nexport function erpProduct(data) {\n  return request({\n    url: 'product/import_erp_product',\n    method: 'post',\n    data: data\n  });\n} // =========================== 库存管理相关接口 ===========================\n\n/**\n * @description 获取库存列表\n * @param {Object} data 查询参数\n */\n\nexport function getInventoryList(data) {\n  return request({\n    url: 'erp/inventory/list',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取库存统计\n * @param {Object} data 查询参数\n */\n\nexport function getInventoryStatistics(data) {\n  return request({\n    url: 'erp/inventory/statistics',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 调整库存\n * @param {Object} data 调整参数\n */\n\nexport function adjustInventory(data) {\n  return request({\n    url: 'erp/inventory/adjust',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 批量调整库存\n * @param {Object} data 批量调整参数\n */\n\nexport function batchAdjustInventory(data) {\n  return request({\n    url: 'erp/inventory/batch_adjust',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 设置库存预警\n * @param {Object} data 预警参数\n */\n\nexport function setInventoryWarning(data) {\n  return request({\n    url: 'erp/inventory/warning',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 获取库存变动记录\n * @param {Object} data 查询参数\n */\n\nexport function getInventoryRecord(data) {\n  return request({\n    url: 'erp/inventory/record',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 导出库存数据\n * @param {Object} data 导出参数\n */\n\nexport function exportInventory(data) {\n  return request({\n    url: 'erp/inventory/export',\n    method: 'post',\n    data: data,\n    responseType: 'blob'\n  });\n}\n/**\n * @description 获取预警商品列表\n * @param {Object} data 查询参数\n */\n\nexport function getWarningList(data) {\n  return request({\n    url: 'erp/inventory/warning',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取门店选项\n */\n\nexport function getStoreOptions() {\n  return request({\n    url: 'erp/inventory/store_options',\n    method: 'get'\n  });\n}\n/**\n * @description 获取分类选项\n */\n\nexport function getCategoryOptions() {\n  return request({\n    url: 'erp/inventory/product_options',\n    method: 'get'\n  });\n}\n/**\n * @description 获取商品销售统计\n * @param {Object} data 查询参数\n */\n\nexport function getSalesStatistics(data) {\n  return request({\n    url: 'erp/inventory/sales_statistics',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取商品销售统计\n * @param {Object} data 查询参数\n */\n\nexport function exportSalesStatistics(data) {\n  return request({\n    url: 'erp/inventory/export_statistics',\n    method: 'post',\n    data: data,\n    responseType: 'blob'\n  });\n} // =========================== 调拨管理相关接口 ===========================\n\n/**\n * @description 获取调拨单列表\n * @param {Object} data 查询参数\n */\n\nexport function getTransferList(data) {\n  return request({\n    url: 'admin/erp/transfer/list',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取调拨单详情\n * @param {Number} id 调拨单ID\n */\n\nexport function getTransferDetail(id) {\n  return request({\n    url: \"admin/erp/transfer/detail/\".concat(id),\n    method: 'get'\n  });\n}\n/**\n * @description 审核调拨单\n * @param {Object} data 审核参数\n */\n\nexport function approveTransfer(data) {\n  return request({\n    url: 'admin/erp/transfer/approve',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 批量审核调拨单\n * @param {Object} data 批量审核参数\n */\n\nexport function batchApproveTransfer(data) {\n  return request({\n    url: 'admin/erp/transfer/batch_approve',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 执行调拨\n * @param {Object} data 执行参数\n */\n\nexport function executeTransfer(data) {\n  return request({\n    url: 'admin/erp/transfer/execute',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 强制执行调拨\n * @param {Object} data 强制执行参数\n */\n\nexport function forceExecuteTransfer(data) {\n  return request({\n    url: 'admin/erp/transfer/force_execute',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 批量执行调拨\n * @param {Object} data 批量执行参数\n */\n\nexport function batchExecuteTransfer(data) {\n  return request({\n    url: 'admin/erp/transfer/batch_execute',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * @description 删除调拨单\n * @param {Number} id 调拨单ID\n */\n\nexport function deleteTransfer(id) {\n  return request({\n    url: \"admin/erp/transfer/delete/\".concat(id),\n    method: 'delete'\n  });\n}\n/**\n * @description 获取调拨统计\n * @param {Object} data 查询参数\n */\n\nexport function getTransferStatistics(data) {\n  return request({\n    url: 'admin/erp/transfer/statistics',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 导出调拨数据\n * @param {Object} data 导出参数\n */\n\nexport function exportTransfer(data) {\n  return request({\n    url: 'admin/erp/transfer/export',\n    method: 'post',\n    data: data,\n    responseType: 'blob'\n  });\n} // =========================== 报表分析相关接口 ===========================\n\n/**\n * @description 获取库存报表\n * @param {Object} data 查询参数\n */\n\nexport function getInventoryReport(data) {\n  return request({\n    url: 'admin/erp/report/inventory',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取调拨报表\n * @param {Object} data 查询参数\n */\n\nexport function getTransferReport(data) {\n  return request({\n    url: 'admin/erp/report/transfer',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取预警报表\n * @param {Object} data 查询参数\n */\n\nexport function getWarningReport(data) {\n  return request({\n    url: 'admin/erp/report/warning',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取数据分析\n * @param {Object} data 查询参数\n */\n\nexport function getDataAnalysis(data) {\n  return request({\n    url: 'admin/erp/report/analysis',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取门店库存报表\n * @param {Object} data 查询参数\n */\n\nexport function getStoreInventoryReport(data) {\n  return request({\n    url: 'admin/erp/report/store_inventory',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取调拨效率报表\n * @param {Object} data 查询参数\n */\n\nexport function getTransferEfficiencyReport(data) {\n  return request({\n    url: 'admin/erp/report/transfer_efficiency',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取热门商品分析\n * @param {Object} data 查询参数\n */\n\nexport function getHotProductAnalysis(data) {\n  return request({\n    url: 'admin/erp/report/hot_products',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 获取库存趋势数据\n * @param {Object} data 查询参数\n */\n\nexport function getInventoryTrend(data) {\n  return request({\n    url: 'admin/erp/report/inventory_trend',\n    method: 'get',\n    params: data\n  });\n}\n/**\n * @description 导出综合报表\n * @param {Object} data 导出参数\n */\n\nexport function exportReport(data) {\n  return request({\n    url: 'admin/erp/report/export',\n    method: 'post',\n    data: data,\n    responseType: 'blob'\n  });\n} // =========================== 基础数据接口 ===========================\n\n/**\n * @description 获取门店列表\n */\n\nexport function getStoreList() {\n  return request({\n    url: 'erp/inventory/store_options',\n    method: 'get'\n  });\n}\n/**\n * @description 获取商品分类列表\n */\n\nexport function getCategoryList() {\n  return request({\n    url: 'erp/inventory/product_options',\n    method: 'get'\n  });\n}\n/**\n * @description 获取商品列表\n * @param {Object} data 查询参数\n */\n\nexport function getProductList(data) {\n  return request({\n    url: 'admin/erp/product/list',\n    method: 'get',\n    params: data\n  });\n} // 导出所有库存管理相关的API\n\nexport var inventoryApi = {\n  getInventoryList: getInventoryList,\n  getInventoryStatistics: getInventoryStatistics,\n  getStatistics: getInventoryStatistics,\n  // 添加别名\n  adjustInventory: adjustInventory,\n  adjustStock: adjustInventory,\n  // 添加别名\n  batchAdjustInventory: batchAdjustInventory,\n  setInventoryWarning: setInventoryWarning,\n  setWarning: setInventoryWarning,\n  // 添加别名\n  getInventoryRecord: getInventoryRecord,\n  getStockRecord: getInventoryRecord,\n  // 添加别名\n  exportInventory: exportInventory,\n  getWarningList: getWarningList,\n  getStoreList: getStoreList,\n  getCategoryList: getCategoryList,\n  getProductList: getProductList,\n  // 新增的销售统计相关API\n  getStoreOptions: getStoreOptions,\n  getCategoryOptions: getCategoryOptions,\n  getSalesStatistics: getSalesStatistics,\n  exportSalesStatistics: exportSalesStatistics\n}; // 导出所有调拨管理相关的API\n\nexport var transferApi = {\n  getTransferList: getTransferList,\n  getTransferDetail: getTransferDetail,\n  approveTransfer: approveTransfer,\n  batchApproveTransfer: batchApproveTransfer,\n  executeTransfer: executeTransfer,\n  forceExecuteTransfer: forceExecuteTransfer,\n  batchExecuteTransfer: batchExecuteTransfer,\n  deleteTransfer: deleteTransfer,\n  getTransferStatistics: getTransferStatistics,\n  exportTransfer: exportTransfer,\n  getStoreList: getStoreList\n}; // 导出所有报表分析相关的API\n\nexport var reportApi = {\n  getInventoryReport: getInventoryReport,\n  getTransferReport: getTransferReport,\n  getWarningReport: getWarningReport,\n  getDataAnalysis: getDataAnalysis,\n  getStoreInventoryReport: getStoreInventoryReport,\n  getTransferEfficiencyReport: getTransferEfficiencyReport,\n  getHotProductAnalysis: getHotProductAnalysis,\n  getInventoryTrend: getInventoryTrend,\n  exportReport: exportReport\n};", null]}