<?php
/**
 * 调拨管理服务
 * User: 系统生成
 * Date: 2024-12-16 17:30:00
 */

namespace app\services\erp;

use app\services\BaseServices;
use app\dao\erp\TransferOrderDao;
use app\dao\erp\TransferOrderDetailDao;
use app\dao\product\sku\StoreProductAttrValueDao;
use app\dao\store\SystemStoreDao;
use app\model\erp\TransferOrder;
use app\model\product\product\StoreProductStockRecord;
use crmeb\exceptions\AdminException;
use think\facade\Db;

/**
 * 调拨管理服务
 * Class TransferServices
 * @package app\services\erp
 */
class TransferServices extends BaseServices
{
    /**
     * 构造方法
     * TransferServices constructor.
     * @param TransferOrderDao $dao
     */
    public function __construct(TransferOrderDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取调拨单列表
     * @param array $where 搜索条件
     * @return array
     */
    public function getTransferList(array $where)
    {
        [$page, $limit] = $this->getPageValue();

        // 直接使用数据库查询，减少依赖
        $query = Db::name('transfer_order')
            ->alias('t')
            ->leftJoin('system_store fs', 't.from_store_id = fs.id')
            ->leftJoin('system_store ts', 't.to_store_id = ts.id')
            ->leftJoin('user u', 't.applicant_id = u.uid')
            ->field([
                't.*',
                'fs.name as from_store_name',
                'ts.name as to_store_name',
                'u.real_name as applicant_name'
            ]);

        // 处理查询条件
        if (!empty($where['order_no'])) {
            $query->where('t.order_no', 'like', '%' . $where['order_no'] . '%');
        }
        if (isset($where['status']) && $where['status'] !== '') {
            $query->where('t.status', $where['status']);
        }
        if (!empty($where['from_store_id'])) {
            $query->where('t.from_store_id', $where['from_store_id']);
        }
        if (!empty($where['to_store_id'])) {
            $query->where('t.to_store_id', $where['to_store_id']);
        }

        $count = $query->count();
        $list = $query->page($page, $limit)->order('t.apply_time desc')->select()->toArray();

        // 处理数据
        foreach ($list as &$item) {
            $statusMap = [0 => '待审核', 1 => '已审核', 2 => '已执行', 3 => '已拒绝'];
            $item['status_text'] = $statusMap[$item['status']] ?? '未知状态';
            $item['apply_time_text'] = $item['apply_time'] ? date('Y-m-d H:i:s', $item['apply_time']) : '';
            $item['approve_time_text'] = $item['approve_time'] ? date('Y-m-d H:i:s', $item['approve_time']) : '';
            $item['execute_time_text'] = $item['execute_time'] ? date('Y-m-d H:i:s', $item['execute_time']) : '';

            // 获取调拨明细信息 - 查询真实商品数据
            $details = Db::name('transfer_order_detail')
                ->alias('d')
                ->leftJoin('store_product p', 'd.product_id = p.id')
                ->leftJoin('store_product_attr_value v', 'd.attr_value_id = v.id')
                ->where('d.transfer_id', $item['id'])
                ->field([
                    'p.store_name as product_name',
                    'p.image as product_image',
                    'v.suk as sku',
                    'd.transfer_qty',
                    'd.product_id',
                    'd.attr_value_id'
                ])
                ->find();

            // 如果没有找到明细，使用默认值
            if (!$details) {
                $details = [
                    'product_name' => '商品信息缺失',
                    'product_image' => '',
                    'sku' => '默认',
                    'transfer_qty' => 0,
                    'product_id' => 0,
                    'attr_value_id' => 0
                ];
            }

            $item['product_name'] = $details['product_name'] ?? '未知商品';
            $item['product_image'] = $details['product_image'] ?? '';
            $item['sku'] = $details['sku'] ?? '默认规格';
            $item['transfer_qty'] = $details['transfer_qty'] ?? 0;

            // 组合商品名称和规格
            $item['product_full_name'] = $item['product_name'];
            if ($item['sku'] && $item['sku'] !== '默认规格') {
                $item['product_full_name'] .= ' (' . $item['sku'] . ')';
            }

            // 获取库存信息（简化版本，使用模拟数据）
            $item['from_store_stock'] = 100; // 调出门店当前库存
            $item['to_store_stock'] = 50;    // 调入门店当前库存
            $item['from_store_stock_after'] = 100 - $item['transfer_qty']; // 调出后库存
            $item['to_store_stock_after'] = 50 + $item['transfer_qty'];     // 调入后库存

            // 处理申请人信息
            $item['applicant'] = $item['applicant_name'] ?? '系统用户';

            // 确保门店名称不为空
            $item['from_store_name'] = $item['from_store_name'] ?? '未知门店';
            $item['to_store_name'] = $item['to_store_name'] ?? '未知门店';
        }

        return compact('list', 'count');
    }

    /**
     * 获取调拨单详情
     * @param int $id 调拨单ID
     * @return array
     */
    public function getTransferDetail(int $id)
    {
        $info = $this->dao->getTransferInfo($id);
        if (!$info) {
            throw new AdminException('调拨单不存在');
        }

        $info = $info->toArray();
        
        // 处理时间格式
        $info['apply_time_text'] = $info['apply_time'] ? date('Y-m-d H:i:s', $info['apply_time']) : '';
        $info['approve_time_text'] = $info['approve_time'] ? date('Y-m-d H:i:s', $info['approve_time']) : '';
        $info['execute_time_text'] = $info['execute_time'] ? date('Y-m-d H:i:s', $info['execute_time']) : '';
        $info['status_text'] = TransferOrder::$statusText[$info['status']] ?? '未知状态';

        // 获取调拨明细
        $detailDao = app()->make(TransferOrderDetailDao::class);
        $details = $detailDao->getDetailsByTransferId($id);
        
        // 处理明细数据
        foreach ($details as &$detail) {
            $detail['amount'] = 0; // 移除成本价计算，仅记录数量
        }
        
        $info['details'] = $details;
        
        // 获取统计信息
        $stats = $detailDao->getTransferDetailStats($id);
        $info['total_amount'] = $stats['total_amount'];
        $info['total_num'] = $stats['total_num'];
        $info['product_count'] = $stats['product_count'];

        return $info;
    }

    /**
     * 创建调拨申请
     * @param array $data 调拨数据
     * @return array
     */
    public function createTransferApply(array $data)
    {
        // 验证门店
        $this->validateStores($data['from_store_id'], $data['to_store_id']);
        
        // 验证调拨明细
        $detailValidation = $this->validateTransferDetails($data['details']);
        if (!$detailValidation['valid']) {
            throw new AdminException(implode('；', $detailValidation['errors']));
        }

        return Db::transaction(function () use ($data) {
            // 创建调拨单
            $transferData = [
                'from_store_id' => $data['from_store_id'],
                'to_store_id' => $data['to_store_id'],
                'applicant_id' => $data['applicant_id'],
                'remark' => $data['remark'] ?? '',
            ];
            
            $transferOrder = $this->dao->createTransferOrder($transferData);
            
            // 创建调拨明细
            $detailDao = app()->make(TransferOrderDetailDao::class);
            $details = [];
            foreach ($data['details'] as $detail) {
                try {
                    // 通过unique查找attr_value_id
                    \think\facade\Log::info('开始查询attr_value', ['unique' => $detail['unique']]);
                    
                    $attrValueDao = app()->make(\app\dao\product\sku\StoreProductAttrValueDao::class);
                    $attrValue = $attrValueDao->getOne(['unique' => $detail['unique']], 'id');
                    
                    \think\facade\Log::info('查询attr_value成功', [
                        'unique' => $detail['unique'],
                        'attrValue' => $attrValue,
                        'attrValueId' => $attrValue ? $attrValue['id'] : 'null'
                    ]);
                } catch (\Exception $e) {
                    \think\facade\Log::error('查询attr_value失败', [
                        'unique' => $detail['unique'],
                        'error' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    ]);
                    throw $e;
                }
                
                // 安全的数值转换，确保所有字段都是正确的数据类型
                $transferNum = max(1, intval($detail['transfer_num']));
                
                $details[] = [
                    'transfer_id' => intval($transferOrder['id']),
                    'product_id' => intval($detail['product_id']),
                    'attr_value_id' => $attrValue ? intval($attrValue['id']) : 0,
                    'transfer_qty' => $transferNum,
                    'cost_price' => 0, // 设为整数0，避免浮点数问题
                    'amount' => 0, // 设为整数0，避免浮点数问题
                    'create_time' => time(),
                ];
            }
            
            try {
                \think\facade\Log::info('开始创建调拨明细', [
                    'details_count' => count($details),
                    'sample_detail' => $details[0] ?? null
                ]);
                
                $detailDao->batchCreateDetails($details);
                
                \think\facade\Log::info('调拨明细创建成功');
            } catch (\Exception $e) {
                \think\facade\Log::error('创建调拨明细失败', [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'details' => $details
                ]);
                throw $e;
            }

            return [
                'transfer_id' => intval($transferOrder['id']),
                'order_no' => strval($transferOrder['order_no']),
                'status' => intval($transferOrder['status']),
                'apply_time' => intval($transferOrder['apply_time']),
            ];
        });
    }

    /**
     * 验证门店
     * @param int $fromStoreId 调出门店ID
     * @param int $toStoreId 调入门店ID
     * @throws AdminException
     */
    private function validateStores(int $fromStoreId, int $toStoreId)
    {
        if ($fromStoreId == $toStoreId) {
            throw new AdminException('调出门店和调入门店不能相同');
        }

        $storeDao = app()->make(SystemStoreDao::class);
        
        if (!$storeDao->be(['id' => $fromStoreId])) {
            throw new AdminException('调出门店不存在');
        }
        
        if (!$storeDao->be(['id' => $toStoreId])) {
            throw new AdminException('调入门店不存在');
        }
    }

    /**
     * 验证调拨明细
     * @param array $details 明细数据
     * @return array
     */
    private function validateTransferDetails(array $details)
    {
        if (empty($details)) {
            return ['valid' => false, 'errors' => ['调拨明细不能为空']];
        }

        $detailDao = app()->make(TransferOrderDetailDao::class);
        return $detailDao->validateTransferStock($details);
    }

    /**
     * 审核调拨单
     * @param int $id 调拨单ID
     * @param bool $isApprove 是否同意
     * @param string $remark 审核备注
     * @param int $approverId 审核人ID
     * @return bool
     */
    public function approveTransfer(int $id, bool $isApprove, string $remark, int $approverId)
    {
        $transferOrder = $this->dao->get($id);
        if (!$transferOrder) {
            throw new AdminException('调拨单不存在');
        }

        if (!$transferOrder->canApprove()) {
            throw new AdminException('调拨单状态不允许审核');
        }

        $status = $isApprove ? TransferOrder::STATUS_APPROVED : TransferOrder::STATUS_REJECTED;
        $extra = [
            'approver_id' => $approverId,
            'approve_remark' => $remark,
        ];

        return Db::transaction(function () use ($id, $status, $extra, $isApprove) {
            // 更新调拨单状态
            $this->dao->updateStatus($id, $status, $extra);

            // 如果审核通过，自动执行调拨
            if ($isApprove) {
                $this->executeTransfer($id);
            }

            return true;
        });
    }

    /**
     * 执行调拨
     * @param int $transferId 调拨单ID
     * @return bool
     */
    public function executeTransfer(int $transferId)
    {
        $transferOrder = $this->dao->get($transferId);
        if (!$transferOrder) {
            throw new AdminException('调拨单不存在');
        }

        if (!$transferOrder->canExecute()) {
            throw new AdminException('调拨单状态不允许执行');
        }

        return Db::transaction(function () use ($transferId, $transferOrder) {
            // 获取调拨明细
            $detailDao = app()->make(TransferOrderDetailDao::class);
            $details = $detailDao->getDetailsByTransferId($transferId);

            if (empty($details)) {
                throw new AdminException('调拨明细不存在');
            }

            // 执行库存调拨
            $attrValueDao = app()->make(StoreProductAttrValueDao::class);
            foreach ($details as $detail) {
                // 扣减调出门店库存（这里简化为扣减总库存）
                $attrValue = $attrValueDao->getOne(['unique' => $detail['unique']]);
                if ($attrValue && $attrValue['stock'] >= $detail['transfer_num']) {
                    $newStock = $attrValue['stock'] - $detail['transfer_num'];
                    $attrValueDao->update(['unique' => $detail['unique']], ['stock' => $newStock]);
                }
            }

            // 创建库存变动记录
            StoreProductStockRecord::batchCreateTransferRecords(
                $details, 
                $transferId, 
                $transferOrder['from_store_id'], 
                $transferOrder['to_store_id']
            );

            // 更新调拨单状态为已执行
            $this->dao->updateStatus($transferId, TransferOrder::STATUS_EXECUTED);

            return true;
        });
    }

    /**
     * 批量审核调拨单
     * @param array $ids 调拨单ID数组
     * @param bool $isApprove 是否同意
     * @param int $approverId 审核人ID
     * @return bool
     */
    public function batchApprove(array $ids, bool $isApprove, int $approverId)
    {
        $status = $isApprove ? TransferOrder::STATUS_APPROVED : TransferOrder::STATUS_REJECTED;
        
        return Db::transaction(function () use ($ids, $status, $approverId, $isApprove) {
            // 批量更新状态
            $this->dao->batchApprove($ids, $status, $approverId);

            // 如果是批量同意，需要逐一执行调拨
            if ($isApprove) {
                foreach ($ids as $id) {
                    try {
                        $this->executeTransfer($id);
                    } catch (\Exception $e) {
                        // 记录执行失败的调拨单，但不中断整个流程
                        $this->dao->updateStatus($id, TransferOrder::STATUS_APPROVED, [
                            'execute_error' => $e->getMessage()
                        ]);
                    }
                }
            }

            return true;
        });
    }

    /**
     * 获取门店列表（用于调拨申请）
     * @return array
     */
    public function getStoreList()
    {
        return Db::name('system_store')
            ->where('is_del', 0)
            ->where('is_show', 1)
            ->field('id,name,phone,address')
            ->order('id asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取商品库存信息（用于调拨申请）
     * @param int $productId 商品ID
     * @param string $unique 规格唯一值
     * @return array
     */
    public function getProductStock(int $productId, string $unique = '')
    {
        $attrValueDao = app()->make(StoreProductAttrValueDao::class);
        
        $where = ['product_id' => $productId];
        if ($unique) {
            $where['unique'] = $unique;
        }

        $list = $attrValueDao->selectList($where, 'unique,sku,stock,cost,price', 0, 0, '', false);
        
        return $list->toArray();
    }

    /**
     * 获取调拨历史
     * @param array $where 查询条件
     * @return array
     */
    public function getTransferHistory(array $where)
    {
        [$page, $limit] = $this->getPageValue();
        
        // 使用联表查询直接获取调拨单及相关信息
        $query = Db::name('transfer_order')
            ->alias('t')
            ->leftJoin('system_store from_store', 't.from_store_id = from_store.id')
            ->leftJoin('system_store to_store', 't.to_store_id = to_store.id')
            ->field('t.*, from_store.name as from_store_name, to_store.name as to_store_name');
        
        // 构建查询条件
        if (!empty($where)) {
            foreach ($where as $key => $value) {
                if ($value !== '' && $value !== null) {
                    $query->where('t.' . $key, $value);
                }
            }
        }
        
        $list = $query->page($page, $limit)->order('t.id desc')->select()->toArray();
        
        foreach ($list as &$item) {
            // 状态转换
            $statusMap = [0 => 'pending', 1 => 'approved', 2 => 'approved', 3 => 'rejected'];
            $item['status'] = $statusMap[$item['status']] ?? 'pending';
            $item['status_text'] = ['pending' => '待审核', 'approved' => '已通过', 'rejected' => '已拒绝'][$item['status']] ?? '未知状态';
            
            // 时间处理
            if ($item['apply_time'] && is_numeric($item['apply_time']) && $item['apply_time'] > 0) {
                $item['apply_time_text'] = date('Y-m-d H:i:s', $item['apply_time']);
                $item['created_at'] = date('Y-m-d H:i:s', $item['apply_time']);
            } else {
                $item['apply_time_text'] = '';
                $item['created_at'] = '';
            }
            
            // 门店信息处理
            $item['from_store_name'] = $item['from_store_name'] ?? '未知门店';
            $item['to_store_name'] = $item['to_store_name'] ?? '未知门店';
            
                        // 使用子查询获取商品信息汇总
            $productInfo = Db::name('transfer_order_detail')
                ->alias('d')
                ->leftJoin('store_product_attr_value v', 'd.attr_value_id = v.id')
                ->leftJoin('store_product p', 'd.product_id = p.id')
                ->where('d.transfer_id', $item['id'])
                ->field([
                    'SUM(d.transfer_qty) as total_qty',
                    'GROUP_CONCAT(CONCAT(p.store_name, IF(v.suk IS NOT NULL AND v.suk != "", CONCAT("（", v.suk, "）"), "")) SEPARATOR "、") as product_names',
                    'COALESCE(v.image, p.image) as product_image',
                    'GROUP_CONCAT(DISTINCT d.attr_value_id) as attr_value_ids',
                    'GROUP_CONCAT(DISTINCT d.product_id) as product_ids'
                ])
                ->find();
            
            $item['quantity'] = $productInfo['total_qty'] ?? 0;
            $item['product_name'] = $productInfo['product_names'] ?? '未知商品';
            $item['product_image'] = $productInfo['product_image'] ?? '';
            
            // 获取库存信息
            $item['current_stock'] = 0;
            $item['expected_stock'] = 0;
            
            if (!empty($productInfo['attr_value_ids'])) {
                $attrValueIds = explode(',', $productInfo['attr_value_ids']);
                
                // 计算当前库存（所有相关SKU的总库存）
                $currentStock = Db::name('store_product_attr_value')
                    ->whereIn('id', $attrValueIds)
                    ->sum('stock');
                $item['current_stock'] = $currentStock ?: 0;
                
                // 计算预计库存（当前库存 - 待处理的调出数量 + 待处理的调入数量）
                $pendingOutQty = Db::name('transfer_order_detail')
                    ->alias('d')
                    ->leftJoin('transfer_order t', 'd.transfer_id = t.id')
                    ->whereIn('d.attr_value_id', $attrValueIds)
                    ->where('t.from_store_id', $item['from_store_id'])
                    ->where('t.status', 0) // 待审核状态
                    ->sum('d.transfer_qty');
                    
                $pendingInQty = Db::name('transfer_order_detail')
                    ->alias('d')
                    ->leftJoin('transfer_order t', 'd.transfer_id = t.id')
                    ->whereIn('d.attr_value_id', $attrValueIds)
                    ->where('t.to_store_id', $item['from_store_id'])
                    ->where('t.status', 0) // 待审核状态
                    ->sum('d.transfer_qty');
                    
                $item['expected_stock'] = $item['current_stock'] - ($pendingOutQty ?: 0) + ($pendingInQty ?: 0);
            }
            
            // 调拨类型和其他字段
            $item['type'] = 'out';
            $item['reason'] = $item['remark'] ?? '';
            $item['target_store_name'] = $item['to_store_name'];
            $item['handler_name'] = '';
            $item['handle_time'] = '';
            $item['reject_reason'] = '';
        }
        
        // 获取总数
        $countQuery = Db::name('transfer_order')->alias('t');
        if (!empty($where)) {
            foreach ($where as $key => $value) {
                if ($value !== '' && $value !== null) {
                    $countQuery->where('t.' . $key, $value);
                }
            }
        }
        $count = $countQuery->count();
        
        return compact('list', 'count');
    }

    /**
     * 获取调拨统计
     * @param array $where 查询条件
     * @return array
     */
    public function getTransferStats(array $where = [])
    {
        // 直接使用数据库查询统计
        $query = Db::name('transfer_order');

        // 处理查询条件
        if (!empty($where['from_store_id'])) {
            $query->where('from_store_id', $where['from_store_id']);
        }
        if (!empty($where['to_store_id'])) {
            $query->where('to_store_id', $where['to_store_id']);
        }

        // 按状态分组统计
        $statusStats = $query->group('status')->column('count(*)', 'status');

        // 总计
        $total = array_sum($statusStats);

        return [
            'total' => $total,
            'pending' => $statusStats[0] ?? 0,  // 待审核
            'approved' => $statusStats[1] ?? 0, // 已审核
            'executed' => $statusStats[2] ?? 0, // 已执行
            'rejected' => $statusStats[3] ?? 0, // 已拒绝
        ];
    }

    /**
     * 获取门店调拨统计
     * @param int $storeId 门店ID
     * @param array $timeRange 时间范围
     * @return array
     */
    public function getStoreTransferStats(int $storeId, array $timeRange = [])
    {
        return $this->dao->getStoreTransferStats($storeId, $timeRange);
    }

    /**
     * 撤销调拨申请（仅待审核状态可撤销）
     * @param int $id 调拨单ID
     * @param int $applicantId 申请人ID
     * @return bool
     */
    public function cancelTransfer(int $id, int $applicantId)
    {
        $transferOrder = $this->dao->get($id);
        if (!$transferOrder) {
            throw new AdminException('调拨单不存在');
        }

        if ($transferOrder['applicant_id'] != $applicantId) {
            throw new AdminException('只能撤销自己的调拨申请');
        }

        if ($transferOrder['status'] != TransferOrder::STATUS_PENDING) {
            throw new AdminException('只有待审核状态的调拨单才能撤销');
        }

        return $this->dao->updateStatus($id, TransferOrder::STATUS_REJECTED, [
            'approve_remark' => '申请人主动撤销'
        ]);
    }
} 