{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue", "mtime": 1717551943000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import vuedraggable from 'vuedraggable'\n    import storeLabelList from \"@/components/storeLabelList\";\n\timport goodsList from '@/components/goodsList'\n\timport uploadPictures from '@/components/uploadPictures';\n\timport { brandList, cascaderListApi } from \"@/api/product\";\n    export default {\n        name: 'c_promotion',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            draggable: vuedraggable,\n\t\t\tstoreLabelList,\n\t\t\tgoodsList,\n\t\t\tuploadPictures\n        },\n        data () {\n            return {\n\t\t\t\tprops: { emitPath: false, multiple: true },\n                defaults: {},\n                configData: {},\n                itemObj: {},\n\t\t\t\tstoreLabelShow:false,\n\t\t\t\tmodals: false,\n\t\t\t\tmodalPic: false,\n\t\t\t\tisChoice: '单选',\n\t\t\t\tgridBtn: {\n\t\t\t\t    xl: 4,\n\t\t\t\t    lg: 8,\n\t\t\t\t    md: 8,\n\t\t\t\t    sm: 8,\n\t\t\t\t    xs: 8\n\t\t\t\t},\n\t\t\t\tgridPic: {\n\t\t\t\t    xl: 6,\n\t\t\t\t    lg: 8,\n\t\t\t\t    md: 12,\n\t\t\t\t    sm: 12,\n\t\t\t\t    xs: 12\n\t\t\t\t},\n\t\t\t\ttypeList: [\n\t\t\t\t\t{\n\t\t\t\t\t  activeValue: 1,\n\t\t\t\t\t  title: '指定商品'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  activeValue: 2,\n\t\t\t\t\t  title: '指定品牌'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  activeValue: 3,\n\t\t\t\t\t  title: '指定分类'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  activeValue: 4,\n\t\t\t\t\t  title: '商品标签'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tbrandData:[],\n\t\t\t\ttreeSelect:[],\n\t\t\t\ttabIndex:1\n            }\n        },\n        mounted () {\n            this.$nextTick(() => {\n                this.defaults = this.configObj\n                this.configData = this.configObj[this.configNme]\n\t\t\t\tthis.getBrandList();\n\t\t\t\tthis.goodsCategory();\n            })\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n\t\t\t\t\tthis.tabIndex = nVal.styleConfig.tabVal\n                },\n                deep: true\n            }\n        },\n        methods: {\n\t\t\t// 点击图文封面\n\t\t\tmodalPicTap (title) {\n\t\t\t    this.modalPic = true;\n\t\t\t},\n\t\t\tbindPicDelete () {\n\t\t\t    this.configData.list[this.configData.tabCur].image = '';\n\t\t\t},\n\t\t\t// 获取图片信息\n\t\t\tgetPic (pc) {\n\t\t\t    this.$nextTick(() => {\n\t\t\t        this.configData.list[this.configData.tabCur].image = pc.att_dir;\n\t\t\t        this.modalPic = false;\n\t\t\t    })\n\t\t\t},\n\t\t\tgetBrandList(){\n\t\t\t  brandList().then(res=>{\n\t\t\t    this.brandData = res.data\n\t\t\t  }).catch(err=>{\n\t\t\t    this.$Message.error(err.msg);\n\t\t\t  })\n\t\t\t},\n\t\t\tgoodsCategory() {\n\t\t\t  cascaderListApi(1)\n\t\t\t    .then((res) => {\n\t\t\t      this.treeSelect = res.data;\n\t\t\t    })\n\t\t\t    .catch((res) => {\n\t\t\t      this.$Message.error(res.msg);\n\t\t\t    });\n\t\t\t},\n\t\t\topenGoods(){\n\t\t\t\tthis.modals = true;\n\t\t\t},\n\t\t\t//对象数组去重；\n\t\t\tunique(arr) {\n\t\t\t    const res = new Map();\n\t\t\t    return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1))\n\t\t\t},\n\t\t\tgetProductId (data) {\n\t\t\t    this.modals = false;\n\t\t\t    let list = this.configData.list[this.configData.tabCur].goodsList.list.concat(data);\n\t\t\t    this.configData.list[this.configData.tabCur].goodsList.list = this.unique(list);\n\t\t\t},\n\t\t\tcancel () {\n\t\t\t    this.modals = false;\n\t\t\t},\n\t\t\tbindGoodDelete (index) {\n\t\t\t    this.configData.list[this.configData.tabCur].goodsList.list.splice(index, 1)\n\t\t\t},\n\t\t\topenStoreLabel(row,index) {\n\t\t\t  this.storeLabelShow = true;\n\t\t\t  this.$refs.storeLabel.storeLabel(JSON.parse(JSON.stringify(row)));\n\t\t\t},\n\t\t\tcloseStoreLabel(label){\n\t\t\t  let list = this.configData.list[this.configData.tabCur].goodsLabel.list;\n\t\t\t  let index = list.indexOf(list.filter(d=>d.id == label.id)[0]);\n\t\t\t  list.splice(index,1);\n\t\t\t  this.getLabelId(list);\n\t\t\t},\n\t\t\tactiveStoreData(storeDataLabel){\n\t\t\t  this.storeLabelShow = false;\n\t\t\t  this.configData.list[this.configData.tabCur].goodsLabel.list = storeDataLabel;\n\t\t\t  this.getLabelId(storeDataLabel);\n\t\t\t},\n\t\t\tgetLabelId(storeDataLabel){\n\t\t\t  let storeActiveIds = [];\n\t\t\t  storeDataLabel.forEach((item)=>{\n\t\t\t    storeActiveIds.push(item.id)\n\t\t\t  });\n\t\t\t  this.configData.list[this.configData.tabCur].goodsLabel.activeValue = storeActiveIds;\n\t\t\t  this.$emit('getConfig', { name: 'goodsLabel'})\n\t\t\t},\n\t\t\t// 标签弹窗关闭\n\t\t\tstoreLabelClose() {\n\t\t\t  this.storeLabelShow = false;\n\t\t\t},\n            addHotTxt () {\n                if (this.configData.list.length == 0) {\n                    let storage = window.localStorage;\n                    this.itemObj = JSON.parse(storage.getItem('itemObj'));\n                    if(this.itemObj.link){\n                        this.itemObj.link.activeVal = 0;\n                    }\n                    this.itemObj.chiild[0].val='首发新品';\n                    this.itemObj.chiild[1].val='最新出炉';\n\t\t\t\t\tthis.itemObj.tabVal = 0;\n\t\t\t\t\tthis.itemObj.selectConfig.activeValue = [];\n\t\t\t\t\tthis.itemObj.goodsLabel.activeValue = [];\n\t\t\t\t\tthis.itemObj.goodsLabel.list = [];\n\t\t\t\t\tthis.itemObj.goodsSort = 0;\n\t\t\t\t\tthis.itemObj.numConfig.val = 6;\n\t\t\t\t\tthis.itemObj.goodsList.list = [];\n\t\t\t\t\tthis.itemObj.productList.list = [];\n                    this.configData.list.push(this.itemObj);\n                } else {\n                    let obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]));\n                    if(obj.chiild[0].empty){\n                        obj.chiild[0].val='';\n                        obj.chiild[1].val='';\n                    }\n\t\t\t\t\tobj.tabVal = 0;\n\t\t\t\t\tobj.selectConfig.activeValue = [];\n\t\t\t\t\tobj.goodsLabel.activeValue = [];\n\t\t\t\t\tobj.goodsLabel.list = [];\n\t\t\t\t\tobj.goodsSort = 0;\n\t\t\t\t\tobj.numConfig.val = 6;\n\t\t\t\t\tobj.goodsList.list = [];\n\t\t\t\t\tobj.productList.list = [];\n                    this.configData.list.push(obj);\n                }\n            },\n            // 删除数组\n            bindDelete (index) {\n                if (this.configData.list.length == 1) {\n                    let itemObj = this.configData.list[0];\n                    this.itemObj = itemObj;\n                    let storage = window.localStorage;\n                    storage.setItem('itemObj', JSON.stringify(itemObj));\n                }\n                this.configData.list.splice(index, 1)\n                this.configData.tabCur = 0;\n                this.$emit('getConfig', { name: 'delete', indexs: 0})\n            },\n            activeBtn (index) {\n                this.configData.tabCur = index;\n                // this.$emit('getConfig', { name: 'product', indexs: index })\n            },\n\t\t\tradioChange (e) {\n\t\t\t\tthis.$emit('getConfig', { name: 'promotion', values: e })\n\t\t\t},\n\t\t\t// 品牌\n\t\t\tbrandChange(){\n\t\t\t\tthis.$emit('getConfig',{ name: 'brands'})\n\t\t\t},\n\t\t\t//商品分类\n\t\t\tsliderChange (e) {\n\t\t\t\tthis.configData.list[this.configData.tabCur].selectConfig.activeValue = e;\n\t\t\t\tthis.$emit('getConfig', { name: 'cascader', values: e })\n\t\t\t},\n\t\t\ttabChange(e){\n\t\t\t\tthis.$emit('getConfig', e)\n\t\t\t}\n        }\n    }\n", null]}