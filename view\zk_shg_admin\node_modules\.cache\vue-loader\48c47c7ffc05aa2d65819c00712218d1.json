{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\commission\\handle\\commissionDetails.vue?vue&type=template&id=700d21b2&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\commission\\handle\\commissionDetails.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div style=\"width: 100%\">\n    <Modal v-model=\"modals\" scrollable footer-hide closable title=\"用户详情\" :mask-closable=\"false\"\n           width=\"700\">\n        <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\n        <div class=\"acea-row\">\n            <div class=\"dashboard-workplace-header-tip\">\n                <div class=\"dashboard-workplace-header-tip-desc\">\n                    <span class=\"dashboard-workplace-header-tip-desc-sp\">姓名：{{detailsData.nickname}}</span>\n                    <!-- <span class=\"dashboard-workplace-header-tip-desc-sp\">上级推广人：{{detailsData.spread_name}}</span> -->\n                    <span class=\"dashboard-workplace-header-tip-desc-sp\">上级推广人：{{detailsData.spread_name?detailsData.spread_name:'无'}}</span>\n                    <span class=\"dashboard-workplace-header-tip-desc-sp\">佣金总收入：{{detailsData.number}}</span>\n                    <span class=\"dashboard-workplace-header-tip-desc-sp\">用户余额：{{detailsData.now_money}}</span>\n                    <span class=\"dashboard-workplace-header-tip-desc-sp\">创建时间：{{detailsData.add_time}}</span>\n                </div>\n            </div>\n        </div>\n        <Divider dashed/>\n        <Form ref=\"formValidate\" :label-width=\"labelWidth\" :label-position=\"labelPosition\" class=\"tabform\" @submit.native.prevent>\n            <Row :gutter=\"24\" type=\"flex\">\n                <!-- <Col span=\"8\">\n                    <FormItem label=\"订单号/昵称：\">\n                        <Input enter-button placeholder=\"请输入\" element-id=\"name\" v-model=\"formValidate.nickname\"\n                               clearable/>\n                    </FormItem>\n                </Col> -->\n                <Col span=\"12\">\n                    <FormItem label=\"时间范围：\" class=\"tab_data\">\n                        <DatePicker :editable=\"false\" style=\"width: 100%\" @on-change=\"onchangeTime\" format=\"yyyy-MM-dd\"\n                                    type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\"></DatePicker>\n                    </FormItem>\n                </Col>\n                <Col span=\"4\">\n                        <Button type=\"primary\" icon=\"ios-search\" @click=\"userSearchs\">搜索</Button>\n                </Col>\n            </Row>\n        </Form>\n        <!-- <Divider dashed/> -->\n        <Table :columns=\"columns\" :data=\"tabList\"\n               ref=\"table\"\n               :loading=\"loading\"\n               no-userFrom-text=\"暂无数据\"\n               no-filtered-userFrom-text=\"暂无筛选结果\"\n               class=\"table\"\n        ></Table>\n        <div class=\"acea-row row-right page\">\n            <Page :total=\"total\" :current=\"formValidate.page\" show-elevator show-total @on-change=\"pageChange\"\n                  :page-size=\"formValidate.limit\"/>\n        </div>\n    </Modal>\n</div>\n", null]}