{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_new_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_new_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    import { categoryList } from '@/api/diy'\n    import { cmsListApi } from '@/api/cms'\n    export default {\n        name: 'c_home_bargain',\n        componentsName: 'home_bargain',\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ],\n\t\t\t\toneStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'filletImg'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'nameConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'likeSuccessColor'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tthreeStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'nameColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'timeColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'browseColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'likeColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'statisticColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsetUp:0,\n\t\t\t\ttype:0\n            }\n        },\n        watch: {\n            num (nVal) {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n                this.categoryList();\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n\t\t\t\t\tthis.setUp = nVal;\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n                        let tempArr = [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t\t\t    configNme: 'styleConfig'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleArticle'\n\t\t\t\t\t\t\t},\n                            {\n                                components: toolCom.c_select,\n                                configNme: 'selectConfig'\n                            },\n                            {\n                                components: toolCom.c_input_number,\n                                configNme: 'numConfig'\n                            },\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\t\t\tconfigNme: 'titleList'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcomponents: toolCom.c_checkbox,\n\t\t\t\t\t\t\t\tconfigNme: 'checkboxList'\n\t\t\t\t\t\t\t},\n                        ]\n                        this.rCom = arr.concat(tempArr)\n                    } else {\n\t\t\t\t\t\tif(this.type){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle];\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle];\n\t\t\t\t\t\t}\n                    }\n                },\n                deep: true\n            },\n\t\t\t'configObj.toneConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tif(nVal){\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.twoStyle,...this.threeStyle];\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tthis.rCom = [...arr,...this.oneStyle,...this.threeStyle];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n                this.categoryList();\n            })\n        },\n        methods: {\n            categoryList () {\n                categoryList().then(res => {\n                    let data = [];\n                    res.data.map(item => {\n                        data.push({ title: item.title, activeValue: item.id.toString() });\n                    });\n                    this.configObj.selectConfig.list = data;\n                    // this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: this.pageData })\n                }).catch(err=>{\n                    this.$Message.error(err.msg);\n                });\n            },\n            // 获取组件参数\n            getConfig (data) {\n                if( data.name=='radio'){\n                    return;\n                }\n                let val = {\n                    pid: parseInt(this.configObj.selectConfig.activeValue),\n                    page: 1,\n                    limit: parseInt(this.configObj.numConfig.val)\n                };\n                cmsListApi(val).then(res=> {\n                    this.configObj.selectList.list = res.data.list;\n                }).catch(err=>{\n                    this.$Message.error(err.msg);\n                });\n            },\n            handleSubmit (name) {\n                let obj = {}\n                obj.activeIndex = this.activeIndex\n                obj.data = this.configObj\n                this.add(obj);\n            },\n            ...mapMutations({\n                add: 'admin/mobildConfig/UPDATEARR'\n            })\n        }\n    }\n", null]}