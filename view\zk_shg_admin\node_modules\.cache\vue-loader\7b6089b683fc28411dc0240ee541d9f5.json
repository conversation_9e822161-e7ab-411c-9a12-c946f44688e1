{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\attrList.vue?vue&type=template&id=80e1fd3c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\attrList.vue", "mtime": 1642123010000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"label-wrapper\"},[_c('div',{staticClass:\"list-box\"},_vm._l((_vm.attrs),function(item,index){return _c('div',{key:index,staticClass:\"label-box\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(item.value))]),_c('div',{staticClass:\"list\"},_vm._l((item.details),function(label,j){return _c('div',{key:j,staticClass:\"label-item\",class:label.select?'on':'',on:{\"click\":function($event){return _vm.selectAttr(label,j)}}},[_vm._v(_vm._s(label.name))])}),0)])}),0),_c('div',{staticClass:\"footer\"},[_c('Button',{staticClass:\"btns\",attrs:{\"type\":\"primary\",\"ghost\":\"\"},on:{\"click\":_vm.cancel}},[_vm._v(\"取消\")]),_c('Button',{staticClass:\"btns\",attrs:{\"type\":\"primary\",\"ghost\":\"\"},on:{\"click\":_vm.reset}},[_vm._v(\"重置\")]),_c('Button',{staticClass:\"btns\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.subBtn}},[_vm._v(\"确定\")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}