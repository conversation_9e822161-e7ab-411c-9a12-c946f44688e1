{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableList.vue?vue&type=template&id=4efb1b1a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\components\\tableList.vue", "mtime": 1733823002405}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 订单列表-表格组件 -->\n  <div>\n    <!-- :loading=\"loading\" -->\n    <!-- tab切换栏 -->\n    <div class=\"new_tab\">\n      <Tabs v-model=\"orderDataStatus\" @on-click=\"selectChange2\">\n        <!-- <TabPane :label=\" '全部订单（'+(tablists.all || 0)+'）'\" name=\" \"/>\n          <TabPane :label=\" '普通订单 ('+(tablists.general || 0)+')'\" name=\"0\"/>\n          <TabPane :label=\" '拼团订单 ('+(tablists.pink || 0)+')'\" name=\"3\"/>\n          <TabPane :label=\" '秒杀订单 ('+(tablists.seckill || 0)+')'\" name=\"1\"/>\n          <TabPane :label=\" '砍价订单 ('+(tablists.bargain || 0)+')'\" name=\"2\"/> -->\n        <!-- <TabPane :label=\"'所有订单 (' + (tablists.all || 0) + ')'\" name=\"-1\" />\n        <TabPane :label=\"'平台订单 (' + (tablists.plat || 0) + ')'\" name=\"0\" />\n        <TabPane :label=\"'门店订单 (' + (tablists.store || 0) + ')'\" name=\"1\" />\n        <TabPane\n          :label=\"'供应商订单 (' + (tablists.supplier || 0) + ')'\"\n          name=\"2\"\n        /> -->\n        <TabPane :label=\"'全部'\" name=\" \"/>\n        <TabPane :label=\"'待支付('+(tablists.unpaid || 0)+')'\" name=\"0\"/>\n        <TabPane :label=\"'待发货('+(tablists.unshipped || 0)+')'\" name=\"1\"/>\n        <TabPane :label=\"'待核销('+(tablists.write_off || 0)+')'\" name=\"5\"/>\n        <TabPane :label=\"'待收货'\" name=\"2\"/>\n        <TabPane :label=\"'待评价'\" name=\"3\"/>\n        <TabPane :label=\"'已核销'\" name=\"6\"/>\n        <TabPane :label=\"'已完成'\" name=\"4\"/>\n        <TabPane :label=\"'已退款'\" name=\"-2\"/>\n      </Tabs>\n    </div>\n    <div class=\"acea-row row-between\">\n      <!-- 相关操作 -->\n      <div>\n        <Tooltip\n          content=\"本页至少选中一项\"\n          :disabled=\"!!checkUidList.length && isAll==0\"\n          v-auth=\"['order-batch-del_orders']\"\n        >\n          <Button\n            class=\"mr10\"\n            type=\"primary\"\n            :disabled=\"!checkUidList.length && isAll==0\"\n            @click=\"delAll\"\n            >批量删除订单</Button\n          >\n        </Tooltip>\n        <Button\n          class=\"mr10\"\n          v-auth=\"['order-hand-batch_delivery']\"\n          type=\"primary\"\n          @click=\"manualModal = true\"\n          >手动批量发货</Button\n        >\n        <Tooltip\n          content=\"本页至少选中一项\"\n          :disabled=\"!!checkUidList.length && isAll==0\"\n          v-auth=\"['order-other-batch_delivery']\"\n        >\n          <Button\n            class=\"mr10\"\n            type=\"primary\"\n            :disabled=\"!checkUidList.length && isAll==0\"\n            @click=\"onAuto\"\n            >自动批量发货</Button\n          >\n        </Tooltip>\n        <Tooltip\n          content=\"本页至少选中一项\"\n          :disabled=\"!!checkUidList.length && isAll==0\"\n        >\n          <Button\n            class=\"mr10\"\n            type=\"primary\"\n            :disabled=\"!checkUidList.length && isAll==0\"\n            @click=\"printOreder\"\n            >打印配货单</Button\n          >\n        </Tooltip>\n        <Dropdown\n          v-auth=\"['export-storeOrder']\"\n          class=\"mr10\"\n          @on-click=\"exports\"\n        >\n          <Button style=\"width: 110px\">\n            {{ exportList[exportListOn].label }}\n            <Icon type=\"ios-arrow-down\"></Icon>\n          </Button>\n          <DropdownMenu slot=\"list\">\n            <DropdownItem\n              v-for=\"(item, index) in exportList\"\n              :key=\"index\"\n              :name=\"item.name\"\n              style=\"font-size: 12px !important\"\n              >{{ item.label }}</DropdownItem\n            >\n          </DropdownMenu>\n        </Dropdown>\n        <Button\n          v-auth=\"['order-write']\"\n          class=\"mr10 greens\"\n          size=\"default\"\n          @click=\"writeOff\"\n          >订单核销</Button\n        >\n      </div>\n      <div class=\"caozuo\">\n        <Button v-auth=\"['queue-index']\" class=\"mr10\" @click=\"queuemModal\"\n          >批量发货记录</Button\n        >\n        <Button\n          v-auth=\"['export-expressList']\"\n          class=\"mr10\"\n          @click=\"getExpressList\"\n          >下载物流公司对照表</Button\n        >\n      </div>\n    </div>\n    <!-- 订单列表表格 -->\n    <vxe-table\n        ref=\"xTable\"\n        class=\"mt25\"\n        :loading=\"loading\"\n        row-id=\"id\"\n        :expand-config=\"{accordion: true}\"\n        :checkbox-config=\"{reserve: true}\"\n        @checkbox-all=\"checkboxAll\"\n        @checkbox-change=\"checkboxItem\"\n        :data=\"orderList\">\n      <vxe-column type=\"\" width=\"0\"></vxe-column>\n      <vxe-column type=\"expand\" width=\"35\">\n        <template #content=\"{ row }\">\n          <div class=\"tdinfo\">\n            <Row class=\"expand-row\">\n              <Col span=\"8\">\n                <span class=\"expand-key\">商品总价：</span>\n                <span class=\"expand-value\" v-text=\"row.total_price\"></span>\n              </Col>\n              <Col span=\"8\">\n                <span class=\"expand-key\">下单时间：</span>\n                <span class=\"expand-value\" v-text=\"row.add_time\"></span>\n              </Col>\n              <Col span=\"8\">\n                <span class=\"expand-key\">推广人：</span>\n                <span class=\"expand-value\" v-text=\"row.spread_nickname?row.spread_nickname:'无'\"></span>\n              </Col>\n            </Row>\n            <Row class=\"expand-row\">\n              <Col span=\"8\">\n                <span class=\"expand-key\">用户备注：</span>\n                <span class=\"expand-value\" v-text=\"row.mark?row.mark:'无'\"></span>\n              </Col>\n              <Col span=\"8\">\n                <span class=\"expand-key\">商家备注：</span>\n                <span class=\"expand-value\" v-text=\"row.remark?row.remark:'无'\"></span>\n              </Col>\n            </Row>\n          </div>\n        </template>\n      </vxe-column>\n      <vxe-column type=\"checkbox\" width=\"100\">\n        <template #header>\n          <div>\n            <Dropdown transfer @on-click=\"allPages\">\n              <a href=\"javascript:void(0)\" class=\"acea-row row-middle\">\n                <span>全选({{isAll==1?(page.total-checkUidList.length):checkUidList.length}})</span>\n                <Icon type=\"ios-arrow-down\"></Icon>\n              </a>\n              <template #list>\n                <DropdownMenu>\n                  <DropdownItem name=\"0\">当前页</DropdownItem>\n                  <DropdownItem name=\"1\">所有页</DropdownItem>\n                </DropdownMenu>\n              </template>\n            </Dropdown>\n          </div>\n        </template>\n      </vxe-column>\n      <vxe-column field=\"order_id\" title=\"订单号\" min-width=\"175\">\n        <template v-slot=\"{ row }\">\n          <Tooltip\n\t\t      :transfer=\"true\"\n              theme=\"dark\"\n              max-width=\"300\"\n              :delay=\"600\"\n              content=\"用户已删除\"\n              v-if=\"row.is_del === 1 && row.delete_time == null\"\n          >\n            <span style=\"color: #ed4014; display: block\">{{ row.order_id }}</span>\n          </Tooltip>\n          <span\n              @click=\"changeMenu(row, '2')\"\n              v-else\n              style=\"color: #2d8cf0; display: block; cursor: pointer\"\n          >{{ row.order_id }}</span\n          >\n        </template>\n      </vxe-column>\n      <vxe-column field=\"pink_name\" title=\"订单类型\" min-width=\"120\"></vxe-column>\n      <vxe-column field=\"nickname\" title=\"用户信息\" min-width=\"130\">\n        <template v-slot=\"{ row }\">\n          <a @click=\"showUserInfo(row)\">{{ row.nickname }}</a>\n          <span style=\"color: #ed4014\" v-if=\"row.delete_time != null\">\n          (已注销)</span>\n        </template>\n      </vxe-column>\n      <vxe-column field=\"info\" title=\"商品信息\" min-width=\"330\">\n        <template v-slot=\"{ row }\">\n          <Tooltip :transfer=\"true\" theme=\"dark\" max-width=\"300\" :delay=\"600\">\n            <div class=\"tabBox\" v-for=\"(val, i) in row._info\" :key=\"i\">\n              <div class=\"tabBox_img\" v-viewer>\n                <img v-lazy=\"val.cart_info.productInfo.attrInfo? val.cart_info.productInfo.attrInfo.image: val.cart_info.productInfo.image\" />\n              </div>\n              <span class=\"tabBox_tit line1\">\n              <span class=\"font-color-red\" v-if=\"val.cart_info.is_gift\"\n              >赠品</span>\n\n              {{ val.cart_info.productInfo.store_name + ' | ' }}\n              {{val.cart_info.productInfo.attrInfo?val.cart_info.productInfo.attrInfo.suk: ''}} </span>\n            </div>\n            <div slot=\"content\">\n              <div v-for=\"(val, i) in row._info\" :key=\"i\">\n                <p class=\"font-color-red\" v-if=\"val.cart_info.is_gift\">赠品</p>\n                <p>{{ val.cart_info.productInfo.store_name }}</p>\n                <p> {{ val.cart_info.productInfo.attrInfo? val.cart_info.productInfo.attrInfo.suk: ''}}</p>\n                <p class=\"tabBox_pice\">{{'￥' + val.cart_info.sum_price +' x ' + val.cart_info.cart_num }} </p>\n              </div>\n            </div>\n          </Tooltip>\n        </template>\n      </vxe-column>\n      <vxe-column field=\"pay_price\" title=\"实际支付\" align=\"center\" min-width=\"70\">\n        <template v-slot=\"{ row }\">\n          <span>{{ row.paid > 0 ? row.pay_price : 0 }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column field=\"_pay_time\" title=\"支付时间\" min-width=\"150\"></vxe-column>\n      <vxe-column field=\"pay_type_name\" title=\"支付类型\" min-width=\"100\">\n        <template v-slot=\"{ row }\">\n          <span>{{ row.pay_type_name }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column field=\"statusName\" title=\"订单状态\" min-width=\"100\">\n        <template v-slot=\"{ row }\">\n          <Tag color=\"default\" size=\"medium\" v-show=\"row.status == 3\">{{\n              row.status_name.status_name\n            }}</Tag>\n          <Tag color=\"orange\" size=\"medium\" v-show=\"row.status == 4\">{{\n              row.status_name.status_name\n            }}</Tag>\n          <Tag\n              color=\"orange\"\n              size=\"medium\"\n              v-show=\"row.status == 1 || row.status == 2 || row.status == 5\"\n          >{{ row.status_name.status_name }}</Tag>\n          <Tag color=\"primary\" size=\"medium\" v-if=\"row.status_name.status_name == '未核销' || row.status_name.status_name == '未发货'\">{{\n              row.status_name.status_name\n            }}</Tag>\n            <Tag color=\"red\" size=\"medium\" v-if=\"row.status == 0 && row.status_name.status_name != '未核销' && row.status_name.status_name != '未发货'\">{{\n              row.status_name.status_name\n            }}</Tag>\n\n          <Tag\n              color=\"orange\"\n              size=\"medium\"\n              v-if=\"!row.is_all_refund && row.refund.length\"\n          >部分退款中</Tag\n          >\n          <Tag\n              color=\"orange\"\n              size=\"medium\"\n              v-if=\"row.is_all_refund && row.refund.length && row.refund_type != 6\"\n          >退款中</Tag\n          >\n          <div class=\"pictrue-box\" size=\"medium\" v-if=\"row.status_name.pics\">\n            <div\n                v-viewer\n                v-for=\"(item, index) in row.status_name.pics || []\"\n                :key=\"index\"\n            >\n              <img class=\"pictrue mr10\" v-lazy=\"item\" :src=\"item\" />\n            </div>\n          </div>\n        </template>\n      </vxe-column>\n      <vxe-column field=\"action\" title=\"操作\" align=\"center\" width=\"140\" fixed=\"right\">\n        <template v-slot=\"{ row }\">\n          <a\n          @click=\"bindWrite(row)\"\n          v-if=\"\n            row.shipping_type == 2 &&\n            row.status == 0 &&\n            row.paid == 1 &&\n            row.refund_status === 0\n          \"\n          >立即核销  </a> \n          <a\n              :disabled=\"openErp\"\n              @click=\"sendOrder(row)\"\n              v-if=\"\n            (row._status === 2 || row._status === 8 || row.status === 4) &&\n            row.shipping_type === 1 &&\n            (row.pinkStatus === null || row.pinkStatus === 2) &&\n            row.delete_time == null &&\n            row.store_id === 0 &&\n            row.supplier_id === 0\n          \"\n          >发送货</a\n          >\n          <a\n              :disabled=\"openErp\"\n              @click=\"btnClick(row)\"\n              v-if=\"row.supplier_id!==0 && row.status_name.status_name == '未发货'\"\n          >提醒发货</a\n          >\n          <Divider\n              type=\"vertical\"\n              v-if=\"row.supplier_id!==0 && row.status_name.status_name == '未发货'\"\n          />\n          <!--    -->\n          <Divider\n              type=\"vertical\"\n              v-if=\"\n            (row._status === 2 || row._status === 8 || row.status === 4) &&\n            row.shipping_type === 1 &&\n            (row.pinkStatus === null || row.pinkStatus === 2) &&\n            row.delete_time == null &&\n            row.store_id === 0 &&\n            row.supplier_id === 0\n          \"\n          />\n          <a @click=\"changeMenu(row, '2')\">详情</a> \n        </template>\n      </vxe-column>\n    </vxe-table>\n    <div class=\"acea-row row-right page\">\n      <Page\n          :total=\"page.total\"\n          :current=\"page.pageNum\"\n          show-elevator\n          show-total\n          @on-change=\"pageChange\"\n          :page-size=\"page.pageSize\"\n          @on-page-size-change=\"limitChange\"\n          show-sizer\n      />\n    </div>\n    <!-- 分配 -->\n    <Distribution ref=\"distshow\"></Distribution>\n    <!-- 编辑 退款 退积分 不退款-->\n    <edit-from\n      ref=\"edits\"\n      :FromData=\"FromData\"\n      @submitFail=\"submitFail\"\n    ></edit-from>\n    <!-- 会员详情-->\n    <user-details ref=\"userDetails\" fromType=\"order\"></user-details>\n    <!-- 详情 -->\n    <details-from\n      ref=\"detailss\"\n      :orderDatalist=\"orderDatalist\"\n      :orderId=\"orderId\"\n      :row-active=\"rowActive\"\n      :openErp=\"openErp\"\n      :formType=\"1\"\n    ></details-from>\n    <!-- 备注 -->\n    <order-remark\n      ref=\"remarks\"\n      :orderId=\"orderId\"\n      @submitFail=\"submitFail\"\n    ></order-remark>\n    <!-- 记录 -->\n    <order-record ref=\"record\"></order-record>\n    <!-- 发送货 -->\n    <order-send\n      ref=\"send\"\n      :orderId=\"orderId\"\n      :status=\"status\"\n      :pay_type=\"pay_type\"\n      @submitFail=\"submitFail(1)\"\n    ></order-send>\n    <Modal\n      v-model=\"manualModal\"\n      title=\"手动批量发货\"\n      @on-ok=\"manualModalOk\"\n      @on-cancel=\"manualModalCancel\"\n      class-name=\"vertical-center-modal\"\n    >\n      <Row type=\"flex\">\n        <Col span=\"4\">\n          <div style=\"line-height: 32px; text-align: right\">文件：</div>\n        </Col>\n        <Col span=\"20\">\n          <Upload\n            ref=\"upload\"\n            :action=\"uploadAction\"\n            :headers=\"uploadHeaders\"\n            accept=\".xlsx,.xls\"\n            :format=\"['xlsx', 'xls']\"\n            :disabled=\"!!fileList.length\"\n            :on-success=\"uploadSuccess\"\n            :on-remove=\"removeFile\"\n          >\n            <Button icon=\"ios-cloud-upload-outline\">上传文件</Button>\n          </Upload>\n        </Col>\n      </Row>\n    </Modal>\n    <!--订单核销模态框-->\n    <Modal\n      v-model=\"modals2\"\n      title=\"订单核销\"\n      class=\"paymentFooter\"\n      scrollable\n      width=\"400\"\n      class-name=\"vertical-center-modal\"\n    >\n      <Form\n        ref=\"writeOffFrom\"\n        :model=\"writeOffFrom\"\n        :rules=\"writeOffRules\"\n        :label-position=\"labelPosition\"\n        class=\"tabform\"\n        @submit.native.prevent\n      >\n        <FormItem prop=\"code\" label-for=\"code\">\n          <Input\n            search\n            enter-button=\"验证\"\n            style=\"width: 100%\"\n            type=\"text\"\n            placeholder=\"请输入12位核销码\"\n            @on-search=\"search('writeOffFrom')\"\n            v-model.number=\"writeOffFrom.code\"\n            number\n          />\n        </FormItem>\n      </Form>\n      <div slot=\"footer\">\n        <Button type=\"primary\" @click=\"ok\">立即核销</Button>\n        <Button @click=\"del('writeOffFrom')\">取消</Button>\n      </div>\n    </Modal>\n    <auto-send ref=\"sends\" :selectArr=\"checkUidList\" :isAll=\"isAll\"></auto-send>\n    <queue-list ref=\"queue\"></queue-list>\n    <Modal v-model=\"refundModal\" title=\"手动退款\" width=\"960\" class-name=\"refund-modal\" @on-visible-change=\"visibleChange\">\n      <Form :label-width=\"100\">\n        <FormItem label=\"退款金额：\" required>\n          <InputNumber v-model=\"refundMoney\" style=\"width: 408px;\"></InputNumber>\n        </FormItem>\n        <FormItem v-if=\"refundProductNum > 1\" label=\"分单退款：\">\n          <i-switch v-model=\"is_split_order\" :true-value=\"1\" :false-value=\"0\" size=\"large\">\n            <span slot=\"open\">开启</span>\n            <span slot=\"close\">关闭</span>\n          </i-switch>\n          <div class=\"tips\">可选择表格中的商品单独退款，退款后且不能撤回，请谨慎操作！</div>\n          <Table v-show=\"is_split_order\" ref=\"refundTable\" max-height=\"500\" :columns=\"refundColumns\" :data=\"refundProduct\" @on-selection-change=\"refundSelectionChange\">\n            <template slot-scope=\"{ row }\" slot=\"product\">\n              <div class=\"image-wrap\" v-viewer><img :src=\"row.productInfo.attrInfo.image\" class=\"image\"></div>\n              <div class=\"title\">{{ row.productInfo.store_name }}</div>\n            </template>\n            <template slot-scope=\"{ row }\" slot=\"action\">\n              <InputNumber v-model=\"row.refundNum\" :max=\"row.cart_num - row.refund_num\" :min=\"1\" :precision=\"0\" controls-outside @on-change=\"refundNumChange(row)\"></InputNumber>\n            </template>\n          </Table>\n        </FormItem>\n      </Form>\n      <div slot=\"footer\">\n        <Button @click=\"cancelRefundModal\">取消</Button>\n        <Button type=\"primary\" @click=\"putOpenRefund\">提交</Button>\n      </div>\n    </Modal>\n  </div>\n", null]}