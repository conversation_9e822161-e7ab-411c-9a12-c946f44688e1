<?php
/**
 * 调拨单模型
 * User: 系统生成
 * Date: <?= date('Y-m-d H:i:s') ?>
 */

namespace app\model\erp;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\Model;

/**
 * 调拨单模型
 * Class TransferOrder
 * @package app\model\erp
 */
class TransferOrder extends BaseModel
{
    use ModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'transfer_order';
    
    /**
     * 自动时间戳字段
     * @var bool
     */
    protected $autoWriteTimestamp = false;
    
    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'create_time';
    
    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'update_time';

    /**
     * 状态常量
     */
    const STATUS_PENDING = 0; // 待审核
    const STATUS_APPROVED = 1; // 已审核
    const STATUS_EXECUTED = 2; // 已执行
    const STATUS_REJECTED = 3; // 已拒绝

    /**
     * 状态文字映射
     * @var array
     */
    public static $statusText = [
        self::STATUS_PENDING => '待审核',
        self::STATUS_APPROVED => '已审核',
        self::STATUS_EXECUTED => '已执行',
        self::STATUS_REJECTED => '已拒绝',
    ];

    /**
     * 获取状态文字
     * @param $value
     * @param $data
     * @return mixed|string
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::$statusText[$data['status']] ?? '未知状态';
    }

    /**
     * 申请时间获取器
     * @param $value
     * @return false|string
     */
    public function getApplyTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 审核时间获取器
     * @param $value
     * @return false|string
     */
    public function getApproveTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 执行时间获取器
     * @param $value
     * @return false|string
     */
    public function getExecuteTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 关联调出门店
     * @return \think\model\relation\HasOne
     */
    public function fromStore()
    {
        return $this->hasOne(\app\model\store\SystemStore::class, 'id', 'from_store_id')
            ->field('id,name,phone,address,detailed_address');
    }

    /**
     * 关联调入门店
     * @return \think\model\relation\HasOne
     */
    public function toStore()
    {
        return $this->hasOne(\app\model\store\SystemStore::class, 'id', 'to_store_id')
            ->field('id,name,phone,address,detailed_address');
    }

    /**
     * 关联申请人
     * @return \think\model\relation\HasOne
     */
    public function applicant()
    {
        return $this->hasOne(\app\model\system\admin\SystemAdmin::class, 'id', 'applicant_id')
            ->field('id,account,real_name');
    }

    /**
     * 关联审核人
     * @return \think\model\relation\HasOne
     */
    public function approver()
    {
        return $this->hasOne(\app\model\system\admin\SystemAdmin::class, 'id', 'approver_id')
            ->field('id,account,real_name');
    }

    /**
     * 关联调拨单明细
     * @return \think\model\relation\HasMany
     */
    public function details()
    {
        return $this->hasMany(TransferOrderDetail::class, 'transfer_id', 'id');
    }

    /**
     * 搜索器：调拨单号
     * @param $query
     * @param $value
     */
    public function searchOrderNoAttr($query, $value)
    {
        if ($value) {
            $query->where('order_no', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：调出门店
     * @param $query
     * @param $value
     */
    public function searchFromStoreIdAttr($query, $value)
    {
        if ($value) {
            $query->where('from_store_id', $value);
        }
    }

    /**
     * 搜索器：调入门店
     * @param $query
     * @param $value
     */
    public function searchToStoreIdAttr($query, $value)
    {
        if ($value) {
            $query->where('to_store_id', $value);
        }
    }

    /**
     * 搜索器：申请时间范围
     * @param $query
     * @param $value
     */
    public function searchApplyTimeAttr($query, $value)
    {
        if ($value && is_array($value) && count($value) == 2) {
            $query->whereTime('apply_time', 'between', $value);
        }
    }

    /**
     * 生成调拨单号
     * @return string
     */
    public static function generateOrderNo()
    {
        return 'TF' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 检查调拨单是否可以审核
     * @return bool
     */
    public function canApprove()
    {
        return $this->status == self::STATUS_PENDING;
    }

    /**
     * 检查调拨单是否可以执行
     * @return bool
     */
    public function canExecute()
    {
        return $this->status == self::STATUS_APPROVED;
    }

    /**
     * 检查调拨单是否已完成
     * @return bool
     */
    public function isCompleted()
    {
        return in_array($this->status, [self::STATUS_EXECUTED, self::STATUS_REJECTED]);
    }
} 