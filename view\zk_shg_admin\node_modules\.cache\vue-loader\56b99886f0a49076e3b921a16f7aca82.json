{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\bargainList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\bargainList.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n// import cardsData from '@/components/cards/cards';\nimport { mapState } from \"vuex\";\nimport { formatDate } from \"@/utils/validate\";\nimport { bargainUserListApi, bargainUserInfo<PERSON><PERSON> } from \"@/api/marketing\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: \"bargainList\",\n  filters: {\n    formatDate(time) {\n      if (time !== 0) {\n        let date = new Date(time * 1000);\n        return formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    },\n  },\n  // components: { cardsData },\n  data() {\n    return {\n      cardLists: [],\n      modals: false,\n      options: timeOptions,\n      fromList: {\n        title: \"选择时间\",\n        custom: true,\n        fromTxt: [\n          { text: \"全部\", val: \"\" },\n          { text: \"今天\", val: \"today\" },\n          { text: \"昨天\", val: \"yesterday\" },\n          { text: \"最近7天\", val: \"lately7\" },\n          { text: \"最近30天\", val: \"lately30\" },\n          { text: \"本月\", val: \"month\" },\n          { text: \"本年\", val: \"year\" },\n        ],\n      },\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 12,\n        xs: 24,\n      },\n      loading: false,\n      formValidate: {\n        status: \"\",\n        data: \"\",\n        page: 1,\n        limit: 15,\n      },\n      columns1: [\n        {\n          title: \"头像\",\n          slot: \"avatar\",\n          minWidth: 100,\n        },\n        {\n          title: \"发起用户\",\n          slot: \"nickname\",\n          minWidth: 170,\n        },\n        {\n          title: \"开启时间\",\n          key: \"add_time\",\n          minWidth: 150,\n        },\n        {\n          title: \"砍价商品\",\n          key: \"title\",\n          minWidth: 300,\n        },\n        {\n          title: \"最低价\",\n          key: \"bargain_price_min\",\n          minWidth: 120,\n        },\n        {\n          title: \"当前价\",\n          key: \"now_price\",\n          minWidth: 100,\n        },\n        {\n          title: \"总砍价次数\",\n          key: \"people_num\",\n          minWidth: 100,\n        },\n        {\n          title: \"剩余砍价次数\",\n          key: \"num\",\n          minWidth: 100,\n        },\n        {\n          title: \"结束时间\",\n          key: \"datatime\",\n          minWidth: 150,\n        },\n        {\n          title: \"状态\",\n          slot: \"status\",\n          minWidth: 100,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n          minWidth: 170,\n        },\n      ],\n      tableList: [],\n      total: 0,\n      timeVal: [],\n      loading2: false,\n      tabList3: [],\n      columns2: [\n        {\n          title: \"用户ID\",\n          key: \"uid\",\n          width: 80,\n        },\n        {\n          title: \"用户头像\",\n          slot: \"avatar\",\n        },\n        {\n          title: \"用户名称\",\n          slot: \"nickname\",\n          minWidth: 150,\n        },\n        {\n          title: \"砍价金额\",\n          key: \"price\",\n        },\n        {\n          title: \"砍价时间\",\n          key: \"add_time\",\n        },\n      ],\n      rows: {},\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 查看详情\n    Info(row) {\n      this.modals = true;\n      this.rows = row;\n      bargainUserInfoApi(row.id)\n        .then(async (res) => {\n          let data = res.data;\n          this.tabList3 = data.list;\n          // this.total = res.data.count;\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      // this.formValidate.data = this.timeVal.join('-');\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.formValidate.page = 1;\n      this.formValidate.data = tab;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      this.formValidate.status = this.formValidate.status || \"\";\n      bargainUserListApi(this.formValidate)\n        .then(async (res) => {\n          let data = res.data;\n          this.tableList = data.list;\n          this.total = res.data.count;\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n  },\n};\n", null]}