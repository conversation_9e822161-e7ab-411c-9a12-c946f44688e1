{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue?vue&type=template&id=04d2cb2e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue", "mtime": 1683692372000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\t<div class=\"goodList\">\n\t\t<Form ref=\"formValidate\" :model=\"formValidate\" :label-width=\"labelWidth\" :label-position=\"labelPosition\"\n\t\t\tclass=\"tabform\">\n\t\t\t<Row type=\"flex\" :gutter=\"24\">\n\t\t\t\t<Col v-bind=\"grid\">\n\t\t\t\t<FormItem label=\"商品分类：\" label-for=\"pid\">\n\t\t\t\t\t<Cascader :data=\"treeSelect\" placeholder=\"请选择商品分类\" change-on-select filterable v-width=\"'200px'\"\n\t\t\t\t\t\t@on-change=\"treeSearchs\"></Cascader>\n\t\t\t\t</FormItem>\n\t\t\t\t</Col>\n\t\t\t\t<Col v-bind=\"grid\">\n\t\t\t\t<FormItem label=\"商品标签：\" label-for=\"pid\">\n\t\t\t\t\t<Select v-model=\"formValidate.store_label_id\" class=\"width20\" clearable\n\t\t\t\t\t\t@on-change=\"userSearchs\">\n\t\t\t\t\t\t<Option v-for=\"item in labelSelect\" :value=\"item.id\" :key=\"item.id\">{{ item.label_name }}\n\t\t\t\t\t\t</Option>\n\t\t\t\t\t</Select>\n\t\t\t\t</FormItem>\n\t\t\t\t</Col>\n\t\t\t\t<Col v-bind=\"grid\">\n\t\t\t\t<FormItem label=\"商品搜索：\" label-for=\"store_name\">\n\t\t\t\t\t<Input search enter-button placeholder=\"请输入商品名称,关键字,编号\" v-model=\"formValidate.store_name\"\n\t\t\t\t\t\tstyle=\"width: 240px\" @on-search=\"userSearchs\" />\n\t\t\t\t</FormItem>\n\t\t\t\t</Col>\n\t\t\t</Row>\n\t\t</Form>\n\n\t\t<div class=\"vxeTable\">\n\t\t\t<vxe-table border=\"inner\" ref=\"xTree\" :column-config=\"{resizable: true}\" row-id=\"id\"\n\t\t\t\t:tree-config=\"{children: 'attrValue',reserve:true}\" :data=\"tableList\" max-height=\"400\" :checkbox-config=\"{reserve: true}\">\n\t\t\t\t<vxe-column type=\"checkbox\" title=\"多选\" width=\"90\" tree-node></vxe-column>\n\t\t\t\t<vxe-column field=\"id\" title=\"商品ID\" width=\"80\"></vxe-column>\n\t\t\t\t<vxe-column field=\"image\" title=\"图片\" min-width=\"90\">\n\t\t\t\t\t<template v-slot=\"{ row }\">\n\t\t\t\t\t\t<viewer>\n\t\t\t\t\t\t\t<div class=\"tabBox_img\">\n\t\t\t\t\t\t\t\t<img v-lazy=\"row.image\" />\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</viewer>\n\t\t\t\t\t</template>\n\t\t\t\t</vxe-column>\n\t\t\t\t<vxe-column field=\"store_name\" title=\"商品名称\" min-width=\"190\">\n\t\t\t\t\t<template v-slot=\"{ row }\">\n\t\t\t\t\t\t<Tooltip max-width=\"500\" placement=\"bottom\">\n\t\t\t\t\t\t\t<span class=\"line2\">{{row.store_name}}</span>\n\t\t\t\t\t\t\t<p slot=\"content\">{{row.store_name}}</p>\n\t\t\t\t\t\t</Tooltip>\n\t\t\t\t\t</template>\n\t\t\t\t</vxe-column>\n\t\t\t\t<vxe-column field=\"product_type\" title=\"商品类型\" min-width=\"100\">\n\t\t\t\t\t<template v-slot=\"{ row }\">\n\t\t\t\t\t\t<span v-if=\"row.product_type==0\">普通商品</span>\n\t\t\t\t\t\t<span v-if=\"row.product_type==1\">卡密商品</span>\n\t\t\t\t\t\t<span v-if=\"row.product_type==3\">虚拟商品</span>\n            <span v-if=\"row.product_type==4\">次卡商品</span>\n\t\t\t\t\t</template>\n\t\t\t\t</vxe-column>\n\t\t\t\t<vxe-column field=\"cate_name\" title=\"商品分类\" min-width=\"150\">\n\t\t\t\t\t<template v-slot=\"{ row }\">\n\t\t\t\t\t\t<Tooltip max-width=\"200\" placement=\"bottom\">\n\t\t\t\t\t\t\t<span class=\"line2\">{{row.cate_name}}</span>\n\t\t\t\t\t\t\t<p slot=\"content\">{{row.cate_name}}</p>\n\t\t\t\t\t\t</Tooltip>\n\t\t\t\t\t</template>\n\t\t\t\t</vxe-column>\n\t\t\t\t<vxe-column field=\"store_label\" title=\"商品标签\" min-width=\"150\">\n\t\t\t\t\t<template v-slot=\"{ row }\">\n\t\t\t\t\t\t<Tooltip max-width=\"500\" placement=\"bottom\">\n\t\t\t\t\t\t\t<span class=\"line2\">{{row.store_label}}</span>\n\t\t\t\t\t\t\t<p slot=\"content\">{{row.store_label}}</p>\n\t\t\t\t\t\t</Tooltip>\n\t\t\t\t\t</template>\n\t\t\t\t</vxe-column>\n\t\t\t</vxe-table>\n\t\t\t<vxe-pager border size=\"medium\" :page-size=\"formValidate.limit\" :current-page=\"formValidate.page\" :total=\"total\"\n\t\t\t\t:layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Total']\" @page-change=\"pageChange\">\n\t\t\t</vxe-pager>\n\t\t</div>\n\t\t<div class=\"footer\" slot=\"footer\">\n\t\t\t<Button type=\"primary\" size=\"large\" long @click=\"ok\">提交</Button>\n\t\t</div>\n\t</div>\n", null]}