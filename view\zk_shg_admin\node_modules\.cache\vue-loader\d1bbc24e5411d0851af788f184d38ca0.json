{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\addClientGroup.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\addClientGroup.vue", "mtime": 1710488036000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState, mapMutations } from \"vuex\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport department from \"@/components/department/index.vue\";\nimport userLabel from \"@/components/labelList\";\nimport { workLabel, workGroupTemplateSave,workClientCount } from \"@/api/work\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport { getNewFormBuildRuleApi } from \"@/api/setting\";\nimport Setting from \"@/setting\";\nexport default {\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      formItem: {\n        template_type: \"0\", //0=立即发送，1=定时发送\n        name: \"\",\n        type: \"0\", //客户群发\n        client_type: \"0\", //0=全部客户，1=所选客户\n        where_time: \"\", //客户筛选时间\n        where_label: [], //标签\n        userids: [], //所选群发账号\n        send_time: \"\", //定时发送账号\n        welcome_words: {\n          text: {\n            content: \"\",\n          },\n          attachments: [],\n        }, //欢迎语\n      },\n      ruleValidate:{\n        name: [\n          { required: true, message: '名称不能为空', trigger: 'blur' }\n        ],\n        client_type:[\n          { required: true, message: '请选择客户类型', trigger: 'change' }\n        ],\n        template_type:[\n          { required: true, message: '请选择发送类型', trigger: 'change' }\n        ]\n      },\n      options: timeOptions,\n      timeVal: [],\n      //客户标签列表\n      labelList: [],\n      newLabelList:[],\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      rontineObj: {\n        msgtype: \"miniprogram\",\n        miniprogram: {\n          pic_url: \"\",\n          pic_media_id: \"\",\n          title: \"\",\n          appid: \"\",\n          page: \"\",\n        },\n      },\n      imageObj: {\n        msgtype: \"image\",\n        image: {\n          media_id: \"\",\n          pic_url: \"\",\n        },\n      },\n      picTit: \"\",\n      modalPic: false,\n      modalRoutine: false,\n      isChoice: \"单选\",\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: [],\n      clientCount:0,\n      labelShow: false,\n      dataLabel: [],\n      notDataLabel: [],\n    };\n  },\n  components: { uploadPictures, department, userLabel },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    },\n  },\n  watch:{\n    \"formItem.where_label\"(val,oldVal){\n      if(val !== oldVal){\n        this.getClientCount();\n      }\n    },\n    \"formItem.where_not_label\"(val,oldVal){\n      if(val !== oldVal){\n        this.getClientCount();\n      }\n    },\n    \"formItem.where_time\"(val,oldVal){\n      if(val !== oldVal){\n        this.getClientCount();\n      }\n    },\n    \"formItem.userids\"(val,oldVal){\n      if(val !== oldVal){\n        this.getClientCount();\n      }\n    },\n    dataLabel(val) {\n        this.formItem.where_label = val.map(item => item.tag_id);\n    },\n    notDataLabel(val) {\n        this.formItem.where_not_label = val.map(item => item.tag_id);\n    }\n  },\n  mounted() {\n    this.setCopyrightShow({ value: false });\n    // this.getWorkLabel();\n    this.getClientCount();\n  },\n  destroyed() {\n    this.setCopyrightShow({ value: true });\n  },\n  methods: {\n    ...mapMutations(\"admin/layout\", [\"setCopyrightShow\"]),\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.formItem.where_time = this.timeVal.join(\"-\");\n    },\n    snedChangeTime(val) {\n      this.formItem.send_time = val;\n    },\n    modalPicTap(picTit) {\n      this.modalPic = true;\n      this.picTit = picTit;\n    },\n    //获取客户标签\n    getWorkLabel() {\n      workLabel().then((res) => {\n        this.labelList = res.data.map((org) => this.mapTree(org));\n        this.newLabelList = this.deepClone(this.labelList);\n      });\n    },\n    mapTree(org) {\n      const haveChildren =\n        Array.isArray(org.children) && org.children.length > 0;\n      return {\n        //分别将我们查询出来的值做出改变他的key\n        title: org.label,\n        expand: true,\n        value: org.value,\n        selected: false,\n        checked: false,\n        children: haveChildren ? org.children.map((i) => this.mapTree(i)) : [],\n      };\n\n    },\n    addRoutine() {\n      getNewFormBuildRuleApi('routine').then(res => {\n          let data = res.data;\n          this.rontineObj.miniprogram.pic_url = '';\n          this.rontineObj.miniprogram.title = data.routine_name.value;\n          this.rontineObj.miniprogram.appid = data.routine_appId.value;\n          this.rontineObj.miniprogram.page = '/pages/index/index';\n      })\n      this.modalRoutine = true;\n    },\n    addUser() {\n      this.userList = this.formItem.userids;\n      this.$refs.department.memberStatus = true;\n    },\n    //tag标签删除成员\n\thandleDel(id) {\n\t  let index = this.formItem.userids.findIndex(function(val){\n\t      return val.id === id\n\t  })\n\t  this.formItem.userids.splice(index, 1);\n\t  this.getClientCount();\n\t},\n    //欢迎语tag删除\n    wordsDel(name) {\n      let index = this.formItem.welcome_words.attachments.indexOf(name);\n      this.formItem.welcome_words.attachments.splice(index, 1);\n    },\n    changeMastart(arr, type) {\n      this.formItem.userids = arr.map((item) => {\n        return {\n          userid: item.userid,\n          name: item.name,\n\t\t  id: item.id\n        };\n      });\n    },\n    // 选中图片\n    getPic(pc) {\n      switch (this.picTit) {\n        case \"image\":\n          this.imageObj.image.pic_url = pc.att_dir;\n          this.formItem.welcome_words.attachments.push(this.imageObj);\n          break;\n        case \"routine\":\n          this.rontineObj.miniprogram.pic_url = pc.att_dir;\n          break;\n      }\n      this.modalPic = false;\n    },\n    insertName() {\n      this.formItem.welcome_words.text.content =\n        this.formItem.welcome_words.text.content.concat(\"##客户名称##\");\n    },\n    routineConfirm() {\n      const routine = this.deepClone(this.rontineObj);\n      this.formItem.welcome_words.attachments.push(routine);\n    },\n    submit() {\n      if(!this.formItem.userids.length) return this.$Message.error(\"请选择成员\")\n      const formData = this.deepClone(this.formItem);\n      formData.userids = formData.userids.map((item) => {\n        return item.userid;\n      });\n      if (formData.client_type === '0') {\n        formData.where_time = '';\n        formData.where_label = [];\n        formData.where_not_label = [];\n      }\n      this.$refs.formItem.validate((valid) => {\n          if (valid) {\n            workGroupTemplateSave(formData).then((res) => {\n              this.$Message.success(res.msg);\n              this.$router.push(this.roterPre + \"/work/client/group\");\n            })\n            .catch((err) => {\n              this.$Message.error(err.msg);\n            });\n          }\n        })\n    },\n    //深克隆\n    deepClone(obj) {\n      let newObj = Array.isArray(obj) ? [] : {};\n      if (obj && typeof obj === \"object\") {\n        for (let key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            newObj[key] =\n              obj && typeof obj[key] === \"object\"\n                ? this.deepClone(obj[key])\n                : obj[key];\n          }\n        }\n      }\n      return newObj;\n    },\n    getClientCount(){\n      workClientCount({\n        is_all: this.formItem.client_type == 1 ? 0 : 1,\n        label:this.formItem.where_label,\n        notLabel:this.formItem.where_not_label,\n        time: this.formItem.where_time,\n        userid: this.formItem.userids.map((item) => {\n          return item.userid;\n        })\n      }).then(res=>{\n        this.clientCount = res.data.sum_count;\n      })\n    },\n    openLabel(label) {\n        this.labelActive = label;\n        this.labelShow = true;\n        this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this[label])));\n    },\n    activeData(dataLabel){\n        this.labelShow = false;\n        this[this.labelActive] = dataLabel;\n    },\n    // 标签弹窗关闭\n    labelClose() {\n        this.labelShow = false;\n    },\n    closeLabel(label,name){\n        let index = this[name].indexOf(this[name].filter(d=>d.id == label.id)[0]);\n        this[name].splice(index,1);\n    },\n  },\n};\n", null]}