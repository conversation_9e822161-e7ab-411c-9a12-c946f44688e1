{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_select_item.vue?vue&type=template&id=64304642&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_select_item.vue", "mtime": 1682663004000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"select-word\" v-if=\"configData\">\n  <div class=\"c_row-item\">\n    <Col class=\"label\" span=\"4\">\n      {{configData.title}}\n    </Col>\n    <Col span=\"19\" class=\"slider-box\">\n      <div class=\"inputs\" v-for=\"(item,index) in configData.list\" :key=\"index\">\n        <Input :icon=\"index>1?'ios-trash-outline':''\" v-model=\"item.val\" maxlength=\"10\" placeholder=\"选填，不超过十个字\" @on-click=\"bindDelete(index)\" />\n      </div>\n      <div class=\"button acea-row row-between-wrapper\" :class=\"configData.list.length==0?'on':''\">\n        <div class=\"bnt acea-row row-center-wrapper\" @click=\"addHotTxt\">\n          <span class=\"iconfont iconjia\"></span>\n          添加单个选项\n        </div>\n        <Poptip placement=\"bottom\" trigger=\"click\" width=\"256\" transfer padding=\"8px\" v-model=\"visible\">\n          <div class=\"bnt acea-row row-center-wrapper\">\n            <span class=\"iconfont iconjia\"></span>\n            批量添加选项\n          </div>\n          <div class=\"batchItem on\" slot=\"content\">\n            <div class=\"title\">批量添加选项</div>\n            <div class=\"tips\">可按回车键添加多个选项</div>\n            <Input v-model=\"batchWord\" type=\"textarea\" :autosize=\"{minRows: 3,maxRows: 5}\" />\n            <div class=\"batchBnt acea-row row-right\">\n              <Button @click.stop=\"cancel(1)\">取消</Button>\n              <Button type=\"primary\" class=\"ml10\" @click.stop=\"cancel(2)\">确定</Button>\n            </div>\n          </div>\n        </Poptip>\n      </div>\n    </Col>\n  </div>\n</div>\n", null]}