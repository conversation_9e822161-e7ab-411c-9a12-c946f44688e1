{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\refund\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\refund\\index.vue", "mtime": 1717468541000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { orderRefundList, orderList, getOrdeDatas, getDataInfo, getRefundDataInfo, getRefundFrom, getRefundOrderFrom, getnoRefund, refundIntegral, getDistribution, writeUpdate } from \"@/api/order\";\nimport { erpConfig } from \"@/api/erp\";\nimport editFrom from \"@/components/from/from\";\nimport detailsFrom from \"../orderList/handle/orderDetails\";\nimport orderRemark from \"../orderList/handle/orderRemark\";\nimport orderRecord from \"../orderList/handle/orderRecord\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  components: {\n    editFrom: editFrom,\n    detailsFrom: detailsFrom,\n    orderRemark: orderRemark,\n    orderRecord: orderRecord\n  },\n  data: function data() {\n    return {\n      openErp: false,\n      thead: [{\n        title: \"订单号\",\n        align: \"center\",\n        slot: \"order_id\",\n        minWidth: 150\n      }, {\n        title: \"用户信息\",\n        slot: \"nickname\",\n        minWidth: 130\n      }, {\n        title: \"商品信息\",\n        slot: \"info\",\n        minWidth: 300\n      }, {\n        title: \"实际支付\",\n        key: \"pay_price\",\n        minWidth: 70\n      }, {\n        title: \"发起退款时间\",\n        key: \"add_time\",\n        minWidth: 110\n      }, // {\n      //   title: \"订单类型\",\n      //   key: \"type\",\n      //   minWidth: 100,\n      // },\n      {\n        title: \"售后类型\",\n        slot: \"apply_type\",\n        minWidth: 180\n      }, {\n        title: \"退款状态\",\n        slot: \"refund_type\",\n        minWidth: 180\n      }, {\n        title: \"退款信息\",\n        slot: \"statusName\",\n        minWidth: 100\n      }, {\n        title: \"售后备注\",\n        key: \"remark\",\n        minWidth: 80\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        fixed: \"right\",\n        minWidth: 150,\n        align: \"center\"\n      }],\n      tbody: [],\n      num: [],\n      orderDatalist: null,\n      loading: false,\n      FromData: null,\n      total: 0,\n      orderId: 0,\n      animal: 1,\n      pagination: {\n        page: 1,\n        limit: 15,\n        order_id: \"\",\n        time: \"\",\n        refund_type: 0\n      },\n      options: timeOptions,\n      timeVal: [],\n      modal: false,\n      qrcode: null,\n      name: \"\",\n      spin: false,\n      rowActive: {}\n    };\n  },\n  computed: _objectSpread({}, mapState(\"order\", [\"orderChartType\"]), {\n    // ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getErpConfig();\n    this.getOrderList();\n  },\n  methods: {\n    //erp配置\n    getErpConfig: function getErpConfig() {\n      var _this = this;\n\n      erpConfig().then(function (res) {\n        _this.openErp = res.data.open_erp;\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n      });\n    },\n    onchangeCode: function onchangeCode(e) {\n      this.animal = e;\n      this.qrcodeShow();\n    },\n    // 具体日期搜索()；\n    onchangeTime: function onchangeTime(e) {\n      this.pagination.page = 1;\n      this.timeVal = e;\n      this.pagination.time = this.timeVal[0] ? this.timeVal.join(\"-\") : \"\";\n      this.getOrderList();\n    },\n    // 获取详情表单数据\n    getData: function getData(id, type) {\n      var _this2 = this;\n\n      getRefundDataInfo(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  if (!type) {\n                    _this2.$refs.detailss.modals = true;\n                  }\n\n                  _this2.$refs.detailss.activeName = \"detail\";\n                  _this2.orderDatalist = res.data; // if (this.orderDatalist.orderInfo.refund_img) {\n                  //   try {\n                  //     this.orderDatalist.orderInfo.refund_img = JSON.parse(\n                  //       this.orderDatalist.orderInfo.refund_img\n                  //     );\n                  //   } catch (e) {\n                  //     this.orderDatalist.orderInfo.refund_img = [];\n                  //   }\n                  // }\n\n                case 3:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.$Message.error(res.msg);\n      });\n    },\n    // 操作\n    changeMenu: function changeMenu(row, name) {\n      var _this3 = this;\n\n      this.orderId = row.id;\n\n      switch (name) {\n        case \"1\":\n          this.delfromData = {\n            title: \"修改立即支付\",\n            url: \"/order/pay_offline/\".concat(row.id),\n            method: \"post\",\n            ids: \"\"\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this3.$Message.success(res.msg);\n\n            _this3.getOrderList();\n          }).catch(function (res) {\n            _this3.$Message.error(res.msg);\n          }); // this.modalTitleSs = '修改立即支付';\n\n          break;\n\n        case \"2\":\n          this.rowActive = row;\n          this.getData(row.id);\n          break;\n\n        case \"3\":\n          this.$refs.record.modals = true;\n          this.$refs.record.getList(row.store_order_id);\n          break;\n\n        case \"4\":\n          this.$refs.remarks.modals = true;\n          this.$refs.remarks.formValidate.remark = row.remark;\n          break;\n\n        case \"5\":\n          this.getRefundData(row.id, row.refund_type);\n          break;\n\n        case '55':\n          this.getRefundGoodsData(row.id, row.refund_type);\n          break;\n\n        case \"6\":\n          this.getRefundIntegral(row.id);\n          break;\n\n        case \"7\":\n          this.getNoRefundData(row.id);\n          break;\n\n        case \"8\":\n          this.delfromData = {\n            title: \"修改确认收货\",\n            url: \"/order/take/\".concat(row.id),\n            method: \"put\",\n            ids: \"\"\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this3.$Message.success(res.msg);\n\n            _this3.getOrderList();\n          }).catch(function (res) {\n            _this3.$Message.error(res.msg);\n          }); // this.modalTitleSs = '修改确认收货';\n\n          break;\n\n        case \"10\":\n          this.delfromData = {\n            title: \"立即打印订单\",\n            info: \"您确认打印此订单吗?\",\n            url: \"/order/print/\".concat(row.id),\n            method: \"get\",\n            ids: \"\"\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this3.$Message.success(res.msg);\n\n            _this3.$emit(\"changeGetTabs\");\n\n            _this3.getOrderList();\n          }).catch(function (res) {\n            _this3.$Message.error(res.msg);\n          });\n          break;\n\n        case \"11\":\n          this.delfromData = {\n            title: \"立即打印电子面单\",\n            info: \"您确认打印此电子面单吗?\",\n            url: \"/order/order_dump/\".concat(row.id),\n            method: \"get\",\n            ids: \"\"\n          };\n          this.$modalSure(this.delfromData).then(function (res) {\n            _this3.$Message.success(res.msg);\n\n            _this3.getOrderList();\n          }).catch(function (res) {\n            _this3.$Message.error(res.msg);\n          });\n          break;\n\n        default:\n          this.delfromData = {\n            title: \"删除订单\",\n            url: \"/order/del/\".concat(row.id),\n            method: \"DELETE\",\n            ids: \"\"\n          }; // this.modalTitleSs = '删除订单';\n\n          this.delOrder(row, this.delfromData);\n      }\n    },\n    // 获取退款表单数据\n    getRefundData: function getRefundData(id, refund_type) {\n      var _this4 = this;\n\n      if (refund_type == 2) {\n        this.delfromData = {\n          title: \"是否立即退货\",\n          url: \"/refund/agree/\".concat(id),\n          method: \"get\"\n        };\n        this.$modalSure(this.delfromData).then(function (res) {\n          _this4.$Message.success(res.msg);\n\n          _this4.getOrderList();\n\n          _this4.getData(_this4.orderId, 1);\n        }).catch(function (res) {\n          _this4.$Message.error(res.msg);\n        });\n      } else {\n        this.$modalForm(getRefundOrderFrom(id)).then(function () {\n          _this4.getOrderList();\n\n          _this4.getData(_this4.orderId, 1);\n\n          _this4.$emit(\"changeGetTabs\");\n        });\n      }\n    },\n    //同意退货\n    getRefundGoodsData: function getRefundGoodsData(id) {\n      var _this5 = this;\n\n      this.delfromData = {\n        title: '是否立即退货',\n        url: \"/refund/agree/\".concat(id),\n        method: 'get'\n      };\n      this.$modalSure(this.delfromData).then(function (res) {\n        _this5.$Message.success(res.msg);\n\n        _this5.getOrderList();\n\n        _this5.getData(_this5.orderId, 1);\n      }).catch(function (res) {\n        _this5.$Message.error(res.msg);\n      });\n    },\n    // 获取退积分表单数据\n    getRefundIntegral: function getRefundIntegral(id) {\n      var _this6 = this;\n\n      refundIntegral(id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref2 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  _this6.FromData = res.data;\n                  _this6.$refs.edits.modals = true;\n\n                case 2:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this6.$Message.error(res.msg);\n      });\n    },\n    // 删除单条订单\n    delOrder: function delOrder(row, data) {\n      var _this7 = this;\n\n      if (row.is_del === 1) {\n        this.$modalSure(data).then(function (res) {\n          _this7.$Message.success(res.msg);\n\n          _this7.getOrderList();\n        }).catch(function (res) {\n          _this7.$Message.error(res.msg);\n        });\n      } else {\n        var title = \"错误！\";\n        var content = \"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>\";\n        this.$Modal.error({\n          title: title,\n          content: content\n        });\n      }\n    },\n    // 修改成功\n    submitFail: function submitFail() {\n      this.getOrderList();\n      this.getData(this.orderId, 1);\n    },\n    // 订单选择状态\n    selectChange2: function selectChange2(tab) {\n      this.pagination.page = 1;\n      this.pagination.refund_type = tab;\n      this.getOrderList(tab);\n    },\n    // 不退款表单数据\n    getNoRefundData: function getNoRefundData(id) {\n      var _this8 = this;\n\n      this.$modalForm(getnoRefund(id)).then(function () {\n        _this8.getOrderList();\n\n        _this8.getData(_this8.orderId);\n\n        _this8.$emit(\"changeGetTabs\");\n      });\n    },\n    // 订单列表\n    getOrderList: function getOrderList() {\n      var _this9 = this;\n\n      this.loading = true;\n      orderRefundList(this.pagination).then(function (res) {\n        _this9.loading = false;\n        var _res$data = res.data,\n            count = _res$data.count,\n            list = _res$data.list,\n            num = _res$data.num;\n        _this9.total = count;\n        _this9.tbody = list;\n        _this9.num = num;\n        list.forEach(function (item) {\n          if (item.id == _this9.orderId) {\n            _this9.rowActive = item;\n          }\n        });\n      }).catch(function (err) {\n        _this9.loading = false;\n\n        _this9.$Message.error(err.msg);\n      });\n    },\n    // 分页\n    pageChange: function pageChange(index) {\n      this.pagination.page = index;\n      this.getOrderList();\n    },\n    nameSearch: function nameSearch() {\n      this.pagination.page = 1;\n      this.getOrderList();\n    },\n    // 订单搜索\n    orderSearch: function orderSearch() {\n      this.pagination.page = 1;\n      this.getOrderList();\n    },\n    // 配送信息表单数据\n    delivery: function delivery(row) {\n      var _this10 = this;\n\n      getDistribution(row.id).then(\n      /*#__PURE__*/\n      function () {\n        var _ref3 = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee3(res) {\n          return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n            while (1) {\n              switch (_context3.prev = _context3.next) {\n                case 0:\n                  _this10.FromData = res.data;\n                  _this10.$refs.edits.modals = true;\n\n                case 2:\n                case \"end\":\n                  return _context3.stop();\n              }\n            }\n          }, _callee3);\n        }));\n\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this10.$Message.error(res.msg);\n      });\n    }\n  }\n};", null]}