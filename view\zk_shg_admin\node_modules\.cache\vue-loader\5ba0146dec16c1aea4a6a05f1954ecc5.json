{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue?vue&type=style&index=0&id=08b87947&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\AreaBox.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.areaBox {\n  position: absolute;\n  background: rgba(24, 144, 255, 0.5);\n  border: 1px dashed #1890FF;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #1989FA;\n  font-size: 12px;\n  cursor: move;\n\n  .prompt-text {\n    overflow: hidden;\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: center;\n    max-width: 100%;\n    max-height: 100%;\n    text-align: center;\n    align-items: center;\n    color: #fff;\n\n    .num {\n      font-size: 12px;\n    }\n\n    .prompt-item {\n      color: #fff;\n      margin: 0 2px;\n    }\n  }\n\n  .del {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 16px;\n    height: 16px;\n    line-height: 16px;\n    font-size: 12px;\n    background: #1890FF;\n    color: #fff;\n    text-align: center;\n    border-radius: 0 0 0 3px;\n    position: absolute;\n    right: 7px;\n    top: 7px;\n    transform: translate3d(50%, -50%, 0);\n    cursor: default;\n  }\n\n  .del:hover {\n    width: 16px;\n    height: 16px;\n    line-height: 16px;\n  }\n\n  .shape {\n    position: absolute;\n    width: 7px;\n    height: 7px;\n    background: transparent;\n    right: 0;\n    bottom: 0;\n    transform: translate3d(50%, 50%, 0);\n    cursor: nwse-resize;\n  }\n}\n\n.area-set {\n  display: flex;\n  align-items: center;\n  margin: 16px 0;\n}\n\n.area-label {\n  width: 100px;\n}\n\n.area-content {\n  flex: 1;\n}\n", null]}