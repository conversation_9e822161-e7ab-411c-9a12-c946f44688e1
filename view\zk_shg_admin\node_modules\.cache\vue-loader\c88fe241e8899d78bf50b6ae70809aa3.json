{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue?vue&type=template&id=0bdf71a4&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <div>\n    <div class=\"seckill-box\" :style=\"{\n\t\tbackground:bottomBgColor,\n\t\tmarginTop:mTop+'px',\n\t\tpaddingTop:topConfig+'px',\n\t\tpaddingBottom:bottomConfig+'px',\n\t\tpaddingLeft:prConfig+'px',\n\t\tpaddingRight:prConfig+'px'\n\t\t}\">\n      <div class=\"hd\" :style=\"\n\t    (styleConfig?\n\t\t'backgroundImage:url(' + imgBgUrl + ')':\n\t    `background:linear-gradient(90deg,${headerBgColorLeft} 0%,${headerBgColorRight} 100%)`)+\n\t\t';borderRadius:'+bgRadius\n\t  \">\n        <div class=\"left acea-row row-middle\">\n\t\t  <div class=\"text\" v-if=\"titleConfig\" :style=\"(titleTabVal==2?'fontStyle:':'fontWeight:') + titleText+';color:'+titleColor+';fontSize:'+titleNumber+'px;'\">{{titleTxtConfig}}</div>\n          <img v-else :src=\"styleConfig?imgUrl:imgColorUrl\" alt=\"\" />\n\t\t  <div class=\"line\" :style=\"{\n\t\t\t  background:dividerColor\n\t\t  }\"></div>\n          <div class=\"tips\" :style=\"{\n\t\t\t  color: styleConfig?tipsColor:tipsColor2\n\t\t  }\">{{tipTxt}}</div>\n        </div>\n        <div class=\"right\" :style=\"{\n\t\t\tcolor:styleConfig?headerBntColor:headerBntColor2,\n\t\t\tfontSize:bntNumber+'px'\n\t\t}\">\n          {{rightBntTxt}}\n\t\t  <span class=\"iconfont iconjinru\" :style=\"{\n\t\t\t fontSize:bntNumber+'px' \n\t\t  }\"></span>\n        </div>\n      </div>\n      <div class=\"list-wrapper\" :class=\"goodStyleConfig == 0?'on':(goodStyleConfig == 1 || goodStyleConfig == 2)?'on2':goodStyleConfig == 3?'on3':''\" :style=\"{\n\t\t  background: bgColor,\n\t\t  borderRadius: bgRadius2\n\t  }\">\n\t    <div v-if=\"goodStyleConfig == 0\" class=\"itemOne acea-row\" v-for=\"(item, index) in numberConfig\" :key=\"index\">\n\t\t\t<div class=\"empty-box\" :style=\"{\n\t\t\t\tborderRadius: imgRadius\n\t\t\t}\">\n\t\t\t\t<img src=\"../../assets/images/shan.png\"/>\n\t\t\t</div>\n\t\t\t<div class=\"text\">\n\t\t\t\t<div class=\"top\">\n\t\t\t\t\t<div class=\"name line2\" v-if=\"checkboxInfo.indexOf(0) != -1\" :style=\"{\n\t\t\t\t\t\tfontWeight: goodsName,\n\t\t\t\t\t\tcolor: goodsNameColor\n\t\t\t\t\t}\">熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...</div>\n\t\t\t\t\t<div class=\"num\" v-if=\"checkboxInfo.indexOf(1) != -1\" :style=\"{\n\t\t\t\t\t\tcolor:toneConfig?joinNumColor:colorStyle.theme\n\t\t\t\t\t}\"><span class=\"iconfont iconic_fire\"></span>1223人正在参与</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"bottom\" :class=\"(checkboxInfo.indexOf(2) != -1 && checkboxInfo.indexOf(3) != -1)?'':'acea-row row-bottom'\">\n\t\t\t\t\t<div class=\"price\" v-if=\"checkboxInfo.indexOf(2) != -1\" :style=\"{\n\t\t\t\t\t\tcolor:toneConfig?bargainPriceColor:colorStyle.theme\n\t\t\t\t\t}\"><span class=\"label\">¥</span><span class=\"num\">3200.00</span></div>\n\t\t\t\t\t<div class=\"yprice\" v-if=\"checkboxInfo.indexOf(3) != -1\" :style=\"{\n\t\t\t\t\t\tcolor:goodsPriceColor\n\t\t\t\t\t}\">¥1233423.00</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"bnt\" v-if=\"!bargainConfig\" :style=\"{\n\t\t\t\t\tcolor:toneConfig?goodsBntTxtColor:'#fff',\n\t\t\t\t\tbackground: toneConfig?`linear-gradient(90deg,${goodsBntColorRight} 0%,${goodsBntColorLeft} 100%)`:themeColor\n\t\t\t\t}\">参与砍价</div>\n\t\t\t</div>\n\t\t</div>\n\t\t<div class=\"itemTwo\" v-if=\"goodStyleConfig == 1\" v-for=\"(item2, index2) in numberConfig\" :key=\"index2\">\n\t\t\t<div class=\"empty-box\" :style=\"{\n\t\t\t\tborderRadius: imgRadius\n\t\t\t}\">\n\t\t\t  <img src=\"../../assets/images/shan.png\"/>\n\t\t\t</div>\n\t\t\t<div :class=\"((checkboxInfo.indexOf(0) != -1 && checkboxInfo.length == 1 && !bargainConfig) || (checkboxInfo.indexOf(0) != -1 && checkboxInfo.indexOf(1) != -1 && checkboxInfo.length == 2) && !bargainConfig)?'item':(!checkboxInfo.length || (checkboxInfo.indexOf(1) != -1 && checkboxInfo.length==1)) && !bargainConfig?'item2':''\">\n\t\t\t\t<div class=\"title line1\" v-if=\"checkboxInfo.indexOf(0) != -1\" :style=\"{\n\t\t\t\t\tfontWeight: goodsName,\n\t\t\t\t\tcolor: goodsNameColor\n\t\t\t\t}\">熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...</div>\n\t\t\t\t<div class=\"price\" :class=\"checkboxInfo.indexOf(3) == -1 && !bargainConfig?'on':''\" v-if=\"checkboxInfo.indexOf(2) != -1\" :style=\"{\n\t\t\t\t\tcolor:toneConfig?bargainPriceColor:colorStyle.theme\n\t\t\t\t}\">¥<span class=\"num\">3200.00</span></div>\n\t\t\t\t<div class=\"yprice\" :class=\"checkboxInfo.indexOf(2) == -1 && !bargainConfig?'on':''\" v-if=\"checkboxInfo.indexOf(3) != -1\" :style=\"{\n\t\t\t\t\tcolor:goodsPriceColor\n\t\t\t\t}\">¥3699.00</div>\n\t\t\t\t<div class=\"bnt\" :class=\"checkboxInfo.indexOf(2) == -1 && !bargainConfig?'on':''\" v-if=\"!bargainConfig\" :style=\"{\n\t\t\t\t\tcolor:toneConfig?goodsBntTxtColor:'#fff',\n\t\t\t\t\tbackground: toneConfig?`linear-gradient(90deg,${goodsBntColorRight} 0%,${goodsBntColorLeft} 100%)`:themeColor\n\t\t\t\t}\">去砍价</div>\n\t\t\t</div>\n\t\t</div>\n        <div\n                v-if=\"goodStyleConfig == 2\"\n\t\t\t\tclass=\"list-item\"\n                v-for=\"(item, index) in numberConfig\"\n                :key=\"index\"\n        >\n          <div class=\"img-box\">\n            <div class=\"empty-box\" :style=\"{\n\t\t\t\tborderRadius: imgRadius\n\t\t\t}\">\n\t\t\t  <img src=\"../../assets/images/shan.png\"/>\n\t\t\t</div>\n          </div>\n          <div class=\"title line1\" v-if=\"checkboxInfo.indexOf(0) != -1\" :style=\"{\n\t\t\t  fontWeight: goodsName,\n\t\t\t  color: goodsNameColor\n\t\t  }\">熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...</div>\n          <div class=\"price\" v-if=\"checkboxInfo.indexOf(2) != -1\" :style=\"{\n\t\t\t  color:toneConfig?bargainPriceColor:colorStyle.theme\n\t\t  }\">低至<span class=\"lable\">¥</span><span class=\"num\">350.00</span></div>\n\t\t  <div class=\"yprice\" v-if=\"checkboxInfo.indexOf(3) != -1\" :style=\"{\n\t\t\t  color:goodsPriceColor\n\t\t  }\">¥3699.00</div>\n        </div>\n\t\t<div class=\"itemThree\" v-if=\"goodStyleConfig == 3\" v-for=\"(item2, index2) in numberConfig\" :key=\"index2\">\n\t\t\t<div class=\"empty-box\" :style=\"{\n\t\t\t\tborderRadius: imgRadius\n\t\t\t}\">\n\t\t\t  <img src=\"../../assets/images/shan.png\"/>\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<div class=\"title line1\" v-if=\"checkboxInfo.indexOf(0) != -1\" :style=\"{\n\t\t\t\t\tfontWeight: goodsName,\n\t\t\t\t\tcolor: goodsNameColor\n\t\t\t\t}\">熙米家藏青色工装锥形裤 les 中性风帅T无性别中性多口...</div>\n\t\t\t\t<div class=\"joinNum\" v-if=\"checkboxInfo.indexOf(1) != -1\" :style=\"{\n\t\t\t\t\tcolor:toneConfig?joinNumColor2:'#fff',\n\t\t\t\t\tbackground:toneConfig?`linear-gradient(90deg,${joinBgColorLeft} 0%,${joinBgColorRight} 100%)`:themeColor2\n\t\t\t\t}\">175人已砍成功</div>\n\t\t\t\t<div class=\"price\" :class=\"checkboxInfo.indexOf(3) == -1 && !bargainConfig?'on':''\" v-if=\"checkboxInfo.indexOf(2) != -1\" :style=\"{\n\t\t\t\t\tcolor:toneConfig?bargainPriceColor:colorStyle.theme\n\t\t\t\t}\">¥<span class=\"num\">3200.00</span></div>\n\t\t\t</div>\n\t\t</div>\n      </div>\n    </div>\n  </div>\n", null]}