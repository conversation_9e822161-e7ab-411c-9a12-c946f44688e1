{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue?vue&type=template&id=20b14438&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_button_style.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('div',{staticClass:\"button-style acea-row row-middle\"},[(_vm.configData)?_c('div',{staticClass:\"title-tips\"},[_c('span',[_vm._v(_vm._s(_vm.configData.title))])]):_vm._e(),_c('div',{staticClass:\"style-box acea-row row-middle\"},[_c('div',{staticClass:\"bnt\",on:{\"click\":_vm.styleTap}},[_vm._v(\"修改风格\")]),_c('div',{staticClass:\"name\"},[_vm._v(\"当前：样式\"+_vm._s(_vm.configData.tabVal + 1))])])]),_c('Modal',{attrs:{\"title\":\"风格选择器\",\"scrollable\":\"\",\"height\":\"500\",\"width\":_vm.configData.type == 'signIn' || _vm.configData.type == 'ranking' ? 616 : 900},on:{\"on-cancel\":_vm.cancel,\"on-ok\":_vm.ok},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('div',{staticClass:\"list acea-row row-middle\"},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"item\",class:_vm.current == index ? 'on' : '',on:{\"click\":function($event){return _vm.tap(index)}}},[_c('div',{staticClass:\"pictrue acea-row row-center-wrapper\"},[_c('img',{style:({\n              width: item.width + 'px',\n              height: item.height + 'px',\n            }),attrs:{\"src\":item.url}}),(_vm.current == index)?_c('span',{staticClass:\"iconfont icona-zu80222\"}):_vm._e()]),_c('div',{staticClass:\"name\"},[_vm._v(\"风格\"+_vm._s(index + 1))])])}),0)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}