<template>
  <div class="transfer-container">
    <!-- 页面头部 -->
    <Card dis-hover>
      <div class="page-header">
        <div class="header-left">
          <h2 class="page-title">调拨管理</h2>
          <p class="page-desc">管理门店间商品调拨申请，支持审核、执行和统计分析</p>
        </div>
        <div class="header-right">
          <Button type="warning" icon="ios-warning" @click="showPendingOnly">待审核单据</Button>
          <Button type="success" icon="ios-download" @click="exportTransfer" style="margin-left: 8px;">导出数据</Button>
          <Button type="primary" icon="ios-stats" @click="showStatistics" style="margin-left: 8px;">统计分析</Button>
          <Button type="primary" icon="ios-add" @click="showAddDialog" style="margin-left: 8px;">添加调拨</Button>
        </div>
      </div>
    </Card>

    <!-- 筛选条件 -->
    <Card dis-hover style="margin-top: 16px;">
      <Form :model="filterForm" inline>
        <FormItem label="调拨单号：">
          <Input
            v-model="filterForm.order_no"
            placeholder="输入调拨单号"
            style="width: 200px">
          </Input>
        </FormItem>

        <FormItem label="调出门店：">
          <Select v-model="filterForm.from_store_id" placeholder="选择调出门店" clearable style="width: 200px">
            <Option label="全部门店" value=""></Option>
            <Option
              v-for="store in storeList"
              :key="store.value || store.id"
              :label="store.label || store.name"
              :value="store.value || store.id">
            </Option>
          </Select>
        </FormItem>

        <FormItem label="调入门店：">
          <Select v-model="filterForm.to_store_id" placeholder="选择调入门店" clearable style="width: 200px">
            <Option label="全部门店" value=""></Option>
            <Option
              v-for="store in storeList"
              :key="store.value || store.id"
              :label="store.label || store.name"
              :value="store.value || store.id">
            </Option>
          </Select>
        </FormItem>

        <FormItem label="单据状态：">
          <Select v-model="filterForm.status" placeholder="单据状态" clearable style="width: 150px">
            <Option label="全部" value=""></Option>
            <Option label="待审核" value="0"></Option>
            <Option label="已通过" value="1"></Option>
            <Option label="已完成" value="2"></Option>
            <Option label="已拒绝" value="3"></Option>
            <Option label="已撤销" value="4"></Option>
          </Select>
        </FormItem>

        <FormItem label="申请日期：">
          <DatePicker
            v-model="filterForm.date_range"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 240px">
          </DatePicker>
        </FormItem>

        <FormItem>
          <Button type="primary" @click="getTransferList">查询</Button>
          <Button @click="resetFilter" style="margin-left: 8px;">重置</Button>
        </FormItem>
      </Form>
    </Card>

    <!-- 统计卡片 -->
    <Row :gutter="16" style="margin-top: 16px;">
      <Col span="6">
        <Card>
          <div class="stat-card">
            <div class="stat-icon total">
              <Icon type="ios-document" size="24"></Icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total_orders }}</div>
              <div class="stat-label">总调拨单</div>
            </div>
          </div>
        </Card>
      </Col>

      <Col span="6">
        <Card>
          <div class="stat-card">
            <div class="stat-icon pending">
              <Icon type="ios-time" size="24"></Icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pending_orders }}</div>
              <div class="stat-label">待审核</div>
            </div>
          </div>
        </Card>
      </Col>

      <Col span="6">
        <Card>
          <div class="stat-card">
            <div class="stat-icon approved">
              <Icon type="ios-checkmark" size="24"></Icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.approved_orders }}</div>
              <div class="stat-label">已通过</div>
            </div>
          </div>
        </Card>
      </Col>

      <Col span="6">
        <Card>
          <div class="stat-card">
            <div class="stat-icon completed">
              <Icon type="ios-checkmark-circle" size="24"></Icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completed_orders }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </Card>
      </Col>
    </Row>

    <!-- 调拨单列表 -->
    <Card dis-hover style="margin-top: 16px;">
      <Table
        :columns="tableColumns"
        :data="transferList"
        :loading="tableLoading"
        stripe
        border
        @on-selection-change="handleSelectionChange">
      </Table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRows.length > 0" style="margin-top: 16px;">
        <span class="selected-count" style="margin-right: 16px; color: #515a6e;">已选择 {{ selectedRows.length }} 项</span>
        <Button size="small" @click="batchApprove(true)" type="success">批量通过</Button>
        <Button size="small" @click="batchApprove(false)" type="error" style="margin-left: 8px;">批量拒绝</Button>
        <Button size="small" @click="batchExecute" type="primary" style="margin-left: 8px;">批量执行</Button>
        <Button size="small" @click="clearSelection" style="margin-left: 8px;">取消选择</Button>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 16px; text-align: right;">
        <Page
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          :current="currentPage"
          :page-size="pageSize"
          :total="total"
          show-sizer
          show-elevator
          show-total>
        </Page>
      </div>
    </Card>

    <!-- 调拨详情对话框 -->
    <Modal v-model="detailDialogVisible" title="调拨详情" width="700">
      <div class="detail-content" v-if="currentDetail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <Row :gutter="20">
            <Col span="12">
              <div class="detail-item">
                <span class="label">调拨单号：</span>
                <span class="value">{{ currentDetail.order_no }}</span>
              </div>
            </Col>
            <Col span="12">
              <div class="detail-item">
                <span class="label">单据状态：</span>
                <Tag :color="getStatusTagColor(currentDetail.status)">
                  {{ getStatusText(currentDetail.status) }}
                </Tag>
              </div>
            </Col>
            <Col span="12">
              <div class="detail-item">
                <span class="label">申请人：</span>
                <span class="value">{{ currentDetail.applicant }}</span>
              </div>
            </Col>
            <Col span="12">
              <div class="detail-item">
                <span class="label">申请时间：</span>
                <span class="value">{{ currentDetail.apply_time_text }}</span>
              </div>
            </Col>
          </Row>
        </div>

        <div class="detail-section">
          <h4>商品信息</h4>
          <div class="product-detail">
            <img
              :src="currentDetail.product_image || '/static/images/default-product.png'"
              style="width: 80px; height: 80px; border-radius: 8px; margin-right: 16px; object-fit: cover;"
              alt="商品图片">
            <div>
              <div class="product-name">{{ currentDetail.product_name }}</div>
              <div class="product-spec" v-if="currentDetail.sku">规格：{{ currentDetail.sku }}</div>
              <div class="transfer-qty">调拨数量：{{ currentDetail.transfer_qty }}</div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>调拨信息</h4>
          <Row :gutter="20">
            <Col span="12">
              <div class="detail-item">
                <span class="label">调出门店：</span>
                <span class="value">{{ currentDetail.from_store_name }}</span>
              </div>
            </Col>
            <Col span="12">
              <div class="detail-item">
                <span class="label">调入门店：</span>
                <span class="value">{{ currentDetail.to_store_name }}</span>
              </div>
            </Col>
          </Row>
        </div>

        <div class="detail-section" v-if="currentDetail.remark">
          <h4>申请原因</h4>
          <div class="remark-content">{{ currentDetail.remark }}</div>
        </div>

        <div class="detail-section" v-if="currentDetail.approve_remark">
          <h4>审核备注</h4>
          <div class="remark-content">{{ currentDetail.approve_remark }}</div>
        </div>
      </div>
    </Modal>

    <!-- 审核对话框 -->
    <Modal v-model="approveDialogVisible" :title="approveType ? '审核通过' : '审核拒绝'" width="500">
      <Form :model="approveForm" :rules="approveRules" ref="approveForm" :label-width="100">
        <FormItem label="审核备注：" prop="remark">
          <Input
            type="textarea"
            v-model="approveForm.remark"
            :placeholder="approveType ? '请填写通过原因（可选）' : '请填写拒绝原因'"
            :rows="4">
          </Input>
        </FormItem>
      </Form>

      <div slot="footer" class="dialog-footer">
        <Button @click="approveDialogVisible = false">取消</Button>
        <Button
          :type="approveType ? 'success' : 'error'"
          @click="submitApprove"
          :loading="approveLoading"
          style="margin-left: 8px;">
          {{ approveType ? '确认通过' : '确认拒绝' }}
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import request from '@/plugins/request';

export default {
  name: 'TransferManagement',
  data() {
    return {
      // 筛选条件
      filterForm: {
        order_no: '',
        from_store_id: '',
        to_store_id: '',
        status: '',
        date_range: []
      },

      // 列表数据
      transferList: [],
      storeList: [],
      selectedRows: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableLoading: false,

      // 表格列定义
      tableColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '调拨单号',
          key: 'order_no',
          width: 160,
          align: 'center'
        },
        {
          title: '商品信息',
          key: 'product_name',
          minWidth: 250
        },
        {
          title: '调拨门店',
          key: 'stores',
          width: 250,
          align: 'center',
          render: (h, params) => {
            return h('div', {
              style: {
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '4px'
              }
            }, [
              h('div', {
                style: {
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }
              }, [
                h('span', {
                  style: {
                    fontSize: '12px',
                    color: '#666'
                  }
                }, params.row.from_store_name || '未知门店'),
                h('Icon', {
                  props: {
                    type: 'ios-arrow-forward'
                  },
                  style: {
                    color: '#2d8cf0'
                  }
                }),
                h('span', {
                  style: {
                    fontSize: '12px',
                    color: '#666'
                  }
                }, params.row.to_store_name || '未知门店')
              ]),
              h('div', {
                style: {
                  fontSize: '11px',
                  color: '#999',
                  marginTop: '2px'
                }
              }, `库存: ${params.row.from_store_stock || 0} → ${params.row.to_store_stock || 0}`)
            ]);
          }
        },
        {
          title: '库存变化',
          key: 'stock_change',
          width: 180,
          align: 'center',
          render: (h, params) => {
            return h('div', {
              style: {
                display: 'flex',
                flexDirection: 'column',
                gap: '4px'
              }
            }, [
              h('div', {
                style: {
                  fontSize: '12px',
                  color: '#ed4014'
                }
              }, `调出: ${params.row.from_store_stock || 0} → ${params.row.from_store_stock_after || 0}`),
              h('div', {
                style: {
                  fontSize: '12px',
                  color: '#19be6b'
                }
              }, `调入: ${params.row.to_store_stock || 0} → ${params.row.to_store_stock_after || 0}`)
            ]);
          }
        },
        {
          title: '单据状态',
          key: 'status',
          width: 100,
          align: 'center',
          render: (h, params) => {
            const statusMap = {
              0: { text: '待审核', color: 'orange' },
              1: { text: '已审核', color: 'blue' },
              2: { text: '已执行', color: 'green' },
              3: { text: '已拒绝', color: 'red' },
              4: { text: '已撤销', color: 'default' }
            };
            const status = statusMap[params.row.status] || { text: '未知', color: 'default' };
            return h('Tag', {
              props: {
                color: status.color
              }
            }, status.text);
          }
        },
        {
          title: '申请人',
          key: 'applicant',
          width: 100,
          align: 'center'
        },
        {
          title: '申请时间',
          key: 'apply_time_text',
          width: 160,
          align: 'center'
        },
        {
          title: '审核时间',
          key: 'approve_time_text',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 250,
          align: 'center',
          fixed: 'right'
        }
      ],

      // 统计数据
      statistics: {
        total_orders: 0,
        pending_orders: 0,
        approved_orders: 0,
        completed_orders: 0
      },

      // 详情对话框
      detailDialogVisible: false,
      currentDetail: null,

      // 审核对话框
      approveDialogVisible: false,
      approveLoading: false,
      approveType: true, // true: 通过, false: 拒绝
      approveForm: {
        id: '',
        remark: ''
      },
      approveRules: {
        remark: [
          { required: false, message: '请填写审核备注', trigger: 'blur' }
        ]
      }
    };
  },

  created() {
    this.initData();
  },

  methods: {
    // 初始化数据
    async initData() {
      await this.getStoreList();
      await this.getTransferList();
      await this.getStatistics();
    },

    // 获取门店列表
    async getStoreList() {
      try {
        const res = await request.get('/erp/transfer/store_options');
        console.log('门店列表API响应:', res);
        if (res && res.status === 200 && res.data) {
          this.storeList = res.data || [];
          console.log('门店列表数据:', this.storeList);
        } else {
          console.error('门店列表API响应格式错误:', res);
          this.storeList = [];
        }
      } catch (error) {
        console.error('获取门店列表失败:', error);
        this.storeList = [];
        // 不显示错误消息，因为门店列表不是必需的
      }
    },

    // 获取调拨单列表
    async getTransferList() {
      this.tableLoading = true;
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          order_no: this.filterForm.order_no,
          from_store_id: this.filterForm.from_store_id,
          to_store_id: this.filterForm.to_store_id,
          status: this.filterForm.status,
        };

        // 处理日期范围
        if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {
          params.start_time = this.formatDate(this.filterForm.date_range[0]);
          params.end_time = this.formatDate(this.filterForm.date_range[1]);
        }

        const res = await request.get('/erp/transfer/list', { params });
        console.log('调拨列表API响应:', res);

        if (res && res.status === 200 && res.data) {
          const data = res.data;
          console.log('调拨列表数据:', data);

          // 使用$set强制更新数据
          this.$set(this, 'transferList', data.list || []);
          this.$set(this, 'total', data.count || 0);

          console.log('设置列表数据:', this.transferList, '总数:', this.total);
          console.log('最终列表数据:', this.transferList);
          console.log('最终总数:', this.total);

          // 强制触发Vue更新
          this.$nextTick(() => {
            console.log('nextTick后的列表数据:', this.transferList.length);
            console.log('nextTick后的总数:', this.total);
            this.$forceUpdate();
          });
        } else {
          this.$Message.error(res.data.msg || '获取调拨单列表失败');
        }
      } catch (error) {
        console.error('获取调拨单列表失败:', error);
        this.$Message.error('获取调拨单列表失败');
      } finally {
        this.tableLoading = false;
      }
    },

    // 获取统计数据
    async getStatistics() {
      try {
        const res = await request.get('/erp/transfer/statistics');
        console.log('统计数据API响应:', res);
        if (res && res.status === 200 && res.data) {
          const stats = res.data;
          console.log('统计数据:', stats);
          console.log('stats.total:', stats.total, 'stats.pending:', stats.pending);

          // 使用$set强制更新统计数据
          this.$set(this.statistics, 'total_orders', stats.total || 0);
          this.$set(this.statistics, 'pending_orders', stats.pending || 0);
          this.$set(this.statistics, 'approved_orders', stats.approved || 0);
          this.$set(this.statistics, 'completed_orders', stats.executed || 0);

          console.log('设置后的统计数据:', this.statistics);

          this.$nextTick(() => {
            console.log('nextTick后的统计数据:', this.statistics);
            this.$forceUpdate();
          });
        } else {
          console.error('统计数据API响应格式错误:', res);
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },

    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        order_no: '',
        from_store_id: '',
        to_store_id: '',
        status: '',
        date_range: []
      };
      this.currentPage = 1;
      this.getTransferList();
    },

    // 分页处理
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.getTransferList();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.getTransferList();
    },

    // 选择处理
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    clearSelection() {
      this.$refs.table && this.$refs.table.clearSelection();
      this.selectedRows = [];
    },

    // 状态处理
    getStatusTagType(status) {
      const typeMap = {
        0: 'warning', // 待审核
        1: 'success', // 已通过
        2: 'info', // 已完成
        3: 'danger', // 已拒绝
        4: 'info' // 已撤销
      };
      return typeMap[status] || 'info';
    },

    getStatusText(status) {
      const textMap = {
        0: '待审核',
        1: '已通过',
        2: '已完成',
        3: '已拒绝',
        4: '已撤销'
      };
      return textMap[status] || '未知';
    },

    // 查看详情
    viewDetail(row) {
      this.currentDetail = { ...row };
      this.detailDialogVisible = true;
    },

    // 审核调拨
    approveTransfer(row, isApprove) {
      this.approveType = isApprove;
      this.approveForm = {
        id: row.id,
        remark: ''
      };
      this.approveDialogVisible = true;
    },

    async submitApprove() {
      try {
        this.approveLoading = true;

        const data = {
          is_approve: this.approveType,
          remark: this.approveForm.remark
        };

        const res = await this.$http.post(`/adminapi/erp/transfer/approve/${this.approveForm.id}`, data);

        if (res.data.status === 200) {
          this.$message.success(res.data.msg || (this.approveType ? '审核通过成功' : '审核拒绝成功'));
          this.approveDialogVisible = false;
          this.getTransferList();
          this.getStatistics();
        } else {
          this.$message.error(res.data.msg || '审核失败');
        }
      } catch (error) {
        console.error('审核失败:', error);
        this.$message.error('审核失败');
      } finally {
        this.approveLoading = false;
      }
    },

    // 执行调拨
    async executeTransfer(row) {
      this.$confirm('确定要执行这个调拨申请吗？', '确认执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);
          if (res.data.status === 200) {
            this.$message.success('调拨执行成功');
            this.getTransferList();
            this.getStatistics();
          } else {
            this.$message.error(res.data.msg || '执行失败');
          }
        } catch (error) {
          console.error('执行调拨失败:', error);
          this.$message.error('执行调拨失败');
        }
      });
    },

    // 强制执行
    async forceExecute(row) {
      this.$confirm('强制执行将忽略库存不足等限制，确定要强制执行吗？', '确认强制执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(async () => {
        try {
          const res = await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);
          if (res.data.status === 200) {
            this.$message.success('强制执行成功');
            this.getTransferList();
            this.getStatistics();
          } else {
            this.$message.error(res.data.msg || '强制执行失败');
          }
        } catch (error) {
          console.error('强制执行失败:', error);
          this.$message.error('强制执行失败');
        }
      });
    },

    // 删除调拨单
    async deleteTransfer(row) {
      this.$confirm('确定要删除这个调拨单吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await this.$http.delete(`/adminapi/erp/transfer/delete/${row.id}`);
          if (res.data.status === 200) {
            this.$message.success('删除成功');
            this.getTransferList();
            this.getStatistics();
          } else {
            this.$message.error(res.data.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败');
        }
      });
    },

    // 批量操作
    async batchApprove(isApprove) {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要审核的调拨单');
        return;
      }

      const action = isApprove ? '通过' : '拒绝';
      this.$confirm(`确定要批量${action}选中的调拨单吗？`, `确认批量${action}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const data = {
            ids: this.selectedRows.map(row => row.id),
            is_approve: isApprove,
            remark: ''
          };

          const res = await this.$http.post('/adminapi/erp/transfer/batch_approve', data);
          if (res.data.status === 200) {
            this.$message.success(`批量${action}成功`);
            this.getTransferList();
            this.getStatistics();
            this.clearSelection();
          } else {
            this.$message.error(res.data.msg || `批量${action}失败`);
          }
        } catch (error) {
          console.error(`批量${action}失败:`, error);
          this.$message.error(`批量${action}失败`);
        }
      });
    },

    async batchExecute() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要执行的调拨单');
        return;
      }

      this.$confirm('确定要批量执行选中的调拨单吗？', '确认批量执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 逐个执行调拨单
          for (const row of this.selectedRows) {
            await this.$http.post(`/adminapi/erp/transfer/force_execute/${row.id}`);
          }

          this.$message.success('批量执行成功');
          this.getTransferList();
          this.getStatistics();
          this.clearSelection();
        } catch (error) {
          console.error('批量执行失败:', error);
          this.$message.error('批量执行失败');
        }
      });
    },

    // 其他功能
    showPendingOnly() {
      this.filterForm.status = 0;
      this.currentPage = 1;
      this.getTransferList();
    },

    async exportTransfer() {
      try {
        const params = {
          order_no: this.filterForm.order_no,
          from_store_id: this.filterForm.from_store_id,
          to_store_id: this.filterForm.to_store_id,
          status: this.filterForm.status,
        };

        // 处理日期范围
        if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {
          params.start_time = this.formatDate(this.filterForm.date_range[0]);
          params.end_time = this.formatDate(this.filterForm.date_range[1]);
        }

        const res = await this.$http.get('/adminapi/erp/transfer/export', { params });
        if (res.data.status === 200) {
          this.$message.success('导出成功');
          // TODO: 处理文件下载
        } else {
          this.$message.error(res.data.msg || '导出失败');
        }
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
    },

    showStatistics() {
      // 跳转到统计分析页面或显示统计弹窗
      this.$router.push('/erp/report');
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已审核',
        2: '已执行',
        3: '已拒绝',
        4: '已撤销'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取状态标签颜色
    getStatusTagColor(status) {
      const colorMap = {
        0: 'orange',    // 待审核
        1: 'blue',      // 已审核
        2: 'green',     // 已执行
        3: 'red',       // 已拒绝
        4: 'default'    // 已撤销
      };
      return colorMap[status] || 'default';
    },

    // 显示添加调拨对话框
    showAddDialog() {
      this.$Message.info('添加调拨功能开发中...');
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 清除选择
    clearSelection() {
      this.selectedRows = [];
    },

    // 分页相关
    handleCurrentChange(page) {
      this.currentPage = page;
      this.getTransferList();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.getTransferList();
    },

    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        order_no: '',
        from_store_id: '',
        to_store_id: '',
        status: '',
        date_range: []
      };
      this.currentPage = 1;
      this.getTransferList();
    },

    // 查看详情
    viewDetail(row) {
      this.currentDetail = row;
      this.detailDialogVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
