<template>
  <div class="transfer-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">调拨管理</h2>
        <p class="page-desc">管理门店间商品调拨申请，支持审核、执行和统计分析</p>
      </div>
      <div class="header-right">
        <el-button type="warning" icon="el-icon-warning" @click="showPendingOnly">待审核单据</el-button>
        <el-button type="success" icon="el-icon-download" @click="exportTransfer">导出数据</el-button>
        <el-button type="primary" icon="el-icon-pie-chart" @click="showStatistics">统计分析</el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-container">
      <el-form :model="filterForm" inline>
        <el-form-item label="调拨单号：">
          <el-input
            v-model="filterForm.order_no"
            placeholder="输入调拨单号"
            style="width: 200px">
          </el-input>
        </el-form-item>

        <el-form-item label="调出门店：">
          <el-select v-model="filterForm.from_store_id" placeholder="选择调出门店" clearable style="width: 200px">
            <el-option label="全部门店" value=""></el-option>
            <el-option
              v-for="store in storeList"
              :key="store.id"
              :label="store.name"
              :value="store.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="调入门店：">
          <el-select v-model="filterForm.to_store_id" placeholder="选择调入门店" clearable style="width: 200px">
            <el-option label="全部门店" value=""></el-option>
            <el-option
              v-for="store in storeList"
              :key="store.id"
              :label="store.name"
              :value="store.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态：">
          <el-select v-model="filterForm.status" placeholder="单据状态" clearable style="width: 150px">
            <el-option label="全部" value=""></el-option>
            <el-option label="待审核" :value="0"></el-option>
            <el-option label="已通过" :value="1"></el-option>
            <el-option label="已完成" :value="2"></el-option>
            <el-option label="已拒绝" :value="3"></el-option>
            <el-option label="已撤销" :value="4"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="申请日期：">
          <el-date-picker
            v-model="filterForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getTransferList">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-container">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-document"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.total_orders }}</div>
          <div class="stat-label">总调拨单</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon pending">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.pending_orders }}</div>
          <div class="stat-label">待审核</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon approved">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.approved_orders }}</div>
          <div class="stat-label">已通过</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon completed">
          <i class="el-icon-circle-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.completed_orders }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <!-- 调拨单列表 -->
    <div class="table-container">
      <el-table
        :data="transferList"
        v-loading="tableLoading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange">

        <el-table-column type="selection" width="55" align="center"></el-table-column>

        <el-table-column prop="order_no" label="调拨单号" width="160" align="center">
          <template slot-scope="scope">
            <el-link type="primary" @click="viewDetail(scope.row)">{{ scope.row.order_no }}</el-link>
          </template>
        </el-table-column>

        <el-table-column label="商品信息" min-width="250">
          <template slot-scope="scope">
            <div class="product-info">
              <el-image
                :src="scope.row.product_image"
                style="width: 40px; height: 40px; border-radius: 4px; margin-right: 12px"
                fit="cover">
              </el-image>
              <div>
                <div class="product-name">{{ scope.row.product_name }}</div>
                <div class="product-spec" v-if="scope.row.sku">{{ scope.row.sku }}</div>
                <div class="transfer-qty">调拨数量：{{ scope.row.transfer_qty }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="调拨门店" width="200" align="center">
          <template slot-scope="scope">
            <div class="transfer-stores">
              <div class="from-store">{{ scope.row.from_store_name }}</div>
              <i class="el-icon-right transfer-arrow"></i>
              <div class="to-store">{{ scope.row.to_store_name }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="单据状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="applicant" label="申请人" width="100" align="center"></el-table-column>

        <el-table-column prop="apply_time" label="申请时间" width="160" align="center"></el-table-column>

        <el-table-column prop="approve_time" label="审核时间" width="160" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.approve_time">{{ scope.row.approve_time }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewDetail(scope.row)">查看详情</el-button>

            <template v-if="scope.row.status === 0">
              <el-button type="text" size="small" @click="approveTransfer(scope.row, true)">通过</el-button>
              <el-button type="text" size="small" @click="approveTransfer(scope.row, false)">拒绝</el-button>
            </template>

            <template v-if="scope.row.status === 1">
              <el-button type="text" size="small" @click="executeTransfer(scope.row)">执行调拨</el-button>
              <el-button type="text" size="small" @click="forceExecute(scope.row)">强制执行</el-button>
            </template>

            <template v-if="scope.row.status === 3 || scope.row.status === 4">
              <el-button type="text" size="small" @click="deleteTransfer(scope.row)">删除</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRows.length > 0">
        <span class="selected-count">已选择 {{ selectedRows.length }} 项</span>
        <el-button size="small" @click="batchApprove(true)">批量通过</el-button>
        <el-button size="small" @click="batchApprove(false)">批量拒绝</el-button>
        <el-button size="small" @click="batchExecute">批量执行</el-button>
        <el-button size="small" @click="clearSelection">取消选择</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[20, 50, 100, 200]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 调拨详情对话框 -->
    <el-dialog title="调拨详情" :visible.sync="detailDialogVisible" width="700px">
      <div class="detail-content" v-if="currentDetail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">调拨单号：</span>
                <span class="value">{{ currentDetail.order_no }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">单据状态：</span>
                <el-tag :type="getStatusTagType(currentDetail.status)">
                  {{ getStatusText(currentDetail.status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">申请人：</span>
                <span class="value">{{ currentDetail.applicant }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">申请时间：</span>
                <span class="value">{{ currentDetail.apply_time }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="detail-section">
          <h4>商品信息</h4>
          <div class="product-detail">
            <el-image
              :src="currentDetail.product_image"
              style="width: 80px; height: 80px; border-radius: 8px; margin-right: 16px"
              fit="cover">
            </el-image>
            <div>
              <div class="product-name">{{ currentDetail.product_name }}</div>
              <div class="product-spec" v-if="currentDetail.sku">规格：{{ currentDetail.sku }}</div>
              <div class="transfer-qty">调拨数量：{{ currentDetail.transfer_qty }}</div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>调拨信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">调出门店：</span>
                <span class="value">{{ currentDetail.from_store_name }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">调入门店：</span>
                <span class="value">{{ currentDetail.to_store_name }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="detail-section" v-if="currentDetail.remark">
          <h4>申请原因</h4>
          <div class="remark-content">{{ currentDetail.remark }}</div>
        </div>

        <div class="detail-section" v-if="currentDetail.approve_remark">
          <h4>审核备注</h4>
          <div class="remark-content">{{ currentDetail.approve_remark }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="approveType ? '审核通过' : '审核拒绝'" :visible.sync="approveDialogVisible" width="500px">
      <el-form :model="approveForm" :rules="approveRules" ref="approveForm" label-width="100px">
        <el-form-item label="审核备注：" prop="remark">
          <el-input
            type="textarea"
            v-model="approveForm.remark"
            :placeholder="approveType ? '请填写通过原因（可选）' : '请填写拒绝原因'"
            :rows="4">
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button
          :type="approveType ? 'success' : 'danger'"
          @click="submitApprove"
          :loading="approveLoading">
          {{ approveType ? '确认通过' : '确认拒绝' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TransferManagement',
  data() {
    return {
      // 筛选条件
      filterForm: {
        order_no: '',
        from_store_id: '',
        to_store_id: '',
        status: '',
        date_range: []
      },

      // 列表数据
      transferList: [],
      storeList: [],
      selectedRows: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableLoading: false,

      // 统计数据
      statistics: {
        total_orders: 0,
        pending_orders: 0,
        approved_orders: 0,
        completed_orders: 0
      },

      // 详情对话框
      detailDialogVisible: false,
      currentDetail: null,

      // 审核对话框
      approveDialogVisible: false,
      approveLoading: false,
      approveType: true, // true: 通过, false: 拒绝
      approveForm: {
        id: '',
        remark: ''
      },
      approveRules: {
        remark: [
          { required: false, message: '请填写审核备注', trigger: 'blur' }
        ]
      }
    };
  },

  created() {
    this.initData();
  },

  methods: {
    // 初始化数据
    async initData() {
      this.getTransferList();
    },

    // 获取调拨单列表
    async getTransferList() {
      this.tableLoading = true;
      // 模拟数据
      setTimeout(() => {
        this.transferList = [
          {
            id: 1,
            order_no: 'TF202412160001',
            product_id: 1,
            product_name: '苹果iPhone 14',
            product_image: 'https://via.placeholder.com/40x40',
            sku: '128GB 蓝色',
            transfer_qty: 10,
            from_store_id: 1,
            from_store_name: '上海总店',
            to_store_id: 2,
            to_store_name: '北京分店',
            status: 0,
            applicant: '张三',
            apply_time: '2024-12-16 10:30:00',
            approve_time: '',
            remark: '北京分店库存不足，急需补货'
          },
          {
            id: 2,
            order_no: 'TF202412160002',
            product_id: 2,
            product_name: '华为Mate50',
            product_image: 'https://via.placeholder.com/40x40',
            sku: '256GB 白色',
            transfer_qty: 5,
            from_store_id: 2,
            from_store_name: '北京分店',
            to_store_id: 3,
            to_store_name: '深圳分店',
            status: 1,
            applicant: '李四',
            apply_time: '2024-12-16 09:15:00',
            approve_time: '2024-12-16 10:00:00',
            remark: '季节性调拨'
          }
        ];
        this.total = 2;
        this.statistics = {
          total_orders: 45,
          pending_orders: 8,
          approved_orders: 12,
          completed_orders: 25
        };
        this.tableLoading = false;
      }, 1000);
    },

    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        order_no: '',
        from_store_id: '',
        to_store_id: '',
        status: '',
        date_range: []
      };
      this.currentPage = 1;
      this.getTransferList();
    },

    // 分页处理
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.getTransferList();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.getTransferList();
    },

    // 选择处理
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    clearSelection() {
      this.$refs.table && this.$refs.table.clearSelection();
      this.selectedRows = [];
    },

    // 状态处理
    getStatusTagType(status) {
      const typeMap = {
        0: 'warning', // 待审核
        1: 'success', // 已通过
        2: 'info', // 已完成
        3: 'danger', // 已拒绝
        4: 'info' // 已撤销
      };
      return typeMap[status] || 'info';
    },

    getStatusText(status) {
      const textMap = {
        0: '待审核',
        1: '已通过',
        2: '已完成',
        3: '已拒绝',
        4: '已撤销'
      };
      return textMap[status] || '未知';
    },

    // 查看详情
    viewDetail(row) {
      this.currentDetail = { ...row };
      this.detailDialogVisible = true;
    },

    // 审核调拨
    approveTransfer(row, isApprove) {
      this.approveType = isApprove;
      this.approveForm = {
        id: row.id,
        remark: ''
      };
      this.approveDialogVisible = true;
    },

    async submitApprove() {
      try {
        this.approveLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));

        this.$message.success(this.approveType ? '审核通过成功' : '审核拒绝成功');
        this.approveDialogVisible = false;
        this.getTransferList();
      } catch (error) {
        this.$message.error('审核失败');
      } finally {
        this.approveLoading = false;
      }
    },

    // 执行调拨
    executeTransfer(row) {
      this.$confirm('确定要执行这个调拨申请吗？', '确认执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('调拨执行成功');
        this.getTransferList();
      });
    },

    // 强制执行
    forceExecute(row) {
      this.$confirm('强制执行将忽略库存不足等限制，确定要强制执行吗？', '确认强制执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.$message.success('强制执行成功');
        this.getTransferList();
      });
    },

    // 删除调拨单
    deleteTransfer(row) {
      this.$confirm('确定要删除这个调拨单吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
        this.getTransferList();
      });
    },

    // 批量操作
    batchApprove(isApprove) {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要审核的调拨单');
        return;
      }

      const action = isApprove ? '通过' : '拒绝';
      this.$confirm(`确定要批量${action}选中的调拨单吗？`, `确认批量${action}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success(`批量${action}成功`);
        this.getTransferList();
        this.clearSelection();
      });
    },

    batchExecute() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要执行的调拨单');
        return;
      }

      this.$confirm('确定要批量执行选中的调拨单吗？', '确认批量执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('批量执行成功');
        this.getTransferList();
        this.clearSelection();
      });
    },

    // 其他功能
    showPendingOnly() {
      this.filterForm.status = 0;
      this.getTransferList();
    },

    exportTransfer() {
      this.$message.info('导出功能开发中...');
    },

    showStatistics() {
      this.$message.info('统计分析功能开发中...');
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
