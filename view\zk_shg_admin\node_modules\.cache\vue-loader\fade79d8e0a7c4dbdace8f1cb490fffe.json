{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue?vue&type=template&id=db491212&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab_list.vue", "mtime": 1717062347000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"c_product\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.configData.title))]),_c('div',{staticClass:\"list-box\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.configData.list,\"group\":\"peoples\",\"handle\":\".move-icon\"}},_vm._l((_vm.configData.list),function(item,index){return _c('div',{key:index,staticClass:\"item\"},[_c('div',{staticClass:\"move-icon\"},[_c('span',{staticClass:\"iconfont-diy iconxingzhuang<PERSON>ehe\"})]),_c('div',[_c('div',{staticClass:\"con-item\"},[_c('span',[_vm._v(_vm._s(item.text.title))]),_c('div',[_c('Input',{attrs:{\"placeholder\":item.text.pla,\"maxlength\":item.text.max,\"show-word-limit\":\"\"},model:{value:(item.text.val),callback:function ($$v) {_vm.$set(item.text, \"val\", $$v)},expression:\"item.text.val\"}})],1)]),_c('div',{staticClass:\"con-item\"},[_c('span',[_vm._v(_vm._s(item.dataType.title))]),_c('div',[_c('RadioGroup',{model:{value:(item.dataType.tabVal),callback:function ($$v) {_vm.$set(item.dataType, \"tabVal\", $$v)},expression:\"item.dataType.tabVal\"}},_vm._l((item.dataType.tabList),function(radio,key){return _c('Radio',{key:key,attrs:{\"label\":key}},[_c('span',[_vm._v(_vm._s(radio.name))])])}),1)],1)]),_c('div',{staticClass:\"con-item\"},[_c('span',[_vm._v(_vm._s(item.dataType.tabList[item.dataType.tabVal].name))]),_c('div',{on:{\"click\":function($event){return _vm.getLink(index)}}},[_c('Input',{attrs:{\"icon\":\"ios-arrow-forward\",\"placeholder\":item.dataType.tabVal?'选择分类':'选择页面',\"readonly\":\"\"},model:{value:(item.dataType.tabVal?item.classPage.name:item.microPage.name),callback:function ($$v) {_vm.$set(item.dataType.tabVal?item.classPage.name:item.microPage, \"name\", $$v)},expression:\"item.dataType.tabVal?item.classPage.name:item.microPage.name\"}})],1)])]),_c('div',{staticClass:\"delete\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete(index)}}},[_c('Icon',{attrs:{\"type\":\"ios-close-circle\",\"size\":\"24\"}})],1)])}),0)],1),(_vm.configData.list)?_c('div',[(_vm.configData.list.length < _vm.configData.max)?_c('div',{staticClass:\"add-btn\",on:{\"click\":_vm.addHotTxt}},[_c('Button',{staticClass:\"btn\",attrs:{\"type\":\"primary\",\"ghost\":\"\"}},[_c('span',{staticClass:\"iconfont iconjiahao\"}),_vm._v(\"添加\\n\\t\\t\\t\\t\")])],1):_vm._e()]):_vm._e(),_c('linkaddress',{ref:\"linkaddres\",attrs:{\"linkType\":1,\"fromType\":\"diyPage\",\"isCateTree\":!['homeComb', 'tabNav'].includes(_vm.defaults.name)},on:{\"linkUrl\":_vm.linkUrl}})],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}