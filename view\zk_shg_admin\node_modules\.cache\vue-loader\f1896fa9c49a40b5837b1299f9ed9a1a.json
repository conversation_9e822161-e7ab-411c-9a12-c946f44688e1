{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\queueList.vue?vue&type=template&id=7a2c1447&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\queueList.vue", "mtime": 1693790344000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<Modal v-model=\"modal\" title=\"任务列表\" width=\"1000\" footer-hide class-name=\"vertical-center-modal\">\n  <Card :bordered=\"false\" dis-hover v-if=\"modal\">\n    <Form\n      ref=\"formValidate\"\n      inline\n      :model=\"formValidate\"\n      :label-width=\"labelWidth\"\n      :label-position=\"labelPosition\"\n      class=\"tabform\"\n      @submit.native.prevent\n    >\n      <FormItem label=\"操作时间：\">\n        <DatePicker\n          :editable=\"false\"\n          @on-change=\"onchangeTime\"\n          :value=\"timeVal\"\n          format=\"yyyy/MM/dd\"\n          type=\"datetimerange\"\n          placement=\"bottom-start\"\n          placeholder=\"自定义时间\"\n          :options=\"options\"\n         class=\"width20\"\n        ></DatePicker>\n      </FormItem>\n      <FormItem label=\"类型：\">\n        <Select\n          v-model=\"formValidate.type\"\n          clearable\n          @on-change=\"typeSearchs\"\n          class=\"width20\"\n        >\n          <Option\n            v-for=\"item in typeList\"\n            :value=\"item.value\"\n            :key=\"item.value\"\n            >{{ item.label }}</Option\n          >\n        </Select>\n      </FormItem>\n      <FormItem label=\"状态：\">\n        <Select\n          v-model=\"formValidate.status\"\n          clearable\n          @on-change=\"statusSearchs\"\n          class=\"width20\"\n        >\n          <Option\n            v-for=\"item in statusList\"\n            :value=\"item.value\"\n            :key=\"item.value\"\n            >{{ item.label }}</Option\n          >\n        </Select>\n      </FormItem>\n    </Form>\n    <Table\n      class=\"mt25\"\n      height=\"500\"\n      :columns=\"columns1\"\n      :data=\"data1\"\n      :loading=\"loading\"\n    >\n      <template slot-scope=\"{ row, index }\" slot=\"action\">\n        <template v-if=\"row.is_show_log\">\n          <a @click=\"deliveryLook(row)\">查看</a>\n          <Divider type=\"vertical\" />\n        </template>\n        <template>\n          <Dropdown @on-click=\"changeMenu(row, $event)\">\n            <a>更多<Icon type=\"ios-arrow-down\"></Icon></a>\n            <DropdownMenu slot=\"list\">\n              <DropdownItem name=\"1\" v-if=\"[7, 8, 9, 10].includes(row.type)\"\n                >下载</DropdownItem\n              >\n              <DropdownItem name=\"2\" v-if=\"row.status == 1\">重新执行</DropdownItem>\n              <DropdownItem v-if=\"row.is_stop_button\" name=\"3\"\n                >停止任务</DropdownItem\n              >\n              <DropdownItem name=\"4\">清除任务</DropdownItem>\n            </DropdownMenu>\n          </Dropdown>\n        </template>\n      </template>\n    </Table>\n    <div class=\"acea-row row-right page\">\n      <Page\n        :total=\"page1.total\"\n        :current=\"page1.pageNum\"\n        show-elevator\n        show-total\n        @on-change=\"pageChange\"\n        @on-page-size-change=\"limitChange\"\n        :page-size=\"page1.pageSize\"\n        show-sizer\n      />\n    </div>\n  </Card>\n  <Modal v-model=\"modal1\" width=\"900\" footer-hide>\n    <Table\n      height=\"500\"\n      class=\"mt25\"\n      :columns=\"columns4\"\n      :data=\"data2\"\n      :loading=\"loading2\"\n    ></Table>\n    <div class=\"acea-row row-right page\">\n      <Page\n        :total=\"page2.total\"\n        :current=\"page2.pageNum\"\n        show-elevator\n        show-total\n        @on-change=\"pageChange2\"\n        @on-page-size-change=\"limitChange2\"\n        :page-size=\"page2.pageSize\"\n        show-sizer\n      />\n    </div>\n  </Modal>\n  <!-- </div> -->\n</Modal>\n", null]}