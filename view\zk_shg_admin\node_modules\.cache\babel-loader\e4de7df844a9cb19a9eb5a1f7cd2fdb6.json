{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addPiecesDiscount\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\addPiecesDiscount\\index.vue", "mtime": 1693882316000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from \"vuex\";\nimport { saveDiscount, discountInfo } from \"@/api/marketing\";\nimport { userLabelAddApi } from \"@/api/user\";\nimport { brandList } from \"@/api/product\";\nimport storeList from \"@/components/storeList\";\nimport userLabel from \"@/components/labelList\";\nimport goodsList from '@/components/goodsList';\nimport storeLabelList from \"@/components/storeLabelList\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"addPiecesDiscount\",\n  components: {\n    userLabel: userLabel,\n    goodsList: goodsList,\n    storeLabelList: storeLabelList,\n    storeList: storeList\n  },\n  data: function data() {\n    return {\n      storesList: [],\n      storeModals: false,\n      roterPre: Setting.roterPre,\n      storeDataLabel: [],\n      storeLabelShow: false,\n      props: {\n        emitPath: false,\n        multiple: true\n      },\n      brandData: [],\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      modals: false,\n      tableData: [],\n      headTab: [{\n        name: \"基础设置\",\n        type: '1'\n      }, {\n        name: \"添加商品\",\n        type: '2'\n      }, {\n        name: \"适用门店\",\n        type: '3'\n      }],\n      discountType: [{\n        name: \"第2件半价\",\n        title: \"例：甜筒第2件半价\",\n        id: 1\n      }, {\n        name: \"买1送1\",\n        title: \"例：T恤买1送1\",\n        id: 2\n      }, {\n        name: \"自定义优惠\",\n        title: \"例：第3件打5折\",\n        id: 3\n      }],\n      activityType: [{\n        name: '优惠券',\n        type: '5'\n      }, {\n        name: '满减满折',\n        type: '3'\n      }, {\n        name: '限时折扣',\n        type: '1'\n      } // {\n      // \tname:'满送活动',\n      // \ttype:'4'\n      // }\n      ],\n      currentTab: '1',\n      dataLabel: [],\n      labelShow: false,\n      formValidate: {\n        applicable_type: 1,\n        name: '',\n        section_time: [],\n        discount: 50,\n        threshold: 2,\n        is_label: 0,\n        label_id: [],\n        is_overlay: '0',\n        overlay: [],\n        product_partake_type: 1,\n        product_id: [],\n        n_piece_n_discount: 1,\n        brand_id: [],\n        store_label_id: []\n      },\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '请输入活动名称',\n          trigger: 'blur'\n        }],\n        section_time: [{\n          required: true,\n          type: 'array',\n          message: '请选择活动时间',\n          trigger: 'change'\n        }],\n        is_overlay: [{\n          required: true,\n          message: '请设置优惠叠加',\n          trigger: 'change'\n        }],\n        threshold: [{\n          type: 'number',\n          required: true,\n          message: '请填写优惠设置',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 90;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n    labelBottom: function labelBottom() {\n      return this.isMobile ? undefined : 15;\n    }\n  }),\n  mounted: function mounted() {\n    this.setCopyrightShow({\n      value: false\n    });\n\n    if (this.$route.params.id != 0) {\n      this.getDiscountInfo();\n    }\n  },\n  destroyed: function destroyed() {\n    this.setCopyrightShow({\n      value: true\n    });\n  },\n  methods: _objectSpread({}, mapMutations('admin/layout', ['setCopyrightShow']), {\n    //删除门店\n    delte: function delte(row) {\n      var _this = this;\n\n      this.storesList.forEach(function (item, index) {\n        if (row.id == item.id) {\n          _this.storesList.splice(index, 1);\n        }\n      });\n    },\n    //添加门店\n    addStore: function addStore() {\n      this.storeModals = true;\n    },\n    //关闭门店弹窗\n    cancelStore: function cancelStore() {\n      this.storeModals = false;\n    },\n    getStoreId: function getStoreId(data) {\n      this.storeModals = false;\n      var list = this.storesList.concat(data);\n      var uni = this.unique(list);\n      this.storesList = uni;\n    },\n    closeStoreLabel: function closeStoreLabel(label) {\n      var index = this.storeDataLabel.indexOf(this.storeDataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.storeDataLabel.splice(index, 1);\n    },\n    openStoreLabel: function openStoreLabel() {\n      this.storeLabelShow = true;\n      this.$refs.storeLabel.storeLabel(JSON.parse(JSON.stringify(this.storeDataLabel)));\n    },\n    activeStoreData: function activeStoreData(storeDataLabel) {\n      this.storeLabelShow = false;\n      this.storeDataLabel = storeDataLabel;\n    },\n    // 标签弹窗关闭\n    storeLabelClose: function storeLabelClose() {\n      this.storeLabelShow = false;\n    },\n    goodTap: function goodTap(e) {\n      if (e == 4) {\n        this.getBrandList();\n      }\n    },\n    // 品牌列表\n    getBrandList: function getBrandList() {\n      var _this2 = this;\n\n      brandList().then(function (res) {\n        _this2.brandData = res.data;\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    discountTypeTap: function discountTypeTap(item) {\n      this.formValidate.n_piece_n_discount = item.id;\n\n      if (item.id == 1) {\n        this.formValidate.threshold = 2;\n        this.formValidate.discount = 50;\n      } else if (item.id == 2) {\n        this.formValidate.threshold = 2;\n        this.formValidate.discount = 0;\n      } else {\n        this.formValidate.threshold = 1;\n        this.formValidate.discount = 100;\n      }\n    },\n    getDiscountInfo: function getDiscountInfo() {\n      var _this3 = this;\n\n      this.formValidate.n_piece_n_discount = 0;\n      discountInfo(this.$route.params.id).then(function (res) {\n        _this3.formValidate = res.data.info;\n        _this3.storesList = res.data.info.stores || [];\n        _this3.formValidate.discount = parseFloat(res.data.info.discount);\n        _this3.formValidate.threshold = parseFloat(res.data.info.threshold);\n        _this3.formValidate.is_overlay = _this3.formValidate.is_overlay.toString();\n        _this3.tableData = res.data.info.products;\n        _this3.dataLabel = res.data.info.label_id;\n        _this3.storeDataLabel = res.data.info.store_label_id;\n\n        if (res.data.info.product_partake_type == 4) {\n          _this3.getBrandList();\n        }\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    del: function del(row) {\n      var _this4 = this;\n\n      this.tableData.forEach(function (i, index) {\n        if (row.id == i.id) {\n          return _this4.tableData.splice(index, 1);\n        } else {\n          i.attrValue.forEach(function (j, indexn) {\n            if (row.id == j.id) {\n              if (i.attrValue.length == 1) {\n                return _this4.tableData.splice(index, 1);\n              } else {\n                return i.attrValue.splice(indexn, 1);\n              }\n            }\n          });\n        }\n      });\n    },\n    addLabel: function addLabel() {\n      this.$modalForm(userLabelAddApi(0)).then(function () {});\n    },\n    addGoods: function addGoods() {\n      this.modals = true;\n    },\n    cancel: function cancel() {\n      this.modals = false;\n    },\n    //对象数组去重；\n    unique: function unique(arr) {\n      var res = new Map();\n      return arr.filter(function (arr) {\n        return !res.has(arr.id) && res.set(arr.id, 1);\n      });\n    },\n    getProductId: function getProductId(data) {\n      this.modals = false;\n      var list = this.tableData.concat(data);\n      var uni = this.unique(list);\n      uni.forEach(function (i) {\n        i.attrValue.forEach(function (j) {\n          j.cate_name = i.cate_name;\n          j.store_label = i.store_label;\n        });\n      });\n      this.tableData = uni;\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.formValidate.section_time = e;\n    },\n    closeLabel: function closeLabel(label) {\n      var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n        return d.id == label.id;\n      })[0]);\n      this.dataLabel.splice(index, 1);\n    },\n    activeData: function activeData(dataLabel) {\n      this.labelShow = false;\n      this.dataLabel = dataLabel;\n    },\n    openLabel: function openLabel(row) {\n      this.labelShow = true;\n      this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    // 标签弹窗关闭\n    labelClose: function labelClose() {\n      this.labelShow = false;\n    },\n    // 上一页：\n    upTab: function upTab() {\n      if (this.currentTab == 1 && this.formValidate.product_type != 0) {\n        this.currentTab = (Number(this.currentTab) - 2).toString();\n      } else {\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    // 下一页；\n    downTab: function downTab(name) {\n      var _this5 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (!_this5.formValidate.threshold) {\n            return _this5.$Message.warning(\"请填写优惠设置的件数\");\n          }\n\n          if (_this5.formValidate.discount == null) {\n            return _this5.$Message.warning(\"请填写优惠设置的折扣\");\n          }\n\n          if (_this5.formValidate.is_label && !_this5.dataLabel.length) {\n            return _this5.$Message.warning(\"请选择用户关联标签\");\n          }\n\n          if (parseInt(_this5.formValidate.is_overlay) && !_this5.formValidate.overlay.length) {\n            return _this5.$Message.warning(\"请选择叠加的营销活动\");\n          }\n\n          if (_this5.formValidate.section_time[0] == '') {\n            return _this5.$Message.warning(\"请选择活动时间\");\n          }\n\n          _this5.currentTab = (Number(_this5.currentTab) + 1).toString();\n        } else {\n          _this5.$Message.warning(\"请完善数据\");\n        }\n      });\n    },\n    handleSubmit: function handleSubmit(name) {\n      var _this6 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (!_this6.formValidate.threshold) {\n            return _this6.$Message.warning(\"请填写优惠设置\");\n          }\n\n          if (_this6.formValidate.is_label && !_this6.dataLabel.length) {\n            return _this6.$Message.warning(\"请选择用户关联标签\");\n          }\n\n          if (parseInt(_this6.formValidate.is_overlay) && !_this6.formValidate.overlay.length) {\n            return _this6.$Message.warning(\"请选择叠加的营销活动\");\n          }\n\n          if (_this6.formValidate.section_time[0] == '') {\n            return _this6.$Message.warning(\"请选择活动时间\");\n          }\n\n          if (_this6.formValidate.product_partake_type == 4 && !_this6.formValidate.brand_id.length) {\n            return _this6.$Message.error('请添加指定品牌');\n          }\n\n          if (_this6.formValidate.product_partake_type == 5) {\n            var labelIds = [];\n\n            _this6.storeDataLabel.forEach(function (item) {\n              labelIds.push(item.id);\n            });\n\n            if (!labelIds.length) {\n              return _this6.$Message.error('请添加指定标签');\n            }\n\n            _this6.formValidate.store_label_id = labelIds;\n          } // 用户标签\n\n\n          var activeIds = [];\n\n          _this6.dataLabel.forEach(function (item) {\n            activeIds.push(item.id);\n          });\n\n          if (_this6.formValidate.is_label == 0) {\n            _this6.formValidate.label_id = [];\n          } else {\n            _this6.formValidate.label_id = activeIds;\n          }\n\n          var product_id = [];\n\n          _this6.tableData.forEach(function (item) {\n            var obj = {\n              product_id: item.id,\n              unique: []\n            };\n\n            if (item.attrValue.length) {\n              item.attrValue.forEach(function (j) {\n                obj.unique.push(j.unique);\n              });\n            }\n\n            product_id.push(obj);\n          });\n\n          if (_this6.formValidate.product_partake_type == 2 && !product_id.length) {\n            return _this6.$Message.error('请添加商品');\n          }\n\n          _this6.formValidate.product_id = product_id;\n          var storeId = [];\n\n          _this6.storesList.forEach(function (item) {\n            storeId.push(item.id);\n          });\n\n          if (_this6.formValidate.applicable_type == 2 && !storeId.length) {\n            return _this6.$Message.warning('请添加适用门店');\n          }\n\n          _this6.formValidate.applicable_store_id = storeId;\n          saveDiscount(2, _this6.$route.params.id, _this6.formValidate).then(function (res) {\n            _this6.$router.push({\n              path: \"\".concat(_this6.roterPre, \"/marketing/discount/pieces_discount\")\n            });\n\n            _this6.$Message.success(res.msg);\n          }).catch(function (err) {\n            _this6.$Message.error(err.msg);\n          });\n        } else {\n          _this6.$Message.warning(\"请完善数据\");\n        }\n      });\n    }\n  })\n};", null]}