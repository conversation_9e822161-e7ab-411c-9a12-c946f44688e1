{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_menu.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_menu.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import toolCom from '@/components/mobileConfigRight/index.js'\n    import { mapState, mapMutations, mapActions } from 'vuex'\n    import rightBtn from '@/components/rightBtn/index.vue';\n    export default {\n        name: 'c_home_menu',\n        cname: '导航组',\n        componentsName: 'home_menu',\n        props: {\n            activeIndex: {\n                type: null\n            },\n            num: {\n                type: null\n            },\n            index: {\n                type: null\n            }\n        },\n        components: {\n            ...toolCom,\n            rightBtn\n        },\n        data () {\n            return {\n                configObj: {},\n                rCom: [\n                    {\n                        components: toolCom.c_set_up,\n                        configNme: 'setUp'\n                    }\n                ],\n\t\t\t\trComContent: [\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleLeft'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'menuStyleConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'number'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'showConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\toneContent: [\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'rowsNum'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttwoContent: [\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleContent'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_menu_list,\n\t\t\t\t\t    configNme: 'menuConfig'\n\t\t\t\t\t}\n\t\t\t\t],\n                oneStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleRight'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'filletImg'\n\t\t\t\t\t}\n                ],\n                twoStyle: [\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titlePointer'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_radio,\n\t\t\t\t\t    configNme: 'toneConfig'\n\t\t\t\t\t}\n                ],\n\t\t\t\tthreeStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'pointerColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'pointerBgColor'\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tfourStyle:[\n\t\t\t\t\t{\n\t\t\t\t\t\tcomponents: toolCom.c_title,\n\t\t\t\t\t\tconfigNme: 'titleCurrency'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_bg_color,\n\t\t\t\t\t    configNme: 'bottomBgColor'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'topConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'bottomConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'prConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_slider,\n\t\t\t\t\t    configNme: 'mbConfig'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t    components: toolCom.c_fillet,\n\t\t\t\t\t    configNme: 'fillet'\n\t\t\t\t\t}\n\t\t\t\t],\n                type:0,  //展示样式索引\n                setUp:0, //0：内容；1：样式\n\t\t\t\ttype2:0, //导航样式索引\n\t\t\t\ttype3:0 //色调索引\n            }\n        },\n        watch: {\n            num (nVal) {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n                this.configObj = value;\n            },\n            configObj: {\n                handler (nVal, oVal) {\n                    this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n                },\n                deep: true\n            },\n            'configObj.setUp.tabVal': {\n                handler (nVal, oVal) {\n                    this.setUp = nVal;\n                    var arr = [this.rCom[0]]\n                    if (nVal == 0) {\n\t\t\t\t\t\tlet rCom = arr.concat(this.rComContent);\n\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.twoContent);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.oneContent);\n\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.twoContent);\n\t\t\t\t\t\t} \n                    } else {\n\t\t\t\t\t\tif(this.type2 == 2){\n\t\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\t\tthis.rCom = arr.concat(this.fourStyle)\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type3 == 0){\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom = arr.concat(this.oneStyle);\n\t\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle)\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type3 == 0){\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom3 = rCom2.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom3.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n                    }\n                },\n                deep: true\n            },\n\t\t\t'configObj.menuStyleConfig.tabVal':{\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type2 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp == 0){\n\t\t\t\t\t\tlet rCom = arr.concat(this.rComContent);\n\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.twoContent);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.oneContent);\n\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.twoContent);\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(nVal == 2){\n\t\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\t\tthis.rCom = arr.concat(this.fourStyle)\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type3 == 0){\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom = arr.concat(this.oneStyle);\n\t\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle);\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type3 == 0){\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom3 = rCom2.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom3.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n            'configObj.showConfig.tabVal': {\n                handler (nVal, oVal) {\n                    this.type = nVal;\n                    var arr = [this.rCom[0]];\n                    if(this.setUp){\n\t\t\t\t\t\tif(this.type2 == 2){\n\t\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\t\tthis.rCom = arr.concat(this.fourStyle)\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type3 == 0){\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom = arr.concat(this.oneStyle);\n\t\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle);\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(this.type3 == 0){\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom3 = rCom2.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom3.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n                    }else{\n\t\t\t\t\t\tlet rCom = arr.concat(this.rComContent);\n\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.twoContent);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.oneContent);\n\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.twoContent);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n                },\n                deep: true\n            },\n\t\t\t'configObj.toneConfig.tabVal': {\n\t\t\t\thandler (nVal, oVal) {\n\t\t\t\t\tthis.type3 = nVal;\n\t\t\t\t\tvar arr = [this.rCom[0]];\n\t\t\t\t\tif(this.setUp){\n\t\t\t\t\t\tif(this.type2 == 2){\n\t\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\t\tthis.rCom = arr.concat(this.fourStyle)\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom = arr.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom = arr.concat(this.oneStyle);\n\t\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.fourStyle)\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(nVal == 0){\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.twoStyle)\n\t\t\t\t\t\t\t\t\tlet rCom3 = rCom2.concat(this.threeStyle)\n\t\t\t\t\t\t\t\t\tthis.rCom = rCom3.concat(this.fourStyle)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tlet rCom = arr.concat(this.rComContent);\n\t\t\t\t\t\tif(this.type == 0){\n\t\t\t\t\t\t\tthis.rCom = rCom.concat(this.twoContent);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tlet rCom2 = rCom.concat(this.oneContent);\n\t\t\t\t\t\t\tthis.rCom = rCom2.concat(this.twoContent);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n        },\n        mounted () {\n            this.$nextTick(() => {\n                let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n                this.configObj = value;\n            })\n        },\n        methods: {\n            getConfig (data) {\n\n            }\n        }\n    }\n", null]}