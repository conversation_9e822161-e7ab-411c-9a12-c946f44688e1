{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\stockEdit.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { productAttrsApi, productSaveStocksApi } from '@/api/product.js';\nexport default {\n  name: 'stockEdit',\n  props: {},\n  data: function data() {\n    return {\n      id: 0,\n      specType: 0,\n      batchShow: false,\n      batchStock: 0,\n      batchPm: 1,\n      modals: false,\n      loading: false,\n      stockData: [],\n      columns: [{\n        title: \"图片\",\n        slot: \"image\",\n        minWidth: 20\n      }, {\n        title: \"产品规格\",\n        key: \"suk\",\n        minWidth: 90\n      }, {\n        title: \"商品条形码\",\n        key: \"bar_code\",\n        minWidth: 35\n      }, {\n        title: \"商品编码\",\n        key: \"code\",\n        minWidth: 35\n      }, {\n        title: \"当前库存\",\n        key: \"stock\",\n        minWidth: 10\n      }, {\n        title: \"入/出库数量\",\n        slot: \"num\",\n        minWidth: 200\n      }]\n    };\n  },\n  methods: {\n    // 批量设置；\n    countBatch: function countBatch() {\n      var _this = this;\n\n      this.batchStock = Math.abs(this.batchStock);\n      this.stockData.forEach(function (item) {\n        item.changeNum = _this.batchStock;\n\n        if (_this.batchPm) {\n          item.pm = 1;\n          item.resultNum = parseInt(item.stock) + parseInt(item.changeNum);\n        } else {\n          item.pm = 0;\n\n          if (parseInt(item.stock) <= 0) {\n            item.resultNum = 0;\n          } else {\n            var num = parseInt(item.stock) - parseInt(item.changeNum);\n            item.resultNum = num <= 0 ? 0 : num;\n          }\n        }\n      });\n    },\n    // 批量加减库存\n    inputTap: function inputTap() {\n      this.batchStock = this.batchStock.replace(/^\\d{10}$/g, \"0\");\n      this.countBatch();\n    },\n    // 批量设置入库或是出库\n    batchStockTap: function batchStockTap() {\n      this.countBatch();\n    },\n    // 单个设置\n    countStock: function countStock(row) {\n      if (row.pm) {\n        row.resultNum = parseInt(row.stock) + parseInt(row.changeNum);\n      } else {\n        if (parseInt(row.stock) <= 0) {\n          row.resultNum = 0;\n        } else {\n          var num = parseInt(row.stock) - parseInt(row.changeNum);\n          row.resultNum = num <= 0 ? 0 : num;\n        }\n      }\n\n      this.stockData.forEach(function (item) {\n        if (row.id == item.id) {\n          item.changeNum = row.changeNum;\n          item.resultNum = row.resultNum;\n          item.pm = row.pm;\n        }\n      });\n    },\n    // 设置加减库存\n    stockTap: function stockTap(row) {\n      this.countStock(row);\n    },\n    // 设置入库或是出库\n    changeTap: function changeTap(row) {\n      row.changeNum = row.changeNum.replace(/^\\d{10}$/g, \"0\");\n      this.countStock(row);\n    },\n    batchTap: function batchTap() {\n      this.batchShow = !this.batchShow;\n    },\n    productAttrs: function productAttrs(data) {\n      var _this2 = this;\n\n      this.specType = data.spec_type;\n      this.id = data.id;\n      productAttrsApi(data.id).then(function (res) {\n        var data = res.data;\n        data.forEach(function (item) {\n          item.resultNum = item.stock;\n          item.changeNum = 0;\n          item.pm = 1;\n        });\n        _this2.stockData = data;\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    productSaveStocks: function productSaveStocks() {\n      var _this3 = this;\n\n      var attrs = [];\n      this.stockData.forEach(function (item) {\n        var data = {\n          \"unique\": item.unique,\n          \"pm\": item.pm,\n          \"stock\": item.changeNum\n        };\n        attrs.push(data);\n      });\n      productSaveStocksApi({\n        attrs: attrs\n      }, this.id).then(function (res) {\n        _this3.$Message.success('修改成功');\n\n        _this3.cancel();\n\n        _this3.$emit('stockChange', res.data.stock);\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    cancel: function cancel() {\n      this.modals = false;\n      this.batchShow = false;\n      this.batchPm = 1;\n      this.batchStock = 0;\n    }\n  }\n};", null]}