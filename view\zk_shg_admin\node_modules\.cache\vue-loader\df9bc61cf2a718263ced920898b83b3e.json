{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue?vue&type=template&id=95adbe0c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\handle\\task.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Modal',{attrs:{\"z-index\":1,\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"等级任务\",\"mask-closable\":false,\"width\":\"950\"},on:{\"on-cancel\":_vm.handleReset},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('Form',{ref:\"levelFrom\",attrs:{\"model\":_vm.levelFrom,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"状态：\"}},[_c('Select',{attrs:{\"placeholder\":\"是否显示\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.levelFrom.is_show),callback:function ($$v) {_vm.$set(_vm.levelFrom, \"is_show\", $$v)},expression:\"levelFrom.is_show\"}},[_c('Option',{attrs:{\"value\":\"1\"}},[_vm._v(\"显示\")]),_c('Option',{attrs:{\"value\":\"0\"}},[_vm._v(\"不显示\")])],1)],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"任务名称：\",\"prop\":\"status2\",\"label-for\":\"status2\"}},[_c('Input',{staticStyle:{\"width\":\"100%\"},attrs:{\"search\":\"\",\"enter-button\":\"\",\"placeholder\":\"请输入任务名称\"},on:{\"on-search\":_vm.userSearchs},model:{value:(_vm.levelFrom.name),callback:function ($$v) {_vm.$set(_vm.levelFrom, \"name\", $$v)},expression:\"levelFrom.name\"}})],1)],1)],1)],1),_c('Divider',{attrs:{\"dashed\":\"\"}}),_c('Row',{attrs:{\"type\":\"flex\"}},[_c('Col',_vm._b({staticClass:\"mb15\"},'Col',_vm.grid,false),[_c('Button',{attrs:{\"type\":\"primary\",\"icon\":\"md-add\"},on:{\"click\":_vm.add}},[_vm._v(\"添加等级任务\")])],1),_c('Col',{staticClass:\"userAlert\",attrs:{\"span\":\"24\"}},[_c('Alert',{attrs:{\"show-icon\":\"\",\"closable\":\"\"}},[_vm._v(\"添加等级任务,任务类型中的{$num}会自动替换成限定数量+系统预设的单位生成任务名\")])],1)],1),_c('Divider',{attrs:{\"dashed\":\"\"}}),_c('Table',{ref:\"table\",attrs:{\"columns\":_vm.columns1,\"data\":_vm.levelLists,\"loading\":_vm.loading,\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"is_shows\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('i-switch',{attrs:{\"value\":row.is_show,\"true-value\":1,\"false-value\":0,\"size\":\"large\"},on:{\"on-change\":function($event){return _vm.onchangeIsShow(row)}},model:{value:(row.is_show),callback:function ($$v) {_vm.$set(row, \"is_show\", $$v)},expression:\"row.is_show\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"显示\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"隐藏\")])])]}},{key:\"is_musts\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('i-switch',{attrs:{\"value\":row.is_must,\"true-value\":1,\"false-value\":0,\"size\":\"large\"},on:{\"on-change\":function($event){return _vm.onchangeIsMust(row)}},model:{value:(row.is_must),callback:function ($$v) {_vm.$set(row, \"is_must\", $$v)},expression:\"row.is_must\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"全部完成\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"达成其一\")])])]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.edit(row)}}},[_vm._v(\"编辑  | \")]),_c('a',{on:{\"click\":function($event){return _vm.del(row,'删除等级任务',index)}}},[_vm._v(\"  删除\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.levelFrom.limit},on:{\"on-change\":_vm.pageChange}})],1),_c('edit-from',{ref:\"edits\",attrs:{\"FromData\":_vm.FromData,\"titleType\":_vm.titleType},on:{\"submitFail\":_vm.submitFail}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}