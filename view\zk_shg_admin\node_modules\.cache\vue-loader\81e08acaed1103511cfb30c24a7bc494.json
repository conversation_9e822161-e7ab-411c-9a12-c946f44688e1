{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=style&index=0&id=2f96c05e&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1750814717059}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1725352506768}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.transfer-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n  .header-left {\r\n    .page-title {\r\n      margin: 0 0 8px 0;\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #303133;\r\n    }\r\n\r\n    .page-desc {\r\n      margin: 0;\r\n      color: #909399;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .header-right {\r\n    .el-button {\r\n      margin-left: 12px;\r\n    }\r\n  }\r\n}\r\n\r\n.filter-container {\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stats-container {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n\r\n  .stat-card {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 24px;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .stat-icon {\r\n      width: 60px;\r\n      height: 60px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 16px;\r\n      font-size: 24px;\r\n      color: white;\r\n\r\n      &.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n      &.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n      &.approved { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n      &.completed { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n    }\r\n\r\n    .stat-content {\r\n      .stat-number {\r\n        font-size: 28px;\r\n        font-weight: 600;\r\n        color: #303133;\r\n        line-height: 1;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .stat-label {\r\n        color: #909399;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-container {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n}\r\n\r\n.product-info {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .product-name {\r\n    font-weight: 500;\r\n    color: #303133;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .product-spec {\r\n    font-size: 12px;\r\n    color: #909399;\r\n    margin-bottom: 2px;\r\n  }\r\n\r\n  .transfer-qty {\r\n    font-size: 12px;\r\n    color: #f56c6c;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.transfer-stores {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .from-store, .to-store {\r\n    padding: 4px 8px;\r\n    background: #f0f9ff;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n    color: #1890ff;\r\n  }\r\n\r\n  .transfer-arrow {\r\n    margin: 0 8px;\r\n    color: #909399;\r\n  }\r\n}\r\n\r\n.text-muted {\r\n  color: #c0c4cc;\r\n}\r\n\r\n.batch-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-top: 16px;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n\r\n  .selected-count {\r\n    color: #606266;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.detail-content {\r\n  .detail-section {\r\n    margin-bottom: 24px;\r\n\r\n    h4 {\r\n      margin: 0 0 16px 0;\r\n      color: #303133;\r\n      font-size: 16px;\r\n      border-left: 4px solid #409eff;\r\n      padding-left: 12px;\r\n    }\r\n\r\n    .detail-item {\r\n      margin-bottom: 12px;\r\n\r\n      .label {\r\n        color: #909399;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .value {\r\n        color: #303133;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .product-detail {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 16px;\r\n      background: #f8f9fa;\r\n      border-radius: 8px;\r\n\r\n      .product-name {\r\n        font-weight: 500;\r\n        color: #303133;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .product-spec, .transfer-qty {\r\n        font-size: 14px;\r\n        color: #606266;\r\n        margin-bottom: 2px;\r\n      }\r\n    }\r\n\r\n    .remark-content {\r\n      padding: 12px 16px;\r\n      background: #f8f9fa;\r\n      border-radius: 8px;\r\n      color: #303133;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n}\r\n", null]}