{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue?vue&type=template&id=5642b922&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\hotpotModal\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Modal',{attrs:{\"title\":\"编辑热区\",\"mask-closable\":false,\"fullscreen\":\"\"},on:{\"on-visible-change\":_vm.openModal},model:{value:(_vm.dialogVisible),callback:function ($$v) {_vm.dialogVisible=$$v},expression:\"dialogVisible\"}},[_c('div',{staticClass:\"operationFloor\"},[_c('div',{staticClass:\"imgBox\",on:{\"mouseup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"left\",37,$event.key,[\"Left\",\"ArrowLeft\"])){ return null; }if('button' in $event && $event.button !== 0){ return null; }$event.stopPropagation();return _vm.changeStop()}}},[_c('div',{ref:\"container\",staticClass:\"container\",attrs:{\"id\":\"img-box-container\"}},[_c('img',{ref:\"backgroundImg\",attrs:{\"src\":_vm.imgs,\"ondragstart\":\"return false;\",\"oncontextmenu\":\"return false;\",\"onselect\":\"document.selection.empty();\",\"alt\":\"img\"},on:{\"mousedown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"left\",37,$event.key,[\"Left\",\"ArrowLeft\"])){ return null; }if('button' in $event && $event.button !== 0){ return null; }$event.stopPropagation();return _vm.mouseDown($event)}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.caseShow),expression:\"caseShow\"}],staticClass:\"area\",style:({\n              width: _vm.areaWidth + 'px',\n              height: _vm.areaHeight + 'px',\n              left: _vm.starX + 'px',\n              top: _vm.starY + 'px',\n            })}),_vm._l((_vm.areaData),function(item,index){return _c('AreaBox',{key:'area' + index,attrs:{\"area-data-index\":index,\"link\":item.link,\"title\":item.title,\"type\":parseInt(item.type),\"area-init\":item,\"parent-width\":_vm.parentWidth,\"parent-height\":_vm.parentHeight},on:{\"update:areaInit\":function($event){item=$event},\"update:area-init\":function($event){item=$event},\"delAreaBox\":_vm.delAreaBox,\"addURL\":_vm.addURL}})})],2)]),_c('div',{staticClass:\"form\"},[_c('h2',{staticClass:\"mb20\"},[_vm._v(\"图片热区\")]),_c('Alert',{staticClass:\"mb-20 w-400\",attrs:{\"show-icon\":\"\"}},[_vm._v(\"框选热区范围，双击设置热区信息\")]),_vm._l((_vm.areaData),function(item,index){return _c('div',{key:index,staticClass:\"form-row\"},[_c('div',{staticClass:\"form-item\"},[_c('span',{staticClass:\"num\"},[_vm._v(\"热区\"+_vm._s(item.number))])]),_c('div',{staticClass:\"form-item label\"},[_c('div',{on:{\"click\":function($event){return _vm.getLink(index)}}},[_c('Input',{style:(_vm.linkInputStyle),attrs:{\"icon\":\"ios-arrow-forward\",\"readonly\":\"\",\"placeholder\":\"选择跳转链接\"},model:{value:(item.link),callback:function ($$v) {_vm.$set(item, \"link\", $$v)},expression:\"item.link\"}})],1)]),_c('i',{staticClass:\"el-icon-delete\",on:{\"click\":function($event){return _vm.delAreaBox(index)}}})])})],2)]),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{staticClass:\"mr20\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.saveAreaData}},[_vm._v(\"\\n        完成\\n      \")])],1)]),_c('linkaddress',{ref:\"linkaddres\",on:{\"linkUrl\":_vm.linkUrl}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}