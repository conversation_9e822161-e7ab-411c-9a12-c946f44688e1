{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_page_ueditor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_page_ueditor.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nexport default {\n  name: 'c_page_ueditor',\n  props: {\n    configObj: {\n      type: Object\n    },\n    configNme: {\n      type: String\n    }\n  },\n  components: {\n    WangEditor: WangEditor\n  },\n  data: function data() {\n    return {\n      myConfig: {\n        autoHeightEnabled: false,\n        // 编辑器不自动被内容撑高\n        initialFrameHeight: 350,\n        // 初始容器高度\n        initialFrameWidth: '100%',\n        // 初始容器宽度\n        UEDITOR_HOME_URL: '/admin/UEditor/',\n        serverUrl: ''\n      },\n      description: '',\n      defaults: {},\n      configData: {},\n      val: ''\n    };\n  },\n  created: function created() {\n    this.defaults = this.configObj;\n    this.configData = this.configObj[this.configNme];\n    this.val = this.configData.val;\n  },\n  watch: {\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.defaults = nVal;\n        this.configData = nVal[this.configNme]; // this.val = this.configData.val || '';\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    getEditorContent: function getEditorContent(data) {\n      this.configData.val = data;\n    },\n    // 添加自定义弹窗\n    addCustomDialog: function addCustomDialog(editorId) {\n      window.UE.registerUI('test-dialog', function (editor, uiName) {\n        // 创建 dialog\n        var dialog = new window.UE.ui.Dialog({\n          iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n          editor: editor,\n          name: uiName,\n          title: '上传图片',\n          cssRules: 'width:1200px;height:500px;padding:20px;'\n        });\n        this.dialog = dialog;\n        var btn = new window.UE.ui.Button({\n          name: 'dialog-button',\n          title: '上传图片',\n          cssRules: \"background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;\",\n          onclick: function onclick() {\n            // 渲染dialog\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 37);\n      window.UE.registerUI('video-dialog', function (editor, uiName) {\n        var dialog = new window.UE.ui.Dialog({\n          iframeUrl: '/admin/widget.video/index.html?fodder=video',\n          editor: editor,\n          name: uiName,\n          title: '上传视频',\n          cssRules: 'width:1000px;height:500px;padding:20px;'\n        });\n        this.dialog = dialog;\n        var btn = new window.UE.ui.Button({\n          name: 'video-button',\n          title: '上传视频',\n          cssRules: \"background-image: url(../../../assets/images/icons.png);background-position: -320px -20px;\",\n          onclick: function onclick() {\n            // 渲染dialog\n            dialog.render();\n            dialog.open();\n          }\n        });\n        return btn;\n      }, 38);\n    }\n  }\n};", null]}