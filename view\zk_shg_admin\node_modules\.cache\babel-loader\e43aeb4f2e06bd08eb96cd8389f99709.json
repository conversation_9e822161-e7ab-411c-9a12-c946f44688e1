{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAttr\\addAttr.vue", "mtime": 1677832908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { ruleAddApi, ruleInfoApi } from \"@/api/product\";\nexport default {\n  name: \"addAttr\",\n  data: function data() {\n    return {\n      spinShow: false,\n      modal_loading: false,\n      grid: {\n        xl: 3,\n        lg: 3,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      modal: false,\n      index: 1,\n      rules: {\n        rule_name: [{\n          required: true,\n          message: \"请输入分类名称\",\n          trigger: \"blur\"\n        }]\n      },\n      formDynamic: {\n        rule_name: \"\",\n        spec: []\n      },\n      attrsName: \"\",\n      attrsVal: \"\",\n      formDynamicNameData: [],\n      isBtn: false,\n      formDynamicName: [],\n      results: [],\n      result: [],\n      ids: 0,\n      title: \"添加商品规格\"\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 110;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  methods: {\n    onCancel: function onCancel() {\n      this.clear();\n    },\n    // 添加按钮\n    addBtn: function addBtn() {\n      if (this.formDynamic.rule_name.trim() == '') {\n        return this.$Message.error('请输入分类名称');\n      }\n\n      this.isBtn = true;\n    },\n    // 详情\n    getIofo: function getIofo(row) {\n      var _this = this;\n\n      this.spinShow = true;\n      this.ids = row.id;\n      this.title = \"编辑商品规格\";\n      ruleInfoApi(row.id).then(function (res) {\n        _this.formDynamic = res.data.info;\n        _this.spinShow = false;\n      }).catch(function (res) {\n        _this.spinShow = false;\n\n        _this.$Message.error(res.msg);\n      });\n    },\n    // 提交\n    handleSubmit: function handleSubmit(name) {\n      var _this2 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this2.formDynamic.spec.length === 0) {\n            return _this2.$Message.warning(\"请至少添加一条商品规格！\");\n          }\n\n          _this2.modal_loading = true;\n          setTimeout(function () {\n            ruleAddApi(_this2.formDynamic, _this2.ids).then(function (res) {\n              _this2.$Message.success(res.msg);\n\n              setTimeout(function () {\n                _this2.modal = false;\n                _this2.modal_loading = false;\n              }, 500);\n              setTimeout(function () {\n                _this2.$emit(\"getList\");\n\n                _this2.clear();\n              }, 600);\n            }).catch(function (res) {\n              _this2.modal_loading = false;\n\n              _this2.$Message.error(res.msg);\n            });\n          }, 1200);\n        } else {\n          return false;\n        }\n      });\n    },\n    clear: function clear() {\n      this.$refs[\"formDynamic\"].resetFields();\n      this.formDynamic.spec = [];\n      this.isBtn = false;\n      this.ids = 0;\n      this.title = \"添加商品规格\";\n      this.attrsName = \"\";\n      this.attrsVal = \"\";\n    },\n    // 取消\n    offAttrName: function offAttrName() {\n      this.isBtn = false;\n    },\n    cancle: function cancle() {\n      this.modal = false;\n      this.clear();\n    },\n    // 删除\n    handleRemove: function handleRemove(index) {\n      this.formDynamic.spec.splice(index, 1);\n    },\n    // 删除属性\n    handleRemove2: function handleRemove2(item, index) {\n      item.splice(index, 1);\n    },\n    // 添加规则名称\n    createAttrName: function createAttrName() {\n      if (this.attrsName && this.attrsVal) {\n        var data = {\n          value: this.attrsName,\n          detail: [this.attrsVal]\n        };\n        this.formDynamic.spec.push(data);\n        var hash = {};\n        this.formDynamic.spec = this.formDynamic.spec.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next.value] ? \"\" : hash[next.value] = true && item.push(next);\n          return item;\n        }, []);\n        this.attrsName = \"\";\n        this.attrsVal = \"\";\n        this.isBtn = false;\n      } else {\n        this.$Message.warning(\"请添加规格名称或规格值\");\n      }\n    },\n    // 添加属性\n    createAttr: function createAttr(num, idx) {\n      if (num) {\n        this.formDynamic.spec[idx].detail.push(num);\n        var hash = {};\n        this.formDynamic.spec[idx].detail = this.formDynamic.spec[idx].detail.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next] ? \"\" : hash[next] = true && item.push(next);\n          return item;\n        }, []);\n      } else {\n        this.$Message.warning(\"请添加属性\");\n      }\n    }\n  }\n};", null]}