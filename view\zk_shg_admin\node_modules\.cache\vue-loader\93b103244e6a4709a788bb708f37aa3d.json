{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import vuedraggable from 'vuedraggable'\n    import uploadPictures from '@/components/uploadPictures';\n    import linkaddress from '@/components/linkaddress';\n    export default {\n        name: \"c_foot\",\n        props:{\n            configObj:{\n                type:Object,\n                default:function () {\n                    return {}\n                }\n            },\n            configNme:{\n                type:String,\n                default:''\n            },\n        },\n        components: {\n            uploadPictures,\n            linkaddress,\n            draggable: vuedraggable,\n        },\n        data(){\n            return{\n                val1:'',\n                val2:'',\n                footConfig:[],\n                modalPic:false,\n                isChoice: '单选',\n                itemIndex:0,\n                itemChildIndex:0,\n                gridBtn: {\n                    xl: 4,\n                    lg: 8,\n                    md: 8,\n                    sm: 8,\n                    xs: 8\n                },\n                gridPic: {\n                    xl: 6,\n                    lg: 8,\n                    md: 12,\n                    sm: 12,\n                    xs: 12\n                },\n\t\t\t\tnavStyle:0\n            }\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.footConfig = nVal[this.configNme]\n\t\t\t\t\tthis.navStyle = nVal.navStyleConfig.tabVal\n                },\n                deep: true\n            }\n        },\n        created() {\n            this.footConfig = this.configObj[this.configNme]\n        },\n        methods:{\n            linkUrl(e){\n                this.footConfig[this.itemIndex].link = e\n            },\n            getLink (index){\n                this.itemIndex = index\n                this.$refs.linkaddres.modals = true\n            },\n            // 点击图文封面\n            modalPicTap (parent,child) {\n                this.itemIndex = parent;\n                this.itemChildIndex = child;\n                this.modalPic = true;\n            },\n            // 获取图片信息\n            getPic (pc) {\n                this.$nextTick(() => {\n                    this.footConfig[this.itemIndex].imgList[this.itemChildIndex] = pc.att_dir;\n                    this.modalPic = false;\n                    this.$store.commit('admin/mobildConfig/footUpdata',this.footConfig)\n                })\n            },\n            // 添加模块\n            addMenu(){\n                let obj = {\n                    imgList:['',''],\n                    name:'自定义',\n                    link:''\n                }\n                this.footConfig.push(obj)\n            },\n            deleteMenu(index){\n                this.$Modal.confirm({\n                    title: '提示',\n                    content: '是否确定删除该菜单',\n                    onOk: () => {\n                        this.footConfig.splice(index,1)\n                    },\n                    onCancel: () => {\n                    }\n                });\n\n            }\n        }\n    }\n", null]}