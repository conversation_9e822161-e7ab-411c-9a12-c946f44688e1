{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRemark.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRemark.vue", "mtime": 1644455204000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { integralOrderPutRemarkData } from '@/api/marketing';\nexport default {\n  name: 'orderMark',\n  data: function data() {\n    return {\n      formValidate: {\n        remark: ''\n      },\n      modals: false,\n      ruleValidate: {\n        remark: [{\n          required: true,\n          message: '请输入备注信息',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  props: {\n    orderId: Number\n  },\n  methods: {\n    cancel: function cancel(name) {\n      this.modals = false;\n      this.$refs[name].resetFields();\n    },\n    putRemark: function putRemark(name) {\n      var _this = this;\n\n      var data = {\n        id: this.orderId,\n        remark: this.formValidate\n      };\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          integralOrderPutRemarkData(data).then(\n          /*#__PURE__*/\n          function () {\n            var _ref = _asyncToGenerator(\n            /*#__PURE__*/\n            _regeneratorRuntime.mark(function _callee(res) {\n              return _regeneratorRuntime.wrap(function _callee$(_context) {\n                while (1) {\n                  switch (_context.prev = _context.next) {\n                    case 0:\n                      _this.$Message.success(res.msg);\n\n                      _this.modals = false;\n\n                      _this.$refs[name].resetFields();\n\n                      _this.$emit('submitFail');\n\n                    case 4:\n                    case \"end\":\n                      return _context.stop();\n                  }\n                }\n              }, _callee);\n            }));\n\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }()).catch(function (res) {\n            _this.$Message.error(res.msg);\n          });\n        } else {\n          _this.$Message.warning('请填写备注信息');\n        }\n      });\n    }\n  }\n};", null]}