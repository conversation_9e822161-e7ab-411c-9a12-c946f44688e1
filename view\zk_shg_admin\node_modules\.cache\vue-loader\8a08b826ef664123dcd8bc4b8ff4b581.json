{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\splitList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\splitList.vue", "mtime": 1733822761343}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport expandRow from \"./components/tableExpand.vue\";\nimport {\n  orderList,\n  getOrdeDatas,\n  getDataInfo,\n  getRefundFrom,\n  getnoRefund,\n  refundIntegral,\n  getDistribution,\n  writeUpdate,\n  splitOrderList,\n} from \"@/api/order\";\nimport { mapState, mapMutations } from \"vuex\";\nimport editFrom from \"../../../components/from/from\";\nimport detailsFrom from \"./handle/orderDetails\";\nimport orderRemark from \"./handle/orderRemark\";\nimport orderRecord from \"./handle/orderRecord\";\nimport orderSend from \"./handle/orderSend\";\nimport userDetails from \"@/pages/user/list/handle/userDetails\";\nimport Setting from '@/setting';\nexport default {\n  name: \"table_list\",\n  components: {\n    expandRow,\n    editFrom,\n    detailsFrom,\n    orderRemark,\n    orderRecord,\n    orderSend,\n    userDetails,\n  },\n  props: [\"where\", \"isAll\"],\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      delfromData: {},\n      modal: false,\n      orderList: [],\n      orderCards: [],\n      loading: false,\n      orderId: 0,\n      columns: [\n        {\n          type: \"expand\",\n          width: 30,\n          render: (h, params) => {\n            return h(expandRow, {\n              props: {\n                row: params.row,\n              },\n            });\n          },\n        },\n        {\n          width: 50,\n          align: \"center\",\n          renderHeader: (h, params) => {\n            return h(\n              \"div\",\n              {\n                class: {\n                  \"select-panel\": true,\n                },\n                on: {\n                  mouseenter: (e) => {\n                    this.display = \"block\";\n                  },\n                  mouseleave: (e) => {\n                    this.display = \"none\";\n                  },\n                },\n              },\n              [\n                h(\"Checkbox\", {\n                  props: {\n                    value: this.checkBox,\n                  },\n                  on: {\n                    \"on-change\": (e) => {\n                      this.checkBox = e;\n                      this.$refs.table.selectAll(this.checkBox);\n                      this.$emit(\"on-all\", e ? 0 : -1);\n                    },\n                  },\n                }),\n                h(\n                  \"div\",\n                  {\n                    style: {\n                      position: \"absolute\",\n                      top: 0,\n                      zIndex: 2,\n                      display: this.display,\n                      width: \"80px\",\n                      height: \"100%\",\n                      padding: \"0px 0\",\n                      borderRadius: \"4px\",\n                      backgroundColor: \"#fff\",\n                      boxShadow: \"0 0px 5px rgba(0, 0, 0, 0.2)\",\n                      transform: \"translateX(25%)\",\n                    },\n                  },\n                  [\n                    h(\n                      \"div\",\n                      {\n                        class: {\n                          \"select-item\": true,\n                          on: this.isAll == 0,\n                        },\n                        style: {\n                          padding: \"1px 6px\",\n                          cursor: \"pointer\",\n                          height: \"50%\",\n                        },\n                        on: {\n                          click: (e) => {\n                            if (this.isAll === 0) {\n                              this.$emit(\"on-all\", -1);\n                              this.checkBox = false;\n                              this.$refs.table.selectAll(this.checkBox);\n                            } else {\n                              this.$emit(\"on-all\", 0);\n                              if (!this.formSelection.length) {\n                                this.checkBox = true;\n                                this.$refs.table.selectAll(this.checkBox);\n                              }\n                            }\n                            this.display = \"none\";\n                          },\n                        },\n                      },\n                      \"选择当页\"\n                    ),\n                    h(\n                      \"div\",\n                      {\n                        class: {\n                          \"select-item\": true,\n                          on: this.isAll == 1,\n                        },\n                        style: {\n                          padding: \"1px 6px\",\n                          cursor: \"pointer\",\n                          height: \"50%\",\n                        },\n                        on: {\n                          click: (e) => {\n                            if (this.isAll === 1) {\n                              this.isAll = -1;\n                              this.$emit(\"on-all\", -1);\n                              this.checkBox = false;\n                            } else {\n                              this.isAll = 1;\n                              this.$emit(\"on-all\", 1);\n                              this.checkBox = true;\n                            }\n                            this.$refs.table.selectAll(this.checkBox);\n                            this.display = \"none\";\n                          },\n                        },\n                      },\n                      \"选择全部\"\n                    ),\n                  ]\n                ),\n              ]\n            );\n          },\n          render: (h, params) => {\n            return h(\"Checkbox\", {\n              props: {\n                value: params.row.checkBox,\n              },\n              on: {\n                \"on-change\": (e) => {\n                  if (e) {\n                    this.formSelection.push(params.row);\n                  } else {\n                    this.checkBox = false;\n                    this.formSelection.forEach((item, index) => {\n                      if (item.id === params.row.id) {\n                        this.formSelection.splice(index, 1);\n                      }\n                    });\n                  }\n                  this.$emit(\"on-all\", this.formSelection.length ? 0 : -1);\n                  // this.responseData为查询出的结果数据\n                  // params.row.checkBox = e;\n                  params.row.checkBox = e;\n                  this.orderList[params.index].checkBox = e;\n                },\n              },\n              ref: \"checkbox\",\n              refInFor: true,\n            });\n          },\n        },\n        {\n          title: \"订单号\",\n          align: \"center\",\n          slot: \"order_id\",\n          minWidth: 150,\n        },\n        {\n          title: \"订单类型\",\n          key: \"pink_name\",\n          minWidth: 120,\n        },\n        {\n          title: \"用户信息\",\n          slot: \"nickname\",\n          minWidth: 100,\n        },\n        {\n          title: \"商品信息\",\n          slot: \"info\",\n          minWidth: 330,\n        },\n        {\n          title: \"实际支付\",\n          key: \"pay_price\",\n          minWidth: 70,\n        },\n        {\n          title: \"支付时间\",\n          key: \"_pay_time\",\n          minWidth: 100,\n        },\n        {\n          title: \"支付状态\",\n          key: \"pay_type_name\",\n          minWidth: 80,\n        },\n        {\n          title: \"订单状态\",\n          key: \"statusName\",\n          slot: \"statusName\",\n          minWidth: 120,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          // fixed: \"right\",\n          minWidth: 150,\n          align: \"center\",\n        },\n      ],\n      page: {\n        total: 0, // 总条数\n        pageNum: 1, // 当前页\n        pageSize: 10, // 每页显示条数\n      },\n      data: [],\n      FromData: null,\n      orderDatalist: null,\n      modalTitleSs: \"\",\n      isDelIdList: [],\n      checkBox: false,\n      formSelection: [],\n      selectionCopy: [],\n      display: \"none\",\n      autoDisabled: false,\n      status: 0, //发货状态判断\n      // isAll: -1,\n    };\n  },\n  computed: {\n    ...mapState(\"admin/order\", [\n      \"orderPayType\",\n      \"orderStatus\",\n      \"orderTime\",\n      \"orderNum\",\n      \"fieldKey\",\n      \"orderType\",\n\t\t\t\"orderChartType\"\n    ]),\n  },\n  mounted() {},\n  created() {\n    this.getList();\n  },\n  watch: {\n    orderType: function () {\n      this.page.pageNum = 1;\n      this.getList();\n    },\n    formSelection(value) {\n      this.$emit(\"order-select\", value);\n      if (value.length) {\n        this.$emit(\"auto-disabled\", 0);\n      } else {\n        this.$emit(\"auto-disabled\", 1);\n      }\n      let isDel = value.some((item) => {\n        return item.is_del === 1;\n      });\n      this.getIsDel(isDel);\n      this.getisDelIdListl(value);\n    },\n    orderList: {\n      deep: true,\n      handler(value) {\n        value.forEach((item) => {\n          this.formSelection.forEach((itm) => {\n            if (itm.id === item.id) {\n              item.checkBox = true;\n            }\n          });\n        });\n        const arr = this.orderList.filter((item) => item.checkBox);\n        if (this.orderList.length) {\n          this.checkBox = this.orderList.length === arr.length;\n        } else {\n          this.checkBox = false;\n        }\n      },\n    },\n  },\n  methods: {\n    ...mapMutations(\"admin/order\", [\"getIsDel\", \"getisDelIdListl\"]),\n    selectAll(row) {\n      if (row.length) {\n        this.formSelection = row;\n        this.selectionCopy = row;\n      }\n      this.selectionCopy.forEach((item, index) => {\n        item.checkBox = this.checkBox;\n        this.$set(this.orderList, index, item);\n      });\n    },\n    showUserInfo(row) {\n      this.$refs.userDetails.modals = true;\n      this.$refs.userDetails.getDetails(row.uid);\n    },\n    // 操作\n    changeMenu(row, name) {\n      this.orderId = row.id;\n      switch (name) {\n        case \"1\":\n          this.delfromData = {\n            title: \"修改立即支付\",\n            url: `/order/pay_offline/${row.id}`,\n            method: \"post\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.$emit(\"changeGetTabs\");\n              this.getList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          // this.modalTitleSs = '修改立即支付';\n          break;\n        case \"2\":\n          this.getData(row.id);\n          break;\n        case \"3\":\n          this.$refs.record.modals = true;\n          this.$refs.record.getList(row.id);\n          break;\n        case \"4\":\n          this.$refs.remarks.modals = true;\n          break;\n        case \"5\":\n          this.getOnlyRefundData(row.id,row.refund_type);\n          break;\n\t\t\t\tcase \"55\":\n\t\t\t\t  this.getRefundData(row.id,row.refund_type);\n\t\t\t\t  break;\t\n        case \"6\":\n          this.getRefundIntegral(row.id);\n          break;\n        case \"7\":\n          this.getNoRefundData(row.id);\n          break;\n        case \"8\":\n          this.delfromData = {\n            title: \"修改确认收货\",\n            url: `/order/take/${row.id}`,\n            method: \"put\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.getList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          // this.modalTitleSs = '修改确认收货';\n          break;\n        case \"10\":\n          this.delfromData = {\n            title: \"立即打印订单\",\n            info: \"您确认打印此订单吗?\",\n            url: `/order/print/${row.id}`,\n            method: \"get\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.$emit(\"changeGetTabs\");\n              this.getList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          break;\n        case \"11\":\n          this.delfromData = {\n            title: \"立即打印电子面单\",\n            info: \"您确认打印此电子面单吗?\",\n            url: `/order/order_dump/${row.id}`,\n            method: \"get\",\n            ids: \"\",\n          };\n          this.$modalSure(this.delfromData)\n            .then((res) => {\n              this.$Message.success(res.msg);\n              this.getList();\n            })\n            .catch((res) => {\n              this.$Message.error(res.msg);\n            });\n          break;\n        default:\n          this.delfromData = {\n            title: \"删除订单\",\n            url: `/order/del/${row.id}`,\n            method: \"DELETE\",\n            ids: \"\",\n          };\n          // this.modalTitleSs = '删除订单';\n          this.delOrder(row, this.delfromData);\n      }\n    },\n    // 立即支付 /确认收货//删除单条订单\n    submitModel() {\n      this.getList();\n    },\n    pageChange(index) {\n      this.page.pageNum = index;\n      this.getList();\n    },\n    limitChange(limit) {\n      this.page.pageSize = limit;\n      this.getList();\n    },\n    getOrderList(id) {\n      splitOrderList(id).then((res) => {\n      });\n    },\n    // 订单列表\n    getList(res) {\n      this.page.pageNum = res === 1 ? 1 : this.page.pageNum;\n      this.loading = true;\n\n      splitOrderList(this.$route.query.id)\n        .then(async (res) => {\n          let data = res.data;\n          // this.orderList = data.data;\n          this.orderList = data.map((item) => {\n            // item.checkBox = false;\n            if (this.isAll === 1) {\n              item.checkBox = true;\n            } else {\n              item.checkBox = false;\n            }\n            return item;\n          });\n          this.orderCards = data.stat;\n          this.page.total = data.count;\n          this.$emit(\"on-changeCards\", data.stat);\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 全选\n    onSelectTab(selection) {\n      this.formSelection = selection;\n      let isDel = selection.some((item) => {\n        return item.is_del === 1;\n      });\n      this.getIsDel(isDel);\n      this.getisDelIdListl(selection);\n    },\n    // 编辑\n    edit(row) {\n      this.getOrderData(row.id);\n    },\n    splitOrderDetail(row) {\n      this.$router.push({\n        path: this.roterPre + \"split_order\",\n        query: {\n          id: row.id,\n        },\n      });\n    },\n    // 删除单条订单\n    delOrder(row, data) {\n      if (row.is_del === 1) {\n        this.$modalSure(data)\n          .then((res) => {\n            this.$Message.success(res.msg);\n            this.getList();\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n      } else {\n        const title = \"错误！\";\n        const content =\n          \"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>\";\n        this.$Modal.error({\n          title: title,\n          content: content,\n        });\n      }\n    },\n    // 获取编辑表单数据\n    getOrderData(id) {\n      getOrdeDatas(id)\n        .then(async (res) => {\n          if (res.data.status === false) {\n            return this.$authLapse(res.data);\n          }\n          this.$authLapse(res.data);\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 获取详情表单数据\n    getData(id) {\n      getDataInfo(id)\n        .then(async (res) => {\n          this.$refs.detailss.modals = true;\n          this.orderDatalist = res.data;\n          if (this.orderDatalist.orderInfo.refund_reason_wap_img) {\n            try {\n              this.orderDatalist.orderInfo.refund_reason_wap_img = JSON.parse(\n                this.orderDatalist.orderInfo.refund_reason_wap_img\n              );\n            } catch (e) {\n              this.orderDatalist.orderInfo.refund_reason_wap_img = [];\n            }\n          }\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 修改成功\n    submitFail() {\n      this.status = 0;\n      this.getList();\n    },\n\t\t// 仅退款\n\t\tgetOnlyRefundData(id,refund_type){\n\t\t\tthis.$modalForm(getRefundFrom(id)).then(() => {\n\t\t\t  this.getList();\n\t\t\t  this.$emit(\"changeGetTabs\");\n\t\t\t});\n\t\t},\n    // 获取退款表单数据\n\t\tgetRefundData(id,refund_type) {\n\t\t\tlet orderChartType = this.$route.query.orderChartType;\n\t\t\tthis.delfromData = {\n\t\t\t  title: \"是否立即退货退款\",\n\t\t\t  url: `/refund/agree/${id}`,\n\t\t\t  method: \"get\",\n\t\t\t};\n\t\t\tthis.$modalSure(this.delfromData)\n\t\t\t  .then((res) => {\n\t\t\t    this.$Message.success(res.msg);\n\t\t\t    this.getList();\n\t\t\t    this.$emit(\"changeGetTabs\");\n\t\t\t  })\n\t\t\t  .catch((res) => {\n\t\t\t    this.$Message.error(res.msg);\n\t\t\t  });\n\t\t},\n    // 获取退积分表单数据\n    getRefundIntegral(id) {\n      refundIntegral(id)\n        .then(async (res) => {\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 不退款表单数据\n    getNoRefundData(id) {\n      this.$modalForm(getnoRefund(id)).then(() => {\n        this.getList();\n        this.$emit(\"changeGetTabs\");\n      });\n    },\n    // 发送货\n    sendOrder(row) {\n      this.$refs.send.modals = true;\n      this.orderId = row.id;\n      this.status = row._status;\n      this.$refs.send.getList();\n      this.$refs.send.getDeliveryList();\n      this.$nextTick((e) => {\n        this.$refs.send.getCartInfo(row._status, row.id);\n      });\n    },\n    // 配送信息表单数据\n    delivery(row) {\n      getDistribution(row.id)\n        .then(async (res) => {\n          this.FromData = res.data;\n          this.$refs.edits.modals = true;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    change(status) {},\n    // 数据导出；\n    exportData: function () {\n      this.$refs.table.exportCsv({\n        filename: \"商品列表\",\n      });\n    },\n    // 核销订单\n    bindWrite(row) {\n      let self = this;\n      this.$Modal.confirm({\n        title: \"提示\",\n        content: \"确定要核销该订单吗？\",\n        cancelText: \"取消\",\n        closable: true,\n        maskClosable: true,\n        onOk: function () {\n          writeUpdate(row.order_id).then((res) => {\n            self.$Message.success(res.msg);\n            self.getList();\n          });\n        },\n        onCancel: () => {},\n      });\n    },\n    onSelectCancel(selection, row) {},\n  },\n};\n", null]}