{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_hot_word.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\timport { getWordsAll } from '@/api/diy'\n    import vuedraggable from 'vuedraggable'\n    export default {\n        name: 'c_hot_word',\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            }\n        },\n        components: {\n            draggable: vuedraggable\n        },\n        data () {\n            return {\n                hotWordList: [],\n                hotIndex: 1,\n                defaults: {},\n                configData: {},\n\t\t\t\twordList: []\n            }\n        },\n        created () {\n            this.defaults = this.configObj\n            this.configData = this.configObj[this.configNme]\n\t\t\tthis.wordsAll();\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    // this.hotWordList = nVal.hotList\n                    this.configData = nVal[this.configNme]\n                },\n                immediate: true,\n                deep: true\n            }\n        },\n        methods: {\n\t\t\twordsAll(){\n\t\t\t\tgetWordsAll().then(res=>{\n\t\t\t\t\tthis.wordList = res.data;\n\t\t\t\t}).catch(err=>{\n\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t})\n\t\t\t},\n            addHotTxt () {\n                // let obj = {}\n                // if(this.configData.list.length){\n                //     obj = JSON.parse(JSON.stringify(this.configData.list[this.configData.list.length - 1]))\n                // }else {\n                //     obj = {\n                //         val: ''\n                //     }\n                // }\n                let obj = {\n                    val: ''\n                };\n                this.configData.list.push(obj)\n                // this.$emit('input', this.hotWordList);\n            },\n            // 删除数组\n            bindDelete (index) {\n                this.configData.list.splice(index, 1)\n            },\n        }\n    }\n", null]}