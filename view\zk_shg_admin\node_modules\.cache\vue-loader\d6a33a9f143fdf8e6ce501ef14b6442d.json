{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\bill\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\bill\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport { billTypeApi, billListApi, userFinanceApi } from \"@/api/finance\";\nimport exportExcel from \"../../../../utils/newToExcel.js\";\nimport timeOptions from \"@/utils/timeOptions\";\nexport default {\n  name: \"bill\",\n  data() {\n    return {\n      billList: [],\n      formValidate: {\n        nickname: \"\",\n        start_time: \"\",\n        end_time: \"\",\n        type: \"\",\n        page: 1, // 当前页\n        limit: 20, // 每页显示条数\n      },\n      options: timeOptions,\n      loading: false,\n      tabList: [],\n      total: 0,\n      columns: [\n        {\n          title: \"用户ID\",\n          key: \"uid\",\n          sortable: true,\n          width: 80,\n        },\n        {\n          title: \"昵称\",\n          key: \"nickname\",\n          minWidth: 150,\n        },\n        {\n          title: \"金额\",\n          // sortable: true,\n          minWidth: 150,\n          slot: \"number\",\n          // render: (h, params) => {\n          //     return h('div', {\n          //         style: {\n          //             color: '#FF5722'\n          //         }\n          //     })\n          // }\n        },\n        {\n          title: \"类型\",\n          key: \"title\",\n          minWidth: 100,\n        },\n        {\n          title: \"备注\",\n          key: \"mark\",\n          minWidth: 150,\n        },\n        {\n          title: \"创建时间\",\n          key: \"add_time\",\n          minWidth: 200,\n        },\n      ],\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.selList();\n    this.getList();\n  },\n  methods: {\n    // 时间\n    onchangeTime(e) {\n      this.formValidate.start_time = e[0];\n      this.formValidate.end_time = e[1];\n\t  this.userSearchs();\n    },\n    // 获取筛选类型\n    selList() {\n      billTypeApi()\n        .then(async (res) => {\n          this.billList = res.data.list;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      billListApi(this.formValidate)\n        .then(async (res) => {\n          let data = res.data;\n          this.tabList = data.data;\n          this.total = data.count;\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 搜索\n    userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 导出\n    //   let formValidate = this.formValidate;\n    //   let data = {\n    //     start_time: formValidate.start_time,\n    //     end_time: formValidate.end_time,\n    //     nickname: formValidate.nickname,\n    //     type: formValidate.type,\n    //   };\n    //   userFinanceApi(data)\n    //     .then((res) => {\n    //       location.href = res.data[0];\n    //     })\n    //     .catch((res) => {\n    //       this.$Message.error(res.msg);\n    //     });\n    // 数据导出；\n    async exports() {\n      let [th, filekey, data, fileName] = [[], [], [], \"\"];\n      //   let fileName = \"\";\n      let excelData = JSON.parse(JSON.stringify(this.formValidate));\n      excelData.page = 1;\n      for (let i = 0; i < excelData.page + 1; i++) {\n        let lebData = await this.getExcelData(excelData);\n        if (!fileName) fileName = lebData.filename;\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) th = lebData.header;\n        if (lebData.export.length) {\n          data = data.concat(lebData.export);\n          excelData.page++;\n        } else {\n          exportExcel(th, filekey, fileName, data);\n          return;\n        }\n      }\n    },\n    getExcelData(excelData) {\n      return new Promise((resolve, reject) => {\n        userFinanceApi(excelData).then((res) => {\n          return resolve(res.data);\n        });\n      });\n    },\n  },\n};\n", null]}