{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=template&id=5aa3ab28&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751013157007}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"transfer-container\">\n  <!-- 页面头部 -->\n  <div class=\"page-header\">\n    <div class=\"header-left\">\n      <h2 class=\"page-title\">调拨管理</h2>\n      <p class=\"page-desc\">管理门店间商品调拨申请，支持审核、执行和统计分析</p>\n    </div>\n    <div class=\"header-right\">\n      <el-button type=\"warning\" icon=\"el-icon-warning\" @click=\"showPendingOnly\">待审核单据</el-button>\n      <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportTransfer\">导出数据</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-pie-chart\" @click=\"showStatistics\">统计分析</el-button>\n    </div>\n  </div>\n\n  <!-- 筛选条件 -->\n  <div class=\"filter-container\">\n    <el-form :model=\"filterForm\" inline>\n      <el-form-item label=\"调拨单号：\">\n        <el-input\n          v-model=\"filterForm.order_no\"\n          placeholder=\"输入调拨单号\"\n          style=\"width: 200px\">\n        </el-input>\n      </el-form-item>\n\n      <el-form-item label=\"调出门店：\">\n        <el-select v-model=\"filterForm.from_store_id\" placeholder=\"选择调出门店\" clearable style=\"width: 200px\">\n          <el-option label=\"全部门店\" value=\"\"></el-option>\n          <el-option\n            v-for=\"store in storeList\"\n            :key=\"store.id\"\n            :label=\"store.name\"\n            :value=\"store.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"调入门店：\">\n        <el-select v-model=\"filterForm.to_store_id\" placeholder=\"选择调入门店\" clearable style=\"width: 200px\">\n          <el-option label=\"全部门店\" value=\"\"></el-option>\n          <el-option\n            v-for=\"store in storeList\"\n            :key=\"store.id\"\n            :label=\"store.name\"\n            :value=\"store.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"单据状态：\">\n        <el-select v-model=\"filterForm.status\" placeholder=\"单据状态\" clearable style=\"width: 150px\">\n          <el-option label=\"全部\" value=\"\"></el-option>\n          <el-option label=\"待审核\" :value=\"0\"></el-option>\n          <el-option label=\"已通过\" :value=\"1\"></el-option>\n          <el-option label=\"已完成\" :value=\"2\"></el-option>\n          <el-option label=\"已拒绝\" :value=\"3\"></el-option>\n          <el-option label=\"已撤销\" :value=\"4\"></el-option>\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"申请日期：\">\n        <el-date-picker\n          v-model=\"filterForm.date_range\"\n          type=\"daterange\"\n          range-separator=\"至\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          style=\"width: 240px\">\n        </el-date-picker>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"getTransferList\">查询</el-button>\n        <el-button @click=\"resetFilter\">重置</el-button>\n      </el-form-item>\n    </el-form>\n  </div>\n\n  <!-- 统计卡片 -->\n  <div class=\"stats-container\">\n    <div class=\"stat-card\">\n      <div class=\"stat-icon total\">\n        <i class=\"el-icon-document\"></i>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-number\">{{ statistics.total_orders }}</div>\n        <div class=\"stat-label\">总调拨单</div>\n      </div>\n    </div>\n\n    <div class=\"stat-card\">\n      <div class=\"stat-icon pending\">\n        <i class=\"el-icon-time\"></i>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-number\">{{ statistics.pending_orders }}</div>\n        <div class=\"stat-label\">待审核</div>\n      </div>\n    </div>\n\n    <div class=\"stat-card\">\n      <div class=\"stat-icon approved\">\n        <i class=\"el-icon-check\"></i>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-number\">{{ statistics.approved_orders }}</div>\n        <div class=\"stat-label\">已通过</div>\n      </div>\n    </div>\n\n    <div class=\"stat-card\">\n      <div class=\"stat-icon completed\">\n        <i class=\"el-icon-circle-check\"></i>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-number\">{{ statistics.completed_orders }}</div>\n        <div class=\"stat-label\">已完成</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 调拨单列表 -->\n  <div class=\"table-container\">\n    <el-table\n      :data=\"transferList\"\n      v-loading=\"tableLoading\"\n      stripe\n      border\n      style=\"width: 100%\"\n      @selection-change=\"handleSelectionChange\">\n\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\n\n      <el-table-column prop=\"order_no\" label=\"调拨单号\" width=\"160\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-link type=\"primary\" @click=\"viewDetail(scope.row)\">{{ scope.row.order_no }}</el-link>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"商品信息\" min-width=\"250\">\n        <template slot-scope=\"scope\">\n          <div class=\"product-info\">\n            <el-image\n              :src=\"scope.row.product_image\"\n              style=\"width: 40px; height: 40px; border-radius: 4px; margin-right: 12px\"\n              fit=\"cover\">\n            </el-image>\n            <div>\n              <div class=\"product-name\">{{ scope.row.product_name }}</div>\n              <div class=\"product-spec\" v-if=\"scope.row.sku\">{{ scope.row.sku }}</div>\n              <div class=\"transfer-qty\">调拨数量：{{ scope.row.transfer_qty }}</div>\n            </div>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"调拨门店\" width=\"200\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <div class=\"transfer-stores\">\n            <div class=\"from-store\">{{ scope.row.from_store_name }}</div>\n            <i class=\"el-icon-right transfer-arrow\"></i>\n            <div class=\"to-store\">{{ scope.row.to_store_name }}</div>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"单据状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\n            {{ getStatusText(scope.row.status) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"applicant\" label=\"申请人\" width=\"100\" align=\"center\"></el-table-column>\n\n      <el-table-column prop=\"apply_time\" label=\"申请时间\" width=\"160\" align=\"center\"></el-table-column>\n\n      <el-table-column prop=\"approve_time\" label=\"审核时间\" width=\"160\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.approve_time\">{{ scope.row.approve_time }}</span>\n          <span v-else class=\"text-muted\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"250\" align=\"center\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" @click=\"viewDetail(scope.row)\">查看详情</el-button>\n\n          <template v-if=\"scope.row.status === 0\">\n            <el-button type=\"text\" size=\"small\" @click=\"approveTransfer(scope.row, true)\">通过</el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"approveTransfer(scope.row, false)\">拒绝</el-button>\n          </template>\n\n          <template v-if=\"scope.row.status === 1\">\n            <el-button type=\"text\" size=\"small\" @click=\"executeTransfer(scope.row)\">执行调拨</el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"forceExecute(scope.row)\">强制执行</el-button>\n          </template>\n\n          <template v-if=\"scope.row.status === 3 || scope.row.status === 4\">\n            <el-button type=\"text\" size=\"small\" @click=\"deleteTransfer(scope.row)\">删除</el-button>\n          </template>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 批量操作 -->\n    <div class=\"batch-actions\" v-if=\"selectedRows.length > 0\">\n      <span class=\"selected-count\">已选择 {{ selectedRows.length }} 项</span>\n      <el-button size=\"small\" @click=\"batchApprove(true)\">批量通过</el-button>\n      <el-button size=\"small\" @click=\"batchApprove(false)\">批量拒绝</el-button>\n      <el-button size=\"small\" @click=\"batchExecute\">批量执行</el-button>\n      <el-button size=\"small\" @click=\"clearSelection\">取消选择</el-button>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[20, 50, 100, 200]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"total\">\n      </el-pagination>\n    </div>\n  </div>\n\n  <!-- 调拨详情对话框 -->\n  <el-dialog title=\"调拨详情\" :visible.sync=\"detailDialogVisible\" width=\"700px\">\n    <div class=\"detail-content\" v-if=\"currentDetail\">\n      <div class=\"detail-section\">\n        <h4>基本信息</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调拨单号：</span>\n              <span class=\"value\">{{ currentDetail.order_no }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">单据状态：</span>\n              <el-tag :type=\"getStatusTagType(currentDetail.status)\">\n                {{ getStatusText(currentDetail.status) }}\n              </el-tag>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">申请人：</span>\n              <span class=\"value\">{{ currentDetail.applicant }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">申请时间：</span>\n              <span class=\"value\">{{ currentDetail.apply_time }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <div class=\"detail-section\">\n        <h4>商品信息</h4>\n        <div class=\"product-detail\">\n          <el-image\n            :src=\"currentDetail.product_image\"\n            style=\"width: 80px; height: 80px; border-radius: 8px; margin-right: 16px\"\n            fit=\"cover\">\n          </el-image>\n          <div>\n            <div class=\"product-name\">{{ currentDetail.product_name }}</div>\n            <div class=\"product-spec\" v-if=\"currentDetail.sku\">规格：{{ currentDetail.sku }}</div>\n            <div class=\"transfer-qty\">调拨数量：{{ currentDetail.transfer_qty }}</div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"detail-section\">\n        <h4>调拨信息</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调出门店：</span>\n              <span class=\"value\">{{ currentDetail.from_store_name }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调入门店：</span>\n              <span class=\"value\">{{ currentDetail.to_store_name }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <div class=\"detail-section\" v-if=\"currentDetail.remark\">\n        <h4>申请原因</h4>\n        <div class=\"remark-content\">{{ currentDetail.remark }}</div>\n      </div>\n\n      <div class=\"detail-section\" v-if=\"currentDetail.approve_remark\">\n        <h4>审核备注</h4>\n        <div class=\"remark-content\">{{ currentDetail.approve_remark }}</div>\n      </div>\n    </div>\n  </el-dialog>\n\n  <!-- 审核对话框 -->\n  <el-dialog :title=\"approveType ? '审核通过' : '审核拒绝'\" :visible.sync=\"approveDialogVisible\" width=\"500px\">\n    <el-form :model=\"approveForm\" :rules=\"approveRules\" ref=\"approveForm\" label-width=\"100px\">\n      <el-form-item label=\"审核备注：\" prop=\"remark\">\n        <el-input\n          type=\"textarea\"\n          v-model=\"approveForm.remark\"\n          :placeholder=\"approveType ? '请填写通过原因（可选）' : '请填写拒绝原因'\"\n          :rows=\"4\">\n        </el-input>\n      </el-form-item>\n    </el-form>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"approveDialogVisible = false\">取消</el-button>\n      <el-button\n        :type=\"approveType ? 'success' : 'danger'\"\n        @click=\"submitApprove\"\n        :loading=\"approveLoading\">\n        {{ approveType ? '确认通过' : '确认拒绝' }}\n      </el-button>\n    </div>\n  </el-dialog>\n</div>\n", null]}