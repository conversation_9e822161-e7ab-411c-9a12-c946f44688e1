{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue", "mtime": 1693985518000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getUserLabel, putUserLabel, putUsersSetLabel } from '@/api/user';\nexport default {\n  name: \"userLabel\",\n  props: {\n    uid: {\n      type: String | Number | Object,\n      default: ''\n    }\n  },\n  data: function data() {\n    return {\n      isUser: false,\n      labelList: [],\n      activeIds: [],\n      info: {},\n      oid: ''\n    };\n  },\n  watch: {\n    uid: {\n      handler: function handler(nVal, oVal) {\n        if (nVal != oVal) {\n          this.getList();\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n\n      var uid = 0;\n\n      if (this.uid instanceof Object) {\n        this.info = this.uid;\n\n        if (!this.uid.all && this.uid.uids.length > 1 || this.uid.all) {\n          uid = 0;\n        } else {\n          uid = this.uid.uids[0];\n        }\n      } else {\n        uid = this.uid;\n      }\n\n      this.oid = uid;\n      getUserLabel(uid).then(function (res) {\n        res.data.map(function (el) {\n          _this.isUser = true;\n          el.label.map(function (label) {\n            if (label.disabled) {\n              _this.activeIds.push(label.id);\n            }\n          });\n        });\n        _this.labelList = res.data;\n      });\n    },\n    selectLabel: function selectLabel(label) {\n      if (label.disabled) {\n        var index = this.activeIds.indexOf(label.id);\n        this.activeIds.splice(index, 1);\n        label.disabled = false;\n      } else {\n        this.activeIds.push(label.id);\n        label.disabled = true;\n      }\n    },\n    // 确定\n    subBtn: function subBtn() {\n      var _this2 = this;\n\n      var unLaberids = [];\n      this.labelList.map(function (item) {\n        item.label.map(function (i) {\n          if (i.disabled == false) {\n            unLaberids.push(i.id);\n          }\n        });\n      });\n      var obj = this.info;\n      obj.label_id = this.activeIds;\n      (this.oid == 0 ? putUsersSetLabel(obj) : putUserLabel(this.oid, {\n        label_ids: this.activeIds,\n        un_label_ids: unLaberids\n      })).then(function (res) {\n        _this2.activeIds = [];\n        _this2.labelList = [];\n\n        _this2.$Message.success(res.msg);\n\n        _this2.$emit('close', _this2.oid);\n      }).catch(function (error) {\n        _this2.$Message.error(error.msg);\n      });\n    },\n    cancel: function cancel() {\n      this.activeIds = [];\n      this.labelList = [];\n      this.$emit('close');\n    }\n  }\n};", null]}