{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=template&id=2f96c05e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751016613853}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"transfer-container\">\n  <!-- 页面头部 -->\n  <Card dis-hover>\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <h2 class=\"page-title\">调拨管理</h2>\n        <p class=\"page-desc\">管理门店间商品调拨申请，支持审核、执行和统计分析</p>\n      </div>\n      <div class=\"header-right\">\n        <Button type=\"warning\" icon=\"ios-warning\" @click=\"showPendingOnly\">待审核单据</Button>\n        <Button type=\"success\" icon=\"ios-download\" @click=\"exportTransfer\" style=\"margin-left: 8px;\">导出数据</Button>\n        <Button type=\"primary\" icon=\"ios-stats\" @click=\"showStatistics\" style=\"margin-left: 8px;\">统计分析</Button>\n        <Button type=\"primary\" icon=\"ios-add\" @click=\"showAddDialog\" style=\"margin-left: 8px;\">添加调拨</Button>\n      </div>\n    </div>\n  </Card>\n\n  <!-- 筛选条件 -->\n  <Card dis-hover style=\"margin-top: 16px;\">\n    <Form :model=\"filterForm\" inline>\n      <FormItem label=\"调拨单号：\">\n        <Input\n          v-model=\"filterForm.order_no\"\n          placeholder=\"输入调拨单号\"\n          style=\"width: 200px\">\n        </Input>\n      </FormItem>\n\n      <FormItem label=\"调出门店：\">\n        <Select v-model=\"filterForm.from_store_id\" placeholder=\"选择调出门店\" clearable style=\"width: 200px\">\n          <Option label=\"全部门店\" value=\"\"></Option>\n          <Option\n            v-for=\"store in storeList\"\n            :key=\"store.value || store.id\"\n            :label=\"store.label || store.name\"\n            :value=\"store.value || store.id\">\n          </Option>\n        </Select>\n      </FormItem>\n\n      <FormItem label=\"调入门店：\">\n        <Select v-model=\"filterForm.to_store_id\" placeholder=\"选择调入门店\" clearable style=\"width: 200px\">\n          <Option label=\"全部门店\" value=\"\"></Option>\n          <Option\n            v-for=\"store in storeList\"\n            :key=\"store.value || store.id\"\n            :label=\"store.label || store.name\"\n            :value=\"store.value || store.id\">\n          </Option>\n        </Select>\n      </FormItem>\n\n      <FormItem label=\"单据状态：\">\n        <Select v-model=\"filterForm.status\" placeholder=\"单据状态\" clearable style=\"width: 150px\">\n          <Option label=\"全部\" value=\"\"></Option>\n          <Option label=\"待审核\" value=\"0\"></Option>\n          <Option label=\"已通过\" value=\"1\"></Option>\n          <Option label=\"已完成\" value=\"2\"></Option>\n          <Option label=\"已拒绝\" value=\"3\"></Option>\n          <Option label=\"已撤销\" value=\"4\"></Option>\n        </Select>\n      </FormItem>\n\n      <FormItem label=\"申请日期：\">\n        <DatePicker\n          v-model=\"filterForm.date_range\"\n          type=\"daterange\"\n          placeholder=\"选择日期范围\"\n          style=\"width: 240px\">\n        </DatePicker>\n      </FormItem>\n\n      <FormItem>\n        <Button type=\"primary\" @click=\"getTransferList\">查询</Button>\n        <Button @click=\"resetFilter\" style=\"margin-left: 8px;\">重置</Button>\n      </FormItem>\n    </Form>\n  </Card>\n\n  <!-- 统计卡片 -->\n  <Row :gutter=\"16\" style=\"margin-top: 16px;\">\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon total\">\n            <Icon type=\"ios-document\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.total_orders }}</div>\n            <div class=\"stat-label\">总调拨单</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon pending\">\n            <Icon type=\"ios-time\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.pending_orders }}</div>\n            <div class=\"stat-label\">待审核</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon approved\">\n            <Icon type=\"ios-checkmark\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.approved_orders }}</div>\n            <div class=\"stat-label\">已通过</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon completed\">\n            <Icon type=\"ios-checkmark-circle\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.completed_orders }}</div>\n            <div class=\"stat-label\">已完成</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n  </Row>\n\n  <!-- 调拨单列表 -->\n  <Card dis-hover style=\"margin-top: 16px;\">\n    <Table\n      :data=\"transferList\"\n      :loading=\"tableLoading\"\n      stripe\n      border\n      @on-selection-change=\"handleSelectionChange\">\n\n      <Column type=\"selection\" width=\"60\" align=\"center\"></Column>\n\n      <Column prop=\"order_no\" label=\"调拨单号\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{ row }\">\n          <a href=\"javascript:;\" @click=\"viewDetail(row)\" style=\"color: #2d8cf0;\">{{ row.order_no }}</a>\n        </template>\n      </Column>\n\n      <Column label=\"商品信息\" min-width=\"250\">\n        <template slot-scope=\"{ row }\">\n          <div class=\"product-info\">\n            <img\n              :src=\"row.product_image || '/static/images/default-product.png'\"\n              style=\"width: 40px; height: 40px; border-radius: 4px; margin-right: 12px; object-fit: cover;\"\n              alt=\"商品图片\">\n            <div>\n              <div class=\"product-name\">{{ row.product_name || '未知商品' }}</div>\n              <div class=\"product-spec\" v-if=\"row.sku\">{{ row.sku }}</div>\n              <div class=\"transfer-qty\">调拨数量：{{ row.transfer_qty || 0 }}</div>\n            </div>\n          </div>\n        </template>\n      </Column>\n\n      <Column label=\"调拨门店\" width=\"200\" align=\"center\">\n        <template slot-scope=\"{ row }\">\n          <div class=\"transfer-stores\">\n            <div class=\"from-store\">{{ row.from_store_name || '未知门店' }}</div>\n            <Icon type=\"ios-arrow-forward\" style=\"margin: 0 8px;\"></Icon>\n            <div class=\"to-store\">{{ row.to_store_name || '未知门店' }}</div>\n          </div>\n        </template>\n      </Column>\n\n      <Column label=\"单据状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{ row }\">\n          <Tag :color=\"getStatusTagColor(row.status)\">\n            {{ getStatusText(row.status) }}\n          </Tag>\n        </template>\n      </Column>\n\n      <Column prop=\"applicant\" label=\"申请人\" width=\"100\" align=\"center\"></Column>\n\n      <Column prop=\"apply_time_text\" label=\"申请时间\" width=\"160\" align=\"center\"></Column>\n\n      <Column label=\"审核时间\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{ row }\">\n          <span v-if=\"row.approve_time_text\">{{ row.approve_time_text }}</span>\n          <span v-else style=\"color: #c5c8ce;\">-</span>\n        </template>\n      </Column>\n\n      <Column label=\"操作\" width=\"250\" align=\"center\" fixed=\"right\">\n        <template slot-scope=\"{ row }\">\n          <Button type=\"text\" size=\"small\" @click=\"viewDetail(row)\">查看详情</Button>\n\n          <template v-if=\"row.status === 0\">\n            <Button type=\"text\" size=\"small\" @click=\"approveTransfer(row, true)\" style=\"color: #19be6b;\">通过</Button>\n            <Button type=\"text\" size=\"small\" @click=\"approveTransfer(row, false)\" style=\"color: #ed4014;\">拒绝</Button>\n          </template>\n\n          <template v-if=\"row.status === 1\">\n            <Button type=\"text\" size=\"small\" @click=\"executeTransfer(row)\" style=\"color: #2d8cf0;\">执行调拨</Button>\n            <Button type=\"text\" size=\"small\" @click=\"forceExecute(row)\" style=\"color: #ff9900;\">强制执行</Button>\n          </template>\n\n          <template v-if=\"row.status === 3 || row.status === 4\">\n            <Button type=\"text\" size=\"small\" @click=\"deleteTransfer(row)\" style=\"color: #ed4014;\">删除</Button>\n          </template>\n        </template>\n      </Column>\n    </Table>\n\n    <!-- 批量操作 -->\n    <div class=\"batch-actions\" v-if=\"selectedRows.length > 0\" style=\"margin-top: 16px;\">\n      <span class=\"selected-count\" style=\"margin-right: 16px; color: #515a6e;\">已选择 {{ selectedRows.length }} 项</span>\n      <Button size=\"small\" @click=\"batchApprove(true)\" type=\"success\">批量通过</Button>\n      <Button size=\"small\" @click=\"batchApprove(false)\" type=\"error\" style=\"margin-left: 8px;\">批量拒绝</Button>\n      <Button size=\"small\" @click=\"batchExecute\" type=\"primary\" style=\"margin-left: 8px;\">批量执行</Button>\n      <Button size=\"small\" @click=\"clearSelection\" style=\"margin-left: 8px;\">取消选择</Button>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\" style=\"margin-top: 16px; text-align: right;\">\n      <Page\n        @on-change=\"handleCurrentChange\"\n        @on-page-size-change=\"handleSizeChange\"\n        :current=\"currentPage\"\n        :page-size=\"pageSize\"\n        :total=\"total\"\n        show-sizer\n        show-elevator\n        show-total>\n      </Page>\n    </div>\n  </Card>\n\n  <!-- 调拨详情对话框 -->\n  <Modal v-model=\"detailDialogVisible\" title=\"调拨详情\" width=\"700\">\n    <div class=\"detail-content\" v-if=\"currentDetail\">\n      <div class=\"detail-section\">\n        <h4>基本信息</h4>\n        <Row :gutter=\"20\">\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调拨单号：</span>\n              <span class=\"value\">{{ currentDetail.order_no }}</span>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">单据状态：</span>\n              <Tag :color=\"getStatusTagColor(currentDetail.status)\">\n                {{ getStatusText(currentDetail.status) }}\n              </Tag>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">申请人：</span>\n              <span class=\"value\">{{ currentDetail.applicant }}</span>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">申请时间：</span>\n              <span class=\"value\">{{ currentDetail.apply_time_text }}</span>\n            </div>\n          </Col>\n        </Row>\n      </div>\n\n      <div class=\"detail-section\">\n        <h4>商品信息</h4>\n        <div class=\"product-detail\">\n          <img\n            :src=\"currentDetail.product_image || '/static/images/default-product.png'\"\n            style=\"width: 80px; height: 80px; border-radius: 8px; margin-right: 16px; object-fit: cover;\"\n            alt=\"商品图片\">\n          <div>\n            <div class=\"product-name\">{{ currentDetail.product_name }}</div>\n            <div class=\"product-spec\" v-if=\"currentDetail.sku\">规格：{{ currentDetail.sku }}</div>\n            <div class=\"transfer-qty\">调拨数量：{{ currentDetail.transfer_qty }}</div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"detail-section\">\n        <h4>调拨信息</h4>\n        <Row :gutter=\"20\">\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调出门店：</span>\n              <span class=\"value\">{{ currentDetail.from_store_name }}</span>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调入门店：</span>\n              <span class=\"value\">{{ currentDetail.to_store_name }}</span>\n            </div>\n          </Col>\n        </Row>\n      </div>\n\n      <div class=\"detail-section\" v-if=\"currentDetail.remark\">\n        <h4>申请原因</h4>\n        <div class=\"remark-content\">{{ currentDetail.remark }}</div>\n      </div>\n\n      <div class=\"detail-section\" v-if=\"currentDetail.approve_remark\">\n        <h4>审核备注</h4>\n        <div class=\"remark-content\">{{ currentDetail.approve_remark }}</div>\n      </div>\n    </div>\n  </Modal>\n\n  <!-- 审核对话框 -->\n  <Modal v-model=\"approveDialogVisible\" :title=\"approveType ? '审核通过' : '审核拒绝'\" width=\"500\">\n    <Form :model=\"approveForm\" :rules=\"approveRules\" ref=\"approveForm\" :label-width=\"100\">\n      <FormItem label=\"审核备注：\" prop=\"remark\">\n        <Input\n          type=\"textarea\"\n          v-model=\"approveForm.remark\"\n          :placeholder=\"approveType ? '请填写通过原因（可选）' : '请填写拒绝原因'\"\n          :rows=\"4\">\n        </Input>\n      </FormItem>\n    </Form>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <Button @click=\"approveDialogVisible = false\">取消</Button>\n      <Button\n        :type=\"approveType ? 'success' : 'error'\"\n        @click=\"submitApprove\"\n        :loading=\"approveLoading\"\n        style=\"margin-left: 8px;\">\n        {{ approveType ? '确认通过' : '确认拒绝' }}\n      </Button>\n    </div>\n  </Modal>\n</div>\n", null]}