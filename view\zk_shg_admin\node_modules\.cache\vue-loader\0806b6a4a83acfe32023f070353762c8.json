{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=template&id=2f96c05e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751247618901}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"transfer-container\">\n  <!-- 页面头部 -->\n  <Card dis-hover>\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <h2 class=\"page-title\">调拨管理</h2>\n        <p class=\"page-desc\">管理门店间商品调拨申请，支持审核、执行和统计分析</p>\n      </div>\n      <div class=\"header-right\">\n        <Button type=\"warning\" icon=\"ios-warning\" @click=\"showPendingOnly\">待审核单据</Button>\n        <Button type=\"success\" icon=\"ios-download\" @click=\"exportTransfer\" style=\"margin-left: 8px;\">导出数据</Button>\n        <Button type=\"primary\" icon=\"ios-add\" @click=\"showAddDialog\" style=\"margin-left: 8px;\">添加调拨</Button>\n      </div>\n    </div>\n  </Card>\n\n  <!-- 筛选条件 -->\n  <Card dis-hover style=\"margin-top: 16px;\">\n    <Form :model=\"filterForm\" inline>\n      <FormItem label=\"调拨单号：\">\n        <Input\n          v-model=\"filterForm.order_no\"\n          placeholder=\"输入调拨单号\"\n          style=\"width: 200px\">\n        </Input>\n      </FormItem>\n\n      <FormItem label=\"调出门店：\">\n        <Select v-model=\"filterForm.from_store_id\" placeholder=\"选择调出门店\" clearable style=\"width: 200px\">\n          <Option label=\"全部门店\" value=\"\"></Option>\n          <Option\n            v-for=\"store in storeList\"\n            :key=\"store.value || store.id\"\n            :label=\"store.label || store.name\"\n            :value=\"store.value || store.id\">\n          </Option>\n        </Select>\n      </FormItem>\n\n      <FormItem label=\"调入门店：\">\n        <Select v-model=\"filterForm.to_store_id\" placeholder=\"选择调入门店\" clearable style=\"width: 200px\">\n          <Option label=\"全部门店\" value=\"\"></Option>\n          <Option\n            v-for=\"store in storeList\"\n            :key=\"store.value || store.id\"\n            :label=\"store.label || store.name\"\n            :value=\"store.value || store.id\">\n          </Option>\n        </Select>\n      </FormItem>\n\n      <FormItem label=\"单据状态：\">\n        <Select v-model=\"filterForm.status\" placeholder=\"单据状态\" clearable style=\"width: 150px\">\n          <Option label=\"全部\" value=\"\"></Option>\n          <Option label=\"待审核\" value=\"0\"></Option>\n          <Option label=\"已通过\" value=\"1\"></Option>\n          <Option label=\"已完成\" value=\"2\"></Option>\n          <Option label=\"已拒绝\" value=\"3\"></Option>\n          <Option label=\"已撤销\" value=\"4\"></Option>\n        </Select>\n      </FormItem>\n\n      <FormItem label=\"申请日期：\">\n        <DatePicker\n          v-model=\"filterForm.date_range\"\n          type=\"daterange\"\n          placeholder=\"选择日期范围\"\n          style=\"width: 240px\">\n        </DatePicker>\n      </FormItem>\n\n      <FormItem>\n        <Button type=\"primary\" @click=\"getTransferList\">查询</Button>\n        <Button @click=\"resetFilter\" style=\"margin-left: 8px;\">重置</Button>\n      </FormItem>\n    </Form>\n  </Card>\n\n  <!-- 统计卡片 -->\n  <Row :gutter=\"16\" style=\"margin-top: 16px;\">\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon total\">\n            <Icon type=\"ios-document\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.total_orders }}</div>\n            <div class=\"stat-label\">总调拨单</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon pending\">\n            <Icon type=\"ios-time\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.pending_orders }}</div>\n            <div class=\"stat-label\">待审核</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon approved\">\n            <Icon type=\"ios-checkmark\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.approved_orders }}</div>\n            <div class=\"stat-label\">已通过</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n\n    <Col span=\"6\">\n      <Card>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon completed\">\n            <Icon type=\"ios-checkmark-circle\" size=\"24\"></Icon>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ statistics.completed_orders }}</div>\n            <div class=\"stat-label\">已完成</div>\n          </div>\n        </div>\n      </Card>\n    </Col>\n  </Row>\n\n  <!-- 调拨单列表 -->\n  <Card dis-hover style=\"margin-top: 16px;\">\n    <Table\n      :columns=\"tableColumns\"\n      :data=\"transferList\"\n      :loading=\"tableLoading\"\n      stripe\n      border\n      @on-selection-change=\"handleSelectionChange\">\n    </Table>\n\n    <!-- 批量操作 -->\n    <div class=\"batch-actions\" v-if=\"selectedRows.length > 0\" style=\"margin-top: 16px;\">\n      <span class=\"selected-count\" style=\"margin-right: 16px; color: #515a6e;\">已选择 {{ selectedRows.length }} 项</span>\n      <Button size=\"small\" @click=\"batchApprove(true)\" type=\"success\">批量通过</Button>\n      <Button size=\"small\" @click=\"batchApprove(false)\" type=\"error\" style=\"margin-left: 8px;\">批量拒绝</Button>\n      <Button size=\"small\" @click=\"batchExecute\" type=\"primary\" style=\"margin-left: 8px;\">批量执行</Button>\n      <Button size=\"small\" @click=\"clearSelection\" style=\"margin-left: 8px;\">取消选择</Button>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\" style=\"margin-top: 16px; text-align: right;\">\n      <Page\n        @on-change=\"handleCurrentChange\"\n        @on-page-size-change=\"handleSizeChange\"\n        :current=\"currentPage\"\n        :page-size=\"pageSize\"\n        :total=\"total\"\n        show-sizer\n        show-elevator\n        show-total>\n      </Page>\n    </div>\n  </Card>\n\n  <!-- 调拨详情对话框 -->\n  <Modal v-model=\"detailDialogVisible\" title=\"调拨详情\" width=\"700\">\n    <div class=\"detail-content\" v-if=\"currentDetail\">\n      <div class=\"detail-section\">\n        <h4>基本信息</h4>\n        <Row :gutter=\"20\">\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调拨单号：</span>\n              <span class=\"value\">{{ currentDetail.order_no }}</span>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">单据状态：</span>\n              <Tag :color=\"getStatusTagColor(currentDetail.status)\">\n                {{ getStatusText(currentDetail.status) }}\n              </Tag>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">申请人：</span>\n              <span class=\"value\">{{ currentDetail.applicant }}</span>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">申请时间：</span>\n              <span class=\"value\">{{ currentDetail.apply_time_text }}</span>\n            </div>\n          </Col>\n        </Row>\n      </div>\n\n      <div class=\"detail-section\">\n        <h4>商品信息</h4>\n        <div class=\"product-detail\">\n          <img\n            :src=\"currentDetail.product_image || '/static/images/default-product.png'\"\n            style=\"width: 80px; height: 80px; border-radius: 8px; margin-right: 16px; object-fit: cover;\"\n            alt=\"商品图片\">\n          <div>\n            <div class=\"product-name\">{{ currentDetail.product_name }}</div>\n            <div class=\"product-spec\" v-if=\"currentDetail.sku\">规格：{{ currentDetail.sku }}</div>\n            <div class=\"transfer-qty\">调拨数量：{{ currentDetail.transfer_qty }}</div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"detail-section\">\n        <h4>调拨信息</h4>\n        <Row :gutter=\"20\">\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调出门店：</span>\n              <span class=\"value\">{{ currentDetail.from_store_name }}</span>\n            </div>\n          </Col>\n          <Col span=\"12\">\n            <div class=\"detail-item\">\n              <span class=\"label\">调入门店：</span>\n              <span class=\"value\">{{ currentDetail.to_store_name }}</span>\n            </div>\n          </Col>\n        </Row>\n      </div>\n\n      <div class=\"detail-section\" v-if=\"currentDetail.remark\">\n        <h4>申请原因</h4>\n        <div class=\"remark-content\">{{ currentDetail.remark }}</div>\n      </div>\n\n      <div class=\"detail-section\" v-if=\"currentDetail.approve_remark\">\n        <h4>审核备注</h4>\n        <div class=\"remark-content\">{{ currentDetail.approve_remark }}</div>\n      </div>\n    </div>\n  </Modal>\n\n  <!-- 审核对话框 -->\n  <Modal v-model=\"approveDialogVisible\" :title=\"approveType ? '审核通过' : '审核拒绝'\" width=\"500\">\n    <Form :model=\"approveForm\" :rules=\"approveRules\" ref=\"approveForm\" :label-width=\"100\">\n      <FormItem label=\"审核备注：\" prop=\"remark\">\n        <Input\n          type=\"textarea\"\n          v-model=\"approveForm.remark\"\n          :placeholder=\"approveType ? '请填写通过原因（可选）' : '请填写拒绝原因'\"\n          :rows=\"4\">\n        </Input>\n      </FormItem>\n    </Form>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <Button @click=\"approveDialogVisible = false\">取消</Button>\n      <Button\n        :type=\"approveType ? 'success' : 'error'\"\n        @click=\"submitApprove\"\n        :loading=\"approveLoading\"\n        style=\"margin-left: 8px;\">\n        {{ approveType ? '确认通过' : '确认拒绝' }}\n      </Button>\n    </div>\n  </Modal>\n\n  <!-- 添加调拨弹窗 -->\n  <Modal\n    v-model=\"addDialogVisible\"\n    title=\"添加调拨申请\"\n    width=\"800\"\n    :mask-closable=\"false\"\n    @on-cancel=\"cancelAddTransfer\">\n\n    <Form ref=\"addForm\" :model=\"addForm\" :rules=\"addRules\" :label-width=\"100\">\n      <Row :gutter=\"16\">\n        <Col span=\"12\">\n          <FormItem label=\"调出门店\" prop=\"from_store_id\">\n            <Select v-model=\"addForm.from_store_id\" placeholder=\"请选择调出门店\">\n              <Option v-for=\"store in storeList\" :key=\"store.value\" :value=\"store.value\">\n                {{ store.label }}\n              </Option>\n            </Select>\n          </FormItem>\n        </Col>\n        <Col span=\"12\">\n          <FormItem label=\"调入门店\" prop=\"to_store_id\">\n            <Select v-model=\"addForm.to_store_id\" placeholder=\"请选择调入门店\">\n              <Option v-for=\"store in storeList\" :key=\"store.value\" :value=\"store.value\">\n                {{ store.label }}\n              </Option>\n            </Select>\n          </FormItem>\n        </Col>\n      </Row>\n\n      <FormItem label=\"申请备注\">\n        <Input\n          v-model=\"addForm.remark\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入申请备注（可选）\" />\n      </FormItem>\n\n      <FormItem label=\"调拨商品\">\n        <div style=\"margin-bottom: 16px;\">\n          <Row :gutter=\"8\">\n            <Col span=\"16\">\n              <Select\n                v-model=\"selectedProduct\"\n                placeholder=\"请选择要调拨的商品\"\n                filterable\n                remote\n                :remote-method=\"searchProducts\"\n                :loading=\"productLoading\">\n                <Option\n                  v-for=\"product in productList\"\n                  :key=\"product.unique\"\n                  :value=\"product\">\n                  <div style=\"display: flex; justify-content: space-between;\">\n                    <span>{{ product.store_name }}</span>\n                    <span style=\"color: #999;\">{{ product.sku }} (库存: {{ product.stock }})</span>\n                  </div>\n                </Option>\n              </Select>\n            </Col>\n            <Col span=\"8\">\n              <Button type=\"primary\" @click=\"addTransferProduct\">添加商品</Button>\n            </Col>\n          </Row>\n        </div>\n\n        <!-- 已选商品列表 -->\n        <Table\n          v-if=\"addForm.details.length > 0\"\n          :columns=\"[\n            { title: '商品名称', key: 'product_name', width: 200 },\n            { title: '规格', key: 'sku', width: 120 },\n            { title: '当前库存', key: 'stock', width: 100, align: 'center' },\n            {\n              title: '调拨数量',\n              key: 'transfer_num',\n              width: 120,\n              align: 'center',\n              render: (h, params) => {\n                return h('InputNumber', {\n                  props: {\n                    value: params.row.transfer_num,\n                    min: 1,\n                    max: params.row.stock,\n                    size: 'small'\n                  },\n                  on: {\n                    'on-change': (value) => {\n                      this.addForm.details[params.index].transfer_num = value;\n                    }\n                  }\n                });\n              }\n            },\n            {\n              title: '操作',\n              key: 'action',\n              width: 80,\n              align: 'center',\n              render: (h, params) => {\n                return h('Button', {\n                  props: {\n                    type: 'error',\n                    size: 'small'\n                  },\n                  on: {\n                    click: () => {\n                      this.removeTransferProduct(params.index);\n                    }\n                  }\n                }, '删除');\n              }\n            }\n          ]\"\n          :data=\"addForm.details\"\n          size=\"small\">\n        </Table>\n\n        <div v-if=\"addForm.details.length === 0\" style=\"text-align: center; color: #999; padding: 20px;\">\n          暂无调拨商品，请添加商品\n        </div>\n      </FormItem>\n    </Form>\n\n    <div slot=\"footer\">\n      <Button @click=\"cancelAddTransfer\">取消</Button>\n      <Button\n        type=\"primary\"\n        @click=\"submitAddTransfer\"\n        :loading=\"addLoading\"\n        style=\"margin-left: 8px;\">\n        提交申请\n      </Button>\n    </div>\n  </Modal>\n</div>\n", null]}