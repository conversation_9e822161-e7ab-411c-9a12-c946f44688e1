{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_upload_img.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import { mapState } from 'vuex';\n\timport linkaddress from '@/components/linkaddress';\n    import uploadPictures from '@/components/uploadPictures';\n    export default {\n        name: 'c_upload_img',\n        components: {\n            uploadPictures,\n\t\t\tlinkaddress\n        },\n        computed: {\n            ...mapState({\n                tabVal: state => state.admin.mobildConfig.searchConfig.data.tabVal\n            })\n        },\n        props: {\n            configObj: {\n                type: Object\n            },\n            configNme: {\n                type: String\n            }\n        },\n        data () {\n            return {\n                defaultList: [\n                    {\n                        'name': 'a42bdcc1178e62b4694c830f028db5c0',\n                        'url': 'https://o5wwk8baw.qnssl.com/a42bdcc1178e62b4694c830f028db5c0/avatar'\n                    },\n                    {\n                        'name': 'bc7521e033abdd1e92222d733590f104',\n                        'url': 'https://o5wwk8baw.qnssl.com/bc7521e033abdd1e92222d733590f104/avatar'\n                    }\n                ],\n                defaults: {},\n                configData: {},\n                modalPic: false,\n                isChoice: '单选',\n                gridBtn: {\n                    xl: 4,\n                    lg: 8,\n                    md: 8,\n                    sm: 8,\n                    xs: 8\n                },\n                gridPic: {\n                    xl: 6,\n                    lg: 8,\n                    md: 12,\n                    sm: 12,\n                    xs: 12\n                },\n                activeIndex: 0\n            }\n        },\n        watch: {\n            configObj: {\n                handler (nVal, oVal) {\n                    this.defaults = nVal\n                    this.configData = nVal[this.configNme]\n                },\n                immediate: true,\n                deep: true\n            }\n        },\n        created () {\n            this.defaults = this.configObj\n            this.configData = this.configObj[this.configNme]\n        },\n        methods: {\n\t\t\tlinkUrl(e){\n\t\t\t    this.configData.link = e;\n\t\t\t},\n\t\t\tgetLink(){\n\t\t\t    this.$refs.linkaddres.modals = true\n\t\t\t},\n            bindDelete () {\n                this.configData.url = '';\n            },\n            // 点击图文封面\n            modalPicTap (title) {\n                this.modalPic = true;\n            },\n            // 添加自定义弹窗\n            addCustomDialog (editorId) {\n                window.UE.registerUI('test-dialog', function (editor, uiName) {\n                    let dialog = new window.UE.ui.Dialog({\n                        iframeUrl: '/admin/widget.images/index.html?fodder=dialog',\n                        editor: editor,\n                        name: uiName,\n                        title: '上传图片',\n                        cssRules: 'width:1200px;height:500px;padding:20px;'\n                    });\n                    this.dialog = dialog;\n                    // 参考上面的自定义按钮\n                    var btn = new window.UE.ui.Button({\n                        name: 'dialog-button',\n                        title: '上传图片',\n                        cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,\n                        onclick: function () {\n                            // 渲染dialog\n                            dialog.render();\n                            dialog.open();\n                        }\n                    });\n\n                    return btn;\n                }, 37);\n            },\n            // 获取图片信息\n            getPic (pc) {\n                this.$nextTick(() => {\n                    this.configData.url = pc.att_dir;\n                    this.modalPic = false;\n                })\n            }\n        }\n    }\n", null]}