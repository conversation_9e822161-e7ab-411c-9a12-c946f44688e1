{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=template&id=5173b4e1&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751012477533}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"sales-stats-container\">\n  <!-- 页面头部 -->\n  <div class=\"page-header\">\n    <div class=\"header-left\">\n      <h2 class=\"page-title\">商品销售统计</h2>\n      <p class=\"page-desc\">查看各门店商品销售量、销售金额和当前库存统计</p>\n    </div>\n    <div class=\"header-right\">\n      <Button type=\"success\" icon=\"ios-download\" @click=\"exportSales\">导出统计</Button>\n    </div>\n  </div>\n\n  <!-- 筛选条件 -->\n  <div class=\"filter-container\">\n    <Form :model=\"filterForm\" inline>\n      <FormItem label=\"门店：\">\n        <Select v-model=\"filterForm.store_id\" placeholder=\"选择门店\" filterable clearable style=\"width: 200px\">\n          <Option\n            v-for=\"store in storeList\"\n            :key=\"store.value\"\n            :value=\"store.value\"\n            :label=\"store.label\">\n            {{ store.label }}\n          </Option>\n        </Select>\n      </FormItem>\n\n      <FormItem label=\"日期范围：\">\n        <DatePicker\n          v-model=\"dateRange\"\n          type=\"daterange\"\n          split-panels\n          placeholder=\"选择日期范围\"\n          style=\"width: 300px\"\n          @on-change=\"handleDateChange\" />\n      </FormItem>\n\n      <FormItem label=\"商品名称：\">\n        <Input\n          v-model=\"filterForm.keyword\"\n          placeholder=\"输入商品名称\"\n          style=\"width: 200px\"\n          search\n          enter-button\n          @on-search=\"getSalesStatistics\" />\n      </FormItem>\n\n      <FormItem>\n        <Button type=\"primary\" @click=\"getSalesStatistics\">查询</Button>\n        <Button @click=\"resetFilter\" style=\"margin-left: 8px\">重置</Button>\n      </FormItem>\n    </Form>\n  </div>\n\n  <!-- 动态表格 -->\n  <Table\n    :loading=\"tableLoading\"\n    :columns=\"columns\"\n    :data=\"tableData\"\n    border\n    highlight-row\n  >\n  </Table>\n\n  <!-- 分页 -->\n  <div class=\"pagination-container\">\n    <Page\n      :total=\"total\"\n      :current.sync=\"currentPage\"\n      :page-size=\"pageSize\"\n      :page-size-opts=\"[20, 50, 100, 200]\"\n      show-total\n      show-sizer\n      show-elevator\n      @on-change=\"handleCurrentChange\"\n      @on-page-size-change=\"handleSizeChange\">\n    </Page>\n  </div>\n</div>\n", null]}