{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751016248065}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getSalesStatistics as _getSalesStatistics, getStoreOptions, exportSalesStatistics } from '@/api/erp';\nimport exportExcel from '@/utils/newToExcel.js';\nexport default {\n  name: 'SalesStatistics',\n  data: function data() {\n    return {\n      // 筛选表单\n      filterForm: {\n        store_id: '',\n        keyword: '',\n        start_date: '',\n        end_date: ''\n      },\n      // 日期范围\n      dateRange: [],\n      // 列表数据\n      storeList: [],\n      columns: [],\n      tableData: [],\n      // 分页\n      currentPage: 1,\n      pageSize: 20,\n      total: 0,\n      // 状态\n      tableLoading: false\n    };\n  },\n  mounted: function mounted() {\n    this.initData();\n  },\n  methods: {\n    // 初始化数据\n    initData: function () {\n      var _initData = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _context.next = 2;\n                return this.fetchStoreOptions();\n\n              case 2:\n                _context.next = 4;\n                return this.getSalesStatistics();\n\n              case 4:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function initData() {\n        return _initData.apply(this, arguments);\n      }\n\n      return initData;\n    }(),\n    // 获取门店选项\n    fetchStoreOptions: function () {\n      var _fetchStoreOptions = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee2() {\n        var _ref, data;\n\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return getStoreOptions();\n\n              case 3:\n                _ref = _context2.sent;\n                data = _ref.data;\n                this.storeList = data || [];\n                _context2.next = 11;\n                break;\n\n              case 8:\n                _context2.prev = 8;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('获取门店列表失败:', _context2.t0);\n\n              case 11:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 8]]);\n      }));\n\n      function fetchStoreOptions() {\n        return _fetchStoreOptions.apply(this, arguments);\n      }\n\n      return fetchStoreOptions;\n    }(),\n    // 获取销售统计数据\n    getSalesStatistics: function () {\n      var _getSalesStatistics2 = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee3() {\n        var params, _ref2, data;\n\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.tableLoading = true;\n                _context3.prev = 1;\n                params = _objectSpread({}, this.filterForm, {\n                  page: this.currentPage,\n                  limit: this.pageSize\n                });\n                _context3.next = 5;\n                return _getSalesStatistics(params);\n\n              case 5:\n                _ref2 = _context3.sent;\n                data = _ref2.data;\n                this.tableData = data.data;\n                this.columns = data.columns;\n                this.total = data.total;\n                _context3.next = 16;\n                break;\n\n              case 12:\n                _context3.prev = 12;\n                _context3.t0 = _context3[\"catch\"](1);\n                console.error('获取销售统计失败:', _context3.t0);\n                this.$Message.error('获取销售统计失败');\n\n              case 16:\n                _context3.prev = 16;\n                this.tableLoading = false;\n                return _context3.finish(16);\n\n              case 19:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[1, 12, 16, 19]]);\n      }));\n\n      function getSalesStatistics() {\n        return _getSalesStatistics2.apply(this, arguments);\n      }\n\n      return getSalesStatistics;\n    }(),\n    // 处理日期变化\n    handleDateChange: function handleDateChange(value) {\n      if (value && value.length === 2) {\n        this.filterForm.start_date = value[0];\n        this.filterForm.end_date = value[1];\n      } else {\n        this.filterForm.start_date = '';\n        this.filterForm.end_date = '';\n      }\n    },\n    // 重置筛选\n    resetFilter: function resetFilter() {\n      this.filterForm = {\n        store_id: '',\n        keyword: '',\n        start_date: '',\n        end_date: ''\n      };\n      this.dateRange = [];\n      this.currentPage = 1;\n      this.getSalesStatistics();\n    },\n    // 分页变化\n    handleCurrentChange: function handleCurrentChange(page) {\n      this.currentPage = page;\n      this.getSalesStatistics();\n    },\n    handleSizeChange: function handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n      this.getSalesStatistics();\n    },\n    // 导出销售数据\n    exportSales: function () {\n      var _exportSales = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee4() {\n        var th, filekey, data, fileName, excelData, i, lebData, sheetData, j, goodsList, k, row, key;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                this.exportListOn = this.exportList.findIndex(function (item) {\n                  return item.name === value;\n                });\n                th = [], filekey = [], data = [], fileName = '';\n                excelData = _objectSpread({}, this.where, {\n                  page: 1,\n                  export_type: value,\n                  ids: this.checkUidList.join()\n                });\n                i = 0;\n\n              case 4:\n                if (!(i < excelData.page)) {\n                  _context4.next = 18;\n                  break;\n                }\n\n                _context4.next = 7;\n                return this.downOrderData(excelData);\n\n              case 7:\n                lebData = _context4.sent;\n\n                if (lebData.export.length) {\n                  _context4.next = 10;\n                  break;\n                }\n\n                return _context4.abrupt(\"break\", 18);\n\n              case 10:\n                if (!fileName) {\n                  fileName = lebData.filename;\n                }\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) {\n                  th = lebData.header;\n                }\n\n                data = data.concat(lebData.export);\n                excelData.page++;\n\n              case 15:\n                i++;\n                _context4.next = 4;\n                break;\n\n              case 18:\n                sheetData = [];\n\n                for (j = 0; j < data.length; j++) {\n                  goodsList = data[j].goods_name.split('\\n');\n\n                  for (k = 0; k < goodsList.length; k++) {\n                    row = _objectSpread({}, data[j]);\n                    row.goods_name = goodsList[k];\n\n                    if (k) {\n                      for (key in row) {\n                        if (Object.hasOwnProperty.call(row, key)) {\n                          if (key !== 'goods_name') {\n                            row[key] = null;\n                          }\n                        }\n                      }\n                    }\n\n                    sheetData.push(row);\n                  }\n                }\n\n                exportExcel(th, filekey, fileName, sheetData);\n\n              case 21:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this);\n      }));\n\n      function exportSales() {\n        return _exportSales.apply(this, arguments);\n      }\n\n      return exportSales;\n    }()\n  }\n};", null]}