{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue?vue&type=template&id=04d2cb2e&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue", "mtime": 1683692372000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"goodList\"},[_c('Form',{ref:\"formValidate\",staticClass:\"tabform\",attrs:{\"model\":_vm.formValidate,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition}},[_c('Row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"商品分类：\",\"label-for\":\"pid\"}},[_c('Cascader',{directives:[{name:\"width\",rawName:\"v-width\",value:('200px'),expression:\"'200px'\"}],attrs:{\"data\":_vm.treeSelect,\"placeholder\":\"请选择商品分类\",\"change-on-select\":\"\",\"filterable\":\"\"},on:{\"on-change\":_vm.treeSearchs}})],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"商品标签：\",\"label-for\":\"pid\"}},[_c('Select',{staticClass:\"width20\",attrs:{\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.formValidate.store_label_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"store_label_id\", $$v)},expression:\"formValidate.store_label_id\"}},_vm._l((_vm.labelSelect),function(item){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.label_name)+\"\\n\\t\\t\\t\\t\\t\\t\")])}),1)],1)],1),_c('Col',_vm._b({},'Col',_vm.grid,false),[_c('FormItem',{attrs:{\"label\":\"商品搜索：\",\"label-for\":\"store_name\"}},[_c('Input',{staticStyle:{\"width\":\"240px\"},attrs:{\"search\":\"\",\"enter-button\":\"\",\"placeholder\":\"请输入商品名称,关键字,编号\"},on:{\"on-search\":_vm.userSearchs},model:{value:(_vm.formValidate.store_name),callback:function ($$v) {_vm.$set(_vm.formValidate, \"store_name\", $$v)},expression:\"formValidate.store_name\"}})],1)],1)],1)],1),_c('div',{staticClass:\"vxeTable\"},[_c('vxe-table',{ref:\"xTree\",attrs:{\"border\":\"inner\",\"column-config\":{resizable: true},\"row-id\":\"id\",\"tree-config\":{children: 'attrValue',reserve:true},\"data\":_vm.tableList,\"max-height\":\"400\",\"checkbox-config\":{reserve: true}}},[_c('vxe-column',{attrs:{\"type\":\"checkbox\",\"title\":\"多选\",\"width\":\"90\",\"tree-node\":\"\"}}),_c('vxe-column',{attrs:{\"field\":\"id\",\"title\":\"商品ID\",\"width\":\"80\"}}),_c('vxe-column',{attrs:{\"field\":\"image\",\"title\":\"图片\",\"min-width\":\"90\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('viewer',[_c('div',{staticClass:\"tabBox_img\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.image),expression:\"row.image\"}]})])])]}}])}),_c('vxe-column',{attrs:{\"field\":\"store_name\",\"title\":\"商品名称\",\"min-width\":\"190\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('Tooltip',{attrs:{\"max-width\":\"500\",\"placement\":\"bottom\"}},[_c('span',{staticClass:\"line2\"},[_vm._v(_vm._s(row.store_name))]),_c('p',{attrs:{\"slot\":\"content\"},slot:\"content\"},[_vm._v(_vm._s(row.store_name))])])]}}])}),_c('vxe-column',{attrs:{\"field\":\"product_type\",\"title\":\"商品类型\",\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [(row.product_type==0)?_c('span',[_vm._v(\"普通商品\")]):_vm._e(),(row.product_type==1)?_c('span',[_vm._v(\"卡密商品\")]):_vm._e(),(row.product_type==3)?_c('span',[_vm._v(\"虚拟商品\")]):_vm._e(),(row.product_type==4)?_c('span',[_vm._v(\"次卡商品\")]):_vm._e()]}}])}),_c('vxe-column',{attrs:{\"field\":\"cate_name\",\"title\":\"商品分类\",\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('Tooltip',{attrs:{\"max-width\":\"200\",\"placement\":\"bottom\"}},[_c('span',{staticClass:\"line2\"},[_vm._v(_vm._s(row.cate_name))]),_c('p',{attrs:{\"slot\":\"content\"},slot:\"content\"},[_vm._v(_vm._s(row.cate_name))])])]}}])}),_c('vxe-column',{attrs:{\"field\":\"store_label\",\"title\":\"商品标签\",\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('Tooltip',{attrs:{\"max-width\":\"500\",\"placement\":\"bottom\"}},[_c('span',{staticClass:\"line2\"},[_vm._v(_vm._s(row.store_label))]),_c('p',{attrs:{\"slot\":\"content\"},slot:\"content\"},[_vm._v(_vm._s(row.store_label))])])]}}])})],1),_c('vxe-pager',{attrs:{\"border\":\"\",\"size\":\"medium\",\"page-size\":_vm.formValidate.limit,\"current-page\":_vm.formValidate.page,\"total\":_vm.total,\"layouts\":['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Total']},on:{\"page-change\":_vm.pageChange}})],1),_c('div',{staticClass:\"footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"long\":\"\"},on:{\"click\":_vm.ok}},[_vm._v(\"提交\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}