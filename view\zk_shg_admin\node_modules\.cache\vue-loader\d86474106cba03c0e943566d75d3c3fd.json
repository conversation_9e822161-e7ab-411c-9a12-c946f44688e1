{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue?vue&type=template&id=4f15628a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\productDetails.vue", "mtime": 1718268362000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Drawer',{attrs:{\"value\":_vm.visible,\"closable\":true,\"styles\":{ padding: '0 0 60px' },\"width\":\"1000\"},on:{\"on-visible-change\":_vm.drawerChange}},[_c('div',{staticClass:\"header\"},[_c('Icon',{attrs:{\"custom\":\"iconfont iconmanjianmanzhe\",\"size\":\"60\"}}),_c('div',[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.formValidate.store_name))]),_c('div',[_vm._v(\"商品ID：\"+_vm._s(_vm.productId))])])],1),_c('Tabs',{model:{value:(_vm.currentTab),callback:function ($$v) {_vm.currentTab=$$v},expression:\"currentTab\"}},_vm._l((_vm.headTab),function(item,index){return (index !=6 || (index==6 && _vm.merchantType==0))?_c('TabPane',{key:item.name,attrs:{\"label\":item.title,\"name\":item.name}}):_vm._e()}),1),_c('Form',{ref:\"formValidate\",staticClass:\"formValidate\",attrs:{\"model\":_vm.formValidate,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Row',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentTab === '1'),expression:\"currentTab === '1'\"}],attrs:{\"type\":\"flex\"}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品类型：\",\"props\":\"is_virtual\"}},[_c('RadioGroup',{model:{value:(_vm.formValidate.product_type),callback:function ($$v) {_vm.$set(_vm.formValidate, \"product_type\", $$v)},expression:\"formValidate.product_type\"}},_vm._l((_vm.productType),function(item){return _c('Radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"}},[_vm._v(_vm._s(item.name))])}),1)],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品分类：\",\"prop\":\"cate_id\"}},[_c('el-cascader',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"placeholder\":\"请选择商品分类\",\"size\":\"mini\",\"options\":_vm.treeSelect,\"props\":_vm.props,\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.formValidate.cate_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"cate_id\", $$v)},expression:\"formValidate.cate_id\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品名称：\",\"prop\":\"store_name\"}},[_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"placeholder\":\"请输入商品名称\"},model:{value:(_vm.formValidate.store_name),callback:function ($$v) {_vm.$set(_vm.formValidate, \"store_name\", $$v)},expression:\"formValidate.store_name\"}})],1)],1),_c('Col',{staticClass:\"brandName\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品品牌：\",\"prop\":\"\"}},[_c('Cascader',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"data\":_vm.brandData,\"placeholder\":\"请选择商品品牌\",\"change-on-select\":\"\",\"filterable\":\"\"},model:{value:(_vm.formValidate.brand_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"brand_id\", $$v)},expression:\"formValidate.brand_id\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"单位：\",\"prop\":\"unit_name\"}},[_c('Select',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"clearable\":\"\",\"filterable\":\"\",\"placeholder\":\"请输入单位\"},model:{value:(_vm.formValidate.unit_name),callback:function ($$v) {_vm.$set(_vm.formValidate, \"unit_name\", $$v)},expression:\"formValidate.unit_name\"}},_vm._l((_vm.unitNameList),function(item,index){return _c('Option',{key:item.id,attrs:{\"value\":item.name}},[_vm._v(_vm._s(item.name))])}),1)],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品编码：\",\"prop\":\"\"}},[_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"placeholder\":\"请输入商品编码\"},model:{value:(_vm.formValidate.code),callback:function ($$v) {_vm.$set(_vm.formValidate, \"code\", $$v)},expression:\"formValidate.code\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品轮播图：\",\"prop\":\"slider_image\"}},[_c('div',{staticClass:\"acea-row\"},[_vm._l((_vm.formValidate.slider_image),function(item,index){return _c('div',{key:index+'img',staticClass:\"pictrue\",attrs:{\"draggable\":\"true\"},on:{\"dragstart\":function($event){return _vm.handleDragStart($event, item)},\"dragover\":function($event){$event.preventDefault();return _vm.handleDragOver($event, item)},\"dragenter\":function($event){return _vm.handleDragEnter($event, item)},\"dragend\":function($event){return _vm.handleDragEnd($event, item)}}},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(item),expression:\"item\"}]}),_c('Button',{staticClass:\"btndel\",attrs:{\"shape\":\"circle\",\"icon\":\"md-close\"},nativeOn:{\"click\":function($event){return _vm.handleRemove(index)}}})],1)}),(_vm.formValidate.slider_image.length < 10)?_c('div',{staticClass:\"upLoad acea-row row-center-wrapper\",on:{\"click\":function($event){return _vm.modalPicTap('duo')}}},[_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1):_vm._e(),_c('Input',{staticClass:\"input-display\",model:{value:(_vm.formValidate.slider_image[0]),callback:function ($$v) {_vm.$set(_vm.formValidate.slider_image, 0, $$v)},expression:\"formValidate.slider_image[0]\"}})],2),_c('div',{staticClass:\"tips\"},[_vm._v(\"\\n              建议尺寸：800\\n              *800px，可拖拽改变图片顺序，默认首张图为主图，最多上传10张\\n            \")])])],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{staticClass:\"labelClass\",attrs:{\"label\":\"商品标签：\",\"prop\":\"store_label_id\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('div',{staticClass:\"labelInput acea-row row-between-wrapper\",on:{\"click\":_vm.openStoreLabel}},[(_vm.storeDataLabel.length)?_c('div',_vm._l((_vm.storeDataLabel),function(item,index){return _c('Tag',{key:item.id,attrs:{\"closable\":\"\"},on:{\"on-close\":function($event){return _vm.closeStoreLabel(item)}}},[_vm._v(_vm._s(item.label_name))])}),1):_c('span',{staticClass:\"span\"},[_vm._v(\"选择商品标签\")])])])])],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"添加视频：\"}},[_c('i-switch',{attrs:{\"size\":\"large\"},model:{value:(_vm.formValidate.video_open),callback:function ($$v) {_vm.$set(_vm.formValidate, \"video_open\", $$v)},expression:\"formValidate.video_open\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1),(_vm.formValidate.video_open)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"视频类型：\"}},[_c('RadioGroup',{on:{\"on-change\":_vm.changeVideo},model:{value:(_vm.seletVideo),callback:function ($$v) {_vm.seletVideo=$$v},expression:\"seletVideo\"}},[_c('Radio',{staticClass:\"radio\",attrs:{\"label\":0}},[_vm._v(\"本地视频\")]),_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"视频链接\")])],1)],1)],1):_vm._e(),(_vm.formValidate.video_open)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\",\"prop\":\"video_link\"}},[(_vm.seletVideo == 1 && !_vm.formValidate.video_link)?_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"placeholder\":\"请输入视频链接\"},model:{value:(_vm.videoLink),callback:function ($$v) {_vm.videoLink=$$v},expression:\"videoLink\"}}):_vm._e(),_c('input',{ref:\"refid\",staticClass:\"input-display\",attrs:{\"type\":\"file\"},on:{\"change\":_vm.zh_uploadFile_change}}),(\n            _vm.seletVideo == 0 &&\n            (_vm.upload_type !== '1' || _vm.videoLink) &&\n            !_vm.formValidate.video_link\n          )?_c('div',{staticClass:\"ml10 videbox\",on:{\"click\":_vm.zh_uploadFile}},[_vm._v(\"\\n              +\\n            \")]):_vm._e(),(\n            _vm.seletVideo == 1 &&\n            (_vm.upload_type !== '1' || _vm.videoLink) &&\n            !_vm.formValidate.video_link\n          )?_c('Button',{staticClass:\"uploadVideo\",attrs:{\"type\":\"primary\",\"icon\":\"ios-cloud-upload-outline\"},on:{\"click\":_vm.zh_uploadFile}},[_vm._v(\"确认添加\")]):_vm._e(),(_vm.upload_type === '1' && !_vm.formValidate.video_link)?_c('Upload',{staticStyle:{\"display\":\"inline-block\"},attrs:{\"show-upload-list\":false,\"action\":_vm.fileUrl2,\"before-upload\":_vm.videoSaveToUrl,\"data\":_vm.uploadData,\"headers\":_vm.header,\"multiple\":true}},[(_vm.seletVideo === 0 && !_vm.formValidate.video_link)?_c('div',{staticClass:\"videbox\"},[_vm._v(\"\\n                +\\n              \")]):_vm._e()]):_vm._e(),(_vm.formValidate.video_link)?_c('div',{staticClass:\"iview-video-style\"},[_c('video',{staticClass:\"video-style\",attrs:{\"src\":_vm.formValidate.video_link,\"controls\":\"controls\"}},[_vm._v(\"\\n                您的浏览器不支持 video 标签。\\n              \")]),_c('div',{staticClass:\"mark\"}),_c('Icon',{staticClass:\"iconv\",attrs:{\"type\":\"ios-trash-outline\"},on:{\"click\":_vm.delVideo}})],1):_vm._e(),(_vm.upload.videoIng || _vm.videoIng)?_c('Progress',{staticClass:\"progress\",attrs:{\"percent\":_vm.progress,\"stroke-width\":5}}):_vm._e(),_c('div',{staticClass:\"tips\"},[_vm._v(\"建议时长：9～30秒，视频宽高比16:9\")])],1)],1):_vm._e(),_c('Col',{staticClass:\"goodsShow\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"上架时间：\"}},[_c('RadioGroup',{on:{\"on-change\":_vm.goodsOn},model:{value:(_vm.formValidate.is_show),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_show\", $$v)},expression:\"formValidate.is_show\"}},[_c('Radio',{attrs:{\"label\":1}},[_c('Icon',{attrs:{\"type\":\"social-apple\"}}),_c('span',[_vm._v(\"立即上架\")])],1),_c('Radio',{attrs:{\"label\":2}},[_c('Icon',{attrs:{\"type\":\"social-android\"}}),_c('span',[_vm._v(\"定时上架\")])],1),_c('Radio',{attrs:{\"label\":0}},[_c('Icon',{attrs:{\"type\":\"social-windows\"}}),_c('span',[_vm._v(\"放入仓库\")])],1)],1)],1)],1),(_vm.formValidate.is_show == 2)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\"}},[_c('DatePicker',{staticStyle:{\"width\":\"260px\"},attrs:{\"type\":\"datetime\",\"options\":_vm.startPickOptions,\"value\":_vm.formValidate.auto_on_time,\"placeholder\":\"请选择上架时间\",\"format\":\"yyyy-MM-dd HH:mm\"},on:{\"on-change\":_vm.onchangeShow},model:{value:(_vm.formValidate.auto_on_time),callback:function ($$v) {_vm.$set(_vm.formValidate, \"auto_on_time\", $$v)},expression:\"formValidate.auto_on_time\"}})],1)],1):_vm._e(),_c('Col',{staticClass:\"goodsShow\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"定时下架：\"}},[_c('i-switch',{attrs:{\"true-value\":1,\"false-value\":0,\"size\":\"large\"},on:{\"on-change\":_vm.goodsOff},model:{value:(_vm.off_show),callback:function ($$v) {_vm.off_show=$$v},expression:\"off_show\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1),(_vm.off_show == 1)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\"}},[_c('DatePicker',{staticStyle:{\"width\":\"260px\"},attrs:{\"type\":\"datetime\",\"options\":_vm.endPickOptions,\"value\":_vm.formValidate.auto_off_time,\"placeholder\":\"请选择下架时间\",\"format\":\"yyyy-MM-dd HH:mm\"},on:{\"on-change\":_vm.onchangeOff},model:{value:(_vm.formValidate.auto_off_time),callback:function ($$v) {_vm.$set(_vm.formValidate, \"auto_off_time\", $$v)},expression:\"formValidate.auto_off_time\"}}),_c('div',{staticClass:\"tips\"},[_vm._v(\"\\n              开启定时下架后，系统会在设置时间下架该商品。下架时间需晚于开售时间，商品才能定时开售。\\n            \")])],1)],1):_vm._e()],1),_c('Row',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentTab === '2'),expression:\"currentTab === '2'\"}],attrs:{\"gutter\":24,\"type\":\"flex\"}},[(_vm.formValidate.product_type != 4)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品规格：\",\"props\":\"spec_type\"}},[_c('RadioGroup',{on:{\"on-change\":_vm.changeSpec},model:{value:(_vm.formValidate.spec_type),callback:function ($$v) {_vm.$set(_vm.formValidate, \"spec_type\", $$v)},expression:\"formValidate.spec_type\"}},[_c('Radio',{staticClass:\"radio\",attrs:{\"label\":0}},[_vm._v(\"单规格\")]),_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"多规格\")])],1)],1)],1):_vm._e(),(_vm.formValidate.spec_type === 1  && _vm.formValidate.product_type != 4)?_c('Col',{staticClass:\"noForm\",attrs:{\"span\":\"24\"}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"选择规格：\",\"prop\":\"\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('Select',{staticStyle:{\"width\":\"23%\"},model:{value:(_vm.formValidate.selectRule),callback:function ($$v) {_vm.$set(_vm.formValidate, \"selectRule\", $$v)},expression:\"formValidate.selectRule\"}},_vm._l((_vm.ruleList),function(item,index){return _c('Option',{key:item.id,attrs:{\"value\":item.rule_name}},[_vm._v(_vm._s(item.rule_name)+\"\\n                  \")])}),1),_c('Button',{staticClass:\"mr20\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.confirm}},[_vm._v(\"确认\")]),_c('Button',{on:{\"click\":_vm.addRule}},[_vm._v(\"添加规格模板\")])],1)])],1),_c('Col',{attrs:{\"span\":\"24\"}},[(_vm.attrs.length !== 0)?_c('FormItem',[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.attrs,\"group\":\"peoples\",\"handle\":\".move-icon\",\"move\":_vm.checkMove},on:{\"end\":_vm.end}},_vm._l((_vm.attrs),function(item,index){return _c('div',{key:index+'attrs',staticClass:\"acea-row row-middle mb10\"},[_c('div',{staticClass:\"move-icon\"},[_c('span',{staticClass:\"iconfont icondrag2\"})]),_c('div',{class:_vm.moveIndex === index ? 'borderStyle' : '',staticStyle:{\"width\":\"90%\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('span',{staticClass:\"mr5\"},[_vm._v(_vm._s(item.value))]),_c('Icon',{staticClass:\"curs\",attrs:{\"type\":\"ios-close-circle\",\"size\":\"14\"},on:{\"click\":function($event){return _vm.handleRemoveRole(index)}}})],1),_c('div',{staticClass:\"rulesBox\"},[_c('draggable',{attrs:{\"list\":item.detail,\"handle\":\".drag\"}},_vm._l((item.detail),function(j,indexn){return _c('Tag',{key:indexn,staticClass:\"mr20 drag\",attrs:{\"type\":\"dot\",\"closable\":\"\",\"color\":\"primary\",\"name\":j},on:{\"on-close\":function($event){return _vm.handleRemove2(item.detail, indexn)}}},[_vm._v(_vm._s(j))])}),1),_c('Input',{staticClass:\"width-add\",attrs:{\"search\":\"\",\"enter-button\":\"添加\",\"placeholder\":\"请输入属性名称\"},on:{\"on-search\":function($event){return _vm.createAttr(item.detail.attrsVal, index)}},model:{value:(item.detail.attrsVal),callback:function ($$v) {_vm.$set(item.detail, \"attrsVal\", $$v)},expression:\"item.detail.attrsVal\"}})],1)])])}),0)],1):_vm._e()],1),(_vm.createBnt)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',[_c('Button',{staticClass:\"mr15\",attrs:{\"type\":\"primary\",\"icon\":\"md-add\"},on:{\"click\":_vm.addBtn}},[_vm._v(\"添加新规格\")]),_c('Button',{attrs:{\"type\":\"success\"},on:{\"click\":function($event){return _vm.generate(1)}}},[_vm._v(\"立即生成\")])],1)],1):_vm._e(),(_vm.showIput)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('Col',{attrs:{\"xl\":6,\"lg\":9,\"md\":10,\"sm\":24,\"xs\":24}},[_c('FormItem',{attrs:{\"label\":\"规格：\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入规格\"},model:{value:(_vm.formDynamic.attrsName),callback:function ($$v) {_vm.$set(_vm.formDynamic, \"attrsName\", $$v)},expression:\"formDynamic.attrsName\"}})],1)],1),_c('Col',{attrs:{\"xl\":6,\"lg\":9,\"md\":10,\"sm\":24,\"xs\":24}},[_c('FormItem',{attrs:{\"label\":\"规格值：\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入规格值\"},model:{value:(_vm.formDynamic.attrsVal),callback:function ($$v) {_vm.$set(_vm.formDynamic, \"attrsVal\", $$v)},expression:\"formDynamic.attrsVal\"}})],1)],1),_c('Col',{attrs:{\"xl\":6,\"lg\":5,\"md\":10,\"sm\":24,\"xs\":24}},[_c('FormItem',[_c('Button',{staticClass:\"mr15\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.createAttrName}},[_vm._v(\"确定\")]),_c('Button',{on:{\"click\":_vm.offAttrName}},[_vm._v(\"取消\")])],1)],1)],1):_vm._e(),(\n          _vm.manyFormValidate.length &&\n          _vm.formValidate.header.length !== 0 &&\n          _vm.attrs.length !== 0\n        )?_c('Col',{attrs:{\"xl\":24,\"lg\":24,\"md\":24,\"sm\":24,\"xs\":24}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{staticClass:\"labeltop\",attrs:{\"label\":\"批量设置：\"}},[_c('Table',{attrs:{\"data\":_vm.oneFormBatch,\"columns\":_vm.formValidate.product_type == 1\n              ? _vm.columnsCarMy\n              : _vm.formValidate.product_type == 3\n                ? _vm.columnsFictitious\n                : _vm.columns2,\"border\":\"\"},scopedSlots:_vm._u([{key:\"attr\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('div',{staticClass:\"acea-row row-between-wrapper\",staticStyle:{\"cursor\":\"pointer\"},on:{\"click\":_vm.batchAttr}},[_c('div',{staticStyle:{\"width\":\"41px\"}},[_vm._v(_vm._s(_vm.oneFormBatch[0].attr))]),_c('span',{staticClass:\"iconfont icondrop-down\"})])]}},{key:\"pic\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('div',{staticClass:\"acea-row row-middle row-center-wrapper\",on:{\"click\":function($event){return _vm.modalPicTap('dan', 'duopi', index)}}},[(_vm.oneFormBatch[0].pic)?_c('div',{staticClass:\"pictrue pictrueTab\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.oneFormBatch[0].pic),expression:\"oneFormBatch[0].pic\"}]})]):_c('div',{staticClass:\"upLoad pictrueTab acea-row row-center-wrapper\"},[_c('Icon',{staticClass:\"iosfont\",attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1)])]}},{key:\"price\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].price),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"price\", $$v)},expression:\"oneFormBatch[0].price\"}})]}},{key:\"settle_price\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn (_vm.merchantType == 2)?[_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].settle_price),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"settle_price\", $$v)},expression:\"oneFormBatch[0].settle_price\"}})]:undefined}},{key:\"cost\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].cost),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"cost\", $$v)},expression:\"oneFormBatch[0].cost\"}})]}},{key:\"ot_price\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormBatch[0].ot_price),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"ot_price\", $$v)},expression:\"oneFormBatch[0].ot_price\"}})]}},{key:\"stock\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0,\"disabled\":_vm.formValidate.product_type == 1 || _vm.openErp},model:{value:(_vm.oneFormBatch[0].stock),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"stock\", $$v)},expression:\"oneFormBatch[0].stock\"}})]}},{key:\"bar_code\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('Input',{model:{value:(_vm.oneFormBatch[0].bar_code),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"bar_code\", $$v)},expression:\"oneFormBatch[0].bar_code\"}})]}},{key:\"code\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('Input',{model:{value:(_vm.oneFormBatch[0].code),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"code\", $$v)},expression:\"oneFormBatch[0].code\"}})]}},{key:\"weight\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"step\":0.1,\"min\":0},model:{value:(_vm.oneFormBatch[0].weight),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"weight\", $$v)},expression:\"oneFormBatch[0].weight\"}})]}},{key:\"volume\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"step\":0.1,\"min\":0},model:{value:(_vm.oneFormBatch[0].volume),callback:function ($$v) {_vm.$set(_vm.oneFormBatch[0], \"volume\", $$v)},expression:\"oneFormBatch[0].volume\"}})]}},{key:\"fictitious\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn (_vm.formValidate.product_type == 1)?[(!row.virtual_list && !row.stock)?_c('Button',{on:{\"click\":function($event){return _vm.addVirtual(0, 'oneFormBatch')}}},[_vm._v(\"添加卡密\")]):_c('span',{staticClass:\"seeCatMy\",on:{\"click\":function($event){return _vm.seeVirtual(_vm.oneFormBatch[0], 'oneFormBatch', 0)}}},[_vm._v(\"已设置\")])]:undefined}},{key:\"action\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('a',{on:{\"click\":_vm.batchAdd}},[_vm._v(\"批量设置\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":_vm.batchDel}},[_vm._v(\"清空\")])]}}],null,true)})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{staticClass:\"labeltop\",attrs:{\"label\":\"商品属性：\"}},[_c('Table',{attrs:{\"data\":_vm.manyFormValidate,\"columns\":_vm.formValidate.header,\"border\":\"\"},scopedSlots:_vm._u([_vm._l((_vm.attrData.length),function(item,i){return {key:'value' + (i + 1),fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('div',{class:_vm.manyFormValidate[index].select ? 'selectOn' : ''},[_vm._v(\"\\n                      \"+_vm._s(_vm.manyFormValidate[index]['value' + (i + 1)])+\"\\n                    \")])]}}}),{key:\"pic\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('div',{staticClass:\"acea-row row-middle row-center-wrapper\",on:{\"click\":function($event){return _vm.modalPicTap('dan', 'duoTable', index)}}},[(_vm.manyFormValidate[index].pic)?_c('div',{staticClass:\"pictrue pictrueTab\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.manyFormValidate[index].pic),expression:\"manyFormValidate[index].pic\"}]})]):_c('div',{staticClass:\"upLoad pictrueTab acea-row row-center-wrapper\"},[_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"21\"}})],1)])]}},{key:\"price\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].price),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"price\", $$v)},expression:\"manyFormValidate[index].price\"}})]}},{key:\"settle_price\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn (_vm.merchantType == 2)?[_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].settle_price),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"settle_price\", $$v)},expression:\"manyFormValidate[index].settle_price\"}})]:undefined}},{key:\"cost\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].cost),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"cost\", $$v)},expression:\"manyFormValidate[index].cost\"}})]}},{key:\"ot_price\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].ot_price),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"ot_price\", $$v)},expression:\"manyFormValidate[index].ot_price\"}})]}},{key:\"stock\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0,\"precision\":0,\"disabled\":_vm.formValidate.product_type == 1 || _vm.openErp},model:{value:(_vm.manyFormValidate[index].stock),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"stock\", $$v)},expression:\"manyFormValidate[index].stock\"}})]}},{key:\"fictitious\",fn:function(ref){\n                var row = ref.row;\n                var index = ref.index;\nreturn (_vm.formValidate.product_type == 1)?[(\n                (!row.virtual_list || !row.virtual_list.length) &&\n                !row.stock\n              )?_c('Button',{on:{\"click\":function($event){return _vm.addVirtual(index, 'manyFormValidate')}}},[_vm._v(\"添加卡密\")]):_c('span',{staticClass:\"seeCatMy\",on:{\"click\":function($event){return _vm.seeVirtual(row, 'manyFormValidate', index)}}},[_vm._v(\"已设置\")])]:undefined}},{key:\"bar_code\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [_c('Input',{model:{value:(_vm.manyFormValidate[index].bar_code),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"bar_code\", $$v)},expression:\"manyFormValidate[index].bar_code\"}})]}},{key:\"code\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [_c('Input',{model:{value:(_vm.manyFormValidate[index].code),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"code\", $$v)},expression:\"manyFormValidate[index].code\"}})]}},{key:\"weight\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].weight),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"weight\", $$v)},expression:\"manyFormValidate[index].weight\"}})]}},{key:\"volume\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].volume),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"volume\", $$v)},expression:\"manyFormValidate[index].volume\"}})]}},{key:\"action\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.delAttrTable(index)}}},[_vm._v(\"删除\")])]}}],null,true)})],1)],1)],1):_vm._e()],1):_vm._e(),(_vm.formValidate.spec_type === 0 || _vm.formValidate.product_type == 4)?_c('div',{staticStyle:{\"width\":\"100%\"}},[(_vm.formValidate.product_type != 4)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"图片：\",\"prop\":\"image\"}},[_c('div',{staticClass:\"pictrueBox\",on:{\"click\":function($event){return _vm.modalPicTap('dan', 'danTable', 0)}}},[(_vm.oneFormValidate[0].pic)?_c('div',{staticClass:\"pictrue\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.oneFormValidate[0].pic),expression:\"oneFormValidate[0].pic\"}]})]):_c('div',{staticClass:\"upLoad acea-row row-center-wrapper\"},[_c('Input',{staticClass:\"input-display\",model:{value:(_vm.oneFormValidate[0].pic),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"pic\", $$v)},expression:\"oneFormValidate[0].pic\"}}),_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1)])])],1):_vm._e(),(_vm.formValidate.product_type == 4)?_c('Col',{staticClass:\"asterisk\",attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"asteriskInfo on2\"},[_vm._v(\"*\")]),_c('FormItem',{attrs:{\"label\":\"核销次数：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":1,\"max\":99999999,\"precision\":0,\"placeholder\":\"请输入核销次数\"},model:{value:(_vm.oneFormValidate[0].write_times),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"write_times\", $$v)},expression:\"oneFormValidate[0].write_times\"}})],1)],1):_vm._e(),(_vm.formValidate.product_type == 4)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"required\":\"\",\"label\":\"核销时效：\"}},[_c('RadioGroup',{model:{value:(_vm.oneFormValidate[0].write_valid),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"write_valid\", $$v)},expression:\"oneFormValidate[0].write_valid\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"永久有效\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"购买后几天有效\")]),_c('Radio',{attrs:{\"label\":3}},[_vm._v(\"固定有效期\")])],1)],1)],1):_vm._e(),(_vm.oneFormValidate[0].write_valid==2)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\",\"prop\":\"freight\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":1,\"placeholder\":\"请输入有效天数\"},model:{value:(_vm.oneFormValidate[0].days),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"days\", $$v)},expression:\"oneFormValidate[0].days\"}}),_c('span',{staticClass:\"ml10\"},[_vm._v(\"天\")])],1)])],1):_vm._e(),(_vm.oneFormValidate[0].write_valid==3)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\",\"prop\":\"freight\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('DatePicker',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"editable\":false,\"type\":\"daterange\",\"format\":\"yyyy-MM-dd\",\"placeholder\":\"请选择固定有效期\",\"value\":_vm.oneFormValidate[0].section_time},on:{\"on-change\":_vm.onchangeTime},model:{value:(_vm.oneFormValidate[0].section_time),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"section_time\", $$v)},expression:\"oneFormValidate[0].section_time\"}})],1)])],1):_vm._e(),_c('Col',{staticClass:\"asterisk\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"售价：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":99999999},model:{value:(_vm.oneFormValidate[0].price),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"price\", $$v)},expression:\"oneFormValidate[0].price\"}})],1)],1),(_vm.merchantType == 2)?_c('Col',{staticClass:\"asterisk\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"结算价：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":99999999},model:{value:(_vm.oneFormValidate[0].settle_price),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"settle_price\", $$v)},expression:\"oneFormValidate[0].settle_price\"}})],1)],1):_vm._e(),_c('Col',{staticClass:\"asterisk\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"成本价：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":99999999},model:{value:(_vm.oneFormValidate[0].cost),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"cost\", $$v)},expression:\"oneFormValidate[0].cost\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"原价：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":99999999},model:{value:(_vm.oneFormValidate[0].ot_price),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"ot_price\", $$v)},expression:\"oneFormValidate[0].ot_price\"}})],1)],1),_c('Col',{staticClass:\"asterisk\",attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"库存：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":99999999,\"disabled\":_vm.formValidate.product_type == 1 || _vm.openErp,\"precision\":0},model:{value:(_vm.oneFormValidate[0].stock),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"stock\", $$v)},expression:\"oneFormValidate[0].stock\"}})],1)],1),(_vm.formValidate.product_type != 4)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品条形码：\"}},[_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"placeholder\":\"请输入商品条形码\"},model:{value:(_vm.oneFormValidate[0].bar_code),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"bar_code\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"oneFormValidate[0].bar_code\"}})],1)],1):_vm._e(),(_vm.formValidate.product_type != 4)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品编号：\"}},[_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"placeholder\":\"请输入商品编码\"},model:{value:(_vm.oneFormValidate[0].code),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"code\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"oneFormValidate[0].code\"}})],1)],1):_vm._e(),(_vm.formValidate.product_type == 0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"重量（KG）：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":99999999},model:{value:(_vm.oneFormValidate[0].weight),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"weight\", $$v)},expression:\"oneFormValidate[0].weight\"}})],1)],1):_vm._e(),(_vm.formValidate.product_type == 0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"体积(m³)：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":99999999},model:{value:(_vm.oneFormValidate[0].volume),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"volume\", $$v)},expression:\"oneFormValidate[0].volume\"}})],1)],1):_vm._e(),(_vm.formValidate.product_type == 1)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"卡密设置：\"}},[(\n              !_vm.oneFormValidate[0].virtual_list.length &&\n              !_vm.oneFormValidate[0].stock\n            )?_c('Button',{on:{\"click\":function($event){return _vm.addVirtual(0, 'oneFormValidate')}}},[_vm._v(\"添加卡密\")]):_c('span',{staticClass:\"seeCatMy\",on:{\"click\":function($event){return _vm.seeVirtual(_vm.oneFormValidate[0], 'oneFormValidate', 0)}}},[_vm._v(\"已设置\")])],1)],1):_vm._e()],1):_vm._e()],1),(_vm.currentTab === '3')?_c('Row',[_c('Col',{attrs:{\"span\":\"20\"}},[_c('wangeditor',{staticStyle:{\"width\":\"100%\"},attrs:{\"content\":_vm.contents},on:{\"editorContent\":_vm.getEditorContent}})],1)],1):_vm._e(),_c('Row',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentTab === '4'),expression:\"currentTab === '4'\"}]},[_c('Col',_vm._b({},'Col',_vm.grid3,false),[_c('FormItem',{attrs:{\"label\":\"配送方式：\",\"prop\":\"\",\"required\":\"\"}},[_c('CheckboxGroup',{model:{value:(_vm.formValidate.delivery_type),callback:function ($$v) {_vm.$set(_vm.formValidate, \"delivery_type\", $$v)},expression:\"formValidate.delivery_type\"}},[(_vm.merchantType!=1)?_c('Checkbox',{attrs:{\"label\":\"1\"}},[_vm._v(\"快递\")]):_vm._e(),_c('Checkbox',{attrs:{\"label\":\"3\"}},[_vm._v(\"门店配送\")]),_c('Checkbox',{attrs:{\"label\":\"2\"}},[_vm._v(\"到店自提\")])],1)],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"运费设置：\"}},[_c('RadioGroup',{model:{value:(_vm.formValidate.freight),callback:function ($$v) {_vm.$set(_vm.formValidate, \"freight\", $$v)},expression:\"formValidate.freight\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"包邮\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"固定邮费\")]),_c('Radio',{attrs:{\"label\":3}},[_vm._v(\"运费模板\")])],1)],1)],1),(_vm.formValidate.freight == 2)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\",\"prop\":\"freight\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('InputNumber',{staticClass:\"perW20 maxW\",attrs:{\"min\":0,\"placeholder\":\"请输入金额\"},model:{value:(_vm.formValidate.postage),callback:function ($$v) {_vm.$set(_vm.formValidate, \"postage\", $$v)},expression:\"formValidate.postage\"}}),_c('span',{staticClass:\"ml10\"},[_vm._v(\"元\")])],1)])],1):_vm._e(),(_vm.formValidate.freight == 3)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\",\"prop\":\"\"}},[_c('div',{staticClass:\"acea-row\"},[_c('Select',{staticClass:\"perW20 maxW\",attrs:{\"clearable\":\"\"},model:{value:(_vm.formValidate.temp_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"temp_id\", $$v)},expression:\"formValidate.temp_id\"}},_vm._l((_vm.templateList),function(item,index){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.name))])}),1)],1)])],1):_vm._e()],1),_c('Row',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentTab === '5'),expression:\"currentTab === '5'\"}]},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"虚拟销量：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":999999,\"placeholder\":\"请输入虚拟销量\"},model:{value:(_vm.formValidate.ficti),callback:function ($$v) {_vm.$set(_vm.formValidate, \"ficti\", $$v)},expression:\"formValidate.ficti\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"排序：\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":999999,\"placeholder\":\"请输入排序\"},model:{value:(_vm.formValidate.sort),callback:function ($$v) {_vm.$set(_vm.formValidate, \"sort\", $$v)},expression:\"formValidate.sort\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"lines\"})]),(_vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"赠送积分：\",\"prop\":\"give_integral\"}},[_c('InputNumber',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"min\":0,\"max\":999999,\"placeholder\":\"请输入积分\"},model:{value:(_vm.formValidate.give_integral),callback:function ($$v) {_vm.$set(_vm.formValidate, \"give_integral\", $$v)},expression:\"formValidate.give_integral\"}})],1)],1):_vm._e(),(_vm.merchantType==0)?_c('Col',_vm._b({},'Col',_vm.grid3,false),[_c('FormItem',{attrs:{\"label\":\"赠送优惠券：\"}},[(_vm.couponName.length)?_c('div',{staticClass:\"mb20\"},_vm._l((_vm.couponName),function(item,index){return _c('Tag',{key:index+'coupon',attrs:{\"closable\":\"\"},on:{\"on-close\":function($event){return _vm.handleClose(item)}}},[_vm._v(_vm._s(item.title))])}),1):_vm._e(),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.addCoupon}},[_vm._v(\"添加优惠券\")])],1)],1):_vm._e(),(_vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{staticClass:\"labelClass\",attrs:{\"label\":\"关联用户标签：\",\"prop\":\"label_id\"}},[_c('div',{staticClass:\"labelInput acea-row row-between-wrapper\",on:{\"click\":_vm.openLabel}},[_c('div',[(_vm.dataLabel.length)?_c('div',_vm._l((_vm.dataLabel),function(item,index){return _c('Tag',{key:item.id,attrs:{\"closable\":\"\"},on:{\"on-close\":function($event){return _vm.closeLabel(item)}}},[_vm._v(_vm._s(item.label_name)+\"\\n                  \")])}),1):_c('span',{staticClass:\"span\"},[_vm._v(\"选择用户关联标签\")])]),_c('Icon',{attrs:{\"type\":\"ios-arrow-down\"}})],1)])],1):_vm._e(),(_vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"仅会员可见：\"}},[_c('i-switch',{attrs:{\"true-value\":1,\"false-value\":0,\"size\":\"large\"},model:{value:(_vm.formValidate.is_vip_product),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_vip_product\", $$v)},expression:\"formValidate.is_vip_product\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),_c('div',{staticClass:\"tips\"},[_vm._v(\"开启后仅付费会员可以看见并购买此商品\")])],1)],1):_vm._e(),(_vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"单独设置：\"}},[_c('CheckboxGroup',{staticClass:\"checkAlls\",on:{\"on-change\":_vm.checkAllGroupChange},model:{value:(_vm.formValidate.is_sub),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_sub\", $$v)},expression:\"formValidate.is_sub\"}},[_c('Checkbox',{attrs:{\"label\":1}},[_vm._v(\"佣金设置（数字即返佣金额）\")]),_c('Checkbox',{attrs:{\"label\":0}},[_vm._v(\"付费会员价\")])],1)],1)],1):_vm._e(),(_vm.formValidate.is_sub.length)?_c('Col',{attrs:{\"span\":\"24\"}},[(_vm.formValidate.spec_type === 0)?_c('FormItem',{attrs:{\"label\":\"商品属性：\"}},[_c('Table',{attrs:{\"data\":_vm.oneFormValidate,\"columns\":_vm.columnsInstall,\"border\":\"\"},scopedSlots:_vm._u([{key:\"pic\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('div',{staticClass:\"pictrue pictrueTab\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.oneFormValidate[0].pic),expression:\"oneFormValidate[0].pic\"}]})])]}},{key:\"price\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].price))]}},{key:\"cost\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].cost))]}},{key:\"ot_price\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].ot_price))]}},{key:\"stock\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].stock))]}},{key:\"bar_code\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].bar_code))]}},{key:\"code\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].code))]}},{key:\"weight\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].weight))]}},{key:\"volume\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.oneFormValidate[0].volume))]}},{key:\"brokerage\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormValidate[0].brokerage),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"brokerage\", $$v)},expression:\"oneFormValidate[0].brokerage\"}})]}},{key:\"brokerage_two\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormValidate[0].brokerage_two),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"brokerage_two\", $$v)},expression:\"oneFormValidate[0].brokerage_two\"}})]}},{key:\"vip_price\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.oneFormValidate[0].vip_price),callback:function ($$v) {_vm.$set(_vm.oneFormValidate[0], \"vip_price\", $$v)},expression:\"oneFormValidate[0].vip_price\"}})]}}],null,false,2114426461)})],1):_vm._e(),(_vm.formValidate.spec_type === 1)?_c('FormItem',{attrs:{\"label\":\"批量设置：\"}},[(_vm.formValidate.is_sub.indexOf(1) > -1)?_c('span',[_vm._v(\"\\n            一级返佣：\"),_c('InputNumber',{staticClass:\"columnsBox perW20\",attrs:{\"placeholder\":\"请输入一级返佣\",\"min\":0},model:{value:(_vm.manyBrokerage),callback:function ($$v) {_vm.manyBrokerage=$$v},expression:\"manyBrokerage\"}}),_vm._v(\"\\n            二级返佣：\"),_c('InputNumber',{staticClass:\"columnsBox perW20\",attrs:{\"placeholder\":\"请输入二级返佣\",\"min\":0},model:{value:(_vm.manyBrokerageTwo),callback:function ($$v) {_vm.manyBrokerageTwo=$$v},expression:\"manyBrokerageTwo\"}})],1):_vm._e(),(_vm.formValidate.is_sub.indexOf(0) > -1)?_c('span',[_vm._v(\"\\n            会员价：\"),_c('InputNumber',{staticClass:\"columnsBox perW20\",attrs:{\"placeholder\":\"请输入会员价\",\"min\":0},model:{value:(_vm.manyVipPrice),callback:function ($$v) {_vm.manyVipPrice=$$v},expression:\"manyVipPrice\"}})],1):_vm._e(),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.brokerageSetUp}},[_vm._v(\"批量设置\")])],1):_vm._e(),(_vm.formValidate.spec_type === 1 && _vm.manyFormValidate.length)?_c('FormItem',{attrs:{\"label\":\"商品属性：\"}},[(_vm.formValidate.is_sub && _vm.visible)?_c('Table',{attrs:{\"data\":_vm.manyFormValidate,\"columns\":_vm.columnsInstal2,\"border\":\"\"},scopedSlots:_vm._u([_vm._l((_vm.attrData.length),function(item,i){return {key:'value' + (i + 1),fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('div',[_vm._v(_vm._s(_vm.manyFormValidate[index]['value' + (i + 1)]))])]}}}),{key:\"pic\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('div',{staticClass:\"pictrue pictrueTab\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.manyFormValidate[index].pic),expression:\"manyFormValidate[index].pic\"}]})])]}},{key:\"price\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].price))]}},{key:\"cost\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].cost))]}},{key:\"ot_price\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].ot_price))]}},{key:\"stock\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].stock))]}},{key:\"bar_code\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].bar_code))]}},{key:\"code\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].code))]}},{key:\"weight\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].weight))]}},{key:\"volume\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_vm._v(_vm._s(_vm.manyFormValidate[index].volume))]}},{key:\"brokerage\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].brokerage),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"brokerage\", $$v)},expression:\"manyFormValidate[index].brokerage\"}})]}},{key:\"brokerage_two\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].brokerage_two),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"brokerage_two\", $$v)},expression:\"manyFormValidate[index].brokerage_two\"}})]}},{key:\"vip_price\",fn:function(ref){\n            var row = ref.row;\n            var index = ref.index;\nreturn [_c('InputNumber',{staticClass:\"priceBox\",attrs:{\"min\":0},model:{value:(_vm.manyFormValidate[index].vip_price),callback:function ($$v) {_vm.$set(_vm.manyFormValidate[index], \"vip_price\", $$v)},expression:\"manyFormValidate[index].vip_price\"}})]}}],null,true)}):_vm._e()],1):_vm._e()],1):_vm._e(),(_vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('div',{staticClass:\"lines\"})]):_vm._e(),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"是否限购：\"}},[_c('i-switch',{attrs:{\"true-value\":1,\"false-value\":0,\"size\":\"large\"},on:{\"on-change\":_vm.limitTap},model:{value:(_vm.formValidate.is_limit),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_limit\", $$v)},expression:\"formValidate.is_limit\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1),(_vm.formValidate.is_limit)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"限购类型：\"}},[_c('RadioGroup',{model:{value:(_vm.formValidate.limit_type),callback:function ($$v) {_vm.$set(_vm.formValidate, \"limit_type\", $$v)},expression:\"formValidate.limit_type\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"单次限购\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"长期限购\")])],1),_c('div',{staticClass:\"tips\"},[_vm._v(\"\\n              单次限购是限制每次下单最多购买的数量，长期限购是限制一个用户总共可以购买的数量\\n            \")])],1)],1):_vm._e(),(_vm.formValidate.is_limit)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"限购数量：\"}},[_c('InputNumber',{staticClass:\"perW20 maxW\",attrs:{\"min\":1,\"placeholder\":\"请输入限购数量\"},model:{value:(_vm.formValidate.limit_num),callback:function ($$v) {_vm.$set(_vm.formValidate, \"limit_num\", $$v)},expression:\"formValidate.limit_num\"}})],1)],1):_vm._e(),(_vm.formValidate.product_type == 0 && _vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"预售商品：\"}},[_c('i-switch',{attrs:{\"true-value\":1,\"false-value\":0,\"size\":\"large\"},model:{value:(_vm.formValidate.is_presale_product),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_presale_product\", $$v)},expression:\"formValidate.is_presale_product\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1):_vm._e(),(\n          _vm.formValidate.product_type == 0 && _vm.formValidate.is_presale_product\n        )?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"预售活动时间：\",\"prop\":\"presale_time\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('DatePicker',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"editable\":false,\"options\":_vm.datePickerOptions,\"type\":\"datetimerange\",\"format\":\"yyyy-MM-dd HH:mm\",\"placeholder\":\"请选择活动时间\",\"value\":_vm.formValidate.presale_time},on:{\"on-change\":_vm.onchangeTime},model:{value:(_vm.formValidate.presale_time),callback:function ($$v) {_vm.$set(_vm.formValidate, \"presale_time\", $$v)},expression:\"formValidate.presale_time\"}})],1),_c('div',{staticClass:\"tips\"},[_vm._v(\"\\n              设置活动开启结束时间，用户可以在设置时间内发起参与预售\\n            \")])])],1):_vm._e(),(\n          _vm.formValidate.product_type == 0 && _vm.formValidate.is_presale_product\n        )?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"发货时间：\",\"prop\":\"presale_day\"}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('span',{staticClass:\"mr10\"},[_vm._v(\"预售活动结束后\")]),_c('InputNumber',{staticStyle:{\"width\":\"100px\"},attrs:{\"placeholder\":\"请输入发货时间\",\"precision\":0,\"min\":1},model:{value:(_vm.formValidate.presale_day),callback:function ($$v) {_vm.$set(_vm.formValidate, \"presale_day\", $$v)},expression:\"formValidate.presale_day\"}}),_c('span',{staticClass:\"ml10\"},[_vm._v(\"天之内\")])],1)])],1):_vm._e(),(_vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"优品推荐：\"}},[_c('i-switch',{attrs:{\"true-value\":1,\"false-value\":0,\"size\":\"large\"},model:{value:(_vm.formValidate.is_good),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_good\", $$v)},expression:\"formValidate.is_good\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1):_vm._e(),(_vm.merchantType==0)?_c('Col',_vm._b({},'Col',_vm.grid3,false),[_c('FormItem',{attrs:{\"label\":\"活动优先级：\"}},[_c('div',{staticClass:\"color-list acea-row row-middle\"},[_vm._l((_vm.formValidate.activity),function(color,index){return _c('div',{directives:[{name:\"dragging\",rawName:\"v-dragging\",value:({\n                item: color,\n                list: _vm.formValidate.activity,\n                group: 'color',\n              }),expression:\"{\\n                item: color,\\n                list: formValidate.activity,\\n                group: 'color',\\n              }\"}],key:color,staticClass:\"color-item acea-row row-center-wrapper\",class:_vm.activity[color]},[_c('div',{staticClass:\"num\"},[_vm._v(_vm._s(index + 1))]),_c('div',[_vm._v(_vm._s(color))])])}),_c('div',{staticClass:\"tips\"},[_vm._v(\"可拖动按钮调整活动的优先展示顺序\")])],2)])],1):_vm._e(),(_vm.merchantType==0)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"选择优品推荐商品：\"}},[_c('div',{staticClass:\"acea-row\"},[_vm._l((_vm.goodsData),function(item,index){return _c('div',{key:index+'goods',staticClass:\"pictrue\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(item.image),expression:\"item.image\"}]}),_c('Button',{staticClass:\"btndel\",attrs:{\"shape\":\"circle\",\"icon\":\"md-close\"},nativeOn:{\"click\":function($event){return _vm.bindDelete(index)}}})],1)}),(_vm.goodsData.length < 12)?_c('div',{staticClass:\"upLoad acea-row row-center-wrapper\",on:{\"click\":_vm.goodsTap}},[_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1):_vm._e()],2)])],1):_vm._e()],1),_c('Row',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentTab === '6'),expression:\"currentTab === '6'\"}]},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品关键字：\",\"prop\":\"\"}},[_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"placeholder\":\"请输入商品关键字\"},model:{value:(_vm.formValidate.keyword),callback:function ($$v) {_vm.$set(_vm.formValidate, \"keyword\", $$v)},expression:\"formValidate.keyword\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品简介：\",\"prop\":\"\"}},[_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入商品简介\"},model:{value:(_vm.formValidate.store_info),callback:function ($$v) {_vm.$set(_vm.formValidate, \"store_info\", $$v)},expression:\"formValidate.store_info\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品口令：\"}},[_c('Input',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入商品口令\"},model:{value:(_vm.formValidate.command_word),callback:function ($$v) {_vm.$set(_vm.formValidate, \"command_word\", $$v)},expression:\"formValidate.command_word\"}})],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品推荐图：\"}},[_c('div',{staticClass:\"pictrueBox\",on:{\"click\":function($event){return _vm.modalPicTap('dan', 'recommend_image')}}},[(_vm.formValidate.recommend_image)?_c('div',{staticClass:\"pictrue\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.formValidate.recommend_image),expression:\"formValidate.recommend_image\"}]}),_c('Input',{staticClass:\"input-display\",model:{value:(_vm.formValidate.recommend_image),callback:function ($$v) {_vm.$set(_vm.formValidate, \"recommend_image\", $$v)},expression:\"formValidate.recommend_image\"}})],1):_c('div',{staticClass:\"upLoad acea-row row-center-wrapper\"},[_c('Input',{staticClass:\"input-display\",model:{value:(_vm.formValidate.recommend_image),callback:function ($$v) {_vm.$set(_vm.formValidate, \"recommend_image\", $$v)},expression:\"formValidate.recommend_image\"}}),_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1)]),_c('div',{staticClass:\"tips\"},[_vm._v(\"(建议图片比例5:2)\")])])],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"服务保障：\"}},[_c('CheckboxGroup',{staticClass:\"checkAlls\",model:{value:(_vm.formValidate.ensure_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"ensure_id\", $$v)},expression:\"formValidate.ensure_id\"}},_vm._l((_vm.ensureData),function(item,index){return _c('Checkbox',{key:item.id,attrs:{\"label\":item.id}},[_vm._v(_vm._s(item.name))])}),1)],1)],1),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"商品参数：\",\"prop\":\"\"}},[_c('Select',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"clearable\":\"\",\"filterable\":\"\",\"placeholder\":\"请输入商品参数\"},on:{\"on-change\":_vm.specsInfo},model:{value:(_vm.formValidate.specs_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"specs_id\", $$v)},expression:\"formValidate.specs_id\"}},_vm._l((_vm.specsData),function(item,index){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.name))])}),1)],1)],1),(_vm.formValidate.specs_id)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\",\"props\":\"\"}},[_c('Table',{ref:\"table\",staticClass:\"specsList\",attrs:{\"border\":\"\",\"columns\":_vm.specsColumns,\"data\":_vm.specsList,\"width\":\"700\"},scopedSlots:_vm._u([{key:\"action\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [(index > 0)?_c('a',{on:{\"click\":function($event){return _vm.delSpecs(index)}}},[_vm._v(\"删除\")]):_vm._e()]}}],null,false,2219201132)}),_c('Button',{staticClass:\"mt20\",on:{\"click\":_vm.addSpecs}},[_vm._v(\"添加参数\")])],1)],1):_vm._e(),(_vm.formValidate.product_type)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"支持退款：\",\"props\":\"status\",\"label-for\":\"status\"}},[_c('i-switch',{attrs:{\"true-value\":1,\"false-value\":0,\"size\":\"large\"},model:{value:(_vm.formValidate.is_support_refund),callback:function ($$v) {_vm.$set(_vm.formValidate, \"is_support_refund\", $$v)},expression:\"formValidate.is_support_refund\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])])],1)],1):_vm._e(),_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"自定义留言：\"}},[_c('i-switch',{attrs:{\"size\":\"large\"},on:{\"on-change\":_vm.customMessBtn},model:{value:(_vm.customBtn),callback:function ($$v) {_vm.customBtn=$$v},expression:\"customBtn\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"开启\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"关闭\")])]),(_vm.customBtn)?_c('div',{staticClass:\"mt10\"},[_c('Select',{directives:[{name:\"width\",rawName:\"v-width\",value:('50%'),expression:\"'50%'\"}],attrs:{\"clearable\":\"\",\"filterable\":\"\",\"placeholder\":\"请选择\"},on:{\"on-change\":_vm.changeForm},model:{value:(_vm.formValidate.system_form_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"system_form_id\", $$v)},expression:\"formValidate.system_form_id\"}},_vm._l((_vm.formList),function(item,index){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.name))])}),1)],1):_vm._e()],1)],1),(_vm.customBtn && _vm.formValidate.system_form_id)?_c('Col',{attrs:{\"span\":\"24\"}},[_c('FormItem',{attrs:{\"label\":\"\",\"props\":\"\"}},[_c('Table',{ref:\"table\",staticClass:\"specsList on\",attrs:{\"border\":\"\",\"columns\":_vm.formColumns,\"data\":_vm.formTypeList},scopedSlots:_vm._u([{key:\"require\",fn:function(ref){\n              var row = ref.row;\nreturn [_c('span',[_vm._v(_vm._s(row.require?'必填':'不必填'))])]}}],null,false,469424967)})],1)],1):_vm._e()],1),_c('Row',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentTab === '8' && _vm.merchantType==0),expression:\"currentTab === '8' && merchantType==0\"}],staticClass:\"storeModule\"},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('RadioGroup',{staticClass:\"radioGroup\",model:{value:(_vm.formValidate.applicable_type),callback:function ($$v) {_vm.$set(_vm.formValidate, \"applicable_type\", $$v)},expression:\"formValidate.applicable_type\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"全部门店\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"部分门店\")]),_c('Radio',{attrs:{\"label\":0}},[_vm._v(\"仅平台适用\")])],1),_c('div',{staticClass:\"tips on\"},[_vm._v(\"可选择将商品同步到哪些门店使用，选择“仅平台适用“则商品不同步任何门店\")])],1),(_vm.formValidate.applicable_type == 2)?_c('Col',{staticClass:\"mt20 mb20\",attrs:{\"span\":\"24\"}},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.addStore}},[_vm._v(\"添加门店\")])],1):_vm._e(),_c('Col',{attrs:{\"span\":\"24\"}},[(_vm.formValidate.applicable_type == 2)?_c('div',{staticClass:\"storeTable\"},[_c('Table',{ref:\"table\",staticClass:\"ivu-mt\",attrs:{\"columns\":_vm.storeColumns,\"data\":_vm.storesList,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"image\",fn:function(ref){\n              var row = ref.row;\nreturn [_c('img',{attrs:{\"src\":row.image}})]}},{key:\"action\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.delte(index)}}},[_vm._v(\"删除\")])]}}],null,false,1468069516)})],1):_vm._e()])],1),(_vm.spinShow)?_c('Spin',{attrs:{\"size\":\"large\",\"fix\":\"\"}}):_vm._e()],1),(_vm.currentTab === '7')?_c('Table',{ref:\"table\",staticClass:\"ivu-mt\",attrs:{\"width\":\"940\",\"columns\":_vm.replyColumns,\"data\":_vm.replyData,\"loading\":_vm.replyLoading,\"no-data-text\":\"暂无数据\",\"no-filtered-data-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"info\",fn:function(ref){\n              var row = ref.row;\nreturn [_c('div',{staticClass:\"imgPic acea-row row-middle\"},[_c('viewer',[_c('div',{staticClass:\"pictrue\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.image),expression:\"row.image\"}]})])]),_c('div',{staticClass:\"info line2\"},[_vm._v(_vm._s(row.store_name))])],1)]}},{key:\"content\",fn:function(ref){\n              var row = ref.row;\nreturn [_c('div',[_vm._v(\"用户：\"+_vm._s(row.nickname))]),_c('div',[_vm._v(\"评分：\"+_vm._s(row.score))]),_c('div',[_c('div',{staticClass:\"mb5 content_font\"},[_vm._v(_vm._s(row.comment))]),_c('viewer',_vm._l((row.pics || []),function(item,index){return _c('div',{key:index+'pics',staticClass:\"pictrue mr10\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(item),expression:\"item\"}]})])}),0)],1)]}},{key:\"action\",fn:function(ref){\n              var row = ref.row;\n              var index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.seeReply(row)}}},[_vm._v(\"查看\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":function($event){return _vm.reply(row)}}},[_vm._v(\"回复\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":function($event){return _vm.delReply(row, '删除评论', index)}}},[_vm._v(\"删除\")])]}}],null,false,2900086987)}):_vm._e(),_c('div',{staticClass:\"drawer-footer\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.handleSubmit('formValidate')}}},[_vm._v(\"保存\")])],1),_c('Modal',{attrs:{\"width\":\"960px\",\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":\"上传商品图\",\"mask-closable\":false},model:{value:(_vm.modalPic),callback:function ($$v) {_vm.modalPic=$$v},expression:\"modalPic\"}},[(_vm.modalPic)?_c('uploadPictures',{attrs:{\"isChoice\":_vm.isChoice,\"gridBtn\":_vm.gridBtn,\"gridPic\":_vm.gridPic},on:{\"getPic\":_vm.getPic,\"getPicD\":_vm.getPicD}}):_vm._e()],1),_c('coupon-list',{ref:\"couponTemplates\",attrs:{\"couponids\":_vm.formValidate.coupon_ids,\"updateIds\":_vm.updateIds,\"updateName\":_vm.updateName},on:{\"nameId\":_vm.nameId}}),_c('Modal',{attrs:{\"title\":\"商品列表\",\"footerHide\":\"\",\"scrollable\":\"\",\"width\":\"900\"},on:{\"on-cancel\":_vm.goodCancel},model:{value:(_vm.goodsModals),callback:function ($$v) {_vm.goodsModals=$$v},expression:\"goodsModals\"}},[(_vm.goodsModals)?_c('goods-list',{ref:\"goodslist\",attrs:{\"ischeckbox\":true},on:{\"getProductId\":_vm.getProductId}}):_vm._e()],1),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"选择用户标签\",\"closable\":true,\"width\":\"540\",\"footer-hide\":true,\"mask-closable\":false},model:{value:(_vm.labelShow),callback:function ($$v) {_vm.labelShow=$$v},expression:\"labelShow\"}},[_c('userLabel',{ref:\"userLabel\",on:{\"activeData\":_vm.activeData,\"close\":_vm.labelClose}})],1),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"请选择商品标签\",\"closable\":true,\"width\":\"540\",\"footer-hide\":true,\"mask-closable\":false},model:{value:(_vm.storeLabelShow),callback:function ($$v) {_vm.storeLabelShow=$$v},expression:\"storeLabelShow\"}},[_c('storeLabelList',{ref:\"storeLabel\",on:{\"activeData\":_vm.activeStoreData,\"close\":_vm.storeLabelClose}})],1),_c('replyList',{ref:\"replyList\"}),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"回复内容\",\"closable\":\"\"},model:{value:(_vm.replyModal),callback:function ($$v) {_vm.replyModal=$$v},expression:\"replyModal\"}},[_c('Form',{ref:\"replyForm\",attrs:{\"model\":_vm.replyForm,\"rules\":_vm.ruleInline,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"prop\":\"content\"}},[_c('Input',{attrs:{\"type\":\"textarea\",\"rows\":4,\"placeholder\":\"请输入回复内容\"},model:{value:(_vm.replyForm.content),callback:function ($$v) {_vm.$set(_vm.replyForm, \"content\", $$v)},expression:\"replyForm.content\"}})],1)],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.oks}},[_vm._v(\"确定\")]),_c('Button',{on:{\"click\":_vm.cancels}},[_vm._v(\"取消\")])],1)],1),_c('Modal',{attrs:{\"title\":\"门店列表\",\"footerHide\":\"\",\"scrollable\":\"\",\"width\":\"900\"},on:{\"on-cancel\":_vm.cancelStore},model:{value:(_vm.storeModals),callback:function ($$v) {_vm.storeModals=$$v},expression:\"storeModals\"}},[(_vm.storeModals)?_c('store-list',{ref:\"storelist\",on:{\"getStoreId\":_vm.getStoreId}}):_vm._e()],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}