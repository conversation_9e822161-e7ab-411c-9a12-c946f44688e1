{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex';\nimport { templatesCityListApi } from '@/api/setting';\nexport default {\n  name: 'city',\n  props: {\n    type: {\n      type: Number,\n      default: 0\n    },\n    selectArr: {\n      type: Array,\n      default: []\n    }\n  },\n  data: function data() {\n    return {\n      iSselect: false,\n      addressModal: false,\n      cityList: [],\n      activeCity: -1,\n      loading: false\n    };\n  },\n  computed: _objectSpread({}, mapState('admin/layout', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  methods: {\n    enter: function enter(index) {\n      this.activeCity = index;\n    },\n    leave: function leave() {\n      this.activeCity = null;\n    },\n    getCityList: function getCityList() {\n      var _this = this;\n\n      this.loading = true;\n      templatesCityListApi().then(function (res) {\n        _this.loading = false;\n        res.data.forEach(function (el, index, arr) {\n          el.isShow = true;\n          el.children.forEach(function (child, j) {\n            child.isShow = true;\n\n            if (_this.selectArr.length > 0) {\n              _this.selectArr.forEach(function (sel, sindex) {\n                sel.children.forEach(function (sitem, sj) {\n                  if (child.city_id == sitem.city_id) {\n                    child.isShow = false;\n                  }\n                });\n              });\n            }\n          });\n        });\n        res.data.forEach(function (el, index, arr) {\n          var num = 0;\n          var oldNum = 0;\n          el.children.forEach(function (child, j) {\n            if (!child.isShow) {\n              num++;\n            } else {\n              oldNum++;\n            }\n          });\n\n          if (num == el.children.length) {\n            el.isShow = false;\n          }\n\n          el.childNum = oldNum;\n        });\n        _this.cityList = res.data;\n      });\n    },\n\n    /**\n     * 全选或者反选\n     * @param checked\n     */\n    allCheckbox: function allCheckbox() {\n      var that = this,\n          checked = this.iSselect;\n      that.cityList.forEach(function (item, key) {\n        that.$set(that.cityList[key], 'checked', checked);\n\n        if (checked) {\n          that.$set(that.cityList[key], 'count', that.cityList[key].children.length);\n        } else {\n          that.$set(that.cityList[key], 'count', 0);\n        }\n\n        that.cityList[key].children.forEach(function (val, k) {\n          that.$set(that.cityList[key].children[k], 'checked', checked);\n        });\n      }); // this.render();\n    },\n    // 清空；\n    empty: function empty() {\n      var that = this;\n      that.cityList.forEach(function (item, key) {\n        that.$set(that.cityList[key], 'checked', false);\n        that.cityList[key].children.forEach(function (val, k) {\n          that.$set(that.cityList[key].children[k], 'checked', false);\n        });\n        that.$set(that.cityList[key], 'count', 0);\n      });\n      this.iSselect = false;\n    },\n\n    /**\n     * 点击省\n     * @param index\n     */\n    checkedClick: function checkedClick(index) {\n      var that = this;\n\n      if (that.cityList[index].checked) {\n        that.$set(that.cityList[index], 'count', that.cityList[index].childNum);\n        that.cityList[index].children.forEach(function (item, key) {\n          that.$set(that.cityList[index].children[key], 'checked', true);\n        });\n      } else {\n        that.$set(that.cityList[index], 'count', 0);\n        that.$set(that.cityList[index], 'checked', false);\n        that.cityList[index].children.forEach(function (item, key) {\n          that.$set(that.cityList[index].children[key], 'checked', false);\n        });\n        that.iSselect = false;\n      } // this.render();\n\n    },\n\n    /**\n     * 点击市区\n     * @param index\n     * @param ind\n     */\n    primary: function primary(index, ind) {\n      var checked = false,\n          count = 0;\n      this.cityList[index].children.forEach(function (item, key) {\n        if (item.checked) {\n          checked = true;\n          count++;\n        }\n      });\n      this.$set(this.cityList[index], 'count', count);\n      this.$set(this.cityList[index], 'checked', checked); // this.render();\n    },\n    // 确定;\n    confirm: function confirm() {\n      var that = this; // 被选中的省市；\n\n      var selectList = [];\n      that.cityList.forEach(function (item, key) {\n        var data = {};\n\n        if (item.checked) {\n          data = {\n            name: item.name,\n            city_id: item.city_id,\n            children: []\n          };\n        }\n\n        that.cityList[key].children.forEach(function (i, k) {\n          if (i.checked) {\n            data.children.push({\n              city_id: i.city_id\n            });\n          }\n        });\n\n        if (data.city_id !== undefined) {\n          selectList.push(data);\n        }\n      });\n\n      if (selectList.length === 0) {\n        return that.$Message.error('至少选择一个省份或者城市');\n      } else {\n        this.$emit('selectCity', selectList, this.type);\n        that.addressModal = false;\n        this.cityList = [];\n      }\n    },\n    close: function close() {\n      this.addressModal = false;\n      this.cityList = [];\n    }\n  },\n  mounted: function mounted() {// this.getCityList();\n  }\n};", null]}