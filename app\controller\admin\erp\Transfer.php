<?php
/**
 * 后台调拨单管理控制器
 * User: 系统生成
 * Date: 2024-12-16 20:30:00
 */

namespace app\controller\admin\erp;

use app\controller\admin\AuthController;
use app\services\erp\TransferServices;
use think\facade\App;

/**
 * 后台调拨单管理控制器
 * Class Transfer
 * @package app\controller\admin\erp
 */
class Transfer extends AuthController
{
    /**
     * 构造方法
     * Transfer constructor.
     * @param App $app
     * @param TransferServices $services
     */
    public function __construct(App $app, TransferServices $services)
    {
        parent::__construct($app);
        $this->services = $services;
    }

    /**
     * 获取调拨单列表
     * @return mixed
     */
    public function index()
    {
        $where = $this->request->getMore([
            ['order_no', ''],
            ['status', ''],
            ['from_store_id', 0],
            ['to_store_id', 0],
            ['applicant_id', 0],
            ['start_time', ''],
            ['end_time', ''],
        ]);

        // 时间筛选
        if ($where['start_time']) {
            $where[] = ['apply_time', '>=', strtotime($where['start_time'])];
        }
        if ($where['end_time']) {
            $where[] = ['apply_time', '<=', strtotime($where['end_time'] . ' 23:59:59')];
        }
        unset($where['start_time'], $where['end_time']);

        // 调拨单号搜索
        if ($where['order_no']) {
            $where[] = ['order_no', 'like', '%' . $where['order_no'] . '%'];
        }
        unset($where['order_no']);

        $result = $this->services->getTransferList($where);

        return $this->success($result);
    }

    /**
     * 获取调拨单详情
     * @param int $id
     * @return mixed
     */
    public function detail(int $id)
    {
        if (!$id) {
            return $this->fail('缺少调拨单ID');
        }

        try {
            $info = $this->services->getTransferDetail($id);
            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 审核调拨单
     * @param int $id
     * @return mixed
     */
    public function approve(int $id)
    {
        $data = $this->request->postMore([
            ['is_approve', true],
            ['remark', ''],
        ]);

        if (!$id) {
            return $this->fail('缺少调拨单ID');
        }

        try {
            $result = $this->services->approveTransfer($id, $data['is_approve'], $data['remark'], $this->adminId);

            if ($result) {
                $msg = $data['is_approve'] ? '审核通过' : '审核拒绝';
                return $this->success([], $msg);
            } else {
                return $this->fail('审核失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量审核调拨单
     * @return mixed
     */
    public function batchApprove()
    {
        $data = $this->request->postMore([
            ['ids', []],
            ['is_approve', true],
            ['remark', ''],
        ]);

        if (empty($data['ids'])) {
            return $this->fail('请选择要审核的调拨单');
        }

        try {
            $result = $this->services->batchApprove($data['ids'], $data['is_approve'], $this->adminId);

            if ($result) {
                $msg = $data['is_approve'] ? '批量审核通过' : '批量审核拒绝';
                return $this->success([], $msg);
            } else {
                return $this->fail('批量审核失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 强制执行调拨
     * @param int $id
     * @return mixed
     */
    public function forceExecute(int $id)
    {
        if (!$id) {
            return $this->fail('缺少调拨单ID');
        }

        try {
            $result = $this->services->executeTransfer($id);

            if ($result) {
                return $this->success([], '强制执行成功');
            } else {
                return $this->fail('强制执行失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除调拨单
     * @param int $id
     * @return mixed
     */
    public function delete(int $id)
    {
        if (!$id) {
            return $this->fail('缺少调拨单ID');
        }

        try {
            $result = $this->services->dao->delete($id);

            if ($result) {
                return $this->success([], '删除成功');
            } else {
                return $this->fail('删除失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 调拨统计
     * @return mixed
     */
    public function statistics()
    {
        $where = $this->request->getMore([
            ['start_time', ''],
            ['end_time', ''],
            ['from_store_id', 0],
            ['to_store_id', 0],
        ]);

        // 时间筛选
        if ($where['start_time']) {
            $where[] = ['apply_time', '>=', strtotime($where['start_time'])];
        }
        if ($where['end_time']) {
            $where[] = ['apply_time', '<=', strtotime($where['end_time'] . ' 23:59:59')];
        }
        unset($where['start_time'], $where['end_time']);

        try {
            $stats = $this->services->getTransferStats($where);
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取门店选项列表
     * @return mixed
     */
    public function storeOptions()
    {
        try {
            $stores = app()->make(\app\dao\store\SystemStoreDao::class)
                ->getList(['is_del' => 0, 'is_show' => 1], ['id', 'name', 'phone', 'address']);

            $options = array_map(function($store) {
                return [
                    'value' => $store['id'],
                    'label' => $store['name']
                ];
            }, $stores);

            return app('json')->success($options);
        } catch (\Exception $e) {
            return app('json')->fail($e->getMessage());
        }
    }
}