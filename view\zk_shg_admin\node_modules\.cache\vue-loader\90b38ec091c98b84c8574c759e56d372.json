{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue?vue&type=style&index=0&id=745b9304&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderDetails.vue", "mtime": 1698031562000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    .order_box .section .item .txtVal{\n      max-width 100px;\n    }\n    .order_box .head .full .ivu-btn.on{\n      color: #c5c8ce!important;\n      background-color: #f7f7f7!important;\n      border-color: #dcdee2!important;\n    }\n    .ivu-description-list-title {\n        margin-bottom: 16px;\n        color: #17233d;\n        font-weight: 500;\n        font-size: 14px;\n    }\n\t.logisticsLook{\n\t\tfont-size 13px;\n\t\tmargin-left 10px;\n\t\tcolor #1890FF;\n\t\tcursor pointer;\n\t}\n\t.value{\n\t\tword-break:break-all\n\t}\n\t/deep/.ivu-icon-ios-more {\n\t\tfont-size 20px;\n\t}\n    .logistics\n        align-items: center\n        padding: 10px 0px\n        .logistics_img\n            width 45px\n            height 45px\n            margin-right: 12px\n            img\n             width 100%\n             height 100%\n        .logistics_cent\n            span\n              display block\n              font-size 12px\n    .trees-coadd\n        width: 100%;\n        height: 400px;\n        border-radius: 4px;\n        overflow: hidden;\n        .scollhide\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            margin-left: 18px;\n            padding: 10px 0 10px 0;\n            box-sizing: border-box;\n            .content\n              font-size 12px\n            .time\n              font-size 12px\n              color: #2d8cf0\n.order_box2\n   position absolute\n   z-index 999999999\n.order_box >>> .ivu-modal-header\n   padding 30x 16px !important\n.order_box >>> .ivu-card\n    font-size 12px !important\n.fontColor1 >>> .ivu-description-term\n    color red !important\n.fontColor1 >>> .ivu-description-detail\n    color red !important\n    padding-bottom 14px !important\n.fontColor2 >>> .ivu-description-detail\n    color #733AF9 !important\n.order_box >>> .ivu-description-term\n    padding-bottom 10px !important\n.order_box >>> .ivu-description-detail\n    padding-bottom 10px !important\n.order_box >>> .ivu-modal-body\n    padding: 0 !important\n.fontColor3 >>> .ivu-description-term\n    color #f1a417 !important\n.fontColor3 >>> .ivu-description-detail\n    color #f1a417 !important\n\n.tabBoxPic\n    width 50px\n    height 50px\n    display inline-block\n    vertical-align top\n    margin-right 6px\n.tabBox_img\n    width 100%\n    height 100%\n    border-radius:4px\n    cursor pointer\n    img\n       width 100%\n       height 100%\n       padding 2px\n\n>>> .order_box\n\n    .head\n        padding 30px 35px 25px\n\n        .full\n            display flex\n\n            .iconfont\n                color #1890FF\n\n                &.sale-after {\n                    color #90ADD5\n                }\n\n            .text\n                align-self center\n                flex 1\n                min-width 0\n                padding-left 12px\n                border 0\n                font-size 13px\n                line-height 13px\n                color #606266\n\n                .title\n                    margin-bottom 10px\n                    font-weight 500\n                    font-size 16px\n                    line-height 16px\n                    color rgba(0, 0, 0, 0.85)\n\n            .ivu-btn\n                margin-left 12px\n\n                &:first-child\n                    display inline-block\n                    border-color #1890FF\n                    margin-left 0\n                    background-color #1890FF\n                    color #FFFFFF\n                \n                &:nth-child(2)\n                    display inline-block\n                    border-color #19BE6B\n                    background-color #19BE6B\n                    color #FFFFFF\n\n                &:nth-child(3)\n                    display inline-block\n\n                &:focus\n                    box-shadow none\n            \n            .ivu-dropdown\n                margin-left 12px\n\n                &:nth-child(n+5)\n                    display inline-block\n\n                .ivu-btn\n                    border-color #DCDEE2\n                    background-color #FFFFFF\n                    color #515A6E\n\n        .list\n            display flex\n            margin-top 20px\n            overflow hidden\n            list-style none\n\n            .item\n                flex none\n                width 200px\n                font-size 14px\n                line-height 14px\n                color rgba(0, 0, 0, 0.85)\n\n                .title\n                    margin-bottom 12px\n                    font-size 13px\n                    line-height 13px\n                    color #666666\n\n                .value1\n                    color #F56022\n\n                .value2\n                    color #1BBE6B\n\n                .value3\n                    color #1890FF\n\n                .value4\n                    color #6A7B9D\n\n                .value5\n                    color #F5222D\n\n    .section\n        padding 25px 0\n        border-bottom 1px dashed #EEEEEE\n\n        .title\n            padding-left 10px\n            border-left 3px solid #1890FF\n            font-size 15px\n            line-height 15px\n            color #303133\n\n        .list\n            display flex\n            flex-wrap wrap\n            list-style none\n\n        .item\n            flex 0 0 calc(100% / 3)\n            display flex\n            margin-top 16px\n            font-size 13px\n\t\t\tcolor #606266\n\n            &:nth-child(3n+1)\n                padding-right 20px\n\n            &:nth-child(3n+2)\n                padding-right 10px\n                padding-left 10px\n\n            &:nth-child(3n+3)\n                padding-left 20px\n\n        .value\n            flex 1\n\n            .image\n                display inline-block\n                width 40px\n                height 40px\n                margin 0 12px 12px 0\n                vertical-align middle\n\n            img\n                width 100%\n                height 100%\n\n    .product\n        display flex\n\n        .image\n            width 50px\n            height 50px\n\n        img\n            width 100%\n            height 100%\n            border-radius 4px\n        \n        .title\n            flex 1\n            padding-left 13px\n            text-align left\n\n>>> .ivu-tabs\n    color rgba(0, 0, 0, 0.85)\n\n    .ivu-tabs-bar\n        border-bottom 0\n        margin-bottom 0\n        background-color #F5F7FA\n\n    .ivu-tabs-nav-container\n        font-size 13px\n\n    .ivu-tabs-nav-wrap\n        margin-bottom 0\n\n    .ivu-tabs-ink-bar\n        display none\n\n    .ivu-tabs-tab\n        padding 7px 19px\n        margin-right 0\n        line-height 26px\n\n    .ivu-tabs-tab-active\n        background-color #FFFFFF\n        color rgba(0, 0, 0, 0.85)\n\n        &::before\n            content \"\"\n            position absolute\n            top 0\n            left 0\n            width 100%\n            height 2px\n            background-color #1890FF\n\n    .ivu-tabs-tabpane\n        padding 25px 35px\n\n        &:first-child\n            padding 0 35px\n\n>>> .ivu-table\n\n\t.ivu-table-header\n\n\t\ttable\n\t\t\tborder-top 0 !important\n\n\t\tth\n\t\t\tbackground-color #F7F7F7 !important\n", null]}