{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  combinationListApi,\n  combinationSetStatusApi,\n  storeCombination<PERSON>pi,\n} from \"@/api/marketing\";\nimport { mapState } from \"vuex\";\nimport { formatDate } from \"@/utils/validate\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"index\",\n  filters: {\n    formatDate(time) {\n      if (time !== 0) {\n        let date = new Date(time * 1000);\n        return formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    },\n    filterStatus(val) {\n      let nowTime = Date.parse(new Date()) / 1000;\n      if (\n        val.start_time < nowTime &&\n        val.stop_time > nowTime &&\n        val.is_show == 1\n      ) {\n        return \"活动进行中\";\n      } else if (val.stop_time < nowTime && val.status == 1) {\n        return \"活动已结束\";\n      } else {\n        return \"活动未开始\";\n      }\n    },\n  },\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      grid: {\n        xl: 7,\n        lg: 7,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      formValidate: {\n        start_status: \"\",\n        is_show: \"\",\n        store_name: \"\",\n        page: 1,\n        limit: 15,\n      },\n      value: \"\",\n      columns1: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          width: 80,\n        },\n        {\n          title: \"拼团图片\",\n          slot: \"image\",\n          minWidth: 90,\n        },\n        {\n          title: \"拼团名称\",\n          slot: \"title\",\n          minWidth: 150,\n        },\n        {\n          title: \"日常售价\",\n          key: \"ot_price\",\n          minWidth: 100,\n        },\n        {\n          title: \"拼团价\",\n          key: \"price\",\n          minWidth: 120,\n        },\n        // {\n        //     title: '库存',\n        //     key: 'stock',\n        //     minWidth: 100\n        // },\n        {\n          title: \"拼团人数\",\n          key: \"count_people\",\n          minWidth: 80,\n        },\n        // {\n        //     title: '访问人数',\n        //     key: 'count_people_browse',\n        //     minWidth: 100\n        // },\n        // {\n        //     title: '展现量',\n        //     key: 'browse',\n        //     minWidth: 150\n        // },\n        {\n          title: \"参与人数\",\n          key: \"count_people_all\",\n          minWidth: 80,\n        },\n        {\n          title: \"成团数量\",\n          key: \"count_people_pink\",\n          minWidth: 80,\n        },\n        // {\n        //     title: '浏览量',\n        //     key: 'browse',\n        //     minWidth: 150\n        // },\n        {\n          title: \"限量\",\n          key: \"quota_show\",\n          minWidth: 80,\n        },\n        {\n          title: \"限量剩余\",\n          key: \"quota\",\n          minWidth: 100,\n        },\n        {\n          title: \"活动状态\",\n          slot: \"start_name\",\n          minWidth: 100,\n        },\n        {\n          title: \"结束时间\",\n          slot: \"stop_time\",\n          minWidth: 150,\n        },\n        {\n          title: \"状态\",\n          slot: \"is_show\",\n          minWidth: 120,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n          width: 180,\n        },\n      ],\n      tableList: [],\n      total: 0,\n      statisticsList: [],\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 导出\n    // exports () {\n    //     let formValidate = this.formValidate;\n    //     let data = {\n    //         start_status: formValidate.start_status,\n    //         is_show: formValidate.is_show,\n    //         store_name: formValidate.store_name\n    //     };\n    //     storeCombinationApi(data).then(res => {\n    //         location.href = res.data[0];\n    //     }).catch(res => {\n    //         this.$Message.error(res.msg)\n    //     })\n    // },\n    // 数据导出；\n    async exports() {\n      let [th, filekey, data, fileName] = [[], [], [], \"\"];\n      //   let fileName = \"\";\n      let excelData = JSON.parse(JSON.stringify(this.formValidate));\n      excelData.page = 1;\n      for (let i = 0; i < excelData.page + 1; i++) {\n        let lebData = await this.getExcelData(excelData);\n        if (!fileName) fileName = lebData.filename;\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) th = lebData.header;\n        if (lebData.export.length) {\n          data = data.concat(lebData.export);\n          excelData.page++;\n        } else {\n          exportExcel(th, filekey, fileName, data);\n          return;\n        }\n      }\n    },\n    getExcelData(excelData) {\n      return new Promise((resolve, reject) => {\n        storeCombinationApi(excelData).then((res) => {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 添加\n    add() {\n      this.$router.push({ path: this.roterPre + \"/marketing/store_combination/create\" });\n    },\n    // 编辑\n    edit(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_combination/create/\" + row.id + \"/0\",\n      });\n    },\n    // 一键复制\n    copy(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_combination/create/\" + row.id + \"/1\",\n      });\n    },\n    // 删除\n    del(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `marketing/combination/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n        .then((res) => {\n          this.$Message.success(res.msg);\n          this.tableList.splice(num, 1);\n          if (!this.tableList.length) {\n            this.formValidate.page =\n                this.formValidate.page == 1 ? 1 : this.formValidate.page - 1;\n          }\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    viewInfo(row) {\n      this.$router.push({\n        path: this.roterPre+'/marketing/store_combination/statistics/' + row.id,\n      });\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      combinationListApi(this.formValidate)\n        .then(async (res) => {\n          let data = res.data;\n          this.tableList = data.list;\n          this.total = res.data.count;\n          this.loading = false;\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    // 修改是否显示\n    onchangeIsShow(row) {\n      let data = {\n        id: row.id,\n        status: row.is_show,\n      };\n      combinationSetStatusApi(data)\n        .then(async (res) => {\n          this.$Message.success(res.msg);\n          this.getList();\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n  },\n};\n", null]}