{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\button\\button.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\button\\button.vue", "mtime": 1725352512383}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n\nimport Icon from '../icon';\nimport { oneOf } from '../../utils/assist';\nimport mixinsLink from '../../mixins/link';\n\nconst prefixCls = 'ivu-btn';\n\nexport default {\n    name: 'Button',\n    mixins: [ mixinsLink ],\n    components: { Icon },\n    props: {\n        type: {\n            validator (value) {\n                return oneOf(value, ['default', 'primary', 'dashed', 'text', 'info', 'success', 'warning', 'error']);\n            },\n            default: 'default'\n        },\n        shape: {\n            validator (value) {\n                return oneOf(value, ['circle', 'circle-outline']);\n            }\n        },\n        size: {\n            validator (value) {\n                return oneOf(value, ['small', 'large', 'default']);\n            },\n            default () {\n                return !this.$IVIEW || this.$IVIEW.size === '' ? 'default' : this.$IVIEW.size;\n            }\n        },\n        loading: Boolean,\n        disabled: Boolean,\n        htmlType: {\n            default: 'button',\n            validator (value) {\n                return oneOf(value, ['button', 'submit', 'reset']);\n            }\n        },\n        icon: {\n            type: String,\n            default: ''\n        },\n        customIcon: {\n            type: String,\n            default: ''\n        },\n        long: {\n            type: Boolean,\n            default: false\n        },\n        ghost: {\n            type: <PERSON>olean,\n            default: false\n        }\n    },\n    data () {\n        return {\n            showSlot: true\n        };\n    },\n    computed: {\n        classes () {\n            return [\n                `${prefixCls}`,\n                `${prefixCls}-${this.type}`,\n                {\n                    [`${prefixCls}-long`]: this.long,\n                    [`${prefixCls}-${this.shape}`]: !!this.shape,\n                    [`${prefixCls}-${this.size}`]: this.size !== 'default',\n                    [`${prefixCls}-loading`]: this.loading != null && this.loading,\n                    [`${prefixCls}-icon-only`]: !this.showSlot && (!!this.icon || !!this.customIcon || this.loading),\n                    [`${prefixCls}-ghost`]: this.ghost\n                }\n            ];\n        },\n        // Point out if it should render as <a> tag\n        isHrefPattern() {\n            const {to} = this;\n            return !!to;\n        },\n        tagName() {\n            const {isHrefPattern} = this;\n            return isHrefPattern ? 'a' : 'button';\n        },\n        tagProps() {\n            const {isHrefPattern} = this;\n            if(isHrefPattern) {\n                const {linkUrl,target}=this;\n                return {href: linkUrl, target};\n            } else {\n                const {htmlType} = this;\n                return {type: htmlType};\n            }\n        }\n    },\n    methods: {\n        // Ctrl or CMD and click, open in new window when use `to`\n        handleClickLink (event) {\n            this.$emit('click', event);\n            const openInNewWindow = event.ctrlKey || event.metaKey;\n\n            this.handleCheckClick(event, openInNewWindow);\n        }\n    },\n    mounted () {\n        this.showSlot = this.$slots.default !== undefined;\n    }\n};\n", null]}