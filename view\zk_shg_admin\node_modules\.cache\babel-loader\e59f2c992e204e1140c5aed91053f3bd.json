{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\clientMomentInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientMoment\\clientMomentInfo.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport cardsData from \"@/components/cards/cards\";\nimport { workMomentInfo, workGroupTemplateSendMsg, workMomentList } from \"@/api/work\";\nimport { formatDate as _formatDate } from '@/utils/validate';\nimport Setting from \"@/setting\";\nexport default {\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      cardLists: [],\n      optionData: {},\n      style: {\n        height: \"400px\"\n      },\n      spinShow: false,\n      tableColumn: [{\n        title: \"员工\",\n        key: \"name\",\n        minWidth: 100\n      }, {\n        title: \"发表状态\",\n        slot: \"status\",\n        minWidth: 100\n      }, {\n        title: \"发送时间\",\n        slot: \"create_time\",\n        minWidth: 100\n      }, {\n        title: \"已送达客户\",\n        slot: \"external_user_list\",\n        minWidth: 150\n      } // {\n      //   title: \"操作\",\n      //   slot: \"action\",\n      //   minWidth: 100,\n      // },\n      ],\n      tabIndex: 0,\n      tableData: [],\n      userLoading: false,\n      timeVal: [],\n      tableForm: {\n        page: 1,\n        limit: 15,\n        status: \"\",\n        moment_id: \"\",\n        userid: \"\"\n      }\n    };\n  },\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  components: {\n    cardsData: cardsData\n  },\n  mounted: function mounted() {\n    this.getData(); // this.getMemberList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n\n      this.userLoading = true;\n      workMomentList(this.tableForm).then(function (res) {\n        _this.tableData = res.data;\n        _this.userLoading = false;\n      }).catch(function (err) {\n        _this.userLoading = false;\n\n        _this.$Message.error(err.msg);\n      });\n    },\n    getData: function getData() {\n      var _this2 = this;\n\n      workMomentInfo(this.$route.params.id).then(function (res) {\n        _this2.tableForm.moment_id = res.data.moment_id;\n\n        if (!!res.data.moment_id) {\n          _this2.getList();\n        }\n\n        _this2.cardLists = [{\n          col: 6,\n          count: res.data.info.externalUserCount,\n          name: \"已送达客户\",\n          type: 1,\n          className: \"iconjinrixinzeng\"\n        }, {\n          col: 6,\n          count: res.data.info.userCount,\n          name: \"全部成员\",\n          type: 1,\n          className: \"iconjinrituiqun\"\n        }, {\n          col: 6,\n          count: res.data.info.unSendUserCount,\n          name: \"未发送成员\",\n          type: 1,\n          className: \"icondangqianqunchengyuan\"\n        }, {\n          col: 6,\n          count: res.data.info.sendUserCount,\n          name: \"已发送成员\",\n          type: 1,\n          className: \"iconleijituiqun\"\n        }];\n      });\n    },\n    sendMsg: function sendMsg(row, index) {\n      var _this3 = this;\n\n      workGroupTemplateSendMsg({\n        userid: row.msg_id,\n        time: row.create_time,\n        id: \"\"\n      }).then(function (res) {\n        _this3.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    search: function search() {\n      this.tableForm.page = 1;\n      this.getList();\n    },\n    pageChange: function pageChange(index) {\n      this.tableForm.page = index;\n      this.getList();\n    }\n  }\n};", null]}