{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRemark.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRemark.vue", "mtime": 1644455204000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { integralOrderPutRemarkData } from '@/api/marketing';\nexport default {\n    name: 'orderMark',\n    data () {\n        return {\n            formValidate: {\n                remark: ''\n            },\n            modals: false,\n            ruleValidate: {\n                remark: [\n                    { required: true, message: '请输入备注信息', trigger: 'blur' }\n                ]\n            }\n        }\n    },\n    props: {\n        orderId: Number\n    },\n    methods: {\n        cancel (name) {\n            this.modals = false;\n            this.$refs[name].resetFields();\n        },\n        putRemark (name) {\n            let data = {\n                id: this.orderId,\n                remark: this.formValidate\n            }\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                    integralOrderPutRemarkData(data).then(async res => {\n                        this.$Message.success(res.msg);\n                        this.modals = false;\n                        this.$refs[name].resetFields();\n                        this.$emit('submitFail')\n                    }).catch(res => {\n                        this.$Message.error(res.msg);\n                    })\n                } else {\n                    this.$Message.warning('请填写备注信息');\n                }\n            })\n        }\n    }\n}\n", null]}