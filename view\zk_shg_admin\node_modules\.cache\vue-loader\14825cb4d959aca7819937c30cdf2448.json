{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\clientGroupInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\clientGroup\\clientGroupInfo.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport cardsData from \"@/components/cards/cards\";\nimport { workGroupTemplateInfo,groupTemplateMemberList,groupTemplateClientList,workGroupTemplateSendMsg } from \"@/api/work\";\n import { formatDate } from '@/utils/validate';\n import Setting from \"@/setting\";\nexport default {\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      cardLists: [],\n      optionData: {},\n      style: { height: \"400px\" },\n      spinShow: false,\n      tableColumn: [\n        {\n          title: \"成员\",\n          key: \"name\",\n          minWidth: 100,\n        },\n        {\n          title: \"发送状态\",\n          slot: \"status\",\n          minWidth: 100,\n        },\n        {\n          title: \"发送数目\",\n          slot: \"sendResult\",\n          minWidth: 100,\n        },\n        {\n          title: \"发送时间\",\n          slot: \"send_time\",\n          minWidth: 100,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          minWidth: 100,\n        },\n      ],\n      tableColumn1:[\n        {\n          title: \"客户\",\n          key: \"name\",\n          minWidth: 100,\n        },\n        {\n          title: \"发送状态\",\n          slot: \"status1\",\n          minWidth: 100,\n        },\n        {\n          title: \"发送时间\",\n          slot: \"send_time\",\n          minWidth: 100,\n        },\n      ],\n      tabIndex:0,\n      tableData: [],\n      userLoading: false,\n      timeVal: [],\n      tableForm: {\n        page: 1,\n        limit: 15,\n        client_name:\"\", //客户名称\n        user_name:\"\", //成员名称\n        status:\"\",\n        status1:\"\",\n        id: 0,\n      },\n      \n    };\n  },\n  filters: {\n      formatDate (time) {\n          if (time !== 0) {\n              let date = new Date(time * 1000);\n              return formatDate(date, 'yyyy-MM-dd hh:mm');\n          }\n      }\n  },\n  components: { cardsData },\n  mounted() {\n    this.tableForm.id = this.$route.params.id;\n    this.getData();\n    this.getMemberList();\n    // this.getList();\n  },\n  methods: {\n    getList() {\n      this.userLoading = true;\n      workGroupStatisticsList(this.tableForm)\n        .then((res) => {\n          this.tableData = res.data;\n          this.userLoading = false;\n        })\n        .catch((err) => {\n          this.userLoading = false;\n          this.$Message.error(err.msg);\n        });\n    },\n    getData() {\n      workGroupTemplateInfo(this.$route.params.id).then((res) => {\n        this.cardLists = [\n          {\n            col: 6,\n            count: res.data.user_count,\n            name: \"已发送成员\",\n            type:1,\n            className: \"iconjinrixinzeng\",\n          },\n          {\n            col: 6,\n            count: res.data.external_user_count,\n            name: \"送达客户\",\n            type:1,\n            className: \"iconjinrituiqun\",\n          },\n          {\n            col: 6,\n            count: res.data.unuser_count,\n            name: \"未发送成员\",\n            type:1,\n            className: \"icondangqianqunchengyuan\",\n          },\n          {\n            col: 6,\n            count: res.data.external_unuser_count,\n            name: \"未送达客户\",\n            type:1,\n            className: \"iconleijituiqun\",\n          },\n        ];\n        \n      });\n    },\n    getMemberList(){\n      groupTemplateMemberList(this.tableForm.id,{\n        user_name:this.tableForm.user_name,\n        status:this.tableForm.status,\n        page:this.tableForm.page,\n        limit:this.tableForm.limit\n      }).then(res=>{\n        this.tableData = res.data;\n      })\n    },\n    getClientList(){\n      groupTemplateClientList(this.tableForm.id,{\n        client_name:this.tableForm.user_name,\n        status:this.tableForm.status1,\n        page:this.tableForm.page,\n        limit:this.tableForm.limit\n      }).then(res=>{\n        this.tableData = res.data;\n      })\n    },\n    sendMsg(row,index){\n      workGroupTemplateSendMsg({\n\t\t\t\t\tuserid:row.userid,\n\t\t\t\t\ttime:row.create_time,\n\t\t\t\t\tid:\"\"\n\t\t\t\t}).then(res=>{\n\t\t\t\t\tthis.$Message.success(res.msg)\n\t\t\t\t}).catch(err=>{\n\t\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t\t})\n    },\n    onChangeType(){\n      if(this.tabIndex == 0){\n        this.getMemberList();\n      }else{\n        this.getClientList();\n      }\n    },\n    search(){\n      if(this.tabIndex == 0){\n        this.getMemberList();\n      }else{\n        this.getClientList();\n      }\n      \n    },\n    pageChange(index) {\n      this.tableForm.page = index;\n      if(this.tabIndex == 0){\n        this.getMemberList();\n      }else{\n        this.getClientList();\n      }\n    },\n  },\n};\n", null]}