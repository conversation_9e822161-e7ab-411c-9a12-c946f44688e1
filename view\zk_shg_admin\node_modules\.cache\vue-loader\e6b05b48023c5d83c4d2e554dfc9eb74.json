{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab.vue?vue&type=template&id=16687342&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_tab.vue", "mtime": 1659407670000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"margin-bottom\":\"20px\"}},[(_vm.configData.tabList)?_c('div',{staticClass:\"title-tips\"},[_c('span',[_vm._v(_vm._s(_vm.configData.title))]),_vm._v(_vm._s(_vm.configData.tabList[_vm.configData.tabVal].name)+\"\\n    \")]):_vm._e(),_c('div',{staticClass:\"radio-box\",class:{on:_vm.configData.type == 1}},[_c('RadioGroup',{attrs:{\"type\":\"button\",\"size\":\"large\"},on:{\"on-change\":function($event){return _vm.radioChange($event)}},model:{value:(_vm.configData.tabVal),callback:function ($$v) {_vm.$set(_vm.configData, \"tabVal\", $$v)},expression:\"configData.tabVal\"}},_vm._l((_vm.configData.tabList),function(item,index){return _c('Radio',{key:index,attrs:{\"label\":index}},[(item.icon)?_c('span',{staticClass:\"iconfont-diy\",class:item.icon}):_c('span',[_vm._v(_vm._s(item.name))])])}),1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}