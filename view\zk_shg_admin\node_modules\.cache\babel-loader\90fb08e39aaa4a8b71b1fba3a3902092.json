{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_pink.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_pink.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nexport default {\n  name: 'c_home_pink',\n  cname: '拼团',\n  componentsName: 'home_pink',\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  watch: {\n    num: function num(nVal) {\n      // debugger;\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          this.getRComContent(arr, this.type, this.type2, this.type3);\n        } else {\n          console.log('kkkk', this.type5);\n          this.getRComStyle(arr, this.type, this.type2, this.type3, this.type5, this.type4);\n        }\n      },\n      deep: true\n    },\n    'configObj.styleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp == 0) {\n          this.getRComContent(arr, nVal, this.type2, this.type3);\n        } else {\n          console.log('kkkk02', this.type5);\n          this.getRComStyle(arr, nVal, this.type2, this.type3, this.type5, this.type4);\n        }\n      },\n      deep: true\n    },\n    'configObj.titleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type2 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp == 0) {\n          this.getRComContent(arr, this.type, nVal, this.type3);\n        } else {\n          console.log('kkkk03', this.type5);\n          this.getRComStyle(arr, this.type, nVal, this.type3, this.type5, this.type4);\n        }\n      },\n      deep: true\n    },\n    'configObj.goodStyleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type3 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp == 0) {\n          this.getRComContent(arr, this.type, this.type2, nVal);\n        } else {\n          console.log('kkkk04', this.type5);\n          this.getRComStyle(arr, this.type, this.type2, nVal, this.type5, this.type4);\n        }\n      },\n      deep: true\n    },\n    'configObj.pinkConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type5 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          console.log('kkkk05', this.type5);\n          this.getRComStyle(arr, this.type, this.type2, this.type3, nVal, this.type4);\n        }\n      },\n      deep: true\n    },\n    'configObj.toneConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type4 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          this.getRComStyle(arr, this.type, this.type2, this.type3, this.type5, nVal);\n        }\n      },\n      deep: true\n    }\n  },\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      oneContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleLeft'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'styleConfig'\n      }],\n      oneContentImg: [{\n        components: toolCom.c_upload_img,\n        configNme: 'imgBgConfig'\n      }],\n      twoContent: [{\n        components: toolCom.c_radio,\n        configNme: 'titleConfig'\n      }],\n      twoContentImg: [{\n        components: toolCom.c_upload_img,\n        configNme: 'imgConfig'\n      }],\n      twoContentColorImg: [{\n        components: toolCom.c_upload_img,\n        configNme: 'imgColorConfig'\n      }],\n      twoContentText: [{\n        components: toolCom.c_input_item,\n        configNme: 'titleTxtConfig'\n      }],\n      threeContent: [{\n        components: toolCom.c_input_item,\n        configNme: 'rightBntConfig'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleGoodsList'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'goodStyleConfig'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleGoods'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'numberConfig'\n      }, {\n        components: toolCom.c_checkbox,\n        configNme: 'checkboxInfo'\n      }],\n      fourContent: [{\n        components: toolCom.c_radio,\n        configNme: 'pinkConfig'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }],\n      twoStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'headerBgColor'\n      }],\n      threeStyle: [{\n        components: toolCom.c_radio,\n        configNme: 'titleText'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'titleColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'titleNumber'\n      }],\n      fourStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'headerBntColor'\n      }],\n      fourStyle2: [{\n        components: toolCom.c_bg_color,\n        configNme: 'headerBntColor2'\n      }],\n      fourBntStyle: [{\n        components: toolCom.c_slider,\n        configNme: 'bntNumber'\n      }],\n      fourColorStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'tipsColor'\n      }],\n      fourColorStyle2: [{\n        components: toolCom.c_bg_color,\n        configNme: 'tipsColor2'\n      }],\n      fourGoodsStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'dividerColor'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleGoodsStyle'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'filletImg'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'goodsName'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'goodsNameColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'goodsPriceColor'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneConfig'\n      }],\n      fiveStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'pinkPriceColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'labelColor'\n      }],\n      bntStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'goodsBntColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'goodsBntTxtColor'\n      }],\n      currencyStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'moduleColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'mbConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      setUp: 0,\n      type: 0,\n      type2: 0,\n      type3: 0,\n      type4: 0,\n      type5: 0\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    getRComContent: function getRComContent(arr, type, type2, type3) {\n      if (type == 0) {\n        if (type2 == 0) {\n          if (type3 == 2 || type3 == 3) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentColorImg), _toConsumableArray(this.threeContent));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentColorImg), _toConsumableArray(this.threeContent), _toConsumableArray(this.fourContent));\n          }\n        } else {\n          if (type3 == 2 || type3 == 3) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentText), _toConsumableArray(this.threeContent));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentText), _toConsumableArray(this.threeContent), _toConsumableArray(this.fourContent));\n          }\n        }\n      } else {\n        if (type2 == 0) {\n          if (type3 == 2 || type3 == 3) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentImg), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentImg), _toConsumableArray(this.threeContent));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentImg), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentImg), _toConsumableArray(this.threeContent), _toConsumableArray(this.fourContent));\n          }\n        } else {\n          if (type3 == 2 || type3 == 3) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentImg), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentText), _toConsumableArray(this.threeContent));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContentImg), _toConsumableArray(this.twoContent), _toConsumableArray(this.twoContentText), _toConsumableArray(this.threeContent), _toConsumableArray(this.fourContent));\n          }\n        }\n      }\n    },\n    getRComStyle: function getRComStyle(arr, type, type2, type3, type5, type4) {\n      var obj = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.currencyStyle));\n      var obj2 = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.currencyStyle));\n      var obj3 = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.currencyStyle));\n      var obj4 = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.currencyStyle));\n\n      if (type == 0) {\n        if (type2 == 0) {\n          if (type3 == 0 || type3 == 1) {\n            if (type5 == 0) {\n              if (type4 == 0) {\n                this.rCom = obj;\n              } else {\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.bntStyle), _toConsumableArray(this.currencyStyle));\n              }\n            } else {\n              if (type4 == 0) {\n                this.rCom = obj;\n              } else {\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n              }\n            }\n          } else {\n            if (type4 == 0) {\n              this.rCom = obj;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n            }\n          }\n        } else {\n          if (type3 == 0 || type3 == 1) {\n            if (type5 == 0) {\n              if (type4 == 0) {\n                this.rCom = obj2;\n              } else {\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.bntStyle), _toConsumableArray(this.currencyStyle));\n              }\n            } else {\n              if (type4 == 0) {\n                this.rCom = obj2;\n              } else {\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n              }\n            }\n          } else {\n            if (type4 == 0) {\n              this.rCom = obj2;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.twoStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle2), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle2), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n            }\n          }\n        }\n      } else {\n        console.log('55555555');\n\n        if (type2 == 0) {\n          console.log('6666');\n\n          if (type3 == 0 || type3 == 1) {\n            console.log('777', type5);\n\n            if (type5 == 0) {\n              console.log('888');\n\n              if (type4 == 0) {\n                this.rCom = obj3;\n              } else {\n                console.log('999');\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.bntStyle), _toConsumableArray(this.currencyStyle));\n              }\n            } else {\n              console.log('444');\n\n              if (type4 == 0) {\n                this.rCom = obj3;\n              } else {\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n              }\n            }\n          } else {\n            if (type4 == 0) {\n              this.rCom = obj3;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n            }\n          }\n        } else {\n          if (type3 == 0 || type3 == 1) {\n            if (type5 == 0) {\n              if (type4 == 0) {\n                this.rCom = obj4;\n              } else {\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.bntStyle), _toConsumableArray(this.currencyStyle));\n              }\n            } else {\n              if (type4 == 0) {\n                this.rCom = obj4;\n              } else {\n                this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n              }\n            }\n          } else {\n            if (type4 == 0) {\n              this.rCom = obj4;\n            } else {\n              this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(this.threeStyle), _toConsumableArray(this.fourStyle), _toConsumableArray(this.fourBntStyle), _toConsumableArray(this.fourColorStyle), _toConsumableArray(this.fourGoodsStyle), _toConsumableArray(this.fiveStyle), _toConsumableArray(this.currencyStyle));\n            }\n          }\n        }\n      }\n    },\n    getConfig: function getConfig(data) {}\n  }\n};", null]}