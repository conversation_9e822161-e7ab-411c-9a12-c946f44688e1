{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\add_template.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\add_template.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState, mapMutations } from \"vuex\";\nimport uploadPictures from \"@/components/uploadPictures\";\n// import department from \"@/components/department/index.vue\";\nimport { workGroupChat, workGroupTemplateChatSave } from \"@/api/work\";\nimport timeOptions from \"@/utils/timeOptions\";\nimport { getNewFormBuildRuleApi } from \"@/api/setting\";\nimport Setting from \"@/setting\";\nexport default {\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      userLoading: false,\n      groupStatus:false,\n      formItem: {\n        template_type: \"0\", //0=立即发送，1=定时发送\n        name: \"\",\n        type: \"1\", //客户群发\n        client_type: \"0\", //0=全部客户，1=所选客户\n        where_time: \"\", //客户筛选时间\n        where_label: [], //标签\n        ownerInfo: [], //所选群发账号\n        send_time: \"\", //定时发送账号\n        welcome_words: {\n          text: {\n            content: \"\",\n          },\n          attachments: [],\n        }, \n      },\n      ruleValidate:{\n        name: [\n          { required: true, message: '群发名称不能为空', trigger: 'blur' }\n        ],\n        template_type:[\n          { required: true, message: '请选择发送类型', trigger: 'change' }\n        ]\n      },\n      options: timeOptions,\n      timeVal: [],\n      //客户标签列表\n      labelList: [],\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      rontineObj: {\n        msgtype: \"miniprogram\",\n        miniprogram: {\n          pic_url: \"\",\n          pic_media_id: \"\",\n          title: \"\",\n          appid: \"\",\n          page: \"\",\n        },\n      },\n      imageObj: {\n        msgtype: \"image\",\n        image: {\n          media_id: \"\",\n          pic_url: \"\",\n        },\n      },\n      groupColumn:[\n        {\n          type: \"selection\",\n          width: 60,\n          align: \"center\",\n        },\n        {\n          title: \"群名称\",\n          key: \"name\",\n          minWidth: 80,\n          align: 'center'\n        },\n        {\n          title: \"群主\",\n          slot: \"ownerInfo\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"群公告\",\n          slot: \"notice\",\n          minWidth: 100,\n          align: 'center'\n        },\n        {\n          title: \"管理员\",\n          slot: \"admin_user_list\",\n          minWidth: 80,\n          align: 'center'\n        },\n        {\n          title: \"创建时间\",\n          key: \"group_create_time\",\n          minWidth: 110,\n          align: 'center'\n        },\n        {\n          title: \"群人数\",\n          key: \"member_num\",\n          minWidth: 80,\n          align: 'center'\n        },\n        {\n          title: \"退群人数\",\n          key: \"retreat_group_num\",\n          minWidth: 80,\n          align: 'center'\n        },\n      ],\n      groupData:[],\n      groupForm:{\n        page:1,\n        limit:15\n      },\n      picTit: \"\",\n      modalPic: false,\n      modalRoutine: false,\n      isChoice: \"单选\",\n      activeDepartment: {},\n      isSite: true,\n      onlyDepartment: false,\n      openType: \"\",\n      userList: [],\n    };\n  },\n  components: { uploadPictures },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 80;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"left\";\n    },\n  },\n  mounted() {\n    this.setCopyrightShow({ value: false });\n    this.getWorkGroupChat();\n  },\n  destroyed() {\n    this.setCopyrightShow({ value: true });\n  },\n  methods: {\n    ...mapMutations(\"admin/layout\", [\"setCopyrightShow\"]),\n    onchangeTime(e) {\n      this.timeVal = e;\n      this.formItem.where_time = this.timeVal.join(\"-\");\n    },\n    snedChangeTime(val) {\n      this.formItem.send_time = val;\n    },\n    modalPicTap(picTit) {\n      this.modalPic = true;\n      this.picTit = picTit;\n    },\n    selectAll(row) {\n      if (row.length) {\n        this.selectGroup = row; \n      }\n    },\n    handleSelectRow(row){\n      this.selectGroup = row; \n    },\n    groupConfirm(){\n      // this.formItem.userids = this.selectGroup.map(item=>{\n      //   return {\n      //     userid:item.ownerInfo.userid,\n      //     name:item.ownerInfo.name\n      //   }\n      // })\n      this.formItem.ownerInfo = this.selectGroup.map(item=>{\n          return {\n            userid:item.ownerInfo.userid,\n            name:item.ownerInfo.name\n          }\n        })\n    },\n    getWorkGroupChat(){\n      this.userLoading = true;\n      workGroupChat(this.groupForm).then(res=>{\n        this.groupData = res.data;\n        this.userLoading = false;\n      }).catch(err=>{\n        this.$Message.error(err.msg)\n        this.userLoading = false;\n      })\n    },\n    mapTree(org) {\n      const haveChildren =\n        Array.isArray(org.children) && org.children.length > 0;\n      return {\n        //分别将我们查询出来的值做出改变他的key\n        title: org.label,\n        expand: true,\n        value: org.value,\n        selected: false,\n        checked: false,\n        children: haveChildren ? org.children.map((i) => this.mapTree(i)) : [],\n      };\n    },\n    addRoutine() {\n      getNewFormBuildRuleApi('routine').then(res => {\n          let data = res.data;\n          this.rontineObj.miniprogram.pic_url = '';\n          this.rontineObj.miniprogram.title = data.routine_name.value;\n          this.rontineObj.miniprogram.appid = data.routine_appId.value;\n          this.rontineObj.miniprogram.page = '/pages/index/index';\n      })\n      this.modalRoutine = true;\n    },\n    addUser() {\n      this.groupStatus = true;\n    },\n    //tag标签删除成员\n    handleDel(e,name) {\n      let index = this.formItem.ownerInfo.indexOf(name)\n      this.formItem.ownerInfo.splice(index, 1);\n    },\n    //群发内容tag删除\n    wordsDel(name) {\n      let index = this.formItem.welcome_words.attachments.indexOf(name);\n      this.formItem.welcome_words.attachments.splice(index, 1);\n    },\n    groupChange(index){\n      this.groupForm.page =index;\n      this.getWorkGroupChat();\n    },\n    // 选中图片\n    getPic(pc) {\n      switch (this.picTit) {\n        case \"image\":\n          this.imageObj.image.pic_url = pc.att_dir;\n          this.formItem.welcome_words.attachments.push(this.imageObj);\n          break;\n        case \"routine\":\n          this.rontineObj.miniprogram.pic_url = pc.att_dir;\n          break;\n      }\n      this.modalPic = false;\n    },\n    // insertName() {\n    //   this.formItem.welcome_words.text.content =\n    //     this.formItem.welcome_words.text.content.concat(\"##客户名称##\");\n    // },\n    routineConfirm() {\n      const routine = this.deepClone(this.rontineObj);\n      this.formItem.welcome_words.attachments.push(routine);\n    },\n    submit() {\n      const formData = this.deepClone(this.formItem);\n      formData.userids = formData.ownerInfo.map((item) => {\n        return item.userid;\n      });\n      this.$refs.formItem.validate((valid) => {\n          if (valid) {\n            workGroupTemplateChatSave(formData).then((res) => {\n              this.$Message.success(res.msg);\n              this.$router.push(this.roterPre + \"/work/group/template\");\n            })\n            .catch((err) => {\n              this.$Message.error(err.msg);\n            });\n          }\n        })\n    },\n    //分页\n    ownerChange(index){\n      this.clientForm.page = index;\n      this.getownerList();\n    },\n    //深克隆\n    deepClone(obj) {\n      let newObj = Array.isArray(obj) ? [] : {};\n      if (obj && typeof obj === \"object\") {\n        for (let key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            newObj[key] =\n              obj && typeof obj[key] === \"object\"\n                ? this.deepClone(obj[key])\n                : obj[key];\n          }\n        }\n      }\n      return newObj;\n    },\n  },\n};\n", null]}