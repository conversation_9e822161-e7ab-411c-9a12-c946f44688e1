{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue?vue&type=template&id=4c7f6892&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_product.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"c_product\"},[(_vm.configData.title)?_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.configData.title))]):_vm._e(),_c('div',{staticClass:\"list-box\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.configData.list,\"group\":\"peoples\",\"handle\":\".move-icon\"}},_vm._l((_vm.configData.list),function(item,index){return _c('div',{key:index,staticClass:\"item\",on:{\"click\":function($event){return _vm.activeBtn(index)}},model:{value:(_vm.configData.tabCur),callback:function ($$v) {_vm.$set(_vm.configData, \"tabCur\", $$v)},expression:\"configData.tabCur\"}},[_c('div',{staticClass:\"move-icon\"},[_c('span',{staticClass:\"iconfont-diy iconxingzhuangjiehe\"})]),_c('div',{staticClass:\"content\"},[_vm._l((item.chiild),function(list,key){return _c('div',{key:key,staticClass:\"con-item\"},[_c('span',[_vm._v(_vm._s(list.title))]),_c('div',{staticStyle:{\"width\":\"100%\"},on:{\"click\":function($event){return _vm.getLink(index,key,item)}}},[_c('Input',{attrs:{\"icon\":key && !item.link?'ios-arrow-forward':'',\"show-word-limit\":!key?true:false,\"readonly\":key && !item.link?true:false,\"placeholder\":list.pla,\"maxlength\":list.max},model:{value:(list.val),callback:function ($$v) {_vm.$set(list, \"val\", $$v)},expression:\"list.val\"}})],1)])}),(_vm.configData.type)?_c('div',{staticClass:\"con-item\"},[_c('span',[_vm._v(\"状态\")]),_c('i-switch',{model:{value:(item.show),callback:function ($$v) {_vm.$set(item, \"show\", $$v)},expression:\"item.show\"}})],1):_vm._e(),(item.link)?_c('div',{staticClass:\"con-item\"},[_c('span',[_vm._v(_vm._s(item.link.title))]),_c('Select',{on:{\"on-change\":function($event){return _vm.sliderChange(index)}},model:{value:(item.link.activeVal),callback:function ($$v) {_vm.$set(item.link, \"activeVal\", $$v)},expression:\"item.link.activeVal\"}},_vm._l((item.link.optiops),function(item,j){return _c('Option',{key:j,attrs:{\"value\":item.value}},[_vm._v(_vm._s(item.label)+\"\\n                                \")])}),1)],1):_vm._e()],2),_c('div',{staticClass:\"delete\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete(index)}}},[_c('Icon',{attrs:{\"type\":\"ios-close-circle\",\"size\":\"26\"}})],1)])}),0)],1),(_vm.configData.list)?_c('div',[(_vm.configData.list.length < _vm.configData.max)?_c('div',{staticClass:\"add-btn\",on:{\"click\":_vm.addHotTxt}},[_c('Button',{staticClass:\"btn\",attrs:{\"type\":\"primary\",\"ghost\":\"\"}},[_c('span',{staticClass:\"iconfont iconjiahao\"}),_vm._v(\"添加\\n\\t\\t\\t\\t\")])],1):_vm._e()]):_vm._e(),_c('linkaddress',{ref:\"linkaddres\",on:{\"linkUrl\":_vm.linkUrl}})],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}