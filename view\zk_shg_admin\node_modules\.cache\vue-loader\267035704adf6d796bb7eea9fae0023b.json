{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\combinaList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeCombination\\combinaList.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n    import cardsData from '@/components/cards/cards';\n    import { mapState } from 'vuex';\n    import { formatDate } from '@/utils/validate';\n    import { combineListApi, orderPinkListApi, statisticsApi } from '@/api/marketing';\n    import timeOptions from \"@/utils/timeOptions\";\n    export default {\n        name: 'combinalist',\n        filters: {\n            formatDate (time) {\n                if (time !== 0) {\n                    let date = new Date(time * 1000);\n                    return formatDate(date, 'yyyy-MM-dd hh:mm');\n                }\n            }\n        },\n        components: { cardsData },\n        data () {\n            return {\n                cardLists: [],\n                modals: false,\n                options: timeOptions,\n                grid: {\n                    xl: 7,\n                    lg: 10,\n                    md: 12,\n                    sm: 12,\n                    xs: 24\n                },\n                loading: false,\n                formValidate: {\n                    status: '',\n                    data: '',\n                    page: 1,\n                    limit: 15\n                },\n                columns1: [\n                    {\n                        title: '头像',\n                        slot: 'avatar',\n                        minWidth: 150\n                    },\n                    {\n                        title: '开团团长',\n                        slot: 'nickname',\n                        minWidth: 170\n                    },\n                    {\n                        title: '开团时间',\n                        slot: 'add_time',\n                        minWidth: 150\n                    },\n                    {\n                        title: '拼团商品',\n                        slot: 'title',\n                        minWidth: 400\n                    },\n                    {\n                        title: '几人团',\n                        key: 'people',\n                        minWidth: 120\n                    },\n                    {\n                        title: '几人参加',\n                        key: 'count_people',\n                        minWidth: 100\n                    },\n                    {\n                        title: '结束时间',\n                        slot: 'stop_time',\n                        minWidth: 150\n                    },\n                    {\n                        title: '状态',\n                        slot: 'status',\n                        minWidth: 100\n                    },\n                    {\n                        title: '操作',\n                        slot: 'action',\n                        fixed: 'right',\n                        minWidth: 170\n                    }\n                ],\n                tableList: [],\n                total: 0,\n                timeVal: [],\n                loading2: false,\n                tabList3: [],\n                columns2: [\n                    {\n                        title: 'ID',\n                        key: 'id',\n                        width: 80\n                    },\n                    {\n                        title: '用户名称',\n                        slot: 'nickname',\n                        minWidth: 150\n                    },\n                    {\n                        title: '用户头像',\n                        slot: 'avatar'\n                    },\n                    {\n                        title: '订单编号',\n                        key: 'order_id'\n                    },\n                    {\n                        title: '金额',\n                        key: 'price'\n                    },\n                    {\n                        title: '订单状态',\n                        slot: 'action'\n                    }\n                ],\n                rows: {}\n            }\n        },\n        computed: {\n            ...mapState('admin/layout', [\n                'isMobile'\n            ]),\n            labelWidth () {\n                return this.isMobile ? undefined : 96;\n            },\n            labelPosition () {\n                return this.isMobile ? 'top' : 'right';\n            }\n        },\n        created () {\n            this.getList();\n            this.getStatistics();\n        },\n        methods: {\n            // 拼团统计\n            getStatistics () {\n                statisticsApi().then(async res => {\n                    let data = res.data\n                    this.cardLists = data.res;\n                }).catch(res => {\n                    this.$Message.error(res.msg);\n                })\n            },\n            // 查看详情\n            Info (row) {\n                this.modals = true;\n                this.rows = row;\n                orderPinkListApi(row.id).then(async res => {\n                    let data = res.data\n                    this.tabList3 = data.list;\n                    this.loading = false;\n                }).catch(res => {\n                    this.loading = false;\n                    this.$Message.error(res.msg);\n                })\n            },\n            // 具体日期\n            onchangeTime (e) {\n                this.timeVal = e\n                if(this.timeVal[0]===''){\n                    this.formValidate.data = '';\n                }else {\n                    this.formValidate.data = this.timeVal.join('-');\n                }\n                this.formValidate.page = 1;\n\t\t\t\tthis.getList();\n            },\n            // 选择时间\n            selectChange (tab) {\n                this.formValidate.page = 1;\n                this.formValidate.data = tab;\n                this.timeVal = [];\n                this.getList();\n            },\n            // 列表\n            getList () {\n                this.loading = true;\n                this.formValidate.status = this.formValidate.status || '';\n                combineListApi(this.formValidate).then(async res => {\n                    let data = res.data\n                    this.tableList = data.list;\n                    this.total = res.data.count;\n                    this.loading = false;\n                }).catch(res => {\n                    this.loading = false;\n                    this.$Message.error(res.msg);\n                })\n            },\n            pageChange (index) {\n                this.formValidate.page = index;\n                this.getList();\n            },\n            // 表格搜索\n            userSearchs () {\n                this.formValidate.page = 1;\n                this.getList();\n            }\n        }\n    }\n", null]}