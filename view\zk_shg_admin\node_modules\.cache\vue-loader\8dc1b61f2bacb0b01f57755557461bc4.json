{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {\n  crawlFromApi,\n  treeListApi,\n  crawlSaveApi,\n  productGetTemplateApi,\n  copyConfigApi,\n} from \"@/api/product\";\nimport { isLoginApi } from \"@/api/setting\";\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport Setting from '@/setting';\nexport default {\n  name: \"taoBao\",\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      // 批量设置表格data\n      oneFormBatch: [\n        {\n          pic: \"\",\n          price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: \"\",\n          weight: 0,\n          volume: 0,\n        },\n      ],\n      columnsBatch: [\n        {\n          title: \"图片\",\n          slot: \"pic\",\n          align: \"center\",\n          minWidth: 80,\n        },\n        {\n          title: \"售价\",\n          slot: \"price\",\n          align: \"center\",\n          minWidth: 95,\n        },\n        {\n          title: \"成本价\",\n          slot: \"cost\",\n          align: \"center\",\n          minWidth: 95,\n        },\n        {\n          title: \"原价\",\n          slot: \"ot_price\",\n          align: \"center\",\n          minWidth: 95,\n        },\n        {\n          title: \"库存\",\n          slot: \"stock\",\n          align: \"center\",\n          minWidth: 95,\n        },\n        {\n          title: \"商品编号\",\n          slot: \"bar_code\",\n          align: \"center\",\n          minWidth: 120,\n        },\n        {\n          title: \"重量（KG）\",\n          slot: \"weight\",\n          align: \"center\",\n          minWidth: 95,\n        },\n        {\n          title: \"体积(m³)\",\n          slot: \"volume\",\n          align: \"center\",\n          minWidth: 95,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: 'right',\n          align: \"center\",\n          minWidth: 140,\n        },\n      ],\n      modal_loading: false,\n      images: \"\",\n      soure_link: \"\",\n      modalPic: false,\n      isChoice: \"\",\n      spinShow: false,\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12,\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8,\n      },\n      copyConfig: {\n        copy_type: 2,\n        copy_num: 0,\n      },\n      columns: [],\n      treeSelect: [],\n      ruleInline: {\n        cate_id: [\n          {\n            required: true,\n            message: \"请选择商品分类\",\n            trigger: \"change\",\n            type: \"array\",\n            min: \"1\",\n          },\n        ],\n        temp_id: [\n          {\n            required: true,\n            message: \"请选择运费模板\",\n            trigger: \"change\",\n            type: \"number\",\n          },\n        ],\n      },\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      grid2: {\n        xl: 12,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      formValidate: {\n        store_name: \"\",\n        cate_id: [],\n        temp_id: \"\",\n        keyword: \"\",\n        unit_name: \"\",\n        store_info: \"\",\n        image: \"\",\n        slider_image: [],\n        description: \"\",\n        ficti: 0,\n        give_integral: 0,\n        is_show: 0,\n        price: 0,\n        cost: 0,\n        ot_price: 0,\n        stock: 0,\n        soure_link: \"\",\n        description_images: \"\",\n        postage: 0,\n        attrs: [],\n        items: [],\n      },\n      items: [\n        {\n          pic: \"\",\n          price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: \"\",\n          weight: 0,\n          volume: 0,\n        },\n      ],\n      templateList: [],\n      isData: false,\n      artFrom: {\n        type: \"taobao\",\n        url: \"\",\n      },\n      tableIndex: 0,\n    };\n  },\n  components: { WangEditor, uploadPictures },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.goodsCategory();\n  },\n  mounted() {\n    this.productGetTemplate();\n    this.getCopyConfig();\n  },\n  methods: {\n\t  getEditorContent(data) {\n\t    this.formValidate.description = data;\n\t  },\n    mealPay(val) {\n      isLoginApi()\n        .then(async (res) => {\n          let data = res.data;\n          if (!data.status) {\n            this.$Message.warning(\"请先登录\");\n            this.$router.push(this.roterPre + \"/setting/sms/sms_config/index\");\n          } else {\n            this.$router.push({\n              path: this.roterPre + \"/setting/sms/sms_pay/index\",\n              query: { type: val },\n            });\n          }\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    getCopyConfig() {\n      copyConfigApi().then((res) => {\n        this.copyConfig.copy_type = res.data.copy_type;\n        this.copyConfig.copy_num = res.data.copy_num;\n      });\n    },\n    batchDel() {\n      this.oneFormBatch = [\n        {\n          pic: \"\",\n          price: 0,\n          cost: 0,\n          ot_price: 0,\n          stock: 0,\n          bar_code: \"\",\n          weight: 0,\n          volume: 0,\n        },\n      ];\n    },\n    batchAdd() {\n      let formBatch = this.oneFormBatch[0];\n      this.$set(\n        this.formValidate,\n        \"attrs\",\n        this.formValidate.attrs.map((item) => {\n          if (formBatch.pic) {\n            item.pic = formBatch.pic;\n          }\n          if (formBatch.price > 0) {\n            item.price = formBatch.price;\n          }\n          if (formBatch.cost > 0) {\n            item.cost = formBatch.cost;\n          }\n          if (formBatch.ot_price > 0) {\n            item.ot_price = formBatch.ot_price;\n          }\n          if (formBatch.stock > 0) {\n            item.stock = formBatch.stock;\n          }\n          if (formBatch.bar_code) {\n            item.bar_code = formBatch.bar_code;\n          }\n          if (formBatch.weight) {\n            item.weight = formBatch.weight;\n          }\n          if (formBatch.volume) {\n            item.volume = formBatch.volume;\n          }\n          return item;\n        })\n      );\n    },\n    // 删除表格中的属性\n    delAttrTable(index) {\n      this.items.splice(index, 1);\n    },\n    // 获取运费模板；\n    productGetTemplate() {\n      productGetTemplateApi().then((res) => {\n        this.templateList = res.data;\n      });\n    },\n    // 删除图片\n    handleRemove(i) {\n      this.formValidate.slider_image.splice(i, 1);\n    },\n    // 选择主图\n    checked(item, index) {\n      this.formValidate.image = item;\n    },\n    // 商品分类；\n    goodsCategory() {\n      treeListApi(1)\n        .then((res) => {\n          this.treeSelect = res.data;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    // 生成表单\n    add() {\n      if (this.soure_link) {\n        var reg = /(http|ftp|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/;\n        if (!reg.test(this.soure_link)) {\n          return this.$Message.warning(\"请输入以http开头的地址！\");\n        }\n        this.spinShow = true;\n        this.artFrom.url = this.soure_link;\n        crawlFromApi(this.artFrom)\n          .then((res) => {\n            let info = res.data.info;\n            this.columns = info.info.header;\n            this.formValidate = info;\n            this.formValidate.soure_link = this.soure_link;\n            this.formValidate.attrs = info.info.value;\n            if (this.formValidate.image) {\n              this.oneFormBatch[0].pic = this.formValidate.image;\n            }\n            this.items = this.formValidate.attrs;\n            this.isData = true;\n            this.spinShow = false;\n          })\n          .catch((res) => {\n            this.spinShow = false;\n            this.$Message.error(res.msg);\n          });\n      } else {\n        this.$Message.warning(\"请输入链接地址！\");\n      }\n    },\n    // 提交\n    handleSubmit(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.modal_loading = true;\n          // this.formValidate.attrs = [\n          //     {\n          //         pic: this.images,\n          //         price: this.formValidate.price,\n          //         cost: this.formValidate.cost,\n          //         ot_price: this.formValidate.ot_price,\n          //         stock: this.formValidate.stock,\n          //         bar_code: this.formValidate.bar_code,\n          //         weight: this.formValidate.weight,\n          //         volume: this.formValidate.volume\n          //     }\n          // ];\n          // this.formValidate.items = [];\n          crawlSaveApi(this.formValidate)\n            .then((res) => {\n              this.$Message.success(\"商品默认为不上架状态请手动上架商品!\");\n              setTimeout(() => {\n                this.modal_loading = false;\n              }, 500);\n              setTimeout(() => {\n                this.$emit(\"on-close\");\n              }, 600);\n            })\n            .catch((res) => {\n              this.modal_loading = false;\n              this.$Message.error(res.msg);\n            });\n        } else {\n          if (!this.formValidate.cate_id) {\n            this.$Message.warning(\"请填写商品分类！\");\n          }\n        }\n      });\n    },\n    // 点击商品图\n    modalPicTap(tit, index) {\n      this.modalPic = true;\n      this.isChoice = tit === \"dan\" ? \"单选\" : \"多选\";\n      this.tableIndex = index;\n    },\n    // 获取单张图片信息\n    getPic(pc) {\n      if (this.tableIndex === \"duopi\") {\n        this.oneFormBatch[0].pic = pc.att_dir;\n      } else {\n        this.formValidate.attrs[this.tableIndex].pic = pc.att_dir;\n      }\n      this.modalPic = false;\n    },\n    handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    // 首先把div变成可以放置的元素，即重写dragenter/dragover\n    handleDragOver(e) {\n      // e.dataTransfer.dropEffect=\"move\";//在dragenter中针对放置目标来设置!\n      e.dataTransfer.dropEffect = \"move\";\n    },\n    handleDragEnter(e, item) {\n      // 为需要移动的元素设置dragstart事件\n      e.dataTransfer.effectAllowed = \"move\";\n      if (item === this.dragging) {\n        return;\n      }\n      const newItems = [...this.formValidate.slider_image];\n      const src = newItems.indexOf(this.dragging);\n      const dst = newItems.indexOf(item);\n      newItems.splice(dst, 0, ...newItems.splice(src, 1));\n      this.formValidate.slider_image = newItems;\n    },\n    // 添加自定义弹窗\n    addCustomDialog(editorId) {\n      window.UE.registerUI(\n        \"test-dialog\",\n        function (editor, uiName) {\n          let dialog = new window.UE.ui.Dialog({\n            iframeUrl: \"/admin/widget.images/index.html?fodder=dialog\",\n            editor: editor,\n            name: uiName,\n            title: \"上传图片\",\n            cssRules: \"width:1200px;height:500px;padding:20px;\",\n          });\n          this.dialog = dialog;\n          var btn = new window.UE.ui.Button({\n            name: \"dialog-button\",\n            title: \"上传图片\",\n            cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,\n            onclick: function () {\n              dialog.render();\n              dialog.open();\n            },\n          });\n          return btn;\n        },\n        37\n      );\n    },\n  },\n};\n", null]}