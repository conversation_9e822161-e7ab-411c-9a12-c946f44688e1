{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\storeLabelList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\storeLabelList\\index.vue", "mtime": 1693985518000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { productStoreLabel } from '@/api/product';\nexport default {\n  name: 'storeLabelList',\n  props: {},\n  data: function data() {\n    return {\n      labelList: [],\n      dataLabel: [],\n      isStore: false\n    };\n  },\n  mounted: function mounted() {},\n  methods: {\n    inArray: function inArray(search, array) {\n      for (var i in array) {\n        if (array[i].id === search) {\n          return true;\n        }\n      }\n\n      return false;\n    },\n    // 用户标签\n    storeLabel: function storeLabel(data) {\n      var _this = this;\n\n      this.dataLabel = data;\n      productStoreLabel().then(function (res) {\n        res.data.map(function (el) {\n          if (el.children && el.children.length) {\n            _this.isStore = true;\n            el.children.map(function (label) {\n              if (_this.inArray(label.id, _this.dataLabel)) {\n                label.disabled = true;\n              } else {\n                label.disabled = false;\n              }\n            });\n          }\n        });\n        _this.labelList = res.data;\n      }).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    selectLabel: function selectLabel(label) {\n      if (label.disabled) {\n        var index = this.dataLabel.indexOf(this.dataLabel.filter(function (d) {\n          return d.id === label.id;\n        })[0]);\n        this.dataLabel.splice(index, 1);\n        label.disabled = false;\n      } else {\n        this.dataLabel.push({\n          'label_name': label.label_name,\n          'id': label.id\n        });\n        label.disabled = true;\n      }\n    },\n    // 确定\n    subBtn: function subBtn() {\n      this.$emit('activeData', JSON.parse(JSON.stringify(this.dataLabel)));\n    },\n    cancel: function cancel() {\n      this.$emit('close');\n    }\n  }\n};", null]}