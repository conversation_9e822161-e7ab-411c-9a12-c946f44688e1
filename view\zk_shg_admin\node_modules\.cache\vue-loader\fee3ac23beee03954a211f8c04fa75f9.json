{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue?vue&type=template&id=1d6e0732&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\report\\index.vue", "mtime": 1750985338988}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"report-container\">\n  <!-- 页面头部 -->\n  <div class=\"page-header\">\n    <div class=\"header-left\">\n      <h2 class=\"page-title\">数据报表</h2>\n      <p class=\"page-desc\">ERP系统数据分析与报表展示</p>\n    </div>\n    <div class=\"header-right\">\n      <el-date-picker\n        v-model=\"dateRange\"\n        type=\"monthrange\"\n        range-separator=\"至\"\n        start-placeholder=\"开始月份\"\n        end-placeholder=\"结束月份\"\n        @change=\"handleDateChange\"\n        style=\"margin-right: 12px\">\n      </el-date-picker>\n      <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"refreshData\">刷新数据</el-button>\n      <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportReport\">导出报表</el-button>\n    </div>\n  </div>\n\n  <!-- 概览卡片 -->\n  <div class=\"overview-cards\">\n    <div class=\"overview-card\">\n      <div class=\"card-icon inventory\">\n        <i class=\"el-icon-goods\"></i>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"card-title\">库存总值</div>\n        <div class=\"card-number\">¥{{ formatMoney(overview.inventory_value) }}</div>\n        <div class=\"card-change\" :class=\"overview.inventory_change >= 0 ? 'positive' : 'negative'\">\n          <i :class=\"overview.inventory_change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n          {{ Math.abs(overview.inventory_change) }}%\n        </div>\n      </div>\n    </div>\n\n    <div class=\"overview-card\">\n      <div class=\"card-icon transfer\">\n        <i class=\"el-icon-sort\"></i>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"card-title\">调拨单数</div>\n        <div class=\"card-number\">{{ overview.transfer_count }}</div>\n        <div class=\"card-change\" :class=\"overview.transfer_change >= 0 ? 'positive' : 'negative'\">\n          <i :class=\"overview.transfer_change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n          {{ Math.abs(overview.transfer_change) }}%\n        </div>\n      </div>\n    </div>\n\n    <div class=\"overview-card\">\n      <div class=\"card-icon warning\">\n        <i class=\"el-icon-warning\"></i>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"card-title\">预警商品</div>\n        <div class=\"card-number\">{{ overview.warning_count }}</div>\n        <div class=\"card-desc\">需要关注</div>\n      </div>\n    </div>\n\n    <div class=\"overview-card\">\n      <div class=\"card-icon efficiency\">\n        <i class=\"el-icon-data-analysis\"></i>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"card-title\">调拨效率</div>\n        <div class=\"card-number\">{{ overview.efficiency }}%</div>\n        <div class=\"card-desc\">平均处理时间</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 图表区域 -->\n  <div class=\"charts-container\">\n    <!-- 库存趋势图 -->\n    <div class=\"chart-card\">\n      <div class=\"chart-header\">\n        <h3>库存变动趋势</h3>\n        <div class=\"chart-controls\">\n          <el-radio-group v-model=\"inventoryPeriod\" size=\"small\" @change=\"loadInventoryTrend\">\n            <el-radio-button label=\"week\">近7天</el-radio-button>\n            <el-radio-button label=\"month\">近30天</el-radio-button>\n            <el-radio-button label=\"quarter\">近3个月</el-radio-button>\n          </el-radio-group>\n        </div>\n      </div>\n      <div class=\"chart-content\">\n        <div ref=\"inventoryChart\" style=\"width: 100%; height: 300px;\"></div>\n      </div>\n    </div>\n\n    <!-- 调拨统计图 -->\n    <div class=\"chart-card\">\n      <div class=\"chart-header\">\n        <h3>调拨统计分析</h3>\n        <div class=\"chart-controls\">\n          <el-select v-model=\"transferType\" size=\"small\" @change=\"loadTransferStats\" style=\"width: 120px\">\n            <el-option label=\"按状态\" value=\"status\"></el-option>\n            <el-option label=\"按门店\" value=\"store\"></el-option>\n            <el-option label=\"按时间\" value=\"time\"></el-option>\n          </el-select>\n        </div>\n      </div>\n      <div class=\"chart-content\">\n        <div ref=\"transferChart\" style=\"width: 100%; height: 300px;\"></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 详细报表 -->\n  <div class=\"report-tables\">\n    <!-- 门店库存报表 -->\n    <div class=\"table-card\">\n      <div class=\"table-header\">\n        <h3>门店库存报表</h3>\n        <el-button type=\"text\" size=\"small\" @click=\"exportStoreReport\">导出</el-button>\n      </div>\n      <el-table :data=\"storeReport\" v-loading=\"storeLoading\" stripe>\n        <el-table-column prop=\"store_name\" label=\"门店名称\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"total_products\" label=\"商品总数\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"total_stock\" label=\"库存总量\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"total_value\" label=\"库存总值\" width=\"120\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span class=\"money\">¥{{ formatMoney(scope.row.total_value) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"warning_count\" label=\"预警商品\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.warning_count > 0 ? 'warning-text' : ''\">\n              {{ scope.row.warning_count }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"out_stock_count\" label=\"缺货商品\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.out_stock_count > 0 ? 'danger-text' : ''\">\n              {{ scope.row.out_stock_count }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"库存状态\" width=\"120\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-progress\n              :percentage=\"getStockHealth(scope.row)\"\n              :color=\"getProgressColor(scope.row)\"\n              :stroke-width=\"8\"\n              :show-text=\"false\">\n            </el-progress>\n            <span class=\"progress-text\">{{ getStockHealth(scope.row) }}%</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"viewStoreDetail(scope.row)\">查看详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 调拨效率报表 -->\n    <div class=\"table-card\">\n      <div class=\"table-header\">\n        <h3>调拨效率报表</h3>\n        <el-button type=\"text\" size=\"small\" @click=\"exportTransferReport\">导出</el-button>\n      </div>\n      <el-table :data=\"transferReport\" v-loading=\"transferLoading\" stripe>\n        <el-table-column prop=\"date\" label=\"日期\" width=\"120\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"total_orders\" label=\"调拨单数\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"approved_orders\" label=\"已审核\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"completed_orders\" label=\"已完成\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column label=\"完成率\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :class=\"getEfficiencyClass(scope.row)\">\n              {{ getCompletionRate(scope.row) }}%\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"avg_process_time\" label=\"平均处理时间\" width=\"120\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.avg_process_time }}小时\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"total_quantity\" label=\"调拨总量\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getEfficiencyTagType(scope.row)\" size=\"small\">\n              {{ getEfficiencyText(scope.row) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n  </div>\n\n  <!-- 商品分析 -->\n  <div class=\"analysis-section\">\n    <div class=\"section-header\">\n      <h3>商品分析</h3>\n      <el-button type=\"text\" size=\"small\" @click=\"refreshProductAnalysis\">刷新</el-button>\n    </div>\n\n    <div class=\"analysis-cards\">\n      <div class=\"analysis-card\">\n        <div class=\"card-header\">\n          <h4>热门调拨商品 TOP10</h4>\n        </div>\n        <div class=\"product-list\">\n          <div\n            v-for=\"(product, index) in hotProducts\"\n            :key=\"product.id\"\n            class=\"product-item\">\n            <div class=\"product-rank\">{{ index + 1 }}</div>\n            <div class=\"product-info\">\n              <div class=\"product-name\">{{ product.name }}</div>\n              <div class=\"product-stats\">调拨次数: {{ product.transfer_count }}</div>\n            </div>\n            <div class=\"product-trend\">\n              <i :class=\"product.trend === 'up' ? 'el-icon-arrow-up trend-up' : 'el-icon-arrow-down trend-down'\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"analysis-card\">\n        <div class=\"card-header\">\n          <h4>库存预警分析</h4>\n        </div>\n        <div class=\"warning-stats\">\n          <div class=\"warning-item\">\n            <div class=\"warning-label\">严重缺货</div>\n            <div class=\"warning-value danger\">{{ warningStats.severe }}</div>\n          </div>\n          <div class=\"warning-item\">\n            <div class=\"warning-label\">库存偏低</div>\n            <div class=\"warning-value warning\">{{ warningStats.low }}</div>\n          </div>\n          <div class=\"warning-item\">\n            <div class=\"warning-label\">库存正常</div>\n            <div class=\"warning-value success\">{{ warningStats.normal }}</div>\n          </div>\n          <div class=\"warning-item\">\n            <div class=\"warning-label\">库存充足</div>\n            <div class=\"warning-value info\">{{ warningStats.abundant }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", null]}