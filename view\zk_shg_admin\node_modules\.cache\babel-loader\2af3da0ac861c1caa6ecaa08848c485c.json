{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\modal\\mixins-scrollbar.js", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview\\src\\components\\modal\\mixins-scrollbar.js", "mtime": 1725352513358}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}], "contextDependencies": [], "result": ["// used for Modal & $Spin & Drawer\nimport { getScrollBarSize } from '../../utils/assist';\nexport default {\n  methods: {\n    checkScrollBar: function checkScrollBar() {\n      var fullWindowWidth = window.innerWidth;\n\n      if (!fullWindowWidth) {\n        // workaround for missing window.innerWidth in IE8\n        var documentElementRect = document.documentElement.getBoundingClientRect();\n        fullWindowWidth = documentElementRect.right - Math.abs(documentElementRect.left);\n      }\n\n      this.bodyIsOverflowing = document.body.clientWidth < fullWindowWidth;\n\n      if (this.bodyIsOverflowing) {\n        this.scrollBarWidth = getScrollBarSize();\n      }\n    },\n    checkMaskInVisible: function checkMaskInVisible() {\n      var masks = document.getElementsByClassName('ivu-modal-mask') || [];\n      return Array.from(masks).every(function (m) {\n        return m.style.display === 'none' || m.classList.contains('fade-leave-to');\n      });\n    },\n    setScrollBar: function setScrollBar() {\n      if (this.bodyIsOverflowing && this.scrollBarWidth !== undefined) {\n        document.body.style.paddingRight = \"\".concat(this.scrollBarWidth, \"px\");\n      }\n    },\n    resetScrollBar: function resetScrollBar() {\n      document.body.style.paddingRight = '';\n    },\n    addScrollEffect: function addScrollEffect() {\n      this.checkScrollBar();\n      this.setScrollBar();\n      document.body.style.overflow = 'hidden';\n    },\n    removeScrollEffect: function removeScrollEffect() {\n      if (this.checkMaskInVisible()) {\n        document.body.style.overflow = '';\n        this.resetScrollBar();\n      }\n    }\n  }\n};", null]}