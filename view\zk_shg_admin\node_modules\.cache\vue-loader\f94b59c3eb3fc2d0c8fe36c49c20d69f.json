{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsAttr\\index.vue", "mtime": 1683692372000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n\tmapState\n} from \"vuex\";\nimport {\n\tcascaderListApi,\n\tchangeListApi,\n\tallLabelApi\n} from \"@/api/product\";\nexport default {\n\tname: \"index\",\n\tprops: {\n\t\tgoodsType: {\n\t\t\ttype: Number,\n\t\t\tdefault: 0,\n\t\t},\n\t\tis_new: {\n\t\t\ttype: String,\n\t\t\tdefault: \"\",\n\t\t},\n\t\tdiy: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false,\n\t\t},\n\t\tisdiy: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false,\n\t\t},\n\t\tischeckbox: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false,\n\t\t},\n\t\tdatas: {\n\t\t\ttype: Object,\n\t\t\tdefault: function() {\n\t\t\t\treturn {};\n\t\t\t},\n\t\t},\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tlabelSelect: [],\n\t\t\tcateIds: [],\n\t\t\ttreeSelect: [],\n\t\t\tformValidate: {\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10,\n\t\t\t\tcate_id: \"\",\n\t\t\t\tstore_name: \"\",\n\t\t\t\tis_new: this.is_new,\n\t\t\t\tstore_label_id: \"\"\n\t\t\t},\n\t\t\ttotal: 0,\n\t\t\tloading: false,\n\t\t\tgrid: {\n\t\t\t\txl: 10,\n\t\t\t\tlg: 10,\n\t\t\t\tmd: 12,\n\t\t\t\tsm: 24,\n\t\t\t\txs: 24,\n\t\t\t},\n\t\t\ttableList: [],\n\t\t\tcurrentid: 0,\n\t\t\tproductRow: {},\n\t\t\timages: []\n\t\t};\n\t},\n\tcomputed: {\n\t\t...mapState(\"admin/layout\", [\"isMobile\"]),\n\t\tlabelWidth() {\n\t\t\treturn this.isMobile ? undefined : 120;\n\t\t},\n\t\tlabelPosition() {\n\t\t\treturn this.isMobile ? \"top\" : \"right\";\n\t\t},\n\t},\n\tcreated() {},\n\tmounted() {\n\t\tthis.goodsCategory();\n\t\tthis.getList();\n\t\tthis.getAllLabelApi();\n\t},\n\tmethods: {\n\t\tgetAllLabelApi() {\n\t\t\tallLabelApi().then(res => {\n\t\t\t\tthis.labelSelect = res.data\n\t\t\t}).catch(err => {\n\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t})\n\t\t},\n\t\t// 商品分类；\n\t\tgoodsCategory() {\n\t\t\tcascaderListApi(1)\n\t\t\t\t.then((res) => {\n\t\t\t\t\tthis.treeSelect = res.data;\n\t\t\t\t})\n\t\t\t\t.catch((res) => {\n\t\t\t\t\tthis.$Message.error(res.msg);\n\t\t\t\t});\n\t\t},\n\t\tpageChange({ currentPage, pageSize }) {\n\t\t\tthis.formValidate.page = currentPage;\n\t\t\tthis.formValidate.limit = pageSize;\n\t\t\tthis.getList();\n\t\t},\n\t\t// 列表\n\t\tgetList() {\n\t\t\tthis.loading = true;\n\t\t\tif (this.goodsType) {\n\t\t\t\tthis.formValidate.is_presale_product = 0;\n\t\t\t\tthis.formValidate.is_vip_product = 0;\n\t\t\t}\n\t\t\tthis.formValidate.cate_id = this.cateIds[this.cateIds.length - 1]\n\t\t\tchangeListApi(this.formValidate)\n\t\t\t\t.then(async (res) => {\n\t\t\t\t\tlet list = res.data.list;\n\t\t\t\t\tlist.forEach(item=>{\n\t\t\t\t\t\titem.attrValue.forEach(j=>{\n\t\t\t\t\t\t\tj.store_name = j.suk,\n\t\t\t\t\t\t\tj.cate_name = item.cate_name,\n\t\t\t\t\t\t\tj.store_label = item.store_label\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\tthis.tableList = list;\n\t\t\t\t\tthis.total = res.data.count;\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t})\n\t\t\t\t.catch((res) => {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\tthis.$Message.error(res.msg);\n\t\t\t\t});\n\t\t},\n\t\tok() {\n\t\t\t let selectRecords = this.$refs.xTree.getCheckboxRecords()\n\t\t\tlet goodsattr = [];\n\t\t\tselectRecords.forEach(function(item) {\n\t\t\t\tif(item.hasOwnProperty('product_id')){\n\t\t\t\t\tgoodsattr.push(item)\n\t\t\t\t}\n\t\t\t});\n\t\t\tif (goodsattr.length > 0) {\n\t\t\t\tthis.$emit(\"getProductId\", goodsattr);\n\t\t\t} else {\n\t\t\t\tthis.$Message.warning(\"请先选择商品\");\n\t\t\t}\n\t\t},\n\t\ttreeSearchs(value) {\n\t\t\tthis.cateIds = value;\n\t\t\tthis.formValidate.page = 1;\n\t\t\tthis.getList();\n\t\t},\n\t\t// 表格搜索\n\t\tuserSearchs() {\n\t\t\tthis.formValidate.page = 1;\n\t\t\tthis.getList();\n\t\t},\n\t\tclear() {\n\t\t\tthis.productRow.id = \"\";\n\t\t\tthis.currentid = \"\";\n\t\t},\n\t},\n};\n", null]}