{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_input_number.vue?vue&type=template&id=95a8396a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_input_number.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.configData)?_c('div',{staticClass:\"numbox\",class:_vm.configData.type=='form'?'on':_vm.configData.type=='words'?'on2':''},[_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_c('span',[_vm._v(_vm._s(_vm.configData.title ||'商品数量'))])]),_c('Col',{staticClass:\"slider-box\",attrs:{\"span\":_vm.configData.type=='form'?19:18}},[_c('div',{staticClass:\"acea-row row-middle\"},[_c('InputNumber',{staticStyle:{\"text-align\":\"right\"},attrs:{\"placeholder\":_vm.configData.placeholder,\"step\":1,\"max\":100,\"min\":1},on:{\"on-change\":_vm.bindChange},model:{value:(_vm.configData.val),callback:function ($$v) {_vm.$set(_vm.configData, \"val\", $$v)},expression:\"configData.val\"}}),(_vm.configData.type=='words')?_c('div',{staticClass:\"unit\"},[_vm._v(\"秒\")]):_vm._e()],1)])],1)]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}