{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeBargain\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { bargainListApi, bargainSetStatusApi, stroeBargainApi } from \"@/api/marketing\";\nimport { formatDate as _formatDate } from \"@/utils/validate\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport Setting from \"@/setting\";\nexport default {\n  name: \"storeBargain\",\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return _formatDate(date, \"yyyy-MM-dd hh:mm\");\n      }\n    },\n    filterStatus: function filterStatus(val) {\n      var nowTime = Date.parse(new Date()) / 1000;\n\n      if (val.start_time < nowTime && val.stop_time > nowTime && val.status == 1) {\n        return \"活动进行中\";\n      } else if (val.stop_time < nowTime && val.status == 1) {\n        return \"活动已结束\";\n      } else {\n        return \"活动未开始\";\n      }\n    }\n  },\n  data: function data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      columns1: [{\n        title: \"ID\",\n        key: \"id\",\n        width: 80\n      }, {\n        title: \"砍价图片\",\n        slot: \"image\",\n        minWidth: 90\n      }, {\n        title: \"砍价名称\",\n        slot: \"title\",\n        minWidth: 150\n      }, {\n        title: \"砍价价格\",\n        key: \"price\",\n        minWidth: 100\n      }, {\n        title: \"最低价\",\n        key: \"min_price\",\n        minWidth: 100\n      }, {\n        title: \"参与人数\",\n        key: \"count_people_all\",\n        minWidth: 100\n      }, {\n        title: \"帮忙砍价人数\",\n        key: \"count_people_help\",\n        minWidth: 100\n      }, {\n        title: \"砍价成功人数\",\n        key: \"count_people_success\",\n        minWidth: 100\n      }, {\n        title: \"限量\",\n        key: \"quota_show\",\n        minWidth: 80\n      }, {\n        title: \"限量剩余\",\n        key: \"quota\",\n        minWidth: 80\n      }, {\n        title: \"活动状态\",\n        slot: \"start_name\",\n        minWidth: 100\n      }, {\n        title: \"结束时间\",\n        slot: \"stop_time\",\n        minWidth: 150\n      }, {\n        title: \"上架状态\",\n        slot: \"status\",\n        minWidth: 130\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        fixed: \"right\",\n        width: 180\n      }],\n      tableList: [],\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      tableFrom: {\n        start_status: \"\",\n        status: \"\",\n        store_name: \"\",\n        export: 0,\n        page: 1,\n        limit: 15\n      },\n      tableFrom2: {\n        status: \"\",\n        store_name: \"\",\n        export: 1\n      },\n      total: 0\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    // 添加\n    add: function add() {\n      this.$router.push({\n        path: \"\".concat(this.roterPre, \"/marketing/store_bargain/create\")\n      });\n    },\n    // 导出\n    // exports () {\n    //     let formValidate = this.tableFrom;\n    //     let data = {\n    //         start_status: formValidate.start_status,\n    //         status: formValidate.status,\n    //         store_name: formValidate.store_name\n    //     };\n    //     stroeBargainApi(data).then(res => {\n    //         location.href = res.data[0];\n    //     }).catch(res => {\n    //         this.$Message.error(res.msg)\n    //     })\n    // },\n    // 数据导出；\n    exports: function () {\n      var _exports = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        var th, filekey, data, fileName, excelData, i, lebData;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                th = [], filekey = [], data = [], fileName = \"\"; //   let fileName = \"\";\n\n                excelData = JSON.parse(JSON.stringify(this.tableFrom));\n                excelData.page = 1;\n                i = 0;\n\n              case 4:\n                if (!(i < excelData.page + 1)) {\n                  _context.next = 21;\n                  break;\n                }\n\n                _context.next = 7;\n                return this.getExcelData(excelData);\n\n              case 7:\n                lebData = _context.sent;\n                if (!fileName) fileName = lebData.filename;\n\n                if (!filekey.length) {\n                  filekey = lebData.filekey;\n                }\n\n                if (!th.length) th = lebData.header;\n\n                if (!lebData.export.length) {\n                  _context.next = 16;\n                  break;\n                }\n\n                data = data.concat(lebData.export);\n                excelData.page++;\n                _context.next = 18;\n                break;\n\n              case 16:\n                exportExcel(th, filekey, fileName, data);\n                return _context.abrupt(\"return\");\n\n              case 18:\n                i++;\n                _context.next = 4;\n                break;\n\n              case 21:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function exports() {\n        return _exports.apply(this, arguments);\n      }\n\n      return exports;\n    }(),\n    getExcelData: function getExcelData(excelData) {\n      return new Promise(function (resolve, reject) {\n        stroeBargainApi(excelData).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 编辑\n    edit: function edit(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_bargain/create/\" + row.id + \"/0\"\n      });\n    },\n    // 一键复制\n    copy: function copy(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_bargain/create/\" + row.id + \"/1\"\n      });\n    },\n    // 删除\n    del: function del(row, tit, num) {\n      var _this = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"marketing/bargain/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this.$Message.success(res.msg);\n\n        _this.tableList.splice(num, 1);\n\n        if (!_this.tableList.length) {\n          _this.tableFrom.page = _this.tableFrom.page == 1 ? 1 : _this.tableFrom.page - 1;\n        }\n\n        _this.getList();\n      }).catch(function (res) {\n        _this.$Message.error(res.msg);\n      });\n    },\n    viewInfo: function viewInfo(row) {\n      this.$router.push({\n        path: this.roterPre + '/marketing/store_bargain/statistics/' + row.id\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      this.tableFrom.start_status = this.tableFrom.start_status || \"\";\n      this.tableFrom.status = this.tableFrom.status || \"\";\n      bargainListApi(this.tableFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee2(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  data = res.data;\n                  _this2.tableList = data.list;\n                  _this2.total = res.data.count;\n                  _this2.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs: function userSearchs() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 修改是否显示\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this3 = this;\n\n      var data = {\n        id: row.id,\n        status: row.status\n      };\n\n      if (!parseInt(row.status)) {\n        this.$Modal.confirm({\n          title: \"您确定要下架【\" + row.title + \"】商品吗？\",\n          content: \"下架将会删除前台用户已砍的所有记录，所有用户将重新发起砍价,您确定要这样操作吗？\",\n          okText: \"我想好了，确定要下架\",\n          cancelText: \"取消下架操作\",\n          onOk: function onOk() {\n            bargainSetStatusApi(data).then(\n            /*#__PURE__*/\n            function () {\n              var _ref2 = _asyncToGenerator(\n              /*#__PURE__*/\n              _regeneratorRuntime.mark(function _callee3(res) {\n                return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n                  while (1) {\n                    switch (_context3.prev = _context3.next) {\n                      case 0:\n                        _this3.$Message.success(res.msg);\n\n                      case 1:\n                      case \"end\":\n                        return _context3.stop();\n                    }\n                  }\n                }, _callee3);\n              }));\n\n              return function (_x2) {\n                return _ref2.apply(this, arguments);\n              };\n            }()).catch(function (res) {\n              _this3.$Message.error(res.msg);\n            });\n          },\n          onCancel: function onCancel() {\n            row.status = 1;\n          }\n        });\n      } else {\n        bargainSetStatusApi(data).then(\n        /*#__PURE__*/\n        function () {\n          var _ref3 = _asyncToGenerator(\n          /*#__PURE__*/\n          _regeneratorRuntime.mark(function _callee4(res) {\n            return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n              while (1) {\n                switch (_context4.prev = _context4.next) {\n                  case 0:\n                    _this3.$Message.success(res.msg);\n\n                  case 1:\n                  case \"end\":\n                    return _context4.stop();\n                }\n              }\n            }, _callee4);\n          }));\n\n          return function (_x3) {\n            return _ref3.apply(this, arguments);\n          };\n        }()).catch(function (res) {\n          _this3.$Message.error(res.msg);\n        });\n      }\n    }\n  }\n};", null]}