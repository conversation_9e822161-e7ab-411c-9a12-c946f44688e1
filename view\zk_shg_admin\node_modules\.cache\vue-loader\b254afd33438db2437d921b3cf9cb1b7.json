{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\add_template.vue?vue&type=style&index=0&id=1dea5522&scoped=true&lang=stylus&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\groupTemplate\\add_template.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.input-add {\n  width:460px;\n}\n.desc {\n  color: #999;\n  font-size: 12px;\n  line-height: 17px;\n  padding-top: 6px;\n}\n\n/deep/.ivu-input-number-input {\n  text-align: center;\n}\n\n.poptip_content {\n  width: 138px;\n  height: 94px;\n  box-sizing: border-box;\n  padding: 17px;\n  display: flex;\n  justify-content: space-between;\n  cursor: pointer;\n}\n\n.add_img {\n  width: 40px;\n  height: 40px;\n  background: rgba(24, 144, 255, 0.1);\n  border-radius: 2px;\n}\n\n.add_routine {\n  width: 40px;\n  height: 40px;\n  background: rgba(27, 190, 107, 0.1);\n  border-radius: 2px;\n}\n\n.icontupian4 {\n  color: #1890FF;\n  font-size: 20px;\n}\n\n.iconxiaochengxu {\n  color: #1BBE6B;\n  font-size: 20px;\n}\n\n.tip_tit {\n  display: block;\n  font-size: 12px;\n  text-align: center;\n  padding-top: 6px;\n}\n\n.tag_icon {\n  font-size: 14px;\n  display: inline-block;\n  margin-right: 8px;\n}\n\n.picBox {\n  display: inline-block;\n  cursor: pointer;\n\n  .upLoad {\n    width: 58px;\n    height: 58px;\n    line-height: 58px;\n    border: 1px dotted rgba(0, 0, 0, 0.1);\n    border-radius: 4px;\n    background: rgba(0, 0, 0, 0.02);\n  }\n\n  .pictrue {\n    width: 60px;\n    height: 60px;\n    border: 1px dotted rgba(0, 0, 0, 0.1);\n    margin-right: 10px;\n\n    img {\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  .iconfont {\n    color: #CCCCCC;\n    font-size: 26px;\n    text-align: center;\n  }\n}\n\n.mb100 {\n  margin-bottom: 100px;\n}\n\n.fixed-card {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 200px;\n  box-shadow: 0 -1px 2px rgb(240, 240, 240);\n\n  /deep/ .ivu-card-body {\n    padding: 15px 16px 14px;\n  }\n\n  .ivu-form-item {\n    margin-bottom: 0;\n  }\n\n  /deep/ .ivu-form-item-content {\n    margin-right: 124px;\n    text-align: center;\n  }\n}\n\n.tips {\n\t  display: inline-bolck;\n\t  font-size: 12px;\n\t  font-weight: 400;\n\t  color: #999999;\n\t  margin-top: 10px;\n\t  line-height: initial;\n\t}\n", null]}