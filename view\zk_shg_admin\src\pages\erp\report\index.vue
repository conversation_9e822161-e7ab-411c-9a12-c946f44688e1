<template>
  <div class="report-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">数据报表</h2>
        <p class="page-desc">ERP系统数据分析与报表展示</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          @change="handleDateChange"
          style="margin-right: 12px">
        </el-date-picker>
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
        <el-button type="success" icon="el-icon-download" @click="exportReport">导出报表</el-button>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <div class="overview-card">
        <div class="card-icon inventory">
          <i class="el-icon-goods"></i>
        </div>
        <div class="card-content">
          <div class="card-title">库存总值</div>
          <div class="card-number">¥{{ formatMoney(overview.inventory_value) }}</div>
          <div class="card-change" :class="overview.inventory_change >= 0 ? 'positive' : 'negative'">
            <i :class="overview.inventory_change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            {{ Math.abs(overview.inventory_change) }}%
          </div>
        </div>
      </div>

      <div class="overview-card">
        <div class="card-icon transfer">
          <i class="el-icon-sort"></i>
        </div>
        <div class="card-content">
          <div class="card-title">调拨单数</div>
          <div class="card-number">{{ overview.transfer_count }}</div>
          <div class="card-change" :class="overview.transfer_change >= 0 ? 'positive' : 'negative'">
            <i :class="overview.transfer_change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            {{ Math.abs(overview.transfer_change) }}%
          </div>
        </div>
      </div>

      <div class="overview-card">
        <div class="card-icon warning">
          <i class="el-icon-warning"></i>
        </div>
        <div class="card-content">
          <div class="card-title">预警商品</div>
          <div class="card-number">{{ overview.warning_count }}</div>
          <div class="card-desc">需要关注</div>
        </div>
      </div>

      <div class="overview-card">
        <div class="card-icon efficiency">
          <i class="el-icon-data-analysis"></i>
        </div>
        <div class="card-content">
          <div class="card-title">调拨效率</div>
          <div class="card-number">{{ overview.efficiency }}%</div>
          <div class="card-desc">平均处理时间</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 库存趋势图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>库存变动趋势</h3>
          <div class="chart-controls">
            <el-radio-group v-model="inventoryPeriod" size="small" @change="loadInventoryTrend">
              <el-radio-button label="week">近7天</el-radio-button>
              <el-radio-button label="month">近30天</el-radio-button>
              <el-radio-button label="quarter">近3个月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-content">
          <div ref="inventoryChart" style="width: 100%; height: 300px;"></div>
        </div>
      </div>

      <!-- 调拨统计图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>调拨统计分析</h3>
          <div class="chart-controls">
            <el-select v-model="transferType" size="small" @change="loadTransferStats" style="width: 120px">
              <el-option label="按状态" value="status"></el-option>
              <el-option label="按门店" value="store"></el-option>
              <el-option label="按时间" value="time"></el-option>
            </el-select>
          </div>
        </div>
        <div class="chart-content">
          <div ref="transferChart" style="width: 100%; height: 300px;"></div>
        </div>
      </div>
    </div>

    <!-- 详细报表 -->
    <div class="report-tables">
      <!-- 门店库存报表 -->
      <div class="table-card">
        <div class="table-header">
          <h3>门店库存报表</h3>
          <el-button type="text" size="small" @click="exportStoreReport">导出</el-button>
        </div>
        <el-table :data="storeReport" v-loading="storeLoading" stripe>
          <el-table-column prop="store_name" label="门店名称" width="150"></el-table-column>
          <el-table-column prop="total_products" label="商品总数" width="100" align="center"></el-table-column>
          <el-table-column prop="total_stock" label="库存总量" width="100" align="center"></el-table-column>
          <el-table-column prop="total_value" label="库存总值" width="120" align="center">
            <template slot-scope="scope">
              <span class="money">¥{{ formatMoney(scope.row.total_value) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="warning_count" label="预警商品" width="100" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.warning_count > 0 ? 'warning-text' : ''">
                {{ scope.row.warning_count }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="out_stock_count" label="缺货商品" width="100" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.out_stock_count > 0 ? 'danger-text' : ''">
                {{ scope.row.out_stock_count }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="库存状态" width="120" align="center">
            <template slot-scope="scope">
              <el-progress
                :percentage="getStockHealth(scope.row)"
                :color="getProgressColor(scope.row)"
                :stroke-width="8"
                :show-text="false">
              </el-progress>
              <span class="progress-text">{{ getStockHealth(scope.row) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="viewStoreDetail(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 调拨效率报表 -->
      <div class="table-card">
        <div class="table-header">
          <h3>调拨效率报表</h3>
          <el-button type="text" size="small" @click="exportTransferReport">导出</el-button>
        </div>
        <el-table :data="transferReport" v-loading="transferLoading" stripe>
          <el-table-column prop="date" label="日期" width="120" align="center"></el-table-column>
          <el-table-column prop="total_orders" label="调拨单数" width="100" align="center"></el-table-column>
          <el-table-column prop="approved_orders" label="已审核" width="100" align="center"></el-table-column>
          <el-table-column prop="completed_orders" label="已完成" width="100" align="center"></el-table-column>
          <el-table-column label="完成率" width="100" align="center">
            <template slot-scope="scope">
              <span :class="getEfficiencyClass(scope.row)">
                {{ getCompletionRate(scope.row) }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="avg_process_time" label="平均处理时间" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.avg_process_time }}小时
            </template>
          </el-table-column>
          <el-table-column prop="total_quantity" label="调拨总量" width="100" align="center"></el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getEfficiencyTagType(scope.row)" size="small">
                {{ getEfficiencyText(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 商品分析 -->
    <div class="analysis-section">
      <div class="section-header">
        <h3>商品分析</h3>
        <el-button type="text" size="small" @click="refreshProductAnalysis">刷新</el-button>
      </div>

      <div class="analysis-cards">
        <div class="analysis-card">
          <div class="card-header">
            <h4>热门调拨商品 TOP10</h4>
          </div>
          <div class="product-list">
            <div
              v-for="(product, index) in hotProducts"
              :key="product.id"
              class="product-item">
              <div class="product-rank">{{ index + 1 }}</div>
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-stats">调拨次数: {{ product.transfer_count }}</div>
              </div>
              <div class="product-trend">
                <i :class="product.trend === 'up' ? 'el-icon-arrow-up trend-up' : 'el-icon-arrow-down trend-down'"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="analysis-card">
          <div class="card-header">
            <h4>库存预警分析</h4>
          </div>
          <div class="warning-stats">
            <div class="warning-item">
              <div class="warning-label">严重缺货</div>
              <div class="warning-value danger">{{ warningStats.severe }}</div>
            </div>
            <div class="warning-item">
              <div class="warning-label">库存偏低</div>
              <div class="warning-value warning">{{ warningStats.low }}</div>
            </div>
            <div class="warning-item">
              <div class="warning-label">库存正常</div>
              <div class="warning-value success">{{ warningStats.normal }}</div>
            </div>
            <div class="warning-item">
              <div class="warning-label">库存充足</div>
              <div class="warning-value info">{{ warningStats.abundant }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 注意：实际项目中需要引入echarts
// import * as echarts from 'echarts'

export default {
  name: 'ERPReport',
  data() {
    return {
      // 日期选择
      dateRange: [],

      // 概览数据
      overview: {
        inventory_value: 1250000,
        inventory_change: 12.5,
        transfer_count: 156,
        transfer_change: 8.3,
        warning_count: 23,
        efficiency: 92
      },

      // 图表控制
      inventoryPeriod: 'month',
      transferType: 'status',

      // 报表数据
      storeReport: [],
      transferReport: [],
      storeLoading: false,
      transferLoading: false,

      // 商品分析
      hotProducts: [],
      warningStats: {
        severe: 5,
        low: 18,
        normal: 234,
        abundant: 89
      }
    };
  },

  created() {
    this.initData();
  },

  methods: {
    // 初始化数据
    async initData() {
      // 设置默认日期范围（最近3个月）
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth() - 2, 1);
      this.dateRange = [start, now];

      await Promise.all([
        this.loadStoreReport(),
        this.loadTransferReport(),
        this.loadProductAnalysis()
      ]);

      this.$nextTick(() => {
        this.initCharts();
      });
    },

    // 加载门店报表
    async loadStoreReport() {
      this.storeLoading = true;
      // 模拟数据
      setTimeout(() => {
        this.storeReport = [
          {
            store_id: 1,
            store_name: '上海总店',
            total_products: 156,
            total_stock: 2340,
            total_value: 450000,
            warning_count: 8,
            out_stock_count: 2
          },
          {
            store_id: 2,
            store_name: '北京分店',
            total_products: 134,
            total_stock: 1890,
            total_value: 380000,
            warning_count: 12,
            out_stock_count: 1
          },
          {
            store_id: 3,
            store_name: '深圳分店',
            total_products: 142,
            total_stock: 2100,
            total_value: 420000,
            warning_count: 3,
            out_stock_count: 2
          }
        ];
        this.storeLoading = false;
      }, 1000);
    },

    // 加载调拨报表
    async loadTransferReport() {
      this.transferLoading = true;
      // 模拟数据
      setTimeout(() => {
        this.transferReport = [
          {
            date: '2024-12-16',
            total_orders: 12,
            approved_orders: 10,
            completed_orders: 8,
            avg_process_time: 2.5,
            total_quantity: 245
          },
          {
            date: '2024-12-15',
            total_orders: 8,
            approved_orders: 8,
            completed_orders: 7,
            avg_process_time: 1.8,
            total_quantity: 156
          },
          {
            date: '2024-12-14',
            total_orders: 15,
            approved_orders: 13,
            completed_orders: 12,
            avg_process_time: 3.2,
            total_quantity: 389
          }
        ];
        this.transferLoading = false;
      }, 1000);
    },

    // 加载商品分析
    async loadProductAnalysis() {
      // 模拟数据
      this.hotProducts = [
        { id: 1, name: '苹果iPhone 14', transfer_count: 45, trend: 'up' },
        { id: 2, name: '华为Mate50', transfer_count: 38, trend: 'up' },
        { id: 3, name: '小米13', transfer_count: 32, trend: 'down' },
        { id: 4, name: 'OPPO Find X5', transfer_count: 28, trend: 'up' },
        { id: 5, name: 'vivo X90', transfer_count: 25, trend: 'down' }
      ];
    },

    // 初始化图表
    initCharts() {
      this.loadInventoryTrend();
      this.loadTransferStats();
    },

    // 加载库存趋势图
    loadInventoryTrend() {
      // 模拟图表初始化
      console.log('加载库存趋势图:', this.inventoryPeriod);
      // 在实际项目中，这里会使用echarts初始化图表
    },

    // 加载调拨统计图
    loadTransferStats() {
      // 模拟图表初始化
      console.log('加载调拨统计图:', this.transferType);
      // 在实际项目中，这里会使用echarts初始化图表
    },

    // 工具方法
    formatMoney(amount) {
      return (amount / 10000).toFixed(1) + '万';
    },

    getStockHealth(row) {
      const total = row.total_products;
      const warning = row.warning_count;
      const outStock = row.out_stock_count;
      return Math.round(((total - warning - outStock) / total) * 100);
    },

    getProgressColor(row) {
      const health = this.getStockHealth(row);
      if (health >= 80) return '#67c23a';
      if (health >= 60) return '#e6a23c';
      return '#f56c6c';
    },

    getCompletionRate(row) {
      return Math.round((row.completed_orders / row.total_orders) * 100);
    },

    getEfficiencyClass(row) {
      const rate = this.getCompletionRate(row);
      if (rate >= 80) return 'success-text';
      if (rate >= 60) return 'warning-text';
      return 'danger-text';
    },

    getEfficiencyTagType(row) {
      const rate = this.getCompletionRate(row);
      if (rate >= 80) return 'success';
      if (rate >= 60) return 'warning';
      return 'danger';
    },

    getEfficiencyText(row) {
      const rate = this.getCompletionRate(row);
      if (rate >= 80) return '优秀';
      if (rate >= 60) return '良好';
      return '待改进';
    },

    // 事件处理
    handleDateChange() {
      this.refreshData();
    },

    refreshData() {
      this.loadStoreReport();
      this.loadTransferReport();
      this.loadProductAnalysis();
      this.initCharts();
    },

    refreshProductAnalysis() {
      this.loadProductAnalysis();
    },

    // 查看详情
    viewStoreDetail(row) {
      this.$message.info(`查看${row.store_name}详情功能开发中...`);
    },

    // 导出功能
    exportReport() {
      this.$message.info('导出报表功能开发中...');
    },

    exportStoreReport() {
      this.$message.info('导出门店报表功能开发中...');
    },

    exportTransferReport() {
      this.$message.info('导出调拨报表功能开发中...');
    }
  }
};
</script>

<style lang="scss" scoped>

</style>

