{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\autoSend.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\orderList\\handle\\autoSend.vue", "mtime": 1676252006000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  getExpressData,\n  orderExpressTemp,\n  orderDeliveryList,\n  orderSheetInfo,\n  otherBatchDelivery,\n} from \"@/api/order\";\nexport default {\n  name: \"orderSend\",\n  props: {\n    isAll: {\n      type: Number | String,\n      default: 0,\n    },\n    ids: {\n      type: Array,\n      default() {\n        return [];\n      },\n    },\n    where: {\n      type: Object,\n      default() {\n        return {};\n      },\n    },\n    selectArr:{\n      type:Array,\n      default() {\n        return [];\n      },\n    }\n  },\n  data() {\n    return {\n      formItem: {\n        type: \"1\",\n        express_record_type: \"2\",\n        delivery_name: \"\",\n        delivery_id: \"\",\n        express_temp_id: \"\",\n        to_name: \"\",\n        to_tel: \"\",\n        to_addr: \"\",\n        sh_delivery: \"\",\n        fictitious_content: \"\",\n      },\n      modals: false,\n      express: [],\n      expressTemp: [],\n      deliveryList: [],\n      temp: {},\n      export_open: true,\n    };\n  },\n  watch: {\n    \"formItem.express_temp_id\"(value) {\n    },\n  },\n  methods: {\n    changeRadio(o) {\n      this.$refs.formItem.resetFields();\n      switch (o) {\n        case \"1\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.delivery_id = \"\";\n          this.formItem.express_temp_id = \"\";\n          this.formItem.express_record_type = \"2\";\n          this.expressTemp = [];\n          break;\n        case \"2\":\n          this.formItem.sh_delivery = \"\";\n          this.formItem.express_record_type = \"1\";\n          break;\n        case \"3\":\n          this.formItem.fictitious_content = \"\";\n          this.formItem.express_record_type = \"1\";\n          break;\n      }\n    },\n    changeExpress(j) {\n      switch (j) {\n        case \"2\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.express_temp_id = \"\";\n          this.expressTemp = [];\n          break;\n        case \"1\":\n          this.formItem.delivery_name = \"\";\n          this.formItem.delivery_id = \"\";\n          break;\n        default:\n          break;\n      }\n    },\n    reset() {\n     // this.expressTemp= [];\n      this.formItem = {\n        type: \"1\",\n        express_record_type: \"2\",\n        delivery_name: \"\",\n        delivery_id: \"\",\n        express_temp_id: \"\",\n        to_name: \"\",\n        to_tel: \"\",\n        to_addr: \"\",\n        sh_delivery: \"\",\n        fictitious_content: \"\",\n      };\n    },\n    // 物流公司列表\n    getList() {\n      getExpressData(1)\n        .then(async (res) => {\n          this.express = res.data;\n          this.getSheetInfo();\n        })\n        .catch((res) => {\n          this.loading = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    // 提交\n    putSend(name) {\n      let data = Object.assign(this.formItem);\n      // let arr = [];\n      // this.selectArr.forEach((item) => {\n      //   arr.push(item.id);\n      // });\n      if (this.isAll == 1) {\n        data.all = 1;\n        data.ids = this.selectArr;\n        data.where = this.where;\n      } else if(this.isAll == 0) {\n        data.all = 0;\n        data.ids = this.selectArr;\n      }\n      if (this.formItem.type === \"1\") {\n        if (this.formItem.delivery_name === \"\") {\n          return this.$Message.error(\"快递公司不能为空\");\n        } else if (this.formItem.express_temp_id === \"\") {\n          return this.$Message.error(\"电子面单不能为空\");\n        } else if (this.formItem.to_name === \"\") {\n          return this.$Message.error(\"寄件人姓名不能为空\");\n        } else if (this.formItem.to_tel === \"\") {\n          return this.$Message.error(\"寄件人电话不能为空\");\n        } else if (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(this.formItem.to_tel)) {\n          return this.$Message.error(\"请输入正确的手机号码\");\n        } else if (this.formItem.to_addr === \"\") {\n          return this.$Message.error(\"寄件人地址不能为空\");\n        }\n      }\n      if (this.formItem.type === \"2\") {\n        if (this.formItem.express_temp_id) {\n          this.formItem.express_temp_id = \"\";\n        }\n        if (this.formItem.sh_delivery === \"\") {\n          return this.$Message.error(\"送货人不能为空\");\n        }\n      }\n      otherBatchDelivery(data)\n        .then(async (res) => {\n          this.modals = false;\n          this.$Message.success(res.msg);\n          this.reset();\n        })\n        .catch((res) => {\n          this.modals = false;\n          this.$Message.error(res.msg);\n        });\n    },\n    cancel(name) {\n      this.modals = false;\n      this.reset();\n    },\n    // 电子面单列表\n    expressChange(value) {\n      let expressItem = this.express.find((item) => {\n        return item.value === value;\n      });\n      if (!expressItem) {\n        return;\n      }\n      this.formItem.delivery_code = expressItem.code;\n      if (this.formItem.type === \"1\") {\n        this.expressTemp = [];\n        this.formItem.express_temp_id = \"\";\n        orderExpressTemp({\n          com: this.formItem.delivery_code,\n        })\n          .then((res) => {\n            this.expressTemp = res.data;\n            if (!res.data.length) {\n              this.$Message.error(\"请配置你所选快递公司的电子面单\");\n            }\n          })\n          .catch((err) => {\n            this.$Message.error(err.msg);\n          });\n      }\n    },\n    getDeliveryList() {\n      orderDeliveryList()\n        .then((res) => {\n          this.deliveryList = res.data.list;\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    getSheetInfo() {\n      orderSheetInfo()\n        .then((res) => {\n          const data = res.data;\n          for (const key in data) {\n            if (data.hasOwnProperty(key) && key !== \"express_temp_id\") {\n              this.formItem[key] = data[key];\n            }\n          }\n          this.export_open =\n            data.export_open === undefined ? true : data.export_open;\n          if (!this.export_open) {\n            this.formItem.express_record_type = \"1\";\n          }\n          this.formItem.to_addr = data.to_add;\n        })\n        .catch((err) => {\n          this.$Message.error(err.msg);\n        });\n    },\n    shDeliveryChange(value) {\n      let deliveryItem = this.deliveryList.find((item) => {\n        return item.id === value;\n      });\n      this.formItem.sh_delivery_name = deliveryItem.wx_name;\n      this.formItem.sh_delivery_id = deliveryItem.phone;\n      this.formItem.sh_delivery_uid = deliveryItem.uid;\n    },\n    expressTempChange() {\n      this.expressTemp.forEach(item=>{\n        if(this.formItem.express_temp_id === item.temp_id){\n          this.temp = item\n        }\n      })\n    },\n    preview() {\n      this.$refs.viewer.$viewer.show();\n    },\n  },\n};\n", null]}