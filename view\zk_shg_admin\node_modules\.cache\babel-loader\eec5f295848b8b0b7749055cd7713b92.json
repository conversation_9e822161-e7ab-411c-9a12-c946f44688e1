{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_menu.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_menu.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nimport rightBtn from '@/components/rightBtn/index.vue';\nexport default {\n  name: 'c_home_menu',\n  cname: '导航组',\n  componentsName: 'home_menu',\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      rComContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleLeft'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'menuStyleConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'number'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'showConfig'\n      }],\n      oneContent: [{\n        components: toolCom.c_radio,\n        configNme: 'rowsNum'\n      }],\n      twoContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleContent'\n      }, {\n        components: toolCom.c_menu_list,\n        configNme: 'menuConfig'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'filletImg'\n      }],\n      twoStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titlePointer'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneConfig'\n      }],\n      threeStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'pointerColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'pointerBgColor'\n      }],\n      fourStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bgColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'mbConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      type: 0,\n      //展示样式索引\n      setUp: 0,\n      //0：内容；1：样式\n      type2: 0,\n      //导航样式索引\n      type3: 0 //色调索引\n\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          var rCom = arr.concat(this.rComContent);\n\n          if (this.type == 0) {\n            this.rCom = rCom.concat(this.twoContent);\n          } else {\n            var rCom2 = rCom.concat(this.oneContent);\n            this.rCom = rCom2.concat(this.twoContent);\n          }\n        } else {\n          if (this.type2 == 2) {\n            if (this.type == 0) {\n              this.rCom = arr.concat(this.fourStyle);\n            } else {\n              if (this.type3 == 0) {\n                var _rCom = arr.concat(this.twoStyle);\n\n                this.rCom = _rCom.concat(this.fourStyle);\n              } else {\n                var _rCom2 = arr.concat(this.twoStyle);\n\n                var _rCom3 = _rCom2.concat(this.threeStyle);\n\n                this.rCom = _rCom3.concat(this.fourStyle);\n              }\n            }\n          } else {\n            var _rCom4 = arr.concat(this.oneStyle);\n\n            if (this.type == 0) {\n              this.rCom = _rCom4.concat(this.fourStyle);\n            } else {\n              if (this.type3 == 0) {\n                var _rCom5 = _rCom4.concat(this.twoStyle);\n\n                this.rCom = _rCom5.concat(this.fourStyle);\n              } else {\n                var _rCom6 = _rCom4.concat(this.twoStyle);\n\n                var rCom3 = _rCom6.concat(this.threeStyle);\n\n                this.rCom = rCom3.concat(this.fourStyle);\n              }\n            }\n          }\n        }\n      },\n      deep: true\n    },\n    'configObj.menuStyleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type2 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp == 0) {\n          var rCom = arr.concat(this.rComContent);\n\n          if (this.type == 0) {\n            this.rCom = rCom.concat(this.twoContent);\n          } else {\n            var rCom2 = rCom.concat(this.oneContent);\n            this.rCom = rCom2.concat(this.twoContent);\n          }\n        } else {\n          if (nVal == 2) {\n            if (this.type == 0) {\n              this.rCom = arr.concat(this.fourStyle);\n            } else {\n              if (this.type3 == 0) {\n                var _rCom7 = arr.concat(this.twoStyle);\n\n                this.rCom = _rCom7.concat(this.fourStyle);\n              } else {\n                var _rCom8 = arr.concat(this.twoStyle);\n\n                var _rCom9 = _rCom8.concat(this.threeStyle);\n\n                this.rCom = _rCom9.concat(this.fourStyle);\n              }\n            }\n          } else {\n            var _rCom10 = arr.concat(this.oneStyle);\n\n            if (this.type == 0) {\n              this.rCom = _rCom10.concat(this.fourStyle);\n            } else {\n              if (this.type3 == 0) {\n                var _rCom11 = _rCom10.concat(this.twoStyle);\n\n                this.rCom = _rCom11.concat(this.fourStyle);\n              } else {\n                var _rCom12 = _rCom10.concat(this.twoStyle);\n\n                var rCom3 = _rCom12.concat(this.threeStyle);\n\n                this.rCom = rCom3.concat(this.fourStyle);\n              }\n            }\n          }\n        }\n      },\n      deep: true\n    },\n    'configObj.showConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          if (this.type2 == 2) {\n            if (nVal == 0) {\n              this.rCom = arr.concat(this.fourStyle);\n            } else {\n              if (this.type3 == 0) {\n                var rCom = arr.concat(this.twoStyle);\n                this.rCom = rCom.concat(this.fourStyle);\n              } else {\n                var _rCom13 = arr.concat(this.twoStyle);\n\n                var rCom2 = _rCom13.concat(this.threeStyle);\n\n                this.rCom = rCom2.concat(this.fourStyle);\n              }\n            }\n          } else {\n            var _rCom14 = arr.concat(this.oneStyle);\n\n            if (nVal == 0) {\n              this.rCom = _rCom14.concat(this.fourStyle);\n            } else {\n              if (this.type3 == 0) {\n                var _rCom15 = _rCom14.concat(this.twoStyle);\n\n                this.rCom = _rCom15.concat(this.fourStyle);\n              } else {\n                var _rCom16 = _rCom14.concat(this.twoStyle);\n\n                var rCom3 = _rCom16.concat(this.threeStyle);\n\n                this.rCom = rCom3.concat(this.fourStyle);\n              }\n            }\n          }\n        } else {\n          var _rCom17 = arr.concat(this.rComContent);\n\n          if (nVal == 0) {\n            this.rCom = _rCom17.concat(this.twoContent);\n          } else {\n            var _rCom18 = _rCom17.concat(this.oneContent);\n\n            this.rCom = _rCom18.concat(this.twoContent);\n          }\n        }\n      },\n      deep: true\n    },\n    'configObj.toneConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type3 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          if (this.type2 == 2) {\n            if (this.type == 0) {\n              this.rCom = arr.concat(this.fourStyle);\n            } else {\n              if (nVal == 0) {\n                var rCom = arr.concat(this.twoStyle);\n                this.rCom = rCom.concat(this.fourStyle);\n              } else {\n                var _rCom19 = arr.concat(this.twoStyle);\n\n                var rCom2 = _rCom19.concat(this.threeStyle);\n\n                this.rCom = rCom2.concat(this.fourStyle);\n              }\n            }\n          } else {\n            var _rCom20 = arr.concat(this.oneStyle);\n\n            if (this.type == 0) {\n              this.rCom = _rCom20.concat(this.fourStyle);\n            } else {\n              if (nVal == 0) {\n                var _rCom21 = _rCom20.concat(this.twoStyle);\n\n                this.rCom = _rCom21.concat(this.fourStyle);\n              } else {\n                var _rCom22 = _rCom20.concat(this.twoStyle);\n\n                var rCom3 = _rCom22.concat(this.threeStyle);\n\n                this.rCom = rCom3.concat(this.fourStyle);\n              }\n            }\n          }\n        } else {\n          var _rCom23 = arr.concat(this.rComContent);\n\n          if (this.type == 0) {\n            this.rCom = _rCom23.concat(this.twoContent);\n          } else {\n            var _rCom24 = _rCom23.concat(this.oneContent);\n\n            this.rCom = _rCom24.concat(this.twoContent);\n          }\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n    });\n  },\n  methods: {\n    getConfig: function getConfig(data) {}\n  }\n};", null]}