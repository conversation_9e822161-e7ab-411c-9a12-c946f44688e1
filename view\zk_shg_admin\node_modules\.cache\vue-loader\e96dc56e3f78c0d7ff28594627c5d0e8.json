{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_brand.vue?vue&type=template&id=3cb0a5c0&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_brand.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"slider-box\"},[(_vm.configData.title)?_c('div',{staticClass:\"c_row-item\"},[_c('Col',{staticClass:\"label\",attrs:{\"span\":\"4\"}},[_vm._v(\"\\n                \"+_vm._s(_vm.configData.title)+\"\\n            \")]),_c('Col',{attrs:{\"span\":\"18\"}},[_c('el-cascader',{attrs:{\"placeholder\":\"请选择品牌\",\"size\":\"mini\",\"options\":_vm.brandData,\"props\":_vm.props,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.sliderChange},model:{value:(_vm.configData.brandVal),callback:function ($$v) {_vm.$set(_vm.configData, \"brandVal\", $$v)},expression:\"configData.brandVal\"}})],1)],1):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}