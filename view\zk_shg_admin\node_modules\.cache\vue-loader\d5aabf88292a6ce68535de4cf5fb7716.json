{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=style&index=0&id=040cd542&scoped=true&lang=less&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751012477533}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1725352507996}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.sales-stats-container {\r\n  padding: 16px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 64px);\r\n}\r\n\r\n// 页面头部\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 16px;\r\n\r\n  .header-left {\r\n    .page-title {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #1f2937;\r\n      margin: 0 0 8px 0;\r\n    }\r\n\r\n    .page-desc {\r\n      font-size: 14px;\r\n      color: #6b7280;\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// 筛选容器\r\n.filter-container {\r\n  background: #ffffff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 16px;\r\n\r\n  :deep(.ivu-form-item) {\r\n    margin-right: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    .ivu-form-item-label {\r\n      font-weight: 500;\r\n      color: #374151;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n// 表格与分页\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  background: #ffffff;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .sales-stats-container {\r\n    padding: 8px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    text-align: center;\r\n  }\r\n}\r\n", null]}