{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=style&index=0&id=040cd542&scoped=true&lang=less&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751016248065}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1725352507996}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.sales-stats-container {\n  padding: 16px;\n  background: #f5f7fa;\n  min-height: calc(100vh - 64px);\n}\n\n// 页面头部\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: #ffffff;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin-bottom: 16px;\n\n  .header-left {\n    .page-title {\n      font-size: 24px;\n      font-weight: 600;\n      color: #1f2937;\n      margin: 0 0 8px 0;\n    }\n\n    .page-desc {\n      font-size: 14px;\n      color: #6b7280;\n      margin: 0;\n    }\n  }\n}\n\n// 筛选容器\n.filter-container {\n  background: #ffffff;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin-bottom: 16px;\n\n  :deep(.ivu-form-item) {\n    margin-right: 16px;\n    margin-bottom: 16px;\n\n    .ivu-form-item-label {\n      font-weight: 500;\n      color: #374151;\n      font-size: 14px;\n    }\n  }\n}\n\n// 表格与分页\n.pagination-container {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 16px;\n  background: #ffffff;\n  padding: 16px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .sales-stats-container {\n    padding: 8px;\n  }\n\n  .page-header {\n    flex-direction: column;\n    gap: 12px;\n    text-align: center;\n  }\n}\n", null]}