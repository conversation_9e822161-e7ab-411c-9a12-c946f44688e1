{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\orderDetall.vue?vue&type=template&id=4c40a268&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\orderDetall.vue", "mtime": 1694567788000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.orderDetail.userInfo)?_c('div',{staticClass:\"order_detail\"},[_c('div',{staticClass:\"msg-box\"},[_c('div',{staticClass:\"box-title\"},[_vm._v(\"收货信息\")]),_c('div',{staticClass:\"msg-wrapper\"},[_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"用户昵称：\")]),_vm._v(_vm._s(_vm.orderDetail.userInfo.nickname)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"收货人：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.real_name)+\"\\n                \")])]),_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"联系电话：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.user_phone)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"收货地址：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.user_address)+\"\\n                \")])])])]),_c('div',{staticClass:\"msg-box\",staticStyle:{\"border\":\"none\"}},[_c('div',{staticClass:\"box-title\"},[_vm._v(\"订单信息\")]),_c('div',{staticClass:\"msg-wrapper\"},[_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"订单ID：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.order_id)+\"\\n                \")]),_c('div',{staticClass:\"item\",staticStyle:{\"color\":\"red\"}},[_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(\"订单状态：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo._status._title)+\"\\n                \")])]),_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"商品总数：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.total_num)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"商品总价：\")]),_vm._v(\"￥\"+_vm._s(_vm.orderDetail.orderInfo.total_price)+\"\\n                \")])]),_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"交付邮费：\")]),_vm._v(\"￥\"+_vm._s(_vm.orderDetail.orderInfo.pay_postage)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"优惠券金额：\")]),_vm._v(\"￥\"+_vm._s(_vm.orderDetail.orderInfo.coupon_price)+\"\\n                \")])]),_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"会员商品优惠：\")]),_vm._v(\"￥\"+_vm._s(_vm.orderDetail.orderInfo.vip_true_price||0.00)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"积分抵扣：\")]),_vm._v(\"￥\"+_vm._s(_vm.orderDetail.orderInfo.deduction_price||0.00)+\"\\n                \")])]),_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"实际支付：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.pay_price)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"首单优惠：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.first_order_price)+\"\\n                \")])]),_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"创建时间：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.add_time)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"支付方式：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo._status._payType)+\"\\n                \")])]),_c('div',{staticClass:\"msg-item\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"推广人：\")]),_vm._v(_vm._s(_vm.orderDetail.userInfo.spread_name)+\"\\n                \")]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"商家备注：\")]),_vm._v(_vm._s(_vm.orderDetail.orderInfo.mark)+\"\\n                \")])])])]),_c('div',{staticClass:\"goods-box\"},[_c('Table',{attrs:{\"columns\":_vm.columns1,\"data\":_vm.orderList},scopedSlots:_vm._u([{key:\"id\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_vm._v(\"\\n                \"+_vm._s(row.productInfo.id)+\"\\n            \")]}},{key:\"name\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',{staticClass:\"product_info\"},[_c('img',{attrs:{\"src\":row.productInfo.image,\"alt\":\"\"}}),_c('p',[_vm._v(_vm._s(row.productInfo.store_name))])])]}},{key:\"className\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_vm._v(\"\\n                \"+_vm._s(row.class_name)+\"\\n            \")]}},{key:\"price\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_vm._v(\"\\n                \"+_vm._s(row.productInfo.attrInfo.price)+\"\\n            \")]}},{key:\"total_num\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_vm._v(\"\\n                \"+_vm._s(_vm.orderDetail.orderInfo.total_num)+\"\\n            \")]}}],null,false,4211245526)})],1),(_vm.spinShow)?_c('Spin',{attrs:{\"fix\":\"\"}}):_vm._e()],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}