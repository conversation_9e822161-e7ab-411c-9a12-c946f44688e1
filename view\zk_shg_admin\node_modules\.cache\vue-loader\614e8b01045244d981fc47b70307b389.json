{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue?vue&type=template&id=26050d6d&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue", "mtime": 1675991652000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <div>\n    <Table\n      :columns=\"columns\"\n      :data=\"orderList\"\n      ref=\"table\"\n      :loading=\"loading\"\n      highlight-row\n      no-data-text=\"暂无数据\"\n      no-filtered-data-text=\"暂无筛选结果\"\n      @on-selection-change=\"onSelectTab\"\n      @on-select-all=\"selectAll\"\n      @on-select-all-cancel=\"selectAll\"\n      @on-select-cancel=\"onSelectCancel\"\n      class=\"orderData\"\n    >\n      <template slot-scope=\"{ row, index }\" slot=\"order_id\">\n        <span v-text=\"row.order_id\" style=\"display: block\"></span>\n        <span v-show=\"row.is_del == 1\" style=\"color: #ed4014; display: block\"\n          >用户已删除</span\n        >\n      </template>\n      <template slot-scope=\"{ row, index }\" slot=\"nickname\">\n        <a @click=\"showUserInfo(row)\">{{ row.nickname }}/{{ row.uid }}</a>\n      </template>\n      <template slot-scope=\"{ row, index }\" slot=\"info\">\n        <div class=\"tabBox\">\n          <div class=\"tabBox_img\" v-viewer>\n            <img v-lazy=\"row.image\" />\n          </div>\n          <span class=\"tabBox_tit\">\n            {{ row.store_name + \" | \" }}{{ row.suk ? row.suk : \"\" }}\n          </span>\n          <span class=\"tabBox_pice\">x {{row.total_num}}</span>\n        </div>\n      </template>\n      <template slot-scope=\"{ row }\" slot=\"status_name\">\n          <!--待发货-->\n          <Tag color=\"blue\" size=\"large\" v-show=\"row.status == 0\">{{row.status_name}}</Tag>\n          <!--待收货-->\n          <Tag color=\"volcano\" size=\"large\" v-show=\"row.status == 1\">{{row.status_name}}</Tag>\n          <!--待评价-->\n         <Tag color=\"volcano\" size=\"large\" v-show=\"row.status == 2\">{{row.status_name}}</Tag>\n         <!--已完成-->\n          <Tag color=\"cyan\" size=\"large\" v-show=\"row.status == 3\">{{row.status_name}}</Tag>\n        </template>\n      <template slot-scope=\"{ row, index }\" slot=\"action\">\n        <!--        <a @click=\"edit(row)\" v-if=\"row.status === 1\">编辑</a>-->\n        <a :disabled=\"openErp\" @click=\"sendOrder(row)\" v-if=\"row.status === 1\">发送货</a>\n       <!-- <a @click=\"delivery(row)\" v-if=\"row.status === 2\">配送信息</a> -->\n        <Divider type=\"vertical\" v-if=\"row.status === 1\" />\n\t\t\t\t<a @click=\"changeMenu(row,'2')\">订单详情</a>\n       <!-- <template>\n          <Dropdown @on-click=\"changeMenu(row, $event)\">\n            <a href=\"javascript:void(0)\">\n              更多\n              <Icon type=\"ios-arrow-down\"></Icon>\n            </a>\n            <DropdownMenu slot=\"list\">\n              <DropdownItem name=\"2\">订单详情</DropdownItem>\n              <DropdownItem name=\"3\">订单记录</DropdownItem>\n              <DropdownItem\n                name=\"11\"\n                v-show=\"row.status >= 1 && row.express_dump\"\n                >电子面单打印</DropdownItem\n              >\n              <DropdownItem name=\"10\" v-show=\"row.status >= 1\"\n                >小票打印</DropdownItem\n              >\n              <DropdownItem name=\"4\" v-show=\"row.status !== 4\"\n                >订单备注</DropdownItem\n              >\n              <DropdownItem name=\"8\" v-show=\"row.status === 2\"\n                >已收货</DropdownItem\n              >\n              <DropdownItem name=\"9\">删除订单</DropdownItem>\n            </DropdownMenu>\n          </Dropdown>\n        </template> -->\n      </template>\n    </Table>\n    <div class=\"acea-row row-right page\">\n      <Page\n        :total=\"page.total\"\n        :current=\"page.pageNum\"\n        show-elevator\n        show-total\n        @on-change=\"pageChange\"\n        :page-size=\"page.pageSize\"\n        @on-page-size-change=\"limitChange\"\n        show-sizer\n      />\n    </div>\n    <!-- 编辑 退款 退积分 不退款-->\n    <edit-from\n      ref=\"edits\"\n      :FromData=\"FromData\"\n      @submitFail=\"submitFail\"\n    ></edit-from>\n    <!-- 会员详情-->\n    <user-details ref=\"userDetails\" fromType=\"order\"></user-details>\n    <!-- 详情 -->\n    <details-from\n      ref=\"detailss\"\n      :orderDatalist=\"orderDatalist\"\n      :orderId=\"orderId\"\n\t  :row-active=\"rowActive\"\n\t  :openErp=\"openErp\"\n    ></details-from>\n    <!-- 备注 -->\n    <order-remark\n      ref=\"remarks\"\n      :orderId=\"orderId\"\n      @submitFail=\"submitFail\"\n    ></order-remark>\n    <!-- 记录 -->\n    <order-record ref=\"record\"></order-record>\n    <!-- 发送货 -->\n    <order-send\n      ref=\"send\"\n      :orderId=\"orderId\"\n      @submitFail=\"submitFail(1)\"\n    ></order-send>\n  </div>\n", null]}