{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobilePage\\home_bargain.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from 'vuex'; // import theme from \"@/mixins/theme\";\n\nimport Setting from '@/setting';\nexport default {\n  name: 'home_bargain',\n  cname: '砍价',\n  icon: '#iconzujian-kanjia',\n  configName: 'c_home_bargain',\n  type: 1,\n  // 0 基础组件 1 营销组件 2工具组件\n  defaultName: 'bargain',\n  // 外面匹配名称\n  props: {\n    index: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    colorStyle: {\n      type: null\n    }\n  },\n  computed: _objectSpread({}, mapState('admin/mobildConfig', ['defaultArray'])),\n  watch: {\n    pageData: {\n      handler: function handler(nVal, oVal) {\n        this.setConfig(nVal);\n      },\n      deep: true\n    },\n    num: {\n      handler: function handler(nVal, oVal) {\n        var data = this.$store.state.admin.mobildConfig.defaultArray[nVal];\n        this.setConfig(data);\n      },\n      deep: true\n    },\n    'defaultArray': {\n      handler: function handler(nVal, oVal) {\n        var data = this.$store.state.admin.mobildConfig.defaultArray[this.num];\n        this.setConfig(data);\n      },\n      deep: true\n    }\n  },\n  // mixins: [theme],\n  data: function data() {\n    return {\n      // 默认初始化数据禁止修改\n      defaultConfig: {\n        cname: '砍价',\n        name: 'bargain',\n        timestamp: this.num,\n        isHide: false,\n        setUp: {\n          tabVal: 0\n        },\n        titleLeft: '头部设置',\n        titleGoodsList: '商品列表',\n        titleGoods: '商品设置',\n        titleRight: '头部样式',\n        titleGoodsStyle: '商品样式',\n        titleCurrency: '通用样式',\n        styleConfig: {\n          title: '选择风格',\n          tabVal: 1,\n          tabList: [{\n            name: '背景色'\n          }, {\n            name: '背景图片'\n          }]\n        },\n        imgBgConfig: {\n          info: '建议：710px * 96px',\n          url: Setting.apiBaseURL.replace(/adminapi/, '') + 'statics/images/bargainBg.png',\n          type: 'code',\n          delType: 0,\n          name: '背景图片'\n        },\n        titleConfig: {\n          title: '标题类型',\n          tabVal: 0,\n          tabList: [{\n            name: '图片'\n          }, {\n            name: '文字'\n          }]\n        },\n        imgConfig: {\n          info: '建议：140px * 32px',\n          url: require('@/assets/images/bargain02.png'),\n          type: 'code',\n          delType: 0,\n          name: '标题图片'\n        },\n        imgColorConfig: {\n          info: '建议：140px * 32px',\n          url: require('@/assets/images/bargain01.png'),\n          type: 'code',\n          delType: 0,\n          name: '标题图片'\n        },\n        titleTxtConfig: {\n          title: '标题文字',\n          value: '疯狂砍价',\n          place: '请输入标题文字',\n          max: 6\n        },\n        tipTxtConfig: {\n          title: '提示文字',\n          value: '低至0元免费拿',\n          place: '请输入提示文字',\n          max: 15\n        },\n        rightBntConfig: {\n          title: '右侧按钮',\n          value: '更多',\n          place: '请输入右侧按钮',\n          max: 6\n        },\n        goodStyleConfig: {\n          title: '选择风格',\n          tabVal: 0,\n          tabList: [{\n            name: '单列展示'\n          }, {\n            name: '两列展示(纵向)'\n          }, {\n            name: '三列展示'\n          }, {\n            name: '左右滑动展示'\n          }]\n        },\n        numberConfig: {\n          title: '商品数量',\n          val: 3,\n          min: 1\n        },\n        checkboxInfo: {\n          title: '展示信息',\n          name: 'checkboxInfo',\n          type: [0, 1, 2, 3],\n          list: [{\n            id: 0,\n            name: '商品名称'\n          }, {\n            id: 1,\n            name: '参与人数'\n          }, {\n            id: 2,\n            name: '商品价格'\n          }, {\n            id: 3,\n            name: '商品原价'\n          }]\n        },\n        bargainConfig: {\n          title: '砍价按钮',\n          tabVal: 0,\n          tabList: [{\n            name: '显示'\n          }, {\n            name: '隐藏'\n          }]\n        },\n        headerBgColor: {\n          title: '背景颜色',\n          name: 'headerBgColor',\n          default: [{\n            item: '#fff'\n          }, {\n            item: '#fff'\n          }],\n          color: [{\n            item: '#fff'\n          }, {\n            item: '#fff'\n          }]\n        },\n        titleText: {\n          title: '标题文字',\n          tabVal: 0,\n          tabList: [{\n            name: '加粗',\n            style: 'bold'\n          }, {\n            name: '正常',\n            style: 'normal'\n          }, {\n            name: '倾斜',\n            style: 'italic'\n          }]\n        },\n        titleColor: {\n          title: '标题颜色',\n          name: 'titleColor',\n          default: [{\n            item: '#282828'\n          }],\n          color: [{\n            item: '#282828'\n          }]\n        },\n        titleNumber: {\n          title: '标题字号',\n          val: 16,\n          min: 0\n        },\n        headerBntColor: {\n          title: '按钮颜色',\n          name: 'headerBntColor',\n          default: [{\n            item: '#fff'\n          }],\n          color: [{\n            item: '#fff'\n          }]\n        },\n        headerBntColor2: {\n          title: '按钮颜色',\n          name: 'headerBntColor2',\n          default: [{\n            item: '#999'\n          }],\n          color: [{\n            item: '#999'\n          }]\n        },\n        bntNumber: {\n          title: '按钮字号',\n          val: 12,\n          min: 0\n        },\n        tipsColor: {\n          title: '提示文字',\n          name: 'tipsColor',\n          default: [{\n            item: '#fff'\n          }],\n          color: [{\n            item: '#fff'\n          }]\n        },\n        tipsColor2: {\n          title: '提示文字',\n          name: 'tipsColor2',\n          default: [{\n            item: '#999'\n          }],\n          color: [{\n            item: '#999'\n          }]\n        },\n        dividerColor: {\n          title: '分割线',\n          name: 'dividerColor',\n          default: [{\n            item: '#DDDDDD'\n          }],\n          color: [{\n            item: '#DDDDDD'\n          }]\n        },\n        filletImg: {\n          title: '图片圆角',\n          type: 0,\n          list: [{\n            val: \"全部\",\n            icon: \"iconcaozuo-zhengti\"\n          }, {\n            val: \"单个\",\n            icon: \"iconcaozuo-bianjiao\"\n          }],\n          valName: '圆角值',\n          val: 0,\n          min: 0,\n          valList: [{\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }]\n        },\n        goodsName: {\n          title: '商品名称',\n          tabVal: 1,\n          tabList: [{\n            name: '加粗',\n            style: 'bold'\n          }, {\n            name: '正常',\n            style: 'normal'\n          }]\n        },\n        goodsNameColor: {\n          title: '商品名称',\n          name: 'goodsNameColor',\n          default: [{\n            item: '#333333'\n          }],\n          color: [{\n            item: '#333333'\n          }]\n        },\n        goodsPriceColor: {\n          title: '商品原价',\n          name: 'goodsPriceColor',\n          default: [{\n            item: '#999999'\n          }],\n          color: [{\n            item: '#999999'\n          }]\n        },\n        toneConfig: {\n          title: '色调',\n          tabVal: 0,\n          tabList: [{\n            name: '跟随主题风格'\n          }, {\n            name: '自定义'\n          }]\n        },\n        joinNumColor: {\n          title: '参与人数',\n          name: 'joinNumColor',\n          default: [{\n            item: '#E93323'\n          }],\n          color: [{\n            item: '#E93323'\n          }]\n        },\n        joinNumColor2: {\n          title: '参与人数',\n          name: 'joinNumColor2',\n          default: [{\n            item: '#fff'\n          }],\n          color: [{\n            item: '#fff'\n          }]\n        },\n        joinBgColor: {\n          title: '参与背景',\n          name: 'progressColor',\n          default: [{\n            item: '#FF7931'\n          }, {\n            item: '#E93323'\n          }],\n          color: [{\n            item: '#FF7931'\n          }, {\n            item: '#E93323'\n          }]\n        },\n        bargainPriceColor: {\n          title: '砍价价格',\n          name: 'bargainPriceColor',\n          default: [{\n            item: '#E93323'\n          }],\n          color: [{\n            item: '#E93323'\n          }]\n        },\n        goodsBntColor: {\n          title: '按钮颜色',\n          name: 'goodsBntColor',\n          default: [{\n            item: '#FF7931'\n          }, {\n            item: '#E93323'\n          }],\n          color: [{\n            item: '#FF7931'\n          }, {\n            item: '#E93323'\n          }]\n        },\n        goodsBntTxtColor: {\n          title: '按钮文字',\n          name: 'goodsBntTxtColor',\n          default: [{\n            item: '#fff'\n          }],\n          color: [{\n            item: '#fff'\n          }]\n        },\n        moduleColor: {\n          title: '组件背景',\n          default: [{\n            item: '#fff'\n          }, {\n            item: '#fff'\n          }],\n          color: [{\n            item: '#fff'\n          }, {\n            item: '#fff'\n          }]\n        },\n        bottomBgColor: {\n          title: '底部背景',\n          default: [{\n            item: '#f5f5f5'\n          }],\n          color: [{\n            item: '#f5f5f5'\n          }]\n        },\n        topConfig: {\n          title: '上边距',\n          val: 0,\n          min: 0\n        },\n        bottomConfig: {\n          title: '下边距',\n          val: 0,\n          min: 0\n        },\n        prConfig: {\n          title: '左右边距',\n          val: 10,\n          min: 0\n        },\n        mbConfig: {\n          title: '页面上间距',\n          val: 0,\n          min: 0\n        },\n        fillet: {\n          title: '背景圆角',\n          type: 0,\n          list: [{\n            val: \"全部\",\n            icon: \"iconcaozuo-zhengti\"\n          }, {\n            val: \"单个\",\n            icon: \"iconcaozuo-bianjiao\"\n          }],\n          valName: '圆角值',\n          val: 8,\n          min: 0,\n          valList: [{\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }, {\n            val: 0\n          }]\n        }\n      },\n      pageData: {},\n      imgUrl: '',\n      imgBgUrl: '',\n      tipsColor: '',\n      tipsColor2: '',\n      dividerColor: '',\n      rightBntTxt: '',\n      tipTxt: '',\n      headerBntColor: '',\n      headerBntColor2: '',\n      bntNumber: 0,\n      styleConfig: 0,\n      headerBgColorLeft: '',\n      headerBgColorRight: '',\n      imgColorUrl: '',\n      titleConfig: 0,\n      titleTxtConfig: '',\n      bgColor: '',\n      bottomBgColor: '',\n      mTop: 0,\n      topConfig: 0,\n      bottomConfig: 0,\n      prConfig: 0,\n      titleText: '',\n      titleTabVal: 0,\n      checkboxInfo: [],\n      imgRadius: 0,\n      bgRadius: 0,\n      bgRadius2: 0,\n      goodsName: '',\n      goodsNameColor: '',\n      goodsPriceColor: '',\n      toneConfig: 0,\n      goodsBntColorLeft: '',\n      goodsBntColorRight: '',\n      goodStyleConfig: 0,\n      goodsBntTxtColor: '',\n      bargainConfig: 0,\n      numberConfig: 1,\n      titleColor: '',\n      titleNumber: 0,\n      joinNumColor: '',\n      joinNumColor2: '',\n      bargainPriceColor: '',\n      joinBgColorLeft: '',\n      joinBgColorRight: '',\n      themeColor: '',\n      themeColor2: ''\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      _this.pageData = _this.$store.state.admin.mobildConfig.defaultArray[_this.num];\n\n      _this.setConfig(_this.pageData);\n    });\n  },\n  methods: {\n    setConfig: function setConfig(data) {\n      if (!data) return;\n\n      if (data.mbConfig) {\n        this.imgUrl = data.imgConfig.url;\n        this.imgBgUrl = data.imgBgConfig.url;\n        this.imgColorUrl = data.imgColorConfig.url;\n        this.tipsColor = data.tipsColor.color[0].item;\n        this.tipsColor2 = data.tipsColor2.color[0].item;\n        this.dividerColor = data.dividerColor.color[0].item;\n        this.rightBntTxt = data.rightBntConfig.value;\n        this.tipTxt = data.tipTxtConfig.value;\n        this.headerBntColor = data.headerBntColor.color[0].item;\n        this.headerBntColor2 = data.headerBntColor2.color[0].item;\n        this.bntNumber = data.bntNumber.val;\n        this.styleConfig = data.styleConfig.tabVal;\n        this.headerBgColorLeft = data.headerBgColor.color[0].item;\n        this.headerBgColorRight = data.headerBgColor.color[1].item;\n        this.titleConfig = data.titleConfig.tabVal;\n        this.titleTxtConfig = data.titleTxtConfig.value;\n        var bgColorLeft = data.moduleColor.color[0].item;\n        var bgColorRight = data.moduleColor.color[1].item;\n        this.bgColor = \"linear-gradient(90deg,\".concat(bgColorLeft, \" 0%,\").concat(bgColorRight, \" 100%)\");\n        this.bottomBgColor = data.bottomBgColor.color[0].item;\n        this.mTop = data.mbConfig.val;\n        this.topConfig = data.topConfig.val;\n        this.bottomConfig = data.bottomConfig.val;\n        this.prConfig = data.prConfig.val;\n        var tabVal = data.titleText.tabVal;\n        this.titleTabVal = tabVal;\n        this.titleText = data.titleText.tabList[tabVal].style;\n        this.checkboxInfo = data.checkboxInfo.type;\n        var filletImg = data.filletImg.type;\n        var filletValImg = data.filletImg.val;\n        var valListImg = data.filletImg.valList;\n        this.imgRadius = filletImg ? valListImg[0].val + 'px ' + valListImg[1].val + 'px ' + valListImg[3].val + 'px ' + valListImg[2].val + 'px' : filletValImg + 'px';\n        var fillet = data.fillet.type;\n        var filletVal = data.fillet.val;\n        var valList = data.fillet.valList;\n        this.bgRadius = fillet ? valList[0].val + 'px ' + valList[1].val + 'px 0 0' : filletVal + 'px ' + filletVal + 'px 0 0';\n        this.bgRadius2 = fillet ? '0 0 ' + valList[3].val + 'px ' + valList[2].val + 'px' : '0 0 ' + filletVal + 'px ' + filletVal + 'px';\n        var goodsTabVal = data.goodsName.tabVal;\n        this.goodsName = data.goodsName.tabList[goodsTabVal].style;\n        this.goodsNameColor = data.goodsNameColor.color[0].item;\n        this.goodsPriceColor = data.goodsPriceColor.color[0].item;\n        this.toneConfig = data.toneConfig.tabVal;\n        this.goodsBntColorLeft = data.goodsBntColor.color[0].item;\n        this.goodsBntColorRight = data.goodsBntColor.color[1].item;\n        this.goodStyleConfig = data.goodStyleConfig.tabVal;\n        this.goodsBntTxtColor = data.goodsBntTxtColor.color[0].item;\n        this.bargainConfig = data.bargainConfig.tabVal;\n        this.numberConfig = data.numberConfig.val;\n        this.titleColor = data.titleColor.color[0].item;\n        this.titleNumber = data.titleNumber.val;\n        this.joinNumColor = data.joinNumColor.color[0].item;\n        this.joinNumColor2 = data.joinNumColor2.color[0].item;\n        this.bargainPriceColor = data.bargainPriceColor.color[0].item;\n        this.joinBgColorLeft = data.joinBgColor.color[0].item;\n        this.joinBgColorRight = data.joinBgColor.color[1].item;\n        this.themeColor = \"linear-gradient(90deg,\".concat(this.colorStyle.theme, \" 0%,\").concat(this.colorStyle.gradient, \" 100%)\");\n        this.themeColor2 = \"linear-gradient(270deg,\".concat(this.colorStyle.theme, \" 0%,\").concat(this.colorStyle.gradient, \" 100%)\");\n      }\n    }\n  }\n};", null]}