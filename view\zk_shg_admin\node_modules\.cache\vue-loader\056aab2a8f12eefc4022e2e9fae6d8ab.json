{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue?vue&type=template&id=a7ba3238&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderStatistics\\index.vue", "mtime": 1709630624583}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 供应商-供应商流水 -->\n<div>\n  <Card :bordered=\"false\" dis-hover class=\"ivu-mt box\">\n    <Form\n      ref=\"formValidate\"\n      inline\n      :model=\"formValidate\"\n      :label-width=\"labelWidth\"\n      :label-position=\"labelPosition\"\n      @submit.native.prevent\n    >\n      <FormItem label=\"创建时间：\">\n        <DatePicker\n          :editable=\"false\"\n          @on-change=\"onchangeTime\"\n          :value=\"timeVal\"\n          format=\"yyyy/MM/dd\"\n          type=\"datetimerange\"\n          placement=\"bottom-start\"\n          placeholder=\"自定义时间\"\n          class=\"input-add\"\n          :options=\"options\"\n        ></DatePicker>\n      </FormItem>\n      <FormItem label=\"订单搜索：\" label-for=\"status1\">\n        <Input\n          v-model=\"formValidate.keyword\"\n          placeholder=\"请输入交易单号/交易人\"\n          class=\"input-add\"\n        />\n      </FormItem>\n      <FormItem>\n        <Button\n          type=\"primary\"\n          @click=\"search()\"\n          style=\"margin-left: -75px; margin-right: 14px\"\n          >查询</Button\n        >\n        <Button @click=\"exports\">导出</Button>\n      </FormItem>\n    </Form>\n  </Card>\n  \n  <Card :bordered=\"false\" dis-hover class=\"ive-mt tablebox\" :padding=\"20\">\n    <div class=\"table\">\n      <Table\n        :columns=\"columns\"\n        :data=\"orderList\"\n        ref=\"table\"\n        :loading=\"loading\"\n        highlight-row\n        no-userFrom-text=\"暂无数据\"\n        no-filtered-userFrom-text=\"暂无筛选结果\"\n      >\n        <template slot-scope=\"{ row, index }\" slot=\"number\">\n          <!-- <span class=\"color\">{{row.number}}</span> -->\n          <span v-if=\"row.pm == 0\" class=\"colorgreen\"\n            >- {{ row.number }}</span\n          >\n          <span v-if=\"row.pm == 1\" class=\"colorred\">+ {{ row.number }}</span>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"user_nickname\">\n          <span>{{ row.uid ? row.user_nickname : '游客' }}</span>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"action\">\n          <a @click=\"remark(row)\">备注</a>\n        </template>\n      </Table>\n    </div>\n    <div class=\"acea-row row-right page\">\n      <Page\n        :total=\"total\"\n        :current=\"formValidate.page\"\n        show-elevator\n        show-total\n        @on-change=\"pageChange\"\n        :page-size=\"formValidate.limit\"\n      />\n    </div>\n  </Card>\n  <!-- 备注 -->\n  <Modal\n    v-model=\"modalmark\"\n    scrollable\n    title=\"请修改内容\"\n    class=\"order_box\"\n    :closable=\"false\"\n    :mask-closable=\"false\"\n  >\n    <Form\n      ref=\"remarks\"\n      :model=\"remarks\"\n      :label-width=\"80\"\n      @submit.native.prevent\n    >\n      <FormItem label=\"备注：\">\n        <Input\n          v-model=\"remarks.mark\"\n          maxlength=\"200\"\n          show-word-limit\n          type=\"textarea\"\n          placeholder=\"请填写备注~\"\n          style=\"width: 100%\"\n        />\n      </FormItem>\n    </Form>\n    <div slot=\"footer\">\n      <Button type=\"primary\" @click=\"putRemark()\">提交</Button>\n      <Button @click=\"cancel()\">取消</Button>\n    </div>\n  </Modal>\n</div>\n", null]}