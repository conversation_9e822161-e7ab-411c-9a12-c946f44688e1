{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRecord.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRecord.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getIntegralOrderRecord } from '@/api/marketing';\nexport default {\n    name: 'orderRecord',\n    data () {\n        return {\n            modals: false,\n            loading: false,\n            recordData: [],\n            page: {\n                page: 1, // 当前页\n                limit: 10 // 每页显示条数\n            },\n            columns: [\n                {\n                    title: '订单ID',\n                    key: 'oid',\n                    align: 'center',\n                    minWidth: 40\n                },\n                {\n                    title: '操作记录',\n                    key: 'change_message',\n                    align: 'center',\n                    minWidth: 280\n                },\n                {\n                    title: '操作时间',\n                    key: 'change_time',\n                    align: 'center',\n                    minWidth: 100\n                }\n            ]\n        }\n    },\n    methods: {\n        pageChange (index) {\n            this.page.pageNum = index\n            this.getList();\n        },\n        getList (id) {\n            let data = {\n                id: id,\n                datas: this.page\n            }\n            this.loading = true;\n            getIntegralOrderRecord(data).then(async res => {\n                this.recordData = res.data;\n                this.loading = false;\n            }).catch(res => {\n                this.loading = false;\n                this.$Message.error(res.msg);\n            })\n        }\n    }\n}\n", null]}