{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\label\\cate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\label\\cate.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState } from \"vuex\";\nimport { userLabelAll, userLabelApi, userLabelAddApi, userLabelEdit, userLabelCreate, workLabelSync } from \"@/api/user\";\nexport default {\n  name: \"user_label\",\n  data: function data() {\n    return {\n      grid: {\n        xl: 24,\n        lg: 24,\n        md: 24,\n        sm: 24,\n        xs: 24\n      },\n      loading: false,\n      columns1: [{\n        title: \"ID\",\n        key: \"id\",\n        Width: 80,\n        minWidth: 50,\n        maxWidth: 80\n      }, {\n        title: \"标签名称\",\n        key: \"label_name\" // minWidth: 600,\n\n      }, {\n        title: \"操作\",\n        slot: \"action\",\n        // fixed: \"right\",\n        align: 'center',\n        minWidth: 120,\n        maxWidth: 140\n      }],\n      labelFrom: {\n        page: 1,\n        limit: 10,\n        label_cate: \"\"\n      },\n      labelLists: [],\n      total: 0,\n      theme3: \"light\",\n      labelSort: [],\n      sortName: \"\"\n    };\n  },\n  computed: _objectSpread({}, mapState(\"admin/layout\", [\"isMobile\"]), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 75;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    }\n  }),\n  created: function created() {\n    this.getUserLabelAll();\n  },\n  methods: {\n    // 添加\n    add: function add() {\n      var _this = this;\n\n      var id = this.labelFrom.label_cate ? this.labelFrom.label_cate : 0;\n      this.$modalForm(userLabelAddApi(0, {\n        label_cate: id\n      })).then(function () {\n        return _this.getList();\n      });\n    },\n    // 分组列表\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      userLabelApi(this.labelFrom).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  data = res.data;\n                  _this2.labelLists = data.list;\n                  _this2.total = data.count;\n                  _this2.loading = false;\n\n                case 4:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.labelFrom.page = index;\n      this.getList();\n    },\n    // 修改\n    edit: function edit(id) {\n      var _this3 = this;\n\n      this.$modalForm(userLabelAddApi(id)).then(function () {\n        return _this3.getList();\n      });\n    },\n    // 删除\n    del: function del(row, tit, num) {\n      var _this4 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"user/user_label/del/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this4.$Message.success(res.msg);\n\n        _this4.labelLists.splice(num, 1);\n\n        if (!_this4.labelLists.length) {\n          _this4.labelFrom.page = _this4.labelFrom.page == 1 ? 1 : _this4.labelFrom.page - 1;\n        }\n\n        _this4.getList();\n      }).catch(function (res) {\n        _this4.$Message.error(res.msg);\n      });\n    },\n    // 标签分类\n    getUserLabelAll: function getUserLabelAll(key) {\n      var _this5 = this;\n\n      userLabelAll().then(function (res) {\n        var obj = {\n          name: \"全部\",\n          id: \"\"\n        };\n        res.data.unshift(obj);\n        res.data.forEach(function (el) {\n          el.status = false;\n        });\n\n        if (!key) {\n          _this5.sortName = res.data[0].id;\n          _this5.labelFrom.label_cate = res.data[0].id;\n\n          _this5.getList();\n        }\n\n        _this5.labelSort = res.data;\n      });\n    },\n    // 显示标签小菜单\n    showMenu: function showMenu(item) {\n      this.labelSort.forEach(function (el) {\n        if (el.id == item.id) {\n          el.status = item.status ? false : true;\n        } else {\n          el.status = false;\n        }\n      });\n    },\n    //编辑标签\n    labelEdit: function labelEdit(item) {\n      var _this6 = this;\n\n      this.$modalForm(userLabelEdit(item.id)).then(function () {\n        return _this6.getUserLabelAll(1);\n      });\n    },\n    // 添加分类\n    addSort: function addSort() {\n      var _this7 = this;\n\n      this.$modalForm(userLabelCreate()).then(function () {\n        return _this7.getUserLabelAll();\n      });\n    },\n    deleteSort: function deleteSort(row, tit, num) {\n      var _this8 = this;\n\n      var delfromData = {\n        title: tit,\n        num: num,\n        url: \"user/user_label_cate/\".concat(row.id),\n        method: \"DELETE\",\n        ids: \"\"\n      };\n      this.$modalSure(delfromData).then(function (res) {\n        _this8.$Message.success(res.msg);\n\n        _this8.labelSort.splice(num, 1);\n\n        _this8.labelSort = [];\n\n        _this8.getUserLabelAll();\n      }).catch(function (res) {\n        _this8.$Message.error(res.msg);\n      });\n    },\n    bindMenuItem: function bindMenuItem(name) {\n      this.labelSort.forEach(function (el) {\n        el.status = false;\n      });\n      this.labelFrom.page = 1;\n      this.labelFrom.label_cate = name.id;\n      this.getList();\n    },\n    userLabeSync: function userLabeSync() {\n      var _this9 = this;\n\n      workLabelSync().then(function (res) {\n        _this9.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this9.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}