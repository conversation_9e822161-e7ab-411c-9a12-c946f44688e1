{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue?vue&type=style&index=0&id=8d17409a&lang=stylus&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue", "mtime": 1693985518000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\css-loader\\index.js", "mtime": 1725352507056}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1725352509392}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\stylus-loader\\index.js", "mtime": 1725352506631}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.label-wrapper {\n  .list {\n    display: flex;\n    flex-wrap: wrap;\n\n    .label-item {\n      margin: 10px 8px 10px 0;\n      padding: 3px 8px;\n      background: #EEEEEE;\n      color: #333333;\n      border-radius: 2px;\n      cursor: pointer;\n      font-size: 12px;\n\n      &.on {\n        color: #fff;\n        background: #1890FF;\n      }\n    }\n  }\n\n  .footer {\n    display: flex;\n    justify-content: flex-end;\n    margin-top: 40px;\n\n    button {\n      margin-left: 10px;\n    }\n  }\n}\n\n.btn {\n  width: 60px;\n  height: 24px;\n}\n\n.title {\n  font-size: 13px;\n}\n\n.list-box {\n  overflow-y: auto;\n  overflow-x: hidden;\n  max-height: 240px;\n  &::-webkit-scrollbar {\n    width: 0;\n  }\n}\n", null]}