{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\1688\\orderBill\\index.vue", "mtime": 1709543824155}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { mapState } from 'vuex';\r\nimport {\r\n  supplierFinanceInfo,\r\n  exportTableList,\r\n} from '@/api/supplier';\r\nimport cardsData from \"@/components/cards/cards\";\r\nimport timeOptions from '@/utils/timeOptions';\r\nimport { getBalance,getBalanceChange } from \"@/api/vop\";\r\nexport default {\r\n  name: 'orderStatistics',\r\n  components: { cardsData},\r\n  data() {\r\n    return {\r\n      cardLists: [],\r\n      extractStatistics: {},\r\n      // 旧\r\n      modalmark: false,\r\n      supplierList: [],\r\n      total: 0,\r\n      grid: {\r\n        xl: 7,\r\n        lg: 7,\r\n        md: 12,\r\n        sm: 24,\r\n        xs: 24,\r\n      },\r\n      loading: false,\r\n      columns: [\r\n        {\r\n          title: '备注',\r\n          key: 'notePub',\r\n          minWidth: 300,\r\n        },\r\n        {\r\n          title: '交易时间',\r\n          key: 'createdDate',\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '交易金额',\r\n          slot: 'number',\r\n          minWidth: 40,\r\n        },\r\n        {\r\n          title: '交易账号',\r\n          key: 'pin',\r\n          minWidth: 40,\r\n        },\r\n        {\r\n          title: '交易号',\r\n          key: 'tradeNo',\r\n          minWidth: 40,\r\n        },\r\n        {\r\n          title: '业务类型',\r\n          slot: 'type',\r\n          minWidth: 40,\r\n        },\r\n        {\r\n          title: '业务类型名称',\r\n          key: 'tradeTypeName',\r\n          minWidth: 80,\r\n        },\r\n      ],\r\n      orderList: [],\r\n      formValidate: {\r\n        keyword: '',\r\n        data: '',\r\n        page: 1,\r\n        limit: 20,\r\n      },\r\n      timeVal: [],\r\n      options: timeOptions,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState('admin/layout', ['isMobile']),\r\n    labelWidth() {\r\n      return this.isMobile ? undefined : 90;\r\n    },\r\n    labelPosition() {\r\n      return this.isMobile ? 'top' : 'left';\r\n    },\r\n  },\r\n  mounted() {\r\n    this.getList();\r\n    getBalance().then(async (res) => {\r\n        this.cardLists = res.data\r\n    }).catch((res) => {\r\n      this.loading = false;\r\n      this.$Message.error(res.msg);\r\n    });\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      getBalanceChange(this.formValidate).then((res) => {\r\n        let data = res.data;\r\n        this.orderList = data.items;\r\n        this.total = data.pageItemTotal;\r\n        this.loading = false;\r\n      }).catch((res) => {\r\n        this.loading = false;\r\n        this.$Message.error(res.msg);\r\n      });;\r\n    },\r\n    search() {\r\n      this.formValidate.page = 1;\r\n      this.getList();\r\n    },\r\n    reset() {\r\n      this.formValidate = {\r\n        keyword: '',\r\n        data: '',\r\n        page: 1,\r\n        limit: 20,\r\n      };\r\n      this.timeVal = [];\r\n      this.getList();\r\n    },\r\n    // 选择时间\r\n    selectChange(tab) {\r\n      this.formValidate.page = 1;\r\n      this.formValidate.data = tab;\r\n      this.timeVal = [];\r\n      this.getList();\r\n    },\r\n    // 具体日期\r\n    onchangeTime(e) {\r\n      this.timeVal = e;\r\n      this.formValidate.data = this.timeVal[0] ? this.timeVal.join('-') : '';\r\n      this.formValidate.page = 1;\r\n      this.getList();\r\n    },\r\n    //分页\r\n    pageChange(status) {\r\n      this.formValidate.page = status;\r\n      this.getList();\r\n    },\r\n    getExcelData(data) {\r\n      return new Promise((resolve) => {\r\n        exportTableList(data).then((res) => resolve(res.data));\r\n      });\r\n    },\r\n  },\r\n};\r\n", null]}