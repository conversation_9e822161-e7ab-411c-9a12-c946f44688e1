{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue?vue&type=template&id=03378fd1&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div>\n    <Modal\n            v-model=\"val\"\n            title=\"商品属性\"\n            width=\"70%\"\n            @on-cancel=\"cancel\"\n    >\n        <div class=\"Modals\">\n            <Form  class=\"form\" ref=\"form\"  :label-width=\"labelWidth\" :label-position=\"labelPosition\">\n                <Row :gutter=\"24\" type=\"flex\">\n                    <Col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n                        <FormItem label=\"规格：\" prop=\"store_name\" label-for=\"store_name\">\n                            <Input placeholder=\"规格\" style=\"width:10%\" class=\"input\" :value=\"item\" v-for=\"(item,index) in specs\" :key=\"index\">\n                                  <Icon type=\"md-close\" slot=\"suffix\" />\n                            </Input>\n                            <Input placeholder=\"请输入\" v-model=\"specsVal\" style=\"width:10%\" class=\"input\">\n                               <Icon type=\"md-add\" slot=\"suffix\" @click=\"confirm\"/>\n                            </Input>\n                            <!--<Button type=\"primary\" icon=\"md-add\" @click=\"confirm\"></Button>-->\n                        </FormItem>\n                    </Col>\n                    <Col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n                        <FormItem :label=\"item.attr+':'\" prop=\"store_name\" label-for=\"store_name\" v-for=\"(item,index) in attrList\" :key=\"index\">\n                            <Tag type=\"border\" closable color=\"primary\" v-for=\"(itemn,index) in item.attrVal\" :key=\"index\">{{itemn}}</Tag>\n                            <Input placeholder=\"请输入\" v-model=\"item.inputVal\" style=\"width:10%\" class=\"input\">\n                               <Icon type=\"md-add\" slot=\"suffix\" @click=\"confirmAttr(index)\"/>\n                            </Input>\n                            <!--<Button type=\"primary\" icon=\"md-add\" @click=\"confirm\"></Button>-->\n                        </FormItem>\n                    </Col>\n                </Row>\n            </Form>\n        </div>\n        <div slot=\"footer\"></div>\n    </Modal>\n</div>\n", null]}