{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue?vue&type=template&id=c4aa0076&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_promotion.vue", "mtime": 1717551943000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"c_product\" v-if=\"configData\">\n        <div class=\"title\">{{configData.title}}</div>\n        <div class=\"list-box\">\n            <draggable\n                    class=\"dragArea list-group\"\n                    :list=\"configData.list\"\n                    group=\"peoples\"\n                    handle=\".move-icon\"\n            >\n                <div class=\"item\" v-for=\"(item,index) in configData.list\" :key=\"index\" @click=\"activeBtn(index)\" v-model=\"configData.tabCur\">\n\t\t\t\t\t<div class=\"acea-row\">\n\t\t\t\t\t\t<div class=\"move-icon\">\n\t\t\t\t\t\t    <span class=\"iconfont-diy iconxingzhuangjiehe\"></span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"content\">\n\t\t\t\t\t\t    <div class=\"con-item\" v-for=\"(list,key) in item.chiild\" :key=\"key\" v-if=\"key<(tabIndex == 0?2:1)\">\n\t\t\t\t\t\t        <span>{{list.title}}</span>\n\t\t\t\t\t\t        <div style=\"width: 100%\">\n\t\t\t\t\t\t            <Input v-model=\"list.val\" :placeholder=\"list.pla\" :maxlength=\"list.max\"/>\n\t\t\t\t\t\t        </div>\n\t\t\t\t\t\t    </div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"acea-row row-right\" v-if=\"configData.tabCur == index\">\n\t\t\t\t\t\t<div class=\"conter\">\n\t\t\t\t\t\t\t<div class=\"c_row-item\" v-if=\"tabIndex == 4\">\n\t\t\t\t\t\t\t\t<div class=\"c_label\">\n\t\t\t\t\t\t\t\t    上传图片\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"color-box\">\n\t\t\t\t\t\t\t\t\t<div class=\"box\" @click=\"modalPicTap('单选')\">\n\t\t\t\t\t\t\t\t\t\t<div class=\"pictrue acea-row row-center-wrapper\" v-if=\"item.image\">\n\t\t\t\t\t\t\t\t\t\t\t<img :src=\"item.image\" alt=\"\">\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"iconfont icondel_1\" @click.stop=\"bindPicDelete\"></div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"upload-box\" v-else><Icon type=\"ios-add\" size=\"40\" /></div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"c_row-item\">\n\t\t\t\t\t\t\t    <Col class=\"c_label\">\n\t\t\t\t\t\t\t        选择方式\n\t\t\t\t\t\t\t    </Col>\n\t\t\t\t\t\t\t    <Col class=\"color-box\">\n\t\t\t\t\t\t\t\t\t<Select v-model=\"item.tabVal\" @on-change=\"tabChange\">\n\t\t\t\t\t\t\t\t\t    <Option v-for=\"(itemn,indexn) in typeList\" :value=\"itemn.activeValue\" :key=\"indexn\">{{ itemn.title }}</Option>\n\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t    </Col>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"goods-box acea-row\" v-if=\"item.tabVal == 1\">\n\t\t\t\t\t\t\t\t<div class=\"title\">选择商品</div>\n\t\t\t\t\t\t\t\t<div class=\"list\">\n\t\t\t\t\t\t\t\t\t<draggable\n\t\t\t\t\t\t\t\t\t        class=\"dragArea list-group\"\n\t\t\t\t\t\t\t\t\t        :list=\"item.goodsList.list\"\n\t\t\t\t\t\t\t\t\t        group=\"peoples\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<div class=\"items\" v-for=\"(goods,gIndex) in item.goodsList.list\" :key=\"gIndex\" v-if=\"item.goodsList.list.length\">\n\t\t\t\t\t\t\t\t\t\t\t<img :src=\"goods.image\" alt=\"\">\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"iconfont-diy icondel_1\" @click.stop=\"bindGoodDelete(gIndex)\"></span>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t    <div class=\"add-item items\" @click=\"openGoods(index)\"><span class=\"iconfont-diy iconaddto\"></span></div>\n\t\t\t\t\t\t\t\t\t</draggable>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div v-else>\n\t\t\t\t\t\t\t\t<div class=\"c_row-item\" v-if=\"item.tabVal == 2\">\n\t\t\t\t\t\t\t\t\t<Col class=\"label\" span=\"4\">品牌名称</Col>\n\t\t\t\t\t\t\t\t\t<Col span=\"19\" class=\"slider-box\">\n\t\t\t\t\t\t\t\t\t\t<el-cascader\n\t\t\t\t\t\t\t\t\t\t        @change=\"brandChange\"\n\t\t\t\t\t\t\t\t\t\t        placeholder=\"请选择品牌\"\n\t\t\t\t\t\t\t\t\t\t        size=\"mini\"\n\t\t\t\t\t\t\t\t\t\t        v-model=\"item.brandConfig.brandVal\"\n\t\t\t\t\t\t\t\t\t\t        :options=\"brandData\"\n\t\t\t\t\t\t\t\t\t\t        :props=\"props\"\n\t\t\t\t\t\t\t\t\t\t        filterable\n\t\t\t\t\t\t\t\t\t\t        clearable>\n\t\t\t\t\t\t\t\t\t\t</el-cascader>\n\t\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"c_row-item\" v-else-if=\"item.tabVal == 3\">\n\t\t\t\t\t\t\t\t    <Col class=\"label\" span=\"4\">商品分类</Col>\n\t\t\t\t\t\t\t\t    <Col span=\"19\" class=\"slider-box\">\n\t\t\t\t\t\t\t\t\t\t<el-cascader\n\t\t\t\t\t\t\t\t\t\t        @change=\"sliderChange\"\n\t\t\t\t\t\t\t\t\t\t        placeholder=\"请选择分类\"\n\t\t\t\t\t\t\t\t\t\t        size=\"mini\"\n\t\t\t\t\t\t\t\t\t\t        v-model=\"item.selectConfig.activeValue\"\n\t\t\t\t\t\t\t\t\t\t        :options=\"treeSelect\"\n\t\t\t\t\t\t\t\t\t\t        :props=\"props\"\n\t\t\t\t\t\t\t\t\t\t        filterable\n\t\t\t\t\t\t\t\t\t\t        clearable>\n\t\t\t\t\t\t\t\t\t\t</el-cascader>\n\t\t\t\t\t\t\t\t    </Col>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"c_row-item\" v-else>\n\t\t\t\t\t\t\t\t    <Col class=\"label\" span=\"4\">商品标签</Col>\n\t\t\t\t\t\t\t\t    <Col span=\"19\" class=\"slider-box\">\n\t\t\t\t\t\t\t\t\t\t<div class=\"labelInput acea-row row-between-wrapper\" @click=\"openStoreLabel(item.goodsLabel.list,index)\">\n\t\t\t\t\t\t\t\t\t\t  <div style=\"width: 90%;\">\n\t\t\t\t\t\t\t\t\t\t    <div v-if=\"item.goodsLabel.list.length\">\n\t\t\t\t\t\t\t\t\t\t      <Tag closable v-for=\"(j,jindex) in item.goodsLabel.list\" :key=\"jindex\" @on-close=\"closeStoreLabel(j)\">{{j.label_name}}</Tag>\n\t\t\t\t\t\t\t\t\t\t    </div>\n\t\t\t\t\t\t\t\t\t\t    <span class=\"span\" v-else>选择商品标签</span>\n\t\t\t\t\t\t\t\t\t\t  </div>\n\t\t\t\t\t\t\t\t\t\t  <div class=\"iconfont iconxiayi\"></div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t    </Col>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"c_row-item\">\n\t\t\t\t\t\t\t\t\t<Col class=\"label\" span=\"4\">\n\t\t\t\t\t\t\t\t\t\t<span>商品数量</span>\n\t\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t\t\t<Col span=\"19\" class=\"slider-box on\">\n\t\t\t\t\t\t\t\t\t\t<!-- sliderChange -->\n\t\t\t\t\t\t\t\t\t\t<Slider v-model=\"item.numConfig.val\" show-input @on-change=\"radioChange($event)\" :max=\"100\" :min='1'></Slider>\n\t\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"c_row-item\">\n\t\t\t\t\t\t\t\t    <Col class=\"c_label\">\n\t\t\t\t\t\t\t\t        商品排序\n\t\t\t\t\t\t\t\t    </Col>\n\t\t\t\t\t\t\t\t    <Col class=\"color-box\">\n\t\t\t\t\t\t\t\t        <RadioGroup v-model=\"item.goodsSort\" @on-change=\"radioChange($event)\">\n\t\t\t\t\t\t\t\t            <Radio :label=\"0\">\n\t\t\t\t\t\t\t\t                <span>综合</span>\n\t\t\t\t\t\t\t\t            </Radio>\n\t\t\t\t\t\t\t\t            <Radio :label=\"1\">\n\t\t\t\t\t\t\t\t                <span>销量</span>\n\t\t\t\t\t\t\t\t            </Radio>\n\t\t\t\t\t\t\t\t\t\t\t<Radio :label=\"2\">\n\t\t\t\t\t\t\t\t\t\t\t    <span>价格</span>\n\t\t\t\t\t\t\t\t\t\t\t</Radio>\n\t\t\t\t\t\t\t\t        </RadioGroup>\n\t\t\t\t\t\t\t\t    </Col>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n                    <div class=\"delete\" @click.stop=\"bindDelete(index)\">\n                        <Icon type=\"ios-close-circle\" size=\"26\"/>\n                    </div>\n                </div>\n            </draggable>\n        </div>\n        <div v-if=\"configData.list\">\n            <div class=\"add-btn\" @click=\"addHotTxt\">\n                <Button style=\"width: 100%; height: 40px;\">+ 添加</Button>\n            </div>\n        </div>\n\t\t<!-- 商品标签 -->\n\t\t<Modal\n\t\t    v-model=\"storeLabelShow\"\n\t\t    scrollable\n\t\t    title=\"选择商品标签\"\n\t\t    :closable=\"true\"\n\t\t    width=\"540\"\n\t\t    :footer-hide=\"true\"\n\t\t    :mask-closable=\"false\"\n\t\t>\n\t\t  <storeLabelList ref=\"storeLabel\" @activeData=\"activeStoreData\" @close=\"storeLabelClose\"></storeLabelList>\n\t\t</Modal>\n\t\t<Modal v-model=\"modals\" title=\"商品列表\" footerHide  class=\"paymentFooter\" scrollable width=\"900\" @on-cancel=\"cancel\">\n\t\t    <goods-list ref=\"goodslist\" :ischeckbox=\"true\" :isdiy=\"true\"  @getProductId=\"getProductId\" v-if=\"modals\"></goods-list>\n\t\t</Modal>\n\t\t<Modal v-model=\"modalPic\" width=\"960px\" scrollable  footer-hide closable :title=\"configData.header?configData.header:'上传图片'\" :mask-closable=\"false\" :z-index=\"1\">\n\t\t    <uploadPictures :isChoice=\"isChoice\" @getPic=\"getPic\" :gridBtn=\"gridBtn\" :gridPic=\"gridPic\" v-if=\"modalPic\"></uploadPictures>\n\t\t</Modal>\n    </div>\n", null]}