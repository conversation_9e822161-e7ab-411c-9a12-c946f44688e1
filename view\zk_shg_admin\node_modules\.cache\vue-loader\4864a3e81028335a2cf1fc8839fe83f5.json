{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRemark.vue?vue&type=template&id=3e603a49&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\handle\\orderRemark.vue", "mtime": 1644455204000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Modal',{staticClass:\"order_box\",attrs:{\"scrollable\":\"\",\"title\":\"备注\",\"closable\":false},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('Form',{ref:\"formValidate\",attrs:{\"model\":_vm.formValidate,\"rules\":_vm.ruleValidate,\"label-width\":80},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"备注：\",\"prop\":\"remark\"}},[_c('Input',{staticStyle:{\"width\":\"100%\"},attrs:{\"maxlength\":\"200\",\"show-word-limit\":\"\",\"type\":\"textarea\",\"placeholder\":\"订单备注\"},model:{value:(_vm.formValidate.remark),callback:function ($$v) {_vm.$set(_vm.formValidate, \"remark\", $$v)},expression:\"formValidate.remark\"}})],1)],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.putRemark('formValidate')}}},[_vm._v(\"提交\")]),_c('Button',{on:{\"click\":function($event){return _vm.cancel('formValidate')}}},[_vm._v(\"取消\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}