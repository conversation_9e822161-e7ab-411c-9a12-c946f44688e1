{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport orderDetall from './orderDetall';\nimport { orderInvoiceChart, orderInvoiceList, orderInvoiceSet, exportInvoiceList } from '@/api/order';\nimport { mapState } from 'vuex';\nimport exportExcel from \"@/utils/newToExcel.js\";\nexport default {\n  name: 'invoice',\n  components: {\n    orderDetall: orderDetall\n  },\n  computed: _objectSpread({}, mapState('media', ['isMobile']), {\n    labelWidth: function labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition: function labelPosition() {\n      return this.isMobile ? 'top' : 'right';\n    }\n  }),\n  data: function data() {\n    return {\n      orderShow: false,\n      invoiceShow: false,\n      invoiceDetails: {},\n      formInline: {\n        is_invoice: 0,\n        invoice_number: '',\n        remark: '',\n        invoice_amount: '' // pay_price: ''\n\n      },\n      loading: false,\n      currentTab: '',\n      tablists: null,\n      timeVal: [],\n      options: {\n        shortcuts: [{\n          text: '今天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()));\n            return [start, end];\n          }\n        }, {\n          text: '昨天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)));\n            return [start, end];\n          }\n        }, {\n          text: '最近7天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 6)));\n            return [start, end];\n          }\n        }, {\n          text: '最近30天',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 29)));\n            return [start, end];\n          }\n        }, {\n          text: \"上月\",\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            var day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)));\n            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth() - 1, day)));\n            return [start, end];\n          }\n        }, {\n          text: '本月',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));\n            return [start, end];\n          }\n        }, {\n          text: '本年',\n          value: function value() {\n            var end = new Date();\n            var start = new Date();\n            start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)));\n            return [start, end];\n          }\n        }]\n      },\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      columns: [{\n        title: '订单号',\n        key: 'order_id',\n        minWidth: 170\n      }, // {\n      //     title: '订单类型',\n      //     key: 'pink_name',\n      //     minWidth: 150\n      // },\n      {\n        title: '订单金额',\n        slot: 'pay_price',\n        minWidth: 100\n      }, {\n        title: '发票类型',\n        slot: 'type',\n        minWidth: 120,\n        filters: [{\n          label: '电子普通发票',\n          value: 1\n        }, {\n          label: '纸质专用发票',\n          value: 2\n        }],\n        filterMultiple: false,\n        filterMethod: function filterMethod(value, row) {\n          if (value === 1) {\n            return row.type === 1;\n          } else if (value === 2) {\n            return row.type === 2;\n          }\n        }\n      }, {\n        title: '发票抬头名称',\n        key: 'name',\n        minWidth: 150\n      }, {\n        title: '发票抬头类型',\n        slot: 'header_type',\n        minWidth: 110,\n        filters: [{\n          label: '个人',\n          value: 1\n        }, {\n          label: '企业',\n          value: 2\n        }],\n        filterMultiple: false,\n        filterMethod: function filterMethod(value, row) {\n          if (value === 1) {\n            return row.header_type === 1;\n          } else if (value === 2) {\n            return row.header_type === 2;\n          }\n        }\n      }, // {\n      //     title: '支付状态',\n      //     key: 'pay_type_name',\n      //     minWidth: 90\n      // },\n      {\n        title: '下单时间',\n        key: 'add_time',\n        minWidth: 150,\n        sortable: true\n      }, {\n        title: '开票状态',\n        slot: 'is_invoice',\n        minWidth: 80\n      }, {\n        title: '订单状态',\n        slot: 'status',\n        minWidth: 80\n      }, {\n        title: '操作',\n        slot: 'action',\n        fixed: 'right',\n        minWidth: 150,\n        align: 'center'\n      }],\n      orderList: [],\n      total: 0,\n      // 总条数\n      orderData: {\n        page: 1,\n        // 当前页\n        limit: 10,\n        // 每页显示条数\n        status: '',\n        data: '',\n        real_name: '',\n        field_key: '',\n        type: ''\n      },\n      orderId: 0\n    };\n  },\n  created: function created() {\n    this.getTabs();\n    this.getList();\n  },\n  mounted: function mounted() {},\n  methods: {\n    // keyUp(e, key, money){\n    //     if(!this.formInline[key]) {\n    //         return (this.formInline[key] = \"\")\n    //     }\n    //     let num = \"\";\n    //     if(money) {\n    //         num = this.formInline[key].match(/^\\d*(\\.?\\d{0,2})/g)[0];\n    //     } else {\n    //         num = this.formInline[key].replace(/^[^\\d]+$/g, \"\").split('.')[0]\n    //     }\n    //     this.formInline[key] = `${num}`\n    //     //  this.$set(this.formInline,key,num)\n    // },\n    detall: function detall(e) {\n      this.orderShow = e;\n    },\n    orderInfo: function orderInfo(id) {\n      this.orderId = id;\n      this.orderShow = true;\n    },\n    empty: function empty() {\n      this.formInline = {\n        is_invoice: 0,\n        invoice_number: '',\n        remark: ''\n      };\n    },\n    cancel: function cancel() {\n      this.invoiceShow = false;\n      this.empty();\n    },\n    kaiInvoice: function kaiInvoice(invoice) {\n      if (invoice !== 1) {\n        this.formInline.invoice_number = '';\n        this.formInline.remark = '';\n      }\n    },\n    handleSubmit: function handleSubmit() {\n      var _this = this;\n\n      if (this.formInline.is_invoice === 1) {\n        if (this.formInline.invoice_number.trim() === '') return this.$Message.error('请填写发票编号');\n      }\n\n      orderInvoiceSet(this.invoiceDetails.invoice_id, this.formInline).then(function (res) {\n        _this.$Message.success(res.msg);\n\n        _this.invoiceShow = false;\n\n        _this.getList();\n\n        _this.empty();\n      }).catch(function (err) {\n        _this.$Message.error(err.msg);\n      });\n    },\n    edit: function edit(row) {\n      this.invoiceShow = true;\n      this.invoiceDetails = row; // this.formInline = row;\n\n      this.formInline.invoice_number = row.invoice_number;\n      this.formInline.invoice_amount = row.invoice_amount;\n      this.formInline.remark = row.invoice_reamrk;\n      this.formInline.is_invoice = row.is_invoice; // this.formInline.pay_price = row.pay_price;\n\n      this.formInline.invoice_amount = row.invoice_amount == '0.00' ? row.pay_price : row.invoice_amount;\n    },\n    // 订单列表\n    getList: function getList() {\n      var _this2 = this;\n\n      this.loading = true;\n      orderInvoiceList(this.orderData).then(\n      /*#__PURE__*/\n      function () {\n        var _ref = _asyncToGenerator(\n        /*#__PURE__*/\n        _regeneratorRuntime.mark(function _callee(res) {\n          var data;\n          return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while (1) {\n              switch (_context.prev = _context.next) {\n                case 0:\n                  _this2.loading = false;\n                  data = res.data;\n                  _this2.orderList = data.list;\n                  _this2.total = data.count;\n\n                case 4:\n                case \"end\":\n                  return _context.stop();\n              }\n            }\n          }, _callee);\n        }));\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this2.loading = false;\n\n        _this2.$Message.error(res.msg);\n      });\n    },\n    pageChange: function pageChange(index) {\n      this.orderData.page = index;\n      this.getList();\n    },\n    getTabs: function getTabs() {\n      var _this3 = this;\n\n      orderInvoiceChart().then(function (res) {\n        _this3.tablists = res.data;\n      }).catch(function (err) {\n        _this3.$Message.error(err.msg);\n      });\n    },\n    // 精确搜索()\n    orderSearch: function orderSearch() {\n      this.orderData.page = 1;\n      this.getList();\n    },\n    // 具体日期搜索()；\n    onchangeTime: function onchangeTime(e) {\n      this.orderData.page = 1;\n      this.timeVal = e;\n      this.orderData.data = this.timeVal[0] ? this.timeVal.join('-') : '';\n      this.getList();\n    },\n    // 订单状态搜索()\n    selectChange: function selectChange() {\n      this.orderData.page = 1;\n      this.getList();\n    },\n    // 订单搜索()\n    onClickTab: function onClickTab() {\n      this.orderData.page = 1;\n      this.orderData.type = this.currentTab;\n      this.getList();\n    },\n    //导出\n    exports: function () {\n      var _exports = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee2() {\n        var th, filekey, data, fileName, lebData;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                th = [], filekey = [], data = [], fileName = \"\";\n                _context2.next = 3;\n                return this.getExcelData(this.orderData);\n\n              case 3:\n                lebData = _context2.sent;\n                if (!fileName) fileName = lebData.filename;\n                filekey = lebData.filekey;\n                if (!th.length) th = lebData.header; //表头\n\n                data = data.concat(lebData.export);\n                exportExcel(th, filekey, fileName, data);\n\n              case 9:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function exports() {\n        return _exports.apply(this, arguments);\n      }\n\n      return exports;\n    }(),\n    getExcelData: function getExcelData(excelData) {\n      return new Promise(function (resolve, reject) {\n        exportInvoiceList(excelData).then(function (res) {\n          return resolve(res.data);\n        });\n      });\n    },\n    inputEnter: function inputEnter() {\n      this.formInline.pay_price = this.formInline.pay_price.replace(/[^\\d.]/g, \"\"); // 清除\"数字\"和\".\"以外的字符 只能输入数字和小数点\n\n      this.formInline.pay_price = this.formInline.pay_price.replace(/\\.{2,}/g, \".\"); // 不能连续输入两个及以上小数点\n\n      this.formInline.pay_price = this.formInline.pay_price.replace(\".\", \"$#$\").replace(/\\./g, \"\").replace(\"$#$\", \".\"); // 只保留第一个\".\", 清除多余的\".\"\n\n      this.formInline.pay_price = this.formInline.pay_price.replace(/^(-)*(\\d+)\\.(\\d\\d).*$/, \"$1$2.$3\"); // 只能输入两位小数\n\n      if (this.formInline.pay_price && this.formInline.pay_price.indexOf(\".\") < 0 && this.formInline.pay_price != \"\") {\n        this.formInline.pay_price = parseFloat(this.formInline.pay_price);\n        this.formInline.pay_price = this.formInline.pay_price + \"\";\n      } // 如果没有小数点，首位不能为类似于 01、02的值\n      // 输入过程中，只能输入六位小数且六位小数都为零，则清空小数点和后面的六个零（如果输入完成了只输入四个零，则在blur事件中处理）\n\n\n      if (this.formInline.pay_price.indexOf(\".\") > 0 && this.formInline.pay_price.length - this.formInline.pay_price.indexOf(\".\") > 6) {\n        var str = this.formInline.pay_price.slice(this.formInline.pay_price.indexOf(\".\"), this.formInline.pay_price.length);\n\n        if (str / 1 <= 0) {\n          this.formInline.pay_price = this.formInline.pay_price.replace(str, \"\");\n        }\n      }\n\n      if (this.formInline.pay_price / 1 > 256) {\n        this.formInline.pay_price = this.formInline.pay_price + \"\";\n        this.formInline.pay_price = this.formInline.pay_price.slice(0, this.formInline.pay_price.length - 1);\n      }\n    },\n    inputBlur: function inputBlur() {\n      // 若小数点后面全是零，则清楚小数点和后面的零\n      if (this.formInline.pay_price.indexOf(\".\") > 0) {\n        var str = this.formInline.pay_price.slice(this.formInline.pay_price.indexOf(\".\"), this.formInline.pay_price.length);\n\n        if (str / 1 <= 0) {\n          this.formInline.pay_price = this.formInline.pay_price.replace(str, \"\");\n        }\n      }\n    }\n  }\n};", null]}