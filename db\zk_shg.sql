/*
 Navicat Premium Dump SQL

 Source Server         : 华为云数据库
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : *************:3306
 Source Schema         : zk_shg

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 26/06/2025 14:04:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zk_agent_level
-- ----------------------------
DROP TABLE IF EXISTS `zk_agent_level`;
CREATE TABLE `zk_agent_level`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '等级名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 字体颜色',
  `one_brokerage` smallint(6) NOT NULL DEFAULT 0 COMMENT '一级分拥比例',
  `two_brokerage` smallint(6) NOT NULL DEFAULT 0 COMMENT '二级分拥比例',
  `grade` smallint(6) NOT NULL DEFAULT 0 COMMENT '等级',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status`, `is_del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销员等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_agent_level_task
-- ----------------------------
DROP TABLE IF EXISTS `zk_agent_level_task`;
CREATE TABLE `zk_agent_level_task`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level_id` int(11) NOT NULL DEFAULT 0 COMMENT '分销等级id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '任务类型',
  `number` int(11) NOT NULL DEFAULT 0 COMMENT '任务限定数',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务描述',
  `is_must` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必须达成0:其一1:所有',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销员等级任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_agent_level_task_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_agent_level_task_record`;
CREATE TABLE `zk_agent_level_task_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `level_id` int(11) NOT NULL DEFAULT 0 COMMENT '等级id',
  `task_id` int(11) NOT NULL DEFAULT 0 COMMENT '任务id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态',
  `add_time` int(11) NOT NULL DEFAULT 10 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销员完成等级任务表记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_agreement
-- ----------------------------
DROP TABLE IF EXISTS `zk_agreement`;
CREATE TABLE `zk_agreement`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '协议类型  1：会员协议',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '协议名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '协议内容',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序倒序',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '1：显示：0：不显示',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员协议' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_article
-- ----------------------------
DROP TABLE IF EXISTS `zk_article`;
CREATE TABLE `zk_article`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文章管理ID',
  `cid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '分类id',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章标题',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章作者',
  `image_input` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章图片',
  `synopsis` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章简介',
  `share_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章分享标题',
  `share_synopsis` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章分享简介',
  `visit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `likes` int(11) NOT NULL DEFAULT 0 COMMENT '点赞量',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原文链接',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `add_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加时间',
  `hide` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否隐藏',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员id',
  `mer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户id',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品关联id',
  `is_hot` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否热门(小程序)',
  `is_banner` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否轮播图(小程序)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文章管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_article_category
-- ----------------------------
DROP TABLE IF EXISTS `zk_article_category`;
CREATE TABLE `zk_article_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文章分类id',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章分类标题',
  `intr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章分类简介',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章分类图片',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1删除0未删除',
  `add_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加时间',
  `hidden` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否隐藏',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文章分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_article_content
-- ----------------------------
DROP TABLE IF EXISTS `zk_article_content`;
CREATE TABLE `zk_article_content`  (
  `nid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文章id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文章内容',
  UNIQUE INDEX `nid`(`nid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文章内容表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_cache
-- ----------------------------
DROP TABLE IF EXISTS `zk_cache`;
CREATE TABLE `zk_cache`  (
  `key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '缓存数据',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '失效时间0=永久',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '缓存时间',
  PRIMARY KEY (`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信缓存表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_capital_flow
-- ----------------------------
DROP TABLE IF EXISTS `zk_capital_flow`;
CREATE TABLE `zk_capital_flow`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `flow_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '流水id',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联id',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电话',
  `price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '交易金额',
  `trading_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '交易类型',
  `pay_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付类型',
  `mark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '交易时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2912 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资金流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_category
-- ----------------------------
DROP TABLE IF EXISTS `zk_category`;
CREATE TABLE `zk_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `owner_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属人，为全部',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `group` tinyint(1) NOT NULL DEFAULT 0 COMMENT '分类类型0=标签分类，1=快捷短语分类,2=商品标签分类，3=商品参数模版,4=企业渠道码，5=门店分类，6=桌码分类',
  `other` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '其他参数',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `integral_min` int(11) NOT NULL DEFAULT 0 COMMENT '积分分类最低',
  `integral_max` int(11) NOT NULL DEFAULT 0 COMMENT '积分分类最高',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `group`(`group`) USING BTREE,
  INDEX `is_be`(`owner_id`, `type`, `id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '标签分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_city_area
-- ----------------------------
DROP TABLE IF EXISTS `zk_city_area`;
CREATE TABLE `zk_city_area`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `path` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '/' COMMENT '省市级别',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父级id',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `level` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '级别',
  `code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '城市编码',
  `snum` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '子级个数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `parent_id`(`parent_id`) USING BTREE,
  INDEX `path`(`path`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44714 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '省市区县数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_delivery_service
-- ----------------------------
DROP TABLE IF EXISTS `zk_delivery_service`;
CREATE TABLE `zk_delivery_service`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '配送员uid',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `avatar` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送员头像',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送员名称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '手机号码',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '0隐藏1显示',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`, `is_del`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配送员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_diy
-- ----------------------------
DROP TABLE IF EXISTS `zk_diy`;
CREATE TABLE `zk_diy`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面名称',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '网站标题',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面图',
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '页面数据',
  `default_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '默认数据',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否使用',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '页面类型',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '显示首页',
  `is_bg_color` tinyint(1) NOT NULL DEFAULT 0 COMMENT '颜色是否选中',
  `is_bg_pic` tinyint(1) NOT NULL DEFAULT 0 COMMENT '背景图是否选中',
  `is_diy` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是diy数据',
  `color_picker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景颜色',
  `bg_pic` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `bg_tab_val` tinyint(1) NOT NULL DEFAULT 0 COMMENT '背景图图片样式',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `order_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '个人中心订单样式',
  `my_banner_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '个人中心banner是否显示',
  `menu_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '个人中心菜单样式',
  `service_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '商家样式',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status_type`(`status`, `type`) USING BTREE,
  INDEX `template_name`(`template_name`, `type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DIY数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_express
-- ----------------------------
DROP TABLE IF EXISTS `zk_express`;
CREATE TABLE `zk_express`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '快递公司id',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递公司简称',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递公司全称',
  `partner_id` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要月结账号',
  `partner_key` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要月结密码',
  `net` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要取件网店',
  `check_man` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否填写电子面单承载快递员名',
  `partner_name` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否填写电子面单客户账户名称',
  `is_code` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否填写电子面单承载编号',
  `courier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '承载快递员名',
  `customer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户账户名称',
  `code_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子面单承载编号',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账号',
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `net_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '网点名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code`) USING BTREE,
  INDEX `is_show`(`is_show`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 566 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递公司表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_live_anchor
-- ----------------------------
DROP TABLE IF EXISTS `zk_live_anchor`;
CREATE TABLE `zk_live_anchor`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播名称',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播图像',
  `wechat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播微信号',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播主播表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_live_goods
-- ----------------------------
DROP TABLE IF EXISTS `zk_live_goods`;
CREATE TABLE `zk_live_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '微信商品ID',
  `audit_id` int(11) NOT NULL DEFAULT 0 COMMENT '审核ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品图片链接',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品小程序链接',
  `price_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '价格类型（1:一口价，此时读price字段; 2:价格区间，此时price字段为左边界，price2字段为右边界; 3:折扣价，此时price字段为原价，price2字段为现价；）',
  `cost_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `price2` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态（0：未审核，1：审核中，2:审核通过，3审核失败）',
  `third_part_tag` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1、2：表示是为 API 添加商品，否则是直播控制台添加的商品',
  `sort` smallint(6) NOT NULL DEFAULT 0,
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_live_room
-- ----------------------------
DROP TABLE IF EXISTS `zk_live_room`;
CREATE TABLE `zk_live_room`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `room_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '直播间 id',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直播间名字',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `share_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享图',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '直播计划开始时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '直播计划结束时间',
  `anchor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播昵称',
  `anchor_wechat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播微信号',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播手机号',
  `type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '直播间类型 【1: 推流，0：手机直播】',
  `screen_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '横屏、竖屏 【1：横屏，0：竖屏】',
  `close_like` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否关闭点赞',
  `close_goods` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否关闭货架',
  `close_comment` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否关闭评论',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '未通过原因',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态0=未审核1=微信审核2=审核通过-1=审核未通过',
  `live_status` smallint(5) UNSIGNED NOT NULL DEFAULT 102 COMMENT '直播状态101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期',
  `mark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `replay_status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '回放状态',
  `sort` smallint(6) NOT NULL DEFAULT 0,
  `is_show` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否显示',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`, `phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_live_room_goods
-- ----------------------------
DROP TABLE IF EXISTS `zk_live_room_goods`;
CREATE TABLE `zk_live_room_goods`  (
  `live_room_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `live_goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  INDEX `broadcast_room_id`(`live_room_id`, `live_goods_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播间导入商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_luck_lottery
-- ----------------------------
DROP TABLE IF EXISTS `zk_luck_lottery`;
CREATE TABLE `zk_luck_lottery`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '抽奖类型1:九宫格2：大转盘3：九宫格升级版 4：幸运翻牌',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '抽奖活动名称',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动描述',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动背景图',
  `factor` tinyint(1) NOT NULL DEFAULT 1 COMMENT '抽奖消耗：1:积分2：余额3：下单支付成功4：订单评价5：关注公众号',
  `factor_num` smallint(6) NOT NULL DEFAULT 10 COMMENT '获取一次抽奖的条件数量',
  `attends_user` tinyint(1) NOT NULL DEFAULT 1 COMMENT '参与用户1：所有2：部分',
  `user_level` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参与用户等级',
  `user_label` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参与用户标签',
  `is_svip` tinyint(1) NOT NULL DEFAULT 1 COMMENT '参与用户是否付费会员',
  `prize_num` smallint(6) NOT NULL DEFAULT 0 COMMENT '奖品数量',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '结束时间',
  `lottery_num_term` tinyint(1) NOT NULL DEFAULT 1 COMMENT '抽奖次数限制：1：每天2：每人',
  `lottery_num` smallint(6) NOT NULL DEFAULT 1 COMMENT '抽奖次数',
  `total_lottery_num` smallint(6) NOT NULL DEFAULT 1 COMMENT '积分抽奖总次数',
  `spread_num` smallint(6) NOT NULL DEFAULT 1 COMMENT '关注推广获取抽奖次数',
  `is_all_record` tinyint(1) NOT NULL DEFAULT 1 COMMENT '中奖纪录展示',
  `is_personal_record` tinyint(1) NOT NULL DEFAULT 1 COMMENT '个人中奖纪录展示',
  `is_content` tinyint(1) NOT NULL DEFAULT 1 COMMENT '活动规格是否展示',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动文案抽奖协议之类',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抽奖活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_luck_lottery_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_luck_lottery_record`;
CREATE TABLE `zk_luck_lottery_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `oid` int(11) NULL DEFAULT 0 COMMENT '订单id',
  `lottery_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动id',
  `prize_id` int(11) NOT NULL DEFAULT 0 COMMENT '奖品id',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '奖品类型1：未中奖2：积分3:余额4：红包5:优惠券6：站内商品7：等级经验8：用户等级 9：svip天数',
  `prize_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '奖品信息',
  `is_receive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否领取',
  `receive_time` int(11) NOT NULL DEFAULT 0 COMMENT '领取时间',
  `receive_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '收获地址、备注等',
  `is_deliver` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发货',
  `deliver_time` int(11) NOT NULL DEFAULT 0 COMMENT '发货处理时间',
  `deliver_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发货单号、备注等',
  `add_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lottery_id`(`lottery_id`) USING BTREE,
  INDEX `prize_id`(`prize_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抽奖记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_luck_prize
-- ----------------------------
DROP TABLE IF EXISTS `zk_luck_prize`;
CREATE TABLE `zk_luck_prize`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '奖品主键id',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '奖品类型1：未中奖2：积分3:余额4：红包5:优惠券6：站内商品7：等级经验8：用户等级 9：svip天数',
  `lottery_id` int(11) NOT NULL DEFAULT 0 COMMENT '抽奖活动id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '奖品名称',
  `prompt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中奖提示语',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '奖品图片',
  `chance` smallint(6) NOT NULL DEFAULT 10 COMMENT '中奖基数',
  `total` smallint(6) NOT NULL DEFAULT 1 COMMENT '奖品数量',
  `coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联优惠券id',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联商品id',
  `unique` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联商品规格唯一值',
  `num` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '积分 经验 会员天数',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lottery_id`(`lottery_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抽奖活动奖品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_member_card
-- ----------------------------
DROP TABLE IF EXISTS `zk_member_card`;
CREATE TABLE `zk_member_card`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `card_batch_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '卡批次id',
  `card_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡号',
  `card_password` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `use_uid` int(11) NOT NULL DEFAULT 0 COMMENT '使用用户',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '卡状态：0：冻结；1：激活',
  `add_time` int(11) NOT NULL DEFAULT 0,
  `update_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`, `card_batch_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员卡表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_member_card_batch
-- ----------------------------
DROP TABLE IF EXISTS `zk_member_card_batch`;
CREATE TABLE `zk_member_card_batch`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '批次名称',
  `total_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '生成卡数量',
  `use_start_time` int(10) UNSIGNED NOT NULL DEFAULT 7 COMMENT '体验开始时间',
  `use_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '体验结束时间',
  `use_day` int(11) NOT NULL DEFAULT 0 COMMENT '体验天数',
  `use_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否生效,控制此批次所有卡0：不生效；1：生效',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `qrcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码图路径',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `add_time` int(11) NOT NULL DEFAULT 0,
  `update_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员卡批次表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_member_points
-- ----------------------------
DROP TABLE IF EXISTS `zk_member_points`;
CREATE TABLE `zk_member_points`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` int(11) NOT NULL COMMENT '用户唯一标识',
  `points` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '积分值',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 124 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员积分表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_member_points_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_member_points_record`;
CREATE TABLE `zk_member_points_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` int(11) NOT NULL COMMENT '用户唯一标识',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联订单ID',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型（支出-1或收入1）',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题（记录描述）',
  `value` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '数值（本次变动的积分值）',
  `before_value` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '修改前值',
  `after_value` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '修改后值',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 221 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员积分变动记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_member_right
-- ----------------------------
DROP TABLE IF EXISTS `zk_member_right`;
CREATE TABLE `zk_member_right`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `right_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权益类别',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 权益名称',
  `show_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '显示权益名称',
  `image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权益图标',
  `explain` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权益介绍',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '权益内容',
  `number` int(11) NOT NULL DEFAULT 1 COMMENT '规则数字',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序倒序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0:禁用，1：启用',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`, `right_type`) USING BTREE,
  INDEX `type`(`right_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员权益' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_member_ship
-- ----------------------------
DROP TABLE IF EXISTS `zk_member_ship`;
CREATE TABLE `zk_member_ship`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'month' COMMENT '会员类别month:月卡会员；quarter:季卡；year:年卡；ever:永久；free:免费',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员名称',
  `vip_day` int(11) NOT NULL DEFAULT 0 COMMENT '会员时间(天)',
  `price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '原价',
  `pre_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠后价格',
  `is_label` tinyint(1) NOT NULL DEFAULT 0 COMMENT '特惠标签',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序倒序',
  `is_del` int(11) NOT NULL DEFAULT 0 COMMENT '删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_other_order
-- ----------------------------
DROP TABLE IF EXISTS `zk_other_order`;
CREATE TABLE `zk_other_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员ID',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户uid',
  `type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '区别 \r\n0：免费领取\r\n1：购买会员卡 \r\n2：卡密领取会员卡 \r\n3：收银订单 \r\n4：管理员赠送',
  `order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `member_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员类型 \r\n月卡：month\r\n季卡：quarter\r\n年卡：year\r\n永久：ever\r\n免费：free',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡号',
  `pay_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `paid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态 0:  未支付 1：已支付',
  `pay_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `member_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '会员卡价格',
  `pay_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员购买时间',
  `trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付宝支付,支付宝交易订单号',
  `channel_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付渠道(微信公众号 h5 小程序)',
  `is_free` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否免费',
  `is_permanent` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否永久',
  `overdue_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员过期时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除',
  `vip_day` int(11) NOT NULL DEFAULT 0 COMMENT '会员有效天数',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `money` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '原价格',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 273 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员购买记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_other_order_status
-- ----------------------------
DROP TABLE IF EXISTS `zk_other_order_status`;
CREATE TABLE `zk_other_order_status`  (
  `oid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `change_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作类型',
  `change_message` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作备注',
  `shop_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单类型 1：会员卡',
  `change_time` int(11) NOT NULL DEFAULT 0,
  INDEX `change_type`(`change_type`) USING BTREE,
  INDEX `oid`(`oid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_out_account
-- ----------------------------
DROP TABLE IF EXISTS `zk_out_account`;
CREATE TABLE `zk_out_account`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `appid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '账号',
  `appsecret` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '密钥',
  `title` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `rules` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '权限',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `last_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后一次登录时间',
  `ip` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'IP',
  `is_del` tinyint(1) NOT NULL DEFAULT 0,
  `push_open` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推送',
  `push_account` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '推送账号',
  `push_password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '推送密码',
  `push_token_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '获取推送token接口',
  `user_update_push` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户推送接口',
  `order_create_push` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单创建推送接口',
  `order_pay_push` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单支付推送接口',
  `refund_create_push` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '售后订单创建推送接口',
  `refund_cancel_push` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '售后订单取消推送接口',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '对外接口账号' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_out_interface
-- ----------------------------
DROP TABLE IF EXISTS `zk_out_interface`;
CREATE TABLE `zk_out_interface`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级pid',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0菜单，1接口',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接口名称',
  `describe` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '功能描述',
  `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接口类型',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接口地址',
  `request_params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
  `return_params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回参数',
  `request_example` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求示例',
  `return_example` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回示例',
  `error_code` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '错误返回',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '对外接口' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_page_category
-- ----------------------------
DROP TABLE IF EXISTS `zk_page_category`;
CREATE TABLE `zk_page_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父类id',
  `type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'link' COMMENT '类型:link、special、product、product_category、custom',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '页面链接分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_page_link
-- ----------------------------
DROP TABLE IF EXISTS `zk_page_link`;
CREATE TABLE `zk_page_link`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '分组1:基础2:分销3:个人中心',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '页面名称',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '页面链接',
  `param` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '参数',
  `example` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '事例',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 61 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '页面链接' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `zk_qrcode`;
CREATE TABLE `zk_qrcode`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '微信二维码ID',
  `third_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码类型',
  `third_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `ticket` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码参数',
  `expire_seconds` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '二维码有效时间',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `add_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '添加时间',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信访问url',
  `qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信二维码url',
  `scan` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '被扫的次数',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '二维码所属平台1=小程序，2=公众号，3=H5',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `third_type`(`third_type`, `third_id`) USING BTREE,
  INDEX `ticket`(`ticket`(191)) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信二维码管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_queue_auxiliary
-- ----------------------------
DROP TABLE IF EXISTS `zk_queue_auxiliary`;
CREATE TABLE `zk_queue_auxiliary`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `binding_id` int(11) NOT NULL DEFAULT 0 COMMENT '绑定id',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型0=客服转接辅助，1=商品和分类辅助，2=优惠券和商品辅助',
  `other` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '其他数据为json',
  `status` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '数据状态 0：未执行，1：成功， 2：失败， 3:删除',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '辅助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_queue_list
-- ----------------------------
DROP TABLE IF EXISTS `zk_queue_list`;
CREATE TABLE `zk_queue_list`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '队列类型  1：批量发放用户优惠券 2 : 批量设置用户分组, 3:批量设置用户标签, 4:批量下架商品, 5:批量删除商品规格, 6:批量删除订单, 7:批量手动发货, 8:批量打印电子面单, 9:批量配送, 10:批量虚拟发货',
  `source` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '来源 前端：home ，后端：admin',
  `execute_key` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '队列执行数据缓存key',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 队列名称',
  `queue_in_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '队列参数',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序倒序',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 :尚未执行，1：正在执行，2：执行成功, 3:执行失败,4: 删除的异常队列',
  `first_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '首次执行时间',
  `again_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '再次执行时间',
  `finish_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '完成时间',
  `surplus_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '剩余未执行数量',
  `total_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT ' 总数量',
  `is_del` tinyint(1) NOT NULL DEFAULT 0,
  `add_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`, `type`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 496 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_refund_notice
-- ----------------------------
DROP TABLE IF EXISTS `zk_refund_notice`;
CREATE TABLE `zk_refund_notice`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `refund_id` bigint(20) NULL DEFAULT NULL,
  `refund_type` tinyint(1) NULL DEFAULT NULL COMMENT '退款处理类型0:未处理3：拒绝4：同意退货5：已退货6：已退款',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_reserve
-- ----------------------------
DROP TABLE IF EXISTS `zk_reserve`;
CREATE TABLE `zk_reserve`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `type` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'haircut',
  `zk_order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `reserve_order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_pay` tinyint(1) NULL DEFAULT -1,
  `pay_type` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pay_price` decimal(8, 2) NULL DEFAULT NULL,
  `pay_points` decimal(8, 2) NULL DEFAULT 0.00,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `wx_ordernum` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `refund_wx_ordernum` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `write_order_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4769 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_reserve_activity
-- ----------------------------
DROP TABLE IF EXISTS `zk_reserve_activity`;
CREATE TABLE `zk_reserve_activity`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type_id` int(11) NOT NULL DEFAULT 1 COMMENT '活动类型',
  `uid` bigint(20) NULL DEFAULT NULL COMMENT '用户Id',
  `user_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `user_phone` varchar(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `user_unit` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `user_time` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动时间',
  `user_address` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动地址',
  `user_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆数',
  `user_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `v1` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属字段',
  `v2` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属字段',
  `v3` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属字段',
  `is_status` decimal(2, 0) NOT NULL DEFAULT 0 COMMENT '状态',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 441 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_reserve_activity_config
-- ----------------------------
DROP TABLE IF EXISTS `zk_reserve_activity_config`;
CREATE TABLE `zk_reserve_activity_config`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户Id',
  `type_id` int(11) NULL DEFAULT NULL COMMENT '活动号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值',
  `is_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`, `is_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_reserve_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `zk_reserve_blacklist`;
CREATE TABLE `zk_reserve_blacklist`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `type` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'haircut',
  `is_status` tinyint(1) NULL DEFAULT 1,
  `start_time` date NULL DEFAULT NULL,
  `end_time` date NULL DEFAULT NULL,
  `admin_uid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作员Id',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `delete_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_reserve_car_points
-- ----------------------------
DROP TABLE IF EXISTS `zk_reserve_car_points`;
CREATE TABLE `zk_reserve_car_points`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NULL DEFAULT NULL,
  `integral` decimal(8, 2) NULL DEFAULT 0.00,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1304 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_reserve_car_points_bill
-- ----------------------------
DROP TABLE IF EXISTS `zk_reserve_car_points_bill`;
CREATE TABLE `zk_reserve_car_points_bill`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NULL DEFAULT NULL,
  `link_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `number` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '账单金额',
  `forward` decimal(6, 2) NULL DEFAULT 0.00,
  `balance` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '剩余',
  `mark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 171 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_shipping_templates
-- ----------------------------
DROP TABLE IF EXISTS `zk_shipping_templates`;
CREATE TABLE `zk_shipping_templates`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `group` tinyint(1) NOT NULL DEFAULT 1 COMMENT '计费方式1按件数,2按重量, 3按体积',
  `appoint` tinyint(1) NOT NULL DEFAULT 0 COMMENT '指定包邮',
  `no_delivery` tinyint(1) NOT NULL DEFAULT 0 COMMENT '指定不送达',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_shipping_templates_free
-- ----------------------------
DROP TABLE IF EXISTS `zk_shipping_templates_free`;
CREATE TABLE `zk_shipping_templates_free`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市ID',
  `number` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '包邮件数',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '包邮金额',
  `group` tinyint(1) NOT NULL DEFAULT 1 COMMENT '计费方式1按件数,2按重量, 3按体积',
  `value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uniqid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分组唯一值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板指定包邮关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_shipping_templates_no_delivery
-- ----------------------------
DROP TABLE IF EXISTS `zk_shipping_templates_no_delivery`;
CREATE TABLE `zk_shipping_templates_no_delivery`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市ID',
  `value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uniqid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分组唯一值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 391 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板指定不送达表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_shipping_templates_region
-- ----------------------------
DROP TABLE IF EXISTS `zk_shipping_templates_region`;
CREATE TABLE `zk_shipping_templates_region`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市ID',
  `first` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '首件',
  `first_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '首件运费',
  `continue` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '续件',
  `continue_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '续件运费',
  `group` tinyint(1) NOT NULL DEFAULT 1 COMMENT '计费方式1按件数,2按重量, 3按体积',
  `value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uniqid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分组唯一值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 573 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板指定城市运费表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_sms_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_sms_record`;
CREATE TABLE `zk_sms_record`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '短信发送记录编号',
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短信平台账号',
  `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接受短信的手机号',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '短信内容',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发送短信时间',
  `add_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加记录ip',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短信模板ID',
  `resultcode` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态码 100=成功,130=失败,131=空号,132=停机,133=关机,134=无状态',
  `record_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发送记录id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 744 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信发送记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_activity
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_activity`;
CREATE TABLE `zk_store_activity`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:秒杀',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动名称',
  `image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '氛围图片',
  `start_day` int(11) NOT NULL DEFAULT 0 COMMENT '开始日期',
  `end_day` int(11) NOT NULL DEFAULT 0 COMMENT '结束日期',
  `start_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '结束时间',
  `time_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '时间段ID多个',
  `once_num` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '活动期间每人每日购买数量，0不限制',
  `num` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '全部活动期间，用户购买总数限制，0不限制	',
  `discount` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠方式',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '是否显示',
  `is_recommend` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '是否推荐',
  `link_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '关联ID',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用门店ids',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `start_day`(`start_day`, `end_day`) USING BTREE,
  INDEX `start_time`(`start_time`, `end_time`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_activity_relation
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_activity_relation`;
CREATE TABLE `zk_store_activity_relation`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `activity_id` int(10) UNSIGNED NOT NULL,
  `product_id` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`activity_id`, `product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '活动关联表中间表(多对多)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_bargain
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_bargain`;
CREATE TABLE `zk_store_bargain`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '砍价商品ID',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联商品ID',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '砍价活动名称',
  `image` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '砍价活动图片',
  `unit_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位名称',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `images` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '砍价商品轮播图',
  `start_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价开启时间',
  `stop_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价结束时间',
  `store_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '砍价商品名称',
  `price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍价金额',
  `min_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍价商品最低价',
  `num` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '可购买砍价商品数量',
  `bargain_max_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '用户每次砍价的最大金额',
  `bargain_min_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '用户每次砍价的最小金额',
  `bargain_num` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户帮砍的次数',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '砍价状态 0(到砍价时间不自动开启)  1(到砍价时间自动开启时间)',
  `give_integral` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '反多少积分',
  `info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '砍价活动简介',
  `cost` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `is_hot` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否推荐0不推荐1推荐',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0未删除 1删除',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_postage` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否包邮 0不包邮 1包邮',
  `postage` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '邮费',
  `rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '砍价规则',
  `look` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价商品浏览量',
  `share` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价商品分享量',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '运费模板ID',
  `weight` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '重量',
  `volume` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '体积',
  `quota` int(11) NOT NULL DEFAULT 0 COMMENT '限购总数',
  `quota_show` int(11) NOT NULL DEFAULT 0 COMMENT '限量总数显示',
  `people_num` int(11) NOT NULL DEFAULT 1 COMMENT '用户帮砍的次数',
  `is_support_refund` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持退款',
  `delivery_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流类型',
  `freight` tinyint(1) NOT NULL DEFAULT 2 COMMENT '运费设置',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  `system_form_id` int(11) NOT NULL DEFAULT 0 COMMENT '系统表单ID',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用门店ids',
  `store_label_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品标签iDS',
  `ensure_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品保障服务ids',
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品参数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del`, `status`, `product_id`, `start_time`, `stop_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '砍价表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_bargain_user
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_bargain_user`;
CREATE TABLE `zk_store_bargain_user`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户参与砍价表ID',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `bargain_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价商品id',
  `bargain_price_min` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍价的最低价',
  `bargain_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '砍价金额',
  `price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍掉的价格',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态 1参与中 2 活动结束参与失败 3活动结束参与成功',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否取消',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户参与砍价表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_bargain_user_help
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_bargain_user_help`;
CREATE TABLE `zk_store_bargain_user_help`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '砍价用户帮助表ID',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '帮助的用户id',
  `bargain_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价商品ID',
  `bargain_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户参与砍价表id',
  `price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '帮助砍价多少金额',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自己砍价',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '砍价用户帮助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_brand
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_brand`;
CREATE TABLE `zk_store_brand`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '品牌名称',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父ID',
  `fid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '父ID集合 ',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店ID',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示	',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '品牌表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_cart
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_cart`;
CREATE TABLE `zk_store_cart`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '购物车表ID',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `tourist_uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '游客uid',
  `type` smallint(6) NOT NULL DEFAULT 0 COMMENT '类型 0:普通、1：秒杀、2:砍价、3:拼团、4:积分、5:套餐、9:拼单、10:桌码',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动商品ID',
  `collate_code_id` int(11) NOT NULL DEFAULT 0 COMMENT '拼单ID/桌码ID',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员id',
  `product_attr_unique` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品属性',
  `cart_num` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品数量',
  `is_pay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = 未购买 1 = 已购买',
  `is_new` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为立即购买',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '购物车商品状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `goods_id`(`product_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `uid`(`uid`, `is_pay`) USING BTREE,
  INDEX `uid_2`(`uid`, `is_del`) USING BTREE,
  INDEX `uid_3`(`uid`, `is_new`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7388 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '购物车表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_combination
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_combination`;
CREATE TABLE `zk_store_combination`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `mer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户id',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '推荐图',
  `images` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '轮播图',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动标题',
  `attr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动属性',
  `people` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '参团人数',
  `info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
  `add_time` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '添加时间',
  `is_host` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '推荐',
  `is_show` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '商品状态',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `combination` tinyint(3) UNSIGNED NOT NULL DEFAULT 1,
  `mer_use` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '商户是否可用1可用0不可用',
  `is_postage` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否包邮1是0否',
  `postage` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '邮费',
  `start_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团开始时间',
  `stop_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团结束时间',
  `effective_time` int(11) NOT NULL DEFAULT 0 COMMENT '拼团订单有效时间',
  `cost` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼图商品成本',
  `browse` int(11) NOT NULL DEFAULT 0 COMMENT '浏览量',
  `unit_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位名',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '运费模板ID',
  `weight` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '重量',
  `volume` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '体积',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '单次购买数量',
  `once_num` int(11) NOT NULL DEFAULT 0 COMMENT '每个订单可购买数量',
  `quota` int(11) NOT NULL DEFAULT 0 COMMENT '限购总数',
  `quota_show` int(11) NOT NULL DEFAULT 0 COMMENT '限量总数显示',
  `virtual` int(11) NOT NULL DEFAULT 100 COMMENT '虚拟成团百分比',
  `is_support_refund` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持退款',
  `delivery_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流类型',
  `freight` tinyint(1) NOT NULL DEFAULT 2 COMMENT '运费设置',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  `system_form_id` int(11) NOT NULL DEFAULT 0 COMMENT '系统表单ID',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用门店ids',
  `store_label_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品标签iDS',
  `ensure_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品保障服务ids',
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品参数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '拼团商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_config
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_config`;
CREATE TABLE `zk_store_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `print_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联打印机ID',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `key_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置key',
  `value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '值',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`, `relation_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '门店配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_coupon_issue
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_coupon_issue`;
CREATE TABLE `zk_store_coupon_issue`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cid` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券ID',
  `category` tinyint(1) NOT NULL DEFAULT 0 COMMENT '优惠券种类：1 普通券，2会员券',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '优惠券类型 0-通用 1-品类券 2-商品券 3-品牌',
  `receive_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 手动领取，3赠送券',
  `coupon_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:满减券2:折扣券',
  `coupon_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券领取开启时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券领取结束时间',
  `total_count` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券领取数量',
  `remain_count` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券剩余领取数量',
  `is_permanent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否无限张数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1 正常 0 未开启 -1 已无效',
  `is_give_subscribe` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否首次关注赠送 0-否(默认) 1-是',
  `is_full_give` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否满赠0-否(默认) 1-是',
  `full_reduction` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '消费满多少赠送优惠券',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券添加时间',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `integral` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '兑换消耗积分值',
  `coupon_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '兑换的优惠券面值',
  `use_min_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最低消费多少金额可用优惠券',
  `coupon_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券有效期限（单位：天）',
  `product_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '所属商品id',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `start_use_time` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券使用开始时间',
  `end_use_time` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券使用结束时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用门店ids',
  `rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '优惠券规则',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `receive_type`(`receive_type`) USING BTREE,
  INDEX `remain_count`(`remain_count`) USING BTREE,
  INDEX `start_time`(`start_time`, `end_time`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券前台领取表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_coupon_issue_user
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_coupon_issue_user`;
CREATE TABLE `zk_store_coupon_issue_user`  (
  `uid` int(11) NULL DEFAULT 0 COMMENT '领取优惠券用户ID',
  `issue_coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券前台领取ID',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '领取时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券前台用户领取记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_coupon_product
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_coupon_product`;
CREATE TABLE `zk_store_coupon_product`  (
  `coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠卷模板id',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  INDEX `coupon_id`(`coupon_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠卷模板关联列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_coupon_user
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_coupon_user`;
CREATE TABLE `zk_store_coupon_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券发放记录id',
  `cid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '兑换的项目id',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券所属用户',
  `coupon_title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `coupon_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠券的面值',
  `use_min_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最低消费多少金额可用优惠券',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券创建时间',
  `start_time` int(11) NOT NULL DEFAULT 0,
  `end_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券结束时间',
  `use_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用时间',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'send' COMMENT '获取方式',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0：未使用，1：已使用, 2:已过期）',
  `is_fail` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否有效',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `cid`(`cid`) USING BTREE,
  INDEX `end_time`(`end_time`) USING BTREE,
  INDEX `is_fail`(`is_fail`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1176 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券发放记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_delivery_order
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_delivery_order`;
CREATE TABLE `zk_store_delivery_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `oid` int(10) UNSIGNED NOT NULL COMMENT '订单ID',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `station_type` int(10) UNSIGNED NOT NULL COMMENT '平台类型',
  `order_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单ID',
  `delivery_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '配送方订单号',
  `city_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '所属城市',
  `cargo_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '配送订单价格',
  `finish_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货码',
  `user_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `receiver_phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货人电话',
  `from_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '起始位置',
  `to_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '结束位置',
  `from_lat` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `from_lng` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `to_lat` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `to_lng` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `distance` float NOT NULL DEFAULT 0 COMMENT '配送距离',
  `fee` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '配送费',
  `deduct_fee` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '取消订单违约金',
  `mer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单备注',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态 取消=-1, 待取货＝2,配送中＝3,已完成＝4,物品返回中=9,物品返回完成=10,骑士到店=100',
  `reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '取消原因',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '发货配送订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_discounts
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_discounts`;
CREATE TABLE `zk_store_discounts`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '套餐名称',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组合套餐主图',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '套餐类型0固定1搭配',
  `is_limit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否限量0不限量1限量',
  `limit_num` int(11) NOT NULL DEFAULT 0 COMMENT '限量个数',
  `link_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联标签',
  `product_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品IDS',
  `is_time` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否限时0不限时1限时',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '开始时间',
  `stop_time` int(11) NOT NULL DEFAULT 0 COMMENT '结束时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `free_shipping` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否包邮0不包邮1包邮',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否上架0不上架1上架',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除0未删除1已删除',
  `is_support_refund` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持退款',
  `delivery_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流类型',
  `freight` tinyint(1) NOT NULL DEFAULT 2 COMMENT '运费设置',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合套餐表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_discounts_products
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_discounts_products`;
CREATE TABLE `zk_store_discounts_products`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `discount_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠套餐ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '商品名称',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品图',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否搭配0不是1是',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '运费模版Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合套餐商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_extract
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_extract`;
CREATE TABLE `zk_store_extract`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `store_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '门店ID',
  `store_staff_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '门店店员ID',
  `extract_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'bank' COMMENT 'bank = 银行卡 alipay = 支付宝wx=微信',
  `bank_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '银行卡',
  `bank_address` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开户地址',
  `alipay_account` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `wechat` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `qrcode_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码地址',
  `extract_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `balance` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '-1 未通过 0 审核中 1 已提现',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 未转账 1 已转账',
  `store_mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '门店备注',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `fail_msg` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '无效原因',
  `fail_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `voucher_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '转账凭证',
  `voucher_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '转账说明',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `extract_type`(`extract_type`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `store_id`(`store_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_finance_flow
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_finance_flow`;
CREATE TABLE `zk_store_finance_flow`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员id',
  `order_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `link_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联订单',
  `pm` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
  `number` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '明细',
  `type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易类型(1:支付订单,2,门店订单,3,订单手续费,4:退款订单,5:充值返点,6:付费会员返点,7:充值订单,8:付费订单,9:收银订单\r\n)',
  `pay_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `pay_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
  `total_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `rate` smallint(6) NOT NULL DEFAULT 0 COMMENT '费率',
  `trade_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:流水，2：交易',
  `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '门店备注',
  `trade_time` int(11) NOT NULL DEFAULT 0 COMMENT '交易时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `staff_id`(`staff_id`) USING BTREE,
  INDEX `store_id`(`store_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30766 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店资金流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_integral
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_integral`;
CREATE TABLE `zk_store_integral`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '积分商品ID',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `image` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品图片',
  `images` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '轮播图',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '活动标题',
  `price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '价格',
  `integral` int(11) NULL DEFAULT 0 COMMENT '积分价格',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `unit_name` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '单位名',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
  `add_time` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '添加时间',
  `is_host` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '推荐',
  `is_show` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '商品状态',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '最多积分几个',
  `quota` int(11) NOT NULL DEFAULT 0 COMMENT '限购总数',
  `once_num` int(11) NOT NULL DEFAULT 0 COMMENT '单次购买个数',
  `quota_show` int(11) NOT NULL DEFAULT 0,
  `delivery_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '物流类型',
  `freight` tinyint(1) NOT NULL DEFAULT 2 COMMENT '运费设置1：包邮，2：固定运费，3：运费模版',
  `postage` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '邮费',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '运费模板ID',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  `system_form_id` int(11) NOT NULL DEFAULT 0 COMMENT '系统表单ID',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '适用门店ids',
  `store_label_id` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '商品标签iDS',
  `ensure_id` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '商品保障服务ids',
  `specs` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '商品参数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '积分商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_integral_order
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_integral_order`;
CREATE TABLE `zk_store_integral_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '订单号',
  `trade_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付订单号',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `real_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户姓名',
  `user_phone` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户电话',
  `user_address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `image` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品图片',
  `store_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `suk` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '规格',
  `unique` char(8) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '唯一值',
  `cart_info` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '积分商品信息',
  `total_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单商品总数',
  `price` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '价格',
  `total_price` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '总价格',
  `integral` int(11) NULL DEFAULT 0 COMMENT '积分价格',
  `total_integral` int(11) NULL DEFAULT 0 COMMENT '总积分',
  `paid` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态',
  `pay_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付时间',
  `pay_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1：待发货；2：待收货；3：已完成；',
  `delivery_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递名称/送货人姓名',
  `delivery_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递公司编码',
  `delivery_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发货类型',
  `delivery_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递单号/手机号',
  `fictitious_content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '虚拟发货内容',
  `delivery_uid` int(11) NOT NULL DEFAULT 0 COMMENT '配送员id',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '管理员备注',
  `mer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `is_mer_check` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `is_remind` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消息提醒',
  `is_system_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '后台是否删除',
  `channel_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户访问端标识',
  `province` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户省份',
  `express_dump` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '订单面单打印信息',
  `verify_code` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '核销码',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `virtual_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '虚拟商品信息',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id_2`(`order_id`, `uid`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '积分订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_integral_order_status
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_integral_order_status`;
CREATE TABLE `zk_store_integral_order_status`  (
  `oid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `change_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作类型',
  `change_message` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作备注',
  `change_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
  INDEX `change_type`(`change_type`) USING BTREE,
  INDEX `oid`(`oid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '积分订单操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_newcomer
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_newcomer`;
CREATE TABLE `zk_store_newcomer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `ot_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '市场价',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '新人专享商品' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order`;
CREATE TABLE `zk_store_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `type` smallint(6) NOT NULL DEFAULT 0 COMMENT '类型 0:普通、1：秒杀、2:砍价、3:拼团、4:积分、5:套餐、6:预售、7:新人礼、8:抽奖、9:拼单、10:桌码',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父类订单id',
  `order_id` varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '订单号',
  `supplier_id` int(11) NOT NULL DEFAULT 0 COMMENT '供应商ID',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `trade_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付宝订单号',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `real_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户姓名',
  `user_phone` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户电话',
  `user_reserve_type` tinyint(4) NULL DEFAULT 1 COMMENT '(1:系统商品、2:理发商品、3:洗车商品)',
  `user_reserve_time` varchar(22) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户预约时间',
  `user_reserve_pay_type` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `user_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `user_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户地址定位',
  `cart_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '购物车ids',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动商品ID',
  `activity_append` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动附加字段',
  `freight_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '运费金额',
  `total_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单商品总数',
  `total_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '订单总价',
  `total_postage` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '邮费',
  `pay_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
  `pay_postage` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付邮费',
  `pay_integral` int(11) NOT NULL DEFAULT 0 COMMENT '实际支付积分',
  `deduction_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '抵扣金额',
  `coupon_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `coupon_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠券金额',
  `promotions_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠活动优惠金额',
  `first_order_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '首单优惠金额',
  `change_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '改价优惠金额',
  `paid` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态',
  `pay_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付时间',
  `pay_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态（0：待发货；1：待收货；2：已收货；3：待评价；4：部分发货；5：部分核销；）',
  `refund_status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 未退款 1 申请中 2 已退款 3 部分退款',
  `refund_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款申请类型1:仅退款2：退款退款3：拒绝4：同意退货5：已退货6：已退款',
  `refund_express` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货快递单号',
  `refund_reason_wap_img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退款图片',
  `refund_reason_wap_explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款用户说明',
  `refund_reason_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '退款时间',
  `refund_reason_wap` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '前台退款原因',
  `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '不退款的理由',
  `refund_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `delivery_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递名称/送货人姓名',
  `delivery_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递公司编码',
  `delivery_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发货类型',
  `delivery_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递单号/手机号',
  `fictitious_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '虚拟发货内容',
  `delivery_uid` int(11) NOT NULL DEFAULT 0 COMMENT '配送员id',
  `gain_integral` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '消费赚取积分',
  `use_integral` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '使用积分',
  `back_integral` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '给用户退了多少积分',
  `spread_uid` int(11) NOT NULL DEFAULT 0 COMMENT '上级推广人uid',
  `spread_two_uid` int(11) NOT NULL DEFAULT 0 COMMENT '上上级推广人uid',
  `one_brokerage` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '一级返佣金额',
  `two_brokerage` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '二级返佣金额',
  `mark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `unique` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一id(md5加密)类似id',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理员备注',
  `mer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `is_mer_check` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `pink_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团id 0没有拼团',
  `cost` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `verify_code` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销码',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员ID',
  `shipping_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式 1=快递 ，2=门店自提，3=门店配送，4=门店收银',
  `clerk_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员/管理员uid',
  `is_channel` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付渠道(0微信公众号1微信小程序)',
  `is_remind` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消息提醒',
  `is_system_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '后台是否删除',
  `channel_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户访问端标识',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户省份',
  `express_dump` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单面单打印信息',
  `kuaidi_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递单号图片',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `virtual_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '虚拟商品信息',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  `promotions_give` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '优惠活动赠送',
  `give_integral` int(11) NOT NULL DEFAULT 0 COMMENT '优惠活动赠送积分',
  `give_coupon` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '优惠活动赠送优惠券',
  `erp_id` int(11) NOT NULL DEFAULT 0 COMMENT 'ERP内部单号',
  `erp_order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ERP订单ID',
  `kuaidi_task_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家寄件任务ID',
  `kuaidi_order_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家寄件订单编号',
  `is_stock_up` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否商家寄件 1：是；0 否',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id_2`(`order_id`, `uid`) USING BTREE,
  UNIQUE INDEX `unique`(`unique`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `erp_order_id`(`erp_order_id`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `is_system_del`(`is_system_del`) USING BTREE,
  INDEX `paid`(`paid`) USING BTREE,
  INDEX `pay_time`(`pay_time`) USING BTREE,
  INDEX `pay_type`(`pay_type`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `pid_2`(`uid`, `is_del`, `is_system_del`) USING BTREE,
  INDEX `refund_status`(`refund_status`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `store_id`(`store_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `uid_2`(`uid`, `paid`, `status`, `refund_status`, `is_del`, `pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9320 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order_cart_info
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order_cart_info`;
CREATE TABLE `zk_store_order_cart_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `oid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `cart_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '购物车id',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员ID',
  `delivery_id` int(11) NOT NULL DEFAULT 0 COMMENT '配送员ID',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `sku_unique` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'sku唯一值',
  `promotions_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联使用优惠活动ID',
  `is_gift` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是赠品1:是0:否',
  `is_support_refund` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持退款',
  `old_cart_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拆单前cart_id',
  `cart_num` int(11) NOT NULL DEFAULT 0 COMMENT '数量	',
  `refund_num` int(11) NOT NULL DEFAULT 0 COMMENT '退款数量',
  `surplus_num` int(11) NOT NULL DEFAULT 0 COMMENT '核销剩余数量',
  `split_surplus_num` int(11) NOT NULL DEFAULT 0 COMMENT '拆分剩余数量',
  `split_status` int(11) NOT NULL DEFAULT 0 COMMENT '0:未拆分1:还可以拆分2：拆分完成',
  `write_times` int(11) NOT NULL DEFAULT 0 COMMENT '核销总次数',
  `write_surplus_times` int(11) NOT NULL DEFAULT 0 COMMENT '核销剩余次数',
  `write_start` int(11) NOT NULL DEFAULT 0 COMMENT '核销开始时间：0不限制',
  `write_end` int(11) NOT NULL DEFAULT 0 COMMENT '核销结束时间：0不限制',
  `is_advent_sms` tinyint(1) NOT NULL DEFAULT 0 COMMENT '临期提醒是否发送',
  `is_expire_sms` tinyint(1) NOT NULL DEFAULT 0 COMMENT '过期提醒是否发送',
  `is_writeoff` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否核销',
  `writeoff_time` int(11) NOT NULL DEFAULT 0 COMMENT '核销时间',
  `cart_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '购买东西的详细信息',
  `unique` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一id',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `oid`(`oid`, `unique`) USING BTREE,
  INDEX `cart_id`(`cart_id`) USING BTREE,
  INDEX `old_cart_id`(`old_cart_id`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE,
  INDEX `split_status`(`split_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9462 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单购物详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order_economize
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order_economize`;
CREATE TABLE `zk_store_order_economize`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `order_type` tinyint(1) UNSIGNED ZEROFILL NOT NULL DEFAULT 1 COMMENT '配送方式 1=商品订单 ，2=线下订单',
  `pay_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
  `postage_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '邮费优惠金额',
  `member_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '会员优惠金额',
  `offline_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '线下优惠金额',
  `coupon_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠券优惠金额',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态',
  PRIMARY KEY (`id`, `order_id`, `uid`) USING BTREE,
  UNIQUE INDEX `order_id_2`(`order_id`, `uid`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `pay_price`(`pay_price`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 145 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户资金节省表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order_invoice
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order_invoice`;
CREATE TABLE `zk_store_order_invoice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `category` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'order' COMMENT '开票分类 order:订单',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `invoice_id` int(11) NOT NULL DEFAULT 0 COMMENT '发票id',
  `header_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '抬头类型',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '发票类型',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称（发票抬头）',
  `duty_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '税号',
  `drawer_phone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票人手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票人邮箱',
  `tell` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册电话',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册地址',
  `bank` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户行',
  `card_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行卡号',
  `is_pay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支付',
  `is_refund` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单是否退款',
  `is_invoice` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0：未开票 1:已开票 -1:已拒绝',
  `invoice_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票票号',
  `invoice_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '开票金额',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `invoice_time` int(11) NOT NULL DEFAULT 0 COMMENT '开票时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户开票表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order_promotions
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order_promotions`;
CREATE TABLE `zk_store_order_promotions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `oid` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `promotions_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠活动ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `promotions_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 256 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单优惠活动辅助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order_refund`;
CREATE TABLE `zk_store_order_refund`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单表ID',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店ID',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户UID',
  `supplier_id` int(11) NOT NULL DEFAULT 0 COMMENT '供应商ID',
  `apply_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '申请售后类型:1:仅退款2:退货退款(快递退回)3:退货退款(到店退货)4:平台退款',
  `apply_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '申请退款金额',
  `refund_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款处理类型0:未处理3：拒绝4：同意退货5：已退货6：已退款',
  `refund_num` int(11) NOT NULL DEFAULT 0 COMMENT '退款件数',
  `refund_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refunded_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '已退款金额',
  `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款的理由',
  `refund_goods_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '退货方式1:不退货2:快递退回3:到店退货',
  `refund_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款电话',
  `refund_express` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货快递单号',
  `refund_express_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货快递名称',
  `refund_explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款用户说明',
  `refund_img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退款图片',
  `refund_goods_explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货用户说明',
  `refund_goods_img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退货凭证',
  `refuse_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `refunded_time` int(11) NOT NULL DEFAULT 0 COMMENT '处理时间',
  `cart_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退款商品信息',
  `is_cancel` tinyint(1) NOT NULL DEFAULT 0 COMMENT '用户是否取消',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '申请退款时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `refund_type`(`is_cancel`, `store_order_id`) USING BTREE,
  INDEX `store_id`(`store_id`) USING BTREE,
  INDEX `store_order_id`(`store_order_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 737 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售后订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order_status
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order_status`;
CREATE TABLE `zk_store_order_status`  (
  `oid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `change_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作类型',
  `change_message` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作备注',
  `change_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
  INDEX `change_type`(`change_type`) USING BTREE,
  INDEX `oid`(`oid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_order_writeoff
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_order_writeoff`;
CREATE TABLE `zk_store_order_writeoff`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户UID',
  `oid` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `order_cart_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单商品ID',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `writeoff_num` int(11) NOT NULL DEFAULT 1 COMMENT '核销数量',
  `writeoff_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '核销金额',
  `writeoff_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销码',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '核销时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2227 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单核销记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_pink
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_pink`;
CREATE TABLE `zk_store_pink`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单id 生成',
  `order_id_key` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id  数据库',
  `total_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买商品个数',
  `total_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '购买总金额',
  `cid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团商品id',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
  `people` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼图总人数',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '拼团商品单价',
  `add_time` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '开始时间',
  `stop_time` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
  `k_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '团长id 0为团长',
  `is_tpl` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否发送模板消息0未发送1已发送',
  `is_refund` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否退款 0未退款 1已退款',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态1进行中2已完成3未完成',
  `is_virtual` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否虚拟拼团',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '拼团表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product`;
CREATE TABLE `zk_store_product`  (
  `id` mediumint(9) NOT NULL AUTO_INCREMENT COMMENT '商品id',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '关联平台商品ID',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `mer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)',
  `image` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品图片',
  `recommend_image` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '推荐图',
  `slider_image` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '轮播图',
  `store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `store_info` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品简介',
  `keyword` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键字',
  `bar_code` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品条码（一维码）',
  `cate_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类id',
  `price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `vip_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '会员价格',
  `ot_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '市场价',
  `postage` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '邮费',
  `delivery_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品配送方式',
  `freight` tinyint(1) NOT NULL DEFAULT 2 COMMENT '运费设置',
  `unit_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位名',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `star` decimal(2, 1) NOT NULL DEFAULT 3.0 COMMENT '商品评分',
  `collect` int(11) NOT NULL DEFAULT 0 COMMENT '商品收藏数',
  `sales` mediumint(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `stock` mediumint(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
  `show_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '展示：0:所有端1:移动端2:收银台',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（0：未上架，1：上架）',
  `is_hot` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否热卖',
  `is_benefit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否优惠',
  `is_best` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否精品',
  `is_new` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否新品',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_postage` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否包邮',
  `is_verify` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否审核：-2强制下架-1未通过0未审核1:通过',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `mer_use` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户是否代理 0不可代理1可代理',
  `give_integral` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '获得积分',
  `cost` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `is_seckill` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '秒杀状态 0 未开启 1已开启',
  `is_bargain` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价状态 0未开启 1开启',
  `is_good` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否优品推荐',
  `is_sub` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否单独分佣',
  `is_vip` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启会员价格',
  `ficti` mediumint(9) NOT NULL DEFAULT 100 COMMENT '虚拟销量',
  `browse` int(11) NOT NULL DEFAULT 0 COMMENT '浏览量',
  `code_path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品二维码地址(用户小程序海报)',
  `soure_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '淘宝京东1688类型',
  `video_open` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品视频是否开启',
  `video_link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主图视频链接',
  `temp_id` int(11) NOT NULL DEFAULT 1 COMMENT '运费模板ID',
  `spec_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '规格 0单 1多',
  `activity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动显示排序1=秒杀，2=砍价，3=拼团',
  `spu` char(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品SPU',
  `label_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签ID',
  `command_word` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '复制口令',
  `recommend_list` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '推荐产品',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌id',
  `brand_com` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌组合',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '编码',
  `is_vip_product` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否会员专属商品',
  `is_presale_product` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否预售商品',
  `presale_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '预售开始时间',
  `presale_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '预售结束时间',
  `presale_day` int(11) NOT NULL DEFAULT 0 COMMENT '预售结束后几天内发货',
  `auto_on_time` int(11) NOT NULL DEFAULT 0 COMMENT '自动上架时间',
  `auto_off_time` int(11) NOT NULL DEFAULT 0 COMMENT '自动下架时间',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  `system_form_id` int(11) NOT NULL DEFAULT 0 COMMENT '系统表单ID',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用门店ids',
  `is_support_refund` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持退款',
  `store_label_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品标签iDS',
  `ensure_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品保障服务ids',
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品参数',
  `specs_id` int(11) NOT NULL DEFAULT 0 COMMENT '参数模版ID',
  `is_limit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启限购',
  `limit_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '限购类型1单次限购2永久限购',
  `limit_num` int(11) NOT NULL DEFAULT 0 COMMENT '限购数量',
  `refusal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核拒绝原因',
  `is_police` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否警告',
  `is_sold` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否售罄',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `cate_id`(`cate_id`) USING BTREE,
  INDEX `is_good`(`is_hot`) USING BTREE,
  INDEX `is_postage`(`is_postage`) USING BTREE,
  INDEX `is_show`(`is_show`) USING BTREE,
  INDEX `price`(`price`) USING BTREE,
  INDEX `sales`(`sales`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `toggle_on_sale, is_del`(`is_del`) USING BTREE,
  INDEX `type`(`type`, `relation_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16319 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_attr
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_attr`;
CREATE TABLE `zk_store_product_attr`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `attr_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '属性名',
  `attr_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '属性值',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '活动类型 0=商品，1=秒杀，2=砍价，3=拼团',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `store_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57944 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品属性表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_attr_result
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_attr_result`;
CREATE TABLE `zk_store_product_attr_result`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品属性参数',
  `change_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '上次修改时间',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '活动类型 0=商品，1=秒杀，2=砍价，3=拼团',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16199 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品属性详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_attr_value
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_attr_value`;
CREATE TABLE `zk_store_product_attr_value`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '活动类型 0=商品，1=秒杀，2=砍价，3=拼团，4=积分商品，5=套餐，7=新人专享',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `suk` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品属性索引值 (attr_value|attr_value[|....])',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '属性对应的库存',
  `sum_stock` int(11) NOT NULL DEFAULT 0 COMMENT '平台库存+门店库存总和',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '属性金额',
  `settle_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '结算价',
  `integral` int(11) NULL DEFAULT 0 COMMENT '积分价格',
  `image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `unique` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一值',
  `cost` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `bar_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品条码',
  `ot_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '原价',
  `vip_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '会员专享价',
  `weight` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '重量',
  `volume` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '体积',
  `brokerage` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '一级返佣',
  `brokerage_two` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '二级返佣',
  `quota` int(11) NOT NULL DEFAULT 0 COMMENT '活动限购数量',
  `quota_show` int(11) NOT NULL DEFAULT 0 COMMENT '活动限购数量显示',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '编码',
  `disk_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '虚拟信息内容',
  `write_times` int(11) NOT NULL DEFAULT 1 COMMENT '核销次数',
  `write_valid` tinyint(1) NOT NULL DEFAULT 1 COMMENT '核销时效：1、永久； 2、购买后几天；3、固定',
  `write_days` int(11) NOT NULL DEFAULT 0 COMMENT '购买后：N天有效',
  `write_start` int(11) NOT NULL DEFAULT 0 COMMENT '核销开始时间',
  `write_end` int(11) NOT NULL DEFAULT 0 COMMENT '核销结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `store_id`(`product_id`, `suk`) USING BTREE,
  INDEX `unique`(`unique`, `suk`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2147487323 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品属性值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_cate
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_cate`;
CREATE TABLE `zk_store_product_cate`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `cate_pid` int(11) NOT NULL DEFAULT 0 COMMENT '一级分类id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分类辅助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_category
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_category`;
CREATE TABLE `zk_store_product_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品分类表ID',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父id',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `cate_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '等级',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `pic` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否推荐',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `big_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类大图',
  `adv_pic` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '广告图片',
  `adv_link` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '广告链接',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `is_base`(`is_show`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_category_brand
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_category_brand`;
CREATE TABLE `zk_store_product_category_brand`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `brand_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌名称',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `cate_id`(`cate_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分类品牌关联' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_coupon
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_coupon`;
CREATE TABLE `zk_store_product_coupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `issue_coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠劵id',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠券名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品关联优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_description
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_description`;
CREATE TABLE `zk_store_product_description`  (
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品详情',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型',
  INDEX `product_id`(`product_id`, `type`) USING BTREE,
  INDEX `type`(`type`, `product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_ensure
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_ensure`;
CREATE TABLE `zk_store_product_ensure`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `relation_id`(`relation_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品保障服务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_label
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_label`;
CREATE TABLE `zk_store_product_label`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `label_cate` int(11) NOT NULL DEFAULT 0 COMMENT '标签分类',
  `label_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `style_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '样式类型1：自定义2：图片',
  `color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '颜色',
  `bg_color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景颜色',
  `border_color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '边框颜色',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图标',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '移动端是否展示',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态是否开启',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `label_cate`(`label_cate`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_label_auxiliary
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_label_auxiliary`;
CREATE TABLE `zk_store_product_label_auxiliary`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `label_id` int(11) NOT NULL DEFAULT 0,
  `product_id` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `label_id`(`label_id`, `product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品标签辅助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_log
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_log`;
CREATE TABLE `zk_store_product_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('visit','cart','order','pay','collect','refund') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'visit' COMMENT '类型',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `visit_num` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否浏览',
  `cart_num` int(11) NOT NULL DEFAULT 0 COMMENT '加入购物车数量',
  `order_num` int(11) NOT NULL DEFAULT 0 COMMENT '下单数量',
  `pay_num` int(11) NOT NULL DEFAULT 0 COMMENT '支付数量',
  `pay_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `cost_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品成本价',
  `pay_uid` int(11) NOT NULL DEFAULT 0 COMMENT '支付用户ID',
  `refund_num` int(11) NOT NULL DEFAULT 0 COMMENT '退款数量',
  `refund_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `collect_num` tinyint(1) NOT NULL DEFAULT 0 COMMENT '收藏',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `delete_time` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `uid_type`(`uid`, `type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 131645 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品统计' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_relation
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_relation`;
CREATE TABLE `zk_store_product_relation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL DEFAULT 1 COMMENT '关联关系1：分类2：品牌3：商品标签4：用户标签5：保障服务6：商品参数',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
  `relation_pid` int(11) NOT NULL DEFAULT 0 COMMENT '一级分类id',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '商品状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE,
  INDEX `relation_id`(`relation_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 120049 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品关联辅助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_reply
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_reply`;
CREATE TABLE `zk_store_product_reply`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `oid` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `unique` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '唯一id',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `sku_unique` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'sku唯一值',
  `sku` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'sku名称',
  `reply_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'product' COMMENT '某种商品类型(普通商品、秒杀商品）',
  `reply_score` tinyint(1) NOT NULL DEFAULT 3 COMMENT '评价分数:3赞2中1差',
  `product_score` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品分数',
  `service_score` tinyint(1) NOT NULL DEFAULT 0 COMMENT '服务分数',
  `delivery_score` tinyint(1) NOT NULL DEFAULT 0 COMMENT '物流评分',
  `comment` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '评论内容',
  `pics` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评论图片',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '评论时间',
  `praise` int(11) NOT NULL DEFAULT 0 COMMENT '点赞',
  `views_num` int(11) NOT NULL DEFAULT 0 COMMENT '浏览量',
  `merchant_reply_content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '管理员回复内容',
  `merchant_reply_time` int(11) NOT NULL DEFAULT 0 COMMENT '管理员回复时间',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未删除1已删除',
  `is_reply` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0未回复1已回复',
  `nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户头像',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id_2`(`oid`, `unique`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `parent_id`(`reply_type`) USING BTREE,
  INDEX `product_score`(`product_score`) USING BTREE,
  INDEX `service_score`(`service_score`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 140 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_reply_comment
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_reply_comment`;
CREATE TABLE `zk_store_product_reply_comment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `uid` int(11) NOT NULL DEFAULT 0,
  `praise` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数量',
  `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复内容',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级回复id',
  `reply_id` int(11) NOT NULL DEFAULT 0 COMMENT '评论id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `reply_id`(`reply_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品评价回复表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_rule
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_rule`;
CREATE TABLE `zk_store_product_rule`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `rule_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格名称',
  `rule_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '规格值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 59 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规则值(规格)表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_specs
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_specs`;
CREATE TABLE `zk_store_product_specs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '模版ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参数名称',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参数值',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `relation_id`(`relation_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品参数表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_stock_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_stock_record`;
CREATE TABLE `zk_store_product_stock_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `unique` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格唯一值',
  `cost_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `number` int(11) NOT NULL DEFAULT 0 COMMENT '数量',
  `pm` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:入库0:出库',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'manual' COMMENT '变动类型:sale,transfer_in,transfer_out,adjust,manual',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联单据ID(订单ID/调拨单ID等)',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26169 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品库存记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_unit
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_unit`;
CREATE TABLE `zk_store_product_unit`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位名称',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否上架',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品单位表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_virtual
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_virtual`;
CREATE TABLE `zk_store_product_virtual`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `attr_unique` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对应商品规格',
  `card_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡密卡号',
  `card_pwd` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡密密码',
  `card_unique` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '虚拟卡密唯一值',
  `order_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '购买订单id',
  `order_type` smallint(6) NOT NULL DEFAULT 1 COMMENT '购买订单类型：1:order 2:积分订单',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '购买人id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '虚拟商品卡密表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_product_words
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_product_words`;
CREATE TABLE `zk_store_product_words`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '词',
  `color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '颜色',
  `bg_color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景颜色',
  `border_color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '边框颜色',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图标',
  `is_show` tinyint(1) NULL DEFAULT 0 COMMENT '是否显示',
  `sort` smallint(6) NULL DEFAULT 0 COMMENT '排序',
  `is_search` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '大家都在搜',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '热词',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品搜索热词' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_promotions
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_promotions`;
CREATE TABLE `zk_store_promotions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '同一活动ID',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型：1平台2:门店',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店ID',
  `promotions_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '活动类型：1：限时折扣2:第N件N折3:满减满折4:满送5:活动边框6:活动背景',
  `promotions_cate` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优惠方式1:阶梯2:循环',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动名称',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠标题',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动图',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '优惠内容详情',
  `threshold_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '门槛类型1:满N元2:满N件',
  `threshold` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠门槛',
  `discount_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优惠类型1:满减2:满折',
  `n_piece_n_discount` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'n件n折类型：1:第二件半件2:买1送1 3:自定义',
  `discount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠',
  `give_integral` int(11) NOT NULL DEFAULT 0 COMMENT '赠送积分',
  `give_coupon_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '赠送优惠券ID',
  `give_product_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '赠送商品ID',
  `give_product_unique` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '赠送商品规格唯一值',
  `overlay` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠叠加',
  `label_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联标签ids',
  `product_partake_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '参与商品类型：1:全部商品2：指定商品参与3：指定商品不参与',
  `product_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联商品ids',
  `system_form_id` int(11) NOT NULL DEFAULT 0 COMMENT '系统表单ID',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用门店ids',
  `is_limit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否限量:0不限量1限量',
  `limit_num` int(11) NOT NULL DEFAULT 0 COMMENT '限量个数',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动开始时间',
  `stop_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动结束时间',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后一次修改时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `promotions_type`(`promotions_type`) USING BTREE,
  INDEX `type`(`type`, `store_id`) USING BTREE,
  INDEX `update_time`(`update_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_promotions_auxiliary
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_promotions_auxiliary`;
CREATE TABLE `zk_store_promotions_auxiliary`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型1：关联商品2：赠送优惠券3：赠送商品',
  `promotions_id` int(11) NOT NULL DEFAULT 0,
  `product_partake_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '参与商品类型：1:全部商品2：指定商品参与3：指定商品不参与4：指定品牌参与5：指定标签参与',
  `product_id` int(11) NOT NULL DEFAULT 0,
  `coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌id',
  `store_label_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品标签id',
  `limit_num` int(11) NOT NULL DEFAULT 0 COMMENT '限量个数',
  `surplus_num` int(11) NOT NULL DEFAULT 0 COMMENT '限量剩余个数',
  `is_all` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否是全部规格：1是0否',
  `unique` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品unique',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `promotions_id`(`promotions_id`, `product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠活动辅助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_seckill
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_seckill`;
CREATE TABLE `zk_store_seckill`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商品秒杀商品表id',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动ID',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品所属：0：平台1:门店2:供应商',
  `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联门店、供应商ID',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '推荐图',
  `images` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '轮播图',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动标题',
  `info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `cost` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本',
  `ot_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '原价',
  `give_integral` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '返多少积分',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `unit_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位名',
  `postage` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '邮费',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `start_time` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开始时间',
  `stop_time` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结束时间',
  `add_time` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加时间',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '商品状态',
  `is_postage` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否包邮',
  `is_hot` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '热门推荐',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除 0未删除1已删除',
  `num` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最多秒杀几个',
  `is_show` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '显示',
  `time_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间段ID，多个',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '运费模板ID',
  `weight` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '商品重量',
  `volume` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '商品体积',
  `quota` int(11) NOT NULL DEFAULT 0 COMMENT '限购总数',
  `quota_show` int(11) NOT NULL DEFAULT 0 COMMENT '限购总数显示',
  `once_num` int(11) NOT NULL DEFAULT 0 COMMENT '单次购买个数',
  `is_support_refund` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持退款',
  `delivery_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流类型',
  `freight` tinyint(1) NOT NULL DEFAULT 2 COMMENT '运费设置',
  `custom_form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单',
  `system_form_id` int(11) NOT NULL DEFAULT 0 COMMENT '系统表单ID',
  `applicable_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '适用门店：0：仅平台1：所有2：部分',
  `applicable_store_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用门店ids',
  `store_label_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品标签iDS',
  `ensure_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品保障服务ids',
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品参数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `is_hot`(`is_hot`) USING BTREE,
  INDEX `is_postage`(`is_postage`) USING BTREE,
  INDEX `is_show`(`status`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `start_time`(`start_time`, `stop_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品秒杀商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_seckill_time
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_seckill_time`;
CREATE TABLE `zk_store_seckill_time`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `pic` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  `describe` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `start_time` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开始时间',
  `end_time` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '结束时间',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1，0状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '秒杀时间段配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_service
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_service`;
CREATE TABLE `zk_store_service`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客服id',
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '客服uid',
  `online` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在线',
  `account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `avatar` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服头像',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '代理名称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服电话',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `account_status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '账号状态,0隐藏1显示',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '客服状态，0隐藏1显示',
  `notify` int(11) NOT NULL DEFAULT 0 COMMENT '订单通知1开启0关闭',
  `customer` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否展示统计管理',
  `uniqid` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客服表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_service_feedback
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_service_feedback`;
CREATE TABLE `zk_store_service_feedback`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户UID',
  `rela_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `phone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '电话',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '反馈内容',
  `make` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0=未查看，1=已查看',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '客服反馈表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_service_log
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_service_log`;
CREATE TABLE `zk_store_service_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客服用户对话记录表ID',
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id',
  `msn` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息内容',
  `uid` int(11) NOT NULL DEFAULT 1 COMMENT '发送人uid',
  `to_uid` int(11) NOT NULL DEFAULT 1 COMMENT '接收人uid',
  `is_tourist` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1=游客模式，0=非游客',
  `time_node` tinyint(1) NOT NULL DEFAULT 0 COMMENT '时间节点',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '发送时间',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读（0：否；1：是；）',
  `remind` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提醒过',
  `msn_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '消息类型 1=文字 2=表情 3=图片 4=语音',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 810 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客服用户对话记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_service_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_service_record`;
CREATE TABLE `zk_store_service_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '发送人的uid',
  `to_uid` int(11) NOT NULL DEFAULT 0 COMMENT '送达人的uid',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `is_tourist` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是游客',
  `online` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在线',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = pc,1=微信，2=小程序，3=H5',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `mssage_num` int(11) NOT NULL DEFAULT 0 COMMENT '消息条数',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `message_type` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `to_uid`(`to_uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 250 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '客服聊天用户记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_service_speechcraft
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_service_speechcraft`;
CREATE TABLE `zk_store_service_speechcraft`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kefu_id` int(11) NOT NULL DEFAULT 0 COMMENT '0为全局话术',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '0为不分类全局话术',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '话术标题',
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '话术内容',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `cate_id`(`cate_id`) USING BTREE,
  INDEX `kefu_id`(`kefu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '客服话术' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_user
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_user`;
CREATE TABLE `zk_store_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '平台关联用户uid',
  `label_id` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '用户标签',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `store_id`(`store_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3680 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_store_visit
-- ----------------------------
DROP TABLE IF EXISTS `zk_store_visit`;
CREATE TABLE `zk_store_visit`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品类型',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品分类ID',
  `type` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品类型',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '访问次数',
  `content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注描述',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16002 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品浏览分析表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_supplier_extract
-- ----------------------------
DROP TABLE IF EXISTS `zk_supplier_extract`;
CREATE TABLE `zk_supplier_extract`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `supplier_id` int(11) NOT NULL DEFAULT 0 COMMENT '供应商ID',
  `extract_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'bank' COMMENT 'bank = 银行卡 alipay = 支付宝wx=微信',
  `bank_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '银行卡',
  `bank_address` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开户地址',
  `alipay_account` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `wechat` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `qrcode_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码地址',
  `extract_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `balance` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '-1 未通过 0 审核中 1 已提现',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 未转账 1 已转账',
  `supplier_mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '供应商备注',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `fail_msg` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '无效原因',
  `fail_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `voucher_image` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '转账凭证',
  `voucher_title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '转账说明',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `extract_type`(`extract_type`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `supplier_id`(`supplier_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '供应商提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_supplier_flowing_water
-- ----------------------------
DROP TABLE IF EXISTS `zk_supplier_flowing_water`;
CREATE TABLE `zk_supplier_flowing_water`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `supplier_id` int(11) NOT NULL DEFAULT 0 COMMENT '供应商ID',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `order_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `link_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联订单',
  `pm` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
  `number` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '明细',
  `type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易类型(1:支付订单,2:退款订单)',
  `pay_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `pay_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
  `total_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台备注',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '供应商备注',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `trade_time` int(11) NOT NULL DEFAULT 0 COMMENT '交易时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 201 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '供应商流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_supplier_ticket_print
-- ----------------------------
DROP TABLE IF EXISTS `zk_supplier_ticket_print`;
CREATE TABLE `zk_supplier_ticket_print`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `supplier_id` int(11) NOT NULL DEFAULT 0 COMMENT '供应商ID',
  `develop_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `api_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '秘钥',
  `client_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '应用ID',
  `terminal_number` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '终端号',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '开启状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `supplier_id`(`supplier_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_supplier_transactions
-- ----------------------------
DROP TABLE IF EXISTS `zk_supplier_transactions`;
CREATE TABLE `zk_supplier_transactions`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `supplier_id` int(11) NOT NULL DEFAULT 0 COMMENT '供应商ID',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `order_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `link_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联订单',
  `pm` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
  `type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易类型(1:支付订单,2:退款订单)',
  `pay_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `pay_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
  `total_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台备注',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '供应商备注',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `trade_time` int(11) NOT NULL DEFAULT 0 COMMENT '交易时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 201 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '供应商交易表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_admin
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_admin`;
CREATE TABLE `zk_system_admin`  (
  `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '后台管理员表ID',
  `account` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '后台管理员账号',
  `admin_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '管理员类型 1平台 2门店 4供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联ID',
  `head_pic` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `pwd` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '后台管理员密码',
  `real_name` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '后台管理员姓名',
  `phone` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户电话',
  `roles` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '后台管理员权限(menus_id)',
  `last_ip` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '后台管理员最后一次登录ip',
  `last_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '后台管理员最后一次登录时间',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '后台管理员添加时间',
  `login_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '登录次数',
  `level` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '后台管理员级别',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '后台管理员状态 1有效0无效',
  `is_way` tinyint(1) NOT NULL DEFAULT 0 COMMENT '图片上传方式 0=本地上传 1=网络图片 2=扫码上传',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `account`(`account`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '后台管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_attachment
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_attachment`;
CREATE TABLE `zk_system_attachment`  (
  `att_id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型：1平台2:门店',
  `file_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '文件类型：1:图片2：视频',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件名称',
  `att_dir` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件路径',
  `satt_dir` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '压缩图片路径',
  `att_size` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件大小',
  `att_type` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件类型',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `time` int(11) NOT NULL DEFAULT 0 COMMENT '上传时间',
  `image_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '图片上传类型 1本地 2七牛云 3OSS 4COS ',
  `module_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '图片上传模块类型 1 后台上传 2 用户生成',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原始文件名',
  `scan_token` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扫码上传的token',
  PRIMARY KEY (`att_id`) USING BTREE,
  INDEX `file_type`(`file_type`) USING BTREE,
  INDEX `time`(`time`) USING BTREE,
  INDEX `type`(`type`, `relation_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18265 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_attachment_category
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_attachment_category`;
CREATE TABLE `zk_system_attachment_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型：1平台2:门店',
  `file_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '文件类型：1:图片2：视频',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联id',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `enname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类目录',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_city
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_city`;
CREATE TABLE `zk_system_city`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市id',
  `level` int(11) NOT NULL DEFAULT 0 COMMENT '省市级别',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父级id',
  `area_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '区号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `merger_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '合并名称',
  `lng` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `lat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否展示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3971 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '城市表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_config
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_config`;
CREATE TABLE `zk_system_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `is_store` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=总后台,1=门店',
  `menu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型(文本框,单选按钮...)',
  `input_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'input' COMMENT '表单类型',
  `config_tab_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '配置分类id',
  `parameter` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规则 单选框和多选框',
  `upload_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '上传文件格式1单图2多图3文件',
  `required` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规则',
  `width` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '多行文本框的宽度',
  `high` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '多行文框的高度',
  `value` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '默认值',
  `info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置名称',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置简介',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否隐藏',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `config_tab_id`(`config_tab_id`) USING BTREE,
  INDEX `is_store`(`is_store`) USING BTREE,
  INDEX `menu_name`(`menu_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 749 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_config_tab
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_config_tab`;
CREATE TABLE `zk_system_config_tab`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置分类id',
  `is_store` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=总后台,1=门店',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级分类id',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置分类名称',
  `eng_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置分类英文名称',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '配置分类状态',
  `info` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '配置分类是否显示',
  `icon` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '配置类型',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `eng_title`(`eng_title`) USING BTREE,
  INDEX `is_store`(`is_store`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 95 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配置分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_file
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_file`;
CREATE TABLE `zk_system_file`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文件对比ID',
  `cthash` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件内容',
  `filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文价名称',
  `atime` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上次访问时间',
  `mtime` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上次修改时间',
  `ctime` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上次改变时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1613 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件对比表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_form
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_form`;
CREATE TABLE `zk_system_form`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单名称',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面图',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单数据',
  `default_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '默认数据',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否使用',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统表单数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_form_data
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_form_data`;
CREATE TABLE `zk_system_form_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户UID',
  `system_form_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单名称',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '收集数据来源：1订单',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联数据来源ID',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '收集数据',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表单收集数据记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_group
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_group`;
CREATE TABLE `zk_system_group`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '组合数据ID',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '数据组名称',
  `info` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '数据提示',
  `config_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '数据字段',
  `fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据组字段以及类型（json数据）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_name`(`config_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 70 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_group_data
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_group_data`;
CREATE TABLE `zk_system_group_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '组合数据详情ID',
  `gid` int(11) NOT NULL DEFAULT 0 COMMENT '对应的数据组id',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据组对应的数据值（json数据）',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加数据时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '数据排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（1：开启；2：关闭；）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 745 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合数据详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_log
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_log`;
CREATE TABLE `zk_system_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员操作记录ID',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员id',
  `admin_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理员姓名',
  `path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `page` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '行为',
  `method` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '访问类型',
  `ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录IP',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
  `merchant_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 475418 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_menus
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_menus`;
CREATE TABLE `zk_system_menus`  (
  `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `pid` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级id',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '菜单类型1:平台2:门店',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `menu_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '按钮名',
  `module` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '模块名',
  `controller` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '控制器',
  `action` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '方法名',
  `api_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api接口地址',
  `methods` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提交方式POST GET PUT DELETE',
  `params` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '[]' COMMENT '参数',
  `sort` tinyint(4) NOT NULL DEFAULT 1 COMMENT '排序',
  `is_show` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否为隐藏菜单0=隐藏菜单,1=显示菜单',
  `is_show_path` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为隐藏菜单供前台使用',
  `access` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '子管理员是否可用',
  `menu_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路由名称 前端使用',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `auth_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为菜单 1菜单 2功能',
  `header` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '顶部菜单标示',
  `is_header` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否顶部菜单1是0否',
  `unique_auth` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '前台唯一标识',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `access`(`access`) USING BTREE,
  INDEX `is_show`(`is_show`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1589 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_message
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_message`;
CREATE TABLE `zk_system_message`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mark` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标识',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '通知标题',
  `content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '通知内容',
  `look` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否查看',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:普通用户，2：管理员',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '通知时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`, `look`, `is_del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35152 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统通知' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_notice
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_notice`;
CREATE TABLE `zk_system_notice`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知模板id',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知标题',
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知类型',
  `icon` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `table_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知数据',
  `template` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知模板',
  `push_admin` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知管理员id',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `type`(`type`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_notice_admin
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_notice_admin`;
CREATE TABLE `zk_system_notice_admin`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知记录ID',
  `notice_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知类型',
  `admin_id` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '通知的管理员',
  `link_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID',
  `table_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '通知的数据',
  `is_click` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击次数',
  `is_visit` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '访问次数',
  `visit_time` int(11) NOT NULL DEFAULT 0 COMMENT '访问时间',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '通知时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `admin_id`(`admin_id`, `notice_type`) USING BTREE,
  INDEX `is_click`(`is_click`) USING BTREE,
  INDEX `is_visit`(`is_visit`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_notification
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_notification`;
CREATE TABLE `zk_system_notification`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mark` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标识',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '通知类型',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '通知场景说明',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '站内信（0：不存在，1：开启，2：关闭）',
  `is_app` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'APP推送（0：不存在，1：开启，2：关闭）',
  `is_wechat` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公众号模板消息（0：不存在，1：开启，2：关闭）',
  `is_routine` tinyint(1) NOT NULL DEFAULT 0 COMMENT '小程序订阅消息（0：不存在，1：开启，2：关闭）',
  `is_sms` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发送短信（0：不存在，1：开启，2：关闭）',
  `is_ent_wechat` tinyint(1) NOT NULL DEFAULT 0 COMMENT '企业微信群通知（0：不存在，1：开启，2：关闭）',
  `system_title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '站内信标题',
  `system_text` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '系统消息id',
  `app_id` int(11) NOT NULL DEFAULT 0 COMMENT 'app推送id',
  `wechat_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '模板消息id',
  `routine_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '订阅消息id',
  `sms_id` int(11) NOT NULL DEFAULT 0 COMMENT '短信id',
  `ent_wechat_text` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '企业微信消息',
  `variable` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '变量',
  `url` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '群机器人链接',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型（1：用户，2：管理员）',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '通知设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_printer
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_printer`;
CREATE TABLE `zk_system_printer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打印机名称',
  `plat_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '打印机类型：1：易联运，2：飞蛾云',
  `print_event` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '2' COMMENT '打印事件1：下单后2：支付后',
  `print_num` tinyint(1) NOT NULL DEFAULT 1 COMMENT '打印联数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '打印机状态',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '打印机' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_role
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_role`;
CREATE TABLE `zk_system_role`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '身份管理id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `role_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份管理名称',
  `rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '身份管理权限(menus_id)',
  `cashier_rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '门店角色管理收银台权限(menus_id)',
  `level` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 94 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '身份管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_sign_reward
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_sign_reward`;
CREATE TABLE `zk_system_sign_reward`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型，0连续签到奖励，1累计签到奖励',
  `days` int(11) NOT NULL DEFAULT 0 COMMENT '天数',
  `point` int(11) NOT NULL DEFAULT 0 COMMENT '赠送积分',
  `exp` int(11) NOT NULL DEFAULT 0 COMMENT '赠送经验',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统签到奖励表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_storage
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_storage`;
CREATE TABLE `zk_system_storage`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `access_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'access_key',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=本地存储,2=七牛,3=oss,4=cos',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '空间名',
  `region` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '地域',
  `acl` enum('private','public-read','public-read-write') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'public-read' COMMENT '权限',
  `domain` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '空间域名',
  `cname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'CNAME值',
  `is_ssl` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=http,1=https',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加事件',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新事件',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '云储存' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_store
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_store`;
CREATE TABLE `zk_system_store`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '门店类型：1自营2加盟',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店分类ID',
  `cate_com` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店分类组合',
  `erp_shop_id` int(11) NOT NULL DEFAULT 0 COMMENT 'EPR门店id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店名称',
  `introduction` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `phone` char(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省市区',
  `province` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `city` int(11) NOT NULL DEFAULT 0 COMMENT '市ID',
  `area` int(11) NOT NULL DEFAULT 0 COMMENT '区ID',
  `street` int(11) NOT NULL DEFAULT 0 COMMENT '街道ID',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店logo',
  `background_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店背景图',
  `oblong_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店推荐图',
  `latitude` char(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `longitude` char(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `bank_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '银行卡',
  `bank_address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户地址',
  `alipay_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `alipay_qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付宝二维码地址',
  `wechat` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `wechat_qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信二维码地址',
  `valid_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销有效日期',
  `valid_range` int(11) NOT NULL DEFAULT 0 COMMENT '有效距离',
  `day_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '每日营业开关时间',
  `day_start` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '每日营业开始时间',
  `day_end` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '每日营业结束时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `is_store` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否到店自提',
  `default_delivery` tinyint(1) NOT NULL DEFAULT 1 COMMENT '默认设置门店配送方式 1=配送  2=自提',
  `customer_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '门店客服类型：1：电话2:企业微信二维码',
  `home_style` tinyint(1) NOT NULL DEFAULT 1 COMMENT '门店首页样式：1：分类商品2:商品筛选',
  `city_delivery_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '同城配送是否开启：1：开启0:关闭',
  `city_delivery_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '同城配送类型：0：不配送1：达达2:uu',
  `delivery_goods_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '同城配送类型：0：不配送1：达达2:uu',
  `business` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支持配送的物品品类',
  `city_shop_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '同城配送门店编码,可自定义,但必须唯一;若不填写,则系统自动生成',
  `product_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '自主添加商品状态：1：开启0:关闭',
  `product_verify_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品免审核：1：开启0:关闭',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `phone`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 73 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '门店' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_store_staff
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_store_staff`;
CREATE TABLE `zk_system_store_staff`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '微信用户id',
  `account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账号',
  `pwd` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '店员头像',
  `staff_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '店员名称',
  `phone` char(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `roles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理权限',
  `last_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后一次登录ip',
  `last_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后一次登录时间',
  `login_count` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '级别',
  `verify_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '核销开关',
  `order_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单管理',
  `is_admin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是管理员',
  `is_store` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是店员',
  `is_manager` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是店长',
  `is_cashier` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否收银员1=是，0=否',
  `is_customer` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是客服',
  `customer_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服手机',
  `customer_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服二维码地址',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态',
  `notify` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单通知1开启0关闭',
  `is_way` tinyint(1) NOT NULL DEFAULT 0 COMMENT '图片上传方式 0=本地上传 1=网络图片 2=扫码上传',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid_status_isdel_verifystatus`(`uid`, `status`, `is_del`, `verify_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '门店店员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_supplier
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_supplier`;
CREATE TABLE `zk_system_supplier`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `supplier_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '供应商名称',
  `avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '联系人姓名',
  `phone` char(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮箱',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '省市区',
  `province` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `city` int(11) NOT NULL DEFAULT 0 COMMENT '市ID',
  `area` int(11) NOT NULL DEFAULT 0 COMMENT '区ID',
  `street` int(11) NOT NULL DEFAULT 0 COMMENT '街道ID',
  `detailed_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `bank_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '银行卡',
  `bank_address` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开户地址',
  `alipay_account` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `alipay_qrcode_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付宝二维码地址',
  `wechat` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `wechat_qrcode_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码地址',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 0关闭 1开启',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `business` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支持配送的物品品类',
  `city_shop_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '同城配送门店编码,可自定义,但必须唯一;若不填写,则系统自动生成',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '供应商表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_timer
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_timer`;
CREATE TABLE `zk_system_timer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '定时器名称',
  `mark` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标识',
  `type` tinyint(1) NOT NULL COMMENT '周期状态 1=N分钟 2=N小时 3=每小时 4=每天 5=N天 6=每星期 7=每月 8=每年',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务说明',
  `is_open` tinyint(1) NULL DEFAULT 0 COMMENT '是否开启',
  `cycle` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '执行周期',
  `last_execution_time` int(11) NULL DEFAULT 0 COMMENT '上次执行时间',
  `update_execution_time` int(11) NULL DEFAULT 0 COMMENT '修改时间',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时器' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_user_apply
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_user_apply`;
CREATE TABLE `zk_system_user_apply`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '申请类型1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联商户',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `system_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户名称',
  `name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
  `images` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '多图',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '处理状态0未处理，1通过 ，2未通过',
  `fail_msg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '未通过原因',
  `is_del` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除状态 1删除 ，0未删除',
  `status_time` int(11) NOT NULL DEFAULT 0 COMMENT '审核时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_system_user_level
-- ----------------------------
DROP TABLE IF EXISTS `zk_system_user_level`;
CREATE TABLE `zk_system_user_level`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员名称',
  `money` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '购买金额',
  `valid_date` int(11) NOT NULL DEFAULT 0 COMMENT '有效时间',
  `is_forever` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为永久会员',
  `is_pay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否购买,1=购买,0=不购买',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示 1=显示,0=隐藏',
  `grade` int(11) NOT NULL DEFAULT 0 COMMENT '会员等级',
  `discount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '享受折扣',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员卡背景',
  `color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 字体颜色',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员图标',
  `explain` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '说明',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除.1=删除,0=未删除',
  `exp_num` int(11) NOT NULL DEFAULT 0 COMMENT '升级经验',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设置用户等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_table_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `zk_table_qrcode`;
CREATE TABLE `zk_table_qrcode`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店ID',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `seat_num` int(11) NOT NULL DEFAULT 0 COMMENT '座位数',
  `qrcode` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '二维码',
  `table_number` int(11) NOT NULL DEFAULT 0 COMMENT '桌号',
  `remarks` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `is_using` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `is_use` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否使用',
  `eat_number` int(11) NOT NULL DEFAULT 0 COMMENT '就餐人数',
  `order_time` int(11) NOT NULL DEFAULT 0 COMMENT '下单时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '桌码二维码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_table_seats
-- ----------------------------
DROP TABLE IF EXISTS `zk_table_seats`;
CREATE TABLE `zk_table_seats`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL COMMENT '门店id',
  `number` int(11) NOT NULL COMMENT '座位数',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '座位数' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_template_message
-- ----------------------------
DROP TABLE IF EXISTS `zk_template_message`;
CREATE TABLE `zk_template_message`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板id',
  `notification_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '模版通知场景ID,多个',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=订阅消息,1=微信模板消息',
  `tempkey` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板编号',
  `name` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名',
  `kid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复内容',
  `example` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版示例',
  `tempid` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板ID',
  `add_time` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加时间',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_third_jd
-- ----------------------------
DROP TABLE IF EXISTS `zk_third_jd`;
CREATE TABLE `zk_third_jd`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `submit_order_time` datetime NOT NULL,
  `total_money` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `freight` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `is_pay` tinyint(4) NOT NULL DEFAULT -1,
  `is_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '(0:未支付、1:已支付、2:部分退款、3:全部退款)',
  `pay_time` datetime NULL DEFAULT NULL,
  `pay_type` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pay_price` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `pay_points` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `wx_ordernum` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `wx_transaction_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `refund_price` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `refund_points` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `create_time` datetime NOT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 192 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_third_jd_refund
-- ----------------------------
DROP TABLE IF EXISTS `zk_third_jd_refund`;
CREATE TABLE `zk_third_jd_refund`  (
  `refund_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_order_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `child_order_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `refund_amount` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `refunded_price` decimal(8, 2) NOT NULL,
  `refunded_points` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`refund_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_transfer_order
-- ----------------------------
DROP TABLE IF EXISTS `zk_transfer_order`;
CREATE TABLE `zk_transfer_order`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '调拨单号',
  `from_store_id` int(11) NOT NULL DEFAULT 0 COMMENT '调出门店ID',
  `to_store_id` int(11) NOT NULL DEFAULT 0 COMMENT '调入门店ID',
  `applicant_id` int(11) NOT NULL DEFAULT 0 COMMENT '申请人ID',
  `approver_id` int(11) NOT NULL DEFAULT 0 COMMENT '审核人ID',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0待审核,1已审核,2已执行,3已拒绝',
  `total_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '调拨总金额',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `apply_time` int(11) NOT NULL DEFAULT 0 COMMENT '申请时间',
  `approve_time` int(11) NOT NULL DEFAULT 0 COMMENT '审核时间',
  `execute_time` int(11) NOT NULL DEFAULT 0 COMMENT '执行时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `idx_from_store`(`from_store_id`) USING BTREE,
  INDEX `idx_to_store`(`to_store_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调拨单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zk_transfer_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `zk_transfer_order_detail`;
CREATE TABLE `zk_transfer_order_detail`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `transfer_id` int(11) NOT NULL DEFAULT 0 COMMENT '调拨单ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `attr_value_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格ID(关联zk_store_product_attr_value.id)',
  `transfer_qty` int(11) NOT NULL DEFAULT 0 COMMENT '调拨数量',
  `cost_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '调拨金额',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_transfer_id`(`transfer_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_attr_value_id`(`attr_value_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调拨单明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zk_user
-- ----------------------------
DROP TABLE IF EXISTS `zk_user`;
CREATE TABLE `zk_user`  (
  `uid` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户账号',
  `pwd` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户密码',
  `real_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '真实姓名',
  `birthday` int(11) NOT NULL DEFAULT 0 COMMENT '生日',
  `card_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证号码',
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户备注',
  `partner_id` int(11) NOT NULL DEFAULT 0 COMMENT '合伙人id',
  `group_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户分组id',
  `nickname` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `phone` char(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `add_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加ip',
  `last_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最后一次登录时间',
  `last_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后一次登录ip',
  `now_money` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '用户余额',
  `brokerage_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金金额',
  `integral` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '用户剩余积分',
  `exp` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '会员经验',
  `sign_num` int(11) NOT NULL DEFAULT 0 COMMENT '连续签到天数',
  `sign_remind` tinyint(1) NOT NULL DEFAULT 0 COMMENT '签到提醒状态',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1为正常，0为禁止',
  `level` int(11) NOT NULL DEFAULT 0 COMMENT '等级',
  `agent_level` int(11) NOT NULL DEFAULT 0 COMMENT '分销等级',
  `spread_open` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有推广资格',
  `spread_uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '推广元id',
  `spread_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '推广员关联时间',
  `spread_lottery` int(11) NOT NULL DEFAULT 1 COMMENT '推广获取抽奖次数',
  `work_uid` int(11) NOT NULL DEFAULT 0 COMMENT '绑定企业微信成员uid',
  `work_userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '绑定企业微信成员uid',
  `user_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户类型',
  `is_promoter` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为推广员',
  `pay_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户购买次数',
  `spread_count` int(11) NOT NULL DEFAULT 0 COMMENT '下级人数',
  `clean_time` int(11) NOT NULL DEFAULT 0 COMMENT '清理会员时间',
  `addres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `adminid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员编号 ',
  `login_type` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户登陆类型，h5,wechat,routine',
  `login_city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录城市',
  `record_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '记录临时电话',
  `is_money_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '会员来源  0: 购买商品升级   1：花钱购买的会员2: 会员卡领取',
  `superior_uid` int(10) NOT NULL DEFAULT 0 COMMENT '上级会员ID',
  `superior_bind_time` int(11) NOT NULL COMMENT '上级会员绑定时间',
  `is_ever_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否永久性会员  0: 非永久会员  1：永久会员',
  `overdue_time` int(11) NOT NULL DEFAULT 0 COMMENT '会员到期时间',
  `uniqid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `bar_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '条形码值',
  `rand_code` int(11) NOT NULL DEFAULT 0 COMMENT '随机code，用于确认余额支付',
  `sex` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:其他,1:男,2:女',
  `provincials` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省市区',
  `province` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `city` int(11) NOT NULL DEFAULT 0 COMMENT '市ID',
  `area` int(11) NOT NULL DEFAULT 0 COMMENT '	区ID',
  `street` int(11) NOT NULL DEFAULT 0 COMMENT '街道ID',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `delete_time` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `extend_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户补充信息',
  `level_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '用户等级是否激活',
  `level_extend_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '激活会员卡补充信息',
  `is_first_order` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否购买首单优惠：0：未购买1已购买',
  `is_newcomer` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否购买新人专享：0：未购买1已购买',
  PRIMARY KEY (`uid`) USING BTREE,
  INDEX `account`(`account`) USING BTREE,
  INDEX `is_promoter`(`is_promoter`) USING BTREE,
  INDEX `level`(`level`) USING BTREE,
  INDEX `phone`(`phone`) USING BTREE,
  INDEX `spreaduid`(`spread_uid`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `work_uid`(`work_uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11741 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_address
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_address`;
CREATE TABLE `zk_user_address`  (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户地址id',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `real_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人电话',
  `province` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人所在省',
  `city` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人所在市',
  `street` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所在镇/街道',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市id',
  `district` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人所在区',
  `detail` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人详细地址',
  `post_code` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '邮编',
  `longitude` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '经度',
  `latitude` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '纬度',
  `is_default` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否默认',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_default`(`is_default`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1607 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_bill
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_bill`;
CREATE TABLE `zk_user_bill`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户账单id',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户uid',
  `link_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '关联id',
  `pm` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账单标题',
  `category` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '明细种类',
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '明细类型',
  `number` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '明细数字',
  `balance` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '剩余',
  `mark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0 = 带确定 1 = 有效 -1 = 无效',
  `take` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = 未收货 1 = 已收货',
  `frozen_time` int(11) NOT NULL DEFAULT 0 COMMENT '积分冻结时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `openid`(`uid`) USING BTREE,
  INDEX `pm`(`pm`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `type`(`category`, `type`, `link_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 776 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户账单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_brokerage
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_brokerage`;
CREATE TABLE `zk_user_brokerage`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户佣金id',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户uid',
  `link_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '关联id',
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '明细类型',
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '账单标题',
  `number` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '明细数字',
  `balance` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '剩余',
  `pm` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0 = 带确定 1 = 有效 -1 = 无效',
  `take` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = 未收货 1 = 已收货',
  `frozen_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '冻结到期时间',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `pm`(`pm`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `type`(`type`, `link_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 820 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户账单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_brokerage_frozen
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_brokerage_frozen`;
CREATE TABLE `zk_user_brokerage_frozen`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `uill_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联id',
  `frozen_time` int(11) NOT NULL DEFAULT 0 COMMENT '冻结到期时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有效',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户佣金冻结记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_card
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_card`;
CREATE TABLE `zk_user_card`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT 'UID',
  `spread_uid` int(11) NOT NULL DEFAULT 0 COMMENT '推广用户uid',
  `wechat_card_id` int(11) NOT NULL DEFAULT 0 COMMENT 'wechat_card表主键',
  `card_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'card_id',
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员卡号',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店ID',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员ID',
  `openid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信openid',
  `is_submit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否激活',
  `submit_time` int(11) NOT NULL DEFAULT 0 COMMENT '激活时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `del_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户领取卡券' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_collage_code
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_collage_code`;
CREATE TABLE `zk_user_collage_code`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT 'UID',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类别 9=拼单,10=桌码',
  `oid` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `checkout_method` tinyint(1) NOT NULL DEFAULT 1 COMMENT '结账方式',
  `qrcode_id` int(11) NOT NULL DEFAULT 0 COMMENT '二维码ID',
  `number_diners` int(11) NOT NULL DEFAULT 0 COMMENT '用餐人数',
  `serial_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流水号',
  `address_id` int(11) NOT NULL DEFAULT 0 COMMENT '地址id',
  `shipping_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '配送方式 1=快递,2=到店自提,3=门店配送',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 0=下单中 1=结算中 2=提交成功 3=已完成 -1=取消拼单/桌码',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户拼单/桌码表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_collage_code_partake
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_collage_code_partake`;
CREATE TABLE `zk_user_collage_code_partake`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT 'UID',
  `collate_code_id` int(11) NOT NULL DEFAULT 0 COMMENT '拼单ID/桌码ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品类型',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `product_attr_unique` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品属性',
  `cart_num` smallint(6) NOT NULL DEFAULT 0 COMMENT '商品数量',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `is_print` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否打印',
  `is_settle` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否结算',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参与拼单/桌码表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_enter
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_enter`;
CREATE TABLE `zk_user_enter`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商户申请ID',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户所在省',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户所在市',
  `district` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户所在区',
  `address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户详细地址',
  `merchant_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户名称',
  `link_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `link_tel` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户电话',
  `charter` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户证书',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `apply_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核时间',
  `success_time` int(11) NOT NULL DEFAULT 0 COMMENT '通过时间',
  `fail_message` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '未通过原因',
  `fail_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '未通过时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '-1 审核未通过 0未审核 1审核通过',
  `is_lock` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = 开启 1= 关闭',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `is_lock`(`is_lock`) USING BTREE,
  INDEX `province`(`province`, `city`, `district`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_extract
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_extract`;
CREATE TABLE `zk_user_extract`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `extract_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'bank' COMMENT 'bank = 银行卡 alipay = 支付宝wx=微信',
  `bank_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '银行卡',
  `bank_address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户地址',
  `alipay_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `extract_price` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `extract_fee` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '手续费金额',
  `mark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `balance` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00,
  `fail_msg` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '无效原因',
  `fail_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '-1 未通过 0 审核中 1 已提现',
  `wechat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码地址',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `extract_type`(`extract_type`) USING BTREE,
  INDEX `fail_time`(`fail_time`) USING BTREE,
  INDEX `openid`(`uid`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_friends
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_friends`;
CREATE TABLE `zk_user_friends`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `friends_uid` int(11) NOT NULL DEFAULT 0 COMMENT '好友uid',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2568 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户好友关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_group
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_group`;
CREATE TABLE `zk_user_group`  (
  `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT,
  `group_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户分组名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_invoice
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_invoice`;
CREATE TABLE `zk_user_invoice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0,
  `header_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '抬头类型1:个人2：企业',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '发票类型1：普通2：专用',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称（发票抬头）',
  `duty_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '税号',
  `drawer_phone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票人手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票人邮箱',
  `tell` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册电话',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册地址',
  `bank` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册开户银行',
  `card_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行卡号',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认',
  `is_del` tinyint(1) NOT NULL DEFAULT 0,
  `add_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户发票管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_label
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_label`;
CREATE TABLE `zk_user_label`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `label_cate` int(11) NOT NULL DEFAULT 0 COMMENT '标签分类',
  `label_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `tag_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业微信同步标签id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `label_cate`(`label_cate`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_label_relation
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_label_relation`;
CREATE TABLE `zk_user_label_relation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0平台1:门店2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店、供应商id',
  `label_id` int(11) NOT NULL DEFAULT 0 COMMENT '标签ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户标签关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_level
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_level`;
CREATE TABLE `zk_user_level`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `level_id` int(11) NOT NULL DEFAULT 0 COMMENT '等级vip',
  `grade` int(11) NOT NULL DEFAULT 0 COMMENT '会员等级',
  `valid_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `is_forever` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否永久',
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:禁止,1:正常',
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `remind` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已通知',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0=未删除,1=删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `discount` int(11) NOT NULL DEFAULT 0 COMMENT '享受折扣',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户等级记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_money
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_money`;
CREATE TABLE `zk_user_money`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户余额id',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户uid',
  `link_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '关联id',
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '明细类型',
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '账单标题',
  `number` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '明细数字',
  `balance` decimal(12, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '剩余',
  `pm` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0 = 带确定 1 = 有效 -1 = 无效',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `pm`(`pm`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `type`(`type`, `link_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户账单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_notice
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_notice`;
CREATE TABLE `zk_user_notice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接收消息的用户id（类型：json数据）',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '消息通知类型（1：系统消息；2：用户通知）',
  `user` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发送人',
  `title` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知消息的标题信息',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知消息的内容',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '通知消息发送的时间',
  `is_send` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发送（0：未发送；1：已发送）',
  `send_time` int(11) NOT NULL DEFAULT 0 COMMENT '发送时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_notice_see
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_notice_see`;
CREATE TABLE `zk_user_notice_see`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nid` int(11) NOT NULL DEFAULT 0 COMMENT '查看的通知id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '查看通知的用户id',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '查看通知的时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户通知发送记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_recharge
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_recharge`;
CREATE TABLE `zk_user_recharge`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '充值用户UID',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员ID',
  `order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `trade_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信订单号',
  `price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '充值金额',
  `give_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '购买赠送金额',
  `recharge_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '充值类型',
  `auth_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款码条码值',
  `paid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否充值',
  `pay_time` int(11) NOT NULL DEFAULT 0 COMMENT '充值支付时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '充值时间',
  `refund_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `channel_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户访问端标识',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `paid`(`paid`) USING BTREE,
  INDEX `recharge_type`(`recharge_type`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户充值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_relation
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_relation`;
CREATE TABLE `zk_user_relation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型(收藏(collect）、点赞(like))',
  `category` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '某种类型的商品(普通商品、秒杀商品、文章)',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category`(`category`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 210602 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品点赞和收藏表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_search
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_search`;
CREATE TABLE `zk_user_search`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `keyword` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '搜索关键词',
  `vicword` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关键词分词',
  `num` int(11) NOT NULL DEFAULT 1 COMMENT '搜索次数',
  `result` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '搜索结果',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1011 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户搜索记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_sign
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_sign`;
CREATE TABLE `zk_user_sign`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '签到说明',
  `number` int(11) NOT NULL DEFAULT 0 COMMENT '获得积分',
  `balance` int(11) NOT NULL DEFAULT 0 COMMENT '剩余积分',
  `exp_num` int(11) NOT NULL DEFAULT 0 COMMENT '获得经验',
  `exp_balance` int(11) NOT NULL DEFAULT 0 COMMENT '剩余经验',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '签到记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_spread
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_spread`;
CREATE TABLE `zk_user_spread`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `staff_id` int(11) NOT NULL DEFAULT 0 COMMENT '店员ID',
  `spread_uid` int(11) NOT NULL DEFAULT 0 COMMENT '推广人uid',
  `spread_time` int(11) NOT NULL DEFAULT 0 COMMENT '推广时间',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `spread_uid`(`spread_uid`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2838 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户推广关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_user_visit
-- ----------------------------
DROP TABLE IF EXISTS `zk_user_visit`;
CREATE TABLE `zk_user_visit`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '访问路径',
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户ip',
  `stay_time` int(11) NOT NULL DEFAULT 0 COMMENT '页面停留时间(秒)',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '访问时间',
  `channel_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户访问端标识',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户省份',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `time`(`channel_type`, `add_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41812 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户访问表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_video
-- ----------------------------
DROP TABLE IF EXISTS `zk_video`;
CREATE TABLE `zk_video`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0:平台1:门店2:用户',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联平台管理门店ID、用户UID',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面图',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频地址',
  `product_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联商品ids',
  `like_num` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数量',
  `collect_num` int(11) NOT NULL DEFAULT 0 COMMENT '收藏数量',
  `play_num` int(11) NOT NULL DEFAULT 0 COMMENT '浏览播放数量',
  `comment_num` int(11) NOT NULL DEFAULT 0 COMMENT '评论数量',
  `share_num` int(11) NOT NULL DEFAULT 0 COMMENT '分享数量',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `is_recommend` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `is_verify` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否审核：-2强制下架-1未通过0未审核1:通过',
  `sort` smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短视频表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_video_comment
-- ----------------------------
DROP TABLE IF EXISTS `zk_video_comment`;
CREATE TABLE `zk_video_comment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0:平台1:门店2:用户',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '发布视频门店ID、用户UID',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级回复id',
  `video_id` int(11) NOT NULL DEFAULT 0 COMMENT '视频id',
  `uid` int(11) NOT NULL DEFAULT 0,
  `nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户名称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `like_num` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数量',
  `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评论内容',
  `ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评论ip',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市',
  `is_reply` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否回复',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `video_id`(`video_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短视频评价回复表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_virtual_account
-- ----------------------------
DROP TABLE IF EXISTS `zk_virtual_account`;
CREATE TABLE `zk_virtual_account`  (
  `account_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '账户Id',
  `account_name` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账户名称',
  `account_status` tinyint(1) NULL DEFAULT 1 COMMENT '账户状态（1:待生效、2:生效中、3:已过期、4:已禁用）',
  `account_balance` decimal(8, 2) NULL DEFAULT 0.00 COMMENT '账户余额',
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '虚拟账户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_balance_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_balance_record`;
CREATE TABLE `zk_vop_balance_record`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '(1:积分、2:余额)',
  `link_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '记录id',
  `uid` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `staff_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `day_time` datetime NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `mark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '无',
  `price` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_card
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_card`;
CREATE TABLE `zk_vop_card`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `init_money` decimal(8, 2) NULL DEFAULT 750.00,
  `signed_image` varchar(666) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'http://img.shg.zkshlm.com/attach/2024/01/8b744202401261609182121.png',
  `signed_number` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(1) NULL DEFAULT -1,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique`(`signed_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3001 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_jd_category
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_jd_category`;
CREATE TABLE `zk_vop_jd_category`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cate_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 403 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_no
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_no`;
CREATE TABLE `zk_vop_no`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NULL DEFAULT 1,
  `vop_sku_id` varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 120 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_order
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_order`;
CREATE TABLE `zk_vop_order`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `zk_order_id` varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `vop_order_id` varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '京东订单号',
  `order_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单类型1:普通商品2:大家电3:实物礼品卡4:售后换新单',
  `parent_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '父子单类型,父单:1,子单:0',
  `order_total_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '订单价格',
  `order_unit_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '订单单价',
  `order_freight_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总计运费',
  `order_state` tinyint(1) NOT NULL DEFAULT 2 COMMENT '京东订单状态',
  `confirm_state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '订单确认状态,1:已确认,0:未确认',
  `cancel_order_state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '订单取消状态1：已取消 0：未取消',
  `delivery_state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '物流配送状态,0:下单默认状态,1:已妥投,2:拒收',
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_order_delivery
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_order_delivery`;
CREATE TABLE `zk_vop_order_delivery`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cart_id` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '购车车id',
  `cart_num` int(11) NOT NULL DEFAULT 0 COMMENT '购物数量',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主订单id',
  `delivery_num` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递单号/手机号',
  `link_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方商城订单id',
  `vop_sku_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `spec_id` varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性id',
  `delivery_status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '1:待发货、2：已发货、3：确认收获、-1:已取消',
  `pay_status` tinyint(4) NOT NULL DEFAULT -1 COMMENT '支付状态（-1：未支付、1：已支付、-2：已取消）',
  `type` tinyint(4) NULL DEFAULT 0 COMMENT '(0:京东货源、1:新精选货源商品、2:老精选货源商品、3:代发货源)',
  `customer_payment` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '客服支付金额',
  `customer_shipping` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '客户支付邮费',
  `merchant_cost` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '商家成本',
  `merchant_shipping` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '商家支付邮费',
  `merchant_profit` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '商家利润',
  `news` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最新消息',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 437 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_order_refund`;
CREATE TABLE `zk_vop_order_refund`  (
  `refund_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `link_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商城订单id',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主订单id',
  `type` tinyint(4) NULL DEFAULT 0 COMMENT '(0:京东货源、1:新精选货源商品、2:老精选货源商品、3:代发货源)',
  `refund_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款申请类型1:仅退款2：退款退款3：拒绝4：同意退货5：已退货6：已退款',
  `refund_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refunded_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '已退款金额',
  `refund_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款电话',
  `refund_express` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货快递单号',
  `refund_express_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货快递名称',
  `refund_explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款用户说明',
  `refund_img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退款图片',
  `refund_goods_explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货用户说明',
  `refund_goods_img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退货凭证',
  `refuse_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `refunded_time` int(11) NOT NULL DEFAULT 0 COMMENT '处理时间',
  `refund_status` tinyint(4) NULL DEFAULT 0 COMMENT '(0:未退款、1:已申请、2:退款成功、-1:退款失败)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`refund_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_product
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_product`;
CREATE TABLE `zk_vop_product`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `vop_sku_id` bigint(20) NOT NULL,
  `create_time` datetime NOT NULL,
  `update_time` datetime NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `jd_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '京东进价',
  `jd_reference_price` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `jd_freight` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00,
  `vop_price` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `mark_up` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '加价额',
  `diff_num` bigint(20) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `vop_sku_id`(`vop_sku_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56321 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_product_attrs
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_product_attrs`;
CREATE TABLE `zk_vop_product_attrs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `vop_sku_id` bigint(20) NOT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `retail_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '铺货价',
  `pay_price` decimal(8, 2) NULL DEFAULT 0.00 COMMENT '销售价',
  `title` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `attrs_name` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性名',
  `spec_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 804 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_sync
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_sync`;
CREATE TABLE `zk_vop_sync`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NULL DEFAULT 1 COMMENT '类型（1：京东，2:1688）',
  `is_attrs` tinyint(4) NULL DEFAULT 1 COMMENT '是否多规格（1:否，2:是）',
  `is_offer_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '(0:京东货源、1:新精选货源商品、2:老精选货源商品、3:代发货源)',
  `vop_sku_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `zk_product_id` bigint(20) NOT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10086 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_sync_log
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_sync_log`;
CREATE TABLE `zk_vop_sync_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `create_time` datetime NULL DEFAULT NULL,
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sync_id` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48599 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_vop_task
-- ----------------------------
DROP TABLE IF EXISTS `zk_vop_task`;
CREATE TABLE `zk_vop_task`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:vop、2:精选、3:一件代发',
  `min_price` decimal(6, 2) NOT NULL DEFAULT 0.00,
  `cate_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `page` int(10) UNSIGNED NULL DEFAULT 1,
  `is_status` tinyint(1) NOT NULL DEFAULT -1 COMMENT '-1:待完成、1:已完成',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 87 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_card
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_card`;
CREATE TABLE `zk_wechat_card`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `card_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '卡券ID（微信返回）',
  `card_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'member_card' COMMENT '卡券类型：默认会员卡',
  `code_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '码类型',
  `brand_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户名称',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡券名称',
  `color` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '颜色',
  `notice` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡券使用提醒',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `center_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '卡券中部居中的按钮，仅在卡券激活后且可用状态 时显示',
  `center_sub_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '显示在入口下方的提示语 ， 仅在卡券激活后且可用状态时显示',
  `center_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '顶部居中的url ，仅在卡券激活后且可用状态时显示',
  `service_phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '联系方式',
  `logo_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'logo URL',
  `background_pic_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `prerogative` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '特权说明',
  `especial` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '特别追加参数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信卡券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_key
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_key`;
CREATE TABLE `zk_wechat_key`  (
  `id` mediumint(9) NOT NULL AUTO_INCREMENT,
  `reply_id` mediumint(9) NOT NULL DEFAULT 0 COMMENT '回复内容id',
  `keys` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键词',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信回复关键词辅助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_media
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_media`;
CREATE TABLE `zk_wechat_media`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '微信视频音频id',
  `type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复类型',
  `path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件路径',
  `media_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信服务器返回的id',
  `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `temporary` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否永久或者临时 0永久1临时',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `type`(`type`, `media_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信回复表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_message
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_message`;
CREATE TABLE `zk_wechat_message`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户行为记录id',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户openid',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作类型',
  `result` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作详细记录',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `openid`(`openid`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户行为记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_news_category
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_news_category`;
CREATE TABLE `zk_wechat_news_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '图文消息管理ID',
  `cate_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图文名称',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `new_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章id',
  `add_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '图文消息管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_qrcode`;
CREATE TABLE `zk_wechat_qrcode`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码名称',
  `image` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码图片',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
  `label_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标签id',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '回复类型',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '回复内容',
  `data` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发送数据',
  `follow` int(11) NOT NULL DEFAULT 0 COMMENT '关注人数',
  `scan` int(11) NOT NULL DEFAULT 0 COMMENT '扫码人数',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `continue_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '到期时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '二维码表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_qrcode_cate
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_qrcode_cate`;
CREATE TABLE `zk_wechat_qrcode_cate`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `cate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '渠道码分类名称',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '二维码类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_qrcode_record
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_qrcode_record`;
CREATE TABLE `zk_wechat_qrcode_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `qid` int(11) NOT NULL DEFAULT 0 COMMENT '渠道码id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `is_follow` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关注',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT '扫码时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '渠道码扫码记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_reply
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_reply`;
CREATE TABLE `zk_wechat_reply`  (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '微信关键字回复id',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复类型',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回复数据',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '0=不可用  1 =可用',
  `hide` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否隐藏',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `hide`(`hide`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信关键字回复表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_wechat_user
-- ----------------------------
DROP TABLE IF EXISTS `zk_wechat_user`;
CREATE TABLE `zk_wechat_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '微信用户id',
  `unionid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段',
  `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户的标识，对当前公众号唯一',
  `nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户的昵称',
  `headimgurl` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `sex` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户的性别，值为1时是男性，值为2时是女性，值为0时是未知',
  `city` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户所在城市',
  `language` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户的语言，简体中文为zh_CN',
  `province` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户所在省份',
  `country` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户所在国家',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注',
  `groupid` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '用户所在的分组ID（兼容旧的用户分组接口）',
  `tagid_list` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户被打上的标签ID列表',
  `subscribe` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '用户是否订阅该公众号标识',
  `subscribe_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注公众号时间',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `second` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '二级推荐人',
  `user_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'wechat' COMMENT '用户类型',
  `is_complete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '授权信息是否完整',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `openid`(`openid`) USING BTREE,
  INDEX `add_time`(`add_time`) USING BTREE,
  INDEX `groupid`(`groupid`) USING BTREE,
  INDEX `subscribe`(`subscribe`) USING BTREE,
  INDEX `subscribe_time`(`subscribe_time`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `unionid`(`unionid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11562 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_channel_code
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_channel_code`;
CREATE TABLE `zk_work_channel_code`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0=全天在线；1=自动上下线',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码名称',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类',
  `label_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户标签',
  `reserve_userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备用成员',
  `userids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '全天在线成员',
  `skip_verify` tinyint(1) NOT NULL DEFAULT 0 COMMENT '自动加好友：0=关闭，1=开启',
  `add_upper_limit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '员工添加上限0=关闭,1=开启',
  `welcome_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=使用渠道欢迎，1=默认欢迎语',
  `welcome_words` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '欢迎语',
  `qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系我二维码链接',
  `config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '新增联系方式的配置id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：1=开启，0=关闭',
  `client_num` int(11) NOT NULL DEFAULT 0 COMMENT '扫码添加客户人数',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `cate_id`(`cate_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业渠道码（联系我）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_channel_cycle
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_channel_cycle`;
CREATE TABLE `zk_work_channel_cycle`  (
  `channel_id` int(11) NOT NULL DEFAULT 0 COMMENT '渠道码',
  `userids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '成员userid',
  `start_time` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上班开始',
  `end_time` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上班结束',
  `wokr_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '工作周期，json转换',
  INDEX `channel_id`(`channel_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '渠道码周期规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_channel_limit
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_channel_limit`;
CREATE TABLE `zk_work_channel_limit`  (
  `channel_id` int(11) NOT NULL DEFAULT 0 COMMENT '渠道码id',
  `userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '成员userid',
  `max` int(11) NOT NULL DEFAULT 0 COMMENT '限制个数',
  INDEX `channel_id`(`channel_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '渠道码添加成员限制器' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_client
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_client`;
CREATE TABLE `zk_work_client`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `corp_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业微信id',
  `external_userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部联系人的userid',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '商城用户uid',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部联系人的名称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部联系人头像',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户',
  `gender` tinyint(1) NOT NULL DEFAULT 0 COMMENT '性别 0-未知 1-男性 2-女性',
  `unionid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开放平台的唯一身份标识',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部联系人的职位',
  `corp_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部联系人所在企业的简称',
  `corp_full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部联系人所在企业的主体名称',
  `external_profile` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注信息',
  `create_time` int(11) NOT NULL DEFAULT 0,
  `update_time` int(11) NOT NULL DEFAULT 0,
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `corp_id`(`corp_id`, `external_userid`) USING BTREE,
  INDEX `external_userid`(`external_userid`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `unionid`(`unionid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 113 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信客户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_client_follow
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_client_follow`;
CREATE TABLE `zk_work_client_follow`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL DEFAULT 0 COMMENT '客户id',
  `userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加了此外部联系人的企业成员userid',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '该成员对此外部联系人的备注',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '该成员对此外部联系人的描述',
  `createtime` int(11) NOT NULL DEFAULT 0 COMMENT '该成员添加此外部联系人的时间',
  `remark_corp_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '该成员对此微信客户备注的企业名称',
  `remark_mobiles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '该成员对此客户备注的手机号码',
  `add_way` int(11) NOT NULL DEFAULT 0 COMMENT '该成员添加此客户的来源',
  `oper_userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发起添加的userid，如果成员主动添加，为成员的userid；如果是客户主动添加，则为客户的外部联系人userid；如果是内部成员共享/管理员分配，则为对应的成员/管理员userid',
  `state` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义字段返回数据',
  `is_del_user` tinyint(1) NOT NULL DEFAULT 0 COMMENT '客户是否删除跟踪人:0=没有,1=删除',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `client_id`(`client_id`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信客服跟踪' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_client_follow_tags
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_client_follow_tags`;
CREATE TABLE `zk_work_client_follow_tags`  (
  `follow_id` int(11) NOT NULL DEFAULT 0 COMMENT '跟踪人id',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '该成员添加此外部联系人所打标签的分组名称',
  `tag_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '该成员添加此外部联系人所打标签名称',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1-企业设置，2-用户自定义，3-规则组标签',
  `tag_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '该成员添加此外部联系人所打企业标签的id，用户自定义类型标签（type=2）不返回',
  `create_time` int(11) NOT NULL DEFAULT 0,
  INDEX `follow_id`(`follow_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信客户跟踪标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_department
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_department`;
CREATE TABLE `zk_work_department`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `corp_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业id',
  `department_id` int(11) NOT NULL DEFAULT 0 COMMENT '部门id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '部门名称',
  `name_en` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '部门英文名称',
  `department_leader` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'leader',
  `parentid` int(11) NOT NULL DEFAULT 0 COMMENT '上级id',
  `srot` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信部门' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_chat
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_chat`;
CREATE TABLE `zk_work_group_chat`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `corp_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业ID',
  `chat_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户群ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群名',
  `owner` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群主ID',
  `group_create_time` int(11) NOT NULL DEFAULT 0 COMMENT '群的创建时间',
  `notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群公告',
  `admin_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群管理员userid',
  `member_num` int(11) NOT NULL DEFAULT 0 COMMENT '群人数',
  `retreat_group_num` int(11) NOT NULL DEFAULT 0 COMMENT '退群总数',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '客户群跟进状态。\r\n0 - 跟进人正常\r\n1 - 跟进人离职\r\n2 - 离职继承中\r\n3 - 离职继承完成',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `corp_id`(`corp_id`, `chat_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 59 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信群' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_chat_auth
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_chat_auth`;
CREATE TABLE `zk_work_group_chat_auth`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码名称',
  `auth_group_chat` tinyint(1) NOT NULL DEFAULT 0 COMMENT '自动建群0=关闭，1=开启',
  `chat_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群聊chat_id',
  `group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群名称',
  `group_num` int(11) NOT NULL DEFAULT 0 COMMENT '群序列号',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户标签',
  `config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置id',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系二维码的UR',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新',
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信自动拉群' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_chat_member
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_chat_member`;
CREATE TABLE `zk_work_group_chat_member`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL DEFAULT 0 COMMENT '企业群ID',
  `userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群成员id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '成员类型。\r\n1 - 企业成员\r\n2 - 外部联系人',
  `unionid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信开放平台的唯一身份标识（微信unionid）',
  `join_time` int(11) NOT NULL DEFAULT 0 COMMENT '入群时间',
  `join_scene` tinyint(1) NOT NULL DEFAULT 0 COMMENT '入群方式。\r\n1 - 由群成员邀请入群（直接邀请入群）\r\n2 - 由群成员邀请入群（通过邀请链接入群）\r\n3 - 通过扫描群二维码入群',
  `invitor_userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邀请者。目前仅当是由本企业内部成员邀请入群时会返回该值',
  `group_nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '在群里的昵称',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名字。仅当 need_name = 1 时返回',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=在群中,0=已退群',
  `chat_sum` int(11) NOT NULL DEFAULT 0 COMMENT '当前群人数',
  `retreat_chat_num` int(11) NOT NULL DEFAULT 0 COMMENT '当前退群人数',
  `state` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `group_id`(`group_id`) USING BTREE,
  INDEX `group_id_2`(`group_id`, `userid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3302 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信群成员列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_chat_statistic
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_chat_statistic`;
CREATE TABLE `zk_work_group_chat_statistic`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL DEFAULT 0 COMMENT '群id',
  `today_sum` int(11) NOT NULL DEFAULT 0 COMMENT '今日新增人员',
  `today_return_sum` int(11) NOT NULL DEFAULT 0 COMMENT '今日退群人员',
  `chat_sum` int(11) NOT NULL DEFAULT 0 COMMENT '当前群人员',
  `chat_return_sum` int(11) NOT NULL DEFAULT 0 COMMENT '当前退群人员',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 820 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信群统计' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_msg_relation
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_msg_relation`;
CREATE TABLE `zk_work_group_msg_relation`  (
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '群发模板id',
  `msg_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '群发消息id'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业群发消息id关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_msg_send_result
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_msg_send_result`;
CREATE TABLE `zk_work_group_msg_send_result`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msg_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '消息id',
  `external_userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部联系人userid，群发消息到企业的客户群不返回该字段',
  `chat_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部客户群id，群发消息到客户不返回该字段',
  `userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业服务人员的userid',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败',
  `send_time` int(11) NOT NULL DEFAULT 0 COMMENT '发送时间，发送状态为1时返回',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `msg_id`(`msg_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业群发记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_msg_task
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_msg_task`;
CREATE TABLE `zk_work_group_msg_task`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msg_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '消息id',
  `userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '成员id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0-未发送 2-已发送',
  `send_time` int(11) NOT NULL DEFAULT 0 COMMENT '发送时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `msg_id`(`msg_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '群发成员发送任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_group_template
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_group_template`;
CREATE TABLE `zk_work_group_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=客户群发，1=客户群群发',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `userids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '成员userid',
  `client_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=全部客户，1=筛选客户',
  `where_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '筛选时间',
  `where_label` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '筛选标签',
  `where_not_label` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '筛选排除标签',
  `template_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=立即发送，1=定时发送',
  `send_time` int(11) NOT NULL DEFAULT 0 COMMENT '定时发送时间',
  `send_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发送1=已发送，2=正在发送中，0=没有发送',
  `welcome_words` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '群发内容',
  `fail_external_userid` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发送失败的客户userid',
  `fail_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发送失败原因',
  `create_time` int(11) NOT NULL DEFAULT 0,
  `update_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `template_type`(`template_type`, `send_time`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信群发' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_label
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_label`;
CREATE TABLE `zk_work_label`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `corp_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业微信id',
  `group_id` int(11) NOT NULL DEFAULT 0 COMMENT '标签组id',
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签组名称',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间 ',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_media
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_media`;
CREATE TABLE `zk_work_media`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `md5_path` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '本地路径md5值',
  `type` enum('image','voice','video','file') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'image' COMMENT '素材类型',
  `upload_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=临时素材附件,1=朋友圈附件,2=商品附件',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '本地文件路径',
  `media_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '媒体文件上传后获取的唯一标识',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '永久素材路径',
  `temporary` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=永久，1=非永久',
  `valid_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`, `md5_path`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信素材' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_member
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_member`;
CREATE TABLE `zk_work_member`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `corp_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业微信id',
  `userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '成员UserID',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '商城uid',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '成员名称',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职务信息',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `gender` tinyint(1) NOT NULL DEFAULT 0 COMMENT '性别。0表示未定义，1表示男性，2表示女性',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邮箱',
  `biz_mail` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业邮箱',
  `direct_leader` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直属上级UserID',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像url',
  `thumb_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像缩略图url',
  `telephone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '座机',
  `alias` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `enable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '启用/禁用成员。1表示启用成员，0表示禁用成员',
  `is_leader` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是领导',
  `hide_mobile` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否隐藏手机号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `open_userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '全局唯一',
  `main_department` tinyint(1) NOT NULL DEFAULT 0 COMMENT '主部门',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '员工个人二维码',
  `external_position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对外职务',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `userid`(`userid`) USING BTREE,
  INDEX `corp_id`(`corp_id`) USING BTREE,
  INDEX `corp_id_2`(`corp_id`, `userid`) USING BTREE,
  INDEX `mobile`(`mobile`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信成员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_member_other
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_member_other`;
CREATE TABLE `zk_work_member_other`  (
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '企业成员id',
  `extattr` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展属性',
  `external_profile` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '成员对外属性',
  UNIQUE INDEX `member_id`(`member_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信成员其他信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_member_relation
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_member_relation`;
CREATE TABLE `zk_work_member_relation`  (
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '员工ID',
  `department` int(11) NOT NULL DEFAULT 0 COMMENT '所属部门',
  `srot` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_leader_in_dept` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为部门负责人',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  INDEX `member_id`(`member_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业成员关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_moment
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_moment`;
CREATE TABLE `zk_work_moment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=全部成员，1=选择成员',
  `user_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '成员userid',
  `client_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '客户类型0=全部，1=筛选',
  `client_tag_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '客户标签',
  `welcome_words` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `send_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发送类型',
  `send_time` int(11) NOT NULL DEFAULT 0 COMMENT '定时发送时间',
  `jobid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务id',
  `invalid_sender_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '不合法的执行者列表，包括不存在的id以及不在应用可见范围内的部门或者成员',
  `moment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '朋友圈id',
  `invalid_external_contact_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '无效客户',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `jobid`(`jobid`) USING BTREE,
  INDEX `moment_id`(`moment_id`) USING BTREE,
  INDEX `send_time`(`send_time`, `send_type`, `jobid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信发送朋友圈' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_moment_send_result
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_moment_send_result`;
CREATE TABLE `zk_work_moment_send_result`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '朋友圈id',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '成员id',
  `external_userid` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '客户ids',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发送状态',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `moment_id`(`moment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信朋友圈发送情况' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_user
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_user`;
CREATE TABLE `zk_work_user`  (
  `work_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工作ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
  `shg_uid` int(11) NULL DEFAULT NULL COMMENT 'SHG用户ID',
  `is_registered` tinyint(1) NULL DEFAULT 0 COMMENT '是否已注册（0：否，1：是）',
  `is_member` tinyint(1) NULL DEFAULT 0 COMMENT '是否已成为会员（0：否，1：是）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`work_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 194 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工工作信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_welcome
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_welcome`;
CREATE TABLE `zk_work_welcome`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=通用,1=指定员工',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息内容',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '欢迎语消息体',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信欢迎语' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_work_welcome_relation
-- ----------------------------
DROP TABLE IF EXISTS `zk_work_welcome_relation`;
CREATE TABLE `zk_work_welcome_relation`  (
  `welcome_id` int(11) NOT NULL DEFAULT 0 COMMENT '欢迎语id',
  `userid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户userid',
  INDEX `welcome_id`(`welcome_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '欢迎语辅助表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
