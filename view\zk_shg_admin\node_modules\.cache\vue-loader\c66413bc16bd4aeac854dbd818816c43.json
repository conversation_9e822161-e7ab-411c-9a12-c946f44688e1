{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\shortVideo\\components\\videoDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\shortVideo\\components\\videoDetails.vue", "mtime": 1661486458000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport { videoInfo } from \"@/api/marketing\";\nexport default {\n    name: \"videoDetails\",\n    computed: {\n        ...mapState(\"admin/layout\", [\"isMobile\"]),\n        labelWidth() {\n            return this.isMobile ? undefined : 90;\n        },\n        labelPosition() {\n            return this.isMobile ? \"top\" : \"right\";\n        }\n    },\n    data() {\n        return {\n            modals:false,\n            activeName:'detail',\n            formValidate:{},\n            tableData:[],\n            columns: [\n                {\n                    title: 'ID',\n                    key: 'id',\n                    width: 60\n                },\n                {\n                    title: \"商品信息\",\n                    slot: \"info\",\n                    minWidth: 180,\n                },\n                {\n                    title: \"商品分类\",\n                    key: \"cate_name\",\n                    minWidth: 180,\n                },\n                {\n                    title: \"售价\",\n                    key: \"price\",\n                    minWidth: 180,\n                },\n                {\n                    title: \"库存\",\n                    key: \"stock\",\n                    minWidth: 180,\n                }\n            ],\n        };\n    },\n    created() {},\n    methods: {\n        verify(){\n            this.$emit('verifyFun',this.formValidate);\n        },\n        force(){\n            this.$emit('forceFun',this.formValidate);\n        },\n        //视频详情\n        getInfo(id){\n            videoInfo(id).then(res=>{\n                this.formValidate = res.data;\n                this.tableData = res.data.productInfo;\n            }).catch(err=>{\n                this.$Message.error(err.msg)\n            })\n        },\n    }\n};\n", null]}