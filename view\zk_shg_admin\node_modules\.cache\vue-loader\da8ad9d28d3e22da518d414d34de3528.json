{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\components\\addCarMy.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\timport Setting from \"@/setting\";\n\timport util from \"@/libs/util\";\n\timport exportExcel from \"@/utils/newToExcel.js\";\n\timport {\n\t  importCard,\n\t  exportProductCard\n\t} from \"@/api/product\";\n    export default {\n        name: \"addCarMy\",\n        props:{\n\t\t\tvirtualList: {\n\t\t\t  type: Array,\n\t\t\t  default: function () {\n\t\t\t    return [];\n\t\t\t  }\n\t\t\t}\n        },\n        data(){\n            return {\n\t\t\t\tcartMyType:1,\n\t\t\t\tfixedCar:{\n\t\t\t\t\tdisk_info:'',\n\t\t\t\t\tstock:0\n\t\t\t\t},\n\t\t\t\tcardUrl: Setting.apiBaseURL + \"/file/upload/1\",\n\t\t\t\theader: {} //请求头部信息\n            }\n        },\n\t\tcreated() {\n\t\t    this.getToken();\n\t\t},\n        mounted() {\n        },\n        methods:{\n\t\t\t// 下载卡密\n\t\t\tasync getCarMyList() {\n\t\t\t  let [th, filekey, data, fileName] = [[], [], [], \"\"];\n\t\t\t  let lebData = await this.getExcelData();\n\t\t\t  if (!fileName) fileName = lebData.filename;\n\t\t\t  if (!filekey.length) {\n\t\t\t    filekey = lebData.filekey;\n\t\t\t  }\n\t\t\t  if (!th.length) th = lebData.header;\n\t\t\t  data = lebData.export;\n\t\t\t  exportExcel(th, filekey, fileName, data);\n\t\t\t},\n\t\t\tgetExcelData() {\n\t\t\t  return new Promise((resolve, reject) => {\n\t\t\t    exportProductCard().then((res) => {\n\t\t\t      return resolve(res.data);\n\t\t\t    });\n\t\t\t  });\n\t\t\t},\n\t\t\tremoveVirtual(index) {\n\t\t\t  this.virtualList.splice(index, 1);\n\t\t\t},\n\t\t\tupFile(res) {\n\t\t\t  importCard({ file: res.data.src }).then((res) => {\n\t\t\t\tthis.$emit('changeVirtual',JSON.parse(JSON.stringify(res.data))) \n\t\t\t   //this.$refs.upload.clearFiles();\t\n\t\t\t  }).catch(err=>{\n\t\t\t\t  return this.$Message.error(err.msg);\n\t\t\t  })\n\t\t\t},\n\t\t\thandleFormatError(file){\n\t\t\t\treturn this.$Message.error('必须上传xlsx格式文件');\n\t\t\t},\n\t\t\t// 上传头部token\n\t\t\tgetToken() {\n\t\t\t  this.header[\"Authori-zation\"] = \"Bearer \" + util.cookies.get(\"token\");\n\t\t\t},\n\t\t\tcancel(){\n\t\t\t\tthis.$emit('closeCarMy')\n\t\t\t},\n\t\t\thandleAdd() {\n\t\t\t  this.virtualList.push({\n\t\t\t    key: \"\",\n\t\t\t    value: \"\",\n\t\t\t  });\n\t\t\t},\n\t\t\tbeforeUpload() {\n\t\t\t  let promise = new Promise((resolve) => {\n\t\t\t    this.$nextTick(function () {\n\t\t\t      resolve(true);\n\t\t\t    });\n\t\t\t  });\n\t\t\t  return promise;\n\t\t\t},\n\t\t\tsubBtn(){\n\t\t\t\tif(this.cartMyType==1){\n\t\t\t\t\tthis.fixedCar.cartMyType = 1;\n\t\t\t\t\tif(this.fixedCar.disk_info == ''){\n\t\t\t\t\t\treturn this.$Message.error(\"请填写卡密信息\");\n\t\t\t\t\t}\n\t\t\t\t\tif(!this.fixedCar.stock){\n\t\t\t\t\t\treturn this.$Message.error(\"请填写库存数量\");\n\t\t\t\t\t}\n\t\t\t\t\tthis.$emit('fixdBtn',JSON.parse(JSON.stringify(this.fixedCar)))\n\t\t\t\t}else{\n\t\t\t\t\tlet data = {\n\t\t\t\t\t\tcartMyType:2,\n\t\t\t\t\t\tvirtualList:this.virtualList\n\t\t\t\t\t}\n\t\t\t\t\tfor (let i = 0; i < this.virtualList.length; i++) {\n\t\t\t\t\t  const element = this.virtualList[i];\n\t\t\t\t\t  if (!element.value) {\n\t\t\t\t\t    return this.$Message.error(\"请输入所有卡密\");\n\t\t\t\t\t  }\n\t\t\t\t\t}\n\t\t\t\t\tthis.$emit('fixdBtn',JSON.parse(JSON.stringify(data)))\n\t\t\t\t}\n\t\t\t}\n        }\n    }\n", null]}