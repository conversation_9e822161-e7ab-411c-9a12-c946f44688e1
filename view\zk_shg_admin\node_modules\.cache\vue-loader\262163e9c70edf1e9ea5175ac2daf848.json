{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\tableExpand.vue?vue&type=template&id=2c2e31b8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\tableExpand.vue", "mtime": 1662341778000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Row',{staticClass:\"expand-row\"},[_c('Col',{attrs:{\"span\":\"8\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"商品分类：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm.row.cate_name))])]),_c('Col',{attrs:{\"span\":\"8\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"商品市场价格：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm.row.ot_price))])]),_c('Col',{attrs:{\"span\":\"8\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"成本价：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm.row.cost))])])],1),_c('Row',{staticClass:\"expand-row\"},[_c('Col',{attrs:{\"span\":\"8\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"收藏：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm.row.collect))])]),_c('Col',{attrs:{\"span\":\"8\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"虚拟销量：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm.row.ficti))])]),_c('Col',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.row.is_verify === -1),expression:\"row.is_verify === -1\"}],attrs:{\"span\":\"8\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"审核未通过原因：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm.row.refusal))])]),_c('Col',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.row.is_verify === -2),expression:\"row.is_verify === -2\"}],attrs:{\"span\":\"8\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"强制下架原因：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm.row.refusal))])])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}