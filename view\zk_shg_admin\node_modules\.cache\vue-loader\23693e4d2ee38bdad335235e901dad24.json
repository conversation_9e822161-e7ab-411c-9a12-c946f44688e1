{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods.vue?vue&type=template&id=53491fa8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.defaults.goodsList)?_c('div',{staticClass:\"goods-box\"},[_c('div',{staticClass:\"acea-row\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"选择商品\")]),_c('div',{staticClass:\"wrapper\"},[_c('draggable',{staticClass:\"dragArea list-group\",attrs:{\"list\":_vm.defaults.goodsList.list,\"group\":\"peoples\"}},[_vm._l((_vm.defaults.goodsList.list),function(goods,index){return (_vm.defaults.goodsList.list.length)?_c('div',{key:index,staticClass:\"item\"},[_c('img',{attrs:{\"src\":goods.image,\"alt\":\"\"}}),_c('span',{staticClass:\"iconfont-diy icondel_1\",on:{\"click\":function($event){$event.stopPropagation();return _vm.bindDelete(index)}}})]):_vm._e()}),_c('div',{staticClass:\"add-item item\",on:{\"click\":function($event){_vm.modals = true}}},[_c('span',{staticClass:\"iconfont-diy iconaddto\"})])],2)],1)]),_c('Modal',{staticClass:\"paymentFooter\",attrs:{\"title\":\"商品列表\",\"footerHide\":\"\",\"scrollable\":\"\",\"width\":\"900\"},on:{\"on-cancel\":_vm.cancel},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[(_vm.modals)?_c('goods-list',{ref:\"goodslist\",attrs:{\"ischeckbox\":true,\"isdiy\":true},on:{\"getProductId\":_vm.getProductId}}):_vm._e()],1)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}