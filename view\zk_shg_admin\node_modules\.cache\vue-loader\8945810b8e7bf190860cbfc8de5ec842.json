{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\agent\\handle\\promotersList.vue?vue&type=template&id=1a72cadc&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\agent\\handle\\promotersList.vue", "mtime": 1682663004000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Modal',{attrs:{\"scrollable\":\"\",\"footer-hide\":\"\",\"closable\":\"\",\"title\":_vm.listTitle === 'man'?'统计推广人列表':'推广订单',\"mask-closable\":false,\"width\":\"900\"},on:{\"on-cancel\":_vm.onCancel},model:{value:(_vm.modals),callback:function ($$v) {_vm.modals=$$v},expression:\"modals\"}},[_c('div',{staticClass:\"table_box\"},[_c('Form',{ref:\"formValidate\",staticClass:\"tabform\",attrs:{\"inline\":\"\",\"model\":_vm.formValidate,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"时间选择：\"}},[_c('DatePicker',{staticClass:\"input-add\",attrs:{\"editable\":false,\"value\":_vm.timeVal,\"format\":\"yyyy/MM/dd\",\"type\":\"datetimerange\",\"placement\":\"bottom-start\",\"placeholder\":\"自定义时间\",\"options\":_vm.options},on:{\"on-change\":_vm.onchangeTime}})],1),_c('FormItem',{attrs:{\"label\":\"用户类型：\"}},[_c('Select',{staticClass:\"input-add\",model:{value:(_vm.formValidate.type),callback:function ($$v) {_vm.$set(_vm.formValidate, \"type\", $$v)},expression:\"formValidate.type\"}},_vm._l((_vm.fromList.fromTxt2),function(item,i){return _c('Option',{key:i,attrs:{\"value\":item.val}},[_vm._v(_vm._s(item.text))])}),1)],1),_c('FormItem',{attrs:{\"label\":\"搜索：\"}},[_c('Input',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请输入请姓名、电话、UID\"},model:{value:(_vm.formValidate.nickname),callback:function ($$v) {_vm.$set(_vm.formValidate, \"nickname\", $$v)},expression:\"formValidate.nickname\"}})],1),(_vm.listTitle !== 'man')?_c('FormItem',{attrs:{\"label\":\"订单号：\"}},[_c('Input',{staticClass:\"input-add mr14\",attrs:{\"placeholder\":\"请输入请订单号\"},model:{value:(_vm.formValidate.order_id),callback:function ($$v) {_vm.$set(_vm.formValidate, \"order_id\", $$v)},expression:\"formValidate.order_id\"}})],1):_vm._e(),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.userSearchs()}}},[_vm._v(\"查询\")])],1)],1),_c('Table',{ref:\"selection\",attrs:{\"columns\":_vm.columns4,\"data\":_vm.tabList,\"loading\":_vm.loading,\"no-data-text\":\"暂无数据\",\"highlight-row\":\"\",\"max-height\":\"400\",\"no-filtered-data-text\":\"暂无筛选结果\"}}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.formValidate.limit},on:{\"on-change\":_vm.pageChange}})],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}