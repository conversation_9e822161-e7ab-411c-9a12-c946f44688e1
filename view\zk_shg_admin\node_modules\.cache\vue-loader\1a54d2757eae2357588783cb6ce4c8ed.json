{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue?vue&type=template&id=03378fd1&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\attribute\\index.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Modal',{attrs:{\"title\":\"商品属性\",\"width\":\"70%\"},on:{\"on-cancel\":_vm.cancel},model:{value:(_vm.val),callback:function ($$v) {_vm.val=$$v},expression:\"val\"}},[_c('div',{staticClass:\"Modals\"},[_c('Form',{ref:\"form\",staticClass:\"form\",attrs:{\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{attrs:{\"xl\":24,\"lg\":24,\"md\":24,\"sm\":24,\"xs\":24}},[_c('FormItem',{attrs:{\"label\":\"规格：\",\"prop\":\"store_name\",\"label-for\":\"store_name\"}},[_vm._l((_vm.specs),function(item,index){return _c('Input',{key:index,staticClass:\"input\",staticStyle:{\"width\":\"10%\"},attrs:{\"placeholder\":\"规格\",\"value\":item}},[_c('Icon',{attrs:{\"slot\":\"suffix\",\"type\":\"md-close\"},slot:\"suffix\"})],1)}),_c('Input',{staticClass:\"input\",staticStyle:{\"width\":\"10%\"},attrs:{\"placeholder\":\"请输入\"},model:{value:(_vm.specsVal),callback:function ($$v) {_vm.specsVal=$$v},expression:\"specsVal\"}},[_c('Icon',{attrs:{\"slot\":\"suffix\",\"type\":\"md-add\"},on:{\"click\":_vm.confirm},slot:\"suffix\"})],1)],2)],1),_c('Col',{attrs:{\"xl\":24,\"lg\":24,\"md\":24,\"sm\":24,\"xs\":24}},_vm._l((_vm.attrList),function(item,index){return _c('FormItem',{key:index,attrs:{\"label\":item.attr+':',\"prop\":\"store_name\",\"label-for\":\"store_name\"}},[_vm._l((item.attrVal),function(itemn,index){return _c('Tag',{key:index,attrs:{\"type\":\"border\",\"closable\":\"\",\"color\":\"primary\"}},[_vm._v(_vm._s(itemn))])}),_c('Input',{staticClass:\"input\",staticStyle:{\"width\":\"10%\"},attrs:{\"placeholder\":\"请输入\"},model:{value:(item.inputVal),callback:function ($$v) {_vm.$set(item, \"inputVal\", $$v)},expression:\"item.inputVal\"}},[_c('Icon',{attrs:{\"slot\":\"suffix\",\"type\":\"md-add\"},on:{\"click\":function($event){return _vm.confirmAttr(index)}},slot:\"suffix\"})],1)],2)}),1)],1)],1)],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"})])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}