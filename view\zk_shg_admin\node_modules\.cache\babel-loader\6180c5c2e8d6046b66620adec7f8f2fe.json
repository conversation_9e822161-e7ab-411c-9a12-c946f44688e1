{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\right\\index.vue", "mtime": 1718158266000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mapState, mapMutations } from \"vuex\";\nimport { memberRight, memberRightSave, saveMemberContent as _saveMemberContent } from \"@/api/user\";\nimport uploadPictures from \"@/components/uploadPictures\";\nimport WangEditor from \"@/components/wangEditor/index.vue\";\nexport default {\n  components: {\n    uploadPictures: uploadPictures,\n    WangEditor: WangEditor\n  },\n  data: function data() {\n    return {\n      thead: [{\n        title: \"权益名称\",\n        key: \"title\"\n      }, {\n        title: \"展示名称\",\n        key: \"show_title\"\n      }, {\n        title: \"权益图标（80x80）\",\n        slot: \"image\"\n      }, {\n        title: \"权益简介\",\n        key: \"explain\"\n      }, {\n        title: \"权益状态\",\n        slot: \"status\"\n      }, {\n        title: \"操作\",\n        slot: \"action\"\n      }],\n      tbody: [],\n      loading: false,\n      total: 0,\n      page: 1,\n      limit: 30,\n      modal1: false,\n      form: {\n        id: \"\",\n        right_type: \"\",\n        title: \"\",\n        show_title: \"\",\n        image: \"\",\n        explain: \"\",\n        number: 1,\n        status: 1\n      },\n      rules: {\n        title: [{\n          required: true,\n          message: \"请输入权益名称\",\n          trigger: \"blur\"\n        }],\n        show_title: [{\n          required: true,\n          message: \"请输入展示名称\",\n          trigger: \"blur\"\n        }],\n        image: [{\n          required: true,\n          message: \"请上传权益图标\"\n        }],\n        explain: [{\n          required: true,\n          message: \"请输入权益简介\",\n          trigger: \"blur\"\n        }],\n        number: [{\n          required: true,\n          type: \"integer\",\n          message: \"请输入正整数\"\n        }]\n      },\n      modal2: false,\n      gridPic: {\n        xl: 6,\n        lg: 8,\n        md: 12,\n        sm: 12,\n        xs: 12\n      },\n      gridBtn: {\n        xl: 4,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 8\n      },\n      modal3: false,\n      content: '',\n      agreementCon: ''\n    };\n  },\n  computed: _objectSpread({}, mapState(\"media\", [\"isMobile\"])),\n  created: function created() {\n    this.getRightList();\n  },\n  methods: {\n    getRightList: function getRightList() {\n      var _this = this;\n\n      this.loading = true;\n      memberRight().then(function (res) {\n        var _res$data = res.data,\n            count = _res$data.count,\n            list = _res$data.list;\n        _this.loading = false;\n        _this.total = count;\n        _this.tbody = list;\n      }).catch(function (err) {\n        _this.loading = false;\n\n        _this.$Message.error(err);\n      });\n    },\n    // 改变状态\n    statusChange: function statusChange(row) {\n      this.form.id = row.id;\n      this.form.right_type = row.right_type;\n      this.form.title = row.title;\n      this.form.show_title = row.show_title;\n      this.form.image = row.image;\n      this.form.explain = row.explain;\n      this.form.number = row.number;\n      this.form.status = row.status;\n      this.rightSave();\n    },\n    // 编辑\n    edit: function edit(row) {\n      this.modal1 = true;\n      this.form.id = row.id;\n      this.form.status = row.status;\n      this.form.right_type = row.right_type;\n      this.form.title = row.title;\n      this.form.show_title = row.show_title;\n      this.form.image = row.image;\n      this.form.explain = row.explain;\n      this.form.number = row.number;\n    },\n    // 分页\n    pageChange: function pageChange(index) {\n      this.page = index;\n      this.getRightList();\n    },\n    // 修改\n    rightSave: function rightSave() {\n      var _this2 = this;\n\n      memberRightSave(this.form).then(function (res) {\n        _this2.modal1 = false;\n\n        _this2.getRightList();\n\n        _this2.$Message.success(res.msg);\n      }).catch(function (err) {\n        _this2.$Message.error(err.msg);\n      });\n    },\n    formSubmit: function formSubmit(name) {\n      var _this3 = this;\n\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this3.rightSave();\n        }\n      });\n    },\n    callImage: function callImage() {\n      this.modal2 = true;\n    },\n    getPic: function getPic(image) {\n      this.form.image = image.att_dir;\n      this.modal2 = false;\n    },\n    editRight: function editRight(row) {\n      this.modal3 = true;\n      this.form.id = row.id;\n      this.content = row.content;\n      this.agreementCon = row.content;\n    },\n    getEditorContent: function getEditorContent(content) {\n      this.agreementCon = content;\n    },\n    saveMemberContent: function saveMemberContent() {\n      var _this4 = this;\n\n      _saveMemberContent(this.form.id, {\n        content: this.agreementCon\n      }).then(function (res) {\n        _this4.modal3 = false;\n\n        _this4.$Message.success(res.msg);\n\n        _this4.getRightList();\n      }).catch(function (err) {\n        _this4.$Message.error(err.msg);\n      });\n    }\n  }\n};", null]}