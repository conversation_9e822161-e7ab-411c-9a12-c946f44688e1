{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue?vue&type=template&id=5173b4e1&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\inventory\\index.vue", "mtime": 1751012477533}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"sales-stats-container\"},[_c('div',{staticClass:\"page-header\"},[_vm._m(0),_c('div',{staticClass:\"header-right\"},[_c('Button',{attrs:{\"type\":\"success\",\"icon\":\"ios-download\"},on:{\"click\":_vm.exportSales}},[_vm._v(\"导出统计\")])],1)]),_c('div',{staticClass:\"filter-container\"},[_c('Form',{attrs:{\"model\":_vm.filterForm,\"inline\":\"\"}},[_c('FormItem',{attrs:{\"label\":\"门店：\"}},[_c('Select',{staticStyle:{\"width\":\"200px\"},attrs:{\"placeholder\":\"选择门店\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.filterForm.store_id),callback:function ($$v) {_vm.$set(_vm.filterForm, \"store_id\", $$v)},expression:\"filterForm.store_id\"}},_vm._l((_vm.storeList),function(store){return _c('Option',{key:store.value,attrs:{\"value\":store.value,\"label\":store.label}},[_vm._v(\"\\n            \"+_vm._s(store.label)+\"\\n          \")])}),1)],1),_c('FormItem',{attrs:{\"label\":\"日期范围：\"}},[_c('DatePicker',{staticStyle:{\"width\":\"300px\"},attrs:{\"type\":\"daterange\",\"split-panels\":\"\",\"placeholder\":\"选择日期范围\"},on:{\"on-change\":_vm.handleDateChange},model:{value:(_vm.dateRange),callback:function ($$v) {_vm.dateRange=$$v},expression:\"dateRange\"}})],1),_c('FormItem',{attrs:{\"label\":\"商品名称：\"}},[_c('Input',{staticStyle:{\"width\":\"200px\"},attrs:{\"placeholder\":\"输入商品名称\",\"search\":\"\",\"enter-button\":\"\"},on:{\"on-search\":_vm.getSalesStatistics},model:{value:(_vm.filterForm.keyword),callback:function ($$v) {_vm.$set(_vm.filterForm, \"keyword\", $$v)},expression:\"filterForm.keyword\"}})],1),_c('FormItem',[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.getSalesStatistics}},[_vm._v(\"查询\")]),_c('Button',{staticStyle:{\"margin-left\":\"8px\"},on:{\"click\":_vm.resetFilter}},[_vm._v(\"重置\")])],1)],1)],1),_c('Table',{attrs:{\"loading\":_vm.tableLoading,\"columns\":_vm.columns,\"data\":_vm.tableData,\"border\":\"\",\"highlight-row\":\"\"}}),_c('div',{staticClass:\"pagination-container\"},[_c('Page',{attrs:{\"total\":_vm.total,\"current\":_vm.currentPage,\"page-size\":_vm.pageSize,\"page-size-opts\":[20, 50, 100, 200],\"show-total\":\"\",\"show-sizer\":\"\",\"show-elevator\":\"\"},on:{\"update:current\":function($event){_vm.currentPage=$event},\"on-change\":_vm.handleCurrentChange,\"on-page-size-change\":_vm.handleSizeChange}})],1)],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_vm._v(\"商品销售统计\")]),_c('p',{staticClass:\"page-desc\"},[_vm._v(\"查看各门店商品销售量、销售金额和当前库存统计\")])])}]\n\nexport { render, staticRenderFns }"]}