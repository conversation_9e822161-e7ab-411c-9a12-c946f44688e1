{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue?vue&type=template&id=455dedc3&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productList\\taoBao.vue", "mtime": 1677460412000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <div class=\"Box\">\n    <Card>\n      <div>\n        生成的商品默认是没有上架的，请手动上架商品！\n        <a\n          href=\"http://help.crmeb.net/crmeb-v4/1863579\"\n          v-if=\"copyConfig.copy_type == 2\"\n          target=\"_blank\"\n          >如何配置密钥</a\n        >\n        <span v-else\n          >您当前剩余{{ copyConfig.copy_num }}条采集次数，<a\n            href=\"#\"\n            @click=\"mealPay('copy')\"\n            >增加采集次数</a\n          ></span\n        >\n      </div>\n      <div>商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置</div>\n    </Card>\n    <Form\n      class=\"formValidate mt20\"\n      ref=\"formValidate\"\n      :model=\"formValidate\"\n      :rules=\"ruleInline\"\n      :label-width=\"labelWidth\"\n      :label-position=\"labelPosition\"\n      @submit.native.prevent\n    >\n      <Row :gutter=\"24\" type=\"flex\">\n        <!--                <Col span=\"24\">-->\n        <!--                    <FormItem label=\"\"  label-for=\"\">-->\n        <!--                        <RadioGroup v-model=\"artFrom.type\">-->\n        <!--                            <Radio label=\"taobao\">淘宝</Radio>-->\n        <!--                            <Radio label=\"tmall\">天猫</Radio>-->\n        <!--                            <Radio label=\"jd\">京东</Radio>-->\n        <!--                            <Radio label=\"pdd\">拼多多</Radio>-->\n        <!--                            <Radio label=\"suning\">苏宁</Radio>-->\n        <!--                            <Radio label=\"1688\">1688</Radio>-->\n        <!--                        </RadioGroup>-->\n        <!--                    </FormItem>-->\n        <!--                </Col>-->\n        <Col span=\"15\">\n          <FormItem label=\"链接地址：\">\n            <Input\n              search\n              enter-button=\"确定\"\n              v-model=\"soure_link\"\n              placeholder=\"请输入链接地址\"\n              class=\"numPut\"\n              @on-search=\"add\"\n            />\n          </FormItem>\n        </Col>\n        <div>\n          <div v-if=\"isData\">\n            <Col span=\"24\">\n              <FormItem label=\"商品名称：\" prop=\"store_name\">\n                <Input\n                  v-model=\"formValidate.store_name\"\n                  placeholder=\"请输入商品名称\"\n                />\n              </FormItem>\n            </Col>\n            <Col span=\"22\">\n              <FormItem\n                label=\"商品简介：\"\n                prop=\"store_info\"\n                label-for=\"store_info\"\n              >\n                <Input\n                  v-model=\"formValidate.store_info\"\n                  type=\"textarea\"\n                  :rows=\"3\"\n                  placeholder=\"请输入商品简介\"\n                />\n              </FormItem>\n            </Col>\n            <Col span=\"22\">\n              <FormItem label=\"商品分类：\" prop=\"cate_id\">\n                <Select v-model=\"formValidate.cate_id\" multiple>\n                  <Option\n                    v-for=\"item in treeSelect\"\n                    :disabled=\"item.pid === 0\"\n                    :value=\"item.id\"\n                    :key=\"item.id\"\n                    >{{ item.html + item.cate_name }}</Option\n                  >\n                </Select>\n              </FormItem>\n            </Col>\n            <Col v-bind=\"grid\">\n              <FormItem label=\"商品关键字：\" prop=\"keyword\" label-for=\"keyword\">\n                <Input\n                  v-model=\"formValidate.keyword\"\n                  placeholder=\"请输入商品关键字\"\n                />\n              </FormItem>\n            </Col>\n            <Col v-bind=\"grid\">\n              <FormItem label=\"单位：\" prop=\"unit_name\" label-for=\"unit_name\">\n                <Input\n                  v-model=\"formValidate.unit_name\"\n                  placeholder=\"请输入单位\"\n                />\n              </FormItem>\n            </Col>\n            <Col v-bind=\"grid\">\n              <FormItem label=\"虚拟销量：\" label-for=\"ficti\">\n                <InputNumber\n                  v-width=\"'100%'\"\n                  v-model=\"formValidate.ficti\"\n                  placeholder=\"请输入虚拟销量\"\n                />\n              </FormItem>\n            </Col>\n            <Col v-bind=\"grid\">\n              <FormItem label=\"积分：\" label-for=\"give_integral\">\n                <InputNumber\n                  v-width=\"'100%'\"\n                  v-model=\"formValidate.give_integral\"\n                  placeholder=\"请输入积分\"\n                />\n              </FormItem>\n            </Col>\n            <Col v-bind=\"grid\">\n              <FormItem label=\"运费模板：\" prop=\"temp_id\">\n                <Select v-model=\"formValidate.temp_id\" clearable>\n                  <Option\n                    v-for=\"(item, index) in templateList\"\n                    :value=\"item.id\"\n                    :key=\"index\"\n                    >{{ item.name }}</Option\n                  >\n                </Select>\n              </FormItem>\n            </Col>\n            <!--<Col v-bind=\"grid\">-->\n            <!--<FormItem label=\"邮费：\"  label-for=\"postage\">-->\n            <!--<InputNumber v-width=\"'100%'\" v-model=\"formValidate.postage\" placeholder=\"请输入邮费\"  />-->\n            <!--</FormItem>-->\n            <!--</Col>-->\n\n            <Col span=\"24\">\n              <FormItem label=\"商品图：\">\n                <div class=\"pictrueBox\">\n                  <div class=\"pictrue\" v-if=\"formValidate.image\">\n                    <viewer><img v-lazy=\"formValidate.image\" /></viewer>\n                  </div>\n                </div>\n              </FormItem>\n            </Col>\n            <Col span=\"24\">\n              <FormItem label=\"商品轮播图：\">\n                <viewer>\n                  <div class=\"acea-row\">\n                    <div\n                      class=\"lunBox mr15\"\n                      v-for=\"(item, index) in formValidate.slider_image\"\n                      :key=\"index\"\n                      draggable=\"true\"\n                      @dragstart=\"handleDragStart($event, item)\"\n                      @dragover.prevent=\"handleDragOver($event, item)\"\n                      @dragenter=\"handleDragEnter($event, item)\"\n                      @dragend=\"handleDragEnd($event, item)\"\n                    >\n                      <div class=\"pictrue\"><img v-lazy=\"item\" /></div>\n                      <ButtonGroup size=\"small\">\n                        <Button @click.native=\"checked(item, index)\"\n                          >主图</Button\n                        >\n                        <Button @click.native=\"handleRemove(index)\"\n                          >移除</Button\n                        >\n                      </ButtonGroup>\n                    </div>\n                  </div>\n                </viewer>\n              </FormItem>\n            </Col>\n            <Col span=\"24\">\n              <FormItem\n                label=\"批量设置：\"\n                class=\"labeltop\"\n                v-if=\"formValidate.attrs\"\n              >\n                <Table :data=\"oneFormBatch\" :columns=\"columnsBatch\" border>\n                  <template slot-scope=\"{ row, index }\" slot=\"pic\">\n                    <div\n                      class=\"acea-row row-middle row-center-wrapper\"\n                      @click=\"modalPicTap('dan', 'duopi', index)\"\n                    >\n                      <div\n                        class=\"pictrue pictrueTab\"\n                        v-if=\"oneFormBatch[0].pic\"\n                      >\n                        <img v-lazy=\"oneFormBatch[0].pic\" />\n                      </div>\n                      <div\n                        class=\"upLoad pictrueTab acea-row row-center-wrapper\"\n                        v-else\n                      >\n                        <Icon\n                          type=\"ios-camera-outline\"\n                          size=\"21\"\n                          class=\"iconfont\"\n                        />\n                      </div>\n                    </div>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"price\">\n                    <InputNumber\n                      v-model=\"oneFormBatch[0].price\"\n                      :min=\"0\"\n                      class=\"priceBox\"\n                    ></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"cost\">\n                    <InputNumber\n                      v-model=\"oneFormBatch[0].cost\"\n                      :min=\"0\"\n                      class=\"priceBox\"\n                    ></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"ot_price\">\n                    <InputNumber\n                      v-model=\"oneFormBatch[0].ot_price\"\n                      :min=\"0\"\n                      class=\"priceBox\"\n                    ></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"stock\">\n                    <InputNumber\n                      v-model=\"oneFormBatch[0].stock\"\n                      :min=\"0\"\n                      class=\"priceBox\"\n                    ></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"bar_code\">\n                    <Input v-model=\"oneFormBatch[0].bar_code\"></Input>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"weight\">\n                    <InputNumber\n                      v-model=\"oneFormBatch[0].weight\"\n                      :min=\"0\"\n                      class=\"priceBox\"\n                    ></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"volume\">\n                    <InputNumber\n                      v-model=\"oneFormBatch[0].volume\"\n                      :min=\"0\"\n                      class=\"priceBox\"\n                    ></InputNumber>\n                  </template>\n                  <template slot-scope=\"{ row, index }\" slot=\"action\">\n                    <a @click=\"batchAdd\">添加</a>\n                    <Divider type=\"vertical\" />\n                    <a @click=\"batchDel\">清空</a>\n                  </template>\n                </Table>\n              </FormItem>\n            </Col>\n            <Col span=\"24\">\n              <FormItem\n                label=\"商品规格：\"\n                props=\"spec_type\"\n                label-for=\"spec_type\"\n              >\n                <!-- 单规格表格-->\n                <Col :xl=\"23\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n                  <FormItem>\n                    <Table :data=\"items\" :columns=\"columns\" border>\n                      <template slot-scope=\"{ row, index }\" slot=\"pic\">\n                        <div\n                          class=\"acea-row row-middle row-center-wrapper\"\n                          @click=\"modalPicTap('dan', index)\"\n                        >\n                          <div\n                            class=\"pictrue pictrueTab\"\n                            v-if=\"formValidate.attrs[index].pic\"\n                          >\n                            <img v-lazy=\"formValidate.attrs[index].pic\" />\n                          </div>\n                          <div\n                            class=\"upLoad upLoadTab acea-row row-center-wrapper\"\n                            v-else\n                          >\n                            <Icon\n                              type=\"ios-camera-outline\"\n                              size=\"26\"\n                              class=\"iconfont\"\n                            />\n                          </div>\n                        </div>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"price\">\n                        <InputNumber\n                          v-model=\"formValidate.attrs[index].price\"\n                          class=\"priceBox\"\n                        ></InputNumber>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"cost\">\n                        <InputNumber\n                          v-model=\"formValidate.attrs[index].cost\"\n                          class=\"priceBox\"\n                        ></InputNumber>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"ot_price\">\n                        <InputNumber\n                          v-model=\"formValidate.attrs[index].ot_price\"\n                          class=\"priceBox\"\n                        ></InputNumber>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"vip_price\">\n                        <InputNumber\n                          v-model=\"formValidate.attrs[index].vip_price\"\n                          class=\"priceBox\"\n                        ></InputNumber>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"stock\">\n                        <InputNumber\n                          v-model=\"formValidate.attrs[index].stock\"\n                          class=\"priceBox\"\n                        ></InputNumber>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"bar_code\">\n                        <Input\n                          v-model=\"formValidate.attrs[index].bar_code\"\n                        ></Input>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"weight\">\n                        <InputNumber\n                          v-model=\"formValidate.attrs[index].weight\"\n                          :min=\"0\"\n                          class=\"priceBox\"\n                        ></InputNumber>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"volume\">\n                        <InputNumber\n                          v-model=\"formValidate.attrs[index].volume\"\n                          :min=\"0\"\n                          class=\"priceBox\"\n                        ></InputNumber>\n                      </template>\n                      <template slot-scope=\"{ row, index }\" slot=\"action\">\n                        <a @click=\"delAttrTable(index)\">删除</a>\n                      </template>\n                    </Table>\n                  </FormItem>\n                </Col>\n              </FormItem>\n            </Col>\n            <Col span=\"24\">\n              <FormItem label=\"商品详情：\">\n\t\t\t\t<WangEditor\n\t\t\t\t  style=\"width: 100%\"\n\t\t\t\t  :content=\"formValidate.description\"\n\t\t\t\t  @editorContent=\"getEditorContent\"\n\t\t\t\t></WangEditor>\n              </FormItem>\n            </Col>\n            <Col span=\"24\">\n              <FormItem>\n                <Button\n                  type=\"primary\"\n                  :loading=\"modal_loading\"\n                  class=\"submission\"\n                  @click=\"handleSubmit('formValidate')\"\n                  >提交</Button\n                >\n              </FormItem>\n            </Col>\n          </div>\n          <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\n        </div>\n      </Row>\n    </Form>\n    <Modal\n      v-model=\"modalPic\"\n      width=\"960px\"\n      scrollable\n      footer-hide\n      closable\n      title=\"上传商品图\"\n      :mask-closable=\"false\"\n      class=\"aaaa\"\n    >\n      <uploadPictures\n        :isChoice=\"isChoice\"\n        @getPic=\"getPic\"\n        :gridBtn=\"gridBtn\"\n        :gridPic=\"gridPic\"\n        v-if=\"modalPic\"\n      ></uploadPictures>\n    </Modal>\n  </div>\n", null]}