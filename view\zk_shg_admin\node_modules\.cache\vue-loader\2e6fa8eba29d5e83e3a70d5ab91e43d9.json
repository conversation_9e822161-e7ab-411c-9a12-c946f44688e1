{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAdd\\taoBao.vue?vue&type=template&id=0633a2e6&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\product\\productAdd\\taoBao.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"Box\"},[_c('Card',[_c('div',[_vm._v(\"生成的商品默认是没有上架的，请手动上架商品！\\n            \"),(_vm.copyConfig.copy_type == 2)?_c('a',{attrs:{\"href\":\"http://help.crmeb.net/crmeb-v4/1863579\",\"target\":\"_blank\"}},[_vm._v(\"如何配置密钥\")]):_c('span',[_vm._v(\"您当前剩余\"+_vm._s(_vm.copyConfig.copy_num)+\"条采集次数，\"),_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.mealPay('copy')}}},[_vm._v(\"增加采集次数\")])])]),_c('div',[_vm._v(\"商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置\")])]),_c('Form',{ref:\"formValidate\",staticClass:\"formValidate mt20\",attrs:{\"label-width\":120,\"label-position\":\"right\"},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{attrs:{\"span\":\"18\"}},[_c('FormItem',{attrs:{\"label\":\"链接地址：\"}},[_c('Input',{staticClass:\"numPut\",attrs:{\"search\":\"\",\"enter-button\":\"确定\",\"placeholder\":\"请输入链接地址\"},on:{\"on-search\":_vm.add},model:{value:(_vm.soure_link),callback:function ($$v) {_vm.soure_link=$$v},expression:\"soure_link\"}})],1)],1)],1)],1),(_vm.spinShow)?_c('Spin',{attrs:{\"size\":\"large\",\"fix\":\"\"}}):_vm._e()],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}