{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\bill\\index.vue?vue&type=template&id=32a2707c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\finance\\financialRecords\\bill\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding= \"0\">\n      <div class=\"new_card_pd\">\n          <Form \n          ref=\"formValidate\"\n          :model=\"formValidate\"\n          :label-width=\"labelWidth\"\n          inline\n          :label-position=\"labelPosition\"\n          class=\"tabform\"\n          @submit.native.prevent\n        >\n            <FormItem label=\"昵称/ID：\">\n              <Input\n                enter-button\n                placeholder=\"请输入\"\n                element-id=\"name\"\n                class=\"input-width\"\n                v-model=\"formValidate.nickname\"\n              />\n            </FormItem>\n            <FormItem label=\"时间范围：\" class=\"tab_data\">\n              <DatePicker\n                :editable=\"false\"\n                class=\"input-width\"\n                @on-change=\"onchangeTime\"\n                format=\"yyyy/MM/dd\"\n                type=\"datetimerange\"\n                placement=\"bottom-start\"\n                placeholder=\"自定义时间\"\n                :options=\"options\"\n              ></DatePicker>\n            </FormItem>\n            <FormItem label=\"筛选类型：\" class=\"tab_data\">\n              <Select\n                v-model=\"formValidate.type\"\n                class=\"input-add mr14\"\n                clearable\n\t\t\t\t@on-change=\"userSearchs\"\n              >\n                <Option\n                  v-for=\"(item, index) in billList\"\n                  :key=\"index\"\n                  :value=\"item.type\"\n                  >{{ item.title }}</Option\n                >\n              </Select>\n              <Button type=\"primary\" @click=\"userSearchs\"\n              >搜索</Button\n              >\n              <Button\n                v-auth=\"['export-userFinance']\"\n                class=\"export\"\n                @click=\"exports\"\n                >导出</Button\n              >\n            </FormItem>\n      </Form>\n      </div>\n    </Card>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n      <Table\n        ref=\"table\"\n        highlight-row\n        :columns=\"columns\"\n        :data=\"tabList\"\n        :loading=\"loading\"\n        no-data-text=\"暂无数据\"\n        no-filtered-data-text=\"暂无筛选结果\"\n      >\n        <template slot-scope=\"{ row }\" slot=\"number\">\n          <div :class=\"[row.pm === 1 ? 'green' : 'red']\">\n            {{ row.pm === 1 ? row.number : \"-\" + row.number }}\n          </div>\n        </template>\n      </Table>\n      <div class=\"acea-row row-right page\">\n        <Page\n          :total=\"total\"\n          :current=\"formValidate.page\"\n          show-elevator\n          show-total\n          :page-size=\"formValidate.limit\"\n          @on-change=\"pageChange\"\n        />\n      </div>\n    </Card>\n  </div>\n", null]}