{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue?vue&type=template&id=60cd1378&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\list\\index.vue", "mtime": 1728874543955}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\",\"padding\":0}},[_c('div',{staticClass:\"padding-add\"},[_c('Form',{ref:\"userFrom\",attrs:{\"model\":_vm.userFrom,\"label-width\":_vm.labelWidth,\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('Row',{attrs:{\"gutter\":24}},[_c('Col',{attrs:{\"span\":\"18\"}},[_c('Row',[_c('Col',{attrs:{\"span\":\"24\"}},[_c('Row',[_c('Col',[_c('FormItem',{attrs:{\"label\":\"用户搜索：\",\"label-for\":\"nickname\"}},[_c('Input',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请输入\",\"element-id\":\"nickname\",\"clearable\":\"\"},model:{value:(_vm.userFrom.nickname),callback:function ($$v) {_vm.$set(_vm.userFrom, \"nickname\", $$v)},expression:\"userFrom.nickname\"}},[_c('Select',{staticStyle:{\"width\":\"80px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\",model:{value:(_vm.field_key),callback:function ($$v) {_vm.field_key=$$v},expression:\"field_key\"}},[_c('Option',{attrs:{\"value\":\"all\"}},[_vm._v(\"全部\")]),_c('Option',{attrs:{\"value\":\"uid\"}},[_vm._v(\"UID\")]),_c('Option',{attrs:{\"value\":\"phone\"}},[_vm._v(\"手机号\")]),_c('Option',{attrs:{\"value\":\"nickname\"}},[_vm._v(\"用户昵称\")])],1)],1)],1)],1)],1)],1)],1)],1),(_vm.collapse)?[_c('Col',{attrs:{\"span\":\"18\"}},[_c('Row',[_c('Col',[_c('FormItem',{attrs:{\"label\":\"用户分组：\",\"label-for\":\"group_id\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"element-id\":\"group_id\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.userFrom.group_id),callback:function ($$v) {_vm.$set(_vm.userFrom, \"group_id\", $$v)},expression:\"userFrom.group_id\"}},_vm._l((_vm.groupList),function(item,index){return _c('Option',{key:index,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.group_name))])}),1)],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"用户标签：\",\"label-for\":\"label_id\"}},[_c('div',{staticClass:\"labelInput acea-row row-between-wrapper input-add\",on:{\"click\":_vm.openLabelList}},[_c('div',[(_vm.dataLabel.length)?_c('div',_vm._l((_vm.dataLabel),function(item,index){return _c('Tag',{key:index,attrs:{\"closable\":\"\"},on:{\"on-close\":function($event){return _vm.closeLabel(item)}}},[_vm._v(_vm._s(item.label_name))])}),1):_c('span',{staticClass:\"span\"},[_vm._v(\"请选择\")])]),_c('div',{staticClass:\"iconfont iconxiayi\"})])])],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"性别：\",\"label-for\":\"sex\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.userFrom.sex),callback:function ($$v) {_vm.$set(_vm.userFrom, \"sex\", $$v)},expression:\"userFrom.sex\"}},[_c('Option',{attrs:{\"value\":\"1\"}},[_vm._v(\"男\")]),_c('Option',{attrs:{\"value\":\"2\"}},[_vm._v(\"女\")]),_c('Option',{attrs:{\"value\":\"0\"}},[_vm._v(\"未知\")])],1)],1)],1)],1)],1),_c('Col',{attrs:{\"span\":\"18\"}},[_c('Row',[_c('Col',[_c('FormItem',{attrs:{\"label\":\"会员等级：\",\"label-for\":\"level\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"element-id\":\"level\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.userFrom.level),callback:function ($$v) {_vm.$set(_vm.userFrom, \"level\", $$v)},expression:\"userFrom.level\"}},_vm._l((_vm.levelList),function(item,index){return _c('Option',{key:index,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.name))])}),1)],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"付费会员：\",\"label-for\":\"isMember\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.userFrom.isMember),callback:function ($$v) {_vm.$set(_vm.userFrom, \"isMember\", $$v)},expression:\"userFrom.isMember\"}},[_c('Option',{attrs:{\"value\":\"1\"}},[_vm._v(\"是\")]),_c('Option',{attrs:{\"value\":\"0\"}},[_vm._v(\"否\")])],1)],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"身份：\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.userFrom.is_promoter),callback:function ($$v) {_vm.$set(_vm.userFrom, \"is_promoter\", $$v)},expression:\"userFrom.is_promoter\"}},[_c('Option',{attrs:{\"value\":\"1\"}},[_vm._v(\"推广员\")]),_c('Option',{attrs:{\"value\":\"0\"}},[_vm._v(\"普通用户\")])],1)],1)],1)],1)],1),_c('Col',{attrs:{\"span\":\"18\"}},[_c('Row',[_c('Col',{staticClass:\"dateMedia\"},[_c('FormItem',{attrs:{\"label\":\"访问时间：\",\"label-for\":\"user_time\"}},[_c('DatePicker',{staticClass:\"input-add\",attrs:{\"editable\":false,\"value\":_vm.timeVal,\"format\":\"yyyy/MM/dd\",\"type\":\"daterange\",\"placement\":\"bottom-start\",\"placeholder\":\"自定义时间\",\"options\":_vm.options},on:{\"on-change\":_vm.onchangeTime}})],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"访问情况：\",\"label-for\":\"user_time_type\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"element-id\":\"user_time_type\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.userFrom.user_time_type),callback:function ($$v) {_vm.$set(_vm.userFrom, \"user_time_type\", $$v)},expression:\"userFrom.user_time_type\"}},[_c('Option',{attrs:{\"value\":\"visitno\"}},[_vm._v(\"时间段未访问\")]),_c('Option',{attrs:{\"value\":\"visit\"}},[_vm._v(\"时间段访问过\")]),_c('Option',{attrs:{\"value\":\"add_time\"}},[_vm._v(\"首次访问\")])],1)],1)],1),_c('Col',[_c('FormItem',{attrs:{\"label\":\"下单次数：\",\"label-for\":\"pay_count\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"element-id\":\"pay_count\",\"clearable\":\"\"},on:{\"on-change\":_vm.userSearchs},model:{value:(_vm.userFrom.pay_count),callback:function ($$v) {_vm.$set(_vm.userFrom, \"pay_count\", $$v)},expression:\"userFrom.pay_count\"}},[_c('Option',{attrs:{\"value\":\"-1\"}},[_vm._v(\"0次\")]),_c('Option',{attrs:{\"value\":\"0\"}},[_vm._v(\"1次以上\")]),_c('Option',{attrs:{\"value\":\"1\"}},[_vm._v(\"2次以上\")]),_c('Option',{attrs:{\"value\":\"2\"}},[_vm._v(\"3次以上\")]),_c('Option',{attrs:{\"value\":\"3\"}},[_vm._v(\"4次以上\")]),_c('Option',{attrs:{\"value\":\"4\"}},[_vm._v(\"5次以上\")])],1)],1)],1)],1)],1),_c('Col',{attrs:{\"span\":\"18\"}},[_c('Row',[_c('Col',[_c('FormItem',{attrs:{\"label\":\"国家：\",\"label-for\":\"country\"}},[_c('Select',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请选择\",\"element-id\":\"country\",\"clearable\":\"\"},on:{\"on-change\":_vm.changeCountry},model:{value:(_vm.userFrom.country),callback:function ($$v) {_vm.$set(_vm.userFrom, \"country\", $$v)},expression:\"userFrom.country\"}},[_c('Option',{attrs:{\"value\":\"domestic\"}},[_vm._v(\"中国\")]),_c('Option',{attrs:{\"value\":\"abroad\"}},[_vm._v(\"其他\")])],1)],1)],1),(_vm.userFrom.country === 'domestic')?_c('Col',[_c('FormItem',{attrs:{\"label\":\"省份：\"}},[_c('Cascader',{staticClass:\"input-add\",attrs:{\"data\":_vm.addresData,\"change-on-select\":\"\",\"value\":_vm.address},on:{\"on-change\":_vm.handleChange},model:{value:(_vm.address),callback:function ($$v) {_vm.address=$$v},expression:\"address\"}})],1)],1):_vm._e()],1)],1)]:_vm._e(),_c('Col',{staticClass:\"ivu-text-right userFrom\",attrs:{\"span\":\"6\"}},[_c('FormItem',[_c('Button',{staticClass:\"mr15\",attrs:{\"type\":\"primary\",\"label\":\"default\"},on:{\"click\":_vm.userSearchs}},[_vm._v(\"搜索\")]),_c('Button',{staticClass:\"ResetSearch\",on:{\"click\":function($event){return _vm.reset('userFrom')}}},[_vm._v(\"重置\")]),_c('a',{directives:[{name:\"font\",rawName:\"v-font\",value:(14),expression:\"14\"}],staticClass:\"ivu-ml-8\",on:{\"click\":function($event){_vm.collapse = !_vm.collapse}}},[(!_vm.collapse)?[_vm._v(\"\\n                    展开 \"),_c('Icon',{attrs:{\"type\":\"ios-arrow-down\"}})]:[_vm._v(\"\\n                    收起 \"),_c('Icon',{attrs:{\"type\":\"ios-arrow-up\"}})]],2)],1)],1)],2)],1)],1)]),_c('Card',{staticClass:\"ivu-mt listbox\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('div',{staticClass:\"new_tab\"},[_c('Tabs',{on:{\"on-click\":_vm.onClickTab},model:{value:(_vm.headeType),callback:function ($$v) {_vm.headeType=$$v},expression:\"headeType\"}},_vm._l((_vm.headeNum),function(item,index){return _c('TabPane',{key:index,attrs:{\"label\":item.name,\"name\":item.type}})}),1)],1),_c('Row',{attrs:{\"type\":\"flex\",\"justify\":\"space-between\"}},[_c('Col',{attrs:{\"span\":\"24\"}},[_c('Button',{directives:[{name:\"auth\",rawName:\"v-auth\",value:(['admin-user-save']),expression:\"['admin-user-save']\"}],staticClass:\"mr20\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.save}},[_vm._v(\"添加用户\")]),_c('Tooltip',{attrs:{\"content\":\"本页至少选中一项\",\"disabled\":!!_vm.checkUidList.length && _vm.isAll==0}},[_c('Button',{directives:[{name:\"auth\",rawName:\"v-auth\",value:(['admin-user-coupon']),expression:\"['admin-user-coupon']\"}],staticClass:\"mr20\",attrs:{\"type\":\"primary\",\"disabled\":!_vm.checkUidList.length && _vm.isAll==0},on:{\"click\":_vm.onSend}},[_vm._v(\"发送优惠券\")])],1),_c('Tooltip',{attrs:{\"content\":\"本页至少选中一项\",\"disabled\":!!_vm.checkUidList.length && _vm.isAll==0}},[(_vm.userFrom.user_type === 'wechat')?_c('Button',{directives:[{name:\"auth\",rawName:\"v-auth\",value:(['admin-wechat-news']),expression:\"['admin-wechat-news']\"}],staticClass:\"greens mr20\",attrs:{\"size\":\"default\",\"disabled\":!_vm.checkUidList.length && _vm.isAll==0},on:{\"click\":_vm.onSendPic}},[_c('Icon',{attrs:{\"type\":\"md-list\"}}),_vm._v(\"\\n              发送图文消息\\n            \")],1):_vm._e()],1),_c('Tooltip',{attrs:{\"content\":\"本页至少选中一项\",\"disabled\":!!_vm.checkUidList.length && _vm.isAll==0}},[_c('Button',{directives:[{name:\"auth\",rawName:\"v-auth\",value:(['admin-user-set_label']),expression:\"['admin-user-set_label']\"}],staticClass:\"mr20\",attrs:{\"disabled\":!_vm.checkUidList.length && _vm.isAll==0},on:{\"click\":_vm.setBatch}},[_vm._v(\"批量设置\")])],1)],1)],1),_c('vxe-table',{ref:\"xTable\",staticClass:\"mt25\",attrs:{\"loading\":_vm.loading,\"row-id\":\"uid\",\"expand-config\":{accordion: true},\"checkbox-config\":{reserve: true},\"data\":_vm.userLists},on:{\"checkbox-all\":_vm.checkboxAll,\"checkbox-change\":_vm.checkboxItem}},[_c('vxe-column',{attrs:{\"type\":\"\",\"width\":\"0\"}}),_c('vxe-column',{attrs:{\"type\":\"expand\",\"width\":\"35\"},scopedSlots:_vm._u([{key:\"content\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',{staticClass:\"tdinfo\"},[_c('Row',{staticClass:\"expand-row\"},[_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"首次访问：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(\" \"+_vm._s(_vm._f(\"formatDate\")(row.add_time)))])]),_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"近次访问：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(_vm._f(\"formatDate\")(row.last_time)))])]),_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"身份证号：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(row.card_id))])]),_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"真实姓名：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(row.real_name))])])],1),_c('Row',{staticClass:\"expand-row\"},[_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"标签：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(row.labels))])]),_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"生日：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(row.birthday))])]),_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"地址：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(row.addres))])])],1),_c('Row',{staticClass:\"expand-row\"},[_c('Col',{attrs:{\"span\":\"6\"}},[_c('span',{staticClass:\"expand-key\"},[_vm._v(\"备注：\")]),_c('span',{staticClass:\"expand-value\"},[_vm._v(_vm._s(row.mark))])])],1)],1)]}}])}),_c('vxe-column',{attrs:{\"type\":\"checkbox\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"header\",fn:function(){return [_c('div',[_c('Dropdown',{attrs:{\"transfer\":\"\"},on:{\"on-click\":_vm.allPages},scopedSlots:_vm._u([{key:\"list\",fn:function(){return [_c('DropdownMenu',[_c('DropdownItem',{attrs:{\"name\":\"0\"}},[_vm._v(\"当前页\")]),_c('DropdownItem',{attrs:{\"name\":\"1\"}},[_vm._v(\"所有页\")])],1)]},proxy:true}])},[_c('a',{staticClass:\"acea-row row-middle\",attrs:{\"href\":\"javascript:void(0)\"}},[_c('span',[_vm._v(\"全选(\"+_vm._s(_vm.isAll==1?(_vm.total-_vm.checkUidList.length):_vm.checkUidList.length)+\")\")]),_c('Icon',{attrs:{\"type\":\"ios-arrow-down\"}})],1)])],1)]},proxy:true}])}),_c('vxe-column',{attrs:{\"field\":\"uid\",\"title\":\"UID\",\"width\":\"60\"}}),_c('vxe-column',{attrs:{\"field\":\"avatars\",\"title\":\"头像\",\"width\":\"50\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('viewer',[_c('div',{staticClass:\"tabBox_img\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.avatar),expression:\"row.avatar\"}]})])])]}}])}),_c('vxe-column',{attrs:{\"field\":\"nickname\",\"title\":\"昵称\",\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',{staticClass:\"acea-row\"},[_c('Icon',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.sex === '男'),expression:\"row.sex === '男'\"}],staticClass:\"mr5\",attrs:{\"type\":\"md-male\",\"color\":\"#2db7f5\",\"size\":\"15\"}}),_c('Icon',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.sex === '女'),expression:\"row.sex === '女'\"}],staticClass:\"mr5\",attrs:{\"type\":\"md-female\",\"color\":\"#ed4014\",\"size\":\"15\"}}),(row.delete_time != null)?_c('div',{staticStyle:{\"color\":\"#ed4014\"}},[_vm._v(_vm._s(row.nickname)+\" (已注销)\")]):_c('div',{domProps:{\"textContent\":_vm._s(row.nickname)}})],1)]}}])}),_c('vxe-column',{attrs:{\"field\":\"isMember\",\"title\":\"付费会员\",\"min-width\":\"90\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('div',[_vm._v(_vm._s(row.isMember ? \"是\" : \"否\"))])]}}])}),_c('vxe-column',{attrs:{\"field\":\"level\",\"title\":\"用户等级\",\"min-width\":\"90\"}}),_c('vxe-column',{attrs:{\"field\":\"group_id\",\"title\":\"分组\",\"min-width\":\"100\"}}),_c('vxe-column',{attrs:{\"field\":\"phone\",\"title\":\"手机号\",\"min-width\":\"110\"}}),_c('vxe-column',{attrs:{\"field\":\"user_type\",\"title\":\"用户类型\",\"min-width\":\"100\"}}),_c('vxe-column',{attrs:{\"field\":\"spread_uid_nickname\",\"title\":\"推荐人\",\"min-width\":\"100\"}}),_c('vxe-column',{attrs:{\"field\":\"now_money\",\"title\":\"余额\",\"min-width\":\"100\"}}),_c('vxe-column',{attrs:{\"field\":\"action\",\"title\":\"操作\",\"align\":\"center\",\"width\":\"180\",\"fixed\":\"right\"},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [(row.delete_time != null)?_c('span',{staticStyle:{\"color\":\"#c5c8ce\"}},[_vm._v(\"编辑\")]):_c('a',{on:{\"click\":function($event){return _vm.edit(row)}}},[_vm._v(\"编辑\")]),(row.is_extend_info)?_c('Divider',{attrs:{\"type\":\"vertical\"}}):_vm._e(),(row.is_extend_info)?_c('a',{on:{\"click\":function($event){return _vm.extendInfo(row)}}},[_vm._v(\"信息补充\")]):_vm._e(),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":function($event){return _vm.changeMenu(row, '1')}}},[_vm._v(\"详情\")])]}}])})],1),_c('vxe-pager',{staticClass:\"mt20\",attrs:{\"border\":\"\",\"size\":\"medium\",\"page-size\":_vm.userFrom.limit,\"current-page\":_vm.userFrom.page,\"total\":_vm.total,\"layouts\":['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Total']},on:{\"page-change\":_vm.pageChange}})],1),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"选择用户标签\",\"closable\":true,\"width\":\"540\",\"footer-hide\":true,\"mask-closable\":false},model:{value:(_vm.labelListShow),callback:function ($$v) {_vm.labelListShow=$$v},expression:\"labelListShow\"}},[_c('labelList',{ref:\"labelList\",on:{\"activeData\":_vm.activeData,\"close\":_vm.labelListClose}})],1),_c('edit-from',{ref:\"edits\",attrs:{\"FromData\":_vm.FromData,\"userEdit\":1},on:{\"submitFail\":_vm.submitFail}}),_c('send-from',{ref:\"sends\",attrs:{\"is-all\":_vm.isAll,\"where\":_vm.userFrom,\"userIds\":_vm.checkUidList.join(',')}}),_c('user-details',{ref:\"userDetails\",attrs:{\"group-list\":_vm.groupList}}),_c('Modal',{staticClass:\"modelBox\",attrs:{\"scrollable\":\"\",\"title\":\"发送消息\",\"width\":\"1200\",\"height\":\"800\",\"footer-hide\":\"\"},model:{value:(_vm.modal13),callback:function ($$v) {_vm.modal13=$$v},expression:\"modal13\"}},[(_vm.modal13)?_c('news-category',{attrs:{\"isShowSend\":_vm.isShowSend,\"is-all\":_vm.isAll,\"where\":_vm.userFrom,\"userIds\":_vm.checkUidList.join(','),\"scrollerHeight\":_vm.scrollerHeight,\"contentTop\":_vm.contentTop,\"contentWidth\":_vm.contentWidth,\"maxCols\":_vm.maxCols}}):_vm._e()],1),_c('Modal',{staticClass:\"order_box\",attrs:{\"scrollable\":\"\",\"title\":\"修改推广人\",\"closable\":false},model:{value:(_vm.promoterShow),callback:function ($$v) {_vm.promoterShow=$$v},expression:\"promoterShow\"}},[_c('Form',{ref:\"formInline\",attrs:{\"model\":_vm.formInline,\"label-width\":100},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"用户头像：\",\"prop\":\"image\"}},[_c('div',{staticClass:\"picBox\",on:{\"click\":_vm.customer}},[(_vm.formInline.image)?_c('div',{staticClass:\"pictrue\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.formInline.image),expression:\"formInline.image\"}]})]):_c('div',{staticClass:\"upLoad acea-row row-center-wrapper\"},[_c('Icon',{attrs:{\"type\":\"ios-camera-outline\",\"size\":\"26\"}})],1)])])],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.putSend('formInline')}}},[_vm._v(\"提交\")]),_c('Button',{on:{\"click\":function($event){return _vm.cancel('formInline')}}},[_vm._v(\"取消\")])],1)],1),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"请选择商城用户\",\"closable\":false,\"width\":\"900\"},model:{value:(_vm.customerShow),callback:function ($$v) {_vm.customerShow=$$v},expression:\"customerShow\"}},[(_vm.customerShow)?_c('customerInfo',{on:{\"imageObject\":_vm.imageObject}}):_vm._e()],1),_c('Modal',{attrs:{\"scrollable\":\"\",\"title\":\"选择用户标签\",\"closable\":true,\"width\":\"540\",\"footer-hide\":true},model:{value:(_vm.labelShow),callback:function ($$v) {_vm.labelShow=$$v},expression:\"labelShow\"}},[_c('userLabel',{attrs:{\"uid\":_vm.labelActive.uid},on:{\"close\":_vm.labelClose}})],1),_c('Modal',{attrs:{\"title\":\"批量设置\",\"width\":\"750\",\"class-name\":\"batch-modal\"},on:{\"on-visible-change\":_vm.batchVisibleChange},model:{value:(_vm.batchModal),callback:function ($$v) {_vm.batchModal=$$v},expression:\"batchModal\"}},[_c('Alert',{attrs:{\"show-icon\":\"\"}},[_vm._v(\"每次只能修改一项，如需修改多项，请多次操作。\")]),_c('Row',{attrs:{\"type\":\"flex\",\"align\":\"middle\"}},[_c('Col',{attrs:{\"span\":\"4\"}},[_c('Menu',{attrs:{\"active-name\":_vm.menuActive,\"width\":\"auto\"},on:{\"on-select\":_vm.menuSelect}},[_c('MenuItem',{attrs:{\"name\":1}},[_vm._v(\"用户分组\")]),_c('MenuItem',{attrs:{\"name\":2}},[_vm._v(\"用户标签\")]),_c('MenuItem',{attrs:{\"name\":3}},[_vm._v(\"用户等级\")]),_c('MenuItem',{attrs:{\"name\":4}},[_vm._v(\"积分余额\")]),_c('MenuItem',{attrs:{\"name\":5}},[_vm._v(\"赠送会员\")]),_c('MenuItem',{attrs:{\"name\":6}},[_vm._v(\"上级推广人\")])],1)],1),_c('Col',{attrs:{\"span\":\"20\"}},[_c('Form',{attrs:{\"model\":_vm.batchData,\"label-width\":122}},[(_vm.menuActive === 1)?_c('FormItem',{attrs:{\"label\":\"用户分组：\"}},[_c('Select',{model:{value:(_vm.batchData.group_id),callback:function ($$v) {_vm.$set(_vm.batchData, \"group_id\", $$v)},expression:\"batchData.group_id\"}},_vm._l((_vm.groupList),function(item){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.group_name))])}),1)],1):_vm._e(),(_vm.menuActive === 2)?_c('FormItem',{attrs:{\"label\":\"用户标签：\"}},[_c('div',{staticClass:\"select-tag\",on:{\"click\":_vm.openLabelList}},[(_vm.batchLabel.length)?_c('div',_vm._l((_vm.batchLabel),function(item){return _c('Tag',{key:item.id,attrs:{\"closable\":\"\"},on:{\"on-close\":function($event){return _vm.tagClose(item.id)}}},[_vm._v(_vm._s(item.label_name))])}),1):_c('span',{staticClass:\"placeholder\"},[_vm._v(\"请选择\")]),_c('Icon',{attrs:{\"type\":\"ios-arrow-down\"}})],1)]):_vm._e(),(_vm.menuActive === 3)?_c('FormItem',{attrs:{\"label\":\"用户等级：\"}},[_c('Select',{model:{value:(_vm.batchData.level_id),callback:function ($$v) {_vm.$set(_vm.batchData, \"level_id\", $$v)},expression:\"batchData.level_id\"}},_vm._l((_vm.levelList),function(item){return _c('Option',{key:item.id,attrs:{\"value\":item.id}},[_vm._v(_vm._s(item.name))])}),1)],1):_vm._e(),(_vm.menuActive === 4)?_c('FormItem',{attrs:{\"label\":\"修改余额：\"}},[_c('RadioGroup',{model:{value:(_vm.batchData.money_status),callback:function ($$v) {_vm.$set(_vm.batchData, \"money_status\", $$v)},expression:\"batchData.money_status\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"增加\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"减少\")])],1)],1):_vm._e(),(_vm.menuActive === 4)?_c('FormItem',{attrs:{\"label\":\"余额：\"}},[_c('InputNumber',{attrs:{\"min\":0,\"max\":999999},model:{value:(_vm.batchData.money),callback:function ($$v) {_vm.$set(_vm.batchData, \"money\", $$v)},expression:\"batchData.money\"}})],1):_vm._e(),(_vm.menuActive === 4)?_c('FormItem',{attrs:{\"label\":\"修改积分：\"}},[_c('RadioGroup',{model:{value:(_vm.batchData.integration_status),callback:function ($$v) {_vm.$set(_vm.batchData, \"integration_status\", $$v)},expression:\"batchData.integration_status\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"增加\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"减少\")])],1)],1):_vm._e(),(_vm.menuActive === 4)?_c('FormItem',{attrs:{\"label\":\"积分：\"}},[_c('InputNumber',{attrs:{\"min\":0,\"max\":999999},model:{value:(_vm.batchData.integration),callback:function ($$v) {_vm.$set(_vm.batchData, \"integration\", $$v)},expression:\"batchData.integration\"}})],1):_vm._e(),(_vm.menuActive === 5)?_c('FormItem',{attrs:{\"label\":\"修改时长：\"}},[_c('RadioGroup',{model:{value:(_vm.batchData.days_status),callback:function ($$v) {_vm.$set(_vm.batchData, \"days_status\", $$v)},expression:\"batchData.days_status\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"增加\")]),_c('Radio',{attrs:{\"label\":2}},[_vm._v(\"减少\")])],1)],1):_vm._e(),(_vm.menuActive === 5)?_c('FormItem',{attrs:{\"label\":\"修改时长(天)：\"}},[_c('InputNumber',{attrs:{\"min\":0,\"max\":999999},model:{value:(_vm.batchData.day),callback:function ($$v) {_vm.$set(_vm.batchData, \"day\", $$v)},expression:\"batchData.day\"}})],1):_vm._e(),(_vm.menuActive === 6)?_c('FormItem',{attrs:{\"label\":\"上级推广员：\"}},[_c('Input',{attrs:{\"value\":_vm.spread_name,\"placeholder\":\"请选择\",\"icon\":\"ios-arrow-down\"},on:{\"on-click\":_vm.customer,\"on-focus\":_vm.customer}})],1):_vm._e()],1)],1)],1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{on:{\"click\":_vm.cancelBatch}},[_vm._v(\"取消\")]),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.saveBatch}},[_vm._v(\"保存\")])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}