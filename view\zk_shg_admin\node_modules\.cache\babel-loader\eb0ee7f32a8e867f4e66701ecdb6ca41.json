{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_goods_list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_goods_list.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getCategory as _getCategory, getProduct } from '@/api/diy';\nimport toolCom from '@/components/mobileConfigRight/index.js';\nimport { mapState, mapMutations, mapActions } from 'vuex';\nimport rightBtn from '@/components/rightBtn/index.vue';\nexport default {\n  name: 'c_home_goods_list',\n  componentsName: 'home_goods_list',\n  cname: '��Ʒ�б�',\n  props: {\n    activeIndex: {\n      type: null\n    },\n    num: {\n      type: null\n    },\n    index: {\n      type: null\n    }\n  },\n  components: _objectSpread({}, toolCom, {\n    rightBtn: rightBtn\n  }),\n  data: function data() {\n    return {\n      configObj: {},\n      rCom: [{\n        components: toolCom.c_set_up,\n        configNme: 'setUp'\n      }],\n      oneContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleLeft'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'styleConfig'\n      }, {\n        components: toolCom.c_title,\n        configNme: 'titleGoods'\n      }, {\n        components: toolCom.c_select,\n        configNme: 'typeConfig'\n      }],\n      oneContent1: [{\n        components: toolCom.c_goods,\n        configNme: 'goodsList'\n      }],\n      oneContent2: [{\n        components: toolCom.c_brand,\n        configNme: 'brandList'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'numberConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'goodsSort'\n      }],\n      oneContent3: [{\n        components: toolCom.c_classify,\n        configNme: 'classList'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'numberConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'goodsSort'\n      }],\n      oneContent4: [{\n        components: toolCom.c_goods_label,\n        configNme: 'goodsLabel'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'numberConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'goodsSort'\n      }],\n      twoContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleContents'\n      }, {\n        components: toolCom.c_checkbox,\n        configNme: 'checkboxInfo'\n      }],\n      threeContent: [{\n        components: toolCom.c_title,\n        configNme: 'titleCart'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'cartConfig'\n      }],\n      threeContent1: [{\n        components: toolCom.c_button_img,\n        configNme: 'bntStyleConfig'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'bntConfig'\n      }],\n      oneStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleRight'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'filletImg'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'goodsName'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneConfig'\n      }],\n      oneStyle1: [{\n        components: toolCom.c_bg_color,\n        configNme: 'goodsNameColor'\n      }, {\n        components: toolCom.c_bg_color,\n        configNme: 'goodsPriceColor'\n      }],\n      oneStyle2: [{\n        components: toolCom.c_bg_color,\n        configNme: 'soldNumColor'\n      }],\n      oneStyle3: [{\n        components: toolCom.c_bg_color,\n        configNme: 'scoreColor'\n      }],\n      twoStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCart'\n      }, {\n        components: toolCom.c_radio,\n        configNme: 'toneCartConfig'\n      }],\n      twoStyle1: [{\n        components: toolCom.c_bg_color,\n        configNme: 'bntBgColor'\n      }],\n      currencyTitleStyle: [{\n        components: toolCom.c_title,\n        configNme: 'titleCurrency'\n      }],\n      moduleColorStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'moduleColor'\n      }],\n      currencyStyle: [{\n        components: toolCom.c_bg_color,\n        configNme: 'bottomBgColor'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'topConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'bottomConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'prConfig'\n      }, {\n        components: toolCom.c_slider,\n        configNme: 'mbConfig'\n      }, {\n        components: toolCom.c_fillet,\n        configNme: 'fillet'\n      }],\n      setUp: 0,\n      type: 0,\n      type2: 0,\n      type3: 0,\n      type4: 0,\n      type5: 0,\n      lockStatus: false\n    };\n  },\n  watch: {\n    num: function num(nVal) {\n      var value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]));\n      this.configObj = value;\n\n      if (!value.selectConfig.list || !value.selectConfig.list[0].value) {\n        this.getCategory();\n      }\n    },\n    configObj: {\n      handler: function handler(nVal, oVal) {\n        this.$store.commit('admin/mobildConfig/UPDATEARR', {\n          num: this.num,\n          val: nVal\n        });\n      },\n      deep: true\n    },\n    'configObj.setUp.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.setUp = nVal;\n        var arr = [this.rCom[0]];\n\n        if (nVal == 0) {\n          this.getRComContent(arr, this.type, this.type2, this.type3);\n        } else {\n          this.getRComStyle(arr, this.type, this.type3, this.type4, this.type5);\n        }\n      },\n      deep: true\n    },\n    'configObj.styleConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp === 0) {\n          this.getRComContent(arr, nVal, this.type2, this.type3);\n        } else {\n          this.getRComStyle(arr, nVal, this.type3, this.type4, this.type5);\n        }\n      },\n      deep: true\n    },\n    'configObj.typeConfig.activeValue': {\n      handler: function handler(nVal, oVal) {\n        this.type2 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp === 0) {\n          this.getRComContent(arr, this.type, nVal, this.type3);\n        }\n      },\n      deep: true\n    },\n    'configObj.cartConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type3 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp === 0) {\n          this.getRComContent(arr, this.type, this.type2, nVal);\n        } else {\n          this.getRComStyle(arr, this.type, nVal, this.type4, this.type5);\n        }\n      },\n      deep: true\n    },\n    'configObj.toneConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type4 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          this.getRComStyle(arr, this.type, this.type3, nVal, this.type5);\n        }\n      },\n      deep: true\n    },\n    'configObj.toneCartConfig.tabVal': {\n      handler: function handler(nVal, oVal) {\n        this.type5 = nVal;\n        var arr = [this.rCom[0]];\n\n        if (this.setUp) {\n          this.getRComStyle(arr, this.type, this.type3, this.type4, nVal);\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    this.$nextTick(function () {\n      var value = JSON.parse(JSON.stringify(_this.$store.state.admin.mobildConfig.defaultArray[_this.num]));\n      _this.configObj = value;\n\n      _this.getCategory();\n    });\n  },\n  methods: {\n    getRComContent: function getRComContent(arr, type, type2, type3) {\n      if (type == 3) {\n        if (type2 == 1) {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent1), _toConsumableArray(this.twoContent));\n        } else if (type2 == 2) {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent2), _toConsumableArray(this.twoContent));\n        } else if (type2 == 3) {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent3), _toConsumableArray(this.twoContent));\n        } else {\n          this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent4), _toConsumableArray(this.twoContent));\n        }\n      } else {\n        if (type2 == 1) {\n          if (type3 == 0) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent1), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent), _toConsumableArray(this.threeContent1));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent1), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent));\n          }\n        } else if (type2 == 2) {\n          if (type3 == 0) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent2), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent), _toConsumableArray(this.threeContent1));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent2), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent));\n          }\n        } else if (type2 == 3) {\n          if (type3 == 0) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent3), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent), _toConsumableArray(this.threeContent1));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent3), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent));\n          }\n        } else {\n          if (type3 == 0) {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent4), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent), _toConsumableArray(this.threeContent1));\n          } else {\n            this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneContent), _toConsumableArray(this.oneContent4), _toConsumableArray(this.twoContent), _toConsumableArray(this.threeContent));\n          }\n        }\n      }\n    },\n    getRComStyle: function getRComStyle(arr, type, type3, type4, type5) {\n      var obj4 = [],\n          currencyStyle = [];\n\n      if (type4) {\n        if (type == 1 || type == 4) {\n          obj4 = [].concat(_toConsumableArray(this.oneStyle1), _toConsumableArray(this.oneStyle2));\n          currencyStyle = [].concat(_toConsumableArray(this.currencyTitleStyle), _toConsumableArray(this.currencyStyle));\n        } else if (type == 0) {\n          obj4 = [].concat(_toConsumableArray(this.oneStyle1), _toConsumableArray(this.oneStyle2), _toConsumableArray(this.oneStyle3));\n          currencyStyle = [].concat(_toConsumableArray(this.currencyTitleStyle), _toConsumableArray(this.currencyStyle));\n        } else if (type == 2 || type == 3) {\n          obj4 = _toConsumableArray(this.oneStyle1);\n          currencyStyle = [].concat(_toConsumableArray(this.currencyTitleStyle), _toConsumableArray(this.moduleColorStyle), _toConsumableArray(this.currencyStyle));\n        } else {\n          obj4 = [].concat(_toConsumableArray(this.oneStyle1), _toConsumableArray(this.oneStyle2));\n          currencyStyle = [].concat(_toConsumableArray(this.currencyTitleStyle), _toConsumableArray(this.moduleColorStyle), _toConsumableArray(this.currencyStyle));\n        }\n      } else {\n        if (type == 0 || type == 1 || type == 4) {\n          currencyStyle = [].concat(_toConsumableArray(this.currencyTitleStyle), _toConsumableArray(this.currencyStyle));\n        } else {\n          currencyStyle = [].concat(_toConsumableArray(this.currencyTitleStyle), _toConsumableArray(this.moduleColorStyle), _toConsumableArray(this.currencyStyle));\n        }\n      }\n\n      var obj5 = [];\n\n      if (type != 3) {\n        if (type5) {\n          obj5 = [].concat(_toConsumableArray(this.twoStyle), _toConsumableArray(this.twoStyle1));\n        } else {\n          obj5 = _toConsumableArray(this.twoStyle);\n        }\n      }\n\n      if (type3 == 0) {\n        this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(obj4), _toConsumableArray(obj5), _toConsumableArray(currencyStyle));\n      } else {\n        this.rCom = [].concat(_toConsumableArray(arr), _toConsumableArray(this.oneStyle), _toConsumableArray(obj4), _toConsumableArray(currencyStyle));\n      }\n    },\n    getConfig: function getConfig(data, name) {\n      var _this2 = this;\n\n      if (name != 'radio' && !data.name && data == 1) {\n        this.configObj.goodsList.list = [];\n        return;\n      }\n\n      if (name != 'radio' && !data.name && data == 0 && !this.configObj.classList.classVal.length) {\n        this.configObj.goodsList.list = [];\n        return;\n      } // if( data.name=='radio'){\n      //     return;\n      // }\n\n\n      var type = this.configObj.typeConfig.activeValue;\n      var dataObj = {\n        page: 1,\n        limit: this.configObj.numberConfig.val,\n        priceOrder: this.configObj.goodsSort.tabVal == 2 ? 'desc' : '',\n        salesOrder: this.configObj.goodsSort.tabVal == 1 ? 'desc' : ''\n      };\n\n      if (type == 1) {\n        this.configObj.productList.list = [];\n        return;\n      } else if (type == 2) {\n        dataObj.brand_id = this.configObj.brandList.brandVal;\n      } else if (type == 3) {\n        dataObj.id = this.configObj.classList.classVal;\n      } else {\n        dataObj.store_label_id = this.configObj.goodsLabel.activeValue;\n      }\n\n      getProduct(dataObj).then(function (res) {\n        _this2.configObj.productList.list = res.data;\n      });\n    },\n    getCategory: function getCategory() {\n      var _this3 = this;\n\n      _getCategory().then(function (res) {\n        _this3.$set(_this3.configObj.selectConfig, 'list', res.data);\n      });\n    }\n  }\n};", null]}