{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_radio.vue?vue&type=template&id=b9b85b00&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_radio.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"c_radio\" :class=\"configData.type=='form'?'mb15':'on mb5'\" v-if=\"configData\">\n    <div class=\"c_row-item\">\n        <Col class=\"c_label\" :class=\"configData.type=='form'?'on':''\" span=\"4\">\n            {{configData.title}}\n        </Col>\n        <Col class=\"color-box\" :span=\"configData.type=='form'?'19':'18'\">\n            <RadioGroup v-model=\"configData.tabVal\" @on-change=\"radioChange($event)\">\n                <Radio :label=\"key\" v-for=\"(radio,key) in configData.tabList\" :key=\"key\">\n                    <span>{{radio.name}}</span>\n                </Radio>\n            </RadioGroup>\n        </Col>\n    </div>\n</div>\n\n", null]}