{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\goodsList\\index.vue", "mtime": 1715745423000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport { cascaderListApi, changeListApi, allLabelApi } from \"@/api/product\";\nimport { liveGoods } from \"@/api/live\";\nexport default {\n  name: \"index\",\n  props: {\n\tgoodsType: {\n\t  type: Number,\n\t  default: 0,\n\t  },\n    storeType: {\n      type: Number,\n      default: 0,\n    },\n    is_new: {\n      type: String,\n      default: \"\",\n    },\n    diy: {\n      type: <PERSON><PERSON><PERSON>,\n      default: false,\n    },\n    isdiy: {\n      type: Boolean,\n      default: false,\n    },\n    ischeckbox: {\n      type: Boolean,\n      default: false,\n    },\n    liveStatus: {\n      type: Boolean,\n      default: false,\n    },\n    isLive: {\n      type: Boolean,\n      default: false,\n    },\n    datas: {\n      type: Object,\n      default: function () {\n        return {};\n      },\n    },\n  },\n  data() {\n    return {\n      //选中商品集合\n      selectEquips:[],\n      // 选中的id集合\n      selectEquipsIds: [],\n      labelSelect:[],\n      cateIds:[],\n      modal_loading: false,\n      treeSelect: [],\n      formValidate: {\n        page: 1,\n        limit: 10,\n        cate_id: \"\",\n        store_name: \"\",\n        is_new: this.is_new,\n\t\tstore_label_id: \"\",\n\t\tis_integral: 1,\n      },\n      total: 0,\n      modals: false,\n      loading: false,\n      grid: {\n        xl: 10,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      tableList: [],\n      currentid: 0,\n      productRow: {},\n      columns4: [\n        {\n          title: \"商品ID\",\n          key: \"id\",\n        },\n        {\n          title: \"图片\",\n          slot: \"image\",\n          width:60,\n        },\n        {\n          title: \"商品名称\",\n          slot: \"store_name\",\n          minWidth: 200,\n        },\n\t\t{\n\t\t  title: \"商品类型\",\n\t\t  slot: \"product_type\",\n\t\t  minWidth: 100,\n\t\t},\n        {\n          title: \"商品分类\",\n          key: \"cate_name\",\n          minWidth: 150,\n        },\n      ],\n      columns5: [\n        {\n          title: \"商品ID\",\n          key: \"id\",\n        },\n        {\n          title: \"图片\",\n          slot: \"image\",\n        },\n        {\n          title: \"商品名称\",\n          key: \"name\",\n          minWidth: 250,\n        },\n      ],\n      images: [],\n      many: \"\",\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 120;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    let radio = {\n      title: \"选择\",\n      width: 70,\n      align: \"center\",\n      render: (h, params) => {\n        let id = params.row.id;\n        let flag = false;\n        if (this.currentid === id) {\n          flag = true;\n        } else {\n          flag = false;\n        }\n        let self = this;\n        return h(\"div\", [\n          h(\"Radio\", {\n            props: {\n              value: flag,\n            },\n            on: {\n              \"on-change\": () => {\n                self.currentid = id;\n                this.productRow = params.row;\n                this.$emit(\"getProductId\", this.productRow);\n                if (this.productRow.id) {\n                  if (this.$route.query.fodder === \"image\") {\n                    /* eslint-disable */\n                    let imageObject = {\n                      image: this.productRow.image,\n                      product_id: this.productRow.id,\n                      name: this.productRow.name,\n                    };\n                    form_create_helper.set(\"image\", imageObject);\n                    form_create_helper.close(\"image\");\n                  }\n                } else {\n                  this.$Message.warning(\"请先选择商品\");\n                }\n              },\n            },\n          }),\n        ]);\n      },\n    };\n\n    let checkbox = {\n      type: \"selection\",\n      width: 60,\n      align: \"center\",\n    };\n    let many = \"\";\n    if (this.ischeckbox) {\n      many = \"many\";\n    } else {\n      many = this.$route.query.type;\n    }\n    this.many = many;\n    if (many === \"many\") {\n      this.columns4.unshift(checkbox);\n      this.columns5.unshift(checkbox);\n    } else {\n      this.columns4.unshift(radio);\n      this.columns5.unshift(radio);\n    }\n  },\n  mounted() {\n    this.goodsCategory();\n    this.getList();\n\t\tthis.getAllLabelApi();\n  },\n  methods: {\n\t\t// 判断是否选中\n\t\tsortData() {\n\t\t\tif (this.selectEquipsIds.length) {\n\t\t\t\tthis.tableList.forEach(ele => {\n\t\t\t\t\tif (this.selectEquipsIds.includes(ele.id)) ele._checked = true;\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t// 选中一行\n\t\tTableSelectRow(selection, row) {\n\t\t\tif (!this.selectEquipsIds.includes(row.id)) {\n\t\t\t\tthis.selectEquipsIds.push(row.id);\n\t\t\t\tthis.selectEquips.push(row);\n\t\t\t}\n\t\t},\n\t\t// 取消选中一行\n\t\tTableSelectCancelRow(selection, row) {\n\t\t\tvar _index = this.selectEquipsIds.indexOf(row.id);\n\t\t\tif (_index != -1) {\n\t\t\t\tthis.selectEquipsIds.splice(_index, 1);\n\t\t\t\tthis.selectEquips.splice(_index, 1);\n\t\t\t}\n\t\t},\n\t\t// 选中所有\n\t\tselectAll() {\n\t\t\tfor (let i = this.tableList.length - 1; i >= 0; i--) {\n\t\t\t\tthis.TableSelectRow(null, this.tableList[i]);\n\t\t\t}\n\t\t},\n\t\t// 取消选中所有\n\t\tcancelAll() {\n\t\t\tfor (let i = this.tableList.length - 1; i >= 0; i--) {\n\t\t\t\tthis.TableSelectCancelRow(null, this.tableList[i]);\n\t\t\t}\n\t\t},\n\t\tgetAllLabelApi () {\n\t\t\tallLabelApi().then(res=>{\n\t\t\t\tthis.labelSelect = res.data\n\t\t\t}).catch(err=>{\n\t\t\t\tthis.$Message.error(err.msg);\n\t\t\t})\n\t\t},\n\t\thandleSelectAll () {\n\t\t  this.$refs.table.selectAll(false);\n\t\t},\n    // 商品分类；\n    goodsCategory() {\n      cascaderListApi(1)\n        .then((res) => {\n          this.treeSelect = res.data;\n        })\n        .catch((res) => {\n          this.$Message.error(res.msg);\n        });\n    },\n    pageChange(index) {\n      this.formValidate.page = index;\n      this.getList();\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      if (!this.liveStatus) {\n        if (this.isLive) {\n          this.formValidate.is_live = 1;\n        }\n\t\tif(this.goodsType){\n\t\t\tthis.formValidate.is_presale_product = 0;\n\t\t\tthis.formValidate.is_vip_product = 0;\n\t\t}\n    if(this.storeType){\n      this.formValidate.is_supplier = 0;\n    }\n\t\tthis.formValidate.cate_id = this.cateIds[this.cateIds.length-1]\n        changeListApi(this.formValidate)\n          .then(async (res) => {\n            let data = res.data;\n            this.tableList = data.list;\n            this.total = res.data.count;\n\t\t\tthis.sortData();\n            this.loading = false;\n          })\n          .catch((res) => {\n            this.loading = false;\n            this.$Message.error(res.msg);\n          });\n      } else {\n        liveGoods({\n          is_show: \"1\",\n          status: \"1\",\n          live_id: this.datas.id,\n          kerword: this.formValidate.store_name,\n          page: this.formValidate.page,\n          limit: this.formValidate.limit,\n        })\n          .then(async (res) => {\n            let data = res.data;\n            data.list.forEach((el) => {\n              el.image = el.cover_img;\n            });\n            this.tableList = data.list;\n            this.total = res.data.count;\n\t\t\tthis.sortData();\n            this.loading = false;\n          })\n          .catch((res) => {\n            this.loading = false;\n            this.$Message.error(res.msg);\n          });\n      }\n    },\n\t\tchangeCheckbox(selection) {\n\t\t  let images = [];\n\t\t  selection.forEach(function (item) {\n\t\t    let imageObject = {\n\t\t      image: item.image,\n\t\t      product_id: item.id,\n\t\t      store_name: item.store_name,\n\t\t      temp_id: item.temp_id\n\t\t    };\n\t\t    images.push(imageObject);\n\t\t  });\n\t\t  this.images = images;\n\t\t  this.$emit(\"getProductDiy\", selection);\n\t\t},\n    ok() {\n\t\t\tlet images = [];\n\t\t\tthis.selectEquips.forEach(function (item) {\n\t\t\t  let imageObject = {\n\t\t\t    image: item.image,\n\t\t\t    product_id: item.id,\n\t\t\t    store_name: item.store_name,\n\t\t\t    temp_id: item.temp_id\n\t\t\t  };\n\t\t\t  images.push(imageObject);\n\t\t\t});\n      if (images.length > 0) {\n        if (this.$route.query.fodder === \"image\") {\n          let imageValue = form_create_helper.get(\"image\");\n          form_create_helper.set(\"image\", imageValue.concat(images));\n          form_create_helper.close(\"image\");\n        } else {\n          if(this.isdiy){\n            this.$emit(\"getProductId\", this.selectEquips);\n          }else {\n            this.$emit(\"getProductId\", images);\n          }\n        }\n      } else {\n        this.$Message.warning(\"请先选择商品\");\n      }\n    },\n\t\ttreeSearchs(value){\n\t\t\tthis.cateIds = value;\n\t\t\tthis.formValidate.page = 1;\n\t\t\tthis.getList();\n\t\t},\n\t\t// 表格搜索\n    userSearchs() {\n      this.formValidate.page = 1;\n      this.getList();\n    },\n    clear() {\n      this.productRow.id = \"\";\n      this.currentid = \"\";\n    },\n  },\n};\n", null]}