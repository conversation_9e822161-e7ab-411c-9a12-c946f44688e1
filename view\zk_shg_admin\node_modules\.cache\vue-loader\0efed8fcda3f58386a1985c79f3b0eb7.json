{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods.vue?vue&type=template&id=53491fa8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"goods-box\" v-if=\"defaults.goodsList\">\n\t\t<div class=\"acea-row\">\n\t\t\t<div class=\"title\">选择商品</div>\n\t\t\t<div class=\"wrapper\">\n\t\t\t    <draggable\n\t\t\t            class=\"dragArea list-group\"\n\t\t\t            :list=\"defaults.goodsList.list\"\n\t\t\t            group=\"peoples\"\n\t\t\t    >\n\t\t\t    <div class=\"item\" v-for=\"(goods,index) in defaults.goodsList.list\" :key=\"index\" v-if=\"defaults.goodsList.list.length\">\n\t\t\t        <img :src=\"goods.image\" alt=\"\">\n\t\t\t        <span class=\"iconfont-diy icondel_1\" @click.stop=\"bindDelete(index)\"></span>\n\t\t\t    </div>\n\t\t\t        <div class=\"add-item item\" @click=\"modals = true\"><span class=\"iconfont-diy iconaddto\"></span></div>\n\t\t\t    </draggable>\n\t\t\t</div>\n\t\t</div>\n\n        <Modal v-model=\"modals\" title=\"商品列表\" footerHide  class=\"paymentFooter\" scrollable width=\"900\" @on-cancel=\"cancel\">\n            <goods-list ref=\"goodslist\" :ischeckbox=\"true\" :isdiy=\"true\"  @getProductId=\"getProductId\" v-if=\"modals\"></goods-list>\n        </Modal>\n    </div>\n", null]}