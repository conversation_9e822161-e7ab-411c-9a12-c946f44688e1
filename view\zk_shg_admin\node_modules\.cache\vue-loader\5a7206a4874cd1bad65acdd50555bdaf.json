{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue?vue&type=template&id=045af22c&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\freightTemplate\\city.vue", "mtime": 1658973958000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Modal',{staticClass:\"modal\",attrs:{\"title\":\"选择可配送区域\",\"width\":\"50%\",\"mask\":true},model:{value:(_vm.addressModal),callback:function ($$v) {_vm.addressModal=$$v},expression:\"addressModal\"}},[_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\"}},[_c('Col',{staticClass:\"item\",attrs:{\"xl\":24,\"lg\":24,\"md\":24,\"sm\":24,\"xs\":24}},[_c('div',{staticClass:\"acea-row row-right row-middle\"},[_c('Checkbox',{on:{\"on-change\":_vm.allCheckbox},model:{value:(_vm.iSselect),callback:function ($$v) {_vm.iSselect=$$v},expression:\"iSselect\"}},[_vm._v(\"全选\")]),_c('div',{staticClass:\"empty\",on:{\"click\":_vm.empty}},[_vm._v(\"清空\")])],1)])],1),_c('Row',{attrs:{\"gutter\":24,\"type\":\"flex\",\"loading\":_vm.loading}},_vm._l((_vm.cityList),function(item,index){return (item.isShow)?_c('Col',{key:index,staticClass:\"item\",attrs:{\"xl\":6,\"lg\":6,\"md\":6,\"sm\":8,\"xs\":6}},[_c('div',{on:{\"mouseenter\":function($event){return _vm.enter(index)},\"mouseleave\":function($event){return _vm.leave()}}},[_c('Checkbox',{attrs:{\"label\":item.name},on:{\"on-change\":function($event){return _vm.checkedClick(index)}},model:{value:(item.checked),callback:function ($$v) {_vm.$set(item, \"checked\", $$v)},expression:\"item.checked\"}},[_vm._v(_vm._s(item.name))]),_c('span',{staticClass:\"red\"},[_vm._v(\"(\"+_vm._s((item.count || 0) + '/' + item.childNum)+\")\")]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activeCity===index),expression:\"activeCity===index\"}],staticClass:\"city\"},[_c('div',{staticClass:\"checkBox\"},[_c('div',{staticClass:\"arrow\"}),_c('div',_vm._l((item.children),function(city,indexn){return _c('Checkbox',{directives:[{name:\"show\",rawName:\"v-show\",value:(city.isShow),expression:\"city.isShow\"}],key:indexn,staticClass:\"itemn\",attrs:{\"label\":city.name},on:{\"on-change\":function($event){return _vm.primary(index,indexn)}},model:{value:(city.checked),callback:function ($$v) {_vm.$set(city, \"checked\", $$v)},expression:\"city.checked\"}},[_vm._v(_vm._s(city.name))])}),1)])])],1)]):_vm._e()}),1),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('Button',{on:{\"click\":_vm.close}},[_vm._v(\"取消\")]),_c('Button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.confirm}},[_vm._v(\"确定\")])],1),(_vm.loading)?_c('Spin',{attrs:{\"size\":\"large\",\"fix\":\"\"}}):_vm._e()],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}