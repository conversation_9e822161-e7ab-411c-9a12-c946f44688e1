{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1751017011594}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nvar _methods;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport request from '@/plugins/request';\nexport default {\n  name: 'TransferManagement',\n  data: function data() {\n    return {\n      // 筛选条件\n      filterForm: {\n        order_no: '',\n        from_store_id: '',\n        to_store_id: '',\n        status: '',\n        date_range: []\n      },\n      // 列表数据\n      transferList: [],\n      storeList: [],\n      selectedRows: [],\n      // 分页\n      currentPage: 1,\n      pageSize: 20,\n      total: 0,\n      tableLoading: false,\n      // 统计数据\n      statistics: {\n        total_orders: 0,\n        pending_orders: 0,\n        approved_orders: 0,\n        completed_orders: 0\n      },\n      // 详情对话框\n      detailDialogVisible: false,\n      currentDetail: null,\n      // 审核对话框\n      approveDialogVisible: false,\n      approveLoading: false,\n      approveType: true,\n      // true: 通过, false: 拒绝\n      approveForm: {\n        id: '',\n        remark: ''\n      },\n      approveRules: {\n        remark: [{\n          required: false,\n          message: '请填写审核备注',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  methods: (_methods = {\n    // 初始化数据\n    initData: function () {\n      var _initData = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _context.next = 2;\n                return this.getStoreList();\n\n              case 2:\n                _context.next = 4;\n                return this.getTransferList();\n\n              case 4:\n                _context.next = 6;\n                return this.getStatistics();\n\n              case 6:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function initData() {\n        return _initData.apply(this, arguments);\n      }\n\n      return initData;\n    }(),\n    // 获取门店列表\n    getStoreList: function () {\n      var _getStoreList = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return request.get('/erp/transfer/store_options');\n\n              case 3:\n                res = _context2.sent;\n                console.log('门店列表API响应:', res);\n\n                if (res && res.status === 200 && res.data) {\n                  this.storeList = res.data || [];\n                  console.log('门店列表数据:', this.storeList);\n                } else {\n                  console.error('门店列表API响应格式错误:', res);\n                  this.storeList = [];\n                }\n\n                _context2.next = 12;\n                break;\n\n              case 8:\n                _context2.prev = 8;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('获取门店列表失败:', _context2.t0);\n                this.storeList = []; // 不显示错误消息，因为门店列表不是必需的\n\n              case 12:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 8]]);\n      }));\n\n      function getStoreList() {\n        return _getStoreList.apply(this, arguments);\n      }\n\n      return getStoreList;\n    }(),\n    // 获取调拨单列表\n    getTransferList: function () {\n      var _getTransferList = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee3() {\n        var params, res, data;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.tableLoading = true;\n                _context3.prev = 1;\n                params = {\n                  page: this.currentPage,\n                  limit: this.pageSize,\n                  order_no: this.filterForm.order_no,\n                  from_store_id: this.filterForm.from_store_id,\n                  to_store_id: this.filterForm.to_store_id,\n                  status: this.filterForm.status\n                }; // 处理日期范围\n\n                if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {\n                  params.start_time = this.formatDate(this.filterForm.date_range[0]);\n                  params.end_time = this.formatDate(this.filterForm.date_range[1]);\n                }\n\n                _context3.next = 6;\n                return request.get('/erp/transfer/list', {\n                  params: params\n                });\n\n              case 6:\n                res = _context3.sent;\n                console.log('调拨列表API响应:', res);\n\n                if (res && res.status === 200 && res.data) {\n                  data = res.data;\n                  console.log('调拨列表数据:', data);\n                  this.transferList = data.list || [];\n                  this.total = data.count || 0;\n                  console.log('设置列表数据:', this.transferList, '总数:', this.total); // 暂时跳过数据处理，直接使用原始数据\n\n                  console.log('跳过数据处理，直接使用原始数据');\n                  console.log('最终列表数据:', this.transferList);\n                  console.log('最终总数:', this.total);\n                } else {\n                  this.$Message.error(res.data.msg || '获取调拨单列表失败');\n                }\n\n                _context3.next = 15;\n                break;\n\n              case 11:\n                _context3.prev = 11;\n                _context3.t0 = _context3[\"catch\"](1);\n                console.error('获取调拨单列表失败:', _context3.t0);\n                this.$Message.error('获取调拨单列表失败');\n\n              case 15:\n                _context3.prev = 15;\n                this.tableLoading = false;\n                return _context3.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[1, 11, 15, 18]]);\n      }));\n\n      function getTransferList() {\n        return _getTransferList.apply(this, arguments);\n      }\n\n      return getTransferList;\n    }(),\n    // 获取统计数据\n    getStatistics: function () {\n      var _getStatistics = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee4() {\n        var res, stats;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                _context4.next = 3;\n                return request.get('/erp/transfer/statistics');\n\n              case 3:\n                res = _context4.sent;\n                console.log('统计数据API响应:', res);\n\n                if (res && res.status === 200 && res.data) {\n                  stats = res.data;\n                  console.log('统计数据:', stats); // 直接赋值更新统计数据\n\n                  this.statistics.total_orders = stats.total || 0;\n                  this.statistics.pending_orders = stats.pending || 0;\n                  this.statistics.approved_orders = stats.approved || 0;\n                  this.statistics.completed_orders = stats.executed || 0;\n                  console.log('处理后的统计数据:', this.statistics);\n                } else {\n                  console.error('统计数据API响应格式错误:', res);\n                }\n\n                _context4.next = 11;\n                break;\n\n              case 8:\n                _context4.prev = 8;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('获取统计数据失败:', _context4.t0);\n\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 8]]);\n      }));\n\n      function getStatistics() {\n        return _getStatistics.apply(this, arguments);\n      }\n\n      return getStatistics;\n    }(),\n    // 格式化日期\n    formatDate: function formatDate(date) {\n      if (!date) return '';\n      var d = new Date(date);\n      var year = d.getFullYear();\n      var month = String(d.getMonth() + 1).padStart(2, '0');\n      var day = String(d.getDate()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n    },\n    // 格式化时间戳\n    formatTimestamp: function formatTimestamp(timestamp) {\n      if (!timestamp) return '';\n      var date = new Date(timestamp * 1000);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      });\n    },\n    // 重置筛选条件\n    resetFilter: function resetFilter() {\n      this.filterForm = {\n        order_no: '',\n        from_store_id: '',\n        to_store_id: '',\n        status: '',\n        date_range: []\n      };\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n    // 分页处理\n    handleSizeChange: function handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n    handleCurrentChange: function handleCurrentChange(page) {\n      this.currentPage = page;\n      this.getTransferList();\n    },\n    // 选择处理\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedRows = selection;\n    },\n    clearSelection: function clearSelection() {\n      this.$refs.table && this.$refs.table.clearSelection();\n      this.selectedRows = [];\n    },\n    // 状态处理\n    getStatusTagType: function getStatusTagType(status) {\n      var typeMap = {\n        0: 'warning',\n        // 待审核\n        1: 'success',\n        // 已通过\n        2: 'info',\n        // 已完成\n        3: 'danger',\n        // 已拒绝\n        4: 'info' // 已撤销\n\n      };\n      return typeMap[status] || 'info';\n    },\n    getStatusText: function getStatusText(status) {\n      var textMap = {\n        0: '待审核',\n        1: '已通过',\n        2: '已完成',\n        3: '已拒绝',\n        4: '已撤销'\n      };\n      return textMap[status] || '未知';\n    },\n    // 查看详情\n    viewDetail: function viewDetail(row) {\n      this.currentDetail = _objectSpread({}, row);\n      this.detailDialogVisible = true;\n    },\n    // 审核调拨\n    approveTransfer: function approveTransfer(row, isApprove) {\n      this.approveType = isApprove;\n      this.approveForm = {\n        id: row.id,\n        remark: ''\n      };\n      this.approveDialogVisible = true;\n    },\n    submitApprove: function () {\n      var _submitApprove = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee5() {\n        var data, res;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                this.approveLoading = true;\n                data = {\n                  is_approve: this.approveType,\n                  remark: this.approveForm.remark\n                };\n                _context5.next = 5;\n                return this.$http.post(\"/adminapi/erp/transfer/approve/\".concat(this.approveForm.id), data);\n\n              case 5:\n                res = _context5.sent;\n\n                if (res.data.status === 200) {\n                  this.$message.success(res.data.msg || (this.approveType ? '审核通过成功' : '审核拒绝成功'));\n                  this.approveDialogVisible = false;\n                  this.getTransferList();\n                  this.getStatistics();\n                } else {\n                  this.$message.error(res.data.msg || '审核失败');\n                }\n\n                _context5.next = 13;\n                break;\n\n              case 9:\n                _context5.prev = 9;\n                _context5.t0 = _context5[\"catch\"](0);\n                console.error('审核失败:', _context5.t0);\n                this.$message.error('审核失败');\n\n              case 13:\n                _context5.prev = 13;\n                this.approveLoading = false;\n                return _context5.finish(13);\n\n              case 16:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 9, 13, 16]]);\n      }));\n\n      function submitApprove() {\n        return _submitApprove.apply(this, arguments);\n      }\n\n      return submitApprove;\n    }(),\n    // 执行调拨\n    executeTransfer: function () {\n      var _executeTransfer = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee7(row) {\n        var _this = this;\n\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                this.$confirm('确定要执行这个调拨申请吗？', '确认执行', {\n                  confirmButtonText: '确定',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }).then(\n                /*#__PURE__*/\n                _asyncToGenerator(\n                /*#__PURE__*/\n                _regeneratorRuntime.mark(function _callee6() {\n                  var res;\n                  return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n                    while (1) {\n                      switch (_context6.prev = _context6.next) {\n                        case 0:\n                          _context6.prev = 0;\n                          _context6.next = 3;\n                          return _this.$http.post(\"/adminapi/erp/transfer/force_execute/\".concat(row.id));\n\n                        case 3:\n                          res = _context6.sent;\n\n                          if (res.data.status === 200) {\n                            _this.$message.success('调拨执行成功');\n\n                            _this.getTransferList();\n\n                            _this.getStatistics();\n                          } else {\n                            _this.$message.error(res.data.msg || '执行失败');\n                          }\n\n                          _context6.next = 11;\n                          break;\n\n                        case 7:\n                          _context6.prev = 7;\n                          _context6.t0 = _context6[\"catch\"](0);\n                          console.error('执行调拨失败:', _context6.t0);\n\n                          _this.$message.error('执行调拨失败');\n\n                        case 11:\n                        case \"end\":\n                          return _context6.stop();\n                      }\n                    }\n                  }, _callee6, null, [[0, 7]]);\n                })));\n\n              case 1:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this);\n      }));\n\n      function executeTransfer(_x) {\n        return _executeTransfer.apply(this, arguments);\n      }\n\n      return executeTransfer;\n    }(),\n    // 强制执行\n    forceExecute: function () {\n      var _forceExecute = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee9(row) {\n        var _this2 = this;\n\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                this.$confirm('强制执行将忽略库存不足等限制，确定要强制执行吗？', '确认强制执行', {\n                  confirmButtonText: '确定',\n                  cancelButtonText: '取消',\n                  type: 'error'\n                }).then(\n                /*#__PURE__*/\n                _asyncToGenerator(\n                /*#__PURE__*/\n                _regeneratorRuntime.mark(function _callee8() {\n                  var res;\n                  return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n                    while (1) {\n                      switch (_context8.prev = _context8.next) {\n                        case 0:\n                          _context8.prev = 0;\n                          _context8.next = 3;\n                          return _this2.$http.post(\"/adminapi/erp/transfer/force_execute/\".concat(row.id));\n\n                        case 3:\n                          res = _context8.sent;\n\n                          if (res.data.status === 200) {\n                            _this2.$message.success('强制执行成功');\n\n                            _this2.getTransferList();\n\n                            _this2.getStatistics();\n                          } else {\n                            _this2.$message.error(res.data.msg || '强制执行失败');\n                          }\n\n                          _context8.next = 11;\n                          break;\n\n                        case 7:\n                          _context8.prev = 7;\n                          _context8.t0 = _context8[\"catch\"](0);\n                          console.error('强制执行失败:', _context8.t0);\n\n                          _this2.$message.error('强制执行失败');\n\n                        case 11:\n                        case \"end\":\n                          return _context8.stop();\n                      }\n                    }\n                  }, _callee8, null, [[0, 7]]);\n                })));\n\n              case 1:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this);\n      }));\n\n      function forceExecute(_x2) {\n        return _forceExecute.apply(this, arguments);\n      }\n\n      return forceExecute;\n    }(),\n    // 删除调拨单\n    deleteTransfer: function () {\n      var _deleteTransfer = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee11(row) {\n        var _this3 = this;\n\n        return _regeneratorRuntime.wrap(function _callee11$(_context11) {\n          while (1) {\n            switch (_context11.prev = _context11.next) {\n              case 0:\n                this.$confirm('确定要删除这个调拨单吗？', '确认删除', {\n                  confirmButtonText: '确定',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }).then(\n                /*#__PURE__*/\n                _asyncToGenerator(\n                /*#__PURE__*/\n                _regeneratorRuntime.mark(function _callee10() {\n                  var res;\n                  return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n                    while (1) {\n                      switch (_context10.prev = _context10.next) {\n                        case 0:\n                          _context10.prev = 0;\n                          _context10.next = 3;\n                          return _this3.$http.delete(\"/adminapi/erp/transfer/delete/\".concat(row.id));\n\n                        case 3:\n                          res = _context10.sent;\n\n                          if (res.data.status === 200) {\n                            _this3.$message.success('删除成功');\n\n                            _this3.getTransferList();\n\n                            _this3.getStatistics();\n                          } else {\n                            _this3.$message.error(res.data.msg || '删除失败');\n                          }\n\n                          _context10.next = 11;\n                          break;\n\n                        case 7:\n                          _context10.prev = 7;\n                          _context10.t0 = _context10[\"catch\"](0);\n                          console.error('删除失败:', _context10.t0);\n\n                          _this3.$message.error('删除失败');\n\n                        case 11:\n                        case \"end\":\n                          return _context10.stop();\n                      }\n                    }\n                  }, _callee10, null, [[0, 7]]);\n                })));\n\n              case 1:\n              case \"end\":\n                return _context11.stop();\n            }\n          }\n        }, _callee11, this);\n      }));\n\n      function deleteTransfer(_x3) {\n        return _deleteTransfer.apply(this, arguments);\n      }\n\n      return deleteTransfer;\n    }(),\n    // 批量操作\n    batchApprove: function () {\n      var _batchApprove = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee13(isApprove) {\n        var _this4 = this;\n\n        var action;\n        return _regeneratorRuntime.wrap(function _callee13$(_context13) {\n          while (1) {\n            switch (_context13.prev = _context13.next) {\n              case 0:\n                if (!(this.selectedRows.length === 0)) {\n                  _context13.next = 3;\n                  break;\n                }\n\n                this.$message.warning('请先选择要审核的调拨单');\n                return _context13.abrupt(\"return\");\n\n              case 3:\n                action = isApprove ? '通过' : '拒绝';\n                this.$confirm(\"\\u786E\\u5B9A\\u8981\\u6279\\u91CF\".concat(action, \"\\u9009\\u4E2D\\u7684\\u8C03\\u62E8\\u5355\\u5417\\uFF1F\"), \"\\u786E\\u8BA4\\u6279\\u91CF\".concat(action), {\n                  confirmButtonText: '确定',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }).then(\n                /*#__PURE__*/\n                _asyncToGenerator(\n                /*#__PURE__*/\n                _regeneratorRuntime.mark(function _callee12() {\n                  var data, res;\n                  return _regeneratorRuntime.wrap(function _callee12$(_context12) {\n                    while (1) {\n                      switch (_context12.prev = _context12.next) {\n                        case 0:\n                          _context12.prev = 0;\n                          data = {\n                            ids: _this4.selectedRows.map(function (row) {\n                              return row.id;\n                            }),\n                            is_approve: isApprove,\n                            remark: ''\n                          };\n                          _context12.next = 4;\n                          return _this4.$http.post('/adminapi/erp/transfer/batch_approve', data);\n\n                        case 4:\n                          res = _context12.sent;\n\n                          if (res.data.status === 200) {\n                            _this4.$message.success(\"\\u6279\\u91CF\".concat(action, \"\\u6210\\u529F\"));\n\n                            _this4.getTransferList();\n\n                            _this4.getStatistics();\n\n                            _this4.clearSelection();\n                          } else {\n                            _this4.$message.error(res.data.msg || \"\\u6279\\u91CF\".concat(action, \"\\u5931\\u8D25\"));\n                          }\n\n                          _context12.next = 12;\n                          break;\n\n                        case 8:\n                          _context12.prev = 8;\n                          _context12.t0 = _context12[\"catch\"](0);\n                          console.error(\"\\u6279\\u91CF\".concat(action, \"\\u5931\\u8D25:\"), _context12.t0);\n\n                          _this4.$message.error(\"\\u6279\\u91CF\".concat(action, \"\\u5931\\u8D25\"));\n\n                        case 12:\n                        case \"end\":\n                          return _context12.stop();\n                      }\n                    }\n                  }, _callee12, null, [[0, 8]]);\n                })));\n\n              case 5:\n              case \"end\":\n                return _context13.stop();\n            }\n          }\n        }, _callee13, this);\n      }));\n\n      function batchApprove(_x4) {\n        return _batchApprove.apply(this, arguments);\n      }\n\n      return batchApprove;\n    }(),\n    batchExecute: function () {\n      var _batchExecute = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee15() {\n        var _this5 = this;\n\n        return _regeneratorRuntime.wrap(function _callee15$(_context15) {\n          while (1) {\n            switch (_context15.prev = _context15.next) {\n              case 0:\n                if (!(this.selectedRows.length === 0)) {\n                  _context15.next = 3;\n                  break;\n                }\n\n                this.$message.warning('请先选择要执行的调拨单');\n                return _context15.abrupt(\"return\");\n\n              case 3:\n                this.$confirm('确定要批量执行选中的调拨单吗？', '确认批量执行', {\n                  confirmButtonText: '确定',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }).then(\n                /*#__PURE__*/\n                _asyncToGenerator(\n                /*#__PURE__*/\n                _regeneratorRuntime.mark(function _callee14() {\n                  var _iteratorNormalCompletion, _didIteratorError, _iteratorError, _iterator, _step, row;\n\n                  return _regeneratorRuntime.wrap(function _callee14$(_context14) {\n                    while (1) {\n                      switch (_context14.prev = _context14.next) {\n                        case 0:\n                          _context14.prev = 0;\n                          // 逐个执行调拨单\n                          _iteratorNormalCompletion = true;\n                          _didIteratorError = false;\n                          _iteratorError = undefined;\n                          _context14.prev = 4;\n                          _iterator = _this5.selectedRows[Symbol.iterator]();\n\n                        case 6:\n                          if (_iteratorNormalCompletion = (_step = _iterator.next()).done) {\n                            _context14.next = 13;\n                            break;\n                          }\n\n                          row = _step.value;\n                          _context14.next = 10;\n                          return _this5.$http.post(\"/adminapi/erp/transfer/force_execute/\".concat(row.id));\n\n                        case 10:\n                          _iteratorNormalCompletion = true;\n                          _context14.next = 6;\n                          break;\n\n                        case 13:\n                          _context14.next = 19;\n                          break;\n\n                        case 15:\n                          _context14.prev = 15;\n                          _context14.t0 = _context14[\"catch\"](4);\n                          _didIteratorError = true;\n                          _iteratorError = _context14.t0;\n\n                        case 19:\n                          _context14.prev = 19;\n                          _context14.prev = 20;\n\n                          if (!_iteratorNormalCompletion && _iterator.return != null) {\n                            _iterator.return();\n                          }\n\n                        case 22:\n                          _context14.prev = 22;\n\n                          if (!_didIteratorError) {\n                            _context14.next = 25;\n                            break;\n                          }\n\n                          throw _iteratorError;\n\n                        case 25:\n                          return _context14.finish(22);\n\n                        case 26:\n                          return _context14.finish(19);\n\n                        case 27:\n                          _this5.$message.success('批量执行成功');\n\n                          _this5.getTransferList();\n\n                          _this5.getStatistics();\n\n                          _this5.clearSelection();\n\n                          _context14.next = 37;\n                          break;\n\n                        case 33:\n                          _context14.prev = 33;\n                          _context14.t1 = _context14[\"catch\"](0);\n                          console.error('批量执行失败:', _context14.t1);\n\n                          _this5.$message.error('批量执行失败');\n\n                        case 37:\n                        case \"end\":\n                          return _context14.stop();\n                      }\n                    }\n                  }, _callee14, null, [[0, 33], [4, 15, 19, 27], [20,, 22, 26]]);\n                })));\n\n              case 4:\n              case \"end\":\n                return _context15.stop();\n            }\n          }\n        }, _callee15, this);\n      }));\n\n      function batchExecute() {\n        return _batchExecute.apply(this, arguments);\n      }\n\n      return batchExecute;\n    }(),\n    // 其他功能\n    showPendingOnly: function showPendingOnly() {\n      this.filterForm.status = 0;\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n    exportTransfer: function () {\n      var _exportTransfer = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee16() {\n        var params, res;\n        return _regeneratorRuntime.wrap(function _callee16$(_context16) {\n          while (1) {\n            switch (_context16.prev = _context16.next) {\n              case 0:\n                _context16.prev = 0;\n                params = {\n                  order_no: this.filterForm.order_no,\n                  from_store_id: this.filterForm.from_store_id,\n                  to_store_id: this.filterForm.to_store_id,\n                  status: this.filterForm.status\n                }; // 处理日期范围\n\n                if (this.filterForm.date_range && this.filterForm.date_range.length === 2) {\n                  params.start_time = this.formatDate(this.filterForm.date_range[0]);\n                  params.end_time = this.formatDate(this.filterForm.date_range[1]);\n                }\n\n                _context16.next = 5;\n                return this.$http.get('/adminapi/erp/transfer/export', {\n                  params: params\n                });\n\n              case 5:\n                res = _context16.sent;\n\n                if (res.data.status === 200) {\n                  this.$message.success('导出成功'); // TODO: 处理文件下载\n                } else {\n                  this.$message.error(res.data.msg || '导出失败');\n                }\n\n                _context16.next = 13;\n                break;\n\n              case 9:\n                _context16.prev = 9;\n                _context16.t0 = _context16[\"catch\"](0);\n                console.error('导出失败:', _context16.t0);\n                this.$message.error('导出失败');\n\n              case 13:\n              case \"end\":\n                return _context16.stop();\n            }\n          }\n        }, _callee16, this, [[0, 9]]);\n      }));\n\n      function exportTransfer() {\n        return _exportTransfer.apply(this, arguments);\n      }\n\n      return exportTransfer;\n    }(),\n    showStatistics: function showStatistics() {\n      // 跳转到统计分析页面或显示统计弹窗\n      this.$router.push('/erp/report');\n    }\n  }, _defineProperty(_methods, \"getStatusText\", function getStatusText(status) {\n    var statusMap = {\n      0: '待审核',\n      1: '已审核',\n      2: '已执行',\n      3: '已拒绝',\n      4: '已撤销'\n    };\n    return statusMap[status] || '未知状态';\n  }), _defineProperty(_methods, \"getStatusTagColor\", function getStatusTagColor(status) {\n    var colorMap = {\n      0: 'orange',\n      // 待审核\n      1: 'blue',\n      // 已审核\n      2: 'green',\n      // 已执行\n      3: 'red',\n      // 已拒绝\n      4: 'default' // 已撤销\n\n    };\n    return colorMap[status] || 'default';\n  }), _defineProperty(_methods, \"showAddDialog\", function showAddDialog() {\n    this.$Message.info('添加调拨功能开发中...');\n  }), _defineProperty(_methods, \"handleSelectionChange\", function handleSelectionChange(selection) {\n    this.selectedRows = selection;\n  }), _defineProperty(_methods, \"clearSelection\", function clearSelection() {\n    this.selectedRows = [];\n  }), _defineProperty(_methods, \"handleCurrentChange\", function handleCurrentChange(page) {\n    this.currentPage = page;\n    this.getTransferList();\n  }), _defineProperty(_methods, \"handleSizeChange\", function handleSizeChange(size) {\n    this.pageSize = size;\n    this.currentPage = 1;\n    this.getTransferList();\n  }), _defineProperty(_methods, \"resetFilter\", function resetFilter() {\n    this.filterForm = {\n      order_no: '',\n      from_store_id: '',\n      to_store_id: '',\n      status: '',\n      date_range: []\n    };\n    this.currentPage = 1;\n    this.getTransferList();\n  }), _defineProperty(_methods, \"viewDetail\", function viewDetail(row) {\n    this.currentDetail = row;\n    this.detailDialogVisible = true;\n  }), _methods)\n};", null]}