{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\erp\\transfer\\index.vue", "mtime": 1750985347698}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:\\\\zksh\\\\zk_shg_3.0\\\\view\\\\zk_shg_admin\\\\node_modules\\\\@babel\\\\runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'TransferManagement',\n  data: function data() {\n    return {\n      // 筛选条件\n      filterForm: {\n        order_no: '',\n        from_store_id: '',\n        to_store_id: '',\n        status: '',\n        date_range: []\n      },\n      // 列表数据\n      transferList: [],\n      storeList: [],\n      selectedRows: [],\n      // 分页\n      currentPage: 1,\n      pageSize: 20,\n      total: 0,\n      tableLoading: false,\n      // 统计数据\n      statistics: {\n        total_orders: 0,\n        pending_orders: 0,\n        approved_orders: 0,\n        completed_orders: 0\n      },\n      // 详情对话框\n      detailDialogVisible: false,\n      currentDetail: null,\n      // 审核对话框\n      approveDialogVisible: false,\n      approveLoading: false,\n      approveType: true,\n      // true: 通过, false: 拒绝\n      approveForm: {\n        id: '',\n        remark: ''\n      },\n      approveRules: {\n        remark: [{\n          required: false,\n          message: '请填写审核备注',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  methods: {\n    // 初始化数据\n    initData: function () {\n      var _initData = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                this.getTransferList();\n\n              case 1:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this);\n      }));\n\n      function initData() {\n        return _initData.apply(this, arguments);\n      }\n\n      return initData;\n    }(),\n    // 获取调拨单列表\n    getTransferList: function () {\n      var _getTransferList = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee2() {\n        var _this = this;\n\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                this.tableLoading = true; // 模拟数据\n\n                setTimeout(function () {\n                  _this.transferList = [{\n                    id: 1,\n                    order_no: 'TF202412160001',\n                    product_id: 1,\n                    product_name: '苹果iPhone 14',\n                    product_image: 'https://via.placeholder.com/40x40',\n                    sku: '128GB 蓝色',\n                    transfer_qty: 10,\n                    from_store_id: 1,\n                    from_store_name: '上海总店',\n                    to_store_id: 2,\n                    to_store_name: '北京分店',\n                    status: 0,\n                    applicant: '张三',\n                    apply_time: '2024-12-16 10:30:00',\n                    approve_time: '',\n                    remark: '北京分店库存不足，急需补货'\n                  }, {\n                    id: 2,\n                    order_no: 'TF202412160002',\n                    product_id: 2,\n                    product_name: '华为Mate50',\n                    product_image: 'https://via.placeholder.com/40x40',\n                    sku: '256GB 白色',\n                    transfer_qty: 5,\n                    from_store_id: 2,\n                    from_store_name: '北京分店',\n                    to_store_id: 3,\n                    to_store_name: '深圳分店',\n                    status: 1,\n                    applicant: '李四',\n                    apply_time: '2024-12-16 09:15:00',\n                    approve_time: '2024-12-16 10:00:00',\n                    remark: '季节性调拨'\n                  }];\n                  _this.total = 2;\n                  _this.statistics = {\n                    total_orders: 45,\n                    pending_orders: 8,\n                    approved_orders: 12,\n                    completed_orders: 25\n                  };\n                  _this.tableLoading = false;\n                }, 1000);\n\n              case 2:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function getTransferList() {\n        return _getTransferList.apply(this, arguments);\n      }\n\n      return getTransferList;\n    }(),\n    // 重置筛选条件\n    resetFilter: function resetFilter() {\n      this.filterForm = {\n        order_no: '',\n        from_store_id: '',\n        to_store_id: '',\n        status: '',\n        date_range: []\n      };\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n    // 分页处理\n    handleSizeChange: function handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n      this.getTransferList();\n    },\n    handleCurrentChange: function handleCurrentChange(page) {\n      this.currentPage = page;\n      this.getTransferList();\n    },\n    // 选择处理\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedRows = selection;\n    },\n    clearSelection: function clearSelection() {\n      this.$refs.table && this.$refs.table.clearSelection();\n      this.selectedRows = [];\n    },\n    // 状态处理\n    getStatusTagType: function getStatusTagType(status) {\n      var typeMap = {\n        0: 'warning',\n        // 待审核\n        1: 'success',\n        // 已通过\n        2: 'info',\n        // 已完成\n        3: 'danger',\n        // 已拒绝\n        4: 'info' // 已撤销\n\n      };\n      return typeMap[status] || 'info';\n    },\n    getStatusText: function getStatusText(status) {\n      var textMap = {\n        0: '待审核',\n        1: '已通过',\n        2: '已完成',\n        3: '已拒绝',\n        4: '已撤销'\n      };\n      return textMap[status] || '未知';\n    },\n    // 查看详情\n    viewDetail: function viewDetail(row) {\n      this.currentDetail = _objectSpread({}, row);\n      this.detailDialogVisible = true;\n    },\n    // 审核调拨\n    approveTransfer: function approveTransfer(row, isApprove) {\n      this.approveType = isApprove;\n      this.approveForm = {\n        id: row.id,\n        remark: ''\n      };\n      this.approveDialogVisible = true;\n    },\n    submitApprove: function () {\n      var _submitApprove = _asyncToGenerator(\n      /*#__PURE__*/\n      _regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                this.approveLoading = true; // 模拟API调用\n\n                _context3.next = 4;\n                return new Promise(function (resolve) {\n                  return setTimeout(resolve, 1000);\n                });\n\n              case 4:\n                this.$message.success(this.approveType ? '审核通过成功' : '审核拒绝成功');\n                this.approveDialogVisible = false;\n                this.getTransferList();\n                _context3.next = 12;\n                break;\n\n              case 9:\n                _context3.prev = 9;\n                _context3.t0 = _context3[\"catch\"](0);\n                this.$message.error('审核失败');\n\n              case 12:\n                _context3.prev = 12;\n                this.approveLoading = false;\n                return _context3.finish(12);\n\n              case 15:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 9, 12, 15]]);\n      }));\n\n      function submitApprove() {\n        return _submitApprove.apply(this, arguments);\n      }\n\n      return submitApprove;\n    }(),\n    // 执行调拨\n    executeTransfer: function executeTransfer(row) {\n      var _this2 = this;\n\n      this.$confirm('确定要执行这个调拨申请吗？', '确认执行', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this2.$message.success('调拨执行成功');\n\n        _this2.getTransferList();\n      });\n    },\n    // 强制执行\n    forceExecute: function forceExecute(row) {\n      var _this3 = this;\n\n      this.$confirm('强制执行将忽略库存不足等限制，确定要强制执行吗？', '确认强制执行', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'error'\n      }).then(function () {\n        _this3.$message.success('强制执行成功');\n\n        _this3.getTransferList();\n      });\n    },\n    // 删除调拨单\n    deleteTransfer: function deleteTransfer(row) {\n      var _this4 = this;\n\n      this.$confirm('确定要删除这个调拨单吗？', '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this4.$message.success('删除成功');\n\n        _this4.getTransferList();\n      });\n    },\n    // 批量操作\n    batchApprove: function batchApprove(isApprove) {\n      var _this5 = this;\n\n      if (this.selectedRows.length === 0) {\n        this.$message.warning('请先选择要审核的调拨单');\n        return;\n      }\n\n      var action = isApprove ? '通过' : '拒绝';\n      this.$confirm(\"\\u786E\\u5B9A\\u8981\\u6279\\u91CF\".concat(action, \"\\u9009\\u4E2D\\u7684\\u8C03\\u62E8\\u5355\\u5417\\uFF1F\"), \"\\u786E\\u8BA4\\u6279\\u91CF\".concat(action), {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this5.$message.success(\"\\u6279\\u91CF\".concat(action, \"\\u6210\\u529F\"));\n\n        _this5.getTransferList();\n\n        _this5.clearSelection();\n      });\n    },\n    batchExecute: function batchExecute() {\n      var _this6 = this;\n\n      if (this.selectedRows.length === 0) {\n        this.$message.warning('请先选择要执行的调拨单');\n        return;\n      }\n\n      this.$confirm('确定要批量执行选中的调拨单吗？', '确认批量执行', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this6.$message.success('批量执行成功');\n\n        _this6.getTransferList();\n\n        _this6.clearSelection();\n      });\n    },\n    // 其他功能\n    showPendingOnly: function showPendingOnly() {\n      this.filterForm.status = 0;\n      this.getTransferList();\n    },\n    exportTransfer: function exportTransfer() {\n      this.$message.info('导出功能开发中...');\n    },\n    showStatistics: function showStatistics() {\n      this.$message.info('统计分析功能开发中...');\n    }\n  }\n};", null]}