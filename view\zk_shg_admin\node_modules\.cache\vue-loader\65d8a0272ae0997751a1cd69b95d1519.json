{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\staffList\\menuChild.vue?vue&type=template&id=c20cf27e&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\work\\staffList\\menuChild.vue", "mtime": 1650783676000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<Submenu :name=\"parentItem.department_id\">\n    <template slot=\"title\">\n         <Icon type=\"ios-folder\" size=\"15\" color=\"#FFCA28\" />\n        <span>{{ parentItem.name }}</span>\n    </template>\n    <template v-for=\"item in parentItem.children\">\n        <side-menu-item \n            v-if=\"item.children&&item.children.length!==0\" \n            :parent-item=\"item\" \n            :key=\"'menu-'+item.name\"\n        >\n        </side-menu-item>\n        <menu-item v-else :name=\"item.department_id\" :key=\"'menu-'+item.name\">\n             <Icon type=\"ios-folder\" size=\"15\" color=\"#FFCA28\" />\n            <span>{{ item.name }} ({{item.count}})</span>\n        </menu-item>\n    </template>\n</Submenu>\n", null]}