<?php
/**
 * 库存管理服务
 * User: 系统生成
 * Date: <?= date('Y-m-d H:i:s') ?>
 */

namespace app\services\erp;

use app\services\BaseServices;
use app\dao\product\sku\StoreProductAttrValueDao;
use app\dao\product\product\StoreProductStockRecordDao;
use app\dao\store\SystemStoreDao;
use app\dao\product\product\StoreProductDao;
use crmeb\exceptions\AdminException;
use crmeb\services\FormBuilder as Form;
use think\facade\Db;
use crmeb\services\SpreadsheetExcelService;

/**
 * 库存管理服务
 * Class InventoryServices
 * @package app\services\erp
 */
class InventoryServices extends BaseServices
{
    /**
     * 构造方法
     * InventoryServices constructor.
     * @param StoreProductAttrValueDao $dao
     */
    public function __construct(StoreProductAttrValueDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取库存列表
     * @param array $where 搜索条件
     * @return array
     */
    public function getInventoryList(array $where)
    {
        [$page, $limit] = $this->getPageValue();
        
        // 使用Db直接查询，不依赖dao
        $query = Db::name('store_product_attr_value')
            ->alias('pav')
            ->leftJoin('store_product p', 'pav.product_id = p.id')
            ->where('p.is_del', 0)
            ->where('p.is_show', 1)
            ->field([
                'pav.unique',
                'pav.product_id', 
                'pav.stock',
                'pav.cost',
                'pav.price',
                'pav.bar_code',
                'pav.weight',
                'pav.volume',
                'pav.suk',
                'p.store_name as product_name',
                'p.image as product_image',
                'p.unit_name',
                'p.is_show'
            ]);
            
        // 关键词搜索
        if (!empty($where['keyword'])) {
            $query->where(function($query) use ($where) {
                $query->whereOr('p.store_name', 'like', '%' . $where['keyword'] . '%')
                      ->whereOr('pav.suk', 'like', '%' . $where['keyword'] . '%')
                      ->whereOr('pav.bar_code', 'like', '%' . $where['keyword'] . '%');
            });
        }
        
        // 库存状态筛选
        if (!empty($where['stock_status'])) {
            if ($where['stock_status'] === 'out') {
                $query->where('pav.stock', '<=', 0);
            } elseif ($where['stock_status'] === 'warning') {
                $query->where('pav.stock', '>', 0)->where('pav.stock', '<=', 10);
            } elseif ($where['stock_status'] === 'normal') {
                $query->where('pav.stock', '>', 10);
            }
        }
        
        // 获取总数
        $count = (clone $query)->count();
        
        // 分页查询
        $list = $query->page($page, $limit)
            ->order('pav.stock asc, pav.product_id desc')
            ->select()
            ->toArray();

        // 处理数据
        foreach ($list as &$item) {
            $item['product_name'] = $item['product_name'] ?: '商品不存在';
            $item['product_image'] = $item['product_image'] ?: '';
            $item['unit_name'] = $item['unit_name'] ?: '件';
            $item['spec_name'] = $item['suk'] ?: '默认规格';
            $item['stock_status'] = $this->getStockStatus($item['stock']);
            $item['stock_value'] = number_format($item['stock'] * $item['cost'], 2);
        }
        
        return compact('list', 'count');
    }

    /**
     * 获取库存状态
     * @param int $stock 库存数量
     * @return array
     */
    private function getStockStatus(int $stock)
    {
        if ($stock <= 0) {
            return ['text' => '缺货', 'class' => 'danger'];
        } elseif ($stock <= 10) {
            return ['text' => '预警', 'class' => 'warning'];
        } elseif ($stock <= 50) {
            return ['text' => '正常', 'class' => 'success'];
        } else {
            return ['text' => '充足', 'class' => 'primary'];
        }
    }

    /**
     * 获取库存详情
     * @param string $unique 规格唯一值
     * @return array
     */
    public function getInventoryDetail(string $unique)
    {
        $info = $this->dao->getOne(['unique' => $unique], '*', ['product']);
        if (!$info) {
            throw new AdminException('商品规格不存在');
        }

        $info = $info->toArray();
        
        // 获取库存变动记录
        $stockRecordDao = app()->make(StoreProductStockRecordDao::class);
        $records = $stockRecordDao->selectList(
            ['unique' => $unique], 
            '*', 
            1, 
            10, 
            'add_time desc'
        )->toArray();

        // 处理记录数据
        foreach ($records as &$record) {
            $record['type_text'] = $this->getStockRecordTypeText($record['type'] ?? 'manual');
            $record['pm_text'] = $record['pm'] ? '入库' : '出库';
            $record['add_time_text'] = date('Y-m-d H:i:s', $record['add_time']);
        }

        $info['records'] = $records;
        $info['stock_status'] = $this->getStockStatus($info['stock']);
        $info['stock_value'] = bcmul($info['stock'], $info['cost'], 2);

        return $info;
    }

    /**
     * 获取商品信息和库存
     * @param int $productId 商品ID
     * @return array
     */
    public function getProductInfo(int $productId)
    {
        $productDao = app()->make(\app\dao\product\product\StoreProductDao::class);
        $product = $productDao->getOne(['id' => $productId], 'id,store_name,image,price,unit_name,spec_type,relation_id,type,pid')->toArray();
        if (!$product) {
            throw new AdminException('商品不存在');
        }
        
        // 查询当前商品的规格库存信息
        $currentProductSpecs = Db::name('store_product_attr_value')
            ->alias('pav')
            ->leftJoin('store_product p', 'pav.product_id = p.id')
            ->leftJoin('system_store s', 'p.relation_id = s.id')
            ->where('pav.product_id', $productId)
            ->field([
                'pav.unique',
                'pav.stock', 
                'pav.cost',
                'pav.price',
                'pav.suk as spec_name',
                'p.store_name as product_name',
                'p.image',
                'p.unit_name',
                'p.spec_type',
                's.name as store_name',
                's.id as store_id'
            ])
            ->select()
            ->toArray();
            
        // 获取平台商品ID，用于后续查询门店库存
        $platformProductId = $product['type'] == 0 ? $productId : $product['pid'];
        
        // 按规格重新组织数据结构 - 每个规格作为一个单独的二维数组
        $specList = [];
        
        // 处理当前商品的规格
        foreach ($currentProductSpecs as $currentSpec) {
            $specKey = $currentSpec['spec_name']; // 使用suk(spec_name)作为规格标识
            
            // 当前商品信息（含门店名称）
            $currentInfo = [
                'unique' => $currentSpec['unique'],
                'product_name' => $currentSpec['product_name'],
                'spec_name' => $currentSpec['spec_name'] ?: '默认规格',
                'stock' => $currentSpec['stock'],
                'cost' => $currentSpec['cost'],
                'price' => $currentSpec['price'],
                'image' => $currentSpec['image'],
                'unit_name' => $currentSpec['unit_name'],
                'store_name' => $currentSpec['store_name'],
                'store_id' => $currentSpec['store_id']
            ];
            
            // 针对当前规格值，查询所有门店的库存情况
            $storeStocks = [];
            
            // 查询平台该规格库存（如果当前商品是平台商品）
            if ($product['type'] == 0) {
                $platformSpecStock = Db::name('store_product_attr_value')
                    ->alias('pav')
                    ->leftJoin('store_product p', 'pav.product_id = p.id')
                    ->where('pav.suk', $specKey) // 使用suk字段匹配规格
                    ->where('p.type', 0)
                    ->where('p.id', $productId)
                    ->field([
                        'p.id as product_id',
                        'pav.stock',
                        'pav.cost',
                        'pav.price',
                        'pav.unique',
                        '"平台库存" as store_name',
                        '0 as store_id'
                    ])
                    ->find();
                    
                if ($platformSpecStock && $platformSpecStock['stock'] > 0) {
                    $storeStocks[] = [
                        'store_id' => 0,
                        'store_name' => '平台库存',
                        'product_id' => $platformSpecStock['product_id'],
                        'stock' => $platformSpecStock['stock'],
                        'cost' => $platformSpecStock['cost'],
                        'price' => $platformSpecStock['price'],
                        'unique' => $platformSpecStock['unique']
                    ];
                }
            }
            
            // 查询所有门店该规格的库存
            if ($platformProductId > 0) {
                $storeSpecStocks = Db::name('store_product_attr_value')
                    ->alias('pav')
                    ->leftJoin('store_product p', 'pav.product_id = p.id')
                    ->leftJoin('system_store s', 'p.relation_id = s.id')
                    ->where('pav.suk', $specKey) // 关键：使用suk字段匹配规格
                    ->where('p.type', 1) // 门店商品
                    ->where('p.pid', $platformProductId) // 关联的平台商品ID
                    ->field([
                        'p.id as product_id',
                        'pav.stock',
                        'pav.cost',
                        'pav.price',
                        'pav.unique',
                        's.name as store_name',
                        's.id as store_id'
                    ])
                    ->select()
                    ->toArray();
                foreach ($storeSpecStocks as $storeStock) {
                    $storeStocks[] = [
                        'store_id' => $storeStock['store_id'],
                        'store_name' => $storeStock['store_name'],
                        'product_id' => $storeStock['product_id'],
                        'stock' => $storeStock['stock'],
                        'cost' => $storeStock['cost'],
                        'price' => $storeStock['price'],
                        'unique' => $storeStock['unique']
                    ];
                }
            }
            
            // 按库存从大到小排序
            usort($storeStocks, function($a, $b) {
                return $b['stock'] - $a['stock'];
            });
            // 组装该规格的完整信息
            $specList[] = [
                // 当前商品信息（含门店名称）
                'current_product' => $currentInfo,
                // 该规格在所有门店的库存信息（按库存从大到小排序）
                'store_stocks' => $storeStocks,
                // 规格汇总信息
                'spec_summary' => [
                    'unique' => $currentSpec['unique'],
                    'spec_name' => $currentSpec['spec_name'] ?: '默认规格',
                    'suk' => $specKey, // 规格属性组合
                    'total_stores' => count($storeStocks),
                    'total_stock' => array_sum(array_column($storeStocks, 'stock'))
                ]
            ];
        }
        
        // 返回以规格为单位的数据结构
        $result = [
            // 规格列表 - 每个规格都是一个独立的二维数组
            'spec_list' => $specList,
            // 基础商品信息
            'base_info' => [
                'id' => $product['id'],
                'store_name' => $product['store_name'],
                'image' => $product['image'],
                'price' => $product['price'],
                'unit_name' => $product['unit_name'],
                'spec_type' => $product['spec_type'],
                'relation_id' => $product['relation_id'],
                'type' => $product['type'],
                'pid' => $product['pid'],
                'total_specs' => count($specList)
            ]
        ];
        
        return $result;
    }

    /**
     * 获取库存记录类型文字
     * @param string $type
     * @return string
     */
    private function getStockRecordTypeText(string $type)
    {
        $typeMap = [
            'manual' => '手动调整',
            'sale' => '销售出库',
            'transfer_in' => '调拨入库',
            'transfer_out' => '调拨出库',
            'adjust' => '库存调整',
        ];
        
        return $typeMap[$type] ?? '未知类型';
    }

    /**
     * 库存调整
     * @param string $unique 规格唯一值
     * @param int $adjustStock 调整数量（正数增加，负数减少）
     * @param string $remark 备注
     * @param int $adminId 操作人员ID
     * @return bool
     * @throws \Exception
     */
    public function adjustStock(string $unique, int $adjustStock, string $remark, int $adminId)
    {
        $attrValue = $this->dao->getOne(['unique' => $unique]);
        if (!$attrValue) {
            throw new AdminException('商品规格不存在');
        }

        // 检查库存是否充足（减库存时）
        if ($adjustStock < 0 && $attrValue['stock'] < abs($adjustStock)) {
            throw new AdminException('库存不足，当前库存：' . $attrValue['stock']);
        }

        return Db::transaction(function () use ($unique, $adjustStock, $remark, $adminId, $attrValue) {
            // 更新库存
            $newStock = $attrValue['stock'] + $adjustStock;
            $this->dao->update(['unique' => $unique], ['stock' => $newStock]);

            // 记录库存变动
            $stockRecordDao = app()->make(StoreProductStockRecordDao::class);
            $stockRecordDao->save([
                'store_id' => 0,  // 总部调整
                'product_id' => $attrValue['product_id'],
                'unique' => $unique,
                'cost_price' => $attrValue['cost'],
                'number' => abs($adjustStock),
                'pm' => $adjustStock > 0 ? 1 : 0,
                'type' => 'adjust',
                'relation_id' => 0,
                'note' => $remark,
                'add_time' => time(),
            ]);

            return true;
        });
    }

    /**
     * 获取库存预警列表
     * @param int $warningStock 预警库存阈值
     * @return array
     */
    public function getStockWarningList(int $warningStock = 10)
    {
        $where = [];
        $list = $this->dao->search($where)
            ->where('stock', '<=', $warningStock)
            ->field('unique,product_id,stock,cost,price')
            ->with(['product' => function($query) {
                $query->field('id,store_name,image,unit_name');
            }])
            ->order('stock asc')
            ->limit(100)
            ->select()
            ->toArray();

        foreach ($list as &$item) {
            $item['product_name'] = $item['product']['store_name'] ?? '商品不存在';
            $item['product_image'] = $item['product']['image'] ?? '';
            $item['stock_status'] = $this->getStockStatus($item['stock']);
            unset($item['product']);
        }

        return $list;
    }

    /**
     * 获取库存统计
     * @param array $where 条件
     * @return array
     */
    public function getInventoryStats(array $where = [])
    {
        try {
            // 使用Db直接查询库存统计数据，添加异常处理
            $query = Db::name('store_product_attr_value')
                ->alias('pav')
                ->leftJoin('store_product p', 'pav.product_id = p.id')
                ->where('p.is_del', 0)
                ->where('p.is_show', 1);
                
            // 总商品规格数（可以理解为SKU数量）
            $totalProducts = (clone $query)->count();
            
            // 缺货商品数（库存<=0）
            $outOfStockProducts = (clone $query)->where('pav.stock', '<=', 0)->count();
            
            // 预警商品数（库存>0 且 <=10）
            $warningProducts = (clone $query)->where('pav.stock', '>', 0)->where('pav.stock', '<=', 10)->count();
            
            // 正常商品数
            $normalProducts = $totalProducts - $outOfStockProducts - $warningProducts;
            
            // 库存总价值（使用PHP计算避免数据库兼容性问题）
            $stockData = (clone $query)
                ->field('pav.stock, pav.cost')
                ->where('pav.stock', '>', 0)
                ->select()
                ->toArray();
                
            $totalValue = 0;
            foreach ($stockData as $item) {
                $totalValue += $item['stock'] * $item['cost'];
            }
            
            return [
                'total_products' => $totalProducts,
                'out_of_stock_products' => $outOfStockProducts,
                'warning_products' => $warningProducts,
                'normal_products' => $normalProducts,
                'total_value' => number_format($totalValue, 2),
            ];
        } catch (\Exception $e) {
            // 如果数据库查询失败，返回默认值
            return [
                'total_products' => 0,
                'out_of_stock_products' => 0,
                'warning_products' => 0,
                'normal_products' => 0,
                'total_value' => '0.00',
            ];
        }
    }

    /**
     * 获取门店库存分布
     * @param int $productId 商品ID
     * @param string $unique 规格唯一值
     * @return array
     */
    public function getStoreStockDistribution(int $productId, string $unique = '')
    {
        // 注意：由于复用现有表结构，这里返回总库存信息
        // 如果需要按门店分布，需要通过销售记录等方式估算
        $attrValue = $this->dao->getOne([
            'product_id' => $productId,
            'unique' => $unique ?: ''
        ]);

        if (!$attrValue) {
            return [];
        }

        // 获取门店列表
        $storeDao = app()->make(SystemStoreDao::class);
        $stores = $storeDao->selectList([], 'id,name', 0, 0, '', false);

        // 模拟门店库存分布（实际应用中可根据业务需求调整）
        $distribution = [];
        foreach ($stores as $store) {
            $distribution[] = [
                'store_id' => $store['id'],
                'store_name' => $store['name'],
                'stock' => 0, // 实际需要根据业务逻辑计算
                'last_update' => date('Y-m-d H:i:s'),
            ];
        }

        return [
            'product_info' => $attrValue,
            'total_stock' => $attrValue['stock'],
            'store_distribution' => $distribution,
        ];
    }

    /**
     * 批量库存调整
     * @param array $adjustData 调整数据
     * @param int $adminId 操作人员ID
     * @return bool
     */
    public function batchAdjustStock(array $adjustData, int $adminId)
    {
        return Db::transaction(function () use ($adjustData, $adminId) {
            foreach ($adjustData as $item) {
                $this->adjustStock(
                    $item['unique'], 
                    $item['adjust_stock'], 
                    $item['remark'] ?? '批量调整', 
                    $adminId
                );
            }
            return true;
        });
    }

    /**
     * 导出库存数据
     * @param array $where 查询条件
     * @return array
     */
    public function exportInventoryData(array $where = [])
    {
        $list = $this->dao->search($where)
            ->field('unique,product_id,stock,cost,price,bar_code')
            ->with(['product' => function($query) {
                $query->field('id,store_name,unit_name,cate_id');
            }])
            ->order('product_id desc')
            ->select()
            ->toArray();

        $exportData = [];
        foreach ($list as $item) {
            $exportData[] = [
                '商品名称' => $item['product']['store_name'] ?? '',
                '规格' => '默认规格',
                '条形码' => $item['bar_code'],
                '当前库存' => $item['stock'],
                '成本价' => $item['cost'],
                '销售价' => $item['price'],
                '库存价值' => bcmul($item['stock'], $item['cost'], 2),
                '单位' => $item['product']['unit_name'] ?? '件',
            ];
        }

        return $exportData;
    }

    /**
     * 获取有指定商品库存的门店列表
     * @param int $productId 商品ID
     * @return array
     */
    public function getStoresWithStock(int $productId)
    {
        // 获取商品的库存信息
        $attrValues = $this->dao->getProductAttrValues($productId);
        if (empty($attrValues)) {
            return [];
        }

        // 获取所有门店列表
        $storeDao = app()->make(\app\dao\store\SystemStoreDao::class);
        $stores = $storeDao->selectList(['is_del' => 0], 'id,name,phone,address,image', 0, 0, '', false);

        $storeList = [];
        foreach ($stores as $store) {
            // 计算门店该商品的总库存（如果有多个规格则累加）
            $totalStock = 0;
            foreach ($attrValues as $attrValue) {
                $totalStock += $attrValue['stock'];
            }

            // 只返回有库存的门店
            if ($totalStock > 0) {
                $storeList[] = [
                    'id' => $store['id'],
                    'name' => $store['name'],
                    'phone' => $store['phone'],
                    'address' => $store['address'],
                    'image' => $store['image'],
                    'stock' => $totalStock,
                ];
            }
        }

        return $storeList;
    }

    /**
     * 获取库存变动记录
     * @param array $where 查询条件
     * @return array
     */
    public function getStockRecords(array $where)
    {
        [$page, $limit] = $this->getPageValue();
        
        $stockRecordDao = app()->make(StoreProductStockRecordDao::class);
        
        $list = $stockRecordDao->selectList($where, '*', $page, $limit, 'add_time desc', true);

        foreach ($list as &$item) {
            $item['type_text'] = $this->getStockRecordTypeText($item['type'] ?? 'manual');
            $item['pm_text'] = $item['pm'] ? '入库' : '出库';
            $item['add_time_text'] = date('Y-m-d H:i:s', $item['add_time']);
            // 获取商品名称
            if (!empty($item['product_id'])) {
                $productDao = app()->make(StoreProductDao::class);
                $product = $productDao->get($item['product_id'], ['store_name']);
                $item['product_name'] = $product['store_name'] ?? '商品不存在';
            }
        }

        $count = $stockRecordDao->count($where);
        
        return compact('list', 'count');
    }

    /**
     * 获取指定门店指定商品的库存
     * @param int $storeId 门店ID
     * @param int $productId 商品ID
     * @return int
     */
    public function getStoreProductStock(int $storeId, int $productId): int
    {
        try {
            // 首先查询该商品在指定门店是否存在（通过relation_id字段）
            $storeProductDao = app()->make(\app\dao\product\product\StoreProductDao::class);
            
            // 查找门店商品 - 通过relation_id(门店ID)和原商品ID查找
            $storeProduct = $storeProductDao->getOne([
                'relation_id' => $storeId,
                'type' => 1, // 门店商品类型
                'is_del' => 0,
                'pid' => $productId // 平台商品ID或者是该商品本身
            ], 'id');
            
            // 如果没有找到对应的门店商品，也尝试查找商品本身是否属于该门店
            if (!$storeProduct) {
                $storeProduct = $storeProductDao->getOne([
                    'id' => $productId,
                    'relation_id' => $storeId,
                    'type' => 1,
                    'is_del' => 0
                ], 'id');
            }
            
            if (!$storeProduct) {
                return 0; // 该门店没有这个商品
            }
            
            // 获取门店商品的库存信息
            $attrValues = $this->dao->getProductAttrValues($storeProduct['id']);
            if (empty($attrValues)) {
                return 0;
            }

            // 计算门店商品的总库存
            $totalStock = 0;
            foreach ($attrValues as $attrValue) {
                $totalStock += $attrValue['stock'];
            }

            return $totalStock;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 通过商品ID获取库存信息（支持多规格）
     * @param int $productId 商品ID
     * @param int $storeId 门店ID（可选，如果提供则查询门店商品库存）
     * @return array
     */
    public function getProductStockById(int $productId, int $storeId = 0): array
    {
        try {
            $targetProductId = $productId;
            
            // 如果提供了门店ID，查询门店商品
            if ($storeId > 0) {
                $storeProductDao = app()->make(\app\dao\product\product\StoreProductDao::class);
                
                // 查找门店商品
                $storeProduct = $storeProductDao->getOne([
                    'relation_id' => $storeId,
                    'type' => 1,
                    'is_del' => 0,
                    'pid' => $productId
                ], 'id');
                
                // 如果没有找到代理商品，尝试查找门店自有商品
                if (!$storeProduct) {
                    $storeProduct = $storeProductDao->getOne([
                        'id' => $productId,
                        'relation_id' => $storeId,
                        'type' => 1,
                        'is_del' => 0
                    ], 'id');
                }
                
                if ($storeProduct) {
                    $targetProductId = $storeProduct['id'];
                } else {
                    // 门店没有这个商品
                    return [
                        'product_id' => $productId,
                        'store_id' => $storeId,
                        'is_multi_spec' => false,
                        'total_stock' => 0,
                        'specs' => []
                    ];
                }
            }
            
            // 获取商品的所有规格信息
            $attrValues = $this->dao->getProductAttrValues($targetProductId);
            
            if (empty($attrValues)) {
                return [
                    'product_id' => $productId,
                    'store_id' => $storeId,
                    'is_multi_spec' => false,
                    'total_stock' => 0,
                    'specs' => []
                ];
            }
            
            // 计算总库存
            $totalStock = 0;
            $specs = [];
            
            foreach ($attrValues as $attrValue) {
                $totalStock += $attrValue['stock'];
                $specs[] = [
                    'unique' => $attrValue['unique'],
                    'suk' => $attrValue['suk'] ?: '默认规格',
                    'stock' => $attrValue['stock'],
                    'cost' => $attrValue['cost'],
                    'price' => $attrValue['price'],
                    'sales' => $attrValue['sales'] ?? 0,
                    'image' => $attrValue['image'] ?? '',
                    'bar_code' => $attrValue['bar_code'] ?? '',
                    'stock_status' => $this->getStockStatus($attrValue['stock'])
                ];
            }
            
            // 判断是否为多规格商品
            $isMultiSpec = count($attrValues) > 1;
            
            return [
                'product_id' => $productId,
                'store_id' => $storeId,
                'is_multi_spec' => $isMultiSpec,
                'total_stock' => $totalStock,
                'total_stock_status' => $this->getStockStatus($totalStock),
                'specs' => $specs
            ];
            
        } catch (\Exception $e) {
            return [
                'product_id' => $productId,
                'store_id' => $storeId,
                'is_multi_spec' => false,
                'total_stock' => 0,
                'specs' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取门店选项
     * @return array
     */
    public function getStoreOptions()
    {
        try {
            $storeList = Db::name('system_store')
                ->where('is_del', 0)
                ->where('is_show', 1)
                ->field('id,name,phone,address')
                ->order('id desc')
                ->select()
                ->toArray();

            $options = [['value' => '', 'label' => '全部门店']];
            foreach ($storeList as $store) {
                $options[] = [
                    'value' => $store['id'],
                    'label' => $store['name'],
                    'phone' => $store['phone'],
                    'address' => $store['address'],
                ];
            }
            
            return $options;
        } catch (\Exception $e) {
            // 如果查询失败，返回默认选项
            return [['value' => '', 'label' => '全部门店']];
        }
    }

    /**
     * 获取商品选项（商品分类）
     * @return array
     */
    public function getProductOptions()
    {
        try {
            $categoryList = Db::name('store_product_category')
                ->where('is_del', 0)
                ->where('is_show', 1)
                ->field('id,cate_name,pid')
                ->order('sort desc,id desc')
                ->select()
                ->toArray();

            $options = [['value' => '', 'label' => '全部分类']];
            foreach ($categoryList as $category) {
                $options[] = [
                    'value' => $category['id'],
                    'label' => $category['cate_name'],
                    'pid' => $category['pid'],
                ];
            }
            
            return $options;
        } catch (\Exception $e) {
            // 如果查询失败，返回默认选项
            return [['value' => '', 'label' => '全部分类']];
        }
    }

    /**
     * 获取商品销售统计
     * @param array $where 查询条件
     * @return array
     */
    public function getSalesStatistics(array $where = [])
    {
        [$page, $limit] = $this->getPageValue();
        
        try {
            // 构建基础查询
            $query = Db::name('store_product_attr_value')
                ->alias('pav')
                ->leftJoin('store_product p', 'pav.product_id = p.id')
                ->leftJoin('system_store s', 'p.relation_id = s.id')
                ->where('p.is_del', 0)
                ->where('p.is_show', 1);
            
            // 商品名称搜索
            if (!empty($where['keyword'])) {
                $query->where('p.store_name', 'like', '%' . $where['keyword'] . '%');
            }
            
            // 门店筛选
            if (!empty($where['store_id'])) {
                $query->where('p.relation_id', $where['store_id']);
            }
            
            // 商品分类筛选
            if (!empty($where['cate_id'])) {
                $query->leftJoin('store_product_cate pc', 'p.id = pc.product_id')
                      ->where('pc.cate_id', $where['cate_id']);
            }
            
            // 获取商品基础信息
            $productList = $query->field([
                'pav.unique',
                'pav.product_id',
                'pav.stock as current_stock',
                'pav.cost',
                'pav.price',
                'pav.suk as spec_name',
                'p.store_name as product_name',
                'p.image as product_image',
                'p.unit_name',
                's.name as store_name',
                's.id as store_id'
            ])
            ->page($page, $limit)
            ->order('pav.product_id desc')
            ->select()
            ->toArray();
            
            // 获取总数
            $count = (clone $query)->count();
            
            // 计算销售统计数据
            foreach ($productList as &$item) {
                $salesData = $this->calculateSalesData($item['unique'], $where);
                $item['sales_quantity'] = $salesData['quantity'];
                $item['sales_amount'] = $salesData['amount'];
                $item['spec_name'] = $item['spec_name'] ?: '默认规格';
                $item['unit_name'] = $item['unit_name'] ?: '件';
                $item['store_name'] = $item['store_name'] ?: '平台';
            }
            
            return [
                'list' => $productList,
                'count' => $count
            ];
            
        } catch (\Exception $e) {
            // 返回空数据，避免页面报错
            return [
                'list' => [],
                'count' => 0
            ];
        }
    }
    
    /**
     * 计算商品销售数据
     * @param string $unique 商品规格唯一标识
     * @param array $where 查询条件
     * @return array
     */
    private function calculateSalesData(string $unique, array $where = [])
    {
        try {
            $query = Db::name('store_order_cart_info')
                ->alias('ci')
                ->leftJoin('store_order o', 'ci.oid = o.id')
                ->where('ci.sku_unique', $unique)
                ->where('o.paid', 1) // 已支付订单
                ->where('o.is_del', 0)
                ->where('o.refund_status', '<>', 2); // 排除全额退款订单
            
            // 日期筛选
            if (!empty($where['start_date']) && !empty($where['end_date'])) {
                $startTime = strtotime($where['start_date'] . ' 00:00:00');
                $endTime = strtotime($where['end_date'] . ' 23:59:59');
                $query->where('o.pay_time', 'between', [$startTime, $endTime]);
            }
            
            $result = $query->field([
                'SUM(ci.cart_num) as total_quantity',
                'SUM(ci.cart_num * ci.truePrice) as total_amount'
            ])->find();
            
            return [
                'quantity' => intval($result['total_quantity'] ?? 0),
                'amount' => floatval($result['total_amount'] ?? 0)
            ];
            
        } catch (\Exception $e) {
            return [
                'quantity' => 0,
                'amount' => 0
            ];
        }
    }
    
    /**
     * 导出销售统计数据
     * @param array $where 查询条件
     * @return array
     */
    public function exportSalesData(array $where = [])
    {
        try {
            // 1. 获取所有门店用于构建动态列
            $stores = Db::name('system_store')
                ->where('is_del', 0)
                ->where('is_show', 1)
                ->when(!empty($where['store_id']), function ($query) use ($where) {
                    $query->where('id', $where['store_id']);
                })
                ->field('id, name')
                ->select()
                ->toArray();

            if (empty($stores)) {
                return [
                    'export' => [],
                    'filename' => '商品销售统计_' . date('YmdHis'),
                    'filekey' => [],
                    'header' => []
                ];
            }

            // 2. 构建表头和字段键
            $header = ['商品编号', '商品名称', '规格型号'];
            $filekey = ['product_code', 'product_name', 'spec'];

            foreach ($stores as $store) {
                $header[] = $store['name'] . '-当前库存';
                $header[] = $store['name'] . '-销售数量';
                $header[] = $store['name'] . '-销售金额';
                $filekey[] = 'store_' . $store['id'] . '_stock';
                $filekey[] = 'store_' . $store['id'] . '_quantity';
                $filekey[] = 'store_' . $store['id'] . '_amount';
            }

            // 3. 查询所有符合条件的SKU作为报表主体
            $skuQuery = Db::name('store_product_attr_value')->alias('pav')
                ->join('store_product p', 'p.id = pav.product_id')
                ->where('p.is_show', 1)
                ->where('p.is_del', 0);

            if(!empty($where['keyword'])) {
                 $skuQuery->where('p.store_name|pav.suk', 'like', '%' . $where['keyword'] . '%');
            }

            if (isset($where['sort']) && $where['sort'] == 102) {
                $skuQuery->where('p.sort', 102);
            } else {
                $skuQuery->where('p.type', 0);
            }

            $skus = $skuQuery
                ->field('p.id as product_code, p.store_name as product_name, pav.suk as spec, pav.unique, pav.stock')
                ->select()
                ->toArray();

            if (empty($skus)) {
                return [
                    'export' => [],
                    'filename' => '商品销售统计_' . date('YmdHis'),
                    'filekey' => $filekey,
                    'header' => $header
                ];
            }

            // 4. 一次性查询所有相关SKU在所有门店的销售数据
            $skuUniques = array_column($skus, 'unique');
            $salesQuery = Db::name('store_order_cart_info')
                ->alias('ci')
                ->join('store_order o', 'ci.oid = o.id')
                ->whereIn('ci.sku_unique', $skuUniques)
                ->where('o.paid', 1)
                ->where('o.is_del', 0)
                ->where('o.refund_status', '<>', 2);

            if (!empty($where['start_date']) && !empty($where['end_date'])) {
                $startTime = strtotime($where['start_date'] . ' 00:00:00');
                $endTime = strtotime($where['end_date'] . ' 23:59:59');
                $salesQuery->whereBetween('o.pay_time', [$startTime, $endTime]);
            }

            $allSalesData = $salesQuery
                ->group('ci.sku_unique, o.store_id')
                ->field([
                    'ci.sku_unique as `unique`',
                    'o.store_id',
                    'SUM(ci.cart_num) as unit_num',
                    \think\facade\Db::raw('SUM(CAST(JSON_UNQUOTE(JSON_EXTRACT(ci.cart_info, \'$.sum_true_price\')) AS DECIMAL(10,2))) as amount')
                ])
                ->select()
                ->toArray();

            // 5. 组装最终返回数据
            $exportData = [];
            foreach ($skus as $sku) {
                $row = [
                    'product_code' => $sku['product_code'],
                    'product_name' => $sku['product_name'],
                    'spec' => $sku['spec'] ?: '默认',
                ];

                foreach ($stores as $store) {
                    $sales = null;
                    foreach($allSalesData as $sale) {
                        if ($sale['unique'] == $sku['unique'] && $sale['store_id'] == $store['id']) {
                            $sales = $sale;
                            break;
                        }
                    }
                    $unit_num = intval($sales['unit_num'] ?? 0);
                    $amount = floatval($sales['amount'] ?? 0);

                    $row['store_' . $store['id'] . '_stock'] = $sku['stock'];
                    $row['store_' . $store['id'] . '_quantity'] = $unit_num;
                    $row['store_' . $store['id'] . '_amount'] = $amount;
                }
                $exportData[] = $row;
            }

            return [
                'export' => $exportData,
                'filename' => '商品销售统计_' . date('YmdHis'),
                'filekey' => $filekey,
                'header' => $header
            ];

        } catch (\Exception $e) {
            return [
                'export' => [],
                'filename' => '商品销售统计_' . date('YmdHis'),
                'filekey' => [],
                'header' => []
            ];
        }
    }

    /**
     * 获取ERP格式的多门店销售统计数据
     * @param array $where 查询条件
     * @return array
     */
    public function getErpInventoryData(array $where = [])
    {
        try {
            [$page, $limit] = $this->getPageValue();

            // 1. 获取所有门店用于构建动态列
            $stores = Db::name('system_store')
                ->where('is_del', 0)
                ->where('is_show', 1)
                ->when(!empty($where['store_id']), function ($query) use ($where) {
                    $query->where('id', $where['store_id']);
                })
                ->field('id, name')
                ->select()
                ->toArray();

            // 2. 构建多级动态表头
            $columns = [
                ['key' => 'product_code', 'title' => '商品编号', 'fixed' => 'left', 'minWidth' => 120],
                ['key' => 'product_name', 'title' => '商品名称', 'fixed' => 'left', 'minWidth' => 200],
                ['key' => 'spec', 'title' => '规格型号', 'minWidth' => 150],
            ];
            foreach ($stores as $store) {
                $columns[] = [
                    'title' => $store['name'],
                    'align' => 'center',
                    'children' => [
                        ['key' => 'stock_' . $store['id'], 'title' => '当前库存', 'minWidth' => 100],
                        ['key' => 'sales_num_' . $store['id'], 'title' => '销售数量', 'minWidth' => 100],
                        ['key' => 'sales_amount_' . $store['id'], 'title' => '销售金额', 'minWidth' => 100],
                    ]
                ];
            }

            // 3. 分页查询平台商品的SKU作为报表主体
            $skuQuery = Db::name('store_product_attr_value')->alias('pav')
                ->join('store_product p', 'p.id = pav.product_id')
                ->where('p.type', 0)
                ->where('p.sort', 102)
                ->where('p.is_show', 1)
                ->where('p.is_del', 0);

            if (!empty($where['keyword'])) {
                $skuQuery->where('p.store_name|pav.suk', 'like', '%' . $where['keyword'] . '%');
            }
            
            $total = (clone $skuQuery)->count();
            if ($total == 0) {
                return ['total' => 0, 'columns' => $columns, 'data' => []];
            }

            $paginatedSkus = (clone $skuQuery)
                ->field('p.id as product_code, p.store_name, p.unit_name, pav.suk as spec, pav.unique, pav.stock')
                ->page($page, $limit)
                ->select()
                ->toArray();

            // 4. 一次性查询所有相关SKU在所有门店的销售数据
            $skuUniques = array_column($paginatedSkus, 'unique');
            $salesQuery = Db::name('store_order_cart_info')
                ->alias('ci')
                ->join('store_order o', 'ci.oid = o.id')
                ->whereIn('ci.sku_unique', $skuUniques)
                ->where('o.paid', 1)
                ->where('o.is_del', 0)
                ->where('o.refund_status', '<>', 2);

            if (!empty($where['start_date']) && !empty($where['end_date'])) {
                $startTime = strtotime($where['start_date'] . ' 00:00:00');
                $endTime = strtotime($where['end_date'] . ' 23:59:59');
                $salesQuery->whereBetween('o.pay_time', [$startTime, $endTime]);
            }

            $allSalesData = $salesQuery
                ->group('ci.sku_unique, o.store_id')
                ->field([
                    'ci.sku_unique as `unique`',
                    'o.store_id',
                    'SUM(ci.cart_num) as unit_num',
                    \think\facade\Db::raw('SUM(CAST(JSON_UNQUOTE(JSON_EXTRACT(ci.cart_info, \'$.sum_true_price\')) AS DECIMAL(10,2))) as amount')
                ])
                ->select()
                ->toArray();
            
            // 5. 组装最终返回数据
            $data = [];
            foreach ($paginatedSkus as $sku) {
                $row = [
                    'product_code' => $sku['product_code'],
                    'product_name' => $sku['store_name'],
                    'spec' => $sku['spec'],
                ];

                foreach ($stores as $store) {
                    $sales = null;
                    foreach($allSalesData as $sale) {
                        if ($sale['unique'] == $sku['unique'] && $sale['store_id'] == $store['id']) {
                            $sales = $sale;
                            break;
                        }
                    }
                    $unit_num = intval($sales['unit_num'] ?? 0);

                    $amount = floatval($sales['amount'] ?? 0);

                    $row['stock_' . $store['id']] = $sku['stock'];
                    $row['sales_num_' . $store['id']] = $unit_num; 
                    $row['sales_amount_' . $store['id']] = $amount;
                }
                $data[] = $row;
            }

            return [
                'total' => $total,
                'columns' => $columns,
                'data' => $data,
            ];
        } catch (\Throwable $e) {
            \think\facade\Log::error("GetErpInventoryData Failed: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e; // 继续向上抛出异常，让控制器能捕获到
        }
    }

    /**
     * 导出ERP格式的销售统计数据
     * @param array $where
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function exportErpInventoryData(array $where)
    {
        // 1. 获取所有门店用于构建动态列
        $stores = Db::name('system_store')
            ->where('is_del', 0)
            ->where('is_show', 1)
             ->when(!empty($where['store_id']), function ($query) use ($where) {
                 $query->where('id', $where['store_id']);
             })
            ->field('id, name')
            ->select()
            ->toArray();

        // 2. 构建动态表头
        $header = ['商品编号', '商品名称', '规格型号'];
        $childHeader = [];
        foreach ($stores as $store) {
            $childHeader[] = [
                'title' => $store['name'],
                'children' => [
                    ['title' => '当前库存'],
                    ['title' => '销售数量'],
                    ['title' => '销售金额'],
                ]
            ];
        }
        $header[] = ['title' => '各门店销售数据', 'children' => $childHeader];

        // 3. 查询所有符合条件的SKU作为报表主体
        $skuQuery = Db::name('store_product_attr_value')->alias('pav')
            ->join('store_product p', 'p.id = pav.product_id')
            ->where('p.is_show', 1)
            ->where('p.is_del', 0);
        if(!empty($where['keyword'])) {
             $skuQuery->where('p.store_name|pav.suk', 'like', '%' . $where['keyword'] . '%');
        }
        if (isset($where['sort']) && $where['sort'] == 102) {
            $skuQuery->where('p.sort', 102);
        } else {
            $skuQuery->where('p.type', 0);
        }

        $skus = $skuQuery
            ->field('p.id as product_code, p.store_name, pav.suk as spec, pav.unique, pav.stock')
            ->select()
            ->toArray();

        // 4. 一次性查询所有相关SKU在所有门店的销售数据
        $skuUniques = array_column($skus, 'unique');
        $salesQuery = Db::name('store_order_cart_info')
            ->alias('ci')
            ->join('store_order o', 'ci.oid = o.id')
            ->whereIn('ci.sku_unique', $skuUniques)
            ->where('o.paid', 1)
            ->where('o.is_del', 0)
            ->where('o.refund_status', '<>', 2);

        if (!empty($where['start_date']) && !empty($where['end_date'])) {
            $startTime = strtotime($where['start_date'] . ' 00:00:00');
            $endTime = strtotime($where['end_date'] . ' 23:59:59');
            $salesQuery->whereBetween('o.pay_time', [$startTime, $endTime]);
        }

        $allSalesData = $salesQuery
            ->group('ci.sku_unique, o.store_id')
            ->field([
                'ci.sku_unique as `unique`',
                'o.store_id',
                'SUM(ci.cart_num) as unit_num',
                \think\facade\Db::raw('SUM(CAST(JSON_UNQUOTE(JSON_EXTRACT(ci.cart_info, \'$.sum_true_price\')) AS DECIMAL(10,2))) as amount')
            ])
            ->select()
            ->toArray();

        // 5. 组装最终返回数据
        $exportData = [];
        foreach ($skus as $sku) {
            $row = [];
            $row[] = $sku['product_code'];
            $row[] = $sku['store_name'];
            $row[] = $sku['spec'];

            foreach ($stores as $store) {
                $saleInfo = $allSalesData[$sku['unique']][$store['id']] ?? null;
                $unit_num = $saleInfo ? (int)$saleInfo['unit_num'] : 0;
                $amount = $saleInfo ? (float)$saleInfo['amount'] : 0;

                // 直接从product_stock表获取库存
                $stock = $stockData[$sku['unique']][$store['id']] ?? 0;
                
                $row[] = $stock;
                $row[] = $unit_num;
                $row[] = $amount;
            }
            $exportData[] = $row;
        }

        SpreadsheetExcelService::instance()->setExcelHeader($header)->setExcelContent($exportData)->excelSave('商品销售统计' . '_' . date('Y_m_d'));
    }
} 