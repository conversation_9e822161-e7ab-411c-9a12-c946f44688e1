{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_comb_data.vue?vue&type=template&id=4ab71983&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_comb_data.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<div class=\"c_radio mb15\" v-if=\"configData\">\n  <div class=\"c_row-item\" :class=\"{on:configData.type=='ranges'}\">\n    <Col class=\"c_label on\" :span=\"configData.type=='ranges'?'':4\">\n      {{configData.title}}\n    </Col>\n    <Col class=\"color-box\" :span=\"configData.type=='ranges'?24:19\">\n      <div>\n        <RadioGroup v-model=\"configData.tabVal\" @on-change=\"radioChange($event)\">\n          <Radio :label=\"key\" v-for=\"(radio,key) in configData.tabList\" :key=\"key\">\n            <span>{{radio.name}}</span>\n          </Radio>\n        </RadioGroup>\n      </div>\n      <div>\n        <RadioGroup v-model=\"configData.tabData\" @on-change=\"radioDataChange($event)\" v-if=\"configData.tabVal==0\">\n          <Radio :label=\"key\" v-for=\"(radio,key) in configData.dataList\" :key=\"key+'data'\">\n            <span>{{radio.name}}</span>\n          </Radio>\n        </RadioGroup>\n      </div>\n      <DatePicker type=\"date\" v-model=\"configData.specifyDate\" placeholder=\"请选择\" style=\"margin-top: 6px;\" v-if=\"configData.tabData==1 && configData.tabVal==0 && configData.type == 'data'\" />\n      <TimePicker type=\"time\" format=\"HH:mm\" v-model=\"configData.specifyDate\" placeholder=\"请选择\" style=\"margin-top: 6px;\" v-else-if=\"configData.tabData==1 && configData.tabVal==0 && configData.type == 'time'\" />\n      <DatePicker type=\"daterange\" placement=\"bottom-end\" v-model=\"configData.specifyDate\" format=\"yyyy/MM/dd\" placeholder=\"请选择\" style=\"margin-top: 6px;\" v-else-if=\"configData.tabData==1 && configData.tabVal==0 && configData.type == 'daterange'\" @on-change=\"getDaterange\"/>\n      <TimePicker format=\"HH:mm\" type=\"timerange\" v-model=\"configData.specifyDate\" placement=\"bottom-end\" placeholder=\"请选择\" style=\"margin-top: 6px;\" v-else-if=\"configData.tabData==1 && configData.tabVal==0 && configData.type == 'timerange'\" />\n    </Col>\n  </div>\n</div>\n", null]}