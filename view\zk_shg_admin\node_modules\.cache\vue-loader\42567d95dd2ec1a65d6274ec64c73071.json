{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue?vue&type=template&id=621824e8&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\order\\invoice\\index.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\",\"padding\":0}},[_c('div',{staticClass:\"new_card_pd\"},[_c('Form',{ref:\"orderData\",staticClass:\"tabform\",attrs:{\"model\":_vm.orderData,\"label-width\":_vm.labelWidth,\"inline\":\"\",\"label-position\":_vm.labelPosition},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('FormItem',{attrs:{\"label\":\"创建时间：\"}},[_c('DatePicker',{staticClass:\"input-add\",attrs:{\"editable\":false,\"value\":_vm.timeVal,\"format\":\"yyyy/MM/dd HH:mm\",\"type\":\"datetimerange\",\"placement\":\"bottom-start\",\"placeholder\":\"自定义时间\",\"options\":_vm.options},on:{\"on-change\":_vm.onchangeTime}})],1),_c('FormItem',{attrs:{\"label\":\"搜索：\",\"prop\":\"real_name\",\"label-for\":\"real_name\"}},[_c('Input',{staticClass:\"input-add\",attrs:{\"placeholder\":\"请输入\",\"element-id\":\"name\"},model:{value:(_vm.orderData.real_name),callback:function ($$v) {_vm.$set(_vm.orderData, \"real_name\", $$v)},expression:\"orderData.real_name\"}},[_c('Select',{staticStyle:{\"width\":\"80px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\",model:{value:(_vm.orderData.field_key),callback:function ($$v) {_vm.$set(_vm.orderData, \"field_key\", $$v)},expression:\"orderData.field_key\"}},[_c('Option',{attrs:{\"value\":\"all\"}},[_vm._v(\"全部\")]),_c('Option',{attrs:{\"value\":\"order_id\"}},[_vm._v(\"订单号\")]),_c('Option',{attrs:{\"value\":\"uid\"}},[_vm._v(\"UID\")]),_c('Option',{attrs:{\"value\":\"real_name\"}},[_vm._v(\"用户姓名\")]),_c('Option',{attrs:{\"value\":\"user_phone\"}},[_vm._v(\"用户电话\")])],1)],1)],1),_c('FormItem',[_c('Button',{staticClass:\"btn-add\",attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.orderSearch()}}},[_vm._v(\"查询\")]),_c('Button',{on:{\"click\":_vm.exports}},[_vm._v(\"导出\")])],1)],1)],1)]),_c('Card',{staticClass:\"ivu-mt\",attrs:{\"bordered\":false,\"dis-hover\":\"\"}},[_c('Table',{ref:\"table\",staticClass:\"mt25\",attrs:{\"columns\":_vm.columns,\"data\":_vm.orderList,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-userFrom-text\":\"暂无数据\",\"no-filtered-userFrom-text\":\"暂无筛选结果\"},scopedSlots:_vm._u([{key:\"pay_price\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',[_vm._v(\"¥ \"+_vm._s(row.pay_price))])]}},{key:\"type\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.type===1)?_c('div',[_vm._v(\"电子普通发票\")]):_c('div',[_vm._v(\"纸质专用发票\")])]}},{key:\"is_invoice\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.is_invoice===1)?_c('Tag',{attrs:{\"color\":\"orange\",\"size\":\"medium\"}},[_vm._v(\"已开票\")]):_c('Tag',{attrs:{\"color\":\"green\",\"size\":\"medium\"}},[_vm._v(\"未开票\")])]}},{key:\"status\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.refund_status)?[(row.refund_status===1)?_c('div',[_vm._v(\"退款中\")]):(row.refund_status===2)?_c('div',[_vm._v(\"已退款\")]):_vm._e()]:[(row.status===0)?_c('Tag',{attrs:{\"color\":\"green\",\"size\":\"medium\"}},[_vm._v(\"未发货\")]):(row.status===1)?_c('Tag',{attrs:{\"color\":\"blue\",\"size\":\"medium\"}},[_vm._v(\"待收货\")]):(row.status===2)?_c('Tag',{attrs:{\"color\":\"orange\",\"size\":\"medium\"}},[_vm._v(\"待评价\")]):(row.status===3)?_c('Tag',{attrs:{\"color\":\"volcano\",\"size\":\"medium\"}},[_vm._v(\"已完成\")]):_vm._e()]]}},{key:\"header_type\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.header_type===1)?_c('div',[_vm._v(\"个人\")]):_c('div',[_vm._v(\"企业\")])]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{attrs:{\"disabled\":row.refund_status !== 0},on:{\"click\":function($event){return _vm.edit(row)}}},[_vm._v(\"编辑\")]),_c('Divider',{attrs:{\"type\":\"vertical\"}}),_c('a',{on:{\"click\":function($event){return _vm.orderInfo(row.id)}}},[_vm._v(\"订单信息\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.total,\"current\":_vm.orderData.page,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.orderData.limit},on:{\"on-change\":_vm.pageChange}})],1)],1),_c('Modal',{staticClass:\"order_box\",attrs:{\"scrollable\":\"\",\"title\":\"发票详情\",\"width\":\"700\",\"footer-hide\":\"\"},on:{\"on-cancel\":_vm.cancel},model:{value:(_vm.invoiceShow),callback:function ($$v) {_vm.invoiceShow=$$v},expression:\"invoiceShow\"}},[_c('Form',{ref:\"formInline\",attrs:{\"model\":_vm.formInline,\"label-width\":100},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[(_vm.invoiceDetails.header_type===1 && _vm.invoiceDetails.type===1)?_c('div',[_c('div',{staticClass:\"list\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"发票信息\")]),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票抬头: \"),_c('span',{staticClass:\"info\"},[_vm._v(_vm._s(_vm.invoiceDetails.name))])]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票类型: \"),_c('span',{staticClass:\"info\"},[_vm._v(\"电子普通发票\")])])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票抬头类型: 个人\")]),(_vm.invoiceDetails.is_invoice === 1)?_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票金额: ￥\"+_vm._s(_vm.invoiceDetails.pay_price))]):_vm._e()],1)],1),_c('div',{staticClass:\"list\"},[_c('div',{staticClass:\"title row\"},[_vm._v(\"联系信息\")]),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"真实姓名: \"+_vm._s(_vm.invoiceDetails.name))]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"联系电话: \"+_vm._s(_vm.invoiceDetails.drawer_phone))])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"联系邮箱: \"+_vm._s(_vm.invoiceDetails.email))])],1)],1)]):_vm._e(),(_vm.invoiceDetails.header_type===2 && _vm.invoiceDetails.type===1)?_c('div',[_c('div',{staticClass:\"list\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"发票信息\")]),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票抬头: \"),_c('span',{staticClass:\"info\"},[_vm._v(_vm._s(_vm.invoiceDetails.name))])]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"企业税号: \"),_c('span',{staticClass:\"info\"},[_vm._v(_vm._s(_vm.invoiceDetails.duty_number))])])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票类型: 电子普通发票\")]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票抬头类型: 企业\")])],1)],1),_c('div',{staticClass:\"list\"},[_c('div',{staticClass:\"title row\"},[_vm._v(\"联系信息\")]),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"真实姓名: \"+_vm._s(_vm.invoiceDetails.name))]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"联系电话: \"+_vm._s(_vm.invoiceDetails.drawer_phone))])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"联系邮箱: \"+_vm._s(_vm.invoiceDetails.email))])],1)],1)]):_vm._e(),(_vm.invoiceDetails.header_type===2 && _vm.invoiceDetails.type===2)?_c('div',[_c('div',{staticClass:\"list\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"发票信息\")]),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票抬头: \"),_c('span',{staticClass:\"info\"},[_vm._v(_vm._s(_vm.invoiceDetails.name))])]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"企业税号: \"),_c('span',{staticClass:\"info\"},[_vm._v(_vm._s(_vm.invoiceDetails.duty_number))])])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票类型: 纸质专用发票\")]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"发票抬头类型: 企业\")])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"开户银行: \"),_c('span',{staticClass:\"info\"},[_vm._v(_vm._s(_vm.invoiceDetails.bank))])]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"银行账号: \"),_c('span',{staticClass:\"info\"},[_vm._v(_vm._s(_vm.invoiceDetails.card_number))])])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"企业地址: \"+_vm._s(_vm.invoiceDetails.address))]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"企业电话: \"+_vm._s(_vm.invoiceDetails.tell))])],1)],1),_c('div',{staticClass:\"list\"},[_c('div',{staticClass:\"title row\"},[_vm._v(\"联系信息\")]),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"真实姓名: \"+_vm._s(_vm.invoiceDetails.name))]),_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"联系电话: \"+_vm._s(_vm.invoiceDetails.drawer_phone))])],1),_c('Row',{staticClass:\"row\"},[_c('Col',{attrs:{\"span\":\"12\"}},[_vm._v(\"联系邮箱: \"+_vm._s(_vm.invoiceDetails.email))])],1)],1)]):_vm._e(),_c('FormItem',{staticStyle:{\"margin-top\":\"14px\"},attrs:{\"label\":\"开票状态：\"}},[_c('RadioGroup',{on:{\"on-change\":function($event){return _vm.kaiInvoice(_vm.formInline.is_invoice)}},model:{value:(_vm.formInline.is_invoice),callback:function ($$v) {_vm.$set(_vm.formInline, \"is_invoice\", $$v)},expression:\"formInline.is_invoice\"}},[_c('Radio',{attrs:{\"label\":1}},[_vm._v(\"已开票\")]),_c('Radio',{attrs:{\"label\":0}},[_vm._v(\"未开票\")]),_c('Radio',{attrs:{\"label\":-1}},[_vm._v(\"已拒绝\")])],1)],1),(_vm.formInline.is_invoice===1)?_c('FormItem',{attrs:{\"label\":\"发票编号：\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入发票编号\"},model:{value:(_vm.formInline.invoice_number),callback:function ($$v) {_vm.$set(_vm.formInline, \"invoice_number\", $$v)},expression:\"formInline.invoice_number\"}})],1):_vm._e(),(_vm.formInline.is_invoice===1)?_c('FormItem',{attrs:{\"label\":\"发票金额：\"}},[_c('Input',{attrs:{\"placeholder\":\"请输入发票金额\"},on:{\"input\":_vm.inputEnter,\"blur\":_vm.inputBlur},model:{value:(_vm.formInline.invoice_amount),callback:function ($$v) {_vm.$set(_vm.formInline, \"invoice_amount\", $$v)},expression:\"formInline.invoice_amount\"}})],1):_vm._e(),(_vm.formInline.is_invoice)?_c('FormItem',{attrs:{\"label\":\"发票备注：\"}},[_c('Input',{attrs:{\"value\":\"备注\",\"type\":\"textarea\",\"autosize\":{minRows: 2,maxRows: 5},\"placeholder\":\"请输入发票备注\"},model:{value:(_vm.formInline.remark),callback:function ($$v) {_vm.$set(_vm.formInline, \"remark\", $$v)},expression:\"formInline.remark\"}})],1):_vm._e(),_c('Button',{staticClass:\"ok-btn\",attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.handleSubmit()}}},[_vm._v(\"确定\")])],1)],1),_c('Modal',{staticClass:\"order_box\",attrs:{\"scrollable\":\"\",\"title\":\"订单详情\",\"footer-hide\":\"\",\"width\":\"700\"},model:{value:(_vm.orderShow),callback:function ($$v) {_vm.orderShow=$$v},expression:\"orderShow\"}},[(_vm.orderShow)?_c('orderDetall',{attrs:{\"orderId\":_vm.orderId},on:{\"detall\":_vm.detall}}):_vm._e()],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}