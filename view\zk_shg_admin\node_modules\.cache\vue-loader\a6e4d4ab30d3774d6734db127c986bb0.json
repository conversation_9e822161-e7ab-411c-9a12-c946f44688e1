{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\index.vue?vue&type=template&id=fbc047d4&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\grade\\card\\index.vue", "mtime": 1676971396000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 用户-付费会员-卡密会员 -->\n  <div>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n      <div class=\"new_card_pd\">\n        <Form\n        :model=\"gradeFrom\"\n        inline\n        :label-width=\"labelWidth\"\n        :label-position=\"labelPosition\"\n        @submit.native.prevent\n      >\n        <FormItem label=\"批次名称：\" label-for=\"title\">\n          <Input\n            v-model=\"gradeFrom.title\"\n            placeholder=\"请输入批次名称\"\n            class=\"input-add mr14\"\n          />\n           <Button type=\"primary\" @click=\"userSearchs()\">查询</Button>\n        </FormItem>\n        \n      </Form>\n      </div>\n    </Card>\n    <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n      <Button type=\"primary\" @click=\"addBatch\" class=\"mr20\">添加批次</Button>\n      <Button @click=\"getMemberScan\">下载二维码</Button>\n      <Table\n        class=\"mt25\"\n        :columns=\"columns\"\n        :data=\"tbody\"\n        :loading=\"loading\"\n        highlight-row\n        no-userFrom-text=\"暂无数据\"\n        no-filtered-userFrom-text=\"暂无筛选结果\"\n      >\n        <template slot-scope=\"{ row }\" slot=\"status\">\n          <i-switch\n            v-model=\"row.status\"\n            :value=\"row.status\"\n            :true-value=\"1\"\n            :false-value=\"0\"\n            @on-change=\"onchangeIsShow(row)\"\n            size=\"large\"\n          >\n            <span slot=\"open\">激活</span>\n            <span slot=\"close\">冻结</span>\n          </i-switch>\n        </template>\n        <template slot-scope=\"{ row, index }\" slot=\"action\">\n          <template>\n            <Dropdown @on-click=\"changeMenu(row, $event, index)\">\n              <a href=\"javascript:void(0)\">\n                更多\n                <Icon type=\"ios-arrow-down\"></Icon>\n              </a>\n              <DropdownMenu slot=\"list\">\n                <DropdownItem name=\"1\">编辑批次名</DropdownItem>\n                <DropdownItem name=\"2\">查看卡列表</DropdownItem>\n                <DropdownItem name=\"3\">导出</DropdownItem>\n              </DropdownMenu>\n            </Dropdown>\n          </template>\n        </template>\n      </Table>\n      <div class=\"acea-row row-right page\">\n        <Page\n          :total=\"total\"\n          :current=\"gradeFrom.page\"\n          :page-size=\"gradeFrom.limit\"\n          show-elevator\n          show-total\n          @on-change=\"pageChange\"\n        />\n      </div>\n    </Card>\n    <Modal v-model=\"modal\" title=\"添加批次\" footer-hide class-name=\"vertical-center-modal\">\n      <form-create\n        v-model=\"fapi\"\n        :rule=\"rule\"\n        @on-submit=\"onSubmit\"\n      ></form-create>\n    </Modal>\n    <Modal v-model=\"modal2\" title=\"编辑批次名\" footer-hide>\n      <form-create :rule=\"rule2\" @on-submit=\"onSubmit2\"></form-create>\n    </Modal>\n    <Modal v-model=\"modal3\" title=\"二维码\" footer-hide>\n      <div v-if=\"qrcode\" class=\"acea-row row-around\">\n        <div\n          v-if=\"qrcode && qrcode.wechat_img\"\n          class=\"acea-row row-column-around row-between-wrapper\"\n        >\n          <div v-viewer class=\"QRpic\">\n            <img v-lazy=\"qrcode.wechat_img\" />\n          </div>\n          <span class=\"mt10\">公众号二维码</span>\n        </div>\n        <div\n          v-if=\"qrcode && qrcode.routine\"\n          class=\"acea-row row-column-around row-between-wrapper\"\n        >\n          <div v-viewer class=\"QRpic\">\n            <img v-lazy=\"qrcode.routine\" />\n          </div>\n          <span class=\"mt10\">小程序二维码</span>\n        </div>\n      </div>\n      <Spin v-else></Spin>\n    </Modal>\n  </div>\n", null]}