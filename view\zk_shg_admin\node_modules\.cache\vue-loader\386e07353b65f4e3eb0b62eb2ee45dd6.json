{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeSeckill\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeSeckill\\index.vue", "mtime": 1683254540000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapState } from \"vuex\";\nimport {\n  seckillProductList,\n  seckillProductStatus,\n  storeSeckillApi,\n} from \"@/api/marketing\";\nimport { formatDate } from \"@/utils/validate\";\nimport exportExcel from \"@/utils/newToExcel.js\";\nimport Setting from '@/setting';\nexport default {\n  name: \"storeSeckill\",\n  filters: {\n    formatDate(time) {\n      if (time !== 0) {\n        let date = new Date(time * 1000);\n        return formatDate(date, \"yyyy-MM-dd\");\n      }\n    },\n  },\n  data() {\n    return {\n      roterPre: Setting.roterPre,\n      loading: false,\n      columns1: [\n        {\n          title: \"ID\",\n          key: \"id\",\n          width: 80,\n        },\n        {\n          title: \"商品图片\",\n          slot: \"image\",\n          minWidth: 90,\n        },\n        // {\n        //   title: \"秒杀名称\",\n        //   slot: \"title\",\n        //   minWidth: 150,\n        // },\n        // {\n        //   title: \"秒杀简介\",\n        //   slot: \"info\",\n        //   minWidth: 150,\n        // },\n        {\n          title: \"原价\",\n          key: \"ot_price\",\n          minWidth: 100,\n        },\n        {\n          title: \"秒杀价\",\n          key: \"price\",\n          minWidth: 100,\n        },\n        {\n          title: \"限量\",\n          key: \"quota_show\",\n          minWidth: 80,\n        },\n        {\n          title: \"限量剩余\",\n          key: \"quota\",\n          minWidth: 80,\n        },\n        {\n          title: \"活动名称\",\n          key: \"activity_name\",\n          minWidth: 100,\n        },\n        {\n          title: \"活动状态\",\n          slot: \"start_name\",\n          minWidth: 100,\n        },\n        {\n          title: \"结束时间\",\n          slot: \"stop_time\",\n          minWidth: 130,\n        },\n        {\n          title: \"状态\",\n          slot: \"status\",\n          minWidth: 100,\n        },\n        {\n          title: \"操作\",\n          slot: \"action\",\n          fixed: \"right\",\n          width: 100,\n        },\n      ],\n      tableList: [],\n      grid: {\n        xl: 7,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24,\n      },\n      tableFrom: {\n        start_status: \"\",\n        status: \"\",\n        store_name: \"\",\n        page: 1,\n        limit: 15,\n      },\n      total: 0,\n    };\n  },\n  computed: {\n    ...mapState(\"admin/layout\", [\"isMobile\"]),\n    labelWidth() {\n      return this.isMobile ? undefined : 96;\n    },\n    labelPosition() {\n      return this.isMobile ? \"top\" : \"right\";\n    },\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 添加\n    add() {\n      this.$router.push({ path: this.roterPre + \"/marketing/store_seckill/create\" });\n    },\n    // 数据导出；\n    async exports() {\n      let [th, filekey, data, fileName] = [[], [], [], \"\"];\n      //   let fileName = \"\";\n      let excelData = JSON.parse(JSON.stringify(this.tableFrom));\n      excelData.page = 1;\n      for (let i = 0; i < excelData.page + 1; i++) {\n        let lebData = await this.getExcelData(excelData);\n        if (!fileName) fileName = lebData.filename;\n        if (!filekey.length) {\n          filekey = lebData.filekey;\n        }\n        if (!th.length) th = lebData.header;\n        if (lebData.export.length) {\n          data = data.concat(lebData.export);\n          excelData.page++;\n        } else {\n          exportExcel(th, filekey, fileName, data);\n          return;\n        }\n      }\n    },\n    getExcelData(excelData) {\n      return new Promise((resolve, reject) => {\n        storeSeckillApi(excelData).then((res) => {\n          return resolve(res.data);\n        });\n      });\n    },\n    // 编辑\n    edit(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_seckill/create/\" + row.id + \"/0\",\n      });\n    },\n    // 一键复制\n    copy(row) {\n      this.$router.push({\n        path: this.roterPre + \"/marketing/store_seckill/create/\" + row.id + \"/1\",\n      });\n    },\n    // 删除\n    del(row, tit, num) {\n      let delfromData = {\n        title: tit,\n        num: num,\n        url: `marketing/seckill/${row.id}`,\n        method: \"DELETE\",\n        ids: \"\",\n      };\n      this.$modalSure(delfromData)\n          .then((res) => {\n            this.$Message.success(res.msg);\n            this.tableList.splice(num, 1);\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n    viewInfo(row) {\n      this.$router.push({\n        path: this.roterPre+'/marketing/store_seckill/statistics/' + row.id,\n      });\n    },\n    // 列表\n    getList() {\n      this.loading = true;\n      this.tableFrom.start_status = this.tableFrom.start_status || \"\";\n      this.tableFrom.status = this.tableFrom.status || \"\";\n      seckillProductList(this.tableFrom)\n          .then(async (res) => {\n            let data = res.data;\n            this.tableList = data.list;\n            this.total = res.data.count;\n            this.loading = false;\n          })\n          .catch((res) => {\n            this.loading = false;\n            this.$Message.error(res.msg);\n          });\n    },\n    pageChange(index) {\n      this.tableFrom.page = index;\n      this.getList();\n    },\n    // 表格搜索\n    userSearchs() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 修改是否显示\n    onchangeIsShow(row) {\n      let data = {\n        id: row.id,\n        status: row.status,\n      };\n      seckillProductStatus(data)\n          .then(async (res) => {\n            this.$Message.success(res.msg);\n            this.getList()\n          })\n          .catch((res) => {\n            this.$Message.error(res.msg);\n          });\n    },\n  },\n};\n", null]}