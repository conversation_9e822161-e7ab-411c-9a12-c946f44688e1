{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_hot.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfig\\c_home_hot.vue", "mtime": 1640264908000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1725352506767}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1725352508478}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport toolCom from '@/components/mobileConfigRight/index.js'\nimport rightBtn from '@/components/rightBtn/index.vue';\nimport { mapState, mapMutations, mapActions } from 'vuex'\nexport default {\n    name: 'c_home_hot',\n    componentsName: 'home_hot',\n    cname: '超值爆款',\n    props: {\n        activeIndex: {\n            type: null\n        },\n        num: {\n            type: null\n        },\n        index: {\n            type: null\n        }\n    },\n    components: {\n        ...toolCom,\n        rightBtn\n    },\n    watch: {\n        num (nVal) {\n            // debugger;\n            let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[nVal]))\n            this.configObj = value;\n        },\n        configObj: {\n            handler (nVal, oVal) {\n                this.$store.commit('admin/mobildConfig/UPDATEARR', { num: this.num, val: nVal });\n            },\n            deep: true\n        },\n        'configObj.setUp.tabVal': {\n            handler (nVal, oVal) {\n                var arr = [this.rCom[0]]\n                if (nVal == 0) {\n                    let tempArr = [\n                        {\n                            components: toolCom.c_input_item,\n                            configNme: 'titleConfig'\n                        },\n                        {\n                            components: toolCom.c_input_item,\n                            configNme: 'desConfig'\n                        },\n                        {\n                            components: toolCom.c_menu_list,\n                            configNme: 'menuConfig'\n                        }\n                    ]\n                    this.rCom = arr.concat(tempArr)\n                } else {\n                    let tempArr = [\n                        {\n                            components: toolCom.c_bg_color,\n                            configNme: 'themeColor'\n                        },\n                        {\n                            components: toolCom.c_bg_color,\n                            configNme: 'bgColor'\n                        },\n                        {\n                            components: toolCom.c_bg_color,\n                            configNme: 'boxColor'\n                        },\n                        {\n                            components: toolCom.c_slider,\n                            configNme: 'mbConfig'\n                        }\n                    ]\n                    this.rCom = arr.concat(tempArr)\n                }\n            },\n            deep: true\n        }\n    },\n    data () {\n        return {\n            configObj: {},\n            rCom: [\n                {\n                    components: toolCom.c_set_up,\n                    configNme: 'setUp'\n                }\n            ]\n        }\n    },\n    mounted () {\n        this.$nextTick(() => {\n            let value = JSON.parse(JSON.stringify(this.$store.state.admin.mobildConfig.defaultArray[this.num]))\n            this.configObj = value;\n        })\n    },\n    methods: {\n        getConfig (data) {\n\n        },\n        handleSubmit (name) {\n            let obj = {}\n            obj.activeIndex = this.activeIndex\n            obj.data = this.configObj\n            this.add(obj);\n        },\n        ...mapMutations({\n            add: 'admin/mobildConfig/UPDATEARR'\n        })\n    }\n}\n", null]}