{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_label.vue?vue&type=template&id=55287365&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_goods_label.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"slider-box\">\n        <div class=\"c_row-item\">\n            <Col class=\"label\" span=\"4\" v-if=\"configData.title\">\n                {{configData.title}}\n            </Col>\n            <Col span=\"18\">\n\t\t\t\t<div class=\"labelInput acea-row row-between-wrapper\" @click=\"openStoreLabel\">\n\t\t\t\t  <div style=\"width: 90%;\">\n\t\t\t\t    <div v-if=\"configData.list.length\">\n\t\t\t\t      <Tag closable v-for=\"(item,index) in configData.list\" :key=\"index\" @on-close=\"closeStoreLabel(item)\">{{item.label_name}}</Tag>\n\t\t\t\t    </div>\n\t\t\t\t    <span class=\"span\" v-else>选择商品标签</span>\n\t\t\t\t  </div>\n\t\t\t\t  <div class=\"iconfont iconxiayi\"></div>\n\t\t\t\t</div>\n            </Col>\n        </div>\n\t\t<!-- 商品标签 -->\n\t\t<Modal\n\t\t    v-model=\"storeLabelShow\"\n\t\t    scrollable\n\t\t    title=\"选择商品标签\"\n\t\t    :closable=\"true\"\n\t\t    width=\"540\"\n\t\t    :footer-hide=\"true\"\n\t\t    :mask-closable=\"false\"\n\t\t>\n\t\t  <storeLabelList ref=\"storeLabel\" @activeData=\"activeStoreData\" @close=\"storeLabelClose\"></storeLabelList>\n\t\t</Modal>\n    </div>\n", null]}