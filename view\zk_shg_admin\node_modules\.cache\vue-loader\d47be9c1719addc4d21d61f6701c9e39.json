{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue?vue&type=template&id=4455e17a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\mobileConfigRight\\c_foot.vue", "mtime": 1713751452000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n    <div class=\"footer\" v-if=\"footConfig\">\n        <p class=\"tips\">图片建议宽度81*81px；鼠标拖拽左侧圆点可调整导航顺序</p>\n        <draggable\n                class=\"dragArea list-group\"\n                :list=\"footConfig\"\n                group=\"peoples\"\n                handle=\".iconfont\"\n        >\n            <div class=\"box-item\" v-for=\"(item,index) in footConfig\" :key=\"index\">\n                <div class=\"left-tool\">\n                    <span class=\"iconfont iconxingzhuangjiehe\"></span>\n                </div>\n                <div class=\"right-wrapper\">\n\t\t\t\t\t<div class=\"acea-row\" v-if=\"navStyle != 1\">\n\t\t\t\t\t\t<div class=\"title\">图标</div>\n\t\t\t\t\t\t<div class=\"img-wrapper\">\n\t\t\t\t\t\t    <div class=\"img-item\" v-for=\"(img,j) in item.imgList\" @click=\"modalPicTap(index,j)\">\n\t\t\t\t\t\t\t\t<div class=\"pictrue\" v-if=\"img\">\n\t\t\t\t\t\t\t\t\t<img :src=\"img\" alt=\"\">\n\t\t\t\t\t\t\t\t\t<p class=\"txt\">替换</p>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t        <div class=\"empty-img\" v-else>\n\t\t\t\t\t\t            <span class=\"iconfont iconjiahao\"></span>\n\t\t\t\t\t\t        </div>\n\t\t\t\t\t\t\t\t<div class=\"name\">{{j==0?'选中':'未选中'}}</div>\n\t\t\t\t\t\t    </div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n                    <div class=\"c_row-item\" v-if=\"navStyle != 2\">\n                        <Col class=\"label\" span=\"4\">\n                            名称\n                        </Col>\n                        <Col class=\"slider-box\" span=\"20\">\n                            <Input v-model=\"item.name\" placeholder=\"选填不超过10个字\"  />\n                        </Col>\n                    </div>\n                    <div class=\"c_row-item\">\n                        <Col class=\"label\" span=\"4\">\n                            链接\n                        </Col>\n                        <Col class=\"slider-box\" span=\"20\">\n                            <div @click=\"getLink(index)\">\n                                <Input icon=\"ios-arrow-forward\" v-model=\"item.link\" readonly placeholder=\"选填不超过10个字\" />\n                            </div>\n                        </Col>\n                    </div>\n                </div>\n                <div class=\"del-box\" @click=\"deleteMenu(index)\">\n                    <span class=\"iconfont iconcha\"></span>\n                </div>\n            </div>\n        </draggable>\n        <Button class=\"add-btn\" @click=\"addMenu\" v-if=\"footConfig.length<5\">+ 添加板块</Button>\n        <div>\n            <Modal v-model=\"modalPic\" width=\"960px\" scrollable  footer-hide closable title='上传底部菜单' :mask-closable=\"false\" :z-index=\"1\">\n                <uploadPictures :isChoice=\"isChoice\" @getPic=\"getPic\" :gridBtn=\"gridBtn\" :gridPic=\"gridPic\" v-if=\"modalPic\"></uploadPictures>\n            </Modal>\n        </div>\n        <linkaddress ref=\"linkaddres\" @linkUrl=\"linkUrl\"></linkaddress>\n    </div>\n", null]}