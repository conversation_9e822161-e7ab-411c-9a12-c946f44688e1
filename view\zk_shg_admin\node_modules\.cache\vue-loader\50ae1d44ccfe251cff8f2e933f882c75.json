{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue?vue&type=template&id=8d17409a&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\components\\userLabel.vue", "mtime": 1693985518000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"label-wrapper\"},[_c('div',{staticClass:\"list-box\"},[_vm._l((_vm.labelList),function(item,index){return (_vm.isUser)?_c('div',{key:index,staticClass:\"label-box\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(item.name))]),_c('div',{staticClass:\"list\"},_vm._l((item.label),function(label,j){return _c('div',{key:j,staticClass:\"label-item\",class:{on:label.disabled},on:{\"click\":function($event){return _vm.selectLabel(label)}}},[_vm._v(_vm._s(label.label_name))])}),0)]):_vm._e()}),(!_vm.isUser)?_c('div',[_vm._v(\"暂无标签\")]):_vm._e()],2),_c('div',{staticClass:\"footer\"},[_c('Button',{staticClass:\"btns\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.subBtn}},[_vm._v(\"确定\")]),_c('Button',{staticClass:\"btns\",attrs:{\"type\":\"primary\",\"ghost\":\"\"},on:{\"click\":_vm.cancel}},[_vm._v(\"取消\")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}