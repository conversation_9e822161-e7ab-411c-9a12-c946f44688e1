{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue?vue&type=template&id=26050d6d&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\marketing\\storeIntegralOrder\\components\\tableList.vue", "mtime": 1675991652000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Table',{ref:\"table\",staticClass:\"orderData\",attrs:{\"columns\":_vm.columns,\"data\":_vm.orderList,\"loading\":_vm.loading,\"highlight-row\":\"\",\"no-data-text\":\"暂无数据\",\"no-filtered-data-text\":\"暂无筛选结果\"},on:{\"on-selection-change\":_vm.onSelectTab,\"on-select-all\":_vm.selectAll,\"on-select-all-cancel\":_vm.selectAll,\"on-select-cancel\":_vm.onSelectCancel},scopedSlots:_vm._u([{key:\"order_id\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('span',{staticStyle:{\"display\":\"block\"},domProps:{\"textContent\":_vm._s(row.order_id)}}),_c('span',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.is_del == 1),expression:\"row.is_del == 1\"}],staticStyle:{\"color\":\"#ed4014\",\"display\":\"block\"}},[_vm._v(\"用户已删除\")])]}},{key:\"nickname\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('a',{on:{\"click\":function($event){return _vm.showUserInfo(row)}}},[_vm._v(_vm._s(row.nickname)+\"/\"+_vm._s(row.uid))])]}},{key:\"info\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [_c('div',{staticClass:\"tabBox\"},[_c('div',{directives:[{name:\"viewer\",rawName:\"v-viewer\"}],staticClass:\"tabBox_img\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(row.image),expression:\"row.image\"}]})]),_c('span',{staticClass:\"tabBox_tit\"},[_vm._v(\"\\n            \"+_vm._s(row.store_name + \" | \")+_vm._s(row.suk ? row.suk : \"\")+\"\\n          \")]),_c('span',{staticClass:\"tabBox_pice\"},[_vm._v(\"x \"+_vm._s(row.total_num))])])]}},{key:\"status_name\",fn:function(ref){\nvar row = ref.row;\nreturn [_c('Tag',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.status == 0),expression:\"row.status == 0\"}],attrs:{\"color\":\"blue\",\"size\":\"large\"}},[_vm._v(_vm._s(row.status_name))]),_c('Tag',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.status == 1),expression:\"row.status == 1\"}],attrs:{\"color\":\"volcano\",\"size\":\"large\"}},[_vm._v(_vm._s(row.status_name))]),_c('Tag',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.status == 2),expression:\"row.status == 2\"}],attrs:{\"color\":\"volcano\",\"size\":\"large\"}},[_vm._v(_vm._s(row.status_name))]),_c('Tag',{directives:[{name:\"show\",rawName:\"v-show\",value:(row.status == 3),expression:\"row.status == 3\"}],attrs:{\"color\":\"cyan\",\"size\":\"large\"}},[_vm._v(_vm._s(row.status_name))])]}},{key:\"action\",fn:function(ref){\nvar row = ref.row;\nvar index = ref.index;\nreturn [(row.status === 1)?_c('a',{attrs:{\"disabled\":_vm.openErp},on:{\"click\":function($event){return _vm.sendOrder(row)}}},[_vm._v(\"发送货\")]):_vm._e(),(row.status === 1)?_c('Divider',{attrs:{\"type\":\"vertical\"}}):_vm._e(),_c('a',{on:{\"click\":function($event){return _vm.changeMenu(row,'2')}}},[_vm._v(\"订单详情\")])]}}])}),_c('div',{staticClass:\"acea-row row-right page\"},[_c('Page',{attrs:{\"total\":_vm.page.total,\"current\":_vm.page.pageNum,\"show-elevator\":\"\",\"show-total\":\"\",\"page-size\":_vm.page.pageSize,\"show-sizer\":\"\"},on:{\"on-change\":_vm.pageChange,\"on-page-size-change\":_vm.limitChange}})],1),_c('edit-from',{ref:\"edits\",attrs:{\"FromData\":_vm.FromData},on:{\"submitFail\":_vm.submitFail}}),_c('user-details',{ref:\"userDetails\",attrs:{\"fromType\":\"order\"}}),_c('details-from',{ref:\"detailss\",attrs:{\"orderDatalist\":_vm.orderDatalist,\"orderId\":_vm.orderId,\"row-active\":_vm.rowActive,\"openErp\":_vm.openErp}}),_c('order-remark',{ref:\"remarks\",attrs:{\"orderId\":_vm.orderId},on:{\"submitFail\":_vm.submitFail}}),_c('order-record',{ref:\"record\"}),_c('order-send',{ref:\"send\",attrs:{\"orderId\":_vm.orderId},on:{\"submitFail\":function($event){return _vm.submitFail(1)}}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}