{"remainingRequest": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js??ref--0-2!D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue?vue&type=template&id=5906bf54&scoped=true&", "dependencies": [{"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\src\\pages\\user\\level\\index.vue", "mtime": 1716340818000}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1725352509454}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1725352508003}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1725352509190}, {"path": "D:\\zksh\\zk_shg_3.0\\view\\zk_shg_admin\\node_modules\\iview-loader\\index.js", "mtime": 1725352506659}], "contextDependencies": [], "result": ["\n<!-- 用户-会员管理-等级列表 -->\n    <div>\n       <Card :bordered=\"false\" dis-hover class=\"ivu-mt\" :padding=\"0\">\n            <div class=\"new_card_pd\">\n                <!-- 筛选条件 -->\n                 <Form ref=\"levelFrom\" :model=\"levelFrom\" inline :label-width=\"labelWidth\" :label-position=\"labelPosition\" @submit.native.prevent>\n                    <FormItem label=\"状态：\"  label-for=\"status1\">\n                        <Select v-model=\"levelFrom.is_show\" placeholder=\"请选择\" clearable element-id=\"status1\" style=\"width:250px;\" @on-change=\"userSearchs\">\n                            <Option value=\"1\">显示</Option>\n                            <Option value=\"0\">不显示</Option>\n                        </Select>\n                    </FormItem>\n                    <FormItem label=\"等级名称：\"  label-for=\"title\">\n                        <Input  v-model=\"levelFrom.title\"  placeholder=\"请输入等级名称\" style=\"width:250px;margin-right:14px;\"/>\n                        <Button type=\"primary\" @click=\"userSearchs\">查询</Button>\n                    </FormItem>\n                </Form>\n            </div>\n        </Card>\n        <Card :bordered=\"false\" dis-hover class=\"ivu-mt\">\n            <!-- 相关操作 -->\n            <Button v-auth=\"['admin-user-level_add']\" type=\"primary\" @click=\"add\">添加会员等级</Button>\n            <!-- 等级列表表格 -->\n            <Table :columns=\"columns1\" :data=\"levelLists\" ref=\"table\" class=\"mt25\"\n                   :loading=\"loading\" highlight-row\n                   no-userFrom-text=\"暂无数据\"\n                   no-filtered-userFrom-text=\"暂无筛选结果\">\n                <template slot-scope=\"{ row, index }\" slot=\"icons\">\n                    <viewer>\n                        <div class=\"tabBox_img\">\n                            <img v-lazy=\"row.icon\">\n                        </div>\n                    </viewer>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"is_forevers\">\n                    <i-switch v-model=\"row.is_forever\" :value=\"row.is_forever\" :true-value=\"1\" :false-value=\"0\" :disabled=\"true\" size=\"large\">\n                        <span slot=\"open\">永久</span>\n                        <span slot=\"close\">非永久</span>\n                    </i-switch>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"is_pays\">\n                    <i-switch v-model=\"row.is_pay\" :value=\"row.is_pay\" :true-value=\"1\" :false-value=\"0\"  :disabled=\"true\" size=\"large\">\n                        <span slot=\"open\">付费</span>\n                        <span slot=\"close\">免费</span>\n                    </i-switch>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"is_shows\">\n                    <i-switch v-model=\"row.is_show\" :value=\"row.is_show\" :true-value=\"1\" :false-value=\"0\" @on-change=\"onchangeIsShow(row)\" size=\"large\">\n                        <span slot=\"open\">显示</span>\n                        <span slot=\"close\">隐藏</span>\n                    </i-switch>\n                </template>\n                <template slot-scope=\"{ row, index }\" slot=\"action\">\n                    <a @click=\"edit(row)\">编辑</a>\n                    <Divider type=\"vertical\" />\n                    <a @click=\"changeMenu(row,2,index)\">删除</a>\n<!--                    <template>-->\n<!--                        <Dropdown @on-click=\"changeMenu(row,$event,index)\">-->\n<!--                            <a href=\"javascript:void(0)\">-->\n<!--                                更多-->\n<!--                                <Icon type=\"ios-arrow-down\"></Icon>-->\n<!--                            </a>-->\n<!--                            <DropdownMenu slot=\"list\">-->\n<!--&lt;!&ndash;                                <DropdownItem name=\"1\">等级任务</DropdownItem>&ndash;&gt;-->\n<!--                                <DropdownItem name=\"2\">删除等级</DropdownItem>-->\n<!--                            </DropdownMenu>-->\n<!--                        </Dropdown>-->\n<!--                    </template>-->\n                </template>\n            </Table>\n            <div class=\"acea-row row-right page\">\n                <Page :total=\"total\" :current=\"levelFrom.page\" show-elevator show-total @on-change=\"pageChange\"\n                      :page-size=\"levelFrom.limit\"/>\n            </div>\n        </Card>\n        <!-- 等级任务-->\n        <task-list ref=\"tasks\"></task-list>\n    </div>\n", null]}